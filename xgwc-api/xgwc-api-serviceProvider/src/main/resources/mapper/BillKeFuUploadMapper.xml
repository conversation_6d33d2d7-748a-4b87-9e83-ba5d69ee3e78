<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.BillKeFuUploadMapper">
    <insert id="insertBillKeFuUpload" useGeneratedKeys="true" keyColumn="id">
        insert into bill_kefu_upload(attachment, file_name, create_by,user_id,service_id,status, create_time, update_time) values
            (#{attachment}, #{fileName}, #{createBy}, #{userId}, #{serviceId}, 0, now(), now())
    </insert>

    <update id="updateBillKeFuUpload">
        update bill_kefu_upload set status = 1 where id = #{id}
    </update>

    <select id="getBillKeFuUploadListByServiceId" resultType="com.xgwc.serviceProvider.entity.BillKefuUpload">
        select id, attachment, file_name fileName, create_by createBy,user_id userId,service_id serviceId,status, create_time createTime from bill_kefu_upload where status = 0 and service_id = #{serviceId}
    </select>
</mapper>