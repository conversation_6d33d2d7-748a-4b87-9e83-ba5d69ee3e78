<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.ServiceAuthorizeMapper">
    <select id="getAuthorizeBrandIdsByServiceId" resultType="java.lang.Long">
        SELECT
            brand_id
        FROM
            `xgwc_service_authorize` t
        WHERE
            t.service_id = #{serviceId}
          AND t.`status` = 0
        GROUP BY t.brand_id
    </select>
    <select id="getAuthorizeBrandInfoByServiceId" resultType="java.util.Map">
        SELECT
            t.brand_id as brandId,
            ifnull(b.company_simple_name,b.company_name) as companyName
        FROM
            `xgwc_service_authorize` t
        LEFT JOIN `xgwc_brand_owner` b ON t.brand_id = b.brand_id
        WHERE
            t.service_id = #{serviceId}
          AND t.`status` = 0
        ORDER BY t.brand_id DESC
    </select>


</mapper>