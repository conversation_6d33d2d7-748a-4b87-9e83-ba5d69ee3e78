<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.ServiceDeptMapper">
    <insert id="saveServiceDept">
        INSERT INTO `xgwc_sass`.`service_dept`
        (`dept_id`, `dept_name`, `service_id`, `pid`, `level`, `sort`, `create_by`, `create_time`)
        VALUES (#{serviceDeptDto.deptId}, #{serviceDeptDto.deptName}, #{serviceDeptDto.serviceId},
                #{serviceDeptDto.pid},
                #{serviceDeptDto.level}, #{serviceDeptDto.sort},
                #{serviceDeptDto.createBy}, now())
    </insert>
    <update id="updateServiceDeptById">
        UPDATE `xgwc_sass`.`service_dept`
        <set>
            <if test="serviceDeptDto.deptName != null and serviceDeptDto.deptName !=''">
                `dept_name` = #{serviceDeptDto.deptName},
            </if>
            <if test="serviceDeptDto.serviceId != null and serviceDeptDto.serviceId !=''">
                `service_id` = #{serviceDeptDto.serviceId},
            </if>
            <if test="serviceDeptDto.pid != null and serviceDeptDto.pid !=''">
                `pid` = #{serviceDeptDto.pid},
            </if>
            <if test="serviceDeptDto.level != null and serviceDeptDto.level !=''">
                `level` = #{serviceDeptDto.level},
            </if>
            <if test="serviceDeptDto.sort != null and serviceDeptDto.sort !=''">
                `sort` = #{serviceDeptDto.sort},
            </if>
            <if test="serviceDeptDto.updateBy != null and serviceDeptDto.updateBy !=''">
                `update_by` = #{serviceDeptDto.updateBy},
            </if>
            `update_time` = now()
        </set>
        wHERE `dept_id` = #{serviceDeptDto.deptId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`service_dept`
        SET `status` = #{status}
        WHERE `dept_id` = #{deptId}
    </update>
    <update id="updateDeptStaffSchedule">
        UPDATE xgwc_service_staff   
        <set>
            `is_assistant` = 0,
            <if test="staff.isSchedule != null">
                `is_schedule` = #{staff.isSchedule},
            </if>
            <if test="staff.updateBy != null and staff.updateBy != ''">
                `update_by` = #{staff.updateBy},
            </if>
            `update_time` = NOW(),
        </set>
        WHERE `id` = #{staff.id}
    </update>


    <update id="updateDeptStaffManage">
        UPDATE xgwc_service_staff
        <trim prefix="SET" suffixOverrides=",">
            `is_principal` = 0,
            <if test="principal.isSchedule != null">
                `is_schedule` = #{principal.isSchedule},
            </if>
            <if test="principal.updateBy != null and principal.updateBy != ''">
                `update_by` = #{principal.updateBy},
            </if>
            `update_time` = NOW(),
        </trim>
        WHERE `id` = #{principal.id}
    </update>
    <update id="updateDeptStaffAssistant">
        UPDATE xgwc_service_staff
        set is_assistant = 1, is_principal = 1, is_schedule = 1
        where `dept_id` = #{deptId}
    </update>

    <select id="getServiceDeptList" resultType="com.xgwc.serviceProvider.entity.vo.ServiceDeptVo">
        SELECT
        `dept_id` AS deptId,
        `dept_name` AS deptName,
        `service_id` AS serviceId,
        `pid`,
        `level`,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`service_dept`
        <where>
            AND `service_id` = #{serviceDeptParam.serviceId}

            <if test="serviceDeptParam.deptName != null and serviceDeptParam.deptName !=''">
                AND `dept_name` Like concat('%', #{serviceDeptParam.deptName}, '%')
            </if>
            <if test="serviceDeptParam.status != null">
                AND `status` = #{serviceDeptParam.status}
            </if>
        </where>
        ORDER BY `sort`
    </select>

    <select id="getServiceDeptById" resultType="com.xgwc.serviceProvider.entity.dto.ServiceDeptDto">
        SELECT `dept_id`      AS deptId,
               `dept_name`    AS deptName,
               `service_id` AS serviceId,
               `pid`,
               `level`,
               `sort`,
               `status`,
               `is_del`       AS isDel,
               `create_by`    AS createBy,
               `create_time`  AS createTime,
               `update_by`    AS updateBy,
               `update_time`  AS updateTime,
               `modify_time`     ASmodifyTime
        FROM `xgwc_sass`.`service_dept`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
            </if>
        </where>
    </select>
    <select id="selectXgwcDeptStaffManage" resultType="com.xgwc.serviceProvider.entity.vo.ServiceStaffVo">
        SELECT
        `id`,
        `name`,
        `dept_id` AS deptId,
        `is_principal` AS isPrincipal
        FROM
        `xgwc_sass`.`xgwc_service_staff`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
                AND `is_principal` = 0
            </if>
        </where>
    </select>
    <select id="selectXgwcDeptStaffSchedule" resultType="com.xgwc.serviceProvider.entity.vo.ServiceStaffVo">
        SELECT
        `id`,
        `name`,
        `dept_id` AS deptId,
        `is_assistant` AS isAssistant,
        `is_schedule` AS isSchedule
        FROM
        `xgwc_sass`.`xgwc_service_staff`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
                AND `is_assistant` = 0
            </if>
        </where>
    </select>

    <select id="getServiceDeptByserviceId" resultType="com.xgwc.serviceProvider.entity.dto.ServiceDeptDto">
        SELECT `dept_id`      AS deptId,
               `dept_name`    AS deptName,
               `service_id` AS serviceId,
               `pid`,
               `level`,
               `sort`,
               `status`,
               `is_del`       AS isDel,
               `create_by`    AS createBy,
               `create_time`  AS createTime,
               `update_by`    AS updateBy,
               `update_time`  AS updateTime,
               `modify_time`     ASmodifyTime
        FROM `xgwc_sass`.`service_dept`
        WHERE `service_id` = #{serviceId}
         and status = 0 and is_del = 0
    </select>

    <select id="selectDeptStaffNum" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
        `xgwc_sass`.`xgwc_service_staff`
        WHERE
        <if test="deptId != null">
            `dept_id` = #{deptId}
        </if>
    </select>
    <select id="getUserDeptByUserId" resultType="com.xgwc.serviceProvider.entity.dto.ServiceDeptDto">
        select
            d.dept_id,
            d.dept_name,
            d.service_id,
            d.pid
        from xgwc_service_staff s
        left join service_dept d on s.dept_id = d.dept_id
        where s.bind_serviceProvider_id = #{serviceProviderId}
        limit 1
    </select>

    <select id="getDeptByPid" resultType="com.xgwc.serviceProvider.entity.dto.ServiceDeptDto">
        SELECT
            dept_id AS deptId,
            dept_name AS deptName,
            service_id AS serviceId,
            pid,
            level
        FROM service_dept
        WHERE pid = #{pid}
        AND service_id = #{serviceId}
    </select>
</mapper>