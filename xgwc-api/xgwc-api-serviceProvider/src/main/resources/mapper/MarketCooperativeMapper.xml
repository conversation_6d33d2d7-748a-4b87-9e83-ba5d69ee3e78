<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.MarketCooperativeMapper">

    <sql id="selectMarketCooperativeVo">
        select id, service_owner_id, franchise_id, status, is_del, create_by_id, create_by, create_time, update_by_id, update_by, update_time, modify_time from xgwc_market_cooperative
    </sql>

    <select id="selectMarketCooperativeList" parameterType="com.xgwc.serviceProvider.entity.vo.MarketCooperativeQueryVo" resultType="com.xgwc.serviceProvider.entity.dto.MarketCooperativeDto">
        SELECT
            t.id,
            t.service_owner_id,
            t.franchise_id,
            t.status,
            t.create_time,
            so.company_name AS serviceName,
            IFNULL(c.collaborators<PERSON><PERSON>, 0) AS collaboratorsNumber,
            (IFNULL(s.totalStaffNumber, 0) - IFNULL(c.collaboratorsNumber, 0)) AS unCollaboratorsNumber
        FROM xgwc_market_cooperative t
        LEFT JOIN xgwc_service_owner so ON so.id = t.service_owner_id
        LEFT JOIN (
            SELECT franchise_id, COUNT(*) AS collaboratorsNumber
            FROM xgwc_market_staff_cooperative
            GROUP BY franchise_id
            ) c ON c.franchise_id = t.franchise_id
        LEFT JOIN (
            SELECT service_owner_id, COUNT(*) AS totalStaffNumber
            FROM xgwc_market_staff
            WHERE status = 0 AND is_del = 0
            GROUP BY service_owner_id
        ) s ON s.service_owner_id = t.service_owner_id
        <where>
            <if test="franchiseId != null "> and t.franchise_id = #{franchiseId}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="isDel == null"> and t.is_del = 0</if>
            <if test="isDel != null "> and t.is_del = #{isDel}</if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectMarketCooperativeById" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.MarketCooperativeDto">
        <include refid="selectMarketCooperativeVo"/>
        where id = #{id}
    </select>

    <insert id="insertMarketCooperative" parameterType="com.xgwc.serviceProvider.entity.MarketCooperative">
        insert into xgwc_market_cooperative
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="serviceOwnerId != null">service_owner_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="status != null">status,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateById != null">update_by_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="serviceOwnerId != null">#{serviceOwnerId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="status != null">#{status},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createById != null">#{createById},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateById != null">#{updateById},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
         </trim>
    </insert>

    <update id="updateMarketCooperative" parameterType="com.xgwc.serviceProvider.entity.MarketCooperative">
        update xgwc_market_cooperative
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceOwnerId != null">service_owner_id = #{serviceOwnerId},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateById != null">update_by_id = #{updateById},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>