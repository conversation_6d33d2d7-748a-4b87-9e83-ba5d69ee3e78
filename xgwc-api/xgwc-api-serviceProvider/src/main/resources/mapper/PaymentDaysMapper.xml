<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.PaymentDaysMapper">
    

    <sql id="selectPaymentDaysVo">
        select brand_id, create_by, create_time, cycle_type, payment_type, tax_money, update_by, update_time from xgwc_payment_days
    </sql>

    <select id="selectPaymentDaysByBrandId" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.PaymentDaysDto">
        <include refid="selectPaymentDaysVo"/>
        where brand_id = #{brandId}
    </select>

    <insert id="insertPaymentDays" parameterType="com.xgwc.serviceProvider.entity.PaymentDays">
        insert into xgwc_payment_days
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brandId != null">brand_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="cycleType != null">cycle_type,</if>
            <if test="paymentType != null">payment_type,</if>
            <if test="taxMoney != null">tax_money,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brandId != null">#{brandId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="cycleType != null">#{cycleType},</if>
            <if test="paymentType != null">#{paymentType},</if>
            <if test="taxMoney != null">#{taxMoney},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePaymentDays" parameterType="com.xgwc.serviceProvider.entity.PaymentDays">
        update xgwc_payment_days
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="cycleType != null">cycle_type = #{cycleType},</if>
            <if test="taxMoney != null">tax_money = #{taxMoney},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where brand_id = #{brandId} and payment_type = #{paymentType}
    </update>

    <select id="selectPaymentDays" resultType="com.xgwc.serviceProvider.entity.dto.PaymentDaysDto">
        <include refid="selectPaymentDaysVo"/>
        where brand_id = #{brandId} and payment_type = #{paymentType}
    </select>

</mapper>