<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.BillOrderMapper">
    <update id="updateOrderStatus">
        UPDATE xgwc_order o
        SET o.settlement = 1,
            o.settlement_time = #{date},
            o.settlement_user = #{nickName}
        WHERE o.order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <select id="getBillOrderDetailByBillId" resultType="com.xgwc.serviceProvider.entity.dto.BillBrandOrderDetail">
        SELECT
            t.order_id orderId,
            t.order_no orderNo,
            t.franchise_id franchiseId,
            t.archive_time archiveTime,
            t.deal_time dealTime,
            ifnull( fo.company_simple_name, fo.company_name ) franchiseName,
            xb.business_id,
            xb.business_name,
            ifnull( t.now_money, t.money ) nowMoney,
            t.taobao_id customerNo,
            t.brand_id
        FROM
            bill_order t
                LEFT JOIN bill_designer_sub bs ON bs.sub_bill_id = t.sub_bill_id
                LEFT JOIN franchise_owner fo ON ( t.franchise_id = fo.franchise_id AND fo.brand_id = t.brand_id )
                LEFT JOIN xgwc_designer xd ON t.designer_id = xd.designer_id
                LEFT JOIN xgwc_business xb ON xb.business_id = xd.good_business
        WHERE
            bs.bill_id = #{billId}
    </select>

    <select id="getBillOrderDetailBySubBillId" resultType="com.xgwc.serviceProvider.entity.dto.BillBrandOrderDetail">
        SELECT
            t.order_id orderId,
            t.order_no orderNo,
            t.franchise_id franchiseId,
            t.archive_time archiveTime,
            t.deal_time dealTime,
            ifnull( fo.company_simple_name, fo.company_name ) franchiseName,
            xb.business_id,
            xb.business_name,
            ifnull( t.now_money, t.money ) nowMoney,
            t.taobao_id customerNo,
            t.brand_id
        FROM
            bill_order t
                LEFT JOIN franchise_owner fo ON ( t.franchise_id = fo.franchise_id AND fo.brand_id = t.brand_id )
                LEFT JOIN xgwc_designer xd ON t.designer_id = xd.designer_id
                LEFT JOIN xgwc_business xb ON xb.business_id = xd.good_business
        WHERE
            t.sub_bill_id = #{subBillId}
    </select>

    <select id="getOrderIdsByBillIds" resultType="java.lang.Long">
        SELECT
            t.order_id
        FROM
            bill_order t
        WHERE
            t.bill_id in
            <foreach item="item" collection="billIds" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>

</mapper>