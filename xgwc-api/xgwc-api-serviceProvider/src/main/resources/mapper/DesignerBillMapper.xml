<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.DesignerBillMapper">
    <sql id="selectDesignerBillVo">
        select bill_id, bill_period_end, bill_period_start, is_lock, billing_time, brand_owner_id, commission_back, confirmation_time, create_by, create_time, fine_amount, manager_user_id, modify_time
                      , no_invoice, order_count, settlement_status, settlement_time, stylist_id, stylist_name, total_commission, update_by, update_time
        from bill_designer
    </sql>

    <select id="selectDesignerBillList"  resultType="com.xgwc.serviceProvider.entity.dto.DesignerBillDto">
        SELECT
        s.bill_id,
        s.bill_period_end,
        s.bill_period_start,
        s.billing_time,
        s.brand_owner_id,
        s.commission_back,
        s.confirmation_time,
        s.create_time,
        s.fine_amount,
        s.manager_user_id,
        s.modify_time,
        s.no_invoice,
        s.is_lock,
        s.order_count,
        s.settlement_status,
        s.settlement_time,
        s.stylist_id,
        s.stylist_name,
        s.total_commission,
        ifnull(o.company_simple_name, o.company_name) brandName,
        d.name designerName,
        b.business_name businessName,
        d.phone,
        d.zfb_name zfbName,
        d.zfb_account zfbAccount
        FROM
        bill_designer s
        left join xgwc_brand_owner o on o.brand_id = s.brand_owner_id
        left join xgwc_designer d on d.designer_id = s.stylist_id
        left join xgwc_business b on d.good_business = b.business_id
        <where>
            <if test="billId = null">and s.bill_id = #{billId}</if>
            <if test="billStart != null and billEnd != null">
                and s.bill_period_end between #{billStart} and #{billEnd}
            </if>
            <if test="billStart != null and billEnd != null">
                and s.bill_period_start between #{billStart} and #{billEnd}
            </if>
            <if test="billingTimeStart != null and billingTimeEnd != null">
                and s.billing_time between #{billingTimeStart} and #{billingTimeEnd}
            </if>
            <if test="brandOwnerId != null "> and s.brand_owner_id = #{brandId}</if>
            <if test="level != null "> and b.level = #{level}</if>
            <if test="settlementStatus != null "> and b.settlement_status = #{settlementStatus}</if>designerName
            <if test="designerName != null "> and d.name = #{designerName}</if>
            <if test="phone != null "> and d.phone = #{phone}</if>
        </where>
    </select>

    <select id="selectDesignerBillDtoByBillId" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.DesignerBillDto">
        SELECT
            t.bill_id,
            t.bill_period_end,
            t.bill_period_start,
            t.is_lock,
            t.billing_time,
            t.brand_owner_id,
            t.commission_back,
            t.confirmation_time,
            t.create_by,
            t.create_time,
            t.fine_amount,
            t.manager_user_id,
            t.modify_time,
            t.no_invoice,
            t.order_count,
            t.payable_amount,
            t.real_amount,
            t.settlement_status,
            t.settlement_time,
            t.stylist_id,
            t.stylist_name,
            t.total_commission,
            t.update_by,
            t.update_time,
            ifnull(o.company_simple_name, o.company_name) brandName
        FROM
            bill_designer t
                left join xgwc_brand_owner o on o.brand_id = t.brand_owner_id
        WHERE
            t.bill_id = #{billId} and t.status = 0
    </select>

    <insert id="insertDesignerBillDto" parameterType="com.xgwc.serviceProvider.entity.DesignerBill">
        insert into stylist_reconciliation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billId != null">bill_id,</if>
            <if test="billPeriodEnd != null">bill_period_end,</if>
            <if test="billPeriodStart != null">bill_period_start,</if>
            <if test="billingTime != null">billing_time,</if>
            <if test="brandOwnerId != null">brand_owner_id,</if>
            <if test="commissionBack != null">commission_back,</if>
            <if test="confirmationTime != null">confirmation_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="fineAmount != null">fine_amount,</if>
            <if test="managerUserId != null">manager_user_id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="noInvoice != null">no_invoice,</if>
            <if test="orderCount != null">order_count,</if>
            <if test="settlementStatus != null">settlement_status,</if>
            <if test="settlementTime != null">settlement_time,</if>
            <if test="stylistId != null">stylist_id,</if>
            <if test="stylistName != null">stylist_name,</if>
            <if test="totalCommission != null">total_commission,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isLock != null">is_lock,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billId != null">#{billId},</if>
            <if test="billPeriodEnd != null">#{billPeriodEnd},</if>
            <if test="billPeriodStart != null">#{billPeriodStart},</if>
            <if test="billingTime != null">#{billingTime},</if>
            <if test="brandOwnerId != null">#{brandOwnerId},</if>
            <if test="commissionBack != null">#{commissionBack},</if>
            <if test="confirmationTime != null">#{confirmationTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="fineAmount != null">#{fineAmount},</if>
            <if test="managerUserId != null">#{managerUserId},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="noInvoice != null">#{noInvoice},</if>
            <if test="orderCount != null">#{orderCount},</if>
            <if test="settlementStatus != null">#{settlementStatus},</if>
            <if test="settlementTime != null">#{settlementTime},</if>
            <if test="stylistId != null">#{stylistId},</if>
            <if test="stylistName != null">#{stylistName},</if>
            <if test="totalCommission != null">#{totalCommission},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isLock != null">#{isLock},</if>
        </trim>
    </insert>

    <update id="updateDesignerBillDto" parameterType="com.xgwc.serviceProvider.entity.DesignerBill">
        update bill_designer
        <trim prefix="SET" suffixOverrides=",">
            <if test="billPeriodEnd != null">bill_period_end = #{billPeriodEnd},</if>
            <if test="billPeriodStart != null">bill_period_start = #{billPeriodStart},</if>
            <if test="billingTime != null">billing_time = #{billingTime},</if>
            <if test="brandOwnerId != null">brand_owner_id = #{brandOwnerId},</if>
            <if test="commissionBack != null">commission_back = #{commissionBack},</if>
            <if test="confirmationTime != null">confirmation_time = #{confirmationTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="fineAmount != null">fine_amount = #{fineAmount},</if>
            <if test="managerUserId != null">manager_user_id = #{managerUserId},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="noInvoice != null">no_invoice = #{noInvoice},</if>
            <if test="orderCount != null">order_count = #{orderCount},</if>
            <if test="settlementStatus != null">settlement_status = #{settlementStatus},</if>
            <if test="settlementTime != null">settlement_time = #{settlementTime},</if>
            <if test="stylistId != null">stylist_id = #{stylistId},</if>
            <if test="stylistName != null">stylist_name = #{stylistName},</if>
            <if test="totalCommission != null">total_commission = #{totalCommission},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isLock != null">is_lock = #{isLock},</if>
            <if test="isInvoice != null">is_invoice = #{isInvoice},</if>
            <if test="payableAmount != null">payable_amount = #{payableAmount}</if>
        </trim>
        where bill_id = #{billId}
    </update>

    <select id="selectBillBrandDtoList" resultType="com.xgwc.serviceProvider.entity.dto.BillBrandDto">
        SELECT
                    t.bill_id billId,
                    t.bill_period_start billStart,
                    t.bill_period_end billEnd,
                    t.order_count orderCount,
                    t.total_commission totalCommission,
                    t.no_invoice noInvoice,
                    t.commission_back commissionBack,
                    t.fine_amount fineAmount,
                    t.is_lock isLock,
                    d.designer_id designerId,
                    d.`name` designerName,
                    b.business_id businessId,
                    b.business_name businessName,
                    d.phone,
                    d.zfb_name zfbName,
                    d.zfb_account zfbAccount,
                    t.billing_time billTime,
                    t.confirmation_time comfirmTime,
                    t.settlement_time settlementTime,
                    t.settlement_status settlementStatus,
                    t.brand_owner_id brandId,
                    t.payable_amount payableAmount,
                    t.real_amount realAmount,
                    ifnull(xb.company_simple_name,xb.company_name) brandName
                FROM
                    bill_designer t
                        LEFT JOIN xgwc_designer d ON d.designer_id = t.stylist_id
                        LEFT JOIN xgwc_business b ON b.business_id = d.good_business
                        LEFT JOIN xgwc_brand_owner xb ON xb.brand_id = t.brand_owner_id
                WHERE
                    t.`status` = 0
                    <if test="billId != null">and t.bill_id = #{billId}</if>
                    <if test="phone != null">and d.phone = #{phone}</if>
                    <if test="designerName != null">and d.name = #{designerName}</if>
                    <if test="businessId != null">and b.business_id = #{businessId}</if>
                    <if test="settlementStatus != null">and t.settlement_status = #{settlementStatus}</if>
                    <if test="billStart != null">and t.bill_period_start &gt;= #{billStart}</if>
                    <if test="billEnd != null">and t.bill_period_end &lt;= #{billEnd}</if>
                    and t.brand_owner_id in
                    <foreach item="id" collection="brandIds" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    <if test="brandId != null">and t.brand_owner_id = #{brandId}</if>
                  order by t.bill_id desc
    </select>

    <select id="getBillIdsByToBeLocked" resultType="java.lang.Long">
        SELECT
            bill_id
        FROM
            bill_designer t
        WHERE
            t.settlement_status = 2
          AND t.is_lock = 0
          AND t.brand_owner_id = #{brandId}
          AND t.status = 0
    </select>

    <update id="updateLockedBills">
        UPDATE bill_designer
        SET is_lock = 1,
        lock_time = now(),
        lock_user = #{lockUser}
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </update>

</mapper>