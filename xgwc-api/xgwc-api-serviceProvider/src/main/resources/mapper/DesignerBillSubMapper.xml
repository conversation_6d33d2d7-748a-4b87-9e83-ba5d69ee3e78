<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.DesignerBillSubMapper">
    <sql id="selectDesignerBillSubVo">
        select sub_bill_id, bill_id, bill_period_end, bill_period_start, is_lock, billing_time, brand_owner_id, commission_back, confirmation_time, create_by,
               create_time, fine_amount, manager_user_id, modify_time, no_invoice, order_count, settlement_status, settlement_time, stylist_id,
               stylist_name, total_commission, update_by, update_time from bill_designer_sub
    </sql>

    <select id="selectDesignerBillSubList"  resultType="com.xgwc.serviceProvider.entity.dto.DesignerBillSubDto">
        SELECT
        s.sub_bill_id,
        s.bill_id,
        s.bill_period_end,
        s.bill_period_start,
        s.billing_time,
        s.brand_owner_id,
        s.commission_back,
        s.confirmation_time,
        s.create_time,
        s.fine_amount,
        s.manager_user_id,
        s.modify_time,
        s.no_invoice,
        s.is_lock,
        s.order_count,
        s.settlement_status,
        s.settlement_time,
        s.stylist_id,
        s.stylist_name,
        s.total_commission,
        ifnull(o.company_simple_name, o.company_name) brandName,
        d.name designerName,
        b.business_name businessName,
        d.phone,
        d.zfb_name zfbName,
        d.zfb_account zfbAccount
        FROM
        bill_designer_sub s
        left join xgwc_brand_owner o on o.brand_id = s.brand_owner_id
        left join xgwc_designer d on d.designer_id = s.stylist_id
        left join xgwc_business b on d.good_business = b.business_id
        <where>
            <if test="subBillId = null">and s.bill_id = #{subBillId}</if>
            <if test="billStart != null and billEnd != null">
                and s.bill_period_end between #{billStart} and #{billEnd}
            </if>
            <if test="billStart != null and billEnd != null">
                and s.bill_period_start between #{billStart} and #{billEnd}
            </if>
            <if test="billingTimeStart != null and billingTimeEnd != null">
                and s.billing_time between #{billingTimeStart} and #{billingTimeEnd}
            </if>
            <if test="brandOwnerId != null "> and s.brand_owner_id = #{brandId}</if>
            <if test="level != null "> and b.level = #{level}</if>
            <if test="settlementStatus != null "> and b.settlement_status = #{settlementStatus}</if>designerName
            <if test="designerName != null "> and d.name = #{designerName}</if>
            <if test="phone != null "> and d.phone = #{phone}</if>
        </where>
    </select>

    <select id="selectDesignerBillSubDtoBySubBillId" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.DesignerBillSubDto">
        SELECT
            t.bill_id,
            t.bill_period_end,
            t.bill_period_start,
            t.is_lock,
            t.billing_time,
            t.brand_owner_id,
            t.commission_back,
            t.confirmation_time,
            t.create_by,
            t.create_time,
            t.fine_amount,
            t.manager_user_id,
            t.modify_time,
            t.no_invoice,
            t.order_count,
            t.payable_amount,
            t.real_amount,
            t.settlement_status,
            t.settlement_time,
            t.stylist_id,
            t.stylist_name,
            t.total_commission,
            t.update_by,
            t.update_time,
            t.fail_reason,
            ifnull( o.company_simple_name, o.company_name ) brandName
        FROM
            bill_designer_sub t
                LEFT JOIN xgwc_brand_owner o ON o.brand_id = t.brand_owner_id
        WHERE
            t.sub_bill_id = #{subBillId}
          AND t.STATUS = 0
    </select>

    <insert id="insertDesignerBillSubDto" parameterType="com.xgwc.serviceProvider.entity.DesignerBillSub">
        insert into bill_designer_sub
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subBillId != null">sub_bill_id,</if>
            <if test="billId != null">bill_id,</if>
            <if test="billPeriodEnd != null">bill_period_end,</if>
            <if test="billPeriodStart != null">bill_period_start,</if>
            <if test="billingTime != null">billing_time,</if>
            <if test="brandOwnerId != null">brand_owner_id,</if>
            <if test="commissionBack != null">commission_back,</if>
            <if test="confirmationTime != null">confirmation_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="fineAmount != null">fine_amount,</if>
            <if test="managerUserId != null">manager_user_id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="noInvoice != null">no_invoice,</if>
            <if test="orderCount != null">order_count,</if>
            <if test="settlementStatus != null">settlement_status,</if>
            <if test="settlementTime != null">settlement_time,</if>
            <if test="stylistId != null">stylist_id,</if>
            <if test="stylistName != null">stylist_name,</if>
            <if test="totalCommission != null">total_commission,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isLock != null">is_lock,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subBillId != null">#{subBillId},</if>
            <if test="billId != null">#{billId},</if>
            <if test="billPeriodEnd != null">#{billPeriodEnd},</if>
            <if test="billPeriodStart != null">#{billPeriodStart},</if>
            <if test="billingTime != null">#{billingTime},</if>
            <if test="brandOwnerId != null">#{brandOwnerId},</if>
            <if test="commissionBack != null">#{commissionBack},</if>
            <if test="confirmationTime != null">#{confirmationTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="fineAmount != null">#{fineAmount},</if>
            <if test="managerUserId != null">#{managerUserId},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="noInvoice != null">#{noInvoice},</if>
            <if test="orderCount != null">#{orderCount},</if>
            <if test="settlementStatus != null">#{settlementStatus},</if>
            <if test="settlementTime != null">#{settlementTime},</if>
            <if test="stylistId != null">#{stylistId},</if>
            <if test="stylistName != null">#{stylistName},</if>
            <if test="totalCommission != null">#{totalCommission},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isLock != null">#{isLock},</if>
        </trim>
    </insert>
    <insert id="insertBillPayment" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `xgwc_sass`.`bill_payment`
        (`excel_name`, `brand_id`, `create_by`, `create_time`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.excelName}, #{item.brandId}, #{item.createBy}, now())
        </foreach>
    </insert>
    <insert id="insertBillPaymentAndSub">
        INSERT INTO `xgwc_sass`.`bill_payment_sub`
        (`payment_id`, `sub_bill_id`, `create_by`, `create_time`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.subBillId}, #{item.createBy}, now())
        </foreach>
    </insert>

    <update id="updateDesignerBillSubDto" parameterType="com.xgwc.serviceProvider.entity.DesignerBillSub">
        update bill_designer_sub
        <trim prefix="SET" suffixOverrides=",">
            <if test="billPeriodEnd != null">bill_period_end = #{billPeriodEnd},</if>
            <if test="billPeriodStart != null">bill_period_start = #{billPeriodStart},</if>
            <if test="billingTime != null">billing_time = #{billingTime},</if>
            <if test="brandOwnerId != null">brand_owner_id = #{brandOwnerId},</if>
            <if test="commissionBack != null">commission_back = #{commissionBack},</if>
            <if test="confirmationTime != null">confirmation_time = #{confirmationTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="fineAmount != null">fine_amount = #{fineAmount},</if>
            <if test="managerUserId != null">manager_user_id = #{managerUserId},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="noInvoice != null">no_invoice = #{noInvoice},</if>
            <if test="orderCount != null">order_count = #{orderCount},</if>
            <if test="settlementStatus != null">settlement_status = #{settlementStatus},</if>
            <if test="settlementTime != null">settlement_time = #{settlementTime},</if>
            <if test="settlementUser != null">settlement_user = #{settlementUser},</if>
            <if test="stylistId != null">stylist_id = #{stylistId},</if>
            <if test="stylistName != null">stylist_name = #{stylistName},</if>
            <if test="totalCommission != null">total_commission = #{totalCommission},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isLock != null">is_lock = #{isLock},</if>
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="payableAmount != null">payable_amount = #{payableAmount}</if>
            <if test="failReason != null">fail_reason = #{failReason},</if>
        </trim>
        where sub_bill_id = #{subBillId}
    </update>
    <update id="updateBillAmount">
        <foreach item="entry" collection="list" separator=";">
            update bill_designer
            set real_amount = #{entry.value},
                settlement_time = #{date},
                settlement_user = #{nickName}
            where bill_id = #{entry.key}
        </foreach>
    </update>

    <update id="updateBillDesignerStatus">
        update bill_designer
        set settlement_status = #{settlementStatus}
        where bill_id = #{billId}
    </update>

    <select id="selectBillBrandSubDtoList" resultType="com.xgwc.serviceProvider.entity.dto.BillBrandSubDto">
        SELECT
            t.sub_bill_id subBillId,
            t.bill_id billId,
            t.bill_period_start billStart,
            t.bill_period_end billEnd,
            t.order_count orderCount,
            t.total_commission totalCommission,
            t.no_invoice noInvoice,
            t.commission_back commissionBack,
            t.fine_amount fineAmount,
            t.is_lock isLock,
            d.designer_id designerId,
            d.`name` designerName,
            d.phone,
            d.zfb_name zfbName,
            d.zfb_account zfbAccount,
            t.billing_time billTime,
            t.confirmation_time comfirmTime,
            t.settlement_time settlementTime,
            t.settlement_status settlementStatus,
            t.brand_owner_id brandId,
            t.payable_amount payableAmount,
            t.real_amount realAmount,
            b.business_id businessId,
            b.business_name businessName,
            ifnull(xb.company_simple_name,xb.company_name) brandName,
            fo.franchise_id franchiseId,
            ifnull(fo.company_simple_name, fo.company_name) franchiseName,
            t.company_info_id,
            ifnull(ci.company_simple_name, ci.company_name) companyInfoName,
            t.fail_reason failReason
        FROM
        bill_designer_sub t
                LEFT JOIN xgwc_designer d ON d.designer_id = t.stylist_id
                LEFT JOIN xgwc_brand_owner xb ON xb.brand_id = t.brand_owner_id
                LEFT JOIN franchise_owner fo ON fo.brand_id = t.brand_owner_id and fo.franchise_id = t.franchise_id
                LEFT JOIN xgwc_business b ON b.business_id = d.good_business
                left join xgwc_company_info ci on ci.id = t.company_info_id
        <where>
            t.`status` = 0
            and t.brand_owner_id = #{brandId}
            <if test="subBillId != null">and t.sub_bill_id = #{subBillId}</if>
            <if test="billId != null">and t.bill_id = #{billId}</if>
            <if test="phone != null">and d.phone = #{phone}</if>
            <if test="designerName != null">and d.name = #{designerName}</if>
            <if test="businessId != null">and b.business_id = #{businessId}</if>
            <if test="settlementStatus != null">and t.settlement_status = #{settlementStatus}</if>
            <if test="billStart != null">and t.bill_period_start &gt;= #{billStart}</if>
            <if test="billEnd != null">and t.bill_period_end &lt;= #{billEnd}</if>
            AND t.brand_owner_id IN
            <foreach item="id" collection="brandIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="isLock != null">and t.is_lock = #{isLock}</if>
        </where>
        ORDER BY t.bill_id DESC
    </select>

    <select id="selectDesignerBillSubListByBillId" resultType="com.xgwc.serviceProvider.entity.dto.DesignerBillSubDto">
        select * from bill_designer_sub t where t.bill_id = #{billId}
    </select>
    <select id="selectPaymentRecordList" resultType="com.xgwc.serviceProvider.entity.dto.BillPaymentRecord">
        select
            bp.id,
            bp.excel_name as excelName,
            bp.brand_id as brandId,
            bp.create_by as createBy,
            bp.create_time as createTime,
            bds.sub_bill_id as subBillId,
            d.zfb_name as alipayName,
            d.zfb_account as alipayAccount,
            bds.payable_amount as amount,
            bds.fail_reason as failReason
        from bill_payment_sub bps
        left join bill_payment bp on bp.id = bps.payment_id
        left join bill_designer_sub bds on bps.sub_bill_id = bds.sub_bill_id
        left join xgwc_designer d ON d.designer_id = bds.stylist_id
        <where>
            and brand_id in
            <foreach item="id" collection="brandIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="brandId != null">and brand_id = #{brandId}</if>
            <if test="paymentId != null">and bps.payment_id = #{paymentId}</if>
            <if test="name != null and name != ''">
                and (bp.excel_name = #{name} or bp.create_by = #{name})
            </if>
            <if test="date != null">
                and (bp.create_time between #{date[0]} and #{date[1]})
            </if>
        </where>
    </select>
    <select id="selectStatusByBillId" resultType="java.lang.Boolean">
        select count(1) > 0
        from bill_designer_sub t
        where t.bill_id = #{billId} and t.settlement_status = 3
    </select>
    <select id="selectOrderId" resultType="java.lang.Long">
        select
            o.order_id
        from bill_order o
                 left join bill_designer_sub t on o.sub_bill_id = t.sub_bill_id
        where t.sub_bill_id = #{subBillId}
    </select>

</mapper>