<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.XgwcPaymentDaysMapper">

   <select id="selectByBrandId" resultType="com.xgwc.serviceProvider.entity.XgwcPaymentDays">
      SELECT
         brand_id brandId, payment_type paymentType,cycle_type cycleType,tax_money taxMoney
      FROM
         xgwc_payment_days t
      WHERE
         t.brand_id = #{brandId}
        AND t.payment_type = #{paymentType}
   </select>

</mapper>