<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.MarketStaffCooperativeMapper">
    

    <sql id="selectMarketStaffCooperativeVo">
        select id, staff_id, service_owner_id, franchise_id, franchise_staff_id, is_del, create_by_id, create_by, create_time, update_by_id, update_by, update_time, modify_time from xgwc_market_staff_cooperative
    </sql>

    <select id="selectMarketStaffCooperativeList" parameterType="com.xgwc.serviceProvider.entity.vo.MarketStaffCooperativeQueryVo" resultType="com.xgwc.serviceProvider.entity.dto.MarketStaffCooperativeDto">
        <include refid="selectMarketStaffCooperativeVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="staffId != null "> and staff_id = #{staffId}</if>
            <if test="serviceOwnerId != null "> and service_owner_id = #{serviceOwnerId}</if>
            <if test="franchiseId != null "> and franchise_id = #{franchiseId}</if>
            <if test="franchiseStaffId != null "> and franchise_staff_id = #{franchiseStaffId}</if>
            <if test="isDel == null"> and is_del = 0</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="createById != null "> and create_by_id = #{createById}</if>
        </where>
    </select>
    
    <select id="selectMarketStaffCooperativeById" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.MarketStaffCooperativeDto">
        <include refid="selectMarketStaffCooperativeVo"/>
        where id = #{id}
    </select>

    <select id="findByStaffIdAndFranchiseId"
            resultType="com.xgwc.serviceProvider.entity.dto.MarketStaffCooperativeDto">
        <include refid="selectMarketStaffCooperativeVo"/>
        where staff_id = #{staffId} and franchise_id = #{franchiseId}
    </select>

    <insert id="insertMarketStaffCooperative" parameterType="com.xgwc.serviceProvider.entity.MarketStaffCooperative">
        insert into xgwc_market_staff_cooperative
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="staffId != null">staff_id,</if>
            <if test="serviceOwnerId != null">service_owner_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="franchiseStaffId != null">franchise_staff_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateById != null">update_by_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="staffId != null">#{staffId},</if>
            <if test="serviceOwnerId != null">#{serviceOwnerId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="franchiseStaffId != null">#{franchiseStaffId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createById != null">#{createById},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateById != null">#{updateById},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
         </trim>
    </insert>

    <update id="updateMarketStaffCooperative" parameterType="com.xgwc.serviceProvider.entity.MarketStaffCooperative">
        update xgwc_market_staff_cooperative
        <trim prefix="SET" suffixOverrides=",">
            <if test="staffId != null">staff_id = #{staffId},</if>
            <if test="serviceOwnerId != null">service_owner_id = #{serviceOwnerId},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="franchiseStaffId != null">franchise_staff_id = #{franchiseStaffId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateById != null">update_by_id = #{updateById},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>