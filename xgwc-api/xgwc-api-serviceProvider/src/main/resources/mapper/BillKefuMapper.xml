<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.BillKefuMapper">

    <sql id="selectBillKefuVo">
        select id, bill_period_start, bill_period_end, user_id, order_count, real_amount, refund_amount, total_commission, percentage_amount,
               base_amount, percentage_back, pre_tax_amount, franchise_id, confirm_time, is_lock, billing_time, status, create_time, update_time,
               modify_time from bill_kefu
    </sql>

    <select id="getBillKefuByBillTimeAndUserId" resultType="com.xgwc.serviceProvider.entity.dto.BillKefuDto">
        <include refid="selectBillKefuVo"/>
        <where>
            bill_period_start = #{billStart}
            and bill_period_end = #{billEnd}
            and user_id = #{userId}
            limit 1
        </where>
    </select>

    <update id="updateBillKefuById">
        update bill_kefu
        <trim prefix="SET" suffixOverrides=",">
            <if test="billPeriodStart != null">bill_period_start = #{billPeriodStart},</if>
            <if test="billPeriodEnd != null">bill_period_end = #{billPeriodEnd},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="orderCount != null">order_count = #{orderCount},</if>
            <if test="realAmount != null">real_amount = #{realAmount},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="totalCommission != null">total_commission = #{totalCommission},</if>
            <if test="percentageAmount != null">percentage_amount = #{percentageAmount},</if>
            <if test="baseAmount != null">base_amount = #{baseAmount},</if>
            <if test="percentageBack != null">percentage_back = #{percentageBack},</if>
            <if test="preTaxAmount != null">pre_tax_amount = #{preTaxAmount},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="isLock != null">is_lock = #{isLock},</if>
            <if test="billingTime != null">billing_time = #{billingTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

</mapper>