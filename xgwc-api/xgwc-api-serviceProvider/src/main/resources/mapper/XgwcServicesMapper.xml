<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.XgwcServicesMapper">

    <select id="getStaffUserIdByServiceId" resultType="java.lang.Long">
        SELECT
            t.bind_user_id
        FROM
            xgwc_franchise_staff t
                LEFT JOIN franchise_owner o ON o.franchise_id = t.franchise_id
                LEFT JOIN xgwc_service_authorize a ON a.brand_id = o.brand_id
        WHERE
            a.service_id = #{serviceId}
          AND a.`status` = 0
          AND a.is_del = 0
          AND o.`status` = 0
          AND o.is_del = 0
          AND o.check_status = 1
          AND t.`status` = 0
          AND t.is_del = 0
    </select>

</mapper>