<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.BillComfirmMapper">
    <sql id="selectBillComfirmVo">
        select check_status, check_time, provide_invoice, total_invoice_amount, comment, create_by, create_time, id, invoices, modify_time, bill_id, status, update_by, update_time, user_id from bill_comfirm
    </sql>

    <select id="selectBillComfirmById" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.BillComfirmDto">
        <include refid="selectBillComfirmVo"/>
        where id = #{id}
    </select>

    <select id="selectBillComfirmByBillId" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.BillComfirmDto">
        <include refid="selectBillComfirmVo"/>
        where bill_id = #{billId}
    </select>

    <insert id="insertBillComfirm" parameterType="com.xgwc.serviceProvider.entity.BillComfirm" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO bill_comfirm (
            bill_id,
            invoices,
            total_invoice_amount,
            provide_invoice,
            status,
            check_status,
            check_time,
            comment,
            user_id,
            create_by,
            create_time,
            update_by,
            update_time
        ) VALUES (
                     #{billId},
                     #{invoices},
                     #{totalInvoiceAmount},
                    #{provideInvoice},
                     0,
                     0,
                     #{checkTime},
                     #{comment},
                     #{userId},
                     #{createBy},
                     #{createTime},
                     #{updateBy},
                     #{updateTime}
                 )

    </insert>

    <update id="updateBillComfirm" parameterType="com.xgwc.serviceProvider.entity.BillComfirm">
        update bill_comfirm
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="totalInvoiceAmount != null">total_invoice_amount = #{totalInvoiceAmount},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="invoices != null">invoices = #{invoices},</if>
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <sql id="selectBillComfirmCheckVo">
        select bill_comfirm_id, check_status, check_user_id, check_user_name, comment, create_by, create_time, id, status, update_time from bill_comfirm_check
    </sql>

    <select id="selectBillComfirmCheckList" resultType="com.xgwc.serviceProvider.entity.dto.BillComfirmCheckDto">
        <include refid="selectBillComfirmCheckVo"/>
        <where>
            bill_comfirm_id = #{billComfirmId}
        </where>
    </select>

    <insert id="insertBillVoiceCheck" parameterType="com.xgwc.serviceProvider.entity.BillComfirmCheck" useGeneratedKeys="true" keyColumn="id">
        insert into bill_comfirm_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billComfirmId != null">bill_comfirm_id,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="checkUserId != null">check_user_id,</if>
            <if test="checkUserName != null">check_user_name,</if>
            <if test="comment != null">comment,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            status,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billComfirmId != null">#{billComfirmId},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="checkUserId != null">#{checkUserId},</if>
            <if test="checkUserName != null">#{checkUserName},</if>
            <if test="comment != null">#{comment},</if>
            <if test="createBy != null">#{createBy},</if>
            now(),
            0,
            now()
        </trim>
    </insert>

    <update id="updateBillVoiceCheck" parameterType="com.xgwc.serviceProvider.entity.BillComfirmCheck">
        update bill_comfirm_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="billComfirmId != null">bill_comfirm_id = #{billComfirmId},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="checkUserId != null">check_user_id = #{checkUserId},</if>
            <if test="checkUserName != null">check_user_name = #{checkUserName},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillComfirmDetail">
        delete from bill_comfirm_detail where bill_comfirm_id = #{billComfirmId}
    </delete>

    <insert id="batchInsertBillComfirmDetails" keyProperty="id" keyColumn="id" useGeneratedKeys="true">
        insert into bill_comfirm_detail(bill_comfirm_id, invoice_url, company_info_id, invoice_amount, purchaser_name, purchaser_tax_number, create_time, update_time)
            values
        <foreach collection="billComfirmDetails" item="item" separator=",">
            (#{item.billComfirmId}, #{item.invoiceUrl}, #{item.invoiceAmount}, #{item.companyInfoId},#{item.purchaserName}, #{item.purchaserTaxNumber}, now(), now())
        </foreach>
    </insert>

    <select id="selectBillComfirmCompanyList" resultType="com.xgwc.serviceProvider.entity.dto.BillCompanyInfoDto">
        SELECT
            t.total_commission amount,
            i.company_name purchaserName,
            i.license_code purchaserTaxNumber,
            i.id companyInfoId
        FROM
            bill_designer_sub t
                inner JOIN xgwc_company_info i ON i.id = t.company_info_id
        WHERE
            t.bill_id = #{billId}
          and t.total_commission > 0
    </select>


</mapper>