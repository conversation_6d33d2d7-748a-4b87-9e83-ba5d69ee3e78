<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.XgwcOrderMapper">

    <update id="lockOrders">
        update xgwc_order set is_lock = 1, lock_time = now(), lock_user = #{lockUser}
        where id in
        <foreach item="item" collection="orderIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
</mapper>