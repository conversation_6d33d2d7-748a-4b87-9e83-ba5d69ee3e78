<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.MarketEmployeesMapper">


    <sql id="selectEmployeesVo">
        select employee_id,staff_id, name, sex, ethnicity, birthdate,education, political_status, marital_status, id_number, email, phone, address, emer_name, emer_phone, entry_date, contract_end_date, social_status, probation_status, probation_end_date, annual_leave_days, resignation_date, create_by, create_time, update_by, update_time, modify_time from market_employees
    </sql>

    <delete id="deleteMarketEmpAttachments">
        DELETE FROM market_emp_attachments WHERE employee_id = #{employeeId}
    </delete>

    <select id="selectEmployeesList" parameterType="com.xgwc.serviceProvider.entity.vo.MarketEmployeesQueryVo" resultType="com.xgwc.serviceProvider.entity.dto.MarketEmployeesDto">
        SELECT
        xe.`employee_id` as employeeId,
        xe.`staff_id` as staffId,
        xe.`name`,
        xs.`stage_name` as stageName,
        xe.`sex`,
        xs.`job_nature` as jobNature,
        xs.`status`,
        xbs.`station_name` as stationName,
        xbd.`dept_name` as deptName,
        xe.`social_status` as socialStatus,
        xe.`phone`,
        xe.`entry_date` as entryDate,
        xe.`contract_end_date` as contractEndDate,
        xe.`resignation_date` as resignationDate,
        xe.`education`,
        xe.`birthdate`
        FROM `market_employees` xe
        LEFT JOIN market_emp_accounts ea on xe.employee_id = ea.employee_id
        LEFT JOIN xgwc_market_staff xs on xe.staff_id = xs.id
        LEFT JOIN market_dept xbd on xs.dept_id = xbd.dept_id
        LEFT JOIN market_station xbs on xs.post_id = xbs.station_id
        <where>
            xe.`market_id` = #{employees.marketId}

            <if test="employees.name != null and employees.name != ''">
                and (
                xe.name = #{employees.name}
                or xs.stage_name = #{employees.name}
                or xe.phone = #{employees.name}
                or xe.id_number = #{employees.name}
                or ea.account_number = #{employees.name}
                or ea.alipay_account = #{employees.name}
                )
            </if>
            <if test="employees.entryDate != null ">
                and xe.entry_date between #{employees.entryDate[0]} and #{employees.entryDate[1]}
            </if>
            <if test="employees.contractEndDate != null ">
                and xe.contract_end_date between #{employees.contractEndDate[0]} and #{employees.contractEndDate[1]}
            </if>
            <if test="employees.birthdate != null ">
                and xe.birthdate between #{employees.birthdate[0]} and #{employees.birthdate[1]}
            </if>
            <if test="employees.resignationDate != null ">
                and xe.resignation_date between #{employees.resignationDate[0]} and #{employees.resignationDate[1]}
            </if>
            <if test="employees.deptId != null">
                and xbd.`dept_id` = #{employees.deptId}
            </if>
            <if test="employees.stationId != null">
                and xbs.`station_id` = #{employees.stationId}
            </if>
            <if test="employees.jobNature != null">
                and xs.`job_nature` = #{employees.jobNature}
            </if>
            <if test="employees.status != null">
                and xs.`status` = #{employees.status}
            </if>
            <if test="employees.socialStatus != null">
                and xe.`social_status` = #{employees.socialStatus}
            </if>
            <if test="employees.education != null">
                and xe.`education` = #{employees.education}
            </if>
        </where>
        ORDER BY xe.`employee_id` DESC
    </select>

    <select id="selectEmployeesByEmployeeId" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.MarketEmployeesDto">
        select
            xe.employee_id as employeeId,
            xe.staff_id as staffId,
            xe.`name`,
            xe.sex,
            xe.ethnicity,
            xe.birthdate,
            xe.education,
            xe.political_status as politicalStatus,
            xe.marital_status as maritalStatus,
            xe.id_number as idNumber,
            xe.email,
            xe.phone,
            xe.address,
            xe.emer_name as emerName,
            xe.emer_phone as emerPhone,
            xe.entry_date as entryDate,
            xe.contract_end_date as contractEndDate,
            xe.social_status as socialStatus,
            xe.buy_social_date as buySocialDate,
            xe.probation_status as probationStatus,
            xe.probation_end_date as probationEndDate,
            xe.annual_leave_days as annualLeaveDays,
            xe.resignation_date as resignationDate,
            eac.account_name as accountName,
            eac.bank_name as bankName,
            eac.account_number as accountNumber,
            eac.alipay_name as alipayName,
            eac.alipay_account as alipayAccount
        from market_employees xe
                 LEFT JOIN market_emp_accounts eac on xe.employee_id = eac.employee_id
        where xe.staff_id = #{employeeId}
    </select>
    <select id="selectMarketEmpAttachments" resultType="com.xgwc.serviceProvider.entity.MarketEmpAttachments">
            select
                eat.attachment_id as attachmentId,
                eat.employee_id as employeeId,
                eat.attachment_type as attachmentType,
                eat.file_name as fileName,
                eat.file_path as filePath,
                eat.upload_date as uploadDate
            from market_employees xe
                     LEFT JOIN market_emp_attachments eat on xe.employee_id = eat.employee_id
            where xe.employee_id=#{employeeId}
    </select>

    <!-- 身份证号校验 -->
    <select id="existsByIdNumber" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM market_employees
        WHERE id_number = #{idNumber}
    </select>

    <!-- 手机号校验 -->
    <select id="existsByPhone" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM market_employees
        WHERE phone = #{phone}
    </select>

    <!-- 银行卡号校验 -->
    <select id="existsByAccountNumber" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM market_emp_accounts
        WHERE account_number = #{accountNumber}
    </select>

    <!-- 支付宝账号校验 -->
    <select id="existsByAlipayAccount" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM market_emp_accounts
        WHERE alipay_account = #{alipayAccount}
    </select>

    <insert id="insertEmployees" parameterType="com.xgwc.serviceProvider.entity.MarketEmployees" useGeneratedKeys="true" keyProperty="employeeId">
        insert into market_employees
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="marketEmployees.employeeId != null">employee_id,</if>
            <if test="marketEmployees.staffId != null">staff_id,</if>
            <if test="marketEmployees.accountId != null">account_id,</if>
            <if test="marketEmployees.attachmentId != null">attachment_id,</if>
            <if test="marketEmployees.marketId != null">market_id,</if>
            <if test="marketEmployees.name != null">`name`,</if>
            <if test="marketEmployees.sex != null">sex,</if>
            <if test="marketEmployees.ethnicity != null">ethnicity,</if>
            <if test="marketEmployees.birthdate != null">birthdate,</if>
            <if test="marketEmployees.education != null">education,</if>
            <if test="marketEmployees.politicalStatus != null">political_status,</if>
            <if test="marketEmployees.maritalStatus != null">marital_status,</if>
            <if test="marketEmployees.idNumber != null">id_number,</if>
            <if test="marketEmployees.email != null">email,</if>
            <if test="marketEmployees.phone != null">phone,</if>
            <if test="marketEmployees.address != null">address,</if>
            <if test="marketEmployees.emerName != null">emer_name,</if>
            <if test="marketEmployees.emerPhone != null">emer_phone,</if>
            <if test="marketEmployees.entryDate != null">entry_date,</if>
            <if test="marketEmployees.contractEndDate != null">contract_end_date,</if>
            <if test="marketEmployees.socialStatus != null">social_status,</if>
            <if test="marketEmployees.buySocialDate != null">buy_social_date,</if>
            <if test="marketEmployees.probationStatus != null">probation_status,</if>
            <if test="marketEmployees.probationEndDate != null">probation_end_date,</if>
            <if test="marketEmployees.annualLeaveDays != null">annual_leave_days,</if>
            <if test="marketEmployees.resignationDate != null">resignation_date,</if>
            <if test="marketEmployees.createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="marketEmployees.employeeId != null">#{marketEmployees.employeeId},</if>
            <if test="marketEmployees.staffId != null">#{marketEmployees.staffId},</if>
            <if test="marketEmployees.accountId != null">#{marketEmployees.accountId},</if>
            <if test="marketEmployees.attachmentId != null">#{marketEmployees.attachmentId},</if>
            <if test="marketEmployees.marketId != null">#{marketEmployees.marketId},</if>
            <if test="marketEmployees.name != null">#{marketEmployees.name},</if>
            <if test="marketEmployees.sex != null">#{marketEmployees.sex},</if>
            <if test="marketEmployees.ethnicity != null">#{marketEmployees.ethnicity},</if>
            <if test="marketEmployees.birthdate != null">#{marketEmployees.birthdate},</if>
            <if test="marketEmployees.education != null">#{marketEmployees.education},</if>
            <if test="marketEmployees.politicalStatus != null">#{marketEmployees.politicalStatus},</if>
            <if test="marketEmployees.maritalStatus != null">#{marketEmployees.maritalStatus},</if>
            <if test="marketEmployees.idNumber != null">#{marketEmployees.idNumber},</if>
            <if test="marketEmployees.email != null">#{marketEmployees.email},</if>
            <if test="marketEmployees.phone != null">#{marketEmployees.phone},</if>
            <if test="marketEmployees.address != null">#{marketEmployees.address},</if>
            <if test="marketEmployees.emerName != null">#{marketEmployees.emerName},</if>
            <if test="marketEmployees.emerPhone != null">#{marketEmployees.emerPhone},</if>
            <if test="marketEmployees.entryDate != null">#{marketEmployees.entryDate},</if>
            <if test="marketEmployees.contractEndDate != null">#{marketEmployees.contractEndDate},</if>
            <if test="marketEmployees.socialStatus != null">#{marketEmployees.socialStatus},</if>
            <if test="marketEmployees.buySocialDate != null">#{marketEmployees.buySocialDate},</if>
            <if test="marketEmployees.probationStatus != null">#{marketEmployees.probationStatus},</if>
            <if test="marketEmployees.probationEndDate != null">#{marketEmployees.probationEndDate},</if>
            <if test="marketEmployees.annualLeaveDays != null">#{marketEmployees.annualLeaveDays},</if>
            <if test="marketEmployees.resignationDate != null">#{marketEmployees.resignationDate},</if>
            <if test="marketEmployees.createBy != null">#{marketEmployees.createBy},</if>
            now()
         </trim>
    </insert>

    <insert id="insertMarketEmpAccounts" parameterType="com.xgwc.serviceProvider.entity.MarketEmpAccounts" useGeneratedKeys="true" keyProperty="accountId">
        insert into market_emp_accounts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="marketEmpAccounts.accountName != null">account_name,</if>
            <if test="marketEmpAccounts.employeeId != null">employee_id,</if>
            <if test="marketEmpAccounts.bankName != null">bank_name,</if>
            <if test="marketEmpAccounts.accountNumber != null">account_number,</if>
            <if test="marketEmpAccounts.alipayName != null">alipay_name,</if>
            <if test="marketEmpAccounts.alipayAccount != null">alipay_account,</if>
            <if test="marketEmpAccounts.createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="marketEmpAccounts.accountName != null">#{marketEmpAccounts.accountName},</if>
            <if test="marketEmpAccounts.employeeId != null">#{marketEmpAccounts.employeeId},</if>
            <if test="marketEmpAccounts.bankName != null">#{marketEmpAccounts.bankName},</if>
            <if test="marketEmpAccounts.accountNumber != null">#{marketEmpAccounts.accountNumber},</if>
            <if test="marketEmpAccounts.alipayName != null">#{marketEmpAccounts.alipayName},</if>
            <if test="marketEmpAccounts.alipayAccount != null">#{marketEmpAccounts.alipayAccount},</if>
            <if test="marketEmpAccounts.createBy != null">#{marketEmpAccounts.createBy},</if>
            now()
        </trim>
    </insert>

    <insert id="insertMarketEmpAttachments" parameterType="com.xgwc.serviceProvider.entity.MarketEmpAttachments" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into market_emp_attachments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="marketEmpAttachments.employeeId != null">employee_id,</if>
            <if test="marketEmpAttachments.attachmentType != null">attachment_type,</if>
            <if test="marketEmpAttachments.fileName != null">file_name,</if>
            <if test="marketEmpAttachments.filePath != null">file_path,</if>
            <if test="marketEmpAttachments.fileCount != null">file_count,</if>
            upload_date,
            <if test="marketEmpAttachments.createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="marketEmpAttachments.employeeId != null">#{marketEmpAttachments.employeeId},</if>
            <if test="marketEmpAttachments.attachmentType != null">#{marketEmpAttachments.attachmentType},</if>
            <if test="marketEmpAttachments.fileName != null">#{marketEmpAttachments.fileName},</if>
            <if test="marketEmpAttachments.filePath != null">#{marketEmpAttachments.filePath},</if>
            <if test="marketEmpAttachments.fileCount != null">#{marketEmpAttachments.fileCount},</if>
            now(),
            <if test="marketEmpAttachments.createBy != null">#{marketEmpAttachments.createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateEmployees" parameterType="com.xgwc.serviceProvider.entity.MarketEmployees">
        update market_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="marketEmployees.name != null">name = #{marketEmployees.name},</if>
            <if test="marketEmployees.staffId != null">staff_id = #{marketEmployees.staffId},</if>
            <if test="marketEmployees.accountId != null">account_id = #{marketEmployees.accountId},</if>
            <if test="marketEmployees.attachmentId != null">attachment_id = #{marketEmployees.attachmentId},</if>
            <if test="marketEmployees.sex != null">sex = #{marketEmployees.sex},</if>
            <if test="marketEmployees.ethnicity != null">ethnicity = #{marketEmployees.ethnicity},</if>
            <if test="marketEmployees.birthdate != null">birthdate = #{marketEmployees.birthdate},</if>
            <if test="marketEmployees.education != null">education = #{marketEmployees.education},</if>
            <if test="marketEmployees.politicalStatus != null">political_status = #{marketEmployees.politicalStatus},</if>
            <if test="marketEmployees.maritalStatus != null">marital_status = #{marketEmployees.maritalStatus},</if>
            <if test="marketEmployees.idNumber != null">id_number = #{marketEmployees.idNumber},</if>
            <if test="marketEmployees.email != null">email = #{marketEmployees.email},</if>
            <if test="marketEmployees.phone != null">phone = #{marketEmployees.phone},</if>
            <if test="marketEmployees.address != null">address = #{marketEmployees.address},</if>
            <if test="marketEmployees.emerName != null">emer_name = #{marketEmployees.emerName},</if>
            <if test="marketEmployees.emerPhone != null">emer_phone = #{marketEmployees.emerPhone},</if>
            <if test="marketEmployees.entryDate != null">entry_date = #{marketEmployees.entryDate},</if>
            <if test="marketEmployees.contractEndDate != null">contract_end_date = #{marketEmployees.contractEndDate},</if>
            <if test="marketEmployees.socialStatus != null">social_status = #{marketEmployees.socialStatus},</if>
            <if test="marketEmployees.buySocialDate != null">buy_social_date = #{marketEmployees.buySocialDate},</if>
            <if test="marketEmployees.probationStatus != null">probation_status = #{marketEmployees.probationStatus},</if>
            <if test="marketEmployees.probationEndDate != null">probation_end_date = #{marketEmployees.probationEndDate},</if>
            <if test="marketEmployees.annualLeaveDays != null">annual_leave_days = #{marketEmployees.annualLeaveDays},</if>
            <if test="marketEmployees.resignationDate != null">resignation_date = #{marketEmployees.resignationDate},</if>
            <if test="marketEmployees.updateBy != null">update_by = #{marketEmployees.updateBy},</if>
            <if test="marketEmployees.updateTime != null">update_time = now(),</if>
        </trim>
        where employee_id = #{marketEmployees.employeeId}
    </update>
    <update id="updateMarketEmpAccounts" parameterType="com.xgwc.serviceProvider.entity.MarketEmpAccounts">
        update market_emp_accounts
        <trim prefix="SET" suffixOverrides=",">
            <if test="marketEmpAccounts.accountName != null">account_name = #{marketEmpAccounts.accountName},</if>
            <if test="marketEmpAccounts.bankName != null">bank_name = #{marketEmpAccounts.bankName},</if>
            <if test="marketEmpAccounts.accountNumber != null">account_number = #{marketEmpAccounts.accountNumber},</if>
            <if test="marketEmpAccounts.alipayName != null">alipay_name = #{marketEmpAccounts.alipayName},</if>
            <if test="marketEmpAccounts.alipayAccount != null">alipay_account = #{marketEmpAccounts.alipayAccount},</if>
            <if test="marketEmpAccounts.updateBy != null">update_by = #{marketEmpAccounts.updateBy},</if>
            <if test="marketEmpAccounts.updateTime != null">update_time = now(),</if>
        </trim>
            where employee_id = #{marketEmpAccounts.employeeId}
    </update>
    <update id="updateMarketEmpAttachments" parameterType="com.xgwc.serviceProvider.entity.MarketEmpAttachments">
        update market_emp_attachments
        <trim prefix="SET" suffixOverrides=",">
            <if test="marketEmpAttachments.attachmentType != null">attachment_type = #{marketEmpAttachments.attachmentType},</if>
            <if test="marketEmpAttachments.fileName != null">file_name = #{marketEmpAttachments.fileName},</if>
            <if test="marketEmpAttachments.filePath != null">file_path = #{marketEmpAttachments.filePath},</if>
            <if test="marketEmpAttachments.uploadDate != null">upload_date = #{marketEmpAttachments.uploadDate},</if>
            <if test="marketEmpAttachments.updateBy != null">update_by = #{marketEmpAttachments.updateBy},</if>
            <if test="marketEmpAttachments.updateTime != null">update_time = now(),</if>
        </trim>
            where employee_id = #{marketEmpAttachments.employeeId} and attachment_type = #{marketEmpAttachments.attachmentType}
    </update>
</mapper>