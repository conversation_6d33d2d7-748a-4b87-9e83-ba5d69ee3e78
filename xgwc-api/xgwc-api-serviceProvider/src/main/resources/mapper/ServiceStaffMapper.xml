<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.ServiceStaffMapper">
    

    <sql id="selectServiceStaffVo">
        select id, name, stage_name, dept_id, post_id, role_ids, job_nature, status, is_principal, superior, bind_status, bind_user_id, login_phone, service_owner_id, is_del, create_by, create_time, update_by, update_time, modify_time,resignation_time from xgwc_service_staff
    </sql>

    <select id="selectServiceStaffList" parameterType="com.xgwc.serviceProvider.entity.vo.ServiceStaffQueryVo" resultType="com.xgwc.serviceProvider.entity.dto.ServiceStaffDto">
        select t.id
             ,t.name
             ,t.stage_name
             ,t.dept_id
             ,t.post_id
             ,t.role_ids
             ,t.job_nature
             ,t.status,bind_status
             ,t.bind_user_id
             ,t.login_phone
             ,t.service_owner_id
             ,t.create_time
             ,t.resignation_time
             ,d.dept_name
             ,p.station_name
        from xgwc_service_staff t
        left join service_dept d on t.dept_id = d.dept_id
        left join service_station p on t.post_id = p.station_id
        <where>
            <if test="id != null "> and t.id = #{id}</if>
            <if test="deptId != null and deptIds.size() > 0">
                and t.dept_id in
                <foreach item="id" collection="deptIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="postId != null "> and t.post_id = #{postId}</if>
            <if test="jobNature != null "> and t.job_nature = #{jobNature}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="bindStatus != null "> and t.bind_status = #{bindStatus}</if>
            <if test="serviceOwnerId != null "> and t.service_owner_id = #{serviceOwnerId}</if>
            <if test="isDel == null"> and t.is_del = 0</if>
            <if test="isDel != null "> and t.is_del = #{isDel}</if>
            <if test="name != null ">and t.name like concat('%', #{name}, '%')</if>
            <if test="stageName != null ">and t.stage_name like concat('%', #{stageName}, '%')</if>
            <if test="loginPhone != null ">and t.login_phone like concat('%', #{loginPhone}, '%')</if>
            <if test="other != null  and other != ''">
                and (
                t.name like concat('%', #{other}, '%')
                or t.stage_name like concat('%', #{other}, '%')
                or t.login_phone like concat('%', #{other}, '%')
                )
            </if>
            <if test="startTime != null and endTime != null">
                and t.create_time BETWEEN CONCAT(#{startTime}, ' 00:00:00') AND CONCAT(#{endTime}, ' 23:59:59')
            </if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectServiceStaffById" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.ServiceStaffDto">
        <include refid="selectServiceStaffVo"/>
        where id = #{id}
    </select>
    <select id="selectStaffListDropDown" resultType="com.xgwc.serviceProvider.entity.dto.ServiceStaffDto">
        select t.id, t.name, t.stage_name, t.bind_user_id
        from xgwc_service_staff t
        <where>
            t.service_owner_id = #{serviceId}
            and t.`status` = 0
            and t.is_del = 0
            and t.bind_status = 1
            <if test="deptId != null">
                and t.dept_id = #{deptId}
            </if>
        </where>
    </select>

    <select id="findByStaffNameAndServiceId" resultType="com.xgwc.serviceProvider.entity.dto.ServiceStaffDto">
        <include refid="selectServiceStaffVo"/>
        where name = #{staffName}
        and service_owner_id = #{serviceId}
        and is_del = 0
    </select>

    <insert id="insertServiceStaff" parameterType="com.xgwc.serviceProvider.entity.ServiceStaff">
        insert into xgwc_service_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="stageName != null">stage_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="postId != null">post_id,</if>
            <if test="roleIds != null">role_ids,</if>
            <if test="jobNature != null">job_nature,</if>
            <if test="status != null">status,</if>
            <if test="isPrincipal != null">is_principal,</if>
            <if test="superior != null">superior,</if>
            <if test="bindStatus != null">bind_status,</if>
            <if test="bindUserId != null">bind_user_id,</if>
            <if test="loginPhone != null">login_phone,</if>
            <if test="serviceOwnerId != null">service_owner_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="resignationTime != null">resignation_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="stageName != null">#{stageName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="postId != null">#{postId},</if>
            <if test="roleIds != null">#{roleIds},</if>
            <if test="jobNature != null">#{jobNature},</if>
            <if test="status != null">#{status},</if>
            <if test="isPrincipal != null">#{isPrincipal},</if>
            <if test="superior != null">#{superior},</if>
            <if test="bindStatus != null">#{bindStatus},</if>
            <if test="bindUserId != null">#{bindUserId},</if>
            <if test="loginPhone != null">#{loginPhone},</if>
            <if test="serviceOwnerId != null">#{serviceOwnerId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="resignationTime != null">#{resignationTime},</if>
         </trim>
    </insert>

    <update id="updateServiceStaff" parameterType="com.xgwc.serviceProvider.entity.ServiceStaff">
        update xgwc_service_staff
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="stageName != null">stage_name = #{stageName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="postId != null">post_id = #{postId},</if>
            <if test="roleIds != null">role_ids = #{roleIds},</if>
            <if test="jobNature != null">job_nature = #{jobNature},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isPrincipal != null">is_principal = #{isPrincipal},</if>
            <if test="superior != null">superior = #{superior},</if>
            <if test="bindStatus != null">bind_status = #{bindStatus},</if>
            <if test="bindUserId != null">bind_user_id = #{bindUserId},</if>
            <if test="loginPhone != null">login_phone = #{loginPhone},</if>
            <if test="serviceOwnerId != null">service_owner_id = #{serviceOwnerId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="resignationTime != null">resignation_time = #{resignationTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteServiceStaffById" parameterType="Long">
        update xgwc_service_staff set is_del = 1 where id = #{id}
    </update>

    <update id="deleteServiceStaffByIds" parameterType="String">
        update xgwc_service_staff set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>