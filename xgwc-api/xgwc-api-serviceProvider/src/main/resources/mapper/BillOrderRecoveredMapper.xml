<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.CommissionRecoveredMapper">
    <select id="getBrandCommissionRecoveredList"
            resultType="com.xgwc.serviceProvider.entity.dto.CommissionBackDetail">
        SELECT
            m.id,
            t.bill_id billId,
            t.bill_period_start as billStart,
            t.bill_period_end as billEnd,
            t.stylist_name as designerName,
            d.phone as designerPhone,
            t.real_commission_back as backMoney,
            t.commission_back_remaining as backRemaining,
            t.commission_back as commissionBack,
            m.create_time backTime
        FROM bill_designer t
        LEFT JOIN bill_order_modify_record r ON t.bill_id = r.bill_id
        LEFT JOIN bill_order_modify m ON r.modify_id = m.id
        LEFT JOIN xgwc_designer d ON t.stylist_id = d.designer_id
        <where>
        t.brand_owner_id in
            <foreach item="id" collection="brandIds" open="(" separator="," close=")">
                #{id}
            </foreach>

        <if test="brandId != null"> and t.brand_owner_id = #{brandId}</if>
        <if test="designerId != null"> and t.stylist_id = #{designerId}</if>
        <if test="backId != null"> and (m.id = #{backId} OR t.bill_id = #{backId})</if>
        <if test="backTime != null"> and m.create_time between #{backTime[0]} and #{backTime[1]} </if>
        and t.real_commission_back IS NOT NULL
        ORDER BY m.id DESC
        </where>
    </select>
    <select id="selectCommissionRecoveredDetail"
            resultType="com.xgwc.serviceProvider.entity.dto.CommissionBackDto">
        SELECT
            r.id as backId,
            m.id as recordId,
            m.order_no as orderNo,
            m.taobao_id as taobaoId,
            m.money,
            m.now_money as nowMoney,
            m.commission_backed as commissionBacked,
            m.create_time as backTime
        FROM bill_order_modify_record r
        LEFT JOIN bill_order_modify m ON r.modify_id = m.id
        WHERE m.bill_id = #{billId}
    </select>
</mapper>