<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.FinanceOfflinePaymentsMapper">

    <select id="selectFinanceOfflinePaymentsList" parameterType="com.xgwc.serviceProvider.entity.param.FinanceOfflinePaymentsParam" resultType="com.xgwc.serviceProvider.entity.dto.FinanceOfflinePaymentsDto">
        SELECT
        op.id,
        fop.order_date as orderDate,
        fop.id as orderId,
        fop.order_no as orderNo,
        fop.taobao_id as customerId,
        fop.franchise_id as franchiseId,
        CONCAT(
        xbo.company_name,
        '-',
        ifnull(fo.company_simple_name, fo.company_name)

        ) AS franchiseName,
        fop.store_name as shopName,
        fop.sale_man_name as contact<PERSON>erson,
        op.collection_no as paymentNumber,
        op.amount as paymentAmount,
        off.amount as actualAmount,
        off.difference_amount as differenceAmount,
        op.pay_channel as paymentMethod,
        off.company_info_id as companyInfoId,
        ifnull(sd.dict_label, ifnull(xci.company_simple_name, xci.company_name)) as companyName,
        off.payment_flow_number as paymentFlowNumber,
        ifnull(off.payment_status, 0) as paymentStatus,
        ifnull(off.archival_status, 0) as archivalStatus
        FROM     xgwc_order_pay op
        LEFT JOIN xgwc_order fop ON fop.id = op.oder_id
        LEFT JOIN xgwc_shop xs ON fop.store_id = xs.shop_id
        LEFT JOIN franchise_owner fo ON fo.franchise_id = fop.franchise_id and fop.brand_id = fo.brand_id
        LEFT JOIN xgwc_brand_owner xbo ON xbo.brand_id = fop.brand_id
        LEFT JOIN finance_offline_payments off ON op.id = off.pay_id
        LEFT JOIN xgwc_company_info xci ON xci.id = off.company_info_id
        LEFT JOIN sys_dict_data sd ON sd.dict_code = off.payment_code_body
        <where>
            fop.brand_id = #{brandId}
            <if test="archivalStatus != null">
                <choose>
                    <when test="archivalStatus == 1">
                        and off.archival_status = 1
                    </when>
                    <otherwise>
                        and (off.archival_status = 0 or off.archival_status IS NULL)
                    </otherwise>
                </choose>
            </if>
            <if test="companyInfoId != null">
                and off.company_info_id = #{companyInfoId}
            </if>
            <if test="differenceAmount != null">
                <choose>
                    <when test="differenceAmount == 0">
                        and off.difference_amount = 0
                    </when>
                    <when test="differenceAmount == 1">
                        and off.difference_amount != 0
                    </when>
                </choose>
            </if>
            <if test="franchiseId != null ">
                and fop.franchise_id = #{franchiseId}
            </if>
            <if test="orderId != null  and orderId != ''">
                and (fop.order_no = #{orderId}
                OR op.collection_no = #{orderId}
                OR off.payment_flow_number = #{orderId})
            </if>
            <if test="paymentMethod != null ">
                and op.pay_channel = #{paymentMethod}
            </if>
            <if test="paymentStatus != null">
                <choose>
                    <when test="paymentStatus == 1">
                        and off.payment_status = 1
                    </when>
                    <otherwise>
                        and (off.payment_status = 0 or off.payment_status IS NULL)
                    </otherwise>
                </choose>
            </if>
            <if test="shopId != null ">
                and fop.store_id = #{shopId}
            </if>
            <if test="orderDate != null ">
                <bind name="updateTimeStart" value="orderDate[0]"/>
                <bind name="updateTimeEnd" value="orderDate[1]"/>
                and fop.order_date between #{updateTimeStart} and #{updateTimeEnd}
            </if>
            <if test="shopManagerId != null ">
                and xs.manager_id = #{shopManagerId}
            </if>
            and fop.id IS NOT NULL
        ORDER BY
            fop.order_date DESC
        </where>
    </select>

    <select id="selectFinanceOfflinePaymentsById" parameterType="Long" resultType="com.xgwc.serviceProvider.entity.dto.FinanceOfflinePaymentsDto">
        SELECT
            off.id,
            op.id as payId,
            fop.id as orderId,
            op.pay_img as paymentScreenshot,
            fop.brand_id as brandId,
            op.amount as paymentAmount,
            off.amount as actualAmount,
            off.company_info_id as companyInfoId,
            ifnull(sd.dict_label, ifnull(xci.company_simple_name, xci.company_name)) as companyName,
            off.payment_flow_number as paymentFlowNumber,
            op.pay_channel as paymentMethod,
            op.collection_no as paymentNumber,
            ifnull(off.payment_status, 0) as paymentStatus,
            off.remarks
        FROM
            xgwc_order_pay op
                     LEFT JOIN xgwc_order fop ON fop.id = op.oder_id
                     LEFT JOIN finance_offline_payments off ON op.id = off.pay_id
                     LEFT JOIN xgwc_company_info xci ON xci.id = off.company_info_id
                     LEFT JOIN sys_dict_data sd ON sd.dict_code = off.payment_code_body
        where op.id = #{id}
    </select>
    <select id="selectPayFlowNumber" resultType="java.lang.Integer">
        SELECT
            1
        FROM
            finance_offline_payments
        WHERE
            payment_flow_number = #{paymentFlowNumber}
        LIMIT 1
    </select>
    <select id="selectArchivalStatusByOrderId" resultType="java.lang.Integer">
        SELECT
            1
        FROM
            finance_offline_payments
        WHERE
            order_id = #{orderId}
            AND archival_status = 0
        LIMIT 1
    </select>

    <insert id="updateFinanceOfflinePayments" parameterType="com.xgwc.serviceProvider.entity.FinanceOfflinePayments">
        INSERT INTO finance_offline_payments (
            id,
            amount,
            archival_status,
            company_info_id,
            payment_code_body,
            create_by,
            create_time,
            difference_amount,
            pay_id,
            order_id,
            payment_flow_number,
            payment_status,
            remarks,
            update_by,
            update_time
        ) VALUES (
                     #{id},
                     #{amount},
                     #{archivalStatus},
                     #{companyInfoId},
                     #{paymentCodeBody},
                     #{createBy},
                     #{createTime},
                     #{differenceAmount},
                     #{payId},
                     #{orderId},
                     #{paymentFlowNumber},
                     #{paymentStatus},
                     #{remarks},
                     #{updateBy},
                     #{updateTime}
                 )
            ON DUPLICATE KEY UPDATE
                                 archival_status = VALUES(archival_status),
                                 amount = VALUES(amount),
                                 company_info_id = VALUES(company_info_id),
                                 payment_code_body = VALUES(payment_code_body),
                                 difference_amount = VALUES(difference_amount),
                                 order_id = VALUES(order_id),
                                 pay_id = VALUES(pay_id),
                                 payment_flow_number = VALUES(payment_flow_number),
                                 payment_status = VALUES(payment_status),
                                 remarks = VALUES(remarks),
                                 update_by = VALUES(update_by),
                                 update_time = VALUES(update_time)
    </insert>
</mapper>