<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.ServiceRoleMapper">
    <insert id="saveServiceRole" useGeneratedKeys="true" keyProperty="roleId">
        INSERT INTO `xgwc_sass`.`service_role`(`role_name`,`is_flag`,`service_id`,`sort`,`create_by`,`create_time`)
        VALUES (#{serviceRoleDto.roleName},#{serviceRoleDto.isFlag},#{serviceRoleDto.serviceId},
                #{serviceRoleDto.sort},#{serviceRoleDto.createBy},now())
    </insert>

    <insert id="saveRoleMenu">
        INSERT INTO `xgwc_sass`.`service_role_menu` (`role_id`, `menu_id`, `create_by`, `create_time`)
        VALUES
        <foreach collection="sysRoleMenuVoList" item="roleMenu" separator=",">
            (#{roleMenu.roleId}, #{roleMenu.menuId}, #{roleMenu.createBy}, now())
        </foreach>
    </insert>

    <update id="updateServiceRole">
        UPDATE `xgwc_sass`.`service_role`
        <set>
            <if test="serviceRoleDto.roleName != null and serviceRoleDto.roleName != ''">
                `role_name` = #{serviceRoleDto.roleName},
            </if>
            <if test="serviceRoleDto.isFlag != null and serviceRoleDto.isFlag != ''">
                `is_flag` = #{serviceRoleDto.isFlag},
            </if>
            <if test="serviceRoleDto.serviceId != null">
                `service_id` = #{serviceRoleDto.serviceId},
            </if>
            <if test="serviceRoleDto.sort != null">
                `sort` = #{serviceRoleDto.sort},
            </if>
            <if test="serviceRoleDto.updateBy != null">
                `update_by` = #{serviceRoleDto.updateBy},
            </if>
            `update_time` = now(),
        </set>
        WHERE `role_id` = #{serviceRoleDto.roleId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`service_role`
        SET `status` = #{status}
        WHERE `role_id` = #{roleId}
    </update>
    <delete id="deleteRoleMenu">
        DELETE FROM `xgwc_sass`.`service_role_menu`
        WHERE `role_id` = #{roleId}
    </delete>

    <select id="getServiceRoleList" resultType="com.xgwc.serviceProvider.entity.vo.ServiceRoleVo">
        SELECT
        `role_id` AS roleId,
        `role_name` AS roleName,
        `service_id` AS serviceId,
        `is_flag` AS isFlag,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`service_role`
        <where>
            `service_id` = #{serviceRoleParam.serviceId}

            <if test="serviceRoleParam.roleName != null and serviceRoleParam.roleName != ''">
                AND `role_name` = #{serviceRoleParam.roleName}
            </if>
            <if test="serviceRoleParam.status != null">
                AND `status` = #{serviceRoleParam.status}
            </if>
        </where>
        ORDER BY `sort`
    </select>
    <select id="getServiceRoleById" resultType="com.xgwc.serviceProvider.entity.vo.ServiceRoleVo">
        SELECT `role_id`     AS roleId,
               `role_name`   AS roleName,
               `is_flag`     AS isFlag,
               `service_id` AS serviceId,
               `sort`,
               `status`,
               `is_del`      AS isDel,
               `create_by`   AS createBy,
               `create_time` AS createTime,
               `update_by`   AS updateBy,
               `update_time` AS updateTime,
               `modify_time` AS modifyTime
        FROM `xgwc_sass`.`service_role`
        WHERE `role_id` = #{roleId}
    </select>
    <select id="getServiceRoleMenusById" resultType="com.xgwc.serviceProvider.entity.vo.ServiceRoleMenuVo">
        SELECT
            frd.`id` AS id,
            frd.`role_id` AS roleId,
            frd.`menu_id` AS menuId,
            sm.pid,
            sm.`name` AS menuName
        FROM `xgwc_sass`.`service_role_menu` frd
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frd.menu_id
        WHERE `role_id` = #{roleId}
    </select>
    <select id="selectLastLevelMenu" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM sys_menu
        WHERE pid = #{menuId} and is_del = 0 and FIND_IN_SET('serviceAdmin',model_type)
    </select>
</mapper>