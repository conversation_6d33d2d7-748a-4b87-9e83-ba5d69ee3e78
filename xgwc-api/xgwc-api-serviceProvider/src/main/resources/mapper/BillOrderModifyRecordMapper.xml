<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.BillOrderModifyRecordMapper">

    <insert id="batchInsertBillOrderModifyRecordList">
        insert into bill_order_modify_record(modify_id, bill_start, bill_end, sub_bill_id, bill_id, commission_back, create_time, update_time)
            values
        <foreach collection="list" item="item" separator=",">
            (#{item.modifyId}, #{item.billStart}, #{item.billEnd},  #{item.subBillId},  #{item.billId}, #{item.commissionBack}, now(), now())
        </foreach>
    </insert>


</mapper>