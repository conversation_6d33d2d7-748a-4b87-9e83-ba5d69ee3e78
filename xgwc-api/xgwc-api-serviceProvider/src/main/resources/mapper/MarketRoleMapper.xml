<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.MarketRoleMapper">
    <insert id="saveMarketRole" useGeneratedKeys="true" keyProperty="roleId">
        INSERT INTO `xgwc_sass`.`market_role`(`role_name`,`is_flag`,`market_id`,`sort`,`create_by`,`create_time`)
        VALUES (#{marketRoleDto.roleName},#{marketRoleDto.isFlag},#{marketRoleDto.marketId},
                #{marketRoleDto.sort},#{marketRoleDto.createBy},now())
    </insert>

    <insert id="saveRoleMenu">
        INSERT INTO `xgwc_sass`.`market_role_menu` (`role_id`, `menu_id`, `create_by`, `create_time`)
        VALUES
        <foreach collection="sysRoleMenuVoList" item="roleMenu" separator=",">
            (#{roleMenu.roleId}, #{roleMenu.menuId}, #{roleMenu.createBy}, now())
        </foreach>
    </insert>

    <update id="updateMarketRole">
        UPDATE `xgwc_sass`.`market_role`
        <set>
            <if test="marketRoleDto.roleName != null and marketRoleDto.roleName != ''">
                `role_name` = #{marketRoleDto.roleName},
            </if>
            <if test="marketRoleDto.isFlag != null and marketRoleDto.isFlag != ''">
                `is_flag` = #{marketRoleDto.isFlag},
            </if>
            <if test="marketRoleDto.marketId != null">
                `market_id` = #{marketRoleDto.marketId},
            </if>
            <if test="marketRoleDto.sort != null">
                `sort` = #{marketRoleDto.sort},
            </if>
            <if test="marketRoleDto.updateBy != null">
                `update_by` = #{marketRoleDto.updateBy},
            </if>
            `update_time` = now(),
        </set>
        WHERE `role_id` = #{marketRoleDto.roleId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`market_role`
        SET `status` = #{status}
        WHERE `role_id` = #{roleId}
    </update>
    <delete id="deleteRoleMenu">
        DELETE FROM `xgwc_sass`.`market_role_menu`
        WHERE `role_id` = #{roleId}
    </delete>

    <select id="getMarketRoleList" resultType="com.xgwc.serviceProvider.entity.vo.ServiceRoleVo">
        SELECT
        `role_id` AS roleId,
        `role_name` AS roleName,
        `market_id` AS marketId,
        `is_flag` AS isFlag,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`market_role`
        <where>
            `market_id` = #{marketRoleParam.marketId}

            <if test="marketRoleParam.roleName != null and marketRoleParam.roleName != ''">
                AND `role_name` = #{marketRoleParam.roleName}
            </if>
            <if test="marketRoleParam.status != null">
                AND `status` = #{marketRoleParam.status}
            </if>
        </where>
        ORDER BY `sort`
    </select>
    <select id="getMarketRoleById" resultType="com.xgwc.serviceProvider.entity.vo.ServiceRoleVo">
        SELECT `role_id`     AS roleId,
               `role_name`   AS roleName,
               `is_flag`     AS isFlag,
               `market_id` AS marketId,
               `sort`,
               `status`,
               `is_del`      AS isDel,
               `create_by`   AS createBy,
               `create_time` AS createTime,
               `update_by`   AS updateBy,
               `update_time` AS updateTime,
               `modify_time` AS modifyTime
        FROM `xgwc_sass`.`market_role`
        WHERE `role_id` = #{roleId}
    </select>
    <select id="getMarketRoleMenusById" resultType="com.xgwc.serviceProvider.entity.vo.ServiceRoleMenuVo">
        SELECT
            frd.`id` AS id,
            frd.`role_id` AS roleId,
            frd.`menu_id` AS menuId,
            sm.pid,
            sm.`name` AS menuName
        FROM `xgwc_sass`.`market_role_menu` frd
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frd.menu_id
        WHERE `role_id` = #{roleId}
    </select>
    <select id="selectLastLevelMenu" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM sys_menu
        WHERE pid = #{menuId} and is_del = 0 and FIND_IN_SET('marketAdmin',model_type)
    </select>
</mapper>