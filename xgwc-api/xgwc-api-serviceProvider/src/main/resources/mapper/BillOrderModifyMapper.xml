<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.BillOrderModifyMapper">

    <select id="getCommissionBackList" resultType="com.xgwc.serviceProvider.entity.dto.CommissionBackDto">
        SELECT
            t.id,
            t.brand_id,
            ifnull(bo.company_simple_name, bo.company_name) brandName,
            t.franchise_id,
            ifnull(fo.company_simple_name, fo.company_name) franchiseName,
            t.order_id,
            t.order_no,
            t.taobao_id,
            t.sale_man_id,
            t.sale_man_name,
            t.designer_name,
            t.designer_id,
            d.phone,
            t.money,
            t.now_money,
            t.modify_time,
            t.back_status,
            t.commission_backed
        FROM
            bill_order_modify t
            left join franchise_owner fo on fo.franchise_id = t.franchise_id and fo.brand_id = t.brand_id
            left join xgwc_brand_owner bo on bo.brand_id = t.brand_id
            left join xgwc_designer d on d.designer_id = t.designer_id
        where
            t.brand_id in
            <foreach item="id" collection="brandIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="id != null"> and t.id = #{id}</if>
            <if test="orderNo != null"> and  t.order_no = #{orderNo}</if>
            <if test="taobaoId != null"> and  t.taobao_id = #{taobaoId}</if>
            <if test="saleManName != null"> and  t.sale_man_name = #{saleManName}</if>
            <if test="franchiseId != null"> and  t.franchise_id = #{franchiseId}</if>
            <if test="updateTimeStart != null"> and  t.modify_time = #{updateTimeStart}</if>
            <if test="updateTimeEnd != null"> and  t.modify_time = #{updateTimeEnd}</if>
            <if test="backStatus != null"> and  t.back_status = #{backStatus}</if>
            <if test="backed == 1"> and  t.back_status = 2</if>
            <if test="backed != 1"> and  t.back_status in (0,1,3)</if>
            <if test="brandId != null">and t.brand_id = #{brandId}</if>
            order by t.id desc
    </select>

    <select id="getDesignerCommissionBackList" resultType="com.xgwc.serviceProvider.entity.dto.CommissionBackDto">
        SELECT
        t.id,
        t.brand_id,
        ifnull(bo.company_simple_name, bo.company_name) brandName,
        t.franchise_id,
        ifnull(fo.company_simple_name, fo.company_name) franchiseName,
        t.order_id,
        t.order_no,
        t.taobao_id,
        t.sale_man_id,
        t.sale_man_name,
        t.designer_name,
        t.designer_id,
        t.money,
        t.now_money,
        t.modify_time,
        t.sub_bill_id subBillId,
        t.back_status,
        t.commission_backed
        FROM
        bill_order_modify t
        left join franchise_owner fo on fo.franchise_id = t.franchise_id and fo.brand_id = t.brand_id
        left join xgwc_brand_owner bo on bo.brand_id = t.brand_id
        left join xgwc_designer d on d.designer_id = t.designer_id
        where
        t.designer_id = #{designerId}
        <if test="id != null"> and t.id = #{id}</if>
        <if test="orderNo != null"> and t.order_no = #{orderNo}</if>
        <if test="taobaoId != null"> and t.taobao_id = #{taobaoId}</if>
        <if test="brandId != null"> and t.brand_id = #{brandId}</if>
        order by t.id desc
    </select>

    <select id="getCommissionBackDetailList" resultType="com.xgwc.serviceProvider.entity.dto.CommissionBackDetail">
        SELECT
            r.id,
            r.bill_id,
            r.bill_start,
            r.bill_end,
            r.sub_bill_id,
            r.commission_back,
            t.designer_id,
            t.designer_name,
            t.franchise_id,
            t.brand_id,
            ifnull(fo.company_simple_name, fo.company_name) franchiseName,
            ifnull(bo.company_simple_name, bo.company_name) brandName,
            r.create_time backTime
        FROM
            bill_order_modify_record r
                left join bill_order_modify t on t.id = r.modify_id
                left join franchise_owner fo on fo.franchise_id = t.franchise_id and t.brand_id = fo.brand_id
                left join xgwc_brand_owner bo on bo.brand_id = t.brand_id
        WHERE
            r.modify_id = #{backId}
        ORDER BY
            r.id DESC
    </select>

    <select id="getUnsettledBillOrderModifyList" resultType="com.xgwc.serviceProvider.entity.BillOrderModify">
        SELECT
            t.id,
            t.amount,
            t.now_amount,
            t.money,
            t.now_money,
            t.commission_backed,
            t.commission_back_remaining
        FROM
            bill_order_modify t
        WHERE
            t.designer_id = #{designerId} and t.back_status !=2 and t.back_status != 3 order by t.update_time asc
    </select>
    <select id="getBrandCommissionRecoveredList"
            resultType="com.xgwc.serviceProvider.entity.dto.CommissionBackDetail">
        SELECT
            m.id,
            t.bill_id billId,
            t.bill_period_start as billStart,
            t.bill_period_end as billEnd,
            t.stylist_name as designerName,
            d.phone as designerPhone,
            t.real_commission_back as backMoney,
            m.create_time backTime
        FROM bill_designer t
        LEFT JOIN bill_order_modify_record r ON t.bill_id = r.bill_id
        LEFT JOIN bill_order_modify m ON r.modify_id = m.id
        LEFT JOIN xgwc_designer d ON t.stylist_id = d.designer_id
        <where>
        t.brand_owner_id in
            <foreach item="id" collection="brandIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        <if test="brandId != null"> and t.brand_owner_id = #{brandId}</if>
        <if test="designerId != null"> and t.stylist_id = #{designerId}</if>
        <if test="backId != null"> and m.id = #{backId}</if>
        <if test="billId != null"> and t.bill_id = #{billId}</if>
        <if test="backTime != null"> and m.create_time between #{backTime[0]} and #{backTime[1]} </if>
        and t.real_commission_back IS NOT NULL
        ORDER BY m.id DESC
        </where>
    </select>
    <select id="selectCommissionRecoveredDetail"
            resultType="com.xgwc.serviceProvider.entity.dto.CommissionBackDto">
        SELECT
            r.id as backId,
            m.id as recordId,
            m.order_no as orderNo,
            m.taobao_id as taobaoId,
            m.money,
            m.now_money as nowMoney,
            m.commission_backed as commissionBacked,
            m.create_time as backTime
        FROM bill_order_modify_record r
        LEFT JOIN bill_order_modify m ON r.modify_id = m.id
        WHERE m.bill_id = #{billId}
    </select>
    <update id="updateBillOrderModify" parameterType="com.xgwc.serviceProvider.entity.BillOrderModify">
        update bill_order_modify
        <trim prefix="SET" suffixOverrides=",">
            <if test="amount != null">amount = #{amount},</if>
            <if test="backStatus != null">back_status = #{backStatus},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="commissionBackRemaining != null">commission_back_remaining = #{commissionBackRemaining},</if>
            <if test="commissionBacked != null">commission_backed = #{commissionBacked},</if>
            <if test="companyInfoId != null">company_info_id = #{companyInfoId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="designerBusiness != null">designer_business = #{designerBusiness},</if>
            <if test="designerId != null">designer_id = #{designerId},</if>
            <if test="designerName != null">designer_name = #{designerName},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="money != null">money = #{money},</if>
            <if test="nowAmount != null">now_amount = #{nowAmount},</if>
            <if test="nowMoney != null">now_money = #{nowMoney},</if>
            <if test="orderAmount != null">order_amount = #{orderAmount},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="saleManId != null">sale_man_id = #{saleManId},</if>
            <if test="saleManName != null">sale_man_name = #{saleManName},</if>
            <if test="taobaoId != null">taobao_id = #{taobaoId},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="getTotalCommissionBack" resultType="java.math.BigDecimal">
        SELECT
            sum( t.commission_back_remaining )
        FROM
            bill_order_modify t
        WHERE
            t.brand_id = #{brandId}
          AND t.designer_id = #{designerId}
    </select>

</mapper>