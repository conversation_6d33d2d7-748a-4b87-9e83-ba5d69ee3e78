<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.ServiceStationMapper">
    <insert id="saveServiceStation">
        INSERT INTO `xgwc_sass`.`service_station`
        (`station_name`, `service_id`, `sort`, `create_by`, `create_time`)
        VALUES (#{serviceStationDto.stationName}, #{serviceStationDto.serviceId}, #{serviceStationDto.sort},
                #{serviceStationDto.createBy}, now())
    </insert>

    <update id="updateServiceStation">
        UPDATE `xgwc_sass`.`service_station`
        <set>
            <if test="serviceStationDto.stationName != null">
                `station_name` = #{serviceStationDto.stationName},
            </if>
            <if test="serviceStationDto.serviceId != null">
                `service_id` = #{serviceStationDto.serviceId},
            </if>
            <if test="serviceStationDto.sort != null">
                `sort` = #{serviceStationDto.sort},
            </if>
            <if test="serviceStationDto.updateBy != null">
                `update_by` = #{serviceStationDto.updateBy},
            </if>
            `update_time` = now()
        </set>
       where `station_id` = #{serviceStationDto.stationId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`service_station`
        SET `status` = #{status}
        WHERE `station_id` = #{stationId}
    </update>

    <select id="getServiceStationList" resultType="com.xgwc.serviceProvider.entity.vo.ServiceStationVo">
        SELECT
        `station_id` AS stationId,
        `station_name` AS stationName,
        `service_id` AS serviceId,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`service_station`
        <where>
            `service_id` = #{serviceStationParam.serviceId}

            <if test="serviceStationParam.stationName != null and serviceStationParam.stationName !=''">
                AND `station_name` = #{serviceStationParam.stationName}
            </if>
            <if test="serviceStationParam.status != null">
                AND `status` = #{serviceStationParam.status}
            </if>
            <if test="serviceStationParam.stationId != null">
                AND `station_id` = #{serviceStationParam.stationId}
            </if>
        </where>
        order by `sort`
    </select>

    <select id="getServiceStationById" resultType="com.xgwc.serviceProvider.entity.vo.ServiceStationVo">
        SELECT
        `station_id` AS stationId,
        `station_name` AS stationName,
        `service_id` AS serviceId,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`service_station`
        WHERE `station_id` = #{stationId}
    </select>
</mapper>