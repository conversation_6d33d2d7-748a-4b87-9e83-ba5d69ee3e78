<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.MarketDeptMapper">
    <insert id="saveMarketDept">
        INSERT INTO `xgwc_sass`.`market_dept`
        (`dept_id`, `dept_name`, `market_id`, `pid`, `level`, `sort`, `create_by`, `create_time`)
        VALUES (#{marketDeptDto.deptId}, #{marketDeptDto.deptName}, #{marketDeptDto.marketId},
                #{marketDeptDto.pid},
                #{marketDeptDto.level}, #{marketDeptDto.sort},
                #{marketDeptDto.createBy}, now())
    </insert>
    <update id="updateMarketDeptById">
        UPDATE `xgwc_sass`.`market_dept`
        <set>
            <if test="marketDeptDto.deptName != null and marketDeptDto.deptName !=''">
                `dept_name` = #{marketDeptDto.deptName},
            </if>
            <if test="marketDeptDto.marketId != null and marketDeptDto.marketId !=''">
                `market_id` = #{marketDeptDto.marketId},
            </if>
            <if test="marketDeptDto.pid != null and marketDeptDto.pid !=''">
                `pid` = #{marketDeptDto.pid},
            </if>
            <if test="marketDeptDto.level != null and marketDeptDto.level !=''">
                `level` = #{marketDeptDto.level},
            </if>
            <if test="marketDeptDto.sort != null and marketDeptDto.sort !=''">
                `sort` = #{marketDeptDto.sort},
            </if>
            <if test="marketDeptDto.updateBy != null and marketDeptDto.updateBy !=''">
                `update_by` = #{marketDeptDto.updateBy},
            </if>
            `update_time` = now()
        </set>
        wHERE `dept_id` = #{marketDeptDto.deptId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`market_dept`
        SET `status` = #{status}
        WHERE `dept_id` = #{deptId}
    </update>
    <update id="updateDeptStaffSchedule">
        UPDATE xgwc_market_staff
        <set>
            `is_assistant` = 0,
            <if test="staff.isSchedule != null">
                `is_schedule` = #{staff.isSchedule},
            </if>
            <if test="staff.updateBy != null and staff.updateBy != ''">
                `update_by` = #{staff.updateBy},
            </if>
            `update_time` = NOW(),
        </set>
        WHERE `id` = #{staff.id}
    </update>


    <update id="updateDeptStaffManage">
        UPDATE xgwc_service_staff
        <trim prefix="SET" suffixOverrides=",">
            `is_principal` = 0,
            <if test="principal.isSchedule != null">
                `is_schedule` = #{principal.isSchedule},
            </if>
            <if test="principal.updateBy != null and principal.updateBy != ''">
                `update_by` = #{principal.updateBy},
            </if>
            `update_time` = NOW(),
        </trim>
        WHERE `id` = #{principal.id}
    </update>
    <update id="updateDeptStaffAssistant">
        UPDATE xgwc_service_staff
        set is_assistant = 1, is_principal = 1, is_schedule = 1
        where `dept_id` = #{deptId}
    </update>

    <select id="getMarketDeptList" resultType="com.xgwc.serviceProvider.entity.vo.ServiceDeptVo">
        SELECT
        `dept_id` AS deptId,
        `dept_name` AS deptName,
        `market_id` AS marketId,
        `pid`,
        `level`,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`market_dept`
        <where>
            AND `market_id` = #{marketDeptParam.marketId}

            <if test="marketDeptParam.deptName != null and marketDeptParam.deptName !=''">
                AND `dept_name` Like concat('%', #{marketDeptParam.deptName}, '%')
            </if>
            <if test="marketDeptParam.status != null">
                AND `status` = #{marketDeptParam.status}
            </if>
        </where>
        ORDER BY `sort`
    </select>

    <select id="getMarketDeptById" resultType="com.xgwc.serviceProvider.entity.dto.ServiceDeptDto">
        SELECT `dept_id`      AS deptId,
               `dept_name`    AS deptName,
               `market_id` AS marketId,
               `pid`,
               `level`,
               `sort`,
               `status`,
               `is_del`       AS isDel,
               `create_by`    AS createBy,
               `create_time`  AS createTime,
               `update_by`    AS updateBy,
               `update_time`  AS updateTime,
               `modify_time`     ASmodifyTime
        FROM `xgwc_sass`.`market_dept`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
            </if>
        </where>
    </select>
    <select id="selectXgwcDeptStaffManage" resultType="com.xgwc.serviceProvider.entity.vo.ServiceStaffVo">
        SELECT
        `id`,
        `name`,
        `dept_id` AS deptId,
        `is_principal` AS isPrincipal
        FROM
        `xgwc_sass`.`xgwc_service_staff`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
                AND `is_principal` = 0
            </if>
        </where>
    </select>
    <select id="selectXgwcDeptStaffSchedule" resultType="com.xgwc.serviceProvider.entity.vo.ServiceStaffVo">
        SELECT
        `id`,
        `name`,
        `dept_id` AS deptId,
        `is_assistant` AS isAssistant,
        `is_schedule` AS isSchedule
        FROM
        `xgwc_sass`.`xgwc_service_staff`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
                AND `is_assistant` = 0
            </if>
        </where>
    </select>

    <select id="getMarketDeptBymarketId" resultType="com.xgwc.serviceProvider.entity.dto.ServiceDeptDto">
        SELECT `dept_id`      AS deptId,
               `dept_name`    AS deptName,
               `market_id` AS marketId,
               `pid`,
               `level`,
               `sort`,
               `status`,
               `is_del`       AS isDel,
               `create_by`    AS createBy,
               `create_time`  AS createTime,
               `update_by`    AS updateBy,
               `update_time`  AS updateTime,
               `modify_time`     ASmodifyTime
        FROM `xgwc_sass`.`market_dept`
        WHERE `market_id` = #{marketId}
         and status = 0 and is_del = 0
    </select>

    <select id="selectDeptStaffNum" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
        `xgwc_sass`.`xgwc_service_staff`
        WHERE
        <if test="deptId != null">
            `dept_id` = #{deptId}
        </if>
    </select>
    <select id="getUserDeptByUserId" resultType="com.xgwc.serviceProvider.entity.dto.ServiceDeptDto">
        select
            d.dept_id,
            d.dept_name,
            d.market_id,
            d.pid
        from xgwc_service_staff s
        left join market_dept d on s.dept_id = d.dept_id
        where s.bind_serviceProvider_id = #{marketProviderId}
        limit 1
    </select>

    <select id="getDeptByPid" resultType="com.xgwc.serviceProvider.entity.dto.ServiceDeptDto">
        SELECT
            dept_id AS deptId,
            dept_name AS deptName,
            market_id AS marketId,
            pid,
            level
        FROM market_dept
        WHERE pid = #{pid}
        AND market_id = #{marketId}
    </select>
</mapper>