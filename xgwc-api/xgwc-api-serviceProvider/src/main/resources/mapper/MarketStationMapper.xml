<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.serviceProvider.dao.MarketStationMapper">
    <insert id="saveMarketStation">
        INSERT INTO `xgwc_sass`.`market_station`
        (`station_name`, `market_id`, `sort`, `create_by`, `create_time`)
        VALUES (#{marketStationDto.stationName}, #{marketStationDto.marketId}, #{marketStationDto.sort},
                #{marketStationDto.createBy}, now())
    </insert>

    <update id="updateMarketStation">
        UPDATE `xgwc_sass`.`market_station`
        <set>
            <if test="marketStationDto.stationName != null">
                `station_name` = #{marketStationDto.stationName},
            </if>
            <if test="marketStationDto.marketId != null">
                `market_id` = #{marketStationDto.marketId},
            </if>
            <if test="marketStationDto.sort != null">
                `sort` = #{marketStationDto.sort},
            </if>
            <if test="marketStationDto.updateBy != null">
                `update_by` = #{marketStationDto.updateBy},
            </if>
            `update_time` = now()
        </set>
       where `station_id` = #{marketStationDto.stationId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`market_station`
        SET `status` = #{status}
        WHERE `station_id` = #{stationId}
    </update>

    <select id="getMarketStationList" resultType="com.xgwc.serviceProvider.entity.vo.ServiceStationVo">
        SELECT
        `station_id` AS stationId,
        `station_name` AS stationName,
        `market_id` AS marketId,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`market_station`
        <where>
            `market_id` = #{marketStationParam.marketId}

            <if test="marketStationParam.stationName != null and marketStationParam.stationName !=''">
                AND `station_name` = #{marketStationParam.stationName}
            </if>
            <if test="marketStationParam.status != null">
                AND `status` = #{marketStationParam.status}
            </if>
            <if test="marketStationParam.stationId != null">
                AND `station_id` = #{marketStationParam.stationId}
            </if>
        </where>
        order by `sort`
    </select>

    <select id="getMarketStationById" resultType="com.xgwc.serviceProvider.entity.vo.ServiceStationVo">
        SELECT
        `station_id` AS stationId,
        `station_name` AS stationName,
        `market_id` AS marketId,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`market_station`
        WHERE `station_id` = #{stationId}
    </select>
</mapper>