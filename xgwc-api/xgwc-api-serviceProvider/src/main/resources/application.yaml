spring:
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  config:
    import: "nacos:xgwc-api-serviceProvider.yml"
  application:
    name: xgwc-api-serviceProvider
  cloud:
    nacos:
      server-addr: 192.168.1.189:8848
      username: nacos
      password: 2BEheJdWQ9tzHfYmy9FH
      config:
        namespace: 6cc1ba75-2b9c-4ceb-8398-fc381a8d747b
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
server:
  port: 8086
mybatis:
  type-aliases-package: com.xgwc.serviceProvider.entity
  mapper-locations: classpath:mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
logging:
  level:
    com.xgwc.serviceProvider.dao: debug