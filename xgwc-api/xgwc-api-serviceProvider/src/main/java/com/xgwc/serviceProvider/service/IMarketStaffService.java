package com.xgwc.serviceProvider.service;

import java.util.List;

import com.xgwc.common.entity.StaffLogDto;
import com.xgwc.serviceProvider.entity.MarketStaff;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperationVo;
import com.xgwc.serviceProvider.entity.vo.MarketStaffVo;
import com.xgwc.serviceProvider.entity.dto.MarketStaffDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffQueryVo;
import com.xgwc.serviceProvider.feign.entity.BindStaffDto;
import com.xgwc.serviceProvider.feign.entity.StaffDto;

public interface IMarketStaffService {
    /**
     * 查询销售服务商员工表
     *
     * @param id 销售服务商员工表主键
     * @return 销售服务商员工表
     */
    MarketStaffDto selectMarketStaffById(Long id);

    /**
     * 查询销售服务商员工表列表
     *
     * @param marketStaff 销售服务商员工表
     * @return 销售服务商员工表集合
     */
    List<MarketStaffDto> selectMarketStaffList(MarketStaffQueryVo marketStaff);

    /**
     * 新增销售服务商员工表
     *
     * @param marketStaff 销售服务商员工表
     * @return 结果
     */
    int insertMarketStaff(MarketStaffVo marketStaff);

    /**
     * 修改销售服务商员工表
     *
     * @param marketStaff 销售服务商员工表
     * @return 结果
     */
    int updateMarketStaff(MarketStaffVo marketStaff);

    /**
     * 删除销售服务商员工表信息
     * @param id 销售服务商员工表主键
     * @return 结果
     */
    int deleteMarketStaffById(Long id);

    /**
     * 查询员工修改日志
     */
    List<StaffLogDto> findStaffLogByStaffIdAndBusinessType(Long id, Integer businessType);

    /**
     * 查询销售服务商员工下拉框
     * @return 服务商员工下拉框集合
     */
    List<MarketStaffDto> selectStaffListDropDown(Long deptId);

    /**
     * 根据员工姓名和销售服务商id查询员工是否存在
     * @param staffName 员工姓名
     * @param serviceId 销售服务商id
     * @return true:存在 false:不存在
     */
    boolean selectMarketStaffByNameAndServiceId(String staffName, Long serviceId);

    /**
     * 更新员工绑定状态
     * @param bindStaffDto 绑定信息
     */
    void updateMarketStaffBindStatus(BindStaffDto bindStaffDto);

    /**
     * 新增服务商员工
     * @param staffDto 员工信息
     */
    void saveMarketStaff(StaffDto staffDto);

    /**
     * 合作加盟商
     * @param dto 合作信息
     * @return 结果
     */
    int cooperate(MarketStaffCooperationVo dto);
}
