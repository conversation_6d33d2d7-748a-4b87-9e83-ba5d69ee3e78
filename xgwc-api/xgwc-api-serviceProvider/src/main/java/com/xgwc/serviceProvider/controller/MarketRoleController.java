package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.ServiceRoleDto;
import com.xgwc.serviceProvider.entity.param.ServiceRoleParam;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleVo;
import com.xgwc.serviceProvider.service.MarketRoleService;
import com.xgwc.serviceProvider.service.ServiceRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 销售服务商角色管理
 */
@RestController
@RequestMapping("/marketRole")
@Slf4j
public class MarketRoleController extends BaseController {

    @Resource
    private MarketRoleService marketRoleService;

    /**
     * @param marketRoleParam 查询条件
     * @return 角色管理列表
     * 查询角色管理列表
     */
    @MethodDesc("查询角色管理列表")
    @PreAuthorize("@ss.hasPermission('marketRole:marketRole:list')")
    @PostMapping("/getMarketRoleList")
    public ApiResult<ServiceRoleVo> getMarketRoleList(@RequestBody ServiceRoleParam marketRoleParam) {
        startPage();
        return getDataTable(marketRoleService.getMarketRoleList(marketRoleParam));
    }

    /**
     * 获取销售服务商角色管理下拉框
     *
     * @return 角色管理下拉框
     */
    @MethodDesc("获取销售服务商角色管理下拉框")
    @GetMapping("/getMarketRoleDropDown")
    public ApiResult getMarketRoleDropDown() {
        try {
            List<ServiceRoleVo> result = marketRoleService.getMarketRoleList(new ServiceRoleParam());
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取销售服务商角色管理下拉框失败", e);
            return ApiResult.error("获取销售服务商角色管理下拉框失败");
        }
    }

    /**
     * @param marketRoleDto 新增角色管理信息
     * @return 插入结果
     * 新增角色管理信息
     */
    @MethodDesc("新增角色管理信息")
    @PreAuthorize("@ss.hasPermission('marketRole:marketRole:add')")
    @Submit(fileds = "userId")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveMarketRole")
    public ApiResult saveMarketRole(@RequestBody ServiceRoleDto marketRoleDto) {
        return marketRoleService.saveMarketRole(marketRoleDto);
    }

    /**
     * @param roleId 角色管理id
     * @return 角色管理信息
     * 根据id查询角色管理信息
     */
    @MethodDesc("根据id查询角色管理信息")
    @PreAuthorize("@ss.hasPermission('marketRole:marketRole:query')")
    @GetMapping("/getMarketRoleById/{roleId}")
    public ApiResult getMarketRoleById(@PathVariable Long roleId) {
        return marketRoleService.getMarketRoleById(roleId);
    }

    /**
     * @param marketRoleDto 修改信息
     * @return 修改结果
     * 修改角色管理信息
     */
    @MethodDesc("修改角色管理信息")
    @PreAuthorize("@ss.hasPermission('marketRole:marketRole:edit')")
    @Submit(fileds = "userId")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMarketRole")
    public ApiResult updateMarketRole(@RequestBody ServiceRoleDto marketRoleDto) {
        return marketRoleService.updateMarketRole(marketRoleDto);
    }

    /**
     * @param roleId 角色id
     * @return 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('marketRole:marketRole:editStatusById')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "roleId") Integer roleId,
                                      @RequestParam(value = "status") Integer status) {
        return marketRoleService.updateStatusById(roleId, status);
    }

}
