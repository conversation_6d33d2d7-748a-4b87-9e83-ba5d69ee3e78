package com.xgwc.serviceProvider.entity.dto;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.serviceProvider.entity.dto
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-25  11:53
 */

import com.xgwc.common.entity.ApiResult;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.math.BigDecimal;
import java.util.List;

/**
 * 佣金统计
 */
@Data
public class CommissionSumDto {

    /** 需追回佣金 */
    private BigDecimal needBackCommission;

    /** 已追回佣金 */
    private BigDecimal recoveredCommission;

    /** 待追回佣金 */
    private BigDecimal toBeRecoveredCommission;

    /** list */
    private ApiResult list;
}
