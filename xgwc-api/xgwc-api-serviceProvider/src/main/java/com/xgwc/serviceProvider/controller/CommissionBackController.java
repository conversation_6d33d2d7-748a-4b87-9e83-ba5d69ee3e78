package com.xgwc.serviceProvider.controller;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.CommissionBackDetail;
import com.xgwc.serviceProvider.entity.dto.CommissionBackDto;
import com.xgwc.serviceProvider.entity.dto.CommissionSumDto;
import com.xgwc.serviceProvider.entity.vo.CommissionBackVo;
import com.xgwc.serviceProvider.service.CommissionBackService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 佣金退回
 */
@RequestMapping("commissionback")
@RestController
public class CommissionBackController extends BaseController {

    @Resource
    private CommissionBackService commissionBackService;

    /**
     * 品牌商获取佣金追回
     */
    @RequestMapping("brand/getCommissionBackList")
    public ApiResult getBrandCommissionBackList(CommissionBackVo commissionBackVo) {
        startPage();
        CommissionSumDto brandCommissionBackList = commissionBackService.getBrandCommissionBackList(commissionBackVo);
        return ApiResult.ok(brandCommissionBackList);
    }

    /**
     * 品牌商获取佣金追回
     */
    @RequestMapping("brand/getCommissionBackedList")
    public ApiResult getBrandCommissionBackedList(CommissionBackVo commissionBackVo) {
        startPage();
        CommissionSumDto commissionBackDtos = commissionBackService.getBrandCommissionBackList(commissionBackVo);
        return ApiResult.ok(commissionBackDtos);
    }

    /**
     * 品牌商获取佣金追回
     */
    @RequestMapping("service/getCommissionBackList")
    public ApiResult getServiceCommissionBackList(CommissionBackVo commissionBackVo) {
        startPage();
        CommissionSumDto commissionBackDtos = commissionBackService.getServiceCommissionBackList(commissionBackVo);
        return ApiResult.ok(commissionBackDtos);
    }

    /**
     * 品牌商获取佣金追回
     */
    @RequestMapping("designer/getCommissionBackList")
    public ApiResult getDesignerCommissionBackList(CommissionBackVo commissionBackVo) {
        startPage();
        CommissionSumDto commissionBackDtos = commissionBackService.getDesignerCommissionBackList(commissionBackVo);
        return ApiResult.ok(commissionBackDtos);
    }

    /**
     * 品牌商获取佣金追回明细
     */
    @RequestMapping("brand/getCommissionDetail")
    private ApiResult getBrandCommissionBackDetail(CommissionBackVo commissionBackVo){
        if(commissionBackVo.getBackId() == null){
            return ApiResult.error("订单ID不能为空");
        }
        List<CommissionBackDetail> commissionBackDtos = commissionBackService.getBrandCommissionBackDetailList(commissionBackVo);
        return ApiResult.ok(commissionBackDtos);
    }

    /**
     * 服务商获取佣金追回明细
     */
    @RequestMapping("service/getCommissionDetail")
    private ApiResult getServiceCommissionBackDetail(CommissionBackVo commissionBackVo){
        if(commissionBackVo.getBackId() == null){
            return ApiResult.error("订单ID不能为空");
        }
        List<CommissionBackDetail> commissionBackDtos = commissionBackService.getServiceCommissionBackDetailList(commissionBackVo);
        return ApiResult.ok(commissionBackDtos);
    }

    /**
     * 设计师获取佣金追回明细
     */
    @RequestMapping("designer/getCommissionDetail")
    private ApiResult getDesignerCommissionBackDetail(CommissionBackVo commissionBackVo){
        if(commissionBackVo.getBackId() == null){
            return ApiResult.error("订单ID不能为空");
        }
        List<CommissionBackDetail> commissionBackDtos = commissionBackService.getDesignerCommissionBackDetailList(commissionBackVo);
        return ApiResult.ok(commissionBackDtos);
    }

    /**
     * 获取待追回的佣金总数
     */
    @RequestMapping("getTotalCommissionBack")
    public ApiResult getTotalCommissionBack(CommissionBackVo commissionBackVo){
        BigDecimal bigDecimal = commissionBackService.getTotalCommissionBack(commissionBackVo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("totalCommissionBack", bigDecimal);
        return ApiResult.ok(jsonObject);
    }
}
