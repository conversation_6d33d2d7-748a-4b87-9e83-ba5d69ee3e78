package com.xgwc.serviceProvider.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class MarketStaffCooperativeVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("员工id")
    private Long staffId;

    @FieldDesc("销售服务商id")
    private Long serviceOwnerId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商员工id")
    private Long franchiseStaffId;

    @FieldDesc("创建人id")
    private Long createById;

}
