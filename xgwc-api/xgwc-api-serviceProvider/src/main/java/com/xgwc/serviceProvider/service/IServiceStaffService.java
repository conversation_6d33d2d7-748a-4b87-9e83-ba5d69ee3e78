package com.xgwc.serviceProvider.service;

import com.xgwc.common.entity.StaffLogDto;
import com.xgwc.serviceProvider.entity.dto.ServiceStaffDto;
import com.xgwc.serviceProvider.entity.vo.ServiceStaffQueryVo;
import com.xgwc.serviceProvider.entity.vo.ServiceStaffVo;
import com.xgwc.serviceProvider.feign.entity.BindStaffDto;
import com.xgwc.serviceProvider.feign.entity.FeignServiceStaffDto;
import com.xgwc.serviceProvider.feign.entity.StaffDto;

import java.util.List;

public interface IServiceStaffService  {
    /**
     * 查询服务商员工
     * 
     * @param id 服务商员工主键
     * @return 服务商员工
     */
    public ServiceStaffDto selectServiceStaffById(Long id);

    /**
     * 查询服务商员工列表
     * 
     * @param serviceStaff 服务商员工
     * @return 服务商员工集合
     */
    public List<ServiceStaffDto> selectServiceStaffList(ServiceStaffQueryVo serviceStaff);

    /**
     * 新增服务商员工
     * 
     * @param serviceStaff 服务商员工
     * @return 结果
     */
    public int insertServiceStaff(ServiceStaffVo serviceStaff);

    /**
     * 修改服务商员工
     * 
     * @param serviceStaff 服务商员工
     * @return 结果
     */
    public int updateServiceStaff(ServiceStaffVo serviceStaff);

    /**
     * 批量删除服务商员工
     * 
     * @param ids 需要删除的服务商员工主键集合
     * @return 结果
     */
    public int deleteServiceStaffByIds(Long[] ids);

    /**
     * 删除服务商员工信息
     * 
     * @param id 服务商员工主键
     * @return 结果
     */
    public int deleteServiceStaffById(Long id);

    /**
     * 查询服务商员工下拉框
     *
     * @return 服务商员工下拉框集合
     */
    List<ServiceStaffDto> selectStaffListDropDown(Long deptId);

    /**
     * 查询员工修改日志
     */
    List<StaffLogDto> findStaffLogByStaffIdAndBusinessType(Long id, Integer businessType);

    /**
     * feign调用 根据员工id查询员工信息
     * @param staffId 员工id
     * @return 员工信息
     */
    FeignServiceStaffDto selectServiceStaffByStaffId(Long staffId);

    /**
     * 根据员工姓名和服务商id查询员工是否存在
     * @param staffName 员工姓名
     * @param serviceId 服务商id
     * @return true:存在 false:不存在
     */
    boolean selectServiceStaffByNameAndServiceId(String staffName, Long serviceId);

    /**
     * 更新员工绑定状态
     * @param bindStaffDto 绑定信息
     */
    void updateServiceStaffBindStatus(BindStaffDto bindStaffDto);

    /**
     * 新增服务商员工
     * @param staffDto 员工信息
     */
    void saveServiceStaff(StaffDto staffDto);
}
