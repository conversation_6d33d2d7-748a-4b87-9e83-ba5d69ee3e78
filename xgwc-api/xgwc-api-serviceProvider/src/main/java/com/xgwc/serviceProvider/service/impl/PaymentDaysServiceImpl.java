package com.xgwc.serviceProvider.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.dao.PaymentDaysMapper;
import com.xgwc.serviceProvider.entity.PaymentDays;
import com.xgwc.serviceProvider.entity.dto.PaymentDaysDto;
import com.xgwc.serviceProvider.entity.vo.PaymentDaysVo;
import com.xgwc.serviceProvider.service.IPaymentDaysService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
public class PaymentDaysServiceImpl implements IPaymentDaysService {
    @Resource
    private PaymentDaysMapper paymentDaysMapper;

    /**
     * 查询账期管理
     * 
     * @return 账期管理
     */
    @Override
    public List<PaymentDaysDto> selectPaymentDaysByBrandId() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        List<PaymentDaysDto> dtos = paymentDaysMapper.selectPaymentDaysByBrandId(brandId);
        if(dtos == null || dtos.isEmpty()) {
            dtos = new ArrayList<PaymentDaysDto>();
            PaymentDaysDto dto = new PaymentDaysDto();
            dto.setPaymentType(1);
            PaymentDaysDto dto2 = new PaymentDaysDto();
            dto2.setPaymentType(2);
            dtos.add(dto);
            dtos.add(dto2);
        } else if(dtos.size() == 1) {
            Integer type = dtos.get(0).getPaymentType();
            PaymentDaysDto dto = new PaymentDaysDto();
            dto.setPaymentType(type == 1 ? 2 : 1);
            dtos.add(dto);
        }
        return dtos;
    }



    /**
     * 修改账期管理
     * 
     * @param dto 账期管理
     * @return 结果
     */
    @Override
    public int updatePaymentDays(PaymentDaysVo dto) {
        PaymentDays paymentDays = BeanUtil.copyProperties(dto, PaymentDays.class);

        if(paymentDays.getBrandId() != null) {
            paymentDays.setUpdateTime(DateUtils.getNowDate());
            paymentDays.setUpdateBy(SecurityUtils.getSysUser().getUserName());
            paymentDaysMapper.updatePaymentDays(paymentDays);
        } else {
            Long brandId = SecurityUtils.getSysUser().getBrandId();
            paymentDays.setBrandId(brandId);
            paymentDays.setCreateTime(DateUtils.getNowDate());
            paymentDays.setCreateBy(SecurityUtils.getSysUser().getUserName());
            paymentDaysMapper.insertPaymentDays(paymentDays);
        }
        return 1;
    }

}
