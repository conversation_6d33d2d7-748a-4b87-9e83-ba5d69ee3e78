package com.xgwc.serviceProvider.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.ServiceStationDto;
import com.xgwc.serviceProvider.entity.param.ServiceStationParam;
import com.xgwc.serviceProvider.entity.vo.ServiceStationVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kou<PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-04-28  14:11
 */
public interface MarketStationService {

    /**
     * 获取销售服务商岗位列表
     * @param marketStationParam 销售服务商岗位参数
     * @return 销售服务商岗位列表
     */
    List<ServiceStationVo> getMarketStationList(ServiceStationParam marketStationParam);

    /**
     * 保存销售服务商岗位
     * @param marketStationDto 销售服务商岗位参数
     * @return 销售服务商岗位列表
     */
    ApiResult saveMarketStation(ServiceStationDto marketStationDto);

    /**
     * 根据销售服务商岗位id获取销售服务商岗位
     * @param stationId 销售服务商岗位id
     * @return 销售服务商岗位
     */
    ApiResult getMarketStationById(Long stationId);

    /**
     * 更新销售服务商岗位
     * @param marketStationDto 销售服务商岗位参数
     * @return 销售服务商岗位列表
     */
    ApiResult updateMarketStation(ServiceStationDto marketStationDto);

    /**
     * 更新销售服务商岗位状态
     * @param stationId 销售服务商岗位id
     * @param status 销售服务商岗位状态
     * @return 销售服务商岗位列表
     */
    ApiResult updateStatusById(Integer stationId, Integer status);
}
