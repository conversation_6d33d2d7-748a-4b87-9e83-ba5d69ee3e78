package com.xgwc.serviceProvider.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.*;
import com.xgwc.serviceProvider.dao.BillOrderMapper;
import com.xgwc.serviceProvider.dao.DesignerBillSubMapper;
import com.xgwc.serviceProvider.dao.ServiceAuthorizeMapper;
import com.xgwc.serviceProvider.entity.DesignerBillSub;
import com.xgwc.serviceProvider.entity.dto.*;
import com.xgwc.serviceProvider.entity.vo.BillVo;
import com.xgwc.serviceProvider.service.BillSubService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillSubServiceImpl implements BillSubService {

    @Resource
    private DesignerBillSubMapper designerBillSubMapper;

    @Resource
    private BillOrderMapper billOrderMapper;

    @Resource
    private ServiceAuthorizeMapper serviceAuthorizeMapper;

    @Override
    public List<BillBrandSubDto> getBillBrandSubList(BillVo billVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        initBillVo(billVo);
        Long brandId = sysUser.getBrandId();
        if (brandId == null) {
            log.error("非品牌商不能获取品牌下的设计师账单:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        billVo.setBrandIds(Collections.singletonList(brandId));
        return getBillBrandListByBillVo(billVo);
    }

    @Override
    public List<BillBrandSubDto> getBillServiceSubList(BillVo billVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        initBillVo(billVo);
        Long serviceId = sysUser.getServiceId();
        if (serviceId == null) {
            log.error("非服务商不能获取服务下的设计师账单:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if (brandIds == null || brandIds.isEmpty()) {
            log.error("服务商没有授权的品牌商，禁止查询:{},{}", JSONObject.toJSONString(sysUser), JSONObject.toJSONString(billVo));
            return null;
        }
        billVo.setBrandIds(brandIds);
        return getBillBrandListByBillVo(billVo);
    }

    @Override
    public BillBrandDetailDto getBillDetail(BillVo billVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if (brandId == null) {
            log.error("非品牌商不能获取品牌下的设计师账单详情:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        DesignerBillSubDto designerBillSubDto = designerBillSubMapper.selectDesignerBillSubDtoBySubBillId(billVo.getSubBillId());
        if(designerBillSubDto == null || !Objects.equals(designerBillSubDto.getBrandOwnerId(), brandId)){
            log.error("非当前品牌商账单，禁止查询:{},查询子账单id:{}", JSONObject.toJSON(sysUser), designerBillSubDto);
            return null;
        }
        BillBrandDetailDto billBrandDetailDto = buildBillBrandDetailDto(designerBillSubDto);
        List<BillBrandOrderDetail> billBrandOrderDetails = billOrderMapper.getBillOrderDetailByBillId(billVo.getSubBillId());
        billBrandDetailDto.setOrderList(billBrandOrderDetails);
        return billBrandDetailDto;
    }

    @Override
    public BillBrandDetailDto getServiceBillDetail(BillVo billVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if (serviceId == null) {
            log.error("非服务商不能获取服务商下的设计师账单详情:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if (brandIds == null || brandIds.isEmpty()) {
            log.error("服务商没有授权的品牌商，禁止查询:{},{}", JSONObject.toJSONString(sysUser), JSONObject.toJSONString(billVo));
            return null;
        }
        DesignerBillSubDto designerBillSubDto = designerBillSubMapper.selectDesignerBillSubDtoBySubBillId(billVo.getSubBillId());
        if(designerBillSubDto == null || !brandIds.contains(designerBillSubDto.getBrandOwnerId())){
            log.error("非当前服务商账单，禁止查询:{},查询子账单id:{}", JSONObject.toJSONString(sysUser), JSONObject.toJSONString(billVo));
            return null;
        }
        BillBrandDetailDto billBrandDetailDto = buildBillBrandDetailDto(designerBillSubDto);
        List<BillBrandOrderDetail> billBrandOrderDetails = billOrderMapper.getBillOrderDetailByBillId(billVo.getSubBillId());
        billBrandDetailDto.setOrderList(billBrandOrderDetails);
        return billBrandDetailDto;
    }

    @Override
    public void downloadSubBill(HttpServletResponse response, Long subBillId, Integer isFlag) {
        // 参数校验
        validateDownloadParams(subBillId, isFlag);

        // 根据 isFlag 获取不同的账单详情
        BillVo billVo = new BillVo();
        billVo.setBillId(subBillId);
        BillBrandDetailDto billBrandDetailDto = (isFlag == 0)
                ? getBillDetail(billVo)
                : getServiceBillDetail(billVo);

        if (billBrandDetailDto == null) {
            throw new ApiException("账单不存在");
        }

        // 构建账单基本信息
        BillBasicInfo billBasicInfo = buildBillBasicInfo(billBrandDetailDto);

        // 构建账单详情列表
        List<BillDetail> billDetails = buildBillDetails(billBrandDetailDto, subBillId);

        // 导出Excel
        exportBillToExcel(billBasicInfo, billDetails, response, subBillId);
    }

    @Override
    public void downloadWaitSettleBill(HttpServletResponse response, Long brandId) {
        BillVo billVo = new BillVo();
        billVo.setSettlementStatus(2);
        billVo.setIsLock(0);
        billVo.setBrandId(brandId);

        // 获取账单列表
        List<BillBrandSubDto> billSubList = getBillServiceSubList(billVo);
        if (CollectionUtils.isEmpty(billSubList)) {
            throw new ApiException("没有可结算的账单");
        }
        // 构建待结算账单信息
        List<BillWaitSettle> billWaitSettles = getBillWaitSettles(billSubList);
        try {
            ExcelExportUtil.exportExcelToResponse(
                    "待结算账单",
                    "待结算账单详情信息",
                    null,
                    billWaitSettles,
                    response,
                    "待结算账单"
            );
        } catch (IOException e) {
            log.error("导出待结算账单失败", e);
            throw new ApiException("下载待结算账单失败，请稍后重试");
        }
    }

    @Override
    public void downloadPaymentFailedBill(HttpServletResponse response, Long brandId) {
        BillVo billVo = new BillVo();
        billVo.setSettlementStatus(2);
        billVo.setIsLock(0);
        billVo.setBrandId(brandId);

        // 获取账单打款失败列表
        List<BillBrandSubDto> billSubList = getBillServiceSubList(billVo);
        if (CollectionUtils.isEmpty(billSubList)) {
            throw new ApiException("没有可导出的打款失败账单");
        }
        // 构建账单失败列表
        List<BillPaymentFailed> billPaymentFailed = getBillPaymentFaileds(billSubList);

        try {
            ExcelExportUtil.exportExcelToResponse(
                    "打款失败账单",
                    "打款失败账单详情信息",
                    null,
                    billPaymentFailed,
                    response,
                    "打款失败账单"
            );
        } catch (IOException e) {
            log.error("导出打款失败账单未成功", e);
            throw new ApiException("下载账单失败，请稍后重试");
        }

    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void importPaymentResult(MultipartFile file, Long brandId) {
        try {
            // 解析Excel中的打款结果
            List<BillBatchPayment> billBatchPayments = ExcelImportUtil.importExcel(file, BillBatchPayment.class);

            if (CollectionUtils.isEmpty(billBatchPayments)) {
                throw new ApiException("导入打款结果失败，请检查Excel文件格式或数据为空");
            }

            BillVo billVo = new BillVo();
            billVo.setBrandId(brandId);
            // 获取当前品牌下的所有子账单
            List<BillBrandSubDto> billSubList = getBillServiceSubList(billVo);

            List<BillPaymentRecord> billPaymentRecords = new ArrayList<>();
            Date date = new Date();
            String nickName = SecurityUtils.getNickName();

            for (BillBatchPayment batchPayment : billBatchPayments) {
                BillPaymentRecord record = new BillPaymentRecord();
                record.setSubBillId(batchPayment.getSubBillId());
                record.setAmount(batchPayment.getAmount());
                record.setAlipayName(batchPayment.getAlipayName());
                record.setAlipayAccount(batchPayment.getAlipayAccount());
                record.setFailReason(batchPayment.getFailReason());
                record.setBrandId(brandId);
                record.setExcelName("设计师打款"+DateUtils.timestamp()+".xlsx");
                record.setCreateBy(nickName);

                List<String> mismatchFields = new ArrayList<>();

                boolean foundSubBillId = false;
                boolean fullyMatched = false;

                for (BillBrandSubDto subDto : billSubList) {
                    if (batchPayment.getSubBillId().equals(subDto.getSubBillId())) {
                        foundSubBillId = true;

                        if (batchPayment.getAmount().compareTo(subDto.getPayableAmount()) != 0) {
                            mismatchFields.add("应发金额");
                        }
                        if (!Objects.equals(batchPayment.getAlipayName(), subDto.getZfbName())) {
                            mismatchFields.add("支付宝姓名");
                        }
                        if (!Objects.equals(batchPayment.getAlipayAccount(), subDto.getZfbAccount())) {
                            mismatchFields.add("支付宝账号");
                        }

                        if (mismatchFields.isEmpty()) {
                            fullyMatched = true;
                            record.setBillId(subDto.getBillId());
                        }
                        break; // 找到子账单ID匹配即可
                    }
                }
                DesignerBillSub designerBillSub = new DesignerBillSub();
                if (fullyMatched) {
                    record.setPaymentResult("打款成功");
                    record.setMatchResult("成功");

                    designerBillSub.setSubBillId(record.getSubBillId());
                    designerBillSub.setSettlementStatus(3);

                    List<Long> orderIds = designerBillSubMapper.selectOrderId(record.getSubBillId());
                    if (!CollectionUtils.isEmpty(orderIds)){
                        String createBy = record.getCreateBy();
                        int updated = billOrderMapper.updateOrderStatus(orderIds, createBy, date);
                        if (updated != orderIds.size()) {
                            log.error("批量更新母账单金额失败，预期更新{}条，实际更新{}条",orderIds.size(), updated);
                            throw new ApiException("导入打款结果失败，请稍后重试");
                        }
                    }
                } else {
                    if (!foundSubBillId) {
                        mismatchFields.add("子账单ID");
                    }
                    String mismatchStr = String.join("、", mismatchFields) + "不匹配";
                    record.setPaymentResult("打款失败");
                    record.setMatchResult(mismatchStr);
                    designerBillSub.setSubBillId(record.getSubBillId());
                    designerBillSub.setSettlementStatus(7);
                    designerBillSub.setFailReason(mismatchStr);
                }

                designerBillSub.setSettlementUser(record.getCreateBy());
                designerBillSub.setSettlementTime(date);
                int billSubDto = designerBillSubMapper.updateDesignerBillSubDto(designerBillSub);
                if(billSubDto <= 0){
                    log.error("更新子账单打款状态失败，入参信息：{}", JSONObject.toJSON(designerBillSub));
                    throw new ApiException("导入打款结果失败，请稍后重试");
                }

                Boolean aBoolean = designerBillSubMapper.selectStatusByBillId(record.getBillId());
                Integer settlementStatus = (aBoolean) ? 3 : 6;
                int updated = designerBillSubMapper.updateBillDesignerStatus(settlementStatus, record.getBillId());
                if (updated <= 0) {
                    log.error("更新母账单打款状态失败，billId: {}，settlementStatus：{}", record.getBillId(), settlementStatus);
                    throw new ApiException("导入打款结果失败，请稍后重试");
                }
                billPaymentRecords.add(record);
            }

            if (billPaymentRecords.isEmpty()){
                int billPayment = designerBillSubMapper.insertBillPayment(billPaymentRecords);
                if (billPayment != billPaymentRecords.size()) {
                    log.error("批量插入打款记录失败，预期插入{}条，实际插入{}条",
                            billPaymentRecords.size(), billPayment);
                    throw new ApiException("导入打款结果失败，请稍后重试");
                }

                int paymentAndSub = designerBillSubMapper.insertBillPaymentAndSub(billPaymentRecords);
                if (paymentAndSub != billPaymentRecords.size()) {
                    log.error("批量插入打款记录（中间表）失败，预期插入{}条，实际插入{}条，打款记录信息{}",
                            billPaymentRecords.size(), paymentAndSub, JSONObject.toJSON(billPaymentRecords));
                    throw new ApiException("导入打款结果失败，请稍后重试");
                }
            }

            // 先过滤出打款成功的记录，根据billId分组、求和
            Map<Long, BigDecimal> result = billPaymentRecords.stream()
                    .filter(record -> "打款成功".equals(record.getPaymentResult()))
                    .collect(Collectors.toMap(
                            BillPaymentRecord::getBillId,
                            record -> Optional.ofNullable(record.getAmount()).orElse(BigDecimal.ZERO),
                            BigDecimal::add
                    ));
            int updated = designerBillSubMapper.updateBillAmount(result, nickName, date);
            if (updated != result.size()) {
                log.error("更新母账单金额失败，预期更新{}条，实际更新{}条",
                        result.size(), updated);
                throw new ApiException("导入打款结果失败，请稍后重试");
            }

        } catch (IOException e) {
            log.error("导入打款结果失败", e);
            throw new ApiException("导入打款结果失败，请稍后重试");
        }
    }

    @Override
    public List<BillPaymentRecord> selectPaymentRecordList(Long brandId, String name, Date[] date, Long paymentId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if (serviceId == null) {
            log.error("非服务商不能获取服务商下的打款记录:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if (brandIds == null || brandIds.isEmpty()) {
            log.error("服务商没有授权的品牌商，禁止查询:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return designerBillSubMapper.selectPaymentRecordList(brandIds, brandId, name, date, paymentId);
    }

    @Override
    public void exportPaymentRecord(HttpServletResponse response, Long paymentId) {
        List<BillPaymentRecord> billPaymentRecords = selectPaymentRecordList(null, null, null, paymentId);
        if (CollectionUtils.isEmpty(billPaymentRecords)){
            throw new ApiException("没有找到该打款结果");
        }
        // 构建Excel打款记录数据
        List<BillPaymentRecord> paymentRecords = getBillPaymentRecords(billPaymentRecords);

        try {
            ExcelExportUtil.exportExcelToResponse("打款记录",
                    "打款记录详情信息",
                    null,
                    paymentRecords,
                    response,
                    "打款记录"
            );
        } catch (IOException e) {
            log.error("导出打款记录失败", e);
            throw new ApiException("导出打款记录失败，请稍后重试");
        }
    }

    @Override
    public String getPaymentFailedReasonBySubBillId(Long subBillId, Integer isFlag) {
        if (isFlag == null || isFlag>1 || isFlag<0){
            log.error("isFlag参数错误");
            return null;
        }
        BillVo billVo = new BillVo();
        billVo.setSubBillId(subBillId);
        BillBrandDetailDto designerBillSubDto =(isFlag == 0)
                ? getBillDetail(billVo)
                : getServiceBillDetail(billVo);
        return designerBillSubDto.getFailReason();
    }

    @Override
    public List<Map<String, Object>> getAuthorizeBrandIdsByServiceId() {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if (serviceId == null) {
            log.error("当前登录账号非服务商账号:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        return serviceAuthorizeMapper.getAuthorizeBrandInfoByServiceId(serviceId);
    }

    @Override
    public void downloadPaymentTemplate(HttpServletResponse response) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if (serviceId == null) {
            log.error("当前登录账号非服务商账号:{}", JSONObject.toJSON(sysUser));
            throw new ApiException("下载批量打款模版失败，请稍后重试");
        }
        try {
            ExcelExportUtil.exportEmptyTemplate("批量打款模版",
                    BillBatchPayment.class,
                    response,
                    "批量打款模版"
            );
        } catch (IOException e) {
            log.error("下载批量打款模版失败", e);
            throw new ApiException("下载批量打款模版失败，请稍后重试");
        }
    }

    @NotNull
    private static List<BillPaymentRecord> getBillPaymentRecords(List<BillPaymentRecord> billPaymentRecords) {
        List<BillPaymentRecord> paymentRecords = new ArrayList<>();
        billPaymentRecords.forEach(billPaymentRecord -> {
            BillPaymentRecord billPaymentRecordDto = new BillPaymentRecord();
            billPaymentRecordDto.setSubBillId(billPaymentRecord.getSubBillId());
            billPaymentRecordDto.setAmount(billPaymentRecord.getAmount());
            billPaymentRecordDto.setAlipayName(billPaymentRecord.getAlipayName());
            billPaymentRecordDto.setAlipayAccount(billPaymentRecord.getAlipayAccount());
            billPaymentRecordDto.setPaymentResult(billPaymentRecord.getPaymentResult());
            billPaymentRecordDto.setFailReason(billPaymentRecord.getFailReason());
            billPaymentRecordDto.setMatchResult(billPaymentRecord.getMatchResult());
            paymentRecords.add(billPaymentRecordDto);
        });
        return paymentRecords;
    }


    @NotNull
    private static List<BillPaymentFailed> getBillPaymentFaileds(List<BillBrandSubDto> billSubList) {
        List<BillPaymentFailed> billPaymentFailed = new ArrayList<>();
        billSubList.forEach(BillPaymentFailed ->{
            BillPaymentFailed billPaymentFailedDto = new BillPaymentFailed();
            billPaymentFailedDto.setSubBillId(BillPaymentFailed.getSubBillId());
            billPaymentFailedDto.setBillCycle(
                    BillPaymentFailed.getBillStart() + "-" +
                            BillPaymentFailed.getBillEnd()
            );
            billPaymentFailedDto.setBrandName(BillPaymentFailed.getBrandName());
            billPaymentFailedDto.setCompanyInfoName(BillPaymentFailed.getCompanyInfoName());
            billPaymentFailedDto.setDesignerName(BillPaymentFailed.getDesignerName());
            billPaymentFailedDto.setPhone(BillPaymentFailed.getPhone());
            billPaymentFailedDto.setSettlementStatus(BillPaymentFailed.getSettlementStatus());
            billPaymentFailedDto.setAmount(BillPaymentFailed.getPayableAmount().toString());
            billPaymentFailedDto.setAlipayName(BillPaymentFailed.getZfbName());
            billPaymentFailedDto.setAlipayAccount(BillPaymentFailed.getZfbAccount());
            billPaymentFailedDto.setFailReason(BillPaymentFailed.getFailReason());
            billPaymentFailed.add(billPaymentFailedDto);
        });
        return billPaymentFailed;
    }

    @NotNull
    private static List<BillWaitSettle> getBillWaitSettles(List<BillBrandSubDto> billSubList) {
        List<BillWaitSettle> billWaitSettles = new ArrayList<>();
        billSubList.forEach(billBrandSubDto -> {
            BillWaitSettle billWaitSettle = new BillWaitSettle();
            billWaitSettle.setSubBillId(billBrandSubDto.getSubBillId());
            billWaitSettle.setSettlementStatus(billBrandSubDto.getSettlementStatus());
            billWaitSettle.setBillCycle(
                    billBrandSubDto.getBillStart() + "-" +
                            billBrandSubDto.getBillEnd()
            );
            billWaitSettle.setBrandName(billBrandSubDto.getBrandName());
            billWaitSettle.setCompanyInfoName(billBrandSubDto.getCompanyInfoName());
            billWaitSettle.setAmount(billBrandSubDto.getPayableAmount());
            billWaitSettle.setAlipayName(billBrandSubDto.getZfbName());
            billWaitSettle.setAlipayAccount(billBrandSubDto.getZfbAccount());
            billWaitSettle.setDesignerName(billBrandSubDto.getDesignerName());
            billWaitSettle.setPhone(billBrandSubDto.getPhone());
            billWaitSettles.add(billWaitSettle);
        });
        return billWaitSettles;
    }

    // 参数校验
    private void validateDownloadParams(Long subBillId, Integer isFlag) {
        if (subBillId == null || subBillId <= 0) {
            log.error("子账单ID无效, subBillId = {}", subBillId);
            throw new ApiException("下载账单失败，请稍后重试");
        }

        if (isFlag == null || isFlag < 0 || isFlag > 1) {
            log.error("账单标识无效, subBillId={}, isFlag={}", subBillId, isFlag);
            throw new ApiException("下载账单失败，请稍后重试");
        }
    }

    // 构建账单基本信息
    private BillBasicInfo buildBillBasicInfo(BillBrandDetailDto billBrandDetailDto) {
        BillBasicInfo billBasicInfo = new BillBasicInfo();
        billBasicInfo.setBillCycle(
                billBrandDetailDto.getBillStart() + "-" + billBrandDetailDto.getBillEnd()
        );
        billBasicInfo.setBrandName(billBrandDetailDto.getBrandName());
        billBasicInfo.setStylistName(billBrandDetailDto.getDesignerName());
        billBasicInfo.setTotalAmount(billBrandDetailDto.getTotalCommission());
        // TODO: 获取结算主体名称
        billBasicInfo.setCompanyInfoName(null);
        billBasicInfo.setBillId(billBrandDetailDto.getBillId());
        return billBasicInfo;
    }

    // 构建账单详情列表
    private List<BillDetail> buildBillDetails(BillBrandDetailDto billBrandDetailDto, Long subBillId) {
        return billBrandDetailDto.getOrderList().stream().map(detail -> {
            BillDetail billDetail = new BillDetail();
            billDetail.setOrderNo(detail.getOrderNo());
            billDetail.setArchiveTime(detail.getArchiveTime());
            billDetail.setFranchiseName(detail.getFranchiseName());
            // TODO 获取订单完成时间
            billDetail.setFinishedTime(null);
            billDetail.setDealTime(detail.getDealTime());
            billDetail.setMoney(detail.getNowMoney());
            billDetail.setTaobaoId(detail.getCustomerNo());
            billDetail.setDesignerBusiness(detail.getBusinessName());
            billDetail.setSubBillId(subBillId);
            return billDetail;
        }).collect(Collectors.toList());
    }

    // 导出Excel
    private void exportBillToExcel(BillBasicInfo billBasicInfo, List<BillDetail> billDetails,
                                   HttpServletResponse response, Long subBillId) {
        try {
            ExcelExportUtil.exportExcelToResponse(
                    "账单",
                    "账单详情信息",
                    billBasicInfo,
                    billDetails,
                    response,
                    "账单"
            );
        } catch (IOException e) {
            log.error("导出对账账单失败，subBillId: {}", subBillId, e);
            throw new ApiException("下载账单失败，请稍后重试");
        }
    }

    private BillBrandDetailDto buildBillBrandDetailDto(DesignerBillSubDto designerBillSubDto) {
        BillBrandDetailDto billBrandDetailDto = new BillBrandDetailDto();
        billBrandDetailDto.setBillStart(designerBillSubDto.getBillPeriodStart());
        billBrandDetailDto.setDesignerName(designerBillSubDto.getStylistName());
        billBrandDetailDto.setDesignerId(designerBillSubDto.getStylistId());
        billBrandDetailDto.setBillEnd(designerBillSubDto.getBillPeriodEnd());
        billBrandDetailDto.setBillId(designerBillSubDto.getBillId());
        billBrandDetailDto.setTotalCommission(designerBillSubDto.getTotalCommission());
        billBrandDetailDto.setBrandName(designerBillSubDto.getBrandName());
        billBrandDetailDto.setNoInvoice(designerBillSubDto.getNoInvoice());
        billBrandDetailDto.setCommissionBack(designerBillSubDto.getCommissionBack());
        billBrandDetailDto.setFineAmount(designerBillSubDto.getFineAmount());
        billBrandDetailDto.setPayableAmount(designerBillSubDto.getPayableAmount());
        billBrandDetailDto.setFailReason(designerBillSubDto.getFailReason());
        return billBrandDetailDto;
    }

    private List<BillBrandSubDto> getBillBrandListByBillVo(BillVo billVo) {
        List<BillBrandSubDto> billBrandSubDtos = designerBillSubMapper.selectBillBrandSubDtoList(billVo);
        if (billBrandSubDtos != null || !billBrandSubDtos.isEmpty()) {
            billBrandSubDtos.forEach(x -> {
                x.setPhone(ParamDecryptUtil.decryptParam(x.getPhone(), ParamDecryptUtil.PHONE_KEY));
                x.setZfbName(ParamDecryptUtil.decryptParam(x.getZfbName(), ParamDecryptUtil.DESIGNER_KEY));
                x.setZfbAccount(ParamDecryptUtil.decryptParam(x.getZfbAccount(), ParamDecryptUtil.DESIGNER_KEY));
            });
        }
        return billBrandSubDtos;
    }

    /**
     * 初始化参数
     */
    private void initBillVo(BillVo billVo) {
        if (StringUtils.isNotEmpty(billVo.getPhone())) {
            billVo.setPhone(ParamDecryptUtil.encrypt(billVo.getPhone(), ParamDecryptUtil.DESIGNER_KEY));
        }
    }


}
