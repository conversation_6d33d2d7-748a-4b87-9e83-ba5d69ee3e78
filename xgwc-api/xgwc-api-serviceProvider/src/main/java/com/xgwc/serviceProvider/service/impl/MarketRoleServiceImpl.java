package com.xgwc.serviceProvider.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.FilterMenuListUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.dao.MarketRoleMapper;
import com.xgwc.serviceProvider.dao.ServiceRoleMapper;
import com.xgwc.serviceProvider.entity.dto.ServiceRoleDto;
import com.xgwc.serviceProvider.entity.param.ServiceRoleParam;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleMenuVo;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleVo;
import com.xgwc.serviceProvider.service.MarketRoleService;
import com.xgwc.serviceProvider.service.ServiceRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  11:55
 */
@Service
@Slf4j
public class MarketRoleServiceImpl implements MarketRoleService {

    @Resource
    private MarketRoleMapper marketRoleMapper;

    /**
     * 获取角色列表
     * @param marketRoleParam 服务商角色信息
     * @return 服务商角色列表
     */
    @Override
    public List<ServiceRoleVo> getMarketRoleList(ServiceRoleParam marketRoleParam) {
        marketRoleParam.setMarketId(SecurityUtils.getSysUser().getMarketId());
        return marketRoleMapper.getMarketRoleList(marketRoleParam);
    }

    /**
     * 新增角色
     * @param marketRoleDto 服务商角色信息
     * @return 添加信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // 回滚所有异常
    public ApiResult saveMarketRole(ServiceRoleDto marketRoleDto) {
        try {
            // 1. 参数校验
            if (marketRoleDto == null) {
                log.error("角色信息不能为空");
                return ApiResult.error("角色信息不能为空");
            }
            String roleName = marketRoleDto.getRoleName();

            // 2. 保存角色基本信息
            marketRoleDto.setMarketId(SecurityUtils.getSysUser().getMarketId());
            marketRoleDto.setCreateBy(SecurityUtils.getNickName());
            int saveResult = marketRoleMapper.saveMarketRole(marketRoleDto);
            if (saveResult <= 0) {
                log.error("新增角色失败，角色名称={}", roleName);
                throw new ApiException("新增角色失败"); // 抛出异常触发回滚
            }

            // 3. 保存角色-菜单关联关系
            List<ServiceRoleMenuVo> menuIds = marketRoleDto.getMenuIds();
            Long roleId = marketRoleDto.getRoleId();
            saveRoleMenuRelations(marketRoleDto, roleId, menuIds);

            log.info("角色及菜单关联保存成功，角色名称={}", roleName);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("保存角色及菜单关联异常，角色名称={}, 错误信息：{}",
                    marketRoleDto.getRoleId(), e.getMessage(), e);
            throw e; // 重新抛出异常确保事务回滚
        }
    }

    // 使用 public 确保通过代理调用（避免自调用事务失效）
    public void saveRoleMenuRelations(ServiceRoleDto marketRoleDto, Long roleId, List<ServiceRoleMenuVo> menuIds) {
        if(!CollectionUtils.isEmpty(menuIds)){
            List<ServiceRoleMenuVo> sysRoleMenuVoList = new ArrayList<>();
            for (ServiceRoleMenuVo menuId : menuIds) {
                ServiceRoleMenuVo rm = new ServiceRoleMenuVo();
                rm.setRoleId(roleId);
                rm.setMenuId(menuId.getMenuId());
                rm.setCreateBy(marketRoleDto.getCreateBy());
                sysRoleMenuVoList.add(rm);
            }
            // 批量插入，若失败则抛出异常
            int insertCount = marketRoleMapper.saveRoleMenu(sysRoleMenuVoList);
            if (insertCount <= 0 ) {
                log.error("角色-菜单关联保存不完整，预期={}，实际={}", menuIds.size(), insertCount);
                throw new ApiException("角色-菜单关联保存失败");
            }
        }
    }

    /**
     * 根据角色ID获取角色信息
     * @param roleId 角色ID
     * @return 角色信息
     */
    @Override
    public ApiResult getMarketRoleById(Long roleId) {
        if (roleId == null || roleId <= 0) {
            log.error("无效的角色ID: {}", roleId);
            return ApiResult.error("角色ID不能为空或非法");
        }
        ServiceRoleVo marketRole = marketRoleMapper.getMarketRoleById(roleId);
        if (marketRole == null) {
            log.warn("未找到对应的角色信息，roleId: {}", roleId);
            return ApiResult.error("未找到该角色的信息");
        }

        List<ServiceRoleMenuVo> filteredMenuList = FilterMenuListUtil.filterList(
                marketRoleMapper.getMarketRoleMenusById(roleId),
                roleMenuVo -> marketRoleMapper.selectLastLevelMenu(roleMenuVo.getMenuId())
        );


        // 使用 toMap 收集器根据 menuId 去重
        ServiceRoleDto marketRoleDto = new ServiceRoleDto();
        BeanUtils.copyProperties(marketRole, marketRoleDto);
        marketRoleDto.setMenuIds(filteredMenuList);

        return ApiResult.ok(marketRoleDto);
    }

    /**
     * 修改角色信息
     * @param marketRoleDto 角色信息
     * @return 修改结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateMarketRole(ServiceRoleDto marketRoleDto) {
        if (marketRoleDto == null) {
            return ApiResult.error("角色信息不能为空");
        }
        Long roleId = marketRoleDto.getRoleId();
        if (roleId == null) {
            return ApiResult.error("角色ID不能为空");
        }
        List<ServiceRoleMenuVo> menuIds = marketRoleDto.getMenuIds();
        marketRoleMapper.deleteRoleMenu(roleId);

        // 插入角色和菜单的关联关系
        saveRoleMenuRelations(marketRoleDto, roleId, menuIds);

        int i = marketRoleMapper.updateMarketRole(marketRoleDto);
        if (i > 0) {
            log.info("修改成功，id为：{}", roleId);
            return ApiResult.ok();
        }
        log.error("修改失败，id为：{}", roleId);
        throw new ApiException("角色信息修改失败,id不存在或数据未找到");
    }

    /**
     * 修改角色状态
     * @param roleId 角色ID
     * @param status 状态
     * @return 修改结果
     */
    @Override
    public ApiResult updateStatusById(Integer roleId, Integer status) {
        // 参数校验：确保 roleId 合法且 status 在允许范围内
        if (roleId == null || roleId <= 0) {
            log.error("无效的角色ID: {}", roleId);
            return ApiResult.error("更新角色状态失败");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新角色状态失败");
        }

        try {
            int i = marketRoleMapper.updateStatusById(roleId, status);
            if (i > 0) {
                log.info("更新角色状态成功，roleId: {}, 新状态: {}", roleId, status);
                return ApiResult.ok();
            } else {
                log.error("更新角色状态失败，roleId: {}，未找到记录或状态未更新", roleId);
                return ApiResult.error("状态更新失败，id不存在或数据未找到");
            }
        } catch (Exception e) {
            log.error("更新角色状态时发生异常，roleId: {}, status: {}", roleId, status, e);
            return ApiResult.error("更新角色状态失败");
        }
    }
}
