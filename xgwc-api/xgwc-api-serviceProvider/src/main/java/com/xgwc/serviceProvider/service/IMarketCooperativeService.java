package com.xgwc.serviceProvider.service;

import com.xgwc.serviceProvider.entity.dto.MarketCooperativeDto;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeQueryVo;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeStatusVo;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeVo;

import java.util.List;

public interface IMarketCooperativeService {
    /**
     * 查询销售合作
     * @param id 销售合作主键
     * @return 销售合作
     */
    MarketCooperativeDto selectMarketCooperativeById(Long id);

    /**
     * 查询销售合作列表
     * @param marketCooperative 销售合作
     * @return 销售合作集合
     */
    List<MarketCooperativeDto> selectMarketCooperativeList(MarketCooperativeQueryVo marketCooperative);

    /**
     * 新增销售合作
     * @param marketCooperative 销售合作
     * @return 结果
     */
    int insertMarketCooperative(MarketCooperativeVo marketCooperative);

    /**
     * 更新销售合作状态
     * @param dto 销售合作
     * @return 结果
     */
    int updateStatus(MarketCooperativeStatusVo dto);
}
