package com.xgwc.serviceProvider.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MarketCooperativeStatusVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    @FieldDesc("状态：0合作中，1终止合作")
    @NotNull(message = "状态不能为空")
    private Integer status;

}
