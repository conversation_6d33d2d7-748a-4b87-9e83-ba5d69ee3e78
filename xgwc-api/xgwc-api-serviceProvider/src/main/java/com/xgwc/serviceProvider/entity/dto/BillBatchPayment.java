package com.xgwc.serviceProvider.entity.dto;

import com.xgwc.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.serviceProvider.entity.dto
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-18  18:11
 */

/**
 * 导入批量打款
 */
@Data
public class BillBatchPayment {

    @Excel(name = "子账单ID", sort = 1)
    private Long subBillId;

    @Excel(name = "应发金额", sort = 2)
    private BigDecimal amount;

    @Excel(name = "支付宝姓名", sort = 3)
    private String alipayName;

    @Excel(name = "支付宝账号", sort = 4)
    private String alipayAccount;

    @Excel(name = "打款结果", sort = 5)
    private String paymentResult;

    @Excel(name = "失败原因", sort = 6)
    private String failReason;
}
