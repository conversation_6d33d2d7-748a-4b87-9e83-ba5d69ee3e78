package com.xgwc.serviceProvider.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

@Data
public class MarketCooperativeVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("销售服务商id")
    @NotNull(message = "销售服务商id不能为空")
    private Long serviceOwnerId;

    @FieldDesc("加盟商id")
    @NotNull(message = "加盟商id不能为空")
    private Long franchiseId;

    @FieldDesc("状态：0合作中，1终止合作")
    private Integer status;

}
