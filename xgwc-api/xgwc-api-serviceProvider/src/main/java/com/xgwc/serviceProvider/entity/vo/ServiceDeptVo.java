package com.xgwc.serviceProvider.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

/**
 * 服务商部门管理
 */
@Data
public class ServiceDeptVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("部门名称")
    private String deptName;

    @FieldDesc(value = "服务商id")
    private Long serviceId;

    @FieldDesc(value = "销售服务商id")
    private Long marketId;

    @FieldDesc("层级")
    private Long level;

    @FieldDesc("层级数")
    private Integer levelNum;

    @FieldDesc("父类id")
    private Long pid;

    @FieldDesc("排序：越小越前")
    private Long sort;

    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("总人数")
    private Integer totalNum;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
