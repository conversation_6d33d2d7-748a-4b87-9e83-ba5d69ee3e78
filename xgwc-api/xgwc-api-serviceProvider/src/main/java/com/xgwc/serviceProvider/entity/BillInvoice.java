package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillInvoice {

    /**
     * 主键
     */
    private Long id;

    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 发票json:发票内容，用json存储， invoiceAmount金额，invoiceUrl表示发票图片,purchaserName抬头，purchaserTaxNumber 税号
     */
    private String invoices;

    /**
     * 发票总金额
     */
    private BigDecimal totalInvoiceAmount;
    /**
     * 状态：0正常，1非正常
     */
    private Integer status;

    /**
     * 是否提供发票
     */
    private Integer provideInvoice;


    /**
     * 审核结果：0待审核，1审核成功，2.审核失败
     */
    private Integer checkStatus;

    /**
     * 审核时间
     */
    private String checkTime;

    /**
     * 备注
     */
    private String comment;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private String updateTime;
}
