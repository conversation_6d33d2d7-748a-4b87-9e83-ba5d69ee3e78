package com.xgwc.serviceProvider.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.serviceProvider.entity.BillOrderModify;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class CommissionBackDto extends BillOrderModify {

    /**
     * 品牌商名称
     */
    @FieldDesc("品牌商名称")
    private String brandName;

    /**
     * 加盟商名称
     */
    @FieldDesc("加盟商名称")
    private String franchiseName;

    /**
     * 手机号
     */
    @FieldDesc("手机号")
    private String phone;

    /**
     * 需追回佣金
     */
    @FieldDesc("需追回佣金")
    private BigDecimal needBackCommission;
}
