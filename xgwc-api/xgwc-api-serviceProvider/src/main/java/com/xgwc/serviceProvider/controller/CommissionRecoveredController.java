package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.CommissionBackDetail;
import com.xgwc.serviceProvider.entity.dto.CommissionBackDto;
import com.xgwc.serviceProvider.entity.dto.CommissionSumDto;
import com.xgwc.serviceProvider.entity.vo.CommissionBackVo;
import com.xgwc.serviceProvider.service.CommissionRecoveredService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.serviceProvider.controller
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-24  09:22
 */

/**
 * 已追回佣金
 */
@RestController
@RequestMapping("/recovered")
public class CommissionRecoveredController extends BaseController {

    @Resource
    private CommissionRecoveredService CommissionrecoveredService;


    /**
     * 品牌商已追回佣金
     */
    @MethodDesc("品牌商已追回佣金")
    @GetMapping("brand/getCommissionRecoveredList")
    public ApiResult getBrandCommissionRecoveredList(CommissionBackVo commissionBackVo) {
        startPage();
        CommissionSumDto commissionBackDtos = CommissionrecoveredService.getBrandCommissionRecoveredList(commissionBackVo);
        return ApiResult.ok(commissionBackDtos);
    }

    /**
     * 服务商已追回佣金
     */
    @MethodDesc("服务商已追回佣金")
    @GetMapping("service/getCommissionRecoveredList")
    public ApiResult getServiceCommissionRecoveredList(CommissionBackVo commissionBackVo) {
        startPage();
        CommissionSumDto commissionBackDtos = CommissionrecoveredService.getServiceCommissionRecoveredList(commissionBackVo);
        return ApiResult.ok(commissionBackDtos);
    }

    /**
     * 品牌商已追回佣金明细
     */
    @MethodDesc("品牌商已追回佣金明细")
    @GetMapping("brand/getCommissionRecoveredDetail")
    private ApiResult<CommissionBackDto> getBrandCommissionRecoveredDetail(CommissionBackVo commissionBackVo){
        if(commissionBackVo.getBillId() == null){
            return ApiResult.error("账单ID不能为空");
        }
        List<CommissionBackDto> commissionBackDtos = CommissionrecoveredService.getBrandCommissionRecoveredDetailList(commissionBackVo);
        return success(commissionBackDtos);
    }

    /**
     * 服务商已追回佣金明细
     */
    @MethodDesc("服务商已追回佣金明细")
    @GetMapping("service/getCommissionRecoveredDetail")
    private ApiResult<CommissionBackDto> getServiceCommissionRecoveredDetail(CommissionBackVo commissionBackVo){
        if(commissionBackVo.getBillId() == null){
            return ApiResult.error("账单ID不能为空");
        }
        List<CommissionBackDto> commissionBackDtos = CommissionrecoveredService.getServiceCommissionRecoveredDetailList(commissionBackVo);
        return success(commissionBackDtos);
    }

    /**
     * 设计师已追回佣金明细
     */
    @MethodDesc("设计师已追回佣金明细")
    @GetMapping("designer/getCommissionRecoveredDetail")
    private ApiResult<CommissionBackDto> getDesignerCommissionRecoveredDetail(CommissionBackVo commissionBackVo){
        if(commissionBackVo.getBillId() == null){
            return ApiResult.error("账单ID不能为空");
        }
        List<CommissionBackDto> commissionBackDtos = CommissionrecoveredService.getDesignerCommissionRecoveredDetailList(commissionBackVo);
        return success(commissionBackDtos);
    }
    
}
