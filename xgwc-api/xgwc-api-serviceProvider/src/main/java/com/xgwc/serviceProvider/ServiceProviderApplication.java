package com.xgwc.serviceProvider;

import com.xgwc.order.feign.api.OrderFeign;
import com.xgwc.settlement.feign.api.SettlementFeign;
import com.xgwc.user.feign.api.OperLogFeign;
import com.xgwc.user.feign.api.StaffFeign;
import com.xgwc.user.feign.api.StaffLogFeign;
import com.xgwc.user.feign.api.UserDetailFeign;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.serviceProvider
 * @Author: kou<PERSON>zhuo
 * @CreateTime: 2025-05-24  18:22
 */
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.xgwc.*"})
@ComponentScan("com.xgwc")
@MapperScan("com.xgwc.serviceProvider.dao")
@EnableFeignClients(clients = {StaffLogFeign.class, UserDetailFeign.class, OrderFeign.class,
        SettlementFeign.class, OperLogFeign.class, StaffFeign.class})
@Slf4j
@EnableAsync
public class ServiceProviderApplication {

    public static void main(String[] args) {
        SpringApplication.run(ServiceProviderApplication.class, args);
        log.info("项目启动成功!");
    }
}
