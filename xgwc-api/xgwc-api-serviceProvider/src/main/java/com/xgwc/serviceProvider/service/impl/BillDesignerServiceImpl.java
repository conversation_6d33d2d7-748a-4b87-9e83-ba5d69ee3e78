package com.xgwc.serviceProvider.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.serviceProvider.dao.*;
import com.xgwc.serviceProvider.entity.BillOrderModify;
import com.xgwc.serviceProvider.entity.BillOrderModifyRecord;
import com.xgwc.serviceProvider.entity.XgwcPaymentDays;
import com.xgwc.serviceProvider.entity.dto.BillComfirmDto;
import com.xgwc.serviceProvider.entity.dto.DesignerBillDto;
import com.xgwc.serviceProvider.entity.dto.DesignerBillSubDto;
import com.xgwc.serviceProvider.service.BillDesignerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class BillDesignerServiceImpl implements BillDesignerService {

    @Resource
    private DesignerBillMapper designerBillMapper;

    @Resource
    private DesignerBillSubMapper designerBillSubMapper;

    @Resource
    private BillOrderModifyMapper billOrderModifyMapper;

    @Resource
    private BillOrderModifyRecordMapper billOrderModifyRecordMapper;

    @Resource
    private BillComfirmMapper billComfirmMapper;

    @Resource
    private XgwcPaymentDaysMapper xgwcPaymentDaysMapper;

    @Transactional
    @Override
    public void comfirmBillDesigner(Long billId) {
        log.info("开始确认账单:{}", billId);
        //首先根据账单ID获取账单信息
        DesignerBillDto designerBillDto = designerBillMapper.selectDesignerBillDtoByBillId(billId);
        //锁定情况下不允许操作
        if(designerBillDto != null && (designerBillDto.getIsLock() != null && designerBillDto.getIsLock() != 1)) {
            //第二步获取子订单
            List<DesignerBillSubDto> designerBillSubDtos = designerBillSubMapper.selectDesignerBillSubListByBillId(billId);
            //处理各类金额
            handleAllTypeAmount(designerBillDto, designerBillSubDtos);
            //根据设计师获取所有未结算的订单
            List<BillOrderModifyRecord> recordList = new ArrayList<>();
            for(DesignerBillSubDto designerBillSubDto : designerBillSubDtos) {
                log.info("开始处理子订单:{}", JSON.toJSONString(designerBillSubDto));
                recordList.addAll(comfirmBillDesignerSub(designerBillSubDto));
            }
            //批量插入扣除记录
            if(!recordList.isEmpty()) {
                billOrderModifyRecordMapper.batchInsertBillOrderModifyRecordList(recordList);
            }
            preStaticsBillDesigner(designerBillSubDtos, designerBillDto);
        }
    }

    @Transactional
    @Override
    public void preStatcsBillDesigner(Long billId) {
        log.info("开始重新统计账单:{}", billId);
        comfirmBillDesigner(billId);
    }

    private void preStaticsBillDesigner(List<DesignerBillSubDto> designerBillSubDtos, DesignerBillDto designerBillDto) {
        BigDecimal commissionBack = BigDecimal.ZERO;
        BigDecimal payableAmount = BigDecimal.ZERO;
        for(DesignerBillSubDto designerBillSubDto : designerBillSubDtos) {
            BigDecimal commissionBackSub = designerBillSubDto.getCommissionBack() == null ? BigDecimal.ZERO : designerBillSubDto.getCommissionBack();
            BigDecimal payableAmountSub = designerBillSubDto.getPayableAmount() == null ? BigDecimal.ZERO : designerBillSubDto.getPayableAmount();
            commissionBack = commissionBack.add(commissionBackSub);
            payableAmount = payableAmount.add(payableAmountSub);
        }
        designerBillDto.setCommissionBack(commissionBack);
        designerBillDto.setPayableAmount(payableAmount);
        designerBillMapper.updateDesignerBillDto(designerBillDto);
    }

    private List<BillOrderModifyRecord> comfirmBillDesignerSub(DesignerBillSubDto designerBillSubDto){
        List<BillOrderModifyRecord> recordList = new ArrayList<>();
        List<BillOrderModify> billOrderModifyList = billOrderModifyMapper.getUnsettledBillOrderModifyList(designerBillSubDto.getStylistId());
        if(billOrderModifyList != null) {
            //可用金额 = 应发金额(如果为空，则为总金额-无票金额)
            BigDecimal availableAmount = designerBillSubDto.getPayableAmount() == null ? designerBillSubDto.getTotalCommission().subtract(designerBillSubDto.getNoInvoice() == null ? BigDecimal.ZERO : designerBillSubDto.getNoInvoice()) : designerBillSubDto.getPayableAmount();
            BigDecimal commissionBack = BigDecimal.ZERO;
            //售后订单中的佣金退回需要扣除佣金
            for (BillOrderModify billOrderModify : billOrderModifyList) {
                //首先绑定账期
                BigDecimal nowMoney = billOrderModify.getNowMoney() == null ? billOrderModify.getMoney() : billOrderModify.getNowMoney();
                BigDecimal money = billOrderModify.getMoney() == null ? nowMoney : billOrderModify.getMoney();
                //需追回的佣金,如果待追回金额已经算出来，则用待追回，如果没有则重新开始计算
                BigDecimal needBack = billOrderModify.getCommissionBackRemaining() == null ? money.subtract(nowMoney) : billOrderModify.getCommissionBackRemaining();
                //如果没有产生佣金变化
                if (needBack.equals(BigDecimal.ZERO)) {
                    //设置无需追回
                    billOrderModify.setBackStatus(3);
                } else {
                    //本次退回
                    BigDecimal thisTimeBack;
                    if (availableAmount.compareTo(needBack) >= 0) {
                        //如果总佣金大于需要追回的
                        billOrderModify.setCommissionBacked(needBack);
                        billOrderModify.setCommissionBackRemaining(BigDecimal.ZERO);
                        billOrderModify.setBackStatus(2);
                        thisTimeBack = needBack;
                        //扣减总佣金
                        availableAmount = availableAmount.subtract(needBack);
                        commissionBack = commissionBack.add(needBack);
                    } else {
                        //本次 = 需要的金额-总金额
                        thisTimeBack = availableAmount;
                        //如果总佣金小于需要追回的
                        billOrderModify.setCommissionBacked(availableAmount);
                        //剩余金额 = 需追回-总佣金
                        billOrderModify.setCommissionBackRemaining(needBack.subtract(availableAmount));
                        billOrderModify.setBackStatus(1);
                        commissionBack = commissionBack.add(availableAmount);
                        //全部扣完
                        availableAmount = BigDecimal.ZERO;

                    }
                    BillOrderModifyRecord billOrderModifyRecord = buildBillOrderModifyRecord(designerBillSubDto, billOrderModify, thisTimeBack);
                    recordList.add(billOrderModifyRecord);
                    log.info("更新扣除信息:{}", JSONObject.toJSONString(billOrderModifyRecord));
                    //更新扣除信息
                    billOrderModifyMapper.updateBillOrderModify(billOrderModify);
                    //可用金额小于等于0就退出循环
                    if(availableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                }
            }
            //佣金扣除 =
            designerBillSubDto.setCommissionBack(commissionBack);
            designerBillSubDto.setPayableAmount(availableAmount);
            designerBillSubMapper.updateDesignerBillSubDto(designerBillSubDto);
        }
        return recordList;
    }

    //构建参数
    private BillOrderModifyRecord buildBillOrderModifyRecord(DesignerBillSubDto designerBillSubDto, BillOrderModify billOrderModify, BigDecimal thisTimeBack) {
        BillOrderModifyRecord billOrderModifyRecord = new BillOrderModifyRecord();
        billOrderModifyRecord.setModifyId(billOrderModify.getId());
        billOrderModifyRecord.setBillStart(designerBillSubDto.getBillPeriodStart());
        billOrderModifyRecord.setBillEnd(designerBillSubDto.getBillPeriodEnd());
        billOrderModifyRecord.setCommissionBack(thisTimeBack);
        billOrderModifyRecord.setOrderId(billOrderModify.getOrderId());
        billOrderModifyRecord.setSubBillId(designerBillSubDto.getSubBillId());
        billOrderModifyRecord.setBillId(designerBillSubDto.getBillId());
        return billOrderModifyRecord;
    }

    /**
     * 处理各类金额
     */
    private void handleAllTypeAmount(DesignerBillDto designerBillDto, List<DesignerBillSubDto> designerBillSubDtos){
        log.info("开始处理账单金额,账单id:{}", designerBillDto.getBillId());
        BillComfirmDto billComfirmDto = billComfirmMapper.selectBillComfirmByBillId(designerBillDto.getBillId());
        if(billComfirmDto != null) {
            if(billComfirmDto.getProvideInvoice() == 1) {
                log.info("账单:{},提供发票不需要进行无票扣款", designerBillDto.getBillId());
            }else{
                log.info("账单:{},没有提供发票需要进行无票扣款", designerBillDto.getBillId());
                handleNoInvoice(designerBillDto, designerBillSubDtos);
            }
        }
    }

    /**
     * 处理无票扣款
     */
    private void handleNoInvoice(DesignerBillDto designerBillDto, List<DesignerBillSubDto> designerBillSubDtos){
        //查看扣款费率
        XgwcPaymentDays xgwcPaymentDays = xgwcPaymentDaysMapper.selectByBrandId(designerBillDto.getBrandOwnerId(), 1);
        if(xgwcPaymentDays != null) {
            BigDecimal noInvoiceRate = xgwcPaymentDays.getTaxMoney() == null ? BigDecimal.ZERO : xgwcPaymentDays.getTaxMoney();
            log.info("品牌商:{},扣款费率为:{}", designerBillDto.getBrandOwnerId(), noInvoiceRate);
            designerBillDto.setNoInvoice(designerBillDto.getTotalCommission().multiply(noInvoiceRate).multiply(new BigDecimal("0.01")));
            designerBillSubDtos.forEach(x-> {
                x.setNoInvoice(x.getTotalCommission().multiply(noInvoiceRate).multiply(new BigDecimal("0.01")));
            });
        }else{
            log.info("品牌商:{},未设置无票扣款", designerBillDto.getBrandOwnerId());
        }
    }
}
