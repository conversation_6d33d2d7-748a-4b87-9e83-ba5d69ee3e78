package com.xgwc.serviceProvider.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class CommissionBackVo {

    /**
     * id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 客户ID
     */
    private String taobaoId;

    /**
     * 谈单人
     */
    private String saleManName;

    /**
     * 加盟商ID
     */
    private Long franchiseId;

    /**
     * 账单开始
     */
    private String billStart;

    /**
     * 账单结束
     */
    private String billEnd;

    /**
     * 修改开始时间
     */
    private String updateTimeStart;

    /**
     * 修改开始结束时间
     */
    private String updateTimeEnd;

    /**
     * 追回状态
     */
    private Integer backStatus;

    /**
     * 是否已经追回：0 需追回，1 已追回
     */
    private int backed;

    /**
     * 品牌商id列表
     */
    private List<Long> brandIds;

    /**
     * 品牌商id
     */
    private Long brandId;

    /**
     * 设计师ID
     */
    private Long designerId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 追回id
     */
    private Long backId;

    /**
     * 账单id
     */
    private Long billId;

    /**
     * 追回时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[] backTime;
}
