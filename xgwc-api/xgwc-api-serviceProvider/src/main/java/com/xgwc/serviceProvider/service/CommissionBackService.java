package com.xgwc.serviceProvider.service;

import com.xgwc.serviceProvider.entity.dto.CommissionBackDetail;
import com.xgwc.serviceProvider.entity.dto.CommissionSumDto;
import com.xgwc.serviceProvider.entity.vo.CommissionBackVo;

import java.math.BigDecimal;
import java.util.List;

public interface CommissionBackService {

    /**
     * 查询佣金追回列表
     *
     * @param commissionBackVo 参数
     * @return 佣金追回列表
     */
    CommissionSumDto getBrandCommissionBackList(CommissionBackVo commissionBackVo);

    /**
     * 查询服务商佣金追回列表
     *
     * @param commissionBackVo 参数
     * @return 佣金追回列表
     */
    CommissionSumDto getServiceCommissionBackList(CommissionBackVo commissionBackVo);

    /**
     * 查询佣金追回列表
     *
     * @param commissionBackVo 参数
     * @return 佣金追回列表
     */
    CommissionSumDto getDesignerCommissionBackList(CommissionBackVo commissionBackVo);

    /**
     * 设计师获取佣金追回明细
     * @param commissionBackVo 参数
     * @return 明细
     */
    List<CommissionBackDetail> getDesignerCommissionBackDetailList(CommissionBackVo commissionBackVo);

    /**
     * 服务商获取佣金追回明细
     * @param commissionBackVo 参数
     * @return 明细
     */
    List<CommissionBackDetail> getServiceCommissionBackDetailList(CommissionBackVo commissionBackVo);

    /**
     * 品牌商获取佣金追回明细
     * @param commissionBackVo 参数
     * @return 明细
     */
    List<CommissionBackDetail> getBrandCommissionBackDetailList(CommissionBackVo commissionBackVo);

    /**
     * 获取追回总额
     * @param commissionBackVo 参数
     * @return 佣金总额
     */
    BigDecimal getTotalCommissionBack(CommissionBackVo commissionBackVo);
}
