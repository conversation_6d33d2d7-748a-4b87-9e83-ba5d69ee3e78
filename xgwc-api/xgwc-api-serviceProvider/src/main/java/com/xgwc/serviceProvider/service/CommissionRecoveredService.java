package com.xgwc.serviceProvider.service;

import com.xgwc.serviceProvider.entity.dto.CommissionBackDto;
import com.xgwc.serviceProvider.entity.dto.CommissionSumDto;
import com.xgwc.serviceProvider.entity.vo.CommissionBackVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.serviceProvider.service
 * @Author: kouwenzhuo
 * @CreateTime: 2025-07-24  09:25
 */
public interface CommissionRecoveredService {

    /**
     * 品牌商已追回佣金
     *
     * @param commissionBackVo 参数
     * @return 已追回佣金
     */
    CommissionSumDto getBrandCommissionRecoveredList(CommissionBackVo commissionBackVo);

    /**
     * 服务商已追回佣金
     *
     * @param commissionBackVo 参数
     * @return 已追回佣金
     */
    CommissionSumDto getServiceCommissionRecoveredList(CommissionBackVo commissionBackVo);

    /**
     * 品牌商已追回佣金明细
     * @param commissionBackVo 参数
     * @return 明细
     */
    List<CommissionBackDto> getBrandCommissionRecoveredDetailList(CommissionBackVo commissionBackVo);

    /**
     * 服务商已追回佣金明细
     * @param commissionBackVo 参数
     * @return 明细
     */
    List<CommissionBackDto> getServiceCommissionRecoveredDetailList(CommissionBackVo commissionBackVo);

    /**
     * 设计师已追回佣金明细
     * @param commissionBackVo 参数
     * @return 明细
     */
    List<CommissionBackDto> getDesignerCommissionRecoveredDetailList(CommissionBackVo commissionBackVo);
}
