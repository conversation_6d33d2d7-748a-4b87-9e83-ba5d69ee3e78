package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.util.Date;

/**
 * 员工档案
 */
@Data
public class MarketEmployees {

private static final long serialVersionUID=1L;

    /** 员工唯一标识 */
    private Long employeeId;

    /** 员工外键 */
    private Long staffId;

    /** 员工账户外键 */
    private Long accountId;

    /** 员工附件外键 */
    private Long attachmentId;

    /** 销售服务商id */
    private Long marketId;

    /** 员工姓名 */
    private String name;

    /** 用户性别（0男 1女 2未知） */
    private Long sex;

    /** 民族 */
    private Integer ethnicity;

    /** 学历 */
    private Integer education;

    /** 出生日期 */
    private Date birthdate;

    /** 政治面貌 */
    private Integer politicalStatus;

    /** 婚姻状况 */
    private Integer maritalStatus;

    /** 身份证号 */
    private String idNumber;

    /** 电子邮箱 */
    private String email;

    /** 手机号码 */
    private String phone;

    /** 联系地址 */
    private String address;

    /** 紧急联系人姓名 */
    private String emerName;

    /** 紧急联系人电话 */
    private String emerPhone;

    /** 入职日期 */
    private Date entryDate;

    /** 合同到期日期 */
    private Date contractEndDate;

    /** 社保状态，（0-未买，1-已买，2-停保） */
    private Long socialStatus;

    /** 购买社保日期 */
    private Date buySocialDate;

    /** 是否转正，（0-否，1-是） */
    private Long probationStatus;

    /** 转正日期 */
    private Date probationEndDate;

    /** 年假天数 */
    private Long annualLeaveDays;

    /** 离职日期 */
    private Date resignationDate;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;



}