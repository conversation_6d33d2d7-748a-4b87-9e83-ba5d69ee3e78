package com.xgwc.serviceProvider.util;

import com.alibaba.cloud.commons.lang.StringUtils;

public class ServiceStaffLogParseUtil {

    public static String parseLogContentToReadable(String rawContent,int businessType) {
        if (StringUtils.isBlank(rawContent)) return "";
        StringBuilder sb = new StringBuilder();
        String[] parts = rawContent.split("；");
        for (String part : parts) {
            if (!part.contains(":")) continue;
            String[] fieldSplit = part.split(":");
            String fieldName = fieldSplit[0].trim();
            String valuePart = fieldSplit[1].trim();
            String[] values = valuePart.split(">");
            if (values.length != 2) {
                sb.append(part).append("；");
                continue;
            }
            String oldVal = values[0].trim();
            String newVal = values[1].trim();
            String oldLabel = "";
            String newLabel = "";
            if(businessType == 3){
                oldLabel = ServiceFieldValueResolver.resolve(fieldName, oldVal);
                newLabel = ServiceFieldValueResolver.resolve(fieldName, newVal);
            }else if(businessType == 4){
                oldLabel = MarketFieldValueResolver.resolve(fieldName, oldVal);
                newLabel = MarketFieldValueResolver.resolve(fieldName, newVal);
            }
            sb.append(String.format("%s: %s > %s；", fieldName, oldLabel, newLabel));
        }
        return sb.toString();
    }
}
