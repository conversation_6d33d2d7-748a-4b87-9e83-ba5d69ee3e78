package com.xgwc.serviceProvider.entity.param;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-28  11:22
 */

/**
 * 服务商部门管理参数
 */
@Data
public class ServiceDeptParam {

    /**
     * 品牌名称
     */
    private String deptName;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 服务商id
     */
    @FieldDesc(value = "服务商id")
    private Long serviceId;

    /**
     * 销售服务商id
     */
    @FieldDesc(value = "销售服务商id")
    private Long marketId;
}
