package com.xgwc.serviceProvider.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.serviceProvider.dao.ServiceDeptMapper;
import com.xgwc.serviceProvider.entity.dto.ServiceDeptDto;
import com.xgwc.serviceProvider.entity.param.ServiceDeptParam;
import com.xgwc.serviceProvider.entity.vo.ServiceDeptInfo;
import com.xgwc.serviceProvider.entity.vo.ServiceDeptVo;
import com.xgwc.serviceProvider.entity.vo.ServiceStaffVo;
import com.xgwc.serviceProvider.service.ServiceDeptService;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  11:36
 */
@Service
@Slf4j
public class ServiceDeptServiceImpl implements ServiceDeptService {

    @Resource
    private ServiceDeptMapper serviceDeptMapper;

    /**
     * 获取所有部门列表
     * @param serviceDeptParam 部门参数
     * @return 部门列表
     */
    @Override
    public List<ServiceDeptInfo> getServiceDeptList(ServiceDeptParam serviceDeptParam) {
        serviceDeptParam.setServiceId(SecurityUtils.getSysUser().getServiceId());
        List<ServiceDeptVo> deptList = serviceDeptMapper.getServiceDeptList(serviceDeptParam);

        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }

        if (deptList.stream().anyMatch(dept -> dept.getPid() == null)) {
            log.error("获取部门信息失败，参数不完整缺少pid");
            return Collections.emptyList();
        }

        // 如果是搜索/筛选条件，直接返回扁平结构（不构建树）
        if (StringUtils.isNotEmpty(serviceDeptParam.getDeptName())) {
            return deptList.stream()
                    .map(deptVo -> {
                        ServiceDeptInfo deptInfo = new ServiceDeptInfo();
                        BeanUtils.copyProperties(deptVo, deptInfo);
                        deptInfo.setLevelNum(1);
                        // 查询当前部门的员工人数
                        deptInfo.setTotalNum(getDeptStaffCount(deptVo.getDeptId()));
                        return deptInfo;
                    })
                    .collect(Collectors.toList());
        }

        // 按pid分组（构建树形结构）
        Map<Long, List<ServiceDeptVo>> pidToChildrenMap = deptList.stream()
                .collect(Collectors.groupingBy(ServiceDeptVo::getPid));

        // 构建树形结构（从顶级节点开始）
        return deptList.stream()
                .filter(dept -> dept.getPid() == null || dept.getPid() == 0)
                .map(rootDept -> {
                    ServiceDeptInfo rootInfo = new ServiceDeptInfo();
                    BeanUtils.copyProperties(rootDept, rootInfo);
                    rootInfo.setLevelNum(1);
                    // 递归构建子树并计算人数
                    rootInfo.setChiledrenList(buildDeptTree(rootInfo, pidToChildrenMap));
                    // 计算总人数（包括子部门）
                    rootInfo.setTotalNum(calculateTotalStaff(rootInfo));
                    return rootInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 递归构建部门树
     */
    private List<ServiceDeptInfo> buildDeptTree(ServiceDeptInfo parentDept,
                                                  Map<Long, List<ServiceDeptVo>> pidToChildrenMap) {
        List<ServiceDeptVo> children = pidToChildrenMap.get(parentDept.getDeptId());
        if (CollectionUtils.isEmpty(children)) {
            // 叶子节点：直接查询当前部门的员工人数
            parentDept.setTotalNum(getDeptStaffCount(parentDept.getDeptId()));
            return Collections.emptyList();
        }

        // 递归处理子节点
        List<ServiceDeptInfo> childInfos = children.stream()
                .map(childDept -> {
                    ServiceDeptInfo childInfo = new ServiceDeptInfo();
                    BeanUtils.copyProperties(childDept, childInfo);
                    childInfo.setLevelNum(parentDept.getLevelNum() + 1);
                    childInfo.setChiledrenList(buildDeptTree(childInfo, pidToChildrenMap));
                    return childInfo;
                })
                .collect(Collectors.toList());

        // 计算当前节点的总人数（如果子节点人数为0，则使用当前部门的员工数）
        int childTotal = calculateTotalStaff(parentDept);
        if (childTotal == 0) {
            parentDept.setTotalNum(getDeptStaffCount(parentDept.getDeptId()));
        } else {
            parentDept.setTotalNum(childTotal);
        }

        return childInfos;
    }

    /**
     * 计算部门及其子部门的总人数（包括当前部门自身）
     */
    private int calculateTotalStaff(ServiceDeptInfo deptInfo) {
        // 1. 获取当前部门的员工人数（自身）
        int currentDeptStaff = getDeptStaffCount(deptInfo.getDeptId());

        // 2. 递归计算子部门的总人数
        int childrenTotal = 0;
        if (!CollectionUtils.isEmpty(deptInfo.getChiledrenList())) {
            childrenTotal = deptInfo.getChiledrenList().stream()
                    .mapToInt(this::calculateTotalStaff) // 递归计算子部门总人数
                    .sum();
        }

        // 3. 返回总人数 = 当前部门人数 + 子部门人数
        return currentDeptStaff + childrenTotal;
    }

    /**
     * 查询指定部门的员工人数
     */
    private int getDeptStaffCount(Long deptId) {
        Integer count = serviceDeptMapper.selectDeptStaffNum(deptId);
        return count != null ? count : 0;
    }
    

    /**
     * 新增部门
     * @param serviceDeptDto 部门信息
     * @return 新增结果
     */
    @Override
    public ApiResult saveServiceDept(ServiceDeptDto serviceDeptDto) {
        // 参数校验
        if (serviceDeptDto == null) {
            log.error("参数校验失败，入参不能为空");
            return ApiResult.error("参数无效，入参不能为空");
        }

        ApiResult<Object> error = getObjectApiResult(serviceDeptDto);
        if (error != null) return error;

        try {
            serviceDeptDto.setCreateBy(SecurityUtils.getNickName());
            serviceDeptDto.setServiceId(SecurityUtils.getSysUser().getServiceId());
            int result = serviceDeptMapper.saveServiceDept(serviceDeptDto);
            if (result > 0) {
                log.info("新增成功，deptId：{}，参数：{}", serviceDeptDto.getDeptId(), serviceDeptDto);
                return ApiResult.ok();
            } else {
                log.error("新增失败，deptId：{}可能存在重复或其他约束冲突，参数：{}", serviceDeptDto.getDeptId(), serviceDeptDto);
                return ApiResult.error("新增失败，可能原因：ID已存在或其他约束冲突");
            }
        } catch (Exception e) {
            log.error("数据库操作异常，deptId：{}，参数：{}", serviceDeptDto.getDeptId(), serviceDeptDto, e);
            return ApiResult.error("数据库操作失败，请检查日志");
        }
    }

    @Nullable
    private static ApiResult<Object> getObjectApiResult(ServiceDeptDto serviceDeptDto) {
        if (serviceDeptDto.getPid() == null) {
            log.error("参数校验失败，pid不能为空");
            return ApiResult.error("参数无效，pid不能为空");
        }
        if (serviceDeptDto.getDeptName() == null) {
            log.error("参数校验失败，deptName不能为空");
            return ApiResult.error("参数无效，deptName不能为空");
        }
        return null;
    }
    /**
     * 根据部门ID查询部门信息
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public ApiResult getServiceDeptById(Long deptId) {
        if (deptId == null) {
            return ApiResult.error("id不能为空");
        }
        ServiceDeptDto serviceDeptDto = serviceDeptMapper.getServiceDeptById(deptId);
        if (serviceDeptDto == null) {
            log.error("查询失败，id：{}不存在", deptId);
            return ApiResult.error("id不存在");
        }

        Long parentId = serviceDeptDto.getPid();
        if(parentId > 0) {
            ServiceDeptDto parentDept = serviceDeptMapper.getServiceDeptById(parentId);
            if (parentDept != null) {
                serviceDeptDto.setDeptName(parentDept.getDeptName());
                serviceDeptDto.setPid(parentDept.getDeptId());
            }
            return ApiResult.ok(serviceDeptDto);
        }
        log.info("查询成功，id:{}", deptId);
        return ApiResult.ok(serviceDeptDto);
    }

    @Override
    public ApiResult getUserDeptByUserId(Long userId) {
        return ApiResult.ok(serviceDeptMapper.getUserDeptByUserId(userId));
    }


    /**
     * 修改部门信息
     * @param serviceDeptDto 部门信息
     * @return 修改结果
     */
    @Override
    public ApiResult updateServiceDeptById(ServiceDeptDto serviceDeptDto) {
        try {
            if (serviceDeptDto == null || serviceDeptDto.getDeptId() == null) {
                log.error("参数校验失败，deptId不能为空");
                return ApiResult.error("参数校验失败，deptId不能为空");
            }

            ApiResult<Object> error = getObjectApiResult(serviceDeptDto);
            if (error != null) return error;

            serviceDeptDto.setUpdateBy(SecurityUtils.getNickName());
            int result = serviceDeptMapper.updateServiceDeptById(serviceDeptDto);
            if (result > 0) {
                log.info("========================id:{},修改成功", serviceDeptDto.getDeptId());
                return ApiResult.ok();
            }
            log.error("修改失败，id：{}不存在或数据未变化", serviceDeptDto.getDeptId());
            return ApiResult.error("修改失败,id不存在");
        } catch (Exception e) {
            log.error("修改过程中发生异常，id：{}", serviceDeptDto.getDeptId(), e);
            return ApiResult.error("修改失败,系统异常");
        }
    }



    /**
     * 修改部门状态
     * @param deptId 部门ID
     * @param status 部门状态
     * @return 修改结果
     */
    @Override
    public ApiResult updateStatusById(Integer deptId, Integer status) {
        // 参数校验：确保 deptId 合法且 status 在允许范围内
        if (deptId == null || deptId <= 0) {
            log.error("无效的部门ID: {}", deptId);
            return ApiResult.error("更新部门状态失败");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新部门状态失败");
        }

        try {
            int result = serviceDeptMapper.updateStatusById(deptId, status);
            if (result > 0) {
                log.info("更新部门状态成功，brandId: {}, 新状态: {}", deptId, status);
                return ApiResult.ok();
            } else {
                log.error("更新部门状态失败，brandId: {}，未找到记录或状态未更新", deptId);
                return ApiResult.error("更新部门状态失败");
            }
        } catch (Exception e) {
            log.error("更新部门状态时发生异常，brandId: {}, status: {}", deptId, status, e);
            return ApiResult.error("更新部门状态失败");
        }
    }


    /**
     * 获取部门员工信息
     * @param deptId 部门ID
     * @return 部门员工信息
     */
    @Override
    public ApiResult getXgwcDeptStaffInfo(Integer deptId) {
        try {
            // 1. 查询数据
            ServiceStaffVo manager = serviceDeptMapper.selectXgwcDeptStaffManage(deptId);
            List<ServiceStaffVo> assistants = serviceDeptMapper.selectXgwcDeptStaffSchedule(deptId);

            // 2. 构建响应对象
            ServiceDeptDto response = new ServiceDeptDto();
            response.setIsPrincipal(getValidStaff(manager));
            response.setIsAssistant(getValidStaffList(assistants));

            return ApiResult.ok(response);
        } catch (Exception e) {
            log.error("获取部门员工信息失败，deptId: {}", deptId, e);
            throw new ApiException("系统异常，请稍后重试");
        }
    }

    /**
     * 确保 StaffVo 不为 null，为空时返回默认实例
     */
    private ServiceStaffVo getValidStaff(ServiceStaffVo staff) {
        return staff != null ? staff : new ServiceStaffVo();
    }

    /**
     * 确保 Staff 列表不为 null，为空时返回不可变的空列表
     */
    private List<ServiceStaffVo> getValidStaffList(List<ServiceStaffVo> staffList) {
        return staffList != null ? staffList : Collections.emptyList();
    }


    /**
     * 修改部门员工信息
     * @param serviceDeptDto 部门员工信息
     * @return 修改结果
     */
    @Override
    public ApiResult updateXgwcDeptStaffInfo(ServiceDeptDto serviceDeptDto) {
        // 参数校验
        if (serviceDeptDto == null) {
            return ApiResult.error("参数不能为空");
        }

        ServiceStaffVo isPrincipal = serviceDeptDto.getIsPrincipal();
        if (isPrincipal != null) {
            int assistant = serviceDeptMapper.updateDeptStaffAssistant(isPrincipal.getDeptId());
            int manageResult = serviceDeptMapper.updateDeptStaffManage(isPrincipal);
            if (manageResult <= 0 && assistant <= 0) {
                log.warn("更新部门负责人、排班信息失败，参数：{}", isPrincipal);
                throw new ApiException("更新部门负责人、排班信息失败");
            }
        }

        List<ServiceStaffVo> isAssistant = serviceDeptDto.getIsAssistant();
        if (!CollectionUtils.isEmpty(isAssistant)) {
            for (ServiceStaffVo staff : isAssistant) {
                if (isPrincipal != null && isPrincipal.getIsSchedule() != null){
                    staff.setIsSchedule(isPrincipal.getIsSchedule());
                }
                int scheduleResult = serviceDeptMapper.updateDeptStaffSchedule(staff);
                if (scheduleResult <= 0) {
                    log.warn("更新部门助理信息失败，参数：{}", isAssistant);
                    throw new ApiException("更新部门助理信息失败");
                }
            }
        }

        log.info("部门人员信息更新成功");
        return ApiResult.ok();
    }
}
