package com.xgwc.serviceProvider.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleMenuVo;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: k<PERSON><PERSON><PERSON>huo
 * @CreateTime: 2025-04-22  14:59
 */

/**
 * 服务商角色表
 */
@Data
public class ServiceRoleDto {

    /**
     * 角色ID
     */
    @FieldDesc("角色ID")
    private Long roleId;

    @FieldDesc("岗位ID")
    private Long stationId;

    /**
     * 岗位名称
     */
    @FieldDesc("角色名称")
    private String roleName;

    @FieldDesc("标识字段")
    private String isFlag;

    /**
     * 服务商id
     */
    @FieldDesc(value = "服务商id")
    private Long serviceId;

    /**
     * 销售服务商id
     */
    @FieldDesc(value = "销售服务商id")
    private Long marketId;

    /** 菜单组 */
    @FieldDesc("菜单权限组")
    private List<ServiceRoleMenuVo> menuIds;

    /**
     * 排序：越小越前
     */
    @FieldDesc("排序：越小越前")
    private Integer sort;

    /**
     * 状态：0正常，1禁用
     */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    /**
     * 是否删除：0正常，1删除
     */
    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    /** 创建人 */
    @FieldDesc("创建人")
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @FieldDesc("创建时间")
    private Date createTime;

    /** 修改人 */
    @FieldDesc("修改人")
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @FieldDesc("修改时间")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @FieldDesc("行更新时间")
    private Date modifyTime;
}
