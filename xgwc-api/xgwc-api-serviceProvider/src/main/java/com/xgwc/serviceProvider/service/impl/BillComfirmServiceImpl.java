package com.xgwc.serviceProvider.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.serviceProvider.dao.BillComfirmMapper;
import com.xgwc.serviceProvider.dao.DesignerBillMapper;
import com.xgwc.serviceProvider.dao.PaymentDaysMapper;
import com.xgwc.serviceProvider.entity.BillComfirm;
import com.xgwc.serviceProvider.entity.BillComfirmCheck;
import com.xgwc.serviceProvider.entity.BillComfirmDetail;
import com.xgwc.serviceProvider.entity.DesignerBill;
import com.xgwc.serviceProvider.entity.dto.*;
import com.xgwc.serviceProvider.service.BillComfirmService;
import com.xgwc.serviceProvider.service.BillDesignerService;
import com.xgwc.settlement.feign.api.SettlementFeign;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class BillComfirmServiceImpl implements BillComfirmService {

    @Resource
    private BillComfirmMapper billComfirmMapper;

    @Resource
    private DesignerBillMapper designerBillMapper;

    @Resource
    private PaymentDaysMapper paymentDaysMapper;

    @Resource
    private BillDesignerService billDesignerService;

    @Resource
    private SettlementFeign settlementFeign;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertBillComfirm(BillComfirm billComfirm) {
        //默认为0
        billComfirm.setProvideInvoice(billComfirm.getProvideInvoice() == null ? 0 : billComfirm.getProvideInvoice());
        SysUser sysUser = SecurityUtils.getSysUser();
        if(billComfirm.getProvideInvoice() != null && billComfirm.getProvideInvoice() == 1 && StringUtils.isEmpty(billComfirm.getInvoices())){
            log.warn("发票数据为空:{}", JSONObject.toJSONString(billComfirm));
            return 0;
        }
        if(billComfirm.getBillId() == null){
            log.warn("账单id不能为空:{}", JSONObject.toJSONString(billComfirm));
        }
        List<BillComfirmDetail> billComfirmDetails = JSONObject.parseArray(billComfirm.getInvoices(), BillComfirmDetail.class);
        BigDecimal totalAmount = getTotalAmount(billComfirmDetails);
        BillComfirmDto billComfirmDto = billComfirmMapper.selectBillComfirmByBillId(billComfirm.getBillId());
        if(billComfirmDto !=null){
            if(!Objects.equals(billComfirmDto.getUserId(), sysUser.getUserId())){
                log.error("非自己提交的发票不能修改:{}, 修改人:{}", JSONObject.toJSONString(billComfirmDto), JSONObject.toJSONString(sysUser));
                return 0;
            }
            billComfirm.setId(billComfirmDto.getId());
        }
        billComfirm.setTotalInvoiceAmount(totalAmount);
        billComfirm.setStatus(0);
        billComfirm.setCheckStatus(0);
        billComfirm.setUserId(sysUser.getUserId());
        billComfirm.setCreateBy(sysUser.getUserName());
        billComfirm.setUpdateBy(sysUser.getUserName());
        int result;
        if(billComfirm.getId() == null) {
            result =  billComfirmMapper.insertBillComfirm(billComfirm);
            comfirmBill(billComfirm);
        }else{
            result = billComfirmMapper.updateBillComfirm(billComfirm);
        }
        handleComfirmDetail(billComfirmDetails, billComfirm.getId());
        //如果设置的无票，则无需审核，直接确认，生成账单
        if(billComfirm.getProvideInvoice() == 0){
            settlementFeign.preStaticsCommissionBack(billComfirm.getBillId());
        }
        return result;
    }

    /**
     * 账单确认
     */
    private void comfirmBill(BillComfirm billComfirm) {
        DesignerBill designerBill = new DesignerBill();
        designerBill.setBillId(billComfirm.getBillId());
        designerBill.setConfirmationTime(DateUtils.getLongDateStr());
        if(billComfirm.getProvideInvoice() == 1){
            //待审核状态
            designerBill.setSettlementStatus(4);
        }
        designerBillMapper.updateDesignerBillDto(designerBill);
    }

    /**
     * 处理发票详情
     */
    private void handleComfirmDetail(List<BillComfirmDetail> billComfirmDetails, Long invoiceId) {
        billComfirmDetails.forEach(x->{
            x.setBillComfirmId(invoiceId);
        });
        billComfirmMapper.deleteBillComfirmDetail(invoiceId);
        billComfirmMapper.batchInsertBillComfirmDetails(billComfirmDetails);
    }

    /**
     * 处理发票明细
     */
    private BigDecimal getTotalAmount(List<BillComfirmDetail> billComfirmDetails) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for(BillComfirmDetail billComfirmDetail : billComfirmDetails){
            totalAmount = totalAmount.add(billComfirmDetail.getInvoiceAmount() == null ? BigDecimal.ZERO : billComfirmDetail.getInvoiceAmount());
        }
        return totalAmount;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertBillVoiceCheck(BillComfirmCheck billComfirmCheck) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(billComfirmCheck.getBillComfirmId() == null){
            log.warn("发票ID不存在:{}", JSONObject.toJSONString(billComfirmCheck));
            return 0;
        }
        BillComfirmDto billComfirmDto = billComfirmMapper.selectBillComfirmById(billComfirmCheck.getBillComfirmId());
        if(billComfirmDto != null){
            BillComfirm newBillComfirm = new BillComfirm();
            newBillComfirm.setId(billComfirmDto.getId());
            newBillComfirm.setCheckStatus(billComfirmCheck.getCheckStatus());
            newBillComfirm.setCheckTime(DateUtils.getLongDateStr());
            newBillComfirm.setComment(billComfirmCheck.getComment());
            billComfirmMapper.updateBillComfirm(newBillComfirm);
        }else {
            log.error("订单确认ID不存在:{}, 操作人:{}", JSONObject.toJSONString(billComfirmCheck), JSONObject.toJSONString(sysUser));
            return 0;
        }
        billComfirmCheck.setCheckUserId(sysUser.getUserId());
        billComfirmCheck.setCheckUserName(sysUser.getUserName());
        billComfirmCheck.setCreateBy(sysUser.getUserName());
        //修改订单结算状态
        handleSettlementStatus(billComfirmDto, billComfirmCheck.getCheckStatus());
        return billComfirmMapper.insertBillVoiceCheck(billComfirmCheck);
    }

    /**
     * 处理结算状态
     */
    private void handleSettlementStatus(BillComfirmDto billComfirmDto, Integer checkStatus) {
        DesignerBill designerBill = new DesignerBill();
        designerBill.setBillId(billComfirmDto.getBillId());
        boolean isComfirm = false;
        if(checkStatus == 1){
            //发票审核通过变成待结算
            designerBill.setIsInvoice(billComfirmDto.getProvideInvoice() == null ? 0 : billComfirmDto.getProvideInvoice());
            designerBill.setSettlementStatus(2);
            //审核通过，则开始结算账单
            isComfirm = true;
        }else{
            //发票审核失败变成审核失败状态
            designerBill.setSettlementStatus(5);
        }
        designerBillMapper.updateDesignerBillDto(designerBill);
        if(isComfirm){
            log.info("订单确认审核完成,开始调用执行统计佣金追回:{}", JSONObject.toJSONString(billComfirmDto));
            settlementFeign.preStaticsCommissionBack(billComfirmDto.getBillId());
        }
    }

    @Override
    public int updateBillComfirm(BillComfirm billComfirm) {
        if(billComfirm.getId() == null){
            log.warn("修改账单发票id不存在:{}", JSONObject.toJSONString(billComfirm));
            return 0;
        }
        return billComfirmMapper.updateBillComfirm(billComfirm);
    }

    @Override
    public BillComfirmDto selectBillComfirmById(Long billId) {
        if(billId == null){
            log.warn("账单ID不能为空:{}", billId);
            return null;
        }
        BillComfirmDto billComfirmDto = billComfirmMapper.selectBillComfirmByBillId(billId);
        if(billComfirmDto != null){
            List<BillComfirmCheckDto> billComfirmCheckDtos = billComfirmMapper.selectBillComfirmCheckList(billComfirmDto.getId());
            billComfirmDto.setComfirmChecks(billComfirmCheckDtos);
        }else{
            billComfirmDto = new BillComfirmDto();
        }
        DesignerBillDto designerBillDto = designerBillMapper.selectDesignerBillDtoByBillId(billId);
        if(designerBillDto != null){
            billComfirmDto.setBillEnd(designerBillDto.getBillPeriodEnd());
            billComfirmDto.setBillStart(designerBillDto.getBillPeriodStart());
            billComfirmDto.setDesignerId(designerBillDto.getStylistId());
            billComfirmDto.setDesignerName(designerBillDto.getStylistName());
            billComfirmDto.setTotalCommission(designerBillDto.getTotalCommission());
            billComfirmDto.setBackCommission(designerBillDto.getCommissionBack());
        }
        List<BillCompanyInfoDto> companyInfoDtoList = billComfirmMapper.selectBillComfirmCompanyList(billId);
        billComfirmDto.setCompanyInfoList(companyInfoDtoList);
        PaymentDaysDto paymentDaysDto = paymentDaysMapper.selectPaymentDays(designerBillDto.getBrandOwnerId(), 1);
        billComfirmDto.setFineAmount(BigDecimal.ZERO);
        billComfirmDto.setNoInvoiceRate(paymentDaysDto == null ? BigDecimal.ZERO : paymentDaysDto.getTaxMoney());
        return billComfirmDto;
    }

    @Override
    public List<BillComfirmCheckDto> selectBillComfirmCheckList(Long billComfirmId) {
        if(billComfirmId == null){
            log.warn("账单发票ID不能为空:{}", billComfirmId);
        }
        return billComfirmMapper.selectBillComfirmCheckList(billComfirmId);
    }
}
