package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.ServiceStationDto;
import com.xgwc.serviceProvider.entity.param.ServiceStationParam;
import com.xgwc.serviceProvider.entity.vo.ServiceStationVo;
import com.xgwc.serviceProvider.service.MarketStationService;
import com.xgwc.serviceProvider.service.ServiceStationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 销售服务商岗位管理
 */
@RestController
@RequestMapping("/marketStation")
@Slf4j
public class MarketStationController extends BaseController {

    @Resource
    private MarketStationService marketStationService;

    /**
     * @param marketStationParam 查询条件
     * @return 岗位管理列表
     * 查询岗位管理列表
     */
    @MethodDesc("查询岗位管理列表")
    @PreAuthorize("@ss.hasPermission('marketStation:marketStation:list')")
    @PostMapping("/getMarketStationList")
    public ApiResult<ServiceStationVo> getMarketStationList(@RequestBody ServiceStationParam marketStationParam) {
        startPage();
        return getDataTable(marketStationService.getMarketStationList(marketStationParam));
    }

    /**
     * 查询销售服务商询岗位管理下拉框
     *
     * @return 岗位管理下拉框
     */
    @GetMapping("/getMarketStationDropDown")
    public ApiResult getMarketStationDropDown() {
        try {
            List<ServiceStationVo> result = marketStationService.getMarketStationList(new ServiceStationParam());
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取销售服务商询岗位管理下拉框失败", e);
            return ApiResult.error("获取销售服务商询岗位管理下拉框失败");
        }
    }

    /**
     * @param marketStationDto 新增岗位管理信息
     * @return 插入结果
     * 新增岗位管理信息
     */
    @MethodDesc("新增岗位管理信息")
    @PreAuthorize("@ss.hasPermission('marketStation:marketStation:add')")
    @Submit(fileds = "userId")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveMarketStation")
    public ApiResult saveMarketStation(@RequestBody ServiceStationDto marketStationDto) {
        return marketStationService.saveMarketStation(marketStationDto);
    }

    /**
     * @param stationId 岗位管理id
     * @return 岗位管理信息
     * 根据id查询岗位管理信息
     */
    @MethodDesc("根据id查询岗位管理信息")
    @PreAuthorize("@ss.hasPermission('marketStation:marketStation:query')")
    @GetMapping("/getMarketStationById/{stationId}")
    public ApiResult getMarketStationById(@PathVariable Long stationId) {
        return marketStationService.getMarketStationById(stationId);
    }

    /**
     * @param marketStationDto 修改信息
     * @return 修改结果
     * 修改岗位管理信息
     */
    @MethodDesc("修改岗位管理信息")
    @PreAuthorize("@ss.hasPermission('marketStation:marketStation:edit')")
    @Submit(fileds = "userId")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMarketStation")
    public ApiResult updateMarketStation(@RequestBody ServiceStationDto marketStationDto) {
        return marketStationService.updateMarketStation(marketStationDto);
    }

    /**
     * @param stationId 岗位id
     * @return 岗位信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('marketStation:marketStation:editStatusById')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "stationId") Integer stationId,
                                 @RequestParam(value = "status") Integer status) {
        return marketStationService.updateStatusById(stationId,status);
    }

}
