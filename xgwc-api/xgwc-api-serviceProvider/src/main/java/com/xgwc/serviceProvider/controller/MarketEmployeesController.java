package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.MarketEmployeesDto;
import com.xgwc.serviceProvider.entity.vo.MarketEmployeesQueryVo;
import com.xgwc.serviceProvider.entity.vo.MarketEmployeesVo;
import com.xgwc.serviceProvider.service.MarketEmployeesService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * 财务服务商员工档案管理
 */
@RestController
@RequestMapping("/market/employees")
public class MarketEmployeesController extends BaseController {
    @Resource
    private MarketEmployeesService marketEmployeesService;

    /**
     * 查询员工档案列表
     */
    @MethodDesc("查询员工档案列表")
    @PreAuthorize("@ss.hasPermission('employees:employees:list')")
    @PostMapping("/list")
    public ApiResult list(@RequestBody MarketEmployeesQueryVo employees) {
        return ApiResult.ok(marketEmployeesService.selectEmployeesList(employees));
    }

    /**
     * 获取员工档案详细信息
     */
    @MethodDesc("获取员工档案详细信息")
    //@PreAuthorize("@ss.hasPermission('employees:employees:query')")
    @GetMapping(value = "/{employeeId}")
    public ApiResult<MarketEmployeesDto> getInfo(@PathVariable("employeeId") Long employeeId) {
        return success(marketEmployeesService.selectEmployeesByEmployeeId(employeeId));
    }

    /**
     * 新增员工档案
     */
    @MethodDesc("新增员工档案")
    @PreAuthorize("@ss.hasPermission('employees:employees:add')")
    @Log(title = "员工档案", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody MarketEmployeesVo employees) {
        return marketEmployeesService.insertEmployees(employees);
    }

    /**
     * 修改员工档案
     */
    @MethodDesc("修改员工档案")
    @PreAuthorize("@ss.hasPermission('employees:employees:edit')")
    @Log(title = "员工档案", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody MarketEmployeesVo employees) {
        return marketEmployeesService.updateEmployees(employees);
    }

    /**
     * 申请离职
     */
    @MethodDesc("申请离职")
    //@PreAuthorize("@ss.hasPermission('employees:employees:resignations')")
    @PostMapping("resignations")
    public ApiResult resignations(@RequestBody MarketEmployeesVo employees) {
        return toAjax(marketEmployeesService.resignationsEmployees(employees));
    }


}
