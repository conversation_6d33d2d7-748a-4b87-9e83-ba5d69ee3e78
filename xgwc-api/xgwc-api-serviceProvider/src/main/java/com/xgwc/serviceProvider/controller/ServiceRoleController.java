package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.ServiceRoleDto;
import com.xgwc.serviceProvider.entity.param.ServiceRoleParam;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleVo;
import com.xgwc.serviceProvider.service.ServiceRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: kouwenzhu<PERSON>
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 服务商角色管理
 */
@RestController
@RequestMapping("/serviceRole")
@Slf4j
public class ServiceRoleController extends BaseController {

    @Resource
    private ServiceRoleService serviceRoleService;

    /**
     * @param serviceRoleParam 查询条件
     * @return 角色管理列表
     * 查询角色管理列表
     */
    @MethodDesc("查询角色管理列表")
    @PreAuthorize("@ss.hasPermission('serviceRole:serviceRole:list')")
    @PostMapping("/getServiceRoleList")
    public ApiResult<ServiceRoleVo> getServiceRoleList(@RequestBody ServiceRoleParam serviceRoleParam) {
        startPage();
        return getDataTable(serviceRoleService.getServiceRoleList(serviceRoleParam));
    }

    /**
     * 获取服务商角色管理下拉框
     *
     * @return 角色管理下拉框
     */
    @MethodDesc("获取服务商角色管理下拉框")
    @GetMapping("/getServiceRoleDropDown")
    public ApiResult getServiceRoleDropDown() {
        try {
            List<ServiceRoleVo> result = serviceRoleService.getServiceRoleList(new ServiceRoleParam());
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取服务商角色管理下拉框失败", e);
            return ApiResult.error("获取服务商角色管理下拉框失败");
        }
    }

    /**
     * @param serviceRoleDto 新增角色管理信息
     * @return 插入结果
     * 新增角色管理信息
     */
    @MethodDesc("新增角色管理信息")
    @PreAuthorize("@ss.hasPermission('serviceRole:serviceRole:add')")
    @Submit(fileds = "userId")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveServiceRole")
    public ApiResult saveServiceRole(@RequestBody ServiceRoleDto serviceRoleDto) {
        return serviceRoleService.saveServiceRole(serviceRoleDto);
    }

    /**
     * @param roleId 角色管理id
     * @return 角色管理信息
     * 根据id查询角色管理信息
     */
    @MethodDesc("根据id查询角色管理信息")
    @PreAuthorize("@ss.hasPermission('serviceRole:serviceRole:query')")
    @GetMapping("/getServiceRoleById/{roleId}")
    public ApiResult getServiceRoleById(@PathVariable Long roleId) {
        return serviceRoleService.getServiceRoleById(roleId);
    }

    /**
     * @param serviceRoleDto 修改信息
     * @return 修改结果
     * 修改角色管理信息
     */
    @MethodDesc("修改角色管理信息")
    @PreAuthorize("@ss.hasPermission('serviceRole:serviceRole:edit')")
    @Submit(fileds = "userId")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateServiceRole")
    public ApiResult updateServiceRole(@RequestBody ServiceRoleDto serviceRoleDto) {
        return serviceRoleService.updateServiceRole(serviceRoleDto);
    }

    /**
     * @param roleId 角色id
     * @return 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('serviceRole:serviceRole:editStatusById')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "roleId") Integer roleId,
                                      @RequestParam(value = "status") Integer status) {
        return serviceRoleService.updateStatusById(roleId, status);
    }

}
