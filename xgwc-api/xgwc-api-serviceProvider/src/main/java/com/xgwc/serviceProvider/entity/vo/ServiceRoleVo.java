package com.xgwc.serviceProvider.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 服务商角色管理
 */
@Data
public class ServiceRoleVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("数据范围（1：全部数据 2：本部门数据 3：本人数据）")
    private Long dataScope;

    @FieldDesc(value = "服务商id")
    private Long serviceId;

    @FieldDesc(value = "销售服务商id")
    private Long marketId;

    @FieldDesc("角色id")
    private Long roleId;

    @FieldDesc("角色名称")
    private String roleName;

    @FieldDesc("标识字段")
    private String isFlag;

    @FieldDesc("排序：越小越前")
    private Integer sort;

    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;



}
