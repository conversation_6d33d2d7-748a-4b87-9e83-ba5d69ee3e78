package com.xgwc.serviceProvider.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.StaffLog;
import com.xgwc.common.entity.StaffLogDto;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.ChangeLogUtil;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.dao.ServiceDeptMapper;
import com.xgwc.serviceProvider.dao.ServiceRoleMapper;
import com.xgwc.serviceProvider.entity.dto.ServiceDeptDto;
import com.xgwc.serviceProvider.entity.dto.ServiceRoleDto;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleMenuVo;
import com.xgwc.serviceProvider.feign.entity.BindStaffDto;
import com.xgwc.serviceProvider.feign.entity.FeignServiceStaffDto;
import com.xgwc.serviceProvider.feign.entity.StaffDto;
import com.xgwc.serviceProvider.util.ServiceStaffLogParseUtil;
import com.xgwc.user.feign.api.StaffLogFeign;
import com.xgwc.user.feign.api.UserDetailFeign;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.xgwc.serviceProvider.dao.ServiceStaffMapper;
import com.xgwc.serviceProvider.entity.ServiceStaff;
import com.xgwc.serviceProvider.entity.dto.ServiceStaffDto;
import com.xgwc.serviceProvider.entity.vo.ServiceStaffQueryVo;
import com.xgwc.serviceProvider.entity.vo.ServiceStaffVo;
import com.xgwc.serviceProvider.service.IServiceStaffService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.xgwc.common.util.ParamDecryptUtil.PHONE_KEY;

@Slf4j
@Service
public class ServiceStaffServiceImpl implements IServiceStaffService  {
    @Resource
    private ServiceStaffMapper serviceStaffMapper;
    @Resource
    private StaffLogFeign staffLogFeign;
    @Resource
    private UserDetailFeign userDetailFeign;
    @Resource
    private ServiceRoleMapper serviceRoleMapper;
    @Resource
    private ServiceDeptMapper serviceDeptMapper;

    /**
     * 查询服务商员工
     * 
     * @param id 服务商员工主键
     * @return 服务商员工
     */
    @Override
    public ServiceStaffDto selectServiceStaffById(Long id) {
        return serviceStaffMapper.selectServiceStaffById(id);
    }

    /**
     * 查询服务商员工列表
     *
     * @param serviceStaff 服务商员工
     * @return 服务商员工
     */
    @Override
    public List<ServiceStaffDto> selectServiceStaffList(ServiceStaffQueryVo serviceStaff) {
        Long serviceId = SecurityUtils.getSysUser().getServiceId();
        if(serviceId == null)return List.of();
        if(StringUtils.isNotEmpty(serviceStaff.getLoginPhone())){
            serviceStaff.setLoginPhone(ParamDecryptUtil.encrypt(serviceStaff.getLoginPhone(), PHONE_KEY));
        }
        if(serviceStaff.getDeptId() != null){
            List<Long> allDeptIds = new ArrayList<>();
            collectChildDeptIds(serviceStaff.getDeptId(), allDeptIds,serviceId);
            serviceStaff.setDeptIds(allDeptIds);
        }
        serviceStaff.setServiceOwnerId(serviceId);
        List<ServiceStaffDto> list = serviceStaffMapper.selectServiceStaffList(serviceStaff);
        if(!list.isEmpty()){
            for (ServiceStaffDto item : list) {
                if(StringUtils.isNotEmpty(item.getLoginPhone())){
                    item.setLoginPhone(ParamDecryptUtil.decryptParam(item.getLoginPhone(), PHONE_KEY));
                }
            }
        }
        return list;
    }

    private void collectChildDeptIds(Long parentId, List<Long> allIds, Long serviceId) {
        allIds.add(parentId);
        List<ServiceDeptDto> children = serviceDeptMapper.getDeptByPid(parentId,serviceId);
        for(ServiceDeptDto child : children){
            collectChildDeptIds(child.getDeptId(), allIds,serviceId);
        }
    }

    /**
     * 新增服务商员工
     * 
     * @param dto 服务商员工
     * @return 结果
     */
    @Override
    public int insertServiceStaff(ServiceStaffVo dto) {
        ServiceStaff serviceStaff = BeanUtil.copyProperties(dto, ServiceStaff.class);
        serviceStaff.setCreateTime(DateUtils.getNowDate());
        serviceStaff.setCreateBy(SecurityUtils.getNickName());
        return serviceStaffMapper.insertServiceStaff(serviceStaff);
    }

    /**
     * 修改服务商员工
     * 
     * @param dto 服务商员工
     * @return 结果
     */
    @Override
    public int updateServiceStaff(ServiceStaffVo dto) {
        ServiceStaffDto oldStaff = serviceStaffMapper.selectServiceStaffById(dto.getId());
        if(oldStaff == null){
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        ServiceStaff newStaff = BeanUtil.copyProperties(dto, ServiceStaff.class);
        newStaff.setUpdateTime(DateUtils.getNowDate());
        newStaff.setUpdateBy(SecurityUtils.getNickName());
        int result = serviceStaffMapper.updateServiceStaff(newStaff);
        try {
            if (result > 0) {
                StaffLog log = ChangeLogUtil.buildAggregatedLog(
                        oldStaff,
                        newStaff,
                        dto.getId(),
                        newStaff.getUpdateBy(),
                        newStaff.getUpdateTime(),
                        "修改员工"
                );
                if (log != null) {
                    log.setBusinessType(3);
                    log.setBusinessId(oldStaff.getServiceOwnerId());
                    staffLogFeign.addStaffLog(log);
                }
            }
            if(StringUtils.isNotEmpty(newStaff.getRoleIds()) && oldStaff.getBindUserId() != null){
                //删除旧的角色
                userDetailFeign.deleteBrandRoleUserByUserId(oldStaff.getBindUserId());
                String[] roleIds = newStaff.getRoleIds().split(",");
                for(String roleId : roleIds){
                    userDetailFeign.addBrandRoleUser(Long.valueOf(roleId), oldStaff.getBindUserId(),2);
                }
            }else if(oldStaff.getBindUserId() != null){
                //删除旧的角色
                userDetailFeign.deleteBrandRoleUserByUserId(oldStaff.getBindUserId());
            }
        } catch (Exception e) {
            log.error("feign调用失败，添加员工角色失败", e);
        }
        return result;
    }

    /**
     * 批量删除服务商员工
     * 
     * @param ids 需要删除的服务商员工主键
     * @return 结果
     */
    @Override
    public int deleteServiceStaffByIds(Long[] ids) {
        return serviceStaffMapper.deleteServiceStaffByIds(ids);
    }

    /**
     * 删除服务商员工信息
     * 
     * @param id 服务商员工主键
     * @return 结果
     */
    @Override
    public int deleteServiceStaffById(Long id) {
        return serviceStaffMapper.deleteServiceStaffById(id);
    }

    @Override
    public List<StaffLogDto> findStaffLogByStaffIdAndBusinessType(Long id, Integer businessType) {
        ApiResult<List<StaffLogDto>> result = staffLogFeign.findStaffLogByStaffIdAndBusinessType(id, businessType);
        if (!result.getData().isEmpty()) {
            List<StaffLogDto> list = result.getData();
            for (StaffLogDto dto : list) {
                String parsed = ServiceStaffLogParseUtil.parseLogContentToReadable(dto.getRemark(),businessType);
                dto.setParsedRemark(parsed);
            }
            return list;
        }
        return List.of();
    }

    @Override
    public FeignServiceStaffDto selectServiceStaffByStaffId(Long staffId) {
        ServiceStaffDto dto = serviceStaffMapper.selectServiceStaffById(staffId);
        return BeanUtil.copyProperties(dto, FeignServiceStaffDto.class);
    }

    @Override
    public boolean selectServiceStaffByNameAndServiceId(String staffName, Long serviceId) {
        if (serviceStaffMapper.findByStaffNameAndServiceId(staffName, serviceId) == null) {
            return true;
        }
        return false;
    }

    @Override
    public void updateServiceStaffBindStatus(BindStaffDto bindStaffDto) {
        ServiceStaffDto serviceStaffDto = serviceStaffMapper.selectServiceStaffById(bindStaffDto.getId());
        if(serviceStaffDto == null){
            log.error("服务商员工不存在");
        }
        serviceStaffMapper.updateServiceStaff(BeanUtil.copyProperties(bindStaffDto, ServiceStaff.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveServiceStaff(StaffDto staffDto) {
        String name = staffDto.getName();
        ServiceStaff newStaff = BeanUtil.copyProperties(staffDto, ServiceStaff.class);
        newStaff.setBindStatus(1);
        newStaff.setStageName(name);
        newStaff.setCreateTime(DateUtils.getNowDate());
        newStaff.setCreateBy(name);
        // 添加角色
        ServiceRoleDto serviceRoleDto = new ServiceRoleDto();
        serviceRoleDto.setRoleName(name + "管理员");
        serviceRoleDto.setServiceId(staffDto.getServiceOwnerId());
        serviceRoleDto.setSort(0);
        serviceRoleDto.setIsFlag("0");
        serviceRoleDto.setCreateBy(name);
        serviceRoleMapper.saveServiceRole(serviceRoleDto);
        List<ServiceRoleMenuVo>  menuList = new ArrayList<>();
        if(!staffDto.getMenuIds().isEmpty()){
            for (Long menuId : staffDto.getMenuIds()) {
                ServiceRoleMenuVo menuVo = new ServiceRoleMenuVo();
                menuVo.setRoleId(serviceRoleDto.getRoleId());
                menuVo.setMenuId(menuId.intValue());
                menuVo.setCreateBy(name);
                menuList.add(menuVo);
            }
            serviceRoleMapper.saveRoleMenu(menuList);
        }
        newStaff.setRoleIds(String.valueOf(serviceRoleDto.getRoleId()));
        serviceStaffMapper.insertServiceStaff(newStaff);
    }

    @Override
    public List<ServiceStaffDto> selectStaffListDropDown(Long deptId) {
        Long serviceId = SecurityUtils.getSysUser().getServiceId();
        return serviceStaffMapper.selectStaffListDropDown(serviceId, deptId);
    }
}
