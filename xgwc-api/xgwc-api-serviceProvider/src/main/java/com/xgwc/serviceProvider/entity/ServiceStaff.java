package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.util.Date;


@Data
public class ServiceStaff {

private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;

    /** 姓名 */
    private String name;

    /** 花名 */
    private String stageName;

    /** 部门id */
    private Long deptId;

    /** 岗位id */
    private Long postId;

    /** 角色权限 */
    private String roleIds;

    /** 工作性质：0全职，1外包，2兼职 */
    private Integer jobNature;

    /** 状态：0在职，1离职 */
    private Integer status;

    /** 部门负责人：0是，1否 */
    private Integer isPrincipal;

    /** 助理：0是，1否 */
    private Integer isAssistant;

    /** 排班：0是，1否 */
    private Integer isSchedule;

    /** 直属上级 */
    private Long superior;

    /** 绑定状态：0未绑，1已绑 */
    private Integer bindStatus;

    /** 绑定的用户id */
    private Long bindUserId;

    /** 登录手机号 */
    private String loginPhone;

    /** 服务商id */
    private Long serviceOwnerId;

    /** 是否删除：0正常，1删除 */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;

    /** 离职时间 */
    private Date resignationTime;

}