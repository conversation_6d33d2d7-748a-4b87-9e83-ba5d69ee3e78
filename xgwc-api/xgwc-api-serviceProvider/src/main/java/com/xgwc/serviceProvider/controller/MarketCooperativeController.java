package com.xgwc.serviceProvider.controller;

import java.util.List;

import com.xgwc.serviceProvider.entity.dto.MarketCooperativeDto;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeQueryVo;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeStatusVo;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeVo;
import com.xgwc.serviceProvider.service.IMarketCooperativeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

@RestController
@RequestMapping("/marketCooperative")
public class MarketCooperativeController extends BaseController {
    @Autowired
    private IMarketCooperativeService marketCooperativeService;

    /**
     * 查询销售合作列表
     */
    @MethodDesc("查询销售合作列表")
    @PreAuthorize("@ss.hasPermission('marketCooperative:marketCooperative:list')")
    @GetMapping("/list")
    public ApiResult<MarketCooperativeDto> list(MarketCooperativeQueryVo marketCooperative) {
        startPage();
        List<MarketCooperativeDto> list = marketCooperativeService.selectMarketCooperativeList(marketCooperative);
        return getDataTable(list);
    }

    /**
     * 新增销售合作
     */
    @MethodDesc("新增销售合作")
    @PreAuthorize("@ss.hasPermission('marketCooperative:marketCooperative:add')")
    @Log(title = "销售合作", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody MarketCooperativeVo marketCooperative) {
        return toAjax(marketCooperativeService.insertMarketCooperative(marketCooperative));
    }

    /**
     * 更新状态
     */
    @MethodDesc("更新状态")
    @PreAuthorize("@ss.hasPermission('marketCooperative:marketCooperative:edit')")
    @Log(title = "销售合作", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public ApiResult updateStatus(@RequestBody MarketCooperativeStatusVo dto) {
        return toAjax(marketCooperativeService.updateStatus(dto));
    }
}
