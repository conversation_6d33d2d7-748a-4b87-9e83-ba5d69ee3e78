package com.xgwc.serviceProvider.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.FileUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.serviceProvider.dao.BillKeFuUploadMapper;
import com.xgwc.serviceProvider.dao.BillKefuMapper;
import com.xgwc.serviceProvider.dao.XgwcServicesMapper;
import com.xgwc.serviceProvider.entity.BillKefuUpload;
import com.xgwc.serviceProvider.entity.dto.BillKefuDto;
import com.xgwc.serviceProvider.entity.dto.BillKfUploadContent;
import com.xgwc.serviceProvider.entity.vo.BillKefuVo;
import com.xgwc.serviceProvider.service.BillKefuUploadService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class BillKefuUploadServiceImpl implements BillKefuUploadService {

    @Resource
    private BillKeFuUploadMapper billKeFuUploadMapper;

    @Resource
    private BillKefuMapper billKefuMapper;

    @Resource
    private XgwcServicesMapper xgwcServicesMapper;


    @Override
    public int insertBillKeFuUpload(BillKefuUpload billKefuUpload) {
        if(StringUtils.isEmpty(billKefuUpload.getFileName()) || StringUtils.isEmpty(billKefuUpload.getAttachment())){
            log.error("文件名或者附件为空:{}", JSONObject.toJSONString(billKefuUpload));
            return 0;
        }
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if(serviceId == null){
            log.error("非服务商员工不能导入工资:{}", JSONObject.toJSONString(sysUser));
            return 0;
        }
        billKefuUpload.setServiceId(serviceId);
        billKefuUpload.setUserId(sysUser.getUserId());
        billKefuUpload.setCreateBy(sysUser.getUserName());
        //将记录插入数据库
        int result = billKeFuUploadMapper.insertBillKeFuUpload(billKefuUpload);
        asyncHandleKefuBaseAmount(billKefuUpload.getAttachment(), serviceId);
        return result;
    }

    @Override
    public int updateBillKeFuUpload(BillKefuUpload billKefuUpload) {
        if(billKefuUpload.getId() == null){
            log.error("上传记录ID为空:{}", JSONObject.toJSONString(billKefuUpload));
            return 0;
        }
        //主要用于删除上传记录
        return billKeFuUploadMapper.updateBillKeFuUpload(billKefuUpload);
    }

    @Override
    public List<BillKefuUpload> getBillKeFuUploadListByServiceId() {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if(serviceId == null){
            log.error("非服务商员工不能获取导入列表:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return billKeFuUploadMapper.getBillKeFuUploadListByServiceId(serviceId);
    }

    /**
     * 从excel url上读取数据, 并且转换对象
     */
    private List<BillKfUploadContent> readByExcel(String excelUrl){
        String destFile = "C:\\Users\\<USER>\\Desktop\\" + UUID.randomUUID() + "." +FileUtil.getSuffix(excelUrl);
        HttpUtil.downloadFile(excelUrl, destFile);
        File excelFile = new File(destFile);
        if(excelFile.exists()){
            ExcelReader reader = ExcelUtil.getReader(excelFile);
            List<BillKfUploadContent> billKfUploadContents = reader.read(0, 1, BillKfUploadContent.class);
            reader.close();
            if(billKfUploadContents != null && !billKfUploadContents.isEmpty()){
                billKfUploadContents.forEach(x->{
                    x.setBillStart(handleDate(x.getBillStart()));
                    x.setBillEnd(handleDate(x.getBillEnd()));
                });
                return billKfUploadContents;
            }
        }

        return new ArrayList<>();
    }

    private String handleDate(String date){
        if(!StringUtils.isEmpty(date)){
            if(date.length() > 10){
                date = date.substring(0, 10);
            }
        }
        return date;
    }

    /**
     * 异步处理客服基础工资
     */
    @Async("scheduledExecutorService")
    protected void asyncHandleKefuBaseAmount(String excelUrl, Long serviceId){
        List<BillKfUploadContent> billKfUploadContents = readByExcel(excelUrl);
        //step1.获取给服务商授权品牌商 以及品牌商下的所有加盟商员工列表
        List<Long> userIds = xgwcServicesMapper.getStaffUserIdByServiceId(serviceId);
        //step2.校验billKfUploadContents是否在授权数据中，有则查看该用户是否已经有在改账期内的账单，有则更新，没有则舍弃
        billKfUploadContents.forEach(billKfUploadContent -> {
            //如果是在授权员工里
            if(userIds.contains(billKfUploadContent.getUserId())) {
                BillKefuDto billKefuDto = getBillKefuByBillTime(billKfUploadContent);
                if (billKefuDto == null) {
                    log.warn("该数据在数据库里未匹配到对应账单:{}", JSONObject.toJSONString(billKfUploadContent));
                } else {
                    BillKefuDto newBillKefuDto = new BillKefuDto();
                    newBillKefuDto.setBaseAmount(new BigDecimal(billKfUploadContent.getBaseAmount()));
                    newBillKefuDto.setId(billKefuDto.getId());
                    //修改基本工资
                    int result = billKefuMapper.updateBillKefuById(newBillKefuDto);
                    log.info("修改客服基本工资:{}, 结果:{}", JSONObject.toJSONString(billKfUploadContent), result);
                }
            }else{
                log.warn("员工ID不在授权列表里:{}, 服务商ID:{}", JSONObject.toJSONString(billKfUploadContent), serviceId);
            }
        });

    }

    private BillKefuDto getBillKefuByBillTime(BillKfUploadContent billKfUploadContent){
        //TODO 应该再加上品牌ID条件查询
        BillKefuVo billKefuVo = new BillKefuVo();
        billKefuVo.setBillStart(billKfUploadContent.getBillStart());
        billKefuVo.setBillEnd(billKfUploadContent.getBillEnd());
        billKefuVo.setUserId(billKfUploadContent.getUserId());
        return billKefuMapper.getBillKefuByBillTimeAndUserId(billKefuVo);
    }
}
