package com.xgwc.serviceProvider.util;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.xgwc.common.util.SpringUtils;
import com.xgwc.serviceProvider.dao.*;
import com.xgwc.serviceProvider.entity.dto.MarketStaffDto;
import com.xgwc.serviceProvider.entity.dto.ServiceDeptDto;
import com.xgwc.serviceProvider.entity.dto.ServiceStaffDto;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleVo;
import com.xgwc.serviceProvider.entity.vo.ServiceStationVo;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MarketFieldValueResolver {

    private static final Map<String, Function<String, String>> fieldResolvers = new HashMap<>();

    static {
        fieldResolvers.put("部门", id -> {
            if (StringUtils.isBlank(id)) return "无";
            ServiceDeptDto serviceDeptDto = SpringUtils.getBean(MarketDeptMapper.class).getMarketDeptById(Long.valueOf(id));
            return serviceDeptDto != null ? serviceDeptDto.getDeptName() : id;
        });

        fieldResolvers.put("岗位", id -> {
            if (StringUtils.isBlank(id)) return "无";
            ServiceStationVo serviceStationVo = SpringUtils.getBean(MarketStationMapper.class).getMarketStationById(Integer.parseInt(id));
            return serviceStationVo != null ? serviceStationVo.getStationName() : id;
        });

        fieldResolvers.put("角色权限", ids -> {
            if (StringUtils.isBlank(ids)) return "无";
            MarketRoleMapper roleMapper = SpringUtils.getBean(MarketRoleMapper.class);
            return Arrays.stream(ids.split(","))
                    .map(id -> roleMapper.getMarketRoleById(Long.parseLong(id)))
                    .filter(Objects::nonNull)
                    .map(ServiceRoleVo::getRoleName)
                    .collect(Collectors.joining(", "));
        });

        fieldResolvers.put("工作性质", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "全职" : id.equals("1") ? "外包" : id.equals("2") ? "兼职" : id;
        });

        fieldResolvers.put("状态", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "在职" : id.equals("1") ? "离职" : id;
        });

        fieldResolvers.put("部门负责人", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "是" : id.equals("1") ? "否" : id;
        });

        fieldResolvers.put("直属上级", id -> {
            if (StringUtils.isBlank(id)) return "无";
            MarketStaffDto marketStaffDto = SpringUtils.getBean(MarketStaffMapper.class).selectMarketStaffById(Long.parseLong(id));
            return marketStaffDto != null ? marketStaffDto.getStageName() : id;
        });
    }

    public static String resolve(String fieldName, String value) {
        Function<String, String> resolver = fieldResolvers.get(fieldName);
        return resolver != null ? resolver.apply(value) : value;
    }
}
