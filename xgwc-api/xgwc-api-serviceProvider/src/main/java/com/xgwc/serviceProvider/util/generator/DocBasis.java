package com.xgwc.serviceProvider.util.generator;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DocBasis implements Doc {


    private String title;

    private int row = 0;
    private int col = 2;

    private List<BasisData> datas;

    public void setDatas(BasisData data) {
        if(this.datas == null) {
            this.datas = new ArrayList<>();
        }
        this.datas.add(data);
        this.row++;
    }
    @Override
    public String getTitle() {
        return title;
    }

    @Override
    public int getRow() {
        return row;
    }

    @Override
    public int getCol() {
        return col;
    }

    @Override
    public List<BasisData> getDatas() {
        return datas;
    }
}

@Data
class BasisData implements DocData {

    private String key;
    private String value;

    public BasisData(String key, String value) {
        this.key = key;
        this.value = value;
    }


    @Override
    public List<String> getData() {
        List<String> list = new ArrayList<>();
        list.add(key);
        list.add(value);
        return list;
    }
}