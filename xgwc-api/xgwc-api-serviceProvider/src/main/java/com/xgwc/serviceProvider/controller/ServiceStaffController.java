package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.StaffLogDto;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.ServiceStaffDto;
import com.xgwc.serviceProvider.entity.vo.ServiceStaffQueryVo;
import com.xgwc.serviceProvider.entity.vo.ServiceStaffVo;
import com.xgwc.serviceProvider.feign.entity.BindStaffDto;
import com.xgwc.serviceProvider.feign.entity.StaffDto;
import com.xgwc.serviceProvider.service.IServiceStaffService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 服务商员工Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("/serviceStaff")
@Slf4j
public class ServiceStaffController extends BaseController {
    @Autowired
    private IServiceStaffService serviceStaffService;

    /**
     * 查询服务商员工列表
     */
    @MethodDesc("查询服务商员工列表")
    @PreAuthorize("@ss.hasPermission('serviceStaff:serviceStaff:list')")
    @GetMapping("/list")
    public ApiResult<ServiceStaffDto> list(ServiceStaffQueryVo serviceStaff) {
        startPage();
        List<ServiceStaffDto> list = serviceStaffService.selectServiceStaffList(serviceStaff);
        return getDataTable(list);
    }

    /**
     * 获取服务商员工详细信息
     */
    @MethodDesc("获取服务商员工详细信息")
    @PreAuthorize("@ss.hasPermission('serviceStaff:serviceStaff:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<ServiceStaffDto> getInfo(@PathVariable("id") Long id) {
        return success(serviceStaffService.selectServiceStaffById(id));
    }

    /**
     * 新增服务商员工
     */
    @MethodDesc("新增服务商员工")
    @PreAuthorize("@ss.hasPermission('serviceStaff:serviceStaff:add')")
    @Log(title = "服务商员工", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody ServiceStaffVo serviceStaff) {
        return toAjax(serviceStaffService.insertServiceStaff(serviceStaff));
    }

    /**
     * 修改服务商员工
     */
    @MethodDesc("修改服务商员工")
    @PreAuthorize("@ss.hasPermission('serviceStaff:serviceStaff:edit')")
    @Log(title = "服务商员工", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody ServiceStaffVo serviceStaff) {
        return toAjax(serviceStaffService.updateServiceStaff(serviceStaff));
    }

    /**
     * 删除服务商员工
     */
    @MethodDesc("删除服务商员工")
    @PreAuthorize("@ss.hasPermission('serviceStaff:serviceStaff:remove')")
    @Log(title = "服务商员工", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(serviceStaffService.deleteServiceStaffByIds(ids));
    }

    @MethodDesc("根据员工id查询痕迹")
    @PreAuthorize("@ss.hasPermission('serviceStaff:serviceStaff:trail')")
    @GetMapping(value = "/getStaffLog/{id}")
    public ApiResult getStaffLog(@PathVariable("id") Long id){
        startPage();
        List<StaffLogDto> list = serviceStaffService.findStaffLogByStaffIdAndBusinessType(id, 3);
        return getDataTable(list);
    }
    /**
     * 获取员工管理下拉框
     *
     * @return 员工管理下拉框 name + "/" + stageName
     */
    @MethodDesc("获取员工管理下拉框")
    @GetMapping("/getStaffDropDown")
    public ApiResult getStaffDropDown(@RequestParam(value = "deptId", required = false) Long deptId) {
        try {
            List<ServiceStaffDto> result = serviceStaffService.selectStaffListDropDown(deptId);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取员工管理下拉框失败", e);
            return ApiResult.error("获取员工管理下拉框失败");
        }
    }
    /**
     * feign调用获取员工信息
     * @param staffId 员工ID
     * @return ApiResult<FeignServiceStaffDto>
     */
    @GetMapping("/getStaffInfo")
    public ApiResult<ServiceStaffDto> getStaffInfo(@RequestParam("staffId") Long staffId) {
        ServiceStaffDto serviceStaffDto = serviceStaffService.selectServiceStaffById(staffId);
        return ApiResult.ok(serviceStaffDto);
    }

    /**
     * feign调用根据员工名称和服务ID查询员工信息
     * @param staffName 员工名称
     * @param serviceId 服务ID
     * @return boolean 是否存在
     */
    @GetMapping("/selectServiceStaffByNameAndServiceId")
    public boolean selectServiceStaffByNameAndServiceId(@RequestParam("staffName") String staffName, @RequestParam("serviceId") Long serviceId){
        return serviceStaffService.selectServiceStaffByNameAndServiceId(staffName, serviceId);
    }

    /**
     * feign调用绑定员工
     * @param bindStaffDto 绑定员工DTO
     */
    @PostMapping("/bindStaff")
    public void updateServiceStaffBindStatus(@RequestBody BindStaffDto bindStaffDto){
        serviceStaffService.updateServiceStaffBindStatus(bindStaffDto);
    }

    /**
     * feign调用保存员工信息
     * @param staffDto 员工信息DTO
     */
    @PostMapping("/saveServiceStaff")
    public void saveServiceStaff(@RequestBody StaffDto staffDto){
        serviceStaffService.saveServiceStaff(staffDto);
    }
}
