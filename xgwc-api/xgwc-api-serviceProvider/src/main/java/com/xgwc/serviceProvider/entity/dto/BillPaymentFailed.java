package com.xgwc.serviceProvider.entity.dto;

import com.xgwc.common.annotation.Excel;
import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.serviceProvider.entity.dto
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-18  18:04
 */

/**
 * 导出打款失败的子账单
 */
@Data
public class BillPaymentFailed {

    @Excel(name = "子账单ID", sort = 1)
    private Long subBillId;

    @Excel(name = "账单周期", sort = 2)
    private String billCycle;

    @Excel(name = "所属品牌商", sort = 3)
    private String brandName;

    @Excel(name = "结算主体", sort = 4)
    private String companyInfoName;

    @Excel(name = "应发金额", sort = 5)
    private String amount;

    @Excel(name = "支付宝姓名", sort = 6)
    private String alipayName;

    @Excel(name = "支付宝账号", sort = 7)
    private String alipayAccount;

    @Excel(name = "结算设计师", sort = 8)
    private String designerName;

    @Excel(name = "手机号", sort = 9)
    private String phone;

    @Excel(name = "结算状态", sort = 10, readConverterExp = "7=结算失败")
    private Integer settlementStatus;

    @Excel(name = "失败原因", sort = 11)
    private String failReason;
}
