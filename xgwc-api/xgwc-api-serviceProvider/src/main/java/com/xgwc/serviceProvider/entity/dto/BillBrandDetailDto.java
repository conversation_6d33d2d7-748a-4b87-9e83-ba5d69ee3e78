package com.xgwc.serviceProvider.entity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BillBrandDetailDto {

    /**
     * 账单id
     */
    private Long billId;

    /**
     * 账期开始
     */
    private String billStart;

    /**
     * 账期结束
     */
    private String billEnd;

    /**
     * 设计师id
     */
    private Long designerId;

    /**
     * 设计师名称
     */
    private String designerName;

    /**
     * 订单列表
     */
    List<BillBrandOrderDetail> orderList;

    /**
     * 总佣金
     */
    private BigDecimal totalCommission;

    /**
     * 品牌商名称
     */
    private String brandName;

    /**
     * 无票扣款
     */
    private BigDecimal noInvoice;

    /**
     * 佣金追回
     */
    private BigDecimal commissionBack;

    /**
     * 罚款
     */
    private BigDecimal fineAmount;

    /**
     * 应发金额
     */
    private BigDecimal payableAmount;

    /**
     * 打款失败原因
     */
    private String failReason;
}
