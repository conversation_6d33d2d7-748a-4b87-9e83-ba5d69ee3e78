package com.xgwc.serviceProvider.controller;

import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.BillBrandDetailDto;
import com.xgwc.serviceProvider.entity.dto.BillBrandDto;
import com.xgwc.serviceProvider.entity.vo.BillVo;
import com.xgwc.serviceProvider.service.impl.BillServiceImpl;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("bill")
@RestController
public class BillController extends BaseController {

    @Resource
    private BillServiceImpl billService;

    /**
     * 获取品牌账单列表
     */
    @RequestMapping("brand/getBrandBillList")
    public ApiResult getBrandBillList(BillVo billVo) {
        startPage();
        List<BillBrandDto> billBrandDtos = billService.getBillBrandList(billVo);
        return getDataTable(billBrandDtos);
    }

    /**
     * 获取服务商账单列表
     */
    @RequestMapping("service/getBillList")
    public ApiResult getServiceBillList(BillVo billVo) {
        startPage();
        List<BillBrandDto> billBrandDtos = billService.getBillServiceList(billVo);
        return getDataTable(billBrandDtos);
    }

    /**
     * 获取品牌商的账单详情
     */
    @RequestMapping("brand/billDetail")
    public ApiResult getBillDetail(BillVo billVo) {
        if(billVo.getBillId() == null){
            logger.error("账单id不能为空");
            return ApiResult.error("账单id不能为空");
        }
        BillBrandDetailDto billBrandDetailDto = billService.getBillDetail(billVo);
        return ApiResult.ok(billBrandDetailDto);
    }

    /**
     * 获取品牌商的账单详情
     */
    @RequestMapping("service/billDetail")
    public ApiResult getServiceBillDetail(BillVo billVo) {
        if(billVo.getBillId() == null){
            logger.error("账单id不能为空");
            return ApiResult.error("账单id不能为空");
        }
        BillBrandDetailDto billBrandDetailDto = billService.getServiceBillDetail(billVo);
        return ApiResult.ok(billBrandDetailDto);
    }

    /**
     * 锁定
     */
    @RequestMapping("lockbill")
    public ApiResult lockBill(Long brandId){
        if(brandId == null){
            logger.error("品牌商ID不能为空");
            return ApiResult.error("参数错误");
        }
        billService.lockBill(brandId);
        return ApiResult.ok();
    }

    /**
     * 下载对账（母账单）
     * @param billId 账单id
     * @param isFlag 标识 0-品牌 1-服务
     */
    @GetMapping("downloadBill")
    public void downloadBrandBill(HttpServletResponse response,
                                  @RequestParam(value = "billId") Long billId,
                                  @RequestParam(value = "isFlag") Integer isFlag) {
        billService.downloadBill(response, billId, isFlag);
    }
}
