package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillComfirmDetail {

    /**
     * 主键
     */
    private Long id;

    /**
     * 账单发票id
     */
    private Long billComfirmId;

    /**
     * 发票地址
     */
    private String invoiceUrl;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 抬头
     */
    private String purchaserName;

    /**
     * 税号
     */
    private String purchaserTaxNumber;

    /**
     * 公司主体ID
     */
    private Long companyInfoId;


    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
