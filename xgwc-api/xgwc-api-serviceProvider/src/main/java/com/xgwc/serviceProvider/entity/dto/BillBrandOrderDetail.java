package com.xgwc.serviceProvider.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillBrandOrderDetail {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 品牌名称
     */
    private String franchiseName;

    /**
     * 品牌id
     */
    private String franchiseId;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 交稿时间
     */
    private String archiveTime;

    /**
     * 成交时间
     */
    private String dealTime;

    /**
     * 佣金
     */
    private BigDecimal nowMoney;

    /**
     * 客户id
     */
    private String customerNo;


}
