package com.xgwc.serviceProvider.entity;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillOrderModify {

    /** 实收金额 */
    @FieldDesc("实收金额")
    private BigDecimal amount;

    /** 账单结束 */
    @FieldDesc("账单结束")
    private String billEnd;

    /** 账单开始 */
    @FieldDesc("账单开始")
    private String billStart;

    /** 品牌商id */
    @FieldDesc("品牌商id")
    private Long brandId;

    /** 公司主体ID */
    @FieldDesc("公司主体ID")
    private Long companyInfoId;

    /** 创建时间 */
    @FieldDesc("创建时间")
    private String createTime;

    /** 设计师业务类型 */
    @FieldDesc("设计师业务类型")
    private String designerBusiness;

    /** 设计师ID */
    @FieldDesc("设计师ID")
    private Long designerId;

    /** 设计师名称 */
    @FieldDesc("设计师名称")
    private String designerName;

    /** 加盟商id */
    @FieldDesc("加盟商id")
    private Long franchiseId;

    /** 主键 */
    @FieldDesc("主键")
    private Long id;

    /** 行更新时间 */
    @FieldDesc("行更新时间")
    private String modifyTime;

    /** 佣金金额 */
    @FieldDesc("佣金金额")
    private BigDecimal money;

    /** 当前金额：当有退款发生时有值 */
    @FieldDesc("当前金额：当有退款发生时有值")
    private BigDecimal nowAmount;

    /** 现佣金：当有退款发生时有值 */
    @FieldDesc("现佣金：当有退款发生时有值")
    private BigDecimal nowMoney;

    /** 订单金额 */
    @FieldDesc("订单金额")
    private BigDecimal orderAmount;

    /** 下单日期 */
    @FieldDesc("下单日期")
    private String orderDate;

    /** 订单id */
    @FieldDesc("订单id")
    private Long orderId;

    /** 订单编号 */
    @FieldDesc("订单编号")
    private String orderNo;

    /** 母订单id */
    @FieldDesc("母订单id")
    private Long pid;

    /** 谈单人员 */
    @FieldDesc("谈单人员")
    private Long saleManId;

    /** 谈单人员名称 */
    @FieldDesc("谈单人员名称")
    private String saleManName;

    /** 账单id */
    @FieldDesc("账单id")
    private Long subBillId;

    /** 客户ID */
    @FieldDesc("客户ID")
    private String taobaoId;

    /**
     * 追回状态：0待追回，1部分追回，2全部追回，3无需追回
     */
    @FieldDesc("追回状态：0待追回，1部分追回，2全部追回，3无需追回")
    private Integer backStatus;

    /**
     * 已追回佣金
     */
    @FieldDesc("已追回佣金")
    private BigDecimal commissionBacked;

    /**
     * 剩余佣金追回
     */
    @FieldDesc("剩余佣金追回")
    private BigDecimal commissionBackRemaining;

    /**
     * 追回流水id
     */
    @FieldDesc("追回流水id")
    private Long backId;

    /**
     * 佣金追回编号
     */
    @FieldDesc("佣金追回编号")
    private String recordId;
}
