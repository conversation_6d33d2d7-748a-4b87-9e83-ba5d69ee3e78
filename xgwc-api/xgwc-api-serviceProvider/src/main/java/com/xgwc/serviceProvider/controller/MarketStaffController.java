package com.xgwc.serviceProvider.controller;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.xgwc.common.entity.StaffLogDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperationVo;
import com.xgwc.serviceProvider.feign.entity.BindStaffDto;
import com.xgwc.serviceProvider.feign.entity.StaffDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.xgwc.serviceProvider.entity.vo.MarketStaffVo;
import com.xgwc.serviceProvider.entity.dto.MarketStaffDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffQueryVo;
import com.xgwc.serviceProvider.service.IMarketStaffService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

@Slf4j
@RestController
@RequestMapping("/marketStaff")
public class MarketStaffController extends BaseController {
    @Autowired
    private IMarketStaffService marketStaffService;

    /**
     * 查询销售服务商员工表列表
     */
    @MethodDesc("查询销售服务商员工表列表")
    @PreAuthorize("@ss.hasPermission('marketStaff:marketStaff:list')")
    @GetMapping("/list")
    public ApiResult<MarketStaffDto> list(MarketStaffQueryVo marketStaff) {
        startPage();
        List<MarketStaffDto> list = marketStaffService.selectMarketStaffList(marketStaff);
        return getDataTable(list);
    }

    /**
     * 获取销售服务商员工表详细信息
     */
    @MethodDesc("获取销售服务商员工表详细信息")
    @PreAuthorize("@ss.hasPermission('marketStaff:marketStaff:query')")
    @GetMapping(value = "/get/{id}")
    public ApiResult<MarketStaffDto> getInfo(@PathVariable("id") Long id) {
        return success(marketStaffService.selectMarketStaffById(id));
    }

    /**
     * 新增销售服务商员工表
     */
    @MethodDesc("新增销售服务商员工表")
    @PreAuthorize("@ss.hasPermission('marketStaff:marketStaff:add')")
    @Log(title = "销售服务商员工表", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody MarketStaffVo marketStaff) {
        return toAjax(marketStaffService.insertMarketStaff(marketStaff));
    }

    /**
     * 修改销售服务商员工表
     */
    @MethodDesc("修改销售服务商员工表")
    @PreAuthorize("@ss.hasPermission('marketStaff:marketStaff:edit')")
    @Log(title = "销售服务商员工表", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody MarketStaffVo marketStaff) {
        return toAjax(marketStaffService.updateMarketStaff(marketStaff));
    }

    /**
     * 删除销售服务商员工表
     */
    @MethodDesc("删除销售服务商员工表")
    @PreAuthorize("@ss.hasPermission('marketStaff:marketStaff:remove')")
    @Log(title = "销售服务商员工表", businessType = BusinessType.DELETE)
	@DeleteMapping("/remove")
    public ApiResult remove(@RequestBody Long id) {
        return toAjax(marketStaffService.deleteMarketStaffById(id));
    }

    @MethodDesc("根据员工id查询痕迹")
    @PreAuthorize("@ss.hasPermission('marketStaff:serviceStaff:trail')")
    @GetMapping(value = "/getStaffLog/{id}")
    public ApiResult getStaffLog(@PathVariable("id") Long id){
        startPage();
        List<StaffLogDto> list = marketStaffService.findStaffLogByStaffIdAndBusinessType(id, 4);
        return getDataTable(list);
    }

    /**
     * 获取员工管理下拉框
     * @return 员工管理下拉框 name + "/" + stageName
     */
    @MethodDesc("获取员工管理下拉框")
    @GetMapping("/getStaffDropDown")
    public ApiResult getStaffDropDown(@RequestParam(value = "deptId", required = false) Long deptId) {
        try {
            List<MarketStaffDto> result = marketStaffService.selectStaffListDropDown(deptId);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取员工管理下拉框失败", e);
            return ApiResult.error("获取员工管理下拉框失败");
        }
    }

    /**
     * feign调用获取员工信息
     * @param staffId 员工ID
     * @return ApiResult<FeignMarketStaffDto>
     */
    @GetMapping("/getMarketStaffInfo")
    public ApiResult<MarketStaffDto> getMarketStaffInfo(@RequestParam("staffId") Long staffId) {
        MarketStaffDto marketStaffDto = marketStaffService.selectMarketStaffById(staffId);
        return ApiResult.ok(marketStaffDto);
    }

    /**
     * feign调用根据员工名称和服务ID查询员工信息
     * @param staffName 员工名称
     * @param serviceId 服务ID
     * @return boolean 是否存在
     */
    @GetMapping("/selectMarketStaffByNameAndServiceId")
    public boolean selectMarketStaffByNameAndServiceId(@RequestParam("staffName") String staffName, @RequestParam("serviceId") Long serviceId){
        return marketStaffService.selectMarketStaffByNameAndServiceId(staffName, serviceId);
    }

    /**
     * feign调用绑定员工
     * @param bindStaffDto 绑定员工DTO
     */
    @PostMapping("/bindStaff")
    public void updateServiceStaffBindStatus(@RequestBody BindStaffDto bindStaffDto){
        marketStaffService.updateMarketStaffBindStatus(bindStaffDto);
    }

    /**
     * feign调用保存员工信息
     * @param staffDto 员工信息DTO
     */
    @PostMapping("/saveMarketStaff")
    public void saveServiceStaff(@RequestBody StaffDto staffDto){
        marketStaffService.saveMarketStaff(staffDto);
    }

    @MethodDesc("合作加盟商")
    @PreAuthorize("@ss.hasPermission('marketStaff:marketStaff:cooperate')")
    @PostMapping("/cooperate")
    public ApiResult cooperate(@RequestBody MarketStaffCooperationVo dto) {
        return toAjax(marketStaffService.cooperate(dto));
    }

}
