package com.xgwc.serviceProvider.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillSubDto {

    /**
     * 子账单id
     */
    private Long subBillId;

    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 账单周期开始
     */
    private String billStart;

    /**
     * 账单周期结束
     */
    private String billEnd;

    /**
     * 订单数
     */
    private Integer orderCount;

    /**
     * 总佣金
     */
    private BigDecimal totalCommission;

    /**
     * 无票扣款
     */
    private BigDecimal noInvoice;

    /**
     * 佣金追回
     */
    private BigDecimal commissionBack;

    /**
     * 罚款金额
     */
    private BigDecimal fineAmount;

    /**
     * 应发金额
     */
    private BigDecimal payableAmount;

    /**
     * 实发金额
     */
    private BigDecimal realAmount;

    /**
     * 设计师ID
     */
    private Long designerId;

    /**
     * 设计师名称
     */
    private String designerName;

    /**
     * 是否锁定
     */
    private Integer isLock;

    /**
     * 出账时间
     */
    private String billTime;

    /**
     * 确认时间
     */
    private String comfirmTime;

    /**
     * 结算时间
     */
    private String settlementTime;

    /**
     * 结算状态
     */
    private Integer settlementStatus;

    /**
     * 品牌商id
     */
    private Long brandId;

    /**
     * 加盟商id
     */
    private Long franchiseId;

    /**
     * 主体id
     */
    private Long companyInfoId;

    /**
     * 打款失败原因
     */
    private String failReason;

}
