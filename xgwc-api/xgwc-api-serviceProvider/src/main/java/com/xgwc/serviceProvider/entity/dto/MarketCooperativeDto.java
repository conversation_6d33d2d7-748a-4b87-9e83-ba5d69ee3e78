package com.xgwc.serviceProvider.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

@Data
public class MarketCooperativeDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("销售服务商id")
    @Excel(name = "销售服务商id")
    private Long serviceOwnerId;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("状态：0合作中，1终止合作")
    @Excel(name = "状态：0合作中，1终止合作")
    private Integer status;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("销售服务商名称")
    private String serviceName;

    @FieldDesc("合作中人数")
    private Integer collaboratorsNumber;

    @FieldDesc("未合作人数")
    private Integer unCollaboratorsNumber;

}
