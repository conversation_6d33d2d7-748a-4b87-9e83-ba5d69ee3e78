package com.xgwc.serviceProvider.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

@Data
public class ServiceStaffQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("其他：姓名、花名、手机号")
    private String other;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("岗位id")
    private Long postId;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    private Integer jobNature;

    @FieldDesc("状态：0在职，1离职")
    private Integer status;

    @FieldDesc("绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("服务商id")
    private Long serviceOwnerId;

    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    @FieldDesc("入职开始时间")
    private String startTime;

    @FieldDesc("入职结束时间")
    private String endTime;

    @FieldDesc("姓名")
    private String name;

    @FieldDesc("花名")
    private String stageName;

    @FieldDesc("手机号")
    private String loginPhone;

    private List<Long> deptIds;

}
