package com.xgwc.serviceProvider.entity.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class FinanceOfflinePaymentsParam {

    private static final long serialVersionUID=1L;
    
    @FieldDesc("归档结果:0-未锁定 1-已锁定")
    private Long archivalStatus;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("收款主体id")
    private Long companyInfoId;

    @FieldDesc("客户ID")
    private String customerId;

    @FieldDesc("差额")
    private Long differenceAmount;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("自增主键")
    private Long id;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("订单id")
    private String orderId;

    @FieldDesc("收款金额")
    private Long paymentAmount;

    @FieldDesc("付款流水号")
    private String paymentFlowNumber;

    @FieldDesc("支付方式:1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫 ")
    private Long paymentMethod;

    @FieldDesc("收款编号")
    private String paymentNumber;

    @FieldDesc("付款截图")
    private String paymentScreenshot;

    @FieldDesc("收款状态:0-财务未收 1-财务已收")
    private Long paymentStatus;

    @FieldDesc("备注")
    private String remarks;

    @FieldDesc("店铺id")
    private Long shopId;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date[]  orderDate;

    @FieldDesc("标识 0-品牌商 1-服务商")
    private Integer isFlag;

    @FieldDesc("店铺负责人userId")
    private Long shopManagerId;



}
