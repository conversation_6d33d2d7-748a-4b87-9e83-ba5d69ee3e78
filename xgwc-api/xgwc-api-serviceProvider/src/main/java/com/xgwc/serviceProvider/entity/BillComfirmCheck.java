package com.xgwc.serviceProvider.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class BillComfirmCheck {

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单发票ID
     */
    @NotNull(message = "ID不能为空")
    private Long billComfirmId;

    /**
     * 审核用户ID
     */
    private Long checkUserId;

    /**
     * 审核人
     */
    private String checkUserName;

    /**
     * 备注
     */
    private String comment;

    /**
     * 状态：0正常，1非正常
     */
    private Integer status;

    /**
     * 审核结果：0待审核，1审核成功，2.审核失败
     */
    @NotNull(message = "审核状态不能为空")
    private Integer checkStatus;
    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
