package com.xgwc.serviceProvider.entity.param;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-28  14:05
 */

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

/**
 * 服务商岗位管理参数
 */
@Data
public class ServiceStationParam {

    /**
     * 岗位名称
     */
    @FieldDesc("岗位名称")
    private String stationName;

    /**
     * 状态：0正常，1禁用
     */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    /**
     * 岗位id
     */
    @FieldDesc("岗位id")
    private Long stationId;

    /**
     * 服务商id
     */
    @FieldDesc(value = "服务商id")
    private Long serviceId;

    /**
     * 销售服务商id
     */
    @FieldDesc(value = "销售服务商id")
    private Long marketId;
}
