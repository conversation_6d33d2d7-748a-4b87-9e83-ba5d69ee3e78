package com.xgwc.serviceProvider.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.ServiceRoleDto;
import com.xgwc.serviceProvider.entity.param.ServiceRoleParam;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: k<PERSON><PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-04-28  11:55
 */
public interface MarketRoleService {

    /**
     * 获取销售服务商角色列表
     * @param marketRoleParam 销售服务商角色管理参数
     * @return 销售服务商角色列表
     */
    List<ServiceRoleVo> getMarketRoleList(ServiceRoleParam marketRoleParam);

    /**
     * 保存销售服务商角色
     * @param marketRoleDto 销售服务商角色参数
     * @return 保存结果
     */
    ApiResult saveMarketRole(ServiceRoleDto marketRoleDto);

    /**
     * 获取销售服务商角色详情
     * @param roleId 角色ID
     * @return 角色详情
     */
    ApiResult getMarketRoleById(Long roleId);

    /**
     * 修改销售服务商角色
     * @param marketRoleDto 销售服务商角色参数
     * @return 修改结果
     */
    ApiResult updateMarketRole(ServiceRoleDto marketRoleDto);

    /**
     * 修改销售服务商角色状态
     * @param roleId 角色ID
     * @param status 状态
     * @return 修改结果
     */
    ApiResult updateStatusById(Integer roleId, Integer status);
}
