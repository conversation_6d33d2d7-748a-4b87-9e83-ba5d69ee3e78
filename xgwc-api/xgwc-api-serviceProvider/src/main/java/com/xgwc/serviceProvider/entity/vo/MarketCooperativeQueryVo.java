package com.xgwc.serviceProvider.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

@Data
public class MarketCooperativeQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("服务商名称")
    private String serviceName;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("状态：0合作中，1终止合作")
    private Integer status;

    private Integer isDel;

}
