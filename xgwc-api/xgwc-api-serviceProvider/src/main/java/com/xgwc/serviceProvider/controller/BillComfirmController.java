package com.xgwc.serviceProvider.controller;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.BillComfirm;
import com.xgwc.serviceProvider.entity.BillComfirmCheck;
import com.xgwc.serviceProvider.entity.dto.BillComfirmDto;
import com.xgwc.serviceProvider.service.BillComfirmService;
import com.xgwc.serviceProvider.service.BillDesignerService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/bill/comfirm")
@Slf4j
public class BillComfirmController {

    @Resource
    private BillComfirmService billComfirmService;

    @Resource
    private BillDesignerService billDesignerService;

    /**
     * 插入发票信息
     */
    @PostMapping("billComfirm")
    public ApiResult billComfirm(@RequestBody BillComfirm billComfirm) {
        int result = billComfirmService.insertBillComfirm(billComfirm);
        return result > 0 ? ApiResult.ok() : ApiResult.error("插入失败");
    }

    /**
     * 插入审核记录
     */
    @PostMapping("check")
    public ApiResult insertBillComfirmCheck(@RequestBody @Valid BillComfirmCheck billComfirmCheck) {
        int result = billComfirmService.insertBillVoiceCheck(billComfirmCheck);
        return result > 0 ? ApiResult.ok() : ApiResult.error("插入失败");
    }

    /**
     * 根据账单ID查询发票信息
     * @param billId 账单ID
     * @return 发票信息
     */
    @RequestMapping("getBillComfirm")
    public ApiResult getBillComfirm(Long billId) {
        BillComfirmDto billComfirmDto = billComfirmService.selectBillComfirmById(billId);
        return ApiResult.ok(billComfirmDto);
    }

    @RequestMapping("preStatcsBillDesigner")
    public ApiResult preStatcsBillDesigner(Long billId){
        billDesignerService.preStatcsBillDesigner(billId);
        return ApiResult.ok();
    }


}
