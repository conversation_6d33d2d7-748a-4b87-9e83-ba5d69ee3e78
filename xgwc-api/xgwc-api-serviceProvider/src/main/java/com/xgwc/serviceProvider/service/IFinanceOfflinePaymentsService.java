package com.xgwc.serviceProvider.service;


import com.xgwc.serviceProvider.entity.dto.FinanceOfflinePaymentsDto;
import com.xgwc.serviceProvider.entity.param.FinanceOfflinePaymentsParam;
import com.xgwc.serviceProvider.entity.vo.FinanceOfflinePaymentsVo;

import java.util.List;

public interface IFinanceOfflinePaymentsService  {
    /**
     * 查询线下收款
     *
     * @param id 线下收款主键
     * @param isFlag 标识 0-品牌商 1-服务商
     * @return 线下收款
     */
    FinanceOfflinePaymentsDto selectFinanceOfflinePaymentsById(Long id, Integer isFlag);

    /**
     * 查询线下收款列表
     * 
     * @param financeOfflinePayments 线下收款
     * @return 线下收款集合
     */
    List<FinanceOfflinePaymentsDto> selectFinanceOfflinePaymentsList(FinanceOfflinePaymentsParam financeOfflinePayments);

    /**
     * 修改线下收款
     * 
     * @param financeOfflinePayments 线下收款
     * @return 结果
     */
    int updateFinanceOfflinePayments(FinanceOfflinePaymentsVo financeOfflinePayments);
}
