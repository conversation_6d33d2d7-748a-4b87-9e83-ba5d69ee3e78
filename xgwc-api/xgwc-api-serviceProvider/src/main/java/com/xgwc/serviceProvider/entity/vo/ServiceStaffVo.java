package com.xgwc.serviceProvider.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

@Data
public class ServiceStaffVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("姓名")
    private String name;

    @FieldDesc("花名")
    private String stageName;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("岗位id")
    private Long postId;

    @FieldDesc("角色权限")
    private String roleIds;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    private Integer jobNature;

    @FieldDesc("状态：0在职，1离职")
    private Integer status;

    @FieldDesc("部门负责人：0是，1否")
    private Integer isPrincipal;

    @FieldDesc("部门助理：0是，1否")
    private Integer isAssistant;

    @FieldDesc("部门排班：0是，1否")
    private Integer isSchedule;

    @FieldDesc("直属上级")
    private Long superior;

    @FieldDesc("绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("绑定的用户id")
    private Long bindUserId;

    @FieldDesc("登录手机号")
    private String loginPhone;

    @FieldDesc("服务商id")
    private Long serviceOwnerId;

}
