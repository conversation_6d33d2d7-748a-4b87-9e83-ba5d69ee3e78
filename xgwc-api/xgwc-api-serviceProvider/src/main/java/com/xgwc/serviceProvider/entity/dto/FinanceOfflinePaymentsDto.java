package com.xgwc.serviceProvider.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class FinanceOfflinePaymentsDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("实收金额")
    @Excel(name = "实收金额")
    private BigDecimal actualAmount;

    @FieldDesc("归档结果:0-未锁定 1-已锁定")
    @Excel(name = "归档结果:0-未锁定 1-已锁定")
    private Integer archivalStatus;

    @FieldDesc("收款主体id")
    @Excel(name = "收款主体id")
    private Long companyInfoId;

    @FieldDesc("主体名称")
    @Excel(name = "主体名称")
    private String companyName;

    @FieldDesc("谈单人")
    @Excel(name = "谈单人")
    private String contactPerson;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String customerId;

    @FieldDesc("差额")
    @Excel(name = "差额")
    private BigDecimal differenceAmount;

    @FieldDesc("品牌商id")
    @Excel(name = "品牌商id")
    private Long brandId;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("来自加盟商")
    @Excel(name = "来自加盟商")
    private String franchiseName;

    @FieldDesc("自增主键")
    @Excel(name = "自增主键")
    private Long id;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long orderId;

    @FieldDesc("收款金额")
    @Excel(name = "收款金额")
    private BigDecimal paymentAmount;

    @FieldDesc("付款流水号")
    @Excel(name = "付款流水号")
    private String paymentFlowNumber;

    @FieldDesc("支付方式:1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫 ")
    @Excel(name = "支付方式:1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫 ")
    private Integer paymentMethod;

    @FieldDesc("收款编号")
    @Excel(name = "收款编号")
    private String paymentNumber;

    @FieldDesc("付款截图")
    @Excel(name = "付款截图")
    private String paymentScreenshot;

    @FieldDesc("收款状态:0-财务未收 1-财务已收")
    @Excel(name = "收款状态:0-财务未收 1-财务已收")
    private Integer paymentStatus;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remarks;

    @FieldDesc("店铺id")
    @Excel(name = "店铺id")
    private Long shopId;

    @FieldDesc("店铺名称")
    @Excel(name = "店铺名称")
    private String shopName;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    @FieldDesc("订单支付流水id")
    @Excel(name = "订单支付流水id")
    private Long payId;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("actualAmount",getActualAmount())
            .append("archivalStatus",getArchivalStatus())
            .append("companyInfoId",getCompanyInfoId())
            .append("contactPerson",getContactPerson())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("customerId",getCustomerId())
            .append("differenceAmount",getDifferenceAmount())
            .append("franchiseId",getFranchiseId())
            .append("id",getId())
            .append("modifyTime",getModifyTime())
            .append("orderId",getOrderId())
            .append("paymentAmount",getPaymentAmount())
            .append("paymentFlowNumber",getPaymentFlowNumber())
            .append("paymentMethod",getPaymentMethod())
            .append("paymentNumber",getPaymentNumber())
            .append("paymentScreenshot",getPaymentScreenshot())
            .append("paymentStatus",getPaymentStatus())
            .append("remarks",getRemarks())
            .append("shopId",getShopId())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
