package com.xgwc.serviceProvider.entity.dto;

import com.xgwc.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

// 数据项类
@Data
public class BillDetail {

    @Excel(name = "账单ID", sort = 1)
    private Long billId;

    @Excel(name = "子账单ID", sort = 1)
    private Long subBillId;

    @Excel(name = "订单编号", sort = 2)
    private String orderNo;

    @Excel(name = "客户ID", sort = 3)
    private String taobaoId;

    @Excel(name = "所属加盟商", sort = 4)
    private String franchiseName;

    @Excel(name = "业务分类", sort = 5)
    private String designerBusiness;

    @Excel(name = "交定稿时间", sort = 6)
    private String archiveTime;

    @Excel(name = "订单完成时间", sort = 7)
    private String finishedTime;

    @Excel(name = "订单成交时间", sort = 8)
    private String dealTime;

    @Excel(name = "佣金", sort = 9)
    private BigDecimal money;


}