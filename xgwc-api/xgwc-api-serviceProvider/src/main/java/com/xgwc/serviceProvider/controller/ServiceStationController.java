package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.ServiceStationDto;
import com.xgwc.serviceProvider.entity.param.ServiceStationParam;
import com.xgwc.serviceProvider.entity.vo.ServiceStationVo;
import com.xgwc.serviceProvider.service.ServiceStationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: kouwenzhu<PERSON>
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 服务商岗位管理
 */
@RestController
@RequestMapping("/serviceStation")
@Slf4j
public class ServiceStationController extends BaseController {

    @Resource
    private ServiceStationService serviceStationService;

    /**
     * @param serviceStationParam 查询条件
     * @return 岗位管理列表
     * 查询岗位管理列表
     */
    @MethodDesc("查询岗位管理列表")
    @PreAuthorize("@ss.hasPermission('serviceStation:serviceStation:list')")
    @PostMapping("/getServiceStationList")
    public ApiResult<ServiceStationVo> getServiceStationList(@RequestBody ServiceStationParam serviceStationParam) {
        startPage();
        return getDataTable(serviceStationService.getServiceStationList(serviceStationParam));
    }

    /**
     * 查询服务商询岗位管理下拉框
     *
     * @return 岗位管理下拉框
     */
    @GetMapping("/getServiceStationDropDown")
    public ApiResult getServiceStationDropDown() {
        try {
            List<ServiceStationVo> result = serviceStationService.getServiceStationList(new ServiceStationParam());
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取服务商询岗位管理下拉框失败", e);
            return ApiResult.error("获取服务商询岗位管理下拉框失败");
        }
    }

    /**
     * @param serviceStationDto 新增岗位管理信息
     * @return 插入结果
     * 新增岗位管理信息
     */
    @MethodDesc("新增岗位管理信息")
    @PreAuthorize("@ss.hasPermission('serviceStation:serviceStation:add')")
    @Submit(fileds = "userId")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveServiceStation")
    public ApiResult saveServiceStation(@RequestBody ServiceStationDto serviceStationDto) {
        return serviceStationService.saveServiceStation(serviceStationDto);
    }

    /**
     * @param stationId 岗位管理id
     * @return 岗位管理信息
     * 根据id查询岗位管理信息
     */
    @MethodDesc("根据id查询岗位管理信息")
    @PreAuthorize("@ss.hasPermission('serviceStation:serviceStation:query')")
    @GetMapping("/getServiceStationById/{stationId}")
    public ApiResult getServiceStationById(@PathVariable Long stationId) {
        return serviceStationService.getServiceStationById(stationId);
    }

    /**
     * @param serviceStationDto 修改信息
     * @return 修改结果
     * 修改岗位管理信息
     */
    @MethodDesc("修改岗位管理信息")
    @PreAuthorize("@ss.hasPermission('serviceStation:serviceStation:edit')")
    @Submit(fileds = "userId")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateServiceStation")
    public ApiResult updateServiceStation(@RequestBody ServiceStationDto serviceStationDto) {
        return serviceStationService.updateServiceStation(serviceStationDto);
    }

    /**
     * @param stationId 岗位id
     * @return 岗位信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('serviceStation:serviceStation:editStatusById')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "stationId") Integer stationId,
                                 @RequestParam(value = "status") Integer status) {
        return serviceStationService.updateStatusById(stationId,status);
    }

}
