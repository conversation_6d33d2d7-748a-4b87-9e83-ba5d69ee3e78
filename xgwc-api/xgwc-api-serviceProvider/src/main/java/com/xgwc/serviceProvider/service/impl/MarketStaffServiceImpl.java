package com.xgwc.serviceProvider.service.impl;

import java.util.ArrayList;
import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.StaffLog;
import com.xgwc.common.entity.StaffLogDto;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.*;
import com.xgwc.serviceProvider.dao.*;
import com.xgwc.serviceProvider.entity.MarketStaffCooperative;
import com.xgwc.serviceProvider.entity.dto.MarketStaffCooperativeDto;
import com.xgwc.serviceProvider.entity.dto.ServiceDeptDto;
import com.xgwc.serviceProvider.entity.dto.ServiceRoleDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperationVo;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleMenuVo;
import com.xgwc.serviceProvider.feign.entity.BindStaffDto;
import com.xgwc.serviceProvider.feign.entity.StaffDto;
import com.xgwc.serviceProvider.util.ServiceStaffLogParseUtil;
import com.xgwc.user.feign.api.StaffFeign;
import com.xgwc.user.feign.api.StaffLogFeign;
import com.xgwc.user.feign.api.UserDetailFeign;
import com.xgwc.user.feign.entity.FeignFranchiseStaffDto;
import com.xgwc.user.feign.entity.SysUserMiddle;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.xgwc.serviceProvider.service.IMarketStaffService;
import com.xgwc.serviceProvider.entity.MarketStaff;
import com.xgwc.serviceProvider.entity.vo.MarketStaffVo;
import com.xgwc.serviceProvider.entity.dto.MarketStaffDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffQueryVo;
import org.springframework.transaction.annotation.Transactional;

import static com.xgwc.common.util.ParamDecryptUtil.PHONE_KEY;


@Service
@Slf4j
public class MarketStaffServiceImpl implements IMarketStaffService {
    @Resource
    private MarketStaffMapper marketStaffMapper;
    @Resource
    private MarketDeptMapper marketDeptMapper;
    @Resource
    private StaffLogFeign staffLogFeign;
    @Resource
    private UserDetailFeign userDetailFeign;
    @Resource
    private MarketRoleMapper marketRoleMapper;
    @Resource
    private MarketStaffCooperativeMapper marketStaffCooperativeMapper;
    @Resource
    private StaffFeign staffFeign;

    /**
     * 查询销售服务商员工表
     * @param id 销售服务商员工表主键
     * @return 销售服务商员工表
     */
    @Override
    public MarketStaffDto selectMarketStaffById(Long id) {
        return marketStaffMapper.selectMarketStaffById(id);
    }

    /**
     * 查询销售服务商员工表列表
     * @param marketStaff 销售服务商员工表
     * @return 销售服务商员工表
     */
    @Override
    public List<MarketStaffDto> selectMarketStaffList(MarketStaffQueryVo marketStaff) {
        Long serviceId = SecurityUtils.getSysUser().getServiceId();
        if(serviceId == null)return List.of();
        if(StringUtils.isNotEmpty(marketStaff.getLoginPhone())){
            marketStaff.setLoginPhone(ParamDecryptUtil.encrypt(marketStaff.getLoginPhone(), PHONE_KEY));
        }
        if(marketStaff.getDeptId() != null){
            List<Long> allDeptIds = new ArrayList<>();
            collectChildDeptIds(marketStaff.getDeptId(), allDeptIds,serviceId);
            marketStaff.setDeptIds(allDeptIds);
        }
        marketStaff.setServiceOwnerId(serviceId);
        List<MarketStaffDto> list = marketStaffMapper.selectMarketStaffList(marketStaff);
        if(!list.isEmpty()){
            for (MarketStaffDto item : list) {
                if(StringUtils.isNotEmpty(item.getLoginPhone())){
                    item.setLoginPhone(ParamDecryptUtil.decryptParam(item.getLoginPhone(), PHONE_KEY));
                }
            }
        }
        return list;
    }

    private void collectChildDeptIds(Long parentId, List<Long> allIds, Long serviceId) {
        allIds.add(parentId);
        List<ServiceDeptDto> children = marketDeptMapper.getDeptByPid(parentId,serviceId);
        for(ServiceDeptDto child : children){
            collectChildDeptIds(child.getDeptId(), allIds,serviceId);
        }
    }

    /**
     * 新增销售服务商员工表
     * @param dto 销售服务商员工表
     * @return 结果
     */
    @Override
    public int insertMarketStaff(MarketStaffVo dto) {
        SysUser sysUser = SecurityUtils.getSysUser();
        MarketStaff marketStaff = BeanUtil.copyProperties(dto, MarketStaff.class);
        marketStaff.setCreateById(sysUser.getUserId());
        marketStaff.setCreateBy(sysUser.getUserName());
        marketStaff.setCreateTime(DateUtils.getNowDate());
        return marketStaffMapper.insertMarketStaff(marketStaff);
    }

    /**
     * 修改销售服务商员工表
     * @param dto 销售服务商员工表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMarketStaff(MarketStaffVo dto) {
        SysUser sysUser = SecurityUtils.getSysUser();
        MarketStaffDto oldStaff = marketStaffMapper.selectMarketStaffById(dto.getId());
        if(oldStaff == null){
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        MarketStaff newStaff = BeanUtil.copyProperties(dto, MarketStaff.class);
        newStaff.setUpdateTime(DateUtils.getNowDate());
        newStaff.setUpdateBy(sysUser.getUserName());
        newStaff.setUpdateById(sysUser.getUserId());
        int result = marketStaffMapper.updateMarketStaff(newStaff);
        try {
            if (result > 0) {
                StaffLog log = ChangeLogUtil.buildAggregatedLog(
                        oldStaff,
                        newStaff,
                        dto.getId(),
                        newStaff.getUpdateBy(),
                        newStaff.getUpdateTime(),
                        "修改员工"
                );
                if (log != null) {
                    log.setBusinessType(4);
                    log.setBusinessId(oldStaff.getServiceOwnerId());
                    staffLogFeign.addStaffLog(log);
                }
            }
            if(StringUtils.isNotEmpty(newStaff.getRoleIds()) && oldStaff.getBindUserId() != null){
                userDetailFeign.deleteBrandRoleUserByUserId(oldStaff.getBindUserId());
                String[] roleIds = newStaff.getRoleIds().split(",");
                for(String roleId : roleIds){
                    userDetailFeign.addBrandRoleUser(Long.valueOf(roleId), oldStaff.getBindUserId(),3);
                }
            }else if(oldStaff.getBindUserId() != null){
                userDetailFeign.deleteBrandRoleUserByUserId(oldStaff.getBindUserId());
            }
        } catch (Exception e) {
            log.error("feign调用失败，添加员工角色失败", e);
        }
        return result;
    }

    /**
     * 删除销售服务商员工表信息
     * @param id 销售服务商员工表主键
     * @return 结果
     */
    @Override
    public int deleteMarketStaffById(Long id) {
        return marketStaffMapper.deleteMarketStaffById(id);
    }

    @Override
    public List<StaffLogDto> findStaffLogByStaffIdAndBusinessType(Long id, Integer businessType) {
        ApiResult<List<StaffLogDto>> result = staffLogFeign.findStaffLogByStaffIdAndBusinessType(id, businessType);
        if (result.getMessage().equals("OK")) {
            List<StaffLogDto> list = result.getData();
            if (list != null && !list.isEmpty()) {
                for (StaffLogDto dto : list) {
                    String parsed = ServiceStaffLogParseUtil.parseLogContentToReadable(dto.getRemark(), businessType);
                    dto.setParsedRemark(parsed);
                }
                return list;
            }
        }
        return List.of();
    }


    @Override
    public List<MarketStaffDto> selectStaffListDropDown(Long deptId) {
        Long serviceId = SecurityUtils.getSysUser().getServiceId();
        return marketStaffMapper.selectStaffListDropDown(serviceId, deptId);
    }

    @Override
    public boolean selectMarketStaffByNameAndServiceId(String staffName, Long serviceId) {
        if (marketStaffMapper.findByStaffNameAndServiceId(staffName, serviceId) == null) {
            return true;
        }
        return false;
    }

    @Override
    public void updateMarketStaffBindStatus(BindStaffDto bindStaffDto) {
        MarketStaffDto marketStaffDto = marketStaffMapper.selectMarketStaffById(bindStaffDto.getId());
        if(marketStaffDto == null){
            log.error("服务商员工不存在");
        }
        marketStaffMapper.updateMarketStaff(BeanUtil.copyProperties(bindStaffDto, MarketStaff.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMarketStaff(StaffDto staffDto) {
        String name = staffDto.getName();
        MarketStaff newStaff = BeanUtil.copyProperties(staffDto, MarketStaff.class);
        newStaff.setBindStatus(1);
        newStaff.setStageName(staffDto.getStageName());
        newStaff.setCreateTime(DateUtils.getNowDate());
        newStaff.setCreateBy(name);
        // 添加角色
        ServiceRoleDto serviceRoleDto = new ServiceRoleDto();
        serviceRoleDto.setRoleName(name + "管理员");
        serviceRoleDto.setServiceId(staffDto.getServiceOwnerId());
        serviceRoleDto.setSort(0);
        serviceRoleDto.setIsFlag("0");
        serviceRoleDto.setCreateBy(name);
        marketRoleMapper.saveMarketRole(serviceRoleDto);
        List<ServiceRoleMenuVo>  menuList = new ArrayList<>();
        if(!staffDto.getMenuIds().isEmpty()){
            for (Long menuId : staffDto.getMenuIds()) {
                ServiceRoleMenuVo menuVo = new ServiceRoleMenuVo();
                menuVo.setRoleId(serviceRoleDto.getRoleId());
                menuVo.setMenuId(menuId.intValue());
                menuVo.setCreateBy(name);
                menuList.add(menuVo);
            }
            marketRoleMapper.saveRoleMenu(menuList);
        }
        newStaff.setRoleIds(String.valueOf(serviceRoleDto.getRoleId()));
        marketStaffMapper.insertMarketStaff(newStaff);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cooperate(MarketStaffCooperationVo dto) {
        SysUser sysUser = SecurityUtils.getSysUser();
        MarketStaffDto marketStaffDto = marketStaffMapper.selectMarketStaffById(dto.getId());
        if(marketStaffDto == null){
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        if(marketStaffDto.getBindStatus() != 1){
            throw new ApiException("该员工未绑定,请先绑定");
        }
        MarketStaffCooperativeDto cooperativeDto = marketStaffCooperativeMapper.findByStaffIdAndFranchiseId(dto.getId(), dto.getFranchiseId());
        if(cooperativeDto != null){
            throw new ApiException("该加盟商已添加,请勿重复添加");
        }
        ApiResult<Long> userMiddleById = userDetailFeign.getUserMiddleById(marketStaffDto.getBindUserId());
        Long userId = userMiddleById.getData();
        if(userId == null){
            throw new ApiException("用户不存在");
        }
        SysUserMiddle middleVo = new SysUserMiddle();
        middleVo.setId(AutoIdUtil.getId());
        middleVo.setMainUserId(userId);
        middleVo.setUserType(5);
        middleVo.setSourceId(dto.getFranchiseId());
        ApiResult<Long> longApiResult = userDetailFeign.insertUserMiddle(middleVo);
        Long middleId = longApiResult.getData();
        if(middleId == null){
            throw new ApiException("添加中间表失败");
        }
        ApiResult<Long> result = addFranchiseStaff(dto.getFranchiseId(), middleId, marketStaffDto);
        Long staffId = result.getData();
        if(staffId == null){
            throw new ApiException("添加加盟商员工失败");
        }
        MarketStaffCooperative cooperative = new MarketStaffCooperative();
        cooperative.setStaffId(dto.getId());
        cooperative.setServiceOwnerId(marketStaffDto.getServiceOwnerId());
        cooperative.setFranchiseId(dto.getFranchiseId());
        cooperative.setFranchiseStaffId(staffId);
        cooperative.setCreateById(sysUser.getUserId());
        cooperative.setCreateBy(sysUser.getUserName());
        cooperative.setCreateTime(DateUtils.getNowDate());
        return marketStaffCooperativeMapper.insertMarketStaffCooperative(cooperative);
    }

    private ApiResult<Long> addFranchiseStaff(Long franchiseId, Long middleId, MarketStaffDto dto) {
        FeignFranchiseStaffDto staffDto = new FeignFranchiseStaffDto();
        staffDto.setName(dto.getName());
        staffDto.setStageName(dto.getStageName());
        staffDto.setJobNature(3);
        staffDto.setStatus(2);
        staffDto.setBindStatus(1);
        staffDto.setBindUserId(middleId);
        staffDto.setLoginPhone(dto.getLoginPhone());
        staffDto.setFranchiseId(franchiseId);
        staffDto.setCreateBy(SecurityUtils.getSysUser().getUserName());
        staffDto.setCreateTime(DateUtils.getNowDate());
        staffDto.setStaffType(1);
        return staffFeign.addFranchiseStaff(staffDto);
    }
}
