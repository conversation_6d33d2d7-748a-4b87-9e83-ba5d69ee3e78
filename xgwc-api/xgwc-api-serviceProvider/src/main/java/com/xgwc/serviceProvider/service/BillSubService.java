package com.xgwc.serviceProvider.service;

import com.xgwc.serviceProvider.entity.dto.BillBrandDetailDto;
import com.xgwc.serviceProvider.entity.dto.BillBrandSubDto;
import com.xgwc.serviceProvider.entity.dto.BillPaymentRecord;
import com.xgwc.serviceProvider.entity.vo.BillVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BillSubService {

    /**
     * 账单品牌列表
     * @return 品牌下账单
     */
    List<BillBrandSubDto> getBillBrandSubList(BillVo billVo);

    /**
     * 账单服务商列表
     * @return 品牌下账单
     */
    List<BillBrandSubDto> getBillServiceSubList(BillVo billVo);

    /**
     * 获取母账单详情
     * @param billVo 参数
     * @return 详情
     */
    BillBrandDetailDto getBillDetail(BillVo billVo);

    /**
     * 获取母账单详情
     * @param billVo 参数
     * @return 详情
     */
    BillBrandDetailDto getServiceBillDetail(BillVo billVo);

    /**
     * 下载子账单
     * @param response   响应
     * @param subBillId 子账单id
     * @param isFlag    0:品牌商 1:服务商
     */
    void downloadSubBill(HttpServletResponse response, Long subBillId, Integer isFlag);

    /**
     * 获取待结算账单
     *
     * @param response 响应
     * @param brandId 品牌商id
     */
    void downloadWaitSettleBill(HttpServletResponse response, Long brandId);

    /**
     * 获取打款失败账单
     *
     * @param response 响应
     * @param brandId 品牌商id
     */
    void downloadPaymentFailedBill(HttpServletResponse response, Long brandId);

    /**
     * 导入打款结果
     *
     * @param file    文件
     * @param brandId 品牌商id
     */
    void importPaymentResult(MultipartFile file, Long brandId);

    /**
     * 查询打款记录
     *
     * @param brandId   品牌商id
     * @param name      表格名/打款人
     * @param date      打款时间
     * @param paymentId 打款id
     * @return 打款记录
     */
    List<BillPaymentRecord> selectPaymentRecordList(Long brandId, String name, Date[] date, Long paymentId);

    /**
     * 导出打款记录
     *
     * @param response  响应
     * @param paymentId 打款记录id
     */
    void exportPaymentRecord(HttpServletResponse response, Long paymentId);

    /**
     * 获取打款失败原因
     *
     * @param subBillId 子账单id
     * @param isFlag    0:品牌商 1:服务商
     * @return 打款失败原因
     */
    String getPaymentFailedReasonBySubBillId(Long subBillId, Integer isFlag);

    /**
     * 获取服务商服务的品牌商
     *
     * @return 品牌商
     */
    List<Map<String, Object>> getAuthorizeBrandIdsByServiceId();

    /**
     * 下载打款模板
     *
     * @param response 响应
     */
    void downloadPaymentTemplate(HttpServletResponse response);
}
