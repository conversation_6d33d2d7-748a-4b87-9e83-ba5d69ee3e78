package com.xgwc.serviceProvider.service;

import com.xgwc.serviceProvider.entity.BillComfirm;
import com.xgwc.serviceProvider.entity.BillComfirmCheck;
import com.xgwc.serviceProvider.entity.dto.BillComfirmCheckDto;
import com.xgwc.serviceProvider.entity.dto.BillComfirmDto;

import java.util.List;

public interface BillComfirmService {

    /**
     * 插入发票数据
     * @param billComfirm 账单发票
     * @return 是否成功
     */
    int insertBillComfirm(BillComfirm billComfirm);

    /**
     * 插入发票审核数据
     * @param billComfirmCheck 审核参数
     * @return 是否成功
     */
    int insertBillVoiceCheck(BillComfirmCheck billComfirmCheck);

    /**
     * 插入发票数据
     * @param billComfirm 账单发票
     * @return 是否成功
     */
    int updateBillComfirm(BillComfirm billComfirm);

    /**
     * 根据账单id获取发票信息
     * @param billId 账单id
     * @return 发票信息
     */
    BillComfirmDto selectBillComfirmById(Long billId);

    /**
     * 查询审核详情
     * @param billComfirmId 账单发票id
     * @return 审核记录
     */
    List<BillComfirmCheckDto> selectBillComfirmCheckList(Long billComfirmId);
}
