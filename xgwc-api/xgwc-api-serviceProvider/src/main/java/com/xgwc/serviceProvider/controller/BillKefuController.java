package com.xgwc.serviceProvider.controller;

import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.BillKefuUpload;
import com.xgwc.serviceProvider.service.BillKefuUploadService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("bill/kefu")
@RestController
public class BillKefuController extends BaseController {

    @Resource
    private BillKefuUploadService billKefuUploadService;

    /**
     * 保存岗位工资
     */
    @PostMapping("save_payroll")
    public ApiResult uploadBillKefu(@RequestBody BillKefuUpload billKefuUpload) {
        int result = billKefuUploadService.insertBillKeFuUpload(billKefuUpload);
        return result > 0 ? ApiResult.ok() : ApiResult.error("上传失败");
    }

    /**
     * 获取岗位工资上传记录
     */
    @GetMapping("getUploadRecordList")
    public ApiResult getUploadRecordList() {
        startPage();
        List<BillKefuUpload> billKefuUploads = billKefuUploadService.getBillKeFuUploadListByServiceId();
        return ApiResult.ok(billKefuUploads);
    }
}
