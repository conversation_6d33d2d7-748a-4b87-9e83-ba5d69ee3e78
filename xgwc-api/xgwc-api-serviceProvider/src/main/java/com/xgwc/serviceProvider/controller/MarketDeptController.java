package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.ServiceDeptDto;
import com.xgwc.serviceProvider.entity.param.ServiceDeptParam;
import com.xgwc.serviceProvider.entity.vo.ServiceDeptInfo;
import com.xgwc.serviceProvider.service.MarketDeptService;
import com.xgwc.serviceProvider.service.ServiceDeptService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: -parent
 * @BelongsPackage: com..order.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 销售服务商部门管理
 */
@RestController
@RequestMapping("/marketDept")
@Slf4j
public class MarketDeptController extends BaseController {

    @Resource
    private MarketDeptService marketDeptService;

    /**
     * @param marketDeptParam 查询条件
     * @return 部门管理列表
     * 查询部门管理列表
     */
    @MethodDesc("查询部门管理列表")
    //@PreAuthorize("@ss.hasPermission('marketDept:marketDept:list')")
    @PostMapping("/getMarketDeptList")
    public ApiResult<ServiceDeptInfo> getMarketDeptList(@RequestBody ServiceDeptParam marketDeptParam) {
        startPage();
        return getDataTable(marketDeptService.getMarketDeptList(marketDeptParam));
    }

    /**
     * 查询部门管理树
     *
     * @return 部门管理列表
     */
    @GetMapping("/getMarketDeptDropDown")
    public ApiResult getMarketDeptListInfo() {
        try {
            ServiceDeptParam marketDeptParam = new ServiceDeptParam();
            marketDeptParam.setStatus(0);
            List<ServiceDeptInfo> result = marketDeptService.getMarketDeptList(marketDeptParam);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取销售服务商部门管理树失败", e);
            return ApiResult.error("获取销售服务商部门管理树失败");
        }
    }

    /**
     * @param marketDeptDto 新增部门管理信息
     * @return 插入结果
     * 新增部门管理信息
     */
    @MethodDesc("新增部门管理信息")
    @Submit(fileds = "userId")
    @PreAuthorize("@ss.hasPermission('marketDept:marketDept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveMarketDept")
    public ApiResult saveMarketDept(@RequestBody @Valid ServiceDeptDto marketDeptDto) {
        return marketDeptService.saveMarketDept(marketDeptDto);
    }

    /**
     * @param deptId 部门管理id
     * @return 部门管理信息
     * 根据id查询部门管理信息
     */
    @MethodDesc("根据id查询部门管理信息")
    @PreAuthorize("@ss.hasPermission('marketDept:marketDept:query')")
    @GetMapping("/getMarketDeptById/{deptId}")
    public ApiResult getMarketDeptById(@PathVariable("deptId") Long deptId) {
        return marketDeptService.getMarketDeptById(deptId);
    }

    /**
     * @param marketDeptDto 修改信息
     * @return 修改结果
     * 修改部门管理信息
     */
    @MethodDesc("修改部门管理信息")
    @Submit(fileds = "userId")
    @PreAuthorize("@ss.hasPermission('marketDept:marketDept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMarketDept")
    public ApiResult updateMarketDeptById(@RequestBody ServiceDeptDto marketDeptDto) {
        return marketDeptService.updateMarketDeptById(marketDeptDto);
    }

    /**
     * @param deptId 部门id
     * @return 品牌信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('marketDept:marketDept:editStatusById')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "deptId") Integer deptId,
                                      @RequestParam(value = "status") Integer status) {
        return marketDeptService.updateStatusById(deptId, status);
    }

    /**
     * 查询回显部门详情信息-负责人、助理、是否排班
     *
     * @param deptId 部门id
     * @return 部门详情信息-负责人、助理、是否排班
     */
    @MethodDesc("查询回显部门详情信息-负责人、助理、是否排班")
    @PreAuthorize("@ss.hasPermission('marketDept:marketDept:query')")
    @GetMapping("/getXgwcBrandDeptInfo")
    public ApiResult getXgwcDeptStaffInfo(@RequestParam(value = "deptId") Integer deptId) {
        return marketDeptService.getXgwcDeptStaffInfo(deptId);
    }

    /**
     * 修改部门详情信息-负责人、助理、是否排班
     *
     * @param marketDeptDto 部门详情信息-负责人、助理、是否排班
     * @return 部门详情信息-负责人、助理、是否排班
     */
    @MethodDesc("修改部门详情信息-负责人、助理、是否排班")
    @PreAuthorize("@ss.hasPermission('marketDept:marketDept:edit')")
    @PostMapping("/getXgwcDeptStaffSchedule")
    public ApiResult getXgwcDeptStaffSchedule(@RequestBody ServiceDeptDto marketDeptDto) {
        return marketDeptService.updateXgwcDeptStaffInfo(marketDeptDto);
    }

    @MethodDesc("查询员工下拉列表")
    @GetMapping("/marketStaff/getStaffDropDown")
    public ApiResult getMarketDeptDropDown() {
        return ApiResult.ok(new ArrayList<>());
    }

}
