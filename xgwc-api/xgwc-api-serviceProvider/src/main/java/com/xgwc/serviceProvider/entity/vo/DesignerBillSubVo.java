package com.xgwc.serviceProvider.entity.vo;

import lombok.Data;

/**
 * 子账单
 */
@Data
public class DesignerBillSubVo {

    /**
     * 品牌商ID
     */
    private Long brandId;

    /**
     * 业务
     */
    private String level;

    /**
     * 结算状态
     */
    private Integer settlementStatus;

    /**
     * 账单开始
     */
    private String billStart;

    /**
     * 账单结束
     */
    private String billEnd;

    /**
     * 设计师名称
     */
    private String designerName;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 账单id
     */
    private String subBillId;

    /**
     * 加盟商
     */
    private Long franchiseId;
}
