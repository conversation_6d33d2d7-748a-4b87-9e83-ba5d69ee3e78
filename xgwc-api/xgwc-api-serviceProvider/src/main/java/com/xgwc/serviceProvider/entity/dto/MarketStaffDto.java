package com.xgwc.serviceProvider.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class MarketStaffDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("姓名")
    @Excel(name = "姓名")
    private String name;

    @FieldDesc("花名")
    @Excel(name = "花名")
    private String stageName;

    @FieldDesc("部门id")
    @Excel(name = "部门id")
    private Long deptId;

    @FieldDesc("岗位id")
    @Excel(name = "岗位id")
    private Long postId;

    @FieldDesc("角色权限")
    @Excel(name = "角色权限")
    private String roleIds;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    @Excel(name = "工作性质：0全职，1外包，2兼职")
    private Integer jobNature;

    @FieldDesc("状态：0在职，1离职")
    @Excel(name = "状态：0在职，1离职")
    private Integer status;

    @FieldDesc("部门负责人：0是，1否")
    @Excel(name = "部门负责人：0是，1否")
    private Integer isPrincipal;

    @FieldDesc("直属上级")
    @Excel(name = "直属上级")
    private Long superior;

    @FieldDesc("绑定状态：0未绑，1已绑")
    @Excel(name = "绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("绑定的用户id")
    @Excel(name = "绑定的用户id")
    private Long bindUserId;

    @FieldDesc("登录手机号")
    @Excel(name = "登录手机号")
    private String loginPhone;

    @FieldDesc("服务商id")
    @Excel(name = "服务商id")
    private Long serviceOwnerId;

    @FieldDesc("是否删除：0正常，1删除")
    @Excel(name = "是否删除：0正常，1删除")
    private Integer isDel;

    @FieldDesc("创建人id")
    @Excel(name = "创建人id")
    private Long createById;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;


    @FieldDesc("离职时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "离职时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date resignationTime;

    @FieldDesc("部门名称")
    @Excel(name = "部门名称")
    private String deptName;

    @FieldDesc("岗位名称")
    @Excel(name = "岗位名称")
    private String stationName;


}
