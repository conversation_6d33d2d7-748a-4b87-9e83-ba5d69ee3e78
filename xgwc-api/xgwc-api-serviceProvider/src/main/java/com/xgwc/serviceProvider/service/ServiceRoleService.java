package com.xgwc.serviceProvider.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.ServiceRoleDto;
import com.xgwc.serviceProvider.entity.param.ServiceRoleParam;
import com.xgwc.serviceProvider.entity.vo.ServiceRoleVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kou<PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-04-28  11:55
 */
public interface ServiceRoleService {

    /**
     * 获取服务商角色列表
     * @param serviceRoleParam 服务商角色管理参数
     * @return 服务商角色列表
     */
    List<ServiceRoleVo> getServiceRoleList(ServiceRoleParam serviceRoleParam);

    /**
     * 保存服务商角色
     * @param serviceRoleDto 服务商角色参数
     * @return 保存结果
     */
    ApiResult saveServiceRole(ServiceRoleDto serviceRoleDto);

    /**
     * 获取服务商角色详情
     * @param roleId 角色ID
     * @return 角色详情
     */
    ApiResult getServiceRoleById(Long roleId);

    /**
     * 修改服务商角色
     * @param serviceRoleDto 服务商角色参数
     * @return 修改结果
     */
    ApiResult updateServiceRole(ServiceRoleDto serviceRoleDto);

    /**
     * 修改服务商角色状态
     * @param roleId 角色ID
     * @param status 状态
     * @return 修改结果
     */
    ApiResult updateStatusById(Integer roleId, Integer status);
}
