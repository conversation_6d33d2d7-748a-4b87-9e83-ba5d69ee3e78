package com.xgwc.serviceProvider.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.MarketEmployeesCountDto;
import com.xgwc.serviceProvider.entity.dto.MarketEmployeesDto;
import com.xgwc.serviceProvider.entity.vo.MarketEmployeesQueryVo;
import com.xgwc.serviceProvider.entity.vo.MarketEmployeesVo;

public interface MarketEmployeesService {
    /**
     * 查询员工档案
     * 
     * @param employeeId 员工档案主键
     * @return 员工档案
     */
    MarketEmployeesDto selectEmployeesByEmployeeId(Long employeeId);

    /**
     * 查询员工档案列表
     *
     * @param employees 员工档案
     * @return 员工档案集合
     */
    MarketEmployeesCountDto selectEmployeesList(MarketEmployeesQueryVo employees);

    /**
     * 新增员工档案
     *
     * @param employees 员工档案
     * @return 结果
     */
    ApiResult insertEmployees(MarketEmployeesVo employees);

    /**
     * 修改员工档案
     *
     * @param employees 员工档案
     * @return 结果
     */
    ApiResult updateEmployees(MarketEmployeesVo employees);

    /**
     * 员工申请离职
     *
     * @param employees 员工档案
     * @return 结果
     */
    int resignationsEmployees(MarketEmployeesVo employees);
}
