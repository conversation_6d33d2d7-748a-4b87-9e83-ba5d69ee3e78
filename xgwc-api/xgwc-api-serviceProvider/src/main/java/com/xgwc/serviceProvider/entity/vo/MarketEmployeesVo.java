package com.xgwc.serviceProvider.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.serviceProvider.entity.MarketEmpAttachments;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MarketEmployeesVo implements Serializable {

    @Serial
    private static final long serialVersionUID=1L;

    @FieldDesc("员工唯一标识")
    private Long employeeId;

    @FieldDesc("员工外键")
    private Long staffId;

    @FieldDesc("销售服务商外键")
    private Long marketId;

    @FieldDesc("员工账户外键")
    private Long accountId;

    @FieldDesc("员工附件外键")
    private Long attachmentId;

    @FieldDesc("员工姓名")
    private String name;

    @FieldDesc("用户性别（0男 1女 2未知）")
    private Long sex;

    @FieldDesc("民族")
    private Integer ethnicity;

    @FieldDesc("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthdate;

    @FieldDesc("学历")
    private Integer education;

    @FieldDesc("政治面貌")
    private Integer politicalStatus;

    @FieldDesc("婚姻状况")
    private Integer maritalStatus;

    @FieldDesc("身份证号")
    private String idNumber;

    @FieldDesc("电子邮箱")
    private String email;

    @FieldDesc("手机号码")
    private String phone;

    @FieldDesc("联系地址")
    private String address;

    @FieldDesc("紧急联系人姓名")
    private String emerName;

    @FieldDesc("紧急联系人电话")
    private String emerPhone;

    @FieldDesc("入职日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    @FieldDesc("合同到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    @FieldDesc("社保状态，（0-未买，1-已买，2-停保）")
    private Long socialStatus;

    @FieldDesc("购买社保日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date buySocialDate;

    @FieldDesc("是否转正，（0-否，1-是）")
    private Long probationStatus;

    @FieldDesc("转正日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date probationEndDate;

    @FieldDesc("年假天数")
    private Long annualLeaveDays;

    @FieldDesc("离职日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date resignationDate;

    /**
     * ===================================================附件=====================
     */
/*    @FieldDesc("附件类型（0-简历 1-劳动合同 2-证明材料）")
    private String attachmentType;

    @FieldDesc("文件名")
    private String fileName;

    @FieldDesc("文件路径")
    private String filePath;

    @FieldDesc("上传日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date uploadDate;*/
    @FieldDesc("附件上传")
    private List<MarketEmpAttachments> xgwcEmpAttachments;

    /**
     * ===================================================账户=====================
     */
    @FieldDesc("开户名")
    private String accountName;

    @FieldDesc("开户行")
    private String bankName;

    @FieldDesc("银行卡号")
    private String accountNumber;

    @FieldDesc("支付宝姓名")
    private String alipayName;

    @FieldDesc("支付宝账号")
    private String alipayAccount;

    public MarketEmployeesVo() {}
}
