package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class BillKefu {

private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;

    /** 账单开始时间 */
    private String billPeriodStart;

    /** 账单结束时间 */
    private String billPeriodEnd;

    /** 用户id */
    private Long userId;

    /** 订单数 */
    private Integer orderCount;

    /** 实收金额 */
    private BigDecimal realAmount;

    /** 退款金额 */
    private BigDecimal refundAmount;

    /** 佣金合计 */
    private BigDecimal totalCommission;

    /** 提成合计 */
    private BigDecimal percentageAmount;

    /** 基础工资 */
    private BigDecimal baseAmount;

    /** 提成追回 */
    private BigDecimal percentageBack;

    /** 税前金额 */
    private BigDecimal preTaxAmount;

    /** 加盟商id */
    private Long franchiseId;

    /** 确认时间 */
    private String confirmTime;

    /** 是否锁定 */
    private Long isLock;

    /** 出账时间 */
    private String billingTime;

    /** 是否正常：0正常，1非正常 */
    private Long status;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date updateTime;

}