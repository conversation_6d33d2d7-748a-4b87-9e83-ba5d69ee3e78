package com.xgwc.serviceProvider.service;

import java.util.List;
import com.xgwc.serviceProvider.entity.MarketStaffCooperative;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperativeVo;
import com.xgwc.serviceProvider.entity.dto.MarketStaffCooperativeDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperativeQueryVo;

public interface IMarketStaffCooperativeService {
    /**
     * 查询员工加盟
     * @param id 员工加盟主键
     * @return 员工加盟
     */
    MarketStaffCooperativeDto selectMarketStaffCooperativeById(Long id);

    /**
     * 查询员工加盟列表
     * @param marketStaffCooperative 员工加盟
     * @return 员工加盟集合
     */
    List<MarketStaffCooperativeDto> selectMarketStaffCooperativeList(MarketStaffCooperativeQueryVo marketStaffCooperative);

    /**
     * 新增员工加盟
     * @param marketStaffCooperative 员工加盟
     * @return 结果
     */
    int insertMarketStaffCooperative(MarketStaffCooperativeVo marketStaffCooperative);

}
