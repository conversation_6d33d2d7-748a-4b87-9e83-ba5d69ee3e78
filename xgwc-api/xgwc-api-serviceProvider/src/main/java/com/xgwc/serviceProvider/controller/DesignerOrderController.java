package com.xgwc.serviceProvider.controller;

import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.DesignerBill;
import com.xgwc.serviceProvider.entity.DesignerBillSub;
import com.xgwc.serviceProvider.entity.dto.DesignerBillDto;
import com.xgwc.serviceProvider.entity.dto.DesignerBillSubDto;
import com.xgwc.serviceProvider.entity.vo.DesignerBillSubVo;
import com.xgwc.serviceProvider.entity.vo.DesignerBillVo;
import com.xgwc.serviceProvider.service.DesignerOrderService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("bill/designer")
@RestController
public class DesignerOrderController extends BaseController {

    @Resource
    private DesignerOrderService designerOrderService;

    /**
     *
     * 获取设计师账单列表
     */
    @RequestMapping("getDesignerBillList")
    public ApiResult getDesignerBillList(@RequestBody DesignerBillVo designerBillVo) {
        startPage();
        List<DesignerBillDto> designerBills = designerOrderService.getDesignerBillList(designerBillVo);
        return ApiResult.ok(designerBills);
    }

    /**
     *
     * 获取设计师子账单列表
     */
    @RequestMapping("getDesignerBillSubList")
    public ApiResult getDesignerBillSubList(@RequestBody DesignerBillSubVo designerBillSubVo) {
        startPage();
        List<DesignerBillSubDto> designerBillSubs = designerOrderService.getDesignerBillSubList(designerBillSubVo);
        return ApiResult.ok(designerBillSubs);
    }
}
