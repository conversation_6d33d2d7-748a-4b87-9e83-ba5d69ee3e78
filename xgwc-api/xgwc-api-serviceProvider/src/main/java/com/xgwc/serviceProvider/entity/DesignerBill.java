package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DesignerBill {

    /** 账单ID */
    private Long billId;

    /** 账单周期结束日期 */
    private String billPeriodEnd;

    /** 账单周期开始日期 */
    private String billPeriodStart;

    /** 出账时间 */
    private String billingTime;

    /** 结算品牌商 */
    private Long brandOwnerId;

    /** 佣金追回 */
    private BigDecimal commissionBack;

    /** 确认时间 */
    private String confirmationTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private String createTime;

    /** 罚款金额 */
    private BigDecimal fineAmount;

    /** 管理员用户id */
    private Long managerUserId;

    /** 行修改时间 */
    private String modifyTime;

    /** 无票扣款 */
    private BigDecimal noInvoice;

    /** 账单订单数 */
    private Long orderCount;

    /** 结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败) */
    private Integer settlementStatus;

    /** 结算时间 */
    private String settlementTime;

    /** 设计师id */
    private Long stylistId;

    /**
     * 是否锁定：0锁定，1未锁定
     */
    private Integer isLock;

    /** 设计师名称 */
    private String stylistName;

    /** 账单佣金合计 */
    private BigDecimal totalCommission;

    /**
     * 应发金额
     */
    private BigDecimal payableAmount;

    /**
     * 实发金额
     */
    private BigDecimal realAmount;

    /**
     * 是否提供发票
     */
    private Integer isInvoice;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private String updateTime;

    /**
     * 锁定时间
     */
    private String lockTime;

    /**
     * 锁定人
     */
    private String lockUser;
}
