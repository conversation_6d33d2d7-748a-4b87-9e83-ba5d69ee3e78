package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class PaymentDays {

private static final long serialVersionUID=1L;

    /** 品牌商id */
    private Long brandId;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 周期：1一周一结,2半个月一结,3一个月一结,4一个季度一结 */
    private Integer cycleType;

    /** 类型：1设计师 2客服 */
    private Integer paymentType;

    /** 扣点 */
    private BigDecimal taxMoney;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;



}