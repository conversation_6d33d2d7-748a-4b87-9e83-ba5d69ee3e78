package com.xgwc.serviceProvider.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.annotation.FieldLabel;
import lombok.Data;

import java.util.Date;

@Data
public class ServiceStaffDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("姓名")
    @Excel(name = "姓名")
    private String name;

    @FieldDesc("花名")
    @Excel(name = "花名")
    private String stageName;

    @FieldDesc("部门id")
    @Excel(name = "部门id")
    @FieldLabel("部门")
    private Long deptId;

    @FieldDesc("岗位id")
    @Excel(name = "岗位id")
    @FieldLabel("岗位")
    private Long postId;

    @FieldDesc("角色权限")
    @Excel(name = "角色权限")
    @FieldLabel("角色权限")
    private String roleIds;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    @Excel(name = "工作性质：0全职，1外包，2兼职")
    @FieldLabel("工作性质")
    private Integer jobNature;

    @FieldDesc("状态：0在职，1离职")
    @Excel(name = "状态：0在职，1离职")
    @FieldLabel("状态")
    private Integer status;

    @FieldDesc("部门负责人：0是，1否")
    @Excel(name = "部门负责人：0是，1否")
    @FieldLabel("部门负责人")
    private Integer isPrincipal;

    @FieldDesc("部门助理：0是，1否")
    private Integer isAssistant;

    @FieldDesc("部门排班：0是，1否")
    private Integer isSchedule;

    @FieldDesc("直属上级")
    @Excel(name = "直属上级")
    private Long superior;

    @FieldDesc("绑定状态：0未绑，1已绑")
    @Excel(name = "绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("绑定的用户id")
    @Excel(name = "绑定的用户id")
    private Long bindUserId;

    @FieldDesc("登录手机号")
    @Excel(name = "登录手机号")
    private String loginPhone;

    @FieldDesc("服务商id")
    @Excel(name = "服务商id")
    private Long serviceOwnerId;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("部门名称")
    @Excel(name = "部门名称")
    private String deptName;

    @FieldDesc("岗位名称")
    @Excel(name = "岗位名称")
    private String stationName;

    @FieldDesc("离职时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date resignationTime;
}
