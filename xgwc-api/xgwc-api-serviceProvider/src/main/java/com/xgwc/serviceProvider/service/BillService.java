package com.xgwc.serviceProvider.service;

import com.xgwc.serviceProvider.entity.dto.BillBrandDetailDto;
import com.xgwc.serviceProvider.entity.dto.BillBrandDto;
import com.xgwc.serviceProvider.entity.vo.BillVo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

public interface BillService {

    /**
     * 账单品牌列表
     * @return 品牌下账单
     */
    List<BillBrandDto> getBillBrandList(BillVo billVo);

    /**
     * 账单服务商列表
     * @return 品牌下账单
     */
    List<BillBrandDto> getBillServiceList(BillVo billVo);

    /**
     * 获取母账单详情
     * @param billVo 参数
     * @return 详情
     */
    BillBrandDetailDto getBillDetail(BillVo billVo);

    /**
     * 获取母账单详情
     * @param billVo 参数
     * @return 详情
     */
    BillBrandDetailDto getServiceBillDetail(BillVo billVo);

    /**
     * 导出账单
     *
     * @param response response
     * @param billId   账单id
     * @param isFlag   0-品牌商 1-服务商
     */
    void downloadBill(HttpServletResponse response, Long billId, Integer isFlag);

    /**
     * 锁定账单
     * @param brandId 品牌商ID
     */
    void lockBill(Long brandId);
}
