package com.xgwc.serviceProvider.entity;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 线下收款表(finance_offline_payments)实体类
 */
@Data
public class FinanceOfflinePayments {

private static final long serialVersionUID=1L;

    /** 实收金额 */
    private BigDecimal amount;

    /** 归档结果:0-未锁定 1-已锁定 */
    private Integer archivalStatus;

    /** 收款主体id */
    private String companyInfoId;

    /** 谈单人 */
    private String contactPerson;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 客户ID */
    private String customerId;

    /** 差额 */
    private BigDecimal differenceAmount;

    /** 加盟商id */
    private Long franchiseId;

    /** 自增主键 */
    private Long id;

    /** 行更新时间 */
    private Date modifyTime;

    /** 订单主键 */
    private Long orderId;

    /** 收款金额 */
    private BigDecimal paymentAmount;

    /** 付款流水号 */
    private String paymentFlowNumber;

    /** 支付方式:1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫  */
    private Integer paymentMethod;

    /** 收款编号 */
    private String paymentNumber;

    /** 付款截图 */
    private String paymentScreenshot;

    /** 收款状态:0-财务未收 1-财务已收 */
    private Integer paymentStatus;

    /** 备注 */
    private String remarks;

    /** 店铺id */
    private Long shopId;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /**
     * 订单支付流水id
     */
    private Long payId;

    /** 付款码主体（字典主键） */
    private Long paymentCodeBody;


}