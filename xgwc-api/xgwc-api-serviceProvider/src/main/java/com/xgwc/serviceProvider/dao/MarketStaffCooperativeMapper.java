package com.xgwc.serviceProvider.dao;

import java.util.List;
import com.xgwc.serviceProvider.entity.MarketStaffCooperative;
import com.xgwc.serviceProvider.entity.dto.MarketStaffCooperativeDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperativeQueryVo;
import io.lettuce.core.dynamic.annotation.Param;


public interface MarketStaffCooperativeMapper {
    /**
     * 查询员工加盟
     * @param id 员工加盟主键
     * @return 员工加盟
     */
    MarketStaffCooperativeDto selectMarketStaffCooperativeById(Long id);

    /**
     * 查询员工加盟列表
     * @param marketStaffCooperative 员工加盟
     * @return 员工加盟集合
     */
    List<MarketStaffCooperativeDto> selectMarketStaffCooperativeList(MarketStaffCooperativeQueryVo marketStaffCooperative);

    /**
     * 新增员工加盟
     * @param marketStaffCooperative 员工加盟
     * @return 结果
     */
    int insertMarketStaffCooperative(MarketStaffCooperative marketStaffCooperative);

    /**
     * 修改员工加盟
     * @param marketStaffCooperative 员工加盟
     * @return 结果
     */
    int updateMarketStaffCooperative(MarketStaffCooperative marketStaffCooperative);

    /**
     * 根据员工id和加盟商id查询员工加盟
     * @param staffId 员工id
     * @param franchiseId 加盟商id
     * @return 员工加盟
     */
    MarketStaffCooperativeDto findByStaffIdAndFranchiseId(@Param("staffId") Long staffId, @Param("franchiseId") Long franchiseId);

}
