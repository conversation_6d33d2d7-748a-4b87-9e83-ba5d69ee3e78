package com.xgwc.serviceProvider.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.dao.CommissionRecoveredMapper;
import com.xgwc.serviceProvider.dao.ServiceAuthorizeMapper;
import com.xgwc.serviceProvider.entity.dto.CommissionBackDetail;
import com.xgwc.serviceProvider.entity.dto.CommissionBackDto;
import com.xgwc.serviceProvider.entity.dto.CommissionSumDto;
import com.xgwc.serviceProvider.entity.vo.CommissionBackVo;
import com.xgwc.serviceProvider.service.CommissionRecoveredService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.serviceProvider.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-07-24  09:26
 */

/**
 * 已追回佣金
 */

@Service
@Slf4j
public class CommissionRecoveredServiceImpl extends BaseController implements CommissionRecoveredService {

    @Resource
    private CommissionRecoveredMapper commissionRecoveredMapper;

    @Resource
    private ServiceAuthorizeMapper serviceAuthorizeMapper;

    /**
     * 品牌商已追回佣金列表
     */
    @Override
    public CommissionSumDto getBrandCommissionRecoveredList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = getBrandId(sysUser);
        if (brandId == null) return null;
        commissionBackVo.setBrandIds(Collections.singletonList(brandId));
        return getCommissionRecoveredList(commissionBackVo);
    }

    /**
     * 服务商已追回佣金列表
     */
    @Override
    public CommissionSumDto getServiceCommissionRecoveredList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        List<Long> brandIds = getServiceId(sysUser);
        if (!CollectionUtils.isEmpty(brandIds)){
            commissionBackVo.setBrandIds(brandIds);
            return getCommissionRecoveredList(commissionBackVo);
        }
        return null;
    }

    /**
     * 品牌商已追回佣金详情
     */
    @Override
    public List<CommissionBackDto> getBrandCommissionRecoveredDetailList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = getBrandIdDetail(commissionBackVo,sysUser);
        if (brandId == null) return null;
        return getCommissionBackDtos(commissionBackVo);
    }

    /**
     * 服务商已追回佣金详情
     */
    @Override
    public List<CommissionBackDto> getServiceCommissionRecoveredDetailList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        List<Long> brandIds = getServiceIdDetail(commissionBackVo,sysUser);
        if (!CollectionUtils.isEmpty(brandIds)){
            return getCommissionBackDtos(commissionBackVo);
        }
        return List.of();
    }

    /**
     * 设计师已追回佣金详情
     */
    @Override
    public List<CommissionBackDto> getDesignerCommissionRecoveredDetailList(CommissionBackVo commissionBackVo) {
        return getCommissionBackDtos(commissionBackVo);
    }


    @Nullable
    private static Long getBrandId(SysUser sysUser) {
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商不能获取品牌下的佣金追回:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return brandId;
    }

    @Nullable
    private List<Long> getServiceId(SysUser sysUser) {
        Long serviceId = sysUser.getServiceId();
        if(serviceId == null){
            log.error("非服务商不能获取服务下的设计师账单:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
    }

    @Nullable
    private List<Long> getServiceIdDetail(CommissionBackVo commissionBackVo, SysUser sysUser) {
        Long serviceId = sysUser.getServiceId();
        if(serviceId == null){
            log.error("非服务商不能获取未授权品牌商下的佣金追回详情:{}, {}", JSONObject.toJSONString(commissionBackVo), JSONObject.toJSONString(sysUser));
            return null;
        }
        List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if(brandIds == null || brandIds.isEmpty()){
            log.error("服务商下没有授权的品牌商，不能获取未授权品牌商下的佣金追回详情:{}, {}", JSONObject.toJSONString(commissionBackVo), JSONObject.toJSONString(sysUser));
            return null;
        }
        return brandIds;
    }

    @Nullable
    private static Long getBrandIdDetail(CommissionBackVo commissionBackVo, SysUser sysUser) {
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商不能获取品牌商下的佣金追回详情:{}, {}", JSONObject.toJSONString(commissionBackVo), JSONObject.toJSONString(sysUser));
            return null;
        }
        return brandId;
    }


    @Nullable
    private CommissionSumDto getCommissionRecoveredList(CommissionBackVo commissionBackVo) {
        List<CommissionBackDetail> commissionRecoveredList = commissionRecoveredMapper.getBrandCommissionRecoveredList(commissionBackVo);
        if (!CollectionUtils.isEmpty(commissionRecoveredList)) {
            commissionRecoveredList.forEach(commissionBackDetail -> {
                String designerPhone = commissionBackDetail.getDesignerPhone();
                commissionBackDetail.setDesignerPhone(ParamDecryptUtil.decryptParam(designerPhone,ParamDecryptUtil.PHONE_KEY));
            });
            return getCommissionSumDto(commissionRecoveredList);
        }
        return null;
    }

    @Nullable
    private List<CommissionBackDto> getCommissionBackDtos(CommissionBackVo commissionBackVo) {
        List<CommissionBackDto> commissionBackDtos = commissionRecoveredMapper.selectCommissionRecoveredDetail(commissionBackVo);
        if (!CollectionUtils.isEmpty(commissionBackDtos)) {
            commissionBackDtos.forEach(commissionBackDto -> {
                commissionBackDto.setNeedBackCommission(commissionBackDto.getMoney().subtract(commissionBackDto.getNowMoney()));
            });
        }
        return commissionBackDtos;
    }

    @NotNull
    private CommissionSumDto getCommissionSumDto(List<CommissionBackDetail> commissionBackDtos) {
        CommissionSumDto commissionSumDto = new CommissionSumDto();
        BigDecimal sumNeedBackCommission = sumNeedBackCommission(commissionBackDtos);
        BigDecimal sumCommissionBacked = sumCommissionBacked(commissionBackDtos);
        BigDecimal sumCommissionBackRemaining = sumCommissionBackRemaining(commissionBackDtos);
        commissionSumDto.setNeedBackCommission(sumNeedBackCommission);
        commissionSumDto.setRecoveredCommission(sumCommissionBacked);
        commissionSumDto.setToBeRecoveredCommission(sumCommissionBackRemaining);
        commissionSumDto.setList(getDataTable(commissionBackDtos));
        return commissionSumDto;
    }


    /**
     * 计算需追回佣金（needBackCommission）的总和
     */
    public static BigDecimal sumNeedBackCommission(List<CommissionBackDetail> commissionBackDtos) {
        return commissionBackDtos.stream()
                .map(CommissionBackDetail::getCommissionBack)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算已追回佣金（commissionBacked）的总和
     */
    public static BigDecimal sumCommissionBacked(List<CommissionBackDetail> commissionBackDtos) {
        return commissionBackDtos.stream()
                .map(CommissionBackDetail::getBackMoney)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算剩余佣金追回（commissionBackRemaining）的总和
     */
    public static BigDecimal sumCommissionBackRemaining(List<CommissionBackDetail> commissionBackDtos) {
        return commissionBackDtos.stream()
                .map(CommissionBackDetail:: getBackRemaining)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
