package com.xgwc.serviceProvider.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.serviceProvider.dao.MarketStaffCooperativeMapper;
import com.xgwc.serviceProvider.service.IMarketStaffCooperativeService;
import com.xgwc.serviceProvider.entity.MarketStaffCooperative;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperativeVo;
import com.xgwc.serviceProvider.entity.dto.MarketStaffCooperativeDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperativeQueryVo;


@Service
public class MarketStaffCooperativeServiceImpl implements IMarketStaffCooperativeService {
    @Resource
    private MarketStaffCooperativeMapper marketStaffCooperativeMapper;

    /**
     * 查询员工加盟
     * @param id 员工加盟主键
     * @return 员工加盟
     */
    @Override
    public MarketStaffCooperativeDto selectMarketStaffCooperativeById(Long id) {
        return marketStaffCooperativeMapper.selectMarketStaffCooperativeById(id);
    }

    /**
     * 查询员工加盟列表
     * @param marketStaffCooperative 员工加盟
     * @return 员工加盟
     */
    @Override
    public List<MarketStaffCooperativeDto> selectMarketStaffCooperativeList(MarketStaffCooperativeQueryVo marketStaffCooperative) {
        return marketStaffCooperativeMapper.selectMarketStaffCooperativeList(marketStaffCooperative);
    }

    /**
     * 新增员工加盟
     * @param dto 员工加盟
     * @return 结果
     */
    @Override
    public int insertMarketStaffCooperative(MarketStaffCooperativeVo dto) {
        MarketStaffCooperative marketStaffCooperative = BeanUtil.copyProperties(dto, MarketStaffCooperative.class);
        marketStaffCooperative.setCreateTime(DateUtils.getNowDate());
        return marketStaffCooperativeMapper.insertMarketStaffCooperative(marketStaffCooperative);
    }

}
