package com.xgwc.serviceProvider.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.serviceProvider.entity.dto
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-18  18:08
 */

/**
 * 导出打款记录
 */
@Data
public class BillPaymentRecord {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 打款Excel名称
     */
    private String excelName;

    /**
     * 品牌商ID
     */
    private Long brandId;

    /**
     * 打款人
     */
    private String createBy;

    /**
     * 打款时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 账单ID
     */
    private Long billId;

    @Excel(name = "子账单ID", sort = 1)
    private Long subBillId;

    @Excel(name = "应发金额", sort = 2)
    private BigDecimal amount;

    @Excel(name = "支付宝姓名", sort = 3)
    private String alipayName;

    @Excel(name = "支付宝账号", sort = 4)
    private String alipayAccount;

    @Excel(name = "打款结果", sort = 5)
    private String paymentResult;

    @Excel(name = "失败原因", sort = 6)
    private String failReason;

    @Excel(name = "匹配结果", sort = 7)
    private String matchResult;
}
