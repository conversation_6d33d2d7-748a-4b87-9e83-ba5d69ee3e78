package com.xgwc.serviceProvider.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaymentDaysDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌商id")
    @Excel(name = "品牌商id")
    private Long brandId;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("周期：1一周一结,2半个月一结,3一个月一结,4一个季度一结")
    @Excel(name = "周期：1一周一结,2半个月一结,3一个月一结,4一个季度一结")
    private Integer cycleType;

    @FieldDesc("类型：1设计师 2客服")
    @Excel(name = "类型：1设计师 2客服")
    private Integer paymentType;

    @FieldDesc("扣点")
    @Excel(name = "扣点")
    private BigDecimal taxMoney;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("brandId",getBrandId())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("cycleType",getCycleType())
            .append("paymentType",getPaymentType())
            .append("taxMoney",getTaxMoney())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
