package com.xgwc.serviceProvider.service;


import com.xgwc.serviceProvider.entity.dto.PaymentDaysDto;
import com.xgwc.serviceProvider.entity.vo.PaymentDaysVo;

import java.util.List;

public interface IPaymentDaysService  {
    /**
     * 查询账期管理
     * 
     * @return 账期管理
     */
    public List<PaymentDaysDto>  selectPaymentDaysByBrandId();


    /**
     * 修改账期管理
     * 
     * @param paymentDays 账期管理
     * @return 结果
     */
    public int updatePaymentDays(PaymentDaysVo paymentDays);

}
