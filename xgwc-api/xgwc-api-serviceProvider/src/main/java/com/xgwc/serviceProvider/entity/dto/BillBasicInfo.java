package com.xgwc.serviceProvider.entity.dto;

import com.xgwc.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

// 基本信息类
@Data
public class BillBasicInfo {

    @Excel(name = "所属品牌商", sort = 0)
    private String brandName;

    @Excel(name = "结算主体", sort = 1)
    private String companyInfoName;

    @Excel(name = "结算品牌商", sort = 1)
    private String settleBrandName;
    
    @Excel(name = "结算设计师", sort = 2)
    private String stylistName;
    
    @Excel(name = "账单周期", sort = 3)
    private String billCycle;

    @Excel(name = "母账单ID", sort = 4)
    private Long billId;
    
    @Excel(name = "总金额", sort = 5)
    private BigDecimal totalAmount;
}