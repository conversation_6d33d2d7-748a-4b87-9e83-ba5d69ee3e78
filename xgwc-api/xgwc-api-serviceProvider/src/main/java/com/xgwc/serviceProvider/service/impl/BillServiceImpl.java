package com.xgwc.serviceProvider.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.*;
import com.xgwc.serviceProvider.dao.BillOrderMapper;
import com.xgwc.serviceProvider.dao.DesignerBillMapper;
import com.xgwc.serviceProvider.dao.ServiceAuthorizeMapper;
import com.xgwc.serviceProvider.dao.XgwcOrderMapper;
import com.xgwc.serviceProvider.entity.DesignerBill;
import com.xgwc.serviceProvider.entity.dto.*;
import com.xgwc.serviceProvider.entity.vo.BillVo;
import com.xgwc.serviceProvider.service.BillService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillServiceImpl implements BillService {

    @Resource
    private DesignerBillMapper designerBillMapper;

    @Resource
    private BillOrderMapper billOrderMapper;

    @Resource
    private ServiceAuthorizeMapper serviceAuthorizeMapper;

    @Resource
    private XgwcOrderMapper xgwcOrderMapper;

    @Override
    public List<BillBrandDto> getBillBrandList(BillVo billVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        initBillVo(billVo);
        Long brandId = sysUser.getBrandId();
        if (brandId == null) {
            log.error("非品牌商不能获取品牌下的设计师账单:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        billVo.setBrandIds(Collections.singletonList(brandId));
        return getBillBrandListByBillVo(billVo);
    }

    @Override
    public List<BillBrandDto> getBillServiceList(BillVo billVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        initBillVo(billVo);
        Long serviceId = sysUser.getServiceId();
        if (serviceId == null) {
            log.error("非服务商不能获取服务下的设计师账单:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if (brandIds != null && !brandIds.isEmpty()) {
            billVo.setBrandIds(brandIds);
            return getBillBrandListByBillVo(billVo);
        }
        return null;
    }

    @Override
    public BillBrandDetailDto getBillDetail(BillVo billVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if (brandId == null) {
            log.error("非品牌商不能获取品牌下的设计师账单详情:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        DesignerBillDto designerBillDto = designerBillMapper.selectDesignerBillDtoByBillId(billVo.getBillId());
        if (designerBillDto == null || !Objects.equals(designerBillDto.getBrandOwnerId(), brandId)) {
            log.error("非当前品牌商账单，禁止查询:{},查询账单id:{}", JSONObject.toJSON(sysUser), designerBillDto);
            return null;
        }
        BillBrandDetailDto billBrandDetailDto = buildBillBrandDetailDto(designerBillDto);
        List<BillBrandOrderDetail> billBrandOrderDetails = billOrderMapper.getBillOrderDetailByBillId(billVo.getBillId());
        billBrandDetailDto.setOrderList(billBrandOrderDetails);
        return billBrandDetailDto;
    }

    @Override
    public BillBrandDetailDto getServiceBillDetail(BillVo billVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if (serviceId == null) {
            log.error("非服务商不能获取服务商下的设计师账单详情:{}", JSONObject.toJSON(sysUser));
            return null;
        }
        List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if (brandIds == null || brandIds.isEmpty()) {
            log.error("服务商没有授权的品牌商，禁止查询:{},{}", JSONObject.toJSONString(sysUser), JSONObject.toJSONString(billVo));
            return null;
        }
        DesignerBillDto designerBillDto = designerBillMapper.selectDesignerBillDtoByBillId(billVo.getBillId());
        if (designerBillDto == null || !brandIds.contains(designerBillDto.getBrandOwnerId())) {
            log.error("非当前服务商账单，禁止查询:{},查询子账单id:{}", JSONObject.toJSONString(sysUser), JSONObject.toJSONString(billVo));
            return null;
        }
        BillBrandDetailDto billBrandDetailDto = buildBillBrandDetailDto(designerBillDto);
        List<BillBrandOrderDetail> billBrandOrderDetails = billOrderMapper.getBillOrderDetailByBillId(billVo.getBillId());
        billBrandDetailDto.setOrderList(billBrandOrderDetails);
        return billBrandDetailDto;
    }

    @Override
    public void downloadBill(HttpServletResponse response, Long billId, Integer isFlag) {
        // 参数校验
        validateDownloadParams(billId, isFlag);

        // 根据 isFlag 获取不同的账单详情
        BillVo billVo = new BillVo();
        billVo.setBillId(billId);
        BillBrandDetailDto billBrandDetailDto = (isFlag == 0)
                ? getBillDetail(billVo)
                : getServiceBillDetail(billVo);

        // 构建账单基本信息
        BillBasicInfo billBasicInfo = buildBillBasicInfo(billBrandDetailDto);

        // 构建账单详情列表
        List<BillDetail> billDetails = buildBillDetails(billBrandDetailDto, billId);

        // 导出Excel
        exportBillToExcel(billBasicInfo, billDetails, response, billId);
    }

    @Override
    public void lockBill(Long brandId) {
        log.info("开始锁定账单, 品牌商ID:{}", brandId);
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if (serviceId == null) {
            log.error("非服务商不能锁定账单:{}", JSONObject.toJSONString(sysUser));
            return;
        }
        List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if(brandIds != null && !brandIds.isEmpty() && brandIds.contains(brandId)) {
            lockBill(brandId, sysUser);
        }else{
            log.error("该服务商没有权限操作,服务商信息:{}", JSONObject.toJSONString(sysUser));
        }
    }

    /**
     * 锁定账单
     */
    @Transactional(rollbackFor = Exception.class)
    protected void lockBill(Long brandId, SysUser sysUser){
        //第一步锁定账单
        List<Long> billIds = designerBillMapper.getBillIdsByToBeLocked(brandId);
        if(billIds != null && !billIds.isEmpty()) {
            log.info("品牌id:{}, 锁定账单数:{}", JSONObject.toJSONString(sysUser), billIds.size());
            designerBillMapper.updateLockedBills(billIds, sysUser.getUserName());
            //第二步锁定订单
            List<Long> orderIds = billOrderMapper.getOrderIdsByBillIds(billIds);
            if(orderIds != null && !orderIds.isEmpty()){
                log.info("品牌商ID:{}, 一共锁定订单数:{}", brandId, orderIds.size());
                xgwcOrderMapper.lockOrders(orderIds, sysUser.getUserName());
            }
        }

    }

    // 参数校验
    private void validateDownloadParams(Long billId, Integer isFlag) {
        if (billId == null || billId <= 0) {
            log.error("账单ID无效, billId = {}", billId);
            throw new ApiException("下载账单失败，请稍后重试");
        }

        if (isFlag == null || isFlag < 0 || isFlag > 1) {
            log.error("账单标识无效, billId={}, isFlag={}", billId, isFlag);
            throw new ApiException("下载账单失败，请稍后重试");
        }
    }

    // 构建账单基本信息
    private BillBasicInfo buildBillBasicInfo(BillBrandDetailDto billBrandDetailDto) {
        BillBasicInfo billBasicInfo = new BillBasicInfo();
        billBasicInfo.setBillCycle(
                billBrandDetailDto.getBillStart() + "-" + billBrandDetailDto.getBillEnd()
        );
        billBasicInfo.setSettleBrandName(billBrandDetailDto.getBrandName());
        billBasicInfo.setStylistName(billBrandDetailDto.getDesignerName());
        billBasicInfo.setTotalAmount(billBrandDetailDto.getTotalCommission());
        return billBasicInfo;
    }

    // 构建账单详情列表
    private List<BillDetail> buildBillDetails(BillBrandDetailDto billBrandDetailDto, Long billId) {
        return billBrandDetailDto.getOrderList().stream().map(detail -> {
            BillDetail billDetail = new BillDetail();
            billDetail.setOrderNo(detail.getOrderNo());
            billDetail.setArchiveTime(detail.getArchiveTime());
            billDetail.setFranchiseName(detail.getFranchiseName());
            // TODO 获取订单完成时间
            billDetail.setFinishedTime(null);
            billDetail.setDealTime(detail.getDealTime());
            billDetail.setMoney(detail.getNowMoney());
            billDetail.setTaobaoId(detail.getCustomerNo());
            billDetail.setDesignerBusiness(detail.getBusinessName());
            billDetail.setBillId(billId);
            return billDetail;
        }).collect(Collectors.toList());
    }

    // 导出Excel
    private void exportBillToExcel(BillBasicInfo billBasicInfo, List<BillDetail> billDetails,
                                   HttpServletResponse response, Long billId) {
        try {
            ExcelExportUtil.exportExcelToResponse(
                    "账单",
                    "账单详情信息",
                    billBasicInfo,
                    billDetails,
                    response,
                    "账单"
            );
        } catch (IOException e) {
            log.error("导出对账账单失败，billId: {}", billId, e);
            throw new ApiException("下载账单失败，请稍后重试");
        }
    }

    private BillBrandDetailDto buildBillBrandDetailDto(DesignerBillDto designerBillDto) {
        BillBrandDetailDto billBrandDetailDto = new BillBrandDetailDto();
        billBrandDetailDto.setBillStart(designerBillDto.getBillPeriodStart());
        billBrandDetailDto.setDesignerName(designerBillDto.getStylistName());
        billBrandDetailDto.setDesignerId(designerBillDto.getStylistId());
        billBrandDetailDto.setBillEnd(designerBillDto.getBillPeriodEnd());
        billBrandDetailDto.setBillId(designerBillDto.getBillId());
        billBrandDetailDto.setTotalCommission(designerBillDto.getTotalCommission());
        billBrandDetailDto.setBrandName(designerBillDto.getBrandName());
        billBrandDetailDto.setNoInvoice(designerBillDto.getNoInvoice());
        billBrandDetailDto.setCommissionBack(designerBillDto.getCommissionBack());
        billBrandDetailDto.setFineAmount(designerBillDto.getFineAmount());
        billBrandDetailDto.setPayableAmount(designerBillDto.getPayableAmount());
        return billBrandDetailDto;
    }

    private List<BillBrandDto> getBillBrandListByBillVo(BillVo billVo) {
        List<BillBrandDto> billBrandDtos = designerBillMapper.selectBillBrandDtoList(billVo);
        if (billBrandDtos != null || !billBrandDtos.isEmpty()) {
            billBrandDtos.forEach(x -> {
                x.setPhone(ParamDecryptUtil.decryptParam(x.getPhone(), ParamDecryptUtil.PHONE_KEY));
                x.setZfbName(ParamDecryptUtil.decryptParam(x.getZfbName(), ParamDecryptUtil.DESIGNER_KEY));
                x.setZfbAccount(ParamDecryptUtil.decryptParam(x.getZfbAccount(), ParamDecryptUtil.DESIGNER_KEY));
            });
        }
        return billBrandDtos;
    }

    /**
     * 初始化参数
     */
    private void initBillVo(BillVo billVo) {
        if (StringUtils.isNotEmpty(billVo.getPhone())) {
            billVo.setPhone(ParamDecryptUtil.encrypt(billVo.getPhone(), ParamDecryptUtil.DESIGNER_KEY));
        }
    }

}
