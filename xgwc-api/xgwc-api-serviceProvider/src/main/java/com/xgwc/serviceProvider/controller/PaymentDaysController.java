package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.PaymentDaysDto;
import com.xgwc.serviceProvider.entity.vo.PaymentDaysVo;
import com.xgwc.serviceProvider.service.IPaymentDaysService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;




@RestController
@RequestMapping("/PaymentDays/PaymentDays")
public class PaymentDaysController extends BaseController {
    @Autowired
    private IPaymentDaysService paymentDaysService;


    /**
     * 获取账期管理详细信息
     */
    @MethodDesc("获取账期管理详细信息")
    @PreAuthorize("@ss.hasPermission('PaymentDays:PaymentDays:query')")
    @GetMapping
    public ApiResult<PaymentDaysDto> getInfo() {
        return success(paymentDaysService.selectPaymentDaysByBrandId());
    }

    /**
     * 修改账期管理
     */
    @MethodDesc("编辑账期管理")
    @PreAuthorize("@ss.hasPermission('PaymentDays:PaymentDays:edit')")
    @Log(title = "账期管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody PaymentDaysVo paymentDays) {
        return toAjax(paymentDaysService.updatePaymentDays(paymentDays));
    }

}
