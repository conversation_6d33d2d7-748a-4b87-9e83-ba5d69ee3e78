package com.xgwc.serviceProvider.entity.vo;

import lombok.Data;

import java.util.List;

@Data
public class BillVo {

    /**
     * 子账单id
     */
    private Long subBillId;

    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 设计师名称
     */
    private String designerName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)
     */
    private Integer settlementStatus;

    /**
     * 账单开始
     */
    private String billStart;

    /**
     * 账单结束
     */
    private String billEnd;

    /**
     * 品牌商ID
     */
    private List<Long> brandIds;

    /**
     * 品牌商ID
     */
    private Long brandId;

    /**
     * 是否锁定：0锁定，1未锁定
     */
    private Integer isLock;
}
