package com.xgwc.serviceProvider.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CommissionBackDetail {

    /**
     * id
     */
    @FieldDesc("id")
    private Long id;

    /**
     * 账单ID
     */
    @FieldDesc("账单ID")
    private Long billId;

    /**
     * 子账单ID
     */
    @FieldDesc("子账单ID")
    private Long subBillId;

    /**
     * 账单开始
     */
    @FieldDesc("账单开始")
    private String billStart;

    /**
     * 账单结束
     */
    @FieldDesc("账单结束")
    private String billEnd;

    /**
     * 品牌商ID
     */
    @FieldDesc("品牌商ID")
    private Long brandId;

    /**
     * 加盟商ID
     */
    @FieldDesc("加盟商ID")
    private Long franchiseId;

    /**
     * 加盟商名称
     */
    @FieldDesc("加盟商名称")
    private String franchiseName;

    /**
     * 品牌商名称
     */
    @FieldDesc("品牌商名称")
    private String brandName;

    /**
     * 设计师名称
     */
    @FieldDesc("设计师名称")
    private String designerName;

    /**
     * 设计师手机号
     */
    @FieldDesc("设计师手机号")
    private String designerPhone;

    /**
     * 设计师ID
     */
    @FieldDesc("设计师ID")
    private Long designerId;

    /**
     * 佣金
     */
    @FieldDesc("佣金")
    private BigDecimal money;

    /**
     * 现在佣金
     */
    @FieldDesc("现在佣金")
    private BigDecimal commissionBack;

    /**
     * 追回时间
     */
    @FieldDesc("追回时间")
    private String backTime;

    /**
     * 已追回佣金
     */
    @FieldDesc("已追回佣金")
    private BigDecimal backMoney;

    /**
     * 剩余佣金追回
     */
    @FieldDesc("剩余佣金追回")
    private BigDecimal backRemaining;

}
