package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class MarketStaffCooperative {

private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;

    /** 员工id */
    private Long staffId;

    /** 销售服务商id */
    private Long serviceOwnerId;

    /** 加盟商id */
    private Long franchiseId;

    /** 加盟商员工id */
    private Long franchiseStaffId;

    /** 是否删除：0正常，1删除 */
    private Long isDel;

    /** 创建人id */
    private Long createById;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人id */
    private Long updateById;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;



}