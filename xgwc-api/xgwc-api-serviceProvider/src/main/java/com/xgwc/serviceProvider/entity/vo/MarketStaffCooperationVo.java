package com.xgwc.serviceProvider.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MarketStaffCooperationVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @NotNull(message = "员工不能为空")
    private Long id;

    @FieldDesc("加盟商id")
    @NotNull(message = "加盟商id不能为空")
    private Long franchiseId;

}
