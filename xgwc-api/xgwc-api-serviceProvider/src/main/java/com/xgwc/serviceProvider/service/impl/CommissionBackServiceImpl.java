package com.xgwc.serviceProvider.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.dao.BillOrderModifyMapper;
import com.xgwc.serviceProvider.dao.ServiceAuthorizeMapper;
import com.xgwc.serviceProvider.entity.dto.CommissionBackDetail;
import com.xgwc.serviceProvider.entity.dto.CommissionBackDto;
import com.xgwc.serviceProvider.entity.dto.CommissionSumDto;
import com.xgwc.serviceProvider.entity.vo.CommissionBackVo;
import com.xgwc.serviceProvider.service.CommissionBackService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 佣金退回
 */
@Slf4j
@Service
public class CommissionBackServiceImpl extends BaseController implements CommissionBackService {

    @Resource
    private BillOrderModifyMapper billOrderModifyMapper;

    @Resource
    private ServiceAuthorizeMapper serviceAuthorizeMapper;

    /**
     * 获取品牌商佣金退回列表
     *
     * @param commissionBackVo 查询参数
     * @return 佣金退回列表
     */
    @Override
    public CommissionSumDto getBrandCommissionBackList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = getBrandId(sysUser);
        if (brandId == null) return null;
        //限制查询范围
        commissionBackVo.setBrandIds(Collections.singletonList(brandId));
        return getCommissionBackList(commissionBackVo);
    }

    /**
     * 获取服务商佣金退回列表
     *
     * @param commissionBackVo 获取参数
     * @return 佣金退回列表
     */
    @Override
    public CommissionSumDto getServiceCommissionBackList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        List<Long> brandIds = getServiceId(sysUser);
        if(brandIds != null && !brandIds.isEmpty()){
            commissionBackVo.setBrandIds(brandIds);
            return getCommissionBackList(commissionBackVo);
        }
        return null;
    }

    /**
     * 获取设计师佣金退回列表
     *
     * @param commissionBackVo 获取参数
     * @return 佣金退回列表
     */
    @Override
    public CommissionSumDto getDesignerCommissionBackList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        commissionBackVo.setDesignerId(sysUser.getUserId());
        List<CommissionBackDto> designerCommissionBackList = billOrderModifyMapper.getDesignerCommissionBackList(commissionBackVo);
        if(!CollectionUtils.isEmpty(designerCommissionBackList)){
            return getCommissionSumDto(designerCommissionBackList);
        }
        return null;
    }

    /**
     * 获取设计师佣金退回明细
     * @param commissionBackVo 获取参数
     * @return 佣金退回明细
     */
    @Override
    public List<CommissionBackDetail> getDesignerCommissionBackDetailList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = new SysUser();//SecurityUtils.getSysUser();
        sysUser.setUserId(199L);
        List<CommissionBackDetail> commissionBackDetails = getCommissionBackDetailList(commissionBackVo);
        if(commissionBackDetails != null && !commissionBackDetails.isEmpty()){
            CommissionBackDetail commissionBackDetail = commissionBackDetails.get(0);
            if(!commissionBackDetail.getDesignerId().equals(sysUser.getUserId())){
                log.error("设计师查看佣金明细,非本人明细不能查看:{}, 查看人:{}", JSONObject.toJSONString(commissionBackDetail), JSONObject.toJSONString(sysUser));
                return null;
            }
        }
        return commissionBackDetails;
    }

    /**
     * 获取服务商佣金退回明细
     * @param commissionBackVo 获取参数
     * @return 佣金退回明细
     */
    @Override
    public List<CommissionBackDetail> getServiceCommissionBackDetailList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        List<Long> brandIds = getServiceIdDetail(commissionBackVo, sysUser);
        if (brandIds == null) return null;
        List<CommissionBackDetail> commissionBackDetails = getCommissionBackDetailList(commissionBackVo);
        if(commissionBackDetails != null && !commissionBackDetails.isEmpty()){
            CommissionBackDetail commissionBackDetail = commissionBackDetails.get(0);
            if(!brandIds.contains(commissionBackDetail.getBrandId())){
                log.error("服务商查看佣金明细,非授权明细不能查看:{}, 查看人:{}", JSONObject.toJSONString(commissionBackDetail), JSONObject.toJSONString(sysUser));
                return null;
            }
        }
        return commissionBackDetails;
    }

    /**
     * 获取品牌商佣金退回明细
     * @param commissionBackVo 获取参数
     * @return 佣金退回明细
     */
    @Override
    public List<CommissionBackDetail> getBrandCommissionBackDetailList(CommissionBackVo commissionBackVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = getBrandIdDetail(commissionBackVo, sysUser);
        if (brandId == null) return null;
        List<CommissionBackDetail> commissionBackDetails = getCommissionBackDetailList(commissionBackVo);
        if(commissionBackDetails != null && !commissionBackDetails.isEmpty()){
            CommissionBackDetail commissionBackDetail = commissionBackDetails.get(0);
            if(!brandId.equals(commissionBackDetail.getBrandId())){
                log.error("品牌商查看佣金明细,非本品牌商下明细不能查看:{}, 查看人:{}", JSONObject.toJSONString(commissionBackDetail), JSONObject.toJSONString(sysUser));
                return null;
            }
        }
        return commissionBackDetails;
    }

    /**
     * 获取佣金退回总金额
     * @param commissionBackVo 获取参数
     * @return 佣金退回总金额
     */
    @Override
    public BigDecimal getTotalCommissionBack(CommissionBackVo commissionBackVo) {
        if(commissionBackVo.getDesignerId() == null || commissionBackVo.getBrandId() == null){
            return null;
        }
        return billOrderModifyMapper.getTotalCommissionBack(commissionBackVo.getBrandId(), commissionBackVo.getDesignerId());
    }


    @Nullable
    private static Long getBrandId(SysUser sysUser) {
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商不能获取品牌下的佣金追回:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return brandId;
    }

    @Nullable
    private List<Long> getServiceId(SysUser sysUser) {
        Long serviceId = sysUser.getServiceId();
        if(serviceId == null){
            log.error("非服务商不能获取服务下的设计师账单:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
    }

    @Nullable
    private List<Long> getServiceIdDetail(CommissionBackVo commissionBackVo, SysUser sysUser) {
        Long serviceId = sysUser.getServiceId();
        if(serviceId == null){
            log.error("非服务商不能获取未授权品牌商下的佣金追回详情:{}, {}", JSONObject.toJSONString(commissionBackVo), JSONObject.toJSONString(sysUser));
            return null;
        }
        List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if(brandIds == null || brandIds.isEmpty()){
            log.error("服务商下没有授权的品牌商，不能获取未授权品牌商下的佣金追回详情:{}, {}", JSONObject.toJSONString(commissionBackVo), JSONObject.toJSONString(sysUser));
            return null;
        }
        return brandIds;
    }

    private List<CommissionBackDetail> getCommissionBackDetailList(CommissionBackVo commissionBackVo){
        return billOrderModifyMapper.getCommissionBackDetailList(commissionBackVo);
    }

    @Nullable
    private static Long getBrandIdDetail(CommissionBackVo commissionBackVo, SysUser sysUser) {
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商不能获取品牌商下的佣金追回详情:{}, {}", JSONObject.toJSONString(commissionBackVo), JSONObject.toJSONString(sysUser));
            return null;
        }
        return brandId;
    }

    private CommissionSumDto getCommissionBackList(CommissionBackVo commissionBackVo) {
        List<CommissionBackDto> commissionBackDtos = billOrderModifyMapper.getCommissionBackList(commissionBackVo);
        if(commissionBackDtos != null && !commissionBackDtos.isEmpty()){
            commissionBackDtos.forEach(commissionBackDto -> {
                commissionBackDto.setPhone(ParamDecryptUtil.decryptParam(commissionBackDto.getPhone(), ParamDecryptUtil.PHONE_KEY));
                commissionBackDto.setNeedBackCommission(commissionBackDto.getMoney().subtract(commissionBackDto.getNowMoney()));
            });
            return getCommissionSumDto(commissionBackDtos);
        }
        return null;
    }

    @NotNull
    private CommissionSumDto getCommissionSumDto(List<CommissionBackDto> commissionBackDtos) {
        CommissionSumDto commissionSumDto = new CommissionSumDto();
        BigDecimal sumNeedBackCommission = sumNeedBackCommission(commissionBackDtos);
        BigDecimal sumCommissionBacked = sumCommissionBacked(commissionBackDtos);
        BigDecimal sumCommissionBackRemaining = sumNeedBackCommission.subtract(sumCommissionBacked);
        commissionSumDto.setNeedBackCommission(sumNeedBackCommission);
        commissionSumDto.setRecoveredCommission(sumCommissionBacked);
        commissionSumDto.setToBeRecoveredCommission(sumCommissionBackRemaining);
        commissionSumDto.setList(getDataTable(commissionBackDtos));
        return commissionSumDto;
    }


    /**
     * 计算需追回佣金（needBackCommission）的总和
     */
    public static BigDecimal sumNeedBackCommission(List<CommissionBackDto> commissionBackDtos) {
        return commissionBackDtos.stream()
                .map(CommissionBackDto::getNeedBackCommission)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算已追回佣金（commissionBacked）的总和
     */
    public static BigDecimal sumCommissionBacked(List<CommissionBackDto> commissionBackDtos) {
        return commissionBackDtos.stream()
                .map(CommissionBackDto::getCommissionBacked)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
