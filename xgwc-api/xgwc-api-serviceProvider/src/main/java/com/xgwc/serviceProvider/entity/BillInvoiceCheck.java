package com.xgwc.serviceProvider.entity;

import lombok.Data;

@Data
public class BillInvoiceCheck {

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单发票ID
     */
    private Long billInvoiceId;

    /**
     * 审核用户ID
     */
    private Long checkUserId;

    /**
     * 审核人
     */
    private String checkUserName;

    /**
     * 备注
     */
    private String comment;

    /**
     * 状态：0正常，1非正常
     */
    private Integer status;

    /**
     * 审核结果：0待审核，1审核成功，2.审核失败
     */
    private Integer checkStatus;
    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
