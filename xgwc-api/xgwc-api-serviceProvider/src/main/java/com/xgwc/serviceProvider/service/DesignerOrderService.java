package com.xgwc.serviceProvider.service;

import com.xgwc.serviceProvider.entity.DesignerBillSub;
import com.xgwc.serviceProvider.entity.dto.DesignerBillDto;
import com.xgwc.serviceProvider.entity.dto.DesignerBillSubDto;
import com.xgwc.serviceProvider.entity.vo.DesignerBillSubVo;
import com.xgwc.serviceProvider.entity.vo.DesignerBillVo;

import java.util.List;

/**
 * 设计师订单
 */
public interface DesignerOrderService {

    /**
     * 获取设计师账单列表
     * @param designerBillVo 参数
     * @return 设计师账单
     */
    List<DesignerBillDto> getDesignerBillList(DesignerBillVo designerBillVo);

    /**
     * 获取设设计师子账单列表
     * @param designerBillSubVo 参数
     * @return 设计师账单
     */
    List<DesignerBillSubDto> getDesignerBillSubList(DesignerBillSubVo designerBillSubVo);
}
