package com.xgwc.serviceProvider.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaymentDaysVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("周期：1一周一结,2半个月一结,3一个月一结,4一个季度一结")
    private Integer cycleType;

    @FieldDesc("类型：1设计师 2客服")
    private Integer paymentType;

    @FieldDesc("扣点")
    private BigDecimal taxMoney;

}
