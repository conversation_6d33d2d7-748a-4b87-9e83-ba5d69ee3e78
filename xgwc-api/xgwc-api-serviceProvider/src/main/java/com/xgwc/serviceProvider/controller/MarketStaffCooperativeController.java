package com.xgwc.serviceProvider.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperativeVo;
import com.xgwc.serviceProvider.entity.dto.MarketStaffCooperativeDto;
import com.xgwc.serviceProvider.entity.vo.MarketStaffCooperativeQueryVo;
import com.xgwc.serviceProvider.service.IMarketStaffCooperativeService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

@RestController
@RequestMapping("/marketStaffCooperative")
public class MarketStaffCooperativeController extends BaseController {
    @Autowired
    private IMarketStaffCooperativeService marketStaffCooperativeService;

    /**
     * 查询员工加盟列表
     */
    @MethodDesc("查询员工加盟列表")
    @PreAuthorize("@ss.hasPermission('marketStaffCooperative:marketStaffCooperative:list')")
    @GetMapping("/list")
    public ApiResult<MarketStaffCooperativeDto> list(MarketStaffCooperativeQueryVo marketStaffCooperative) {
        startPage();
        List<MarketStaffCooperativeDto> list = marketStaffCooperativeService.selectMarketStaffCooperativeList(marketStaffCooperative);
        return getDataTable(list);
    }

    /**
     * 获取员工加盟详细信息
     */
    @MethodDesc("获取员工加盟详细信息")
    @PreAuthorize("@ss.hasPermission('marketStaffCooperative:marketStaffCooperative:query')")
    @GetMapping(value = "/get")
    public ApiResult<MarketStaffCooperativeDto> getInfo(@RequestParam("id") Long id) {
        return success(marketStaffCooperativeService.selectMarketStaffCooperativeById(id));
    }

}
