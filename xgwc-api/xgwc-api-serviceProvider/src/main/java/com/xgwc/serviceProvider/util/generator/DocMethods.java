package com.xgwc.serviceProvider.util.generator;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DocMethods  implements Doc {

    private String title;

    private int row = 0;
    private int col = 5;

    private List<MethodData> datas;

    public void setDatas(MethodData data) {
        if(this.datas == null) {
            this.datas = new ArrayList<>();
            this.datas.add(new MethodData("参数名","参数类型","是否必传","参数示例","字段说明"));
            this.row++;
        }
        this.datas.add(data);
        this.row++;
    }

    @Override
    public String getTitle() {
        return title;
    }

    @Override
    public int getRow() {
        return row;
    }

    @Override
    public int getCol() {
        return col;
    }

    @Override
    public List<MethodData> getDatas() {
        return datas;
    }
}

@Data
class MethodData implements DocData {

    private String name;
    private String type;
    private String isEq;
    private String example;
    private String describe;

    public MethodData(String name, String type, String isEq, String example, String describe) {
        this.name = name;
        this.type = type;
        this.isEq = isEq;
        this.example = example;
        this.describe = describe;
    }

    @Override
    public List<String> getData() {
        List<String> list = new ArrayList<>();
        list.add(name);
        list.add(type);
        list.add(isEq);
        list.add(example);
        list.add(describe);
        return list;
    }
}