package com.xgwc.serviceProvider.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.dao.ServiceStationMapper;
import com.xgwc.serviceProvider.entity.dto.ServiceStationDto;
import com.xgwc.serviceProvider.entity.param.ServiceStationParam;
import com.xgwc.serviceProvider.entity.vo.ServiceStationVo;
import com.xgwc.serviceProvider.service.ServiceStationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  14:13
 */
@Service
@Slf4j
public class ServiceStationServiceImpl implements ServiceStationService {

    @Resource
    private ServiceStationMapper serviceStationMapper;

    /**
     * 获取岗位列表
     * @param serviceStationParam 查询参数
     * @return 岗位列表
     */
    @Override
    public List<ServiceStationVo> getServiceStationList(ServiceStationParam serviceStationParam) {
        serviceStationParam.setServiceId(SecurityUtils.getSysUser().getServiceId());
        return serviceStationMapper.getServiceStationList(serviceStationParam);
    }

    /**
     * 保存岗位
     * @param serviceStationDto 岗位信息
     * @return 保存结果
     */
    @Override
    public ApiResult saveServiceStation(ServiceStationDto serviceStationDto) {
        // 1. 参数校验
        if (serviceStationDto == null) {
            log.warn("保存岗位失败：参数不能为空");
            return ApiResult.error("岗位信息不能为空");
        }

        // 2. 执行保存操作
        try {
            serviceStationDto.setServiceId(SecurityUtils.getSysUser().getServiceId());
            serviceStationDto.setCreateBy(SecurityUtils.getNickName());
            int affectedRows = serviceStationMapper.saveServiceStation(serviceStationDto);
            if (affectedRows <= 0) {
                log.error("保存岗位失败，未影响任何行，stationId={}", serviceStationDto.getStationId());
                return ApiResult.error("保存岗位失败");
            }
            log.info("保存岗位成功，stationId={}", serviceStationDto.getStationId());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("保存岗位异常，stationId={}, 错误信息：{}",
                    serviceStationDto.getStationId(), e.getMessage(), e);
            return ApiResult.error("系统异常，保存失败");
        }
    }

    /**
     * 根据ID查询岗位
     * @param stationId 岗位ID
     * @return 岗位信息
     */
    @Override
    public ApiResult getServiceStationById(Long stationId) {
        // 1. 参数校验
        if (stationId == null) {
            log.warn("查询岗位失败：stationId不能为空");
            return ApiResult.error("岗位ID不能为空");
        }

        // 2. 查询数据
        try {
            ServiceStationParam param = new ServiceStationParam();
            param.setStationId(stationId);
            List<ServiceStationVo> resultList = serviceStationMapper.getServiceStationList(param);

            // 3. 处理空结果
            if (CollectionUtils.isEmpty(resultList)) {
                log.warn("未找到岗位信息，stationId={}", stationId);
                return ApiResult.error("岗位不存在");
            }
            return ApiResult.ok(resultList.get(0));
        } catch (Exception e) {
            log.error("查询岗位异常，stationId={}, 错误信息：{}", stationId, e.getMessage(), e);
            return ApiResult.error("系统异常，查询失败");
        }
    }

    /**
     * 更新岗位信息
     * @param serviceStationDto 岗位信息
     * @return 更新结果
     */
    @Override
    public ApiResult updateServiceStation(ServiceStationDto serviceStationDto) {
        // 1. 参数校验
        if (serviceStationDto == null || serviceStationDto.getStationId() == null) {
            log.warn("更新岗位失败：参数或stationId为空");
            return ApiResult.error("岗位信息不完整");
        }

        // 2. 执行更新
        try {
            serviceStationDto.setUpdateBy(SecurityUtils.getNickName());
            int affectedRows = serviceStationMapper.updateServiceStation(serviceStationDto);
            if (affectedRows <= 0) {
                log.error("更新岗位失败，未影响任何行，stationId={}", serviceStationDto.getStationId());
                return ApiResult.error("更新岗位失败，可能ID不存在");
            }
            log.info("更新岗位成功，stationId={}", serviceStationDto.getStationId());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新岗位异常，stationId={}, 错误信息：{}",
                    serviceStationDto.getStationId(), e.getMessage(), e);
            return ApiResult.error("系统异常，更新失败");
        }
    }

    /**
     * 更新岗位状态
     * @param stationId 岗位ID
     * @param status 状态
     * @return 更新结果
     */
    @Override
    public ApiResult updateStatusById(Integer stationId, Integer status) {
        // 1. 参数校验
        if (stationId == null ) {
            log.warn("更新岗位状态失败：stationId或status为空");
            return ApiResult.error("参数不完整");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新岗位状态失败");
        }
        // 2. 执行更新
        try {
            int affectedRows = serviceStationMapper.updateStatusById(stationId, status);
            if (affectedRows <= 0) {
                log.error("更新岗位状态失败，未影响任何行，stationId={}", stationId);
                return ApiResult.error("更新状态失败，可能ID不存在");
            }
            log.info("更新岗位状态成功，stationId={}, status={}", stationId, status);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新岗位状态异常，stationId={}, 错误信息：{}", stationId, e.getMessage(), e);
            return ApiResult.error("系统异常，更新失败");
        }
    }
}
