package com.xgwc.serviceProvider.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class MarketStaffCooperativeDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("员工id")
    @Excel(name = "员工id")
    private Long staffId;

    @FieldDesc("销售服务商id")
    @Excel(name = "销售服务商id")
    private Long serviceOwnerId;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商员工id")
    @Excel(name = "加盟商员工id")
    private Long franchiseStaffId;

    @FieldDesc("是否删除：0正常，1删除")
    @Excel(name = "是否删除：0正常，1删除")
    private Long isDel;

    @FieldDesc("创建人id")
    @Excel(name = "创建人id")
    private Long createById;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人id")
    @Excel(name = "修改人id")
    private Long updateById;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("staffId",getStaffId())
            .append("serviceOwnerId",getServiceOwnerId())
            .append("franchiseId",getFranchiseId())
            .append("franchiseStaffId",getFranchiseStaffId())
            .append("isDel",getIsDel())
            .append("createById",getCreateById())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateById",getUpdateById())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
        .toString();
    }
}
