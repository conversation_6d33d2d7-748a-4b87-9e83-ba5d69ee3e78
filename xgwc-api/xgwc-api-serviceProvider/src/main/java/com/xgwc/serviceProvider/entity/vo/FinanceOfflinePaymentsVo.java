package com.xgwc.serviceProvider.entity.vo;

import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FinanceOfflinePaymentsVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("实收金额")
    private BigDecimal amount;

    @FieldDesc("归档结果:0-未锁定 1-已锁定")
    private Integer archivalStatus;

    @FieldDesc("收款主体id")
    private Long companyInfoId;

    @FieldDesc("谈单人")
    private String contactPerson;

    @FieldDesc("客户ID")
    private String customerId;

    @FieldDesc("差额")
    private BigDecimal differenceAmount;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("自增主键")
    private Long id;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("订单id")
    private Long orderId;

    @FieldDesc("收款金额")
    private BigDecimal paymentAmount;

    @FieldDesc("付款流水号")
    private String paymentFlowNumber;

    @FieldDesc("支付方式:1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫 ")
    private Integer paymentMethod;

    @FieldDesc("收款编号")
    private String paymentNumber;

    @FieldDesc("付款截图")
    private String paymentScreenshot;

    @FieldDesc("收款状态:0-财务未收 1-财务已收")
    private Integer paymentStatus;

    @FieldDesc("备注")
    private String remarks;

    @FieldDesc("店铺id")
    private Long shopId;

    @FieldDesc("订单支付流水id")
    private Long payId;

    @FieldDesc("付款码主体（字典主键）")
    private Long paymentCodeBody;




}
