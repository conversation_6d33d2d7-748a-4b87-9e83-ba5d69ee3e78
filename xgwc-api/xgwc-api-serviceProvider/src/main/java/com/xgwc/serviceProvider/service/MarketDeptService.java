package com.xgwc.serviceProvider.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.ServiceDeptDto;
import com.xgwc.serviceProvider.entity.param.ServiceDeptParam;
import com.xgwc.serviceProvider.entity.vo.ServiceDeptInfo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-28  11:16
 */

public interface MarketDeptService {

    /**
     * 获取部门列表
     * @param marketDeptParam 部门参数
     * @return 部门列表
     */
    List<ServiceDeptInfo> getMarketDeptList(ServiceDeptParam marketDeptParam);

    /**
     * 保存部门
     * @param marketDeptDto 部门信息
     * @return 保存结果
     */
    ApiResult saveMarketDept(ServiceDeptDto marketDeptDto);

    /**
     * 根据部门ID获取部门信息
     * @param deptId 部门ID
     * @return 部门信息
     */
    ApiResult getMarketDeptById(Long deptId);

    /**
     * 通过userId查询部门信息
     * @param userId
     * @return
     */
    ApiResult getUserDeptByUserId(Long userId);

    /**
     * 更新部门信息
     * @param marketDeptDto 部门信息
     * @return 更新结果
     */
    ApiResult updateMarketDeptById(ServiceDeptDto marketDeptDto);

    /**
     * 更新部门状态
     * @param deptId 部门ID
     * @param status 部门状态
     * @return 更新结果
     */
    ApiResult updateStatusById(Integer deptId, Integer status);

    /**
     * 查询回显部门详情信息-负责人、助理、是否排班
     *
     * @param deptId 部门id
     * @return 部门详情信息-负责人、助理、是否排班
     */
    ApiResult getXgwcDeptStaffInfo(Integer deptId);

    /**
     * 更新部门信息-负责人、助理、是否排班
     *
     * @param marketDeptDto 部门信息
     * @return 更新结果
     */
    ApiResult updateXgwcDeptStaffInfo(ServiceDeptDto marketDeptDto);
}
