package com.xgwc.serviceProvider.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.serviceProvider.entity.dto.ServiceStationDto;
import com.xgwc.serviceProvider.entity.param.ServiceStationParam;
import com.xgwc.serviceProvider.entity.vo.ServiceStationVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwen<PERSON>hu<PERSON>
 * @CreateTime: 2025-04-28  14:11
 */
public interface ServiceStationService {

    /**
     * 获取服务商岗位列表
     * @param serviceStationParam 服务商岗位参数
     * @return 服务商岗位列表
     */
    List<ServiceStationVo> getServiceStationList(ServiceStationParam serviceStationParam);

    /**
     * 保存服务商岗位
     * @param serviceStationDto 服务商岗位参数
     * @return 服务商岗位列表
     */
    ApiResult saveServiceStation(ServiceStationDto serviceStationDto);

    /**
     * 根据服务商岗位id获取服务商岗位
     * @param stationId 服务商岗位id
     * @return 服务商岗位
     */
    ApiResult getServiceStationById(Long stationId);

    /**
     * 更新服务商岗位
     * @param serviceStationDto 服务商岗位参数
     * @return 服务商岗位列表
     */
    ApiResult updateServiceStation(ServiceStationDto serviceStationDto);

    /**
     * 更服务商岗位状态
     * @param stationId 服务商岗位id
     * @param status 服务商岗位状态
     * @return 服务商岗位列表
     */
    ApiResult updateStatusById(Integer stationId, Integer status);
}
