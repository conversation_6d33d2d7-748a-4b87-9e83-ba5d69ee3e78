package com.xgwc.serviceProvider.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.feign.api.OrderFeign;
import com.xgwc.serviceProvider.dao.FinanceOfflinePaymentsMapper;
import com.xgwc.serviceProvider.dao.ServiceAuthorizeMapper;
import com.xgwc.serviceProvider.entity.FinanceOfflinePayments;
import com.xgwc.serviceProvider.entity.dto.FinanceOfflinePaymentsDto;
import com.xgwc.serviceProvider.entity.param.FinanceOfflinePaymentsParam;
import com.xgwc.serviceProvider.entity.vo.FinanceOfflinePaymentsVo;
import com.xgwc.serviceProvider.service.IFinanceOfflinePaymentsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class FinanceOfflinePaymentsServiceImpl implements IFinanceOfflinePaymentsService {
    @Resource
    private FinanceOfflinePaymentsMapper financeOfflinePaymentsMapper;

    @Resource
    private ServiceAuthorizeMapper serviceAuthorizeMapper;

    @Resource
    private OrderFeign orderFeign;

    /**
     * 查询线下收款
     *
     * @param id 线下收款主键
     * @param isFlag 0品牌商 1服务商
     * @return 线下收款
     */
    @Override
    public FinanceOfflinePaymentsDto selectFinanceOfflinePaymentsById(Long id, Integer isFlag) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if (extracted(isFlag, sysUser, new FinanceOfflinePaymentsParam())) return null;
        FinanceOfflinePaymentsDto financeOfflinePaymentsDto;
        if (isFlag == 0){
            financeOfflinePaymentsDto = financeOfflinePaymentsMapper.selectFinanceOfflinePaymentsById(id);
            if(financeOfflinePaymentsDto == null || !Objects.equals(financeOfflinePaymentsDto.getBrandId(), sysUser.getBrandId())){
                log.error("非当前品牌商账号，禁止查询:{},查询详情id:{}", JSONObject.toJSON(sysUser), id);
                return null;
            }
        }else {
            Long serviceId = sysUser.getServiceId();
            List<Long> brandIds = serviceAuthorizeMapper.getAuthorizeBrandIdsByServiceId(serviceId);
            if (brandIds == null || brandIds.isEmpty()) {
                log.error("服务商没有授权的品牌商，禁止查询:{},id：{}", JSONObject.toJSONString(sysUser), id);
                return null;
            }
            financeOfflinePaymentsDto = financeOfflinePaymentsMapper.selectFinanceOfflinePaymentsById(id);
            if(financeOfflinePaymentsDto == null || !brandIds.contains(financeOfflinePaymentsDto.getBrandId())){
                log.error("非当前服务商账单，禁止查询:{},查询详情id:{}", JSONObject.toJSONString(sysUser), id);
                return null;
            }
        }
        return financeOfflinePaymentsDto;
    }

    /**
     * 查询线下收款列表
     * 
     * @param financeOfflinePayments 线下收款
     * @return 线下收款
     */
    @Override
    public List<FinanceOfflinePaymentsDto> selectFinanceOfflinePaymentsList(FinanceOfflinePaymentsParam financeOfflinePayments) {
        Integer isFlag = financeOfflinePayments.getIsFlag();
        SysUser sysUser = SecurityUtils.getSysUser();
        if (extracted(isFlag, sysUser, financeOfflinePayments)) return null;
        Long brandId = financeOfflinePayments.getBrandId();
        financeOfflinePayments.setBrandId( brandId == null ? SecurityUtils.getSysUser().getBrandId() : brandId);
        return financeOfflinePaymentsMapper.selectFinanceOfflinePaymentsList(financeOfflinePayments);
    }

    private static boolean extracted(Integer isFlag, SysUser sysUser, FinanceOfflinePaymentsParam financeOfflinePayments) {
        if(isFlag == null || isFlag < 0 || isFlag > 1){
            log.warn("isFlag 参数错误");
            return true;
        }
        if(isFlag == 0){
            Long brandId = sysUser.getBrandId();
            if(brandId == null){
                log.warn("当前登录账号非品牌商账号，登录信息{}", JSONObject.toJSON(sysUser));
                return true;
            }
        }else {
            Long serviceId = sysUser.getServiceId();
            if(serviceId == null){
                log.warn("当前登录账号非服务商账号，登录信息{}", JSONObject.toJSON(sysUser));
                return true;
            }
            financeOfflinePayments.setShopManagerId(sysUser.getUserId());
        }
        return false;
    }

    /**
     * 修改线下收款
     * 
     * @param dto 线下收款
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateFinanceOfflinePayments(FinanceOfflinePaymentsVo dto) {
        if (dto == null || dto.getPaymentFlowNumber() == null) {
            throw new ApiException("支付流水号不能为空");
        }
        FinanceOfflinePayments financeOfflinePayments = BeanUtil.copyProperties(dto, FinanceOfflinePayments.class);
        extracted(financeOfflinePayments);

        FinanceOfflinePaymentsDto financeOfflinePaymentsDto = financeOfflinePaymentsMapper.selectFinanceOfflinePaymentsById(dto.getPayId());
        if (financeOfflinePaymentsDto == null) {
            log.error("该数据不存在:{}", JSONObject.toJSON(dto));
            throw new ApiException("该数据不存在");
        }
        String paymentFlowNumber = financeOfflinePaymentsDto.getPaymentFlowNumber();
        if (!Objects.equals(dto.getPaymentFlowNumber(), paymentFlowNumber)){
            Integer res = financeOfflinePaymentsMapper.selectPayFlowNumber(dto.getPaymentFlowNumber());
            if (res != null) {
                throw new ApiException("流水号已存在");
            }
        }
        BigDecimal paymentAmount = financeOfflinePaymentsDto.getPaymentAmount() == null ? BigDecimal.ZERO : financeOfflinePaymentsDto.getPaymentAmount();
        BigDecimal actualAmount = dto.getAmount() == null ? BigDecimal.ZERO : dto.getAmount();
        Integer paymentStatus = dto.getPaymentStatus();
        // 当收款金额等于实收金额且收款状态为财务已收时，归档状态为已锁定
        if (paymentAmount.compareTo(actualAmount) == 0 && paymentStatus == 1) {
            financeOfflinePayments.setArchivalStatus(1);
        }
        if (actualAmount.compareTo(paymentAmount) > 0) {
            throw new ApiException("实收金额不能大于收款金额");
        }
        financeOfflinePayments.setDifferenceAmount(paymentAmount.subtract(actualAmount));
        int updated = financeOfflinePaymentsMapper.updateFinanceOfflinePayments(financeOfflinePayments);
        if (updated <= 0) {
            log.error("线下归档失败:{}", JSONObject.toJSON(financeOfflinePayments));
            throw new ApiException("归档失败");
        }

        // 校验当前订单id下的流水是否全部归档
        Integer res = financeOfflinePaymentsMapper.selectArchivalStatusByOrderId(financeOfflinePaymentsDto.getOrderId());
        if (res == null) {
            try {
                orderFeign.updateOrderStatus(dto.getOrderId(), 4);
            } catch (Exception e) {
                log.error("Feign 调用远程更新订单状态失败: {}", e.getMessage());
                throw new ApiException("归档失败");
            }
        }
        return updated;
    }

    private static void extracted(FinanceOfflinePayments financeOfflinePayments) {
        String nickName = SecurityUtils.getNickName();
        Date nowDate = DateUtils.getNowDate();
        financeOfflinePayments.setUpdateBy(nickName);
        financeOfflinePayments.setUpdateTime(nowDate);
        financeOfflinePayments.setCreateBy(nickName);
        financeOfflinePayments.setCreateTime(nowDate);
    }
}
