package com.xgwc.serviceProvider.service;

import com.xgwc.serviceProvider.entity.BillKefuUpload;

import java.util.List;

public interface BillKefuUploadService {

    /**
     * 插入客服岗位工资记录
     * @param billKefuUpload 上传信息
     * @return 是否成功
     */
    int insertBillKeFuUpload(BillKefuUpload billKefuUpload);

    /**
     * 修改
     * @param billKefuUpload 上传信息
     * @return 是否成功
     */
    int updateBillKeFuUpload(BillKefuUpload billKefuUpload);


    /**
     * 获取服务商上传的所有记录
     * @return 上传列表
     */
    List<BillKefuUpload> getBillKeFuUploadListByServiceId();
}
