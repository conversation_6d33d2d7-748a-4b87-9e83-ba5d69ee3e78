package com.xgwc.serviceProvider.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.dao.MarketCooperativeMapper;
import com.xgwc.serviceProvider.entity.MarketCooperative;
import com.xgwc.serviceProvider.entity.dto.MarketCooperativeDto;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeQueryVo;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeStatusVo;
import com.xgwc.serviceProvider.entity.vo.MarketCooperativeVo;
import com.xgwc.serviceProvider.service.IMarketCooperativeService;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;



@Service
public class MarketCooperativeServiceImpl implements IMarketCooperativeService {
    @Resource
    private MarketCooperativeMapper marketCooperativeMapper;

    /**
     * 查询销售合作
     * @param id 销售合作主键
     * @return 销售合作
     */
    @Override
    public MarketCooperativeDto selectMarketCooperativeById(Long id) {
        return marketCooperativeMapper.selectMarketCooperativeById(id);
    }

    /**
     * 查询销售合作列表
     * @param marketCooperative 销售合作
     * @return 销售合作
     */
    @Override
    public List<MarketCooperativeDto> selectMarketCooperativeList(MarketCooperativeQueryVo marketCooperative) {
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        if(franchiseId == null) return List.of();
        marketCooperative.setFranchiseId(franchiseId);
        List<MarketCooperativeDto> list = marketCooperativeMapper.selectMarketCooperativeList(marketCooperative);
        return list;
    }

    /**
     * 新增销售合作
     *
     * @param dto 销售合作
     * @return 结果
     */
    @Override
    public int insertMarketCooperative(MarketCooperativeVo dto) {
        SysUser sysUser = SecurityUtils.getSysUser();
        MarketCooperative marketCooperative = BeanUtil.copyProperties(dto, MarketCooperative.class);
        marketCooperative.setCreateById(sysUser.getUserId());
        marketCooperative.setCreateTime(DateUtils.getNowDate());
        marketCooperative.setCreateBy(sysUser.getUserName());
        return marketCooperativeMapper.insertMarketCooperative(marketCooperative);
    }

    @Override
    public int updateStatus(MarketCooperativeStatusVo dto) {
        if(marketCooperativeMapper.selectMarketCooperativeById(dto.getId()) == null){
            throw new ApiException("该数据不存在");
        }
        SysUser sysUser = SecurityUtils.getSysUser();
        MarketCooperative marketCooperative = new MarketCooperative();
        marketCooperative.setId(dto.getId());
        marketCooperative.setStatus(dto.getStatus());
        marketCooperative.setUpdateById(sysUser.getUserId());
        marketCooperative.setUpdateBy(sysUser.getUserName());
        marketCooperative.setUpdateTime(DateUtils.getNowDate());
        return marketCooperativeMapper.updateMarketCooperative(marketCooperative);
    }

}
