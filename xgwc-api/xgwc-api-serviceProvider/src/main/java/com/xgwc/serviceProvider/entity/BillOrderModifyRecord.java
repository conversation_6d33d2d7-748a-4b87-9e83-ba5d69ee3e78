package com.xgwc.serviceProvider.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillOrderModifyRecord extends BillOrderModify{

    /**
     * 主键
     */
    private Long id;

    /**
     * billordermodify 主键
     */
    private Long modifyId;

    /**
     * 账单开始
     */
    private String billStart;

    /**
     * 账单结束
     */
    private String billEnd;

    /**
     * 子账单id
     */
    private Long subBillId;

    /**
     * 主账单id
     */
    private Long billId;

    /**
     * 佣金追回
     */
    private BigDecimal commissionBack;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

}
