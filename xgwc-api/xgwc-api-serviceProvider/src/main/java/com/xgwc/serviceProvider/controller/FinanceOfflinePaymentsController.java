package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.entity.dto.FinanceOfflinePaymentsDto;
import com.xgwc.serviceProvider.entity.param.FinanceOfflinePaymentsParam;
import com.xgwc.serviceProvider.entity.vo.FinanceOfflinePaymentsVo;
import com.xgwc.serviceProvider.service.IFinanceOfflinePaymentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.finance.controller
 * @Author: k<PERSON><PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-07-07  14:13
 */

/**
 * 线下收款
 */

@RestController
@RequestMapping("/financeOfflinePayments")
public class FinanceOfflinePaymentsController extends BaseController {
    @Autowired
    private IFinanceOfflinePaymentsService financeOfflinePaymentsService;

    /**
     * 查询线下收款列表
     */
    @MethodDesc("查询品牌商线下收款列表")
    //@PreAuthorize("@ss.hasPermission('financeOfflinePayments:financeOfflinePayments:list')")
    @GetMapping("/brandList")
    public ApiResult<FinanceOfflinePaymentsDto> brandList(FinanceOfflinePaymentsParam financeOfflinePayments) {
        startPage();
        financeOfflinePayments.setIsFlag(0);
        List<FinanceOfflinePaymentsDto> list = financeOfflinePaymentsService.selectFinanceOfflinePaymentsList(financeOfflinePayments);
        return getDataTable(list);
    }

    /**
     * 查询线下收款列表
     */
    @MethodDesc("查询服务商线下收款列表")
    //@PreAuthorize("@ss.hasPermission('financeOfflinePayments:financeOfflinePayments:list')")
    @GetMapping("/serviceList")
    public ApiResult<FinanceOfflinePaymentsDto> serviceList(FinanceOfflinePaymentsParam financeOfflinePayments) {
        startPage();
        financeOfflinePayments.setIsFlag(1);
        List<FinanceOfflinePaymentsDto> list = financeOfflinePaymentsService.selectFinanceOfflinePaymentsList(financeOfflinePayments);
        return getDataTable(list);
    }



    /**
     * 获取线下收款详细信息
     */
    @MethodDesc("获取品牌商线下收款详细信息")
    //@PreAuthorize("@ss.hasPermission('financeOfflinePayments:financeOfflinePayments:query')")
    @GetMapping(value = "/brand/{id}")
    public ApiResult<FinanceOfflinePaymentsDto> getBrandInfo(@PathVariable("id") Long id) {
        return success(financeOfflinePaymentsService.selectFinanceOfflinePaymentsById(id, 0));
    }

    /**
     * 获取线下收款详细信息
     */
    @MethodDesc("获取服务商线下收款详细信息")
    //@PreAuthorize("@ss.hasPermission('financeOfflinePayments:financeOfflinePayments:query')")
    @GetMapping(value = "/service{id}")
    public ApiResult<FinanceOfflinePaymentsDto> getServiceInfo(@PathVariable("id") Long id) {
        return success(financeOfflinePaymentsService.selectFinanceOfflinePaymentsById(id, 1));
    }

    /**
     * 归档
     */
    @MethodDesc("归档")
    //@PreAuthorize("@ss.hasPermission('financeOfflinePayments:financeOfflinePayments:edit')")
    @Log(title = "线下收款", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody FinanceOfflinePaymentsVo financeOfflinePayments) {
        return toAjax(financeOfflinePaymentsService.updateFinanceOfflinePayments(financeOfflinePayments));
    }
}
