package com.xgwc.serviceProvider.service.impl;

import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.StringUtils;
import com.xgwc.serviceProvider.dao.DesignerBillMapper;
import com.xgwc.serviceProvider.dao.DesignerBillSubMapper;
import com.xgwc.serviceProvider.entity.dto.DesignerBillDto;
import com.xgwc.serviceProvider.entity.dto.DesignerBillSubDto;
import com.xgwc.serviceProvider.entity.vo.DesignerBillSubVo;
import com.xgwc.serviceProvider.entity.vo.DesignerBillVo;
import com.xgwc.serviceProvider.service.DesignerOrderService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DesignerOrderServiceImpl implements DesignerOrderService {

    @Resource
    private DesignerBillMapper designerBillMapper;

    @Resource
    private DesignerBillSubMapper designerBillSubMapper;

    @Override
    public List<DesignerBillDto> getDesignerBillList(DesignerBillVo designerBillVo) {
        //TODO 校验品牌商给财务授权列表，将加盟商id作为查询限制，只能查询授权的加盟商
        if(StringUtils.isNotEmpty(designerBillVo.getPhone())){
            //加密后进行匹配
            designerBillVo.setDesignerName(ParamDecryptUtil.encrypt(designerBillVo.getPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        List<DesignerBillDto> designerBillDtos = designerBillMapper.selectDesignerBillList(designerBillVo);
        if(designerBillDtos != null && !designerBillDtos.isEmpty()){
            designerBillDtos.forEach(x->{
                x.setPhone(ParamDecryptUtil.encrypt(x.getPhone(), ParamDecryptUtil.DESIGNER_KEY));
                x.setZfbName(ParamDecryptUtil.encrypt(x.getZfbName(), ParamDecryptUtil.DESIGNER_KEY));
                x.setZfbAccount(ParamDecryptUtil.encrypt(x.getZfbAccount(), ParamDecryptUtil.DESIGNER_KEY));
            });
        }
        return designerBillDtos;
    }

    @Override
    public List<DesignerBillSubDto> getDesignerBillSubList(DesignerBillSubVo designerBillSubVo) {
        //TODO 校验品牌商给财务授权列表，将加盟商id作为查询限制，只能查询授权的加盟商
        if(StringUtils.isNotEmpty(designerBillSubVo.getPhone())){
            //加密后进行匹配
            designerBillSubVo.setDesignerName(ParamDecryptUtil.encrypt(designerBillSubVo.getPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        List<DesignerBillSubDto> designerBillSubDtos = designerBillSubMapper.selectDesignerBillSubList(designerBillSubVo);
        if(designerBillSubDtos != null && !designerBillSubDtos.isEmpty()){
            designerBillSubDtos.forEach(x->{
                x.setPhone(ParamDecryptUtil.encrypt(x.getPhone(), ParamDecryptUtil.DESIGNER_KEY));
                x.setZfbName(ParamDecryptUtil.encrypt(x.getZfbName(), ParamDecryptUtil.DESIGNER_KEY));
                x.setZfbAccount(ParamDecryptUtil.encrypt(x.getZfbAccount(), ParamDecryptUtil.DESIGNER_KEY));
            });
        }
        return designerBillSubDtos;
    }
}
