package com.xgwc.serviceProvider.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.EmployeesDateFlag;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.serviceProvider.dao.MarketEmployeesMapper;
import com.xgwc.serviceProvider.entity.MarketEmpAccounts;
import com.xgwc.serviceProvider.entity.MarketEmpAttachments;
import com.xgwc.serviceProvider.entity.MarketEmployees;
import com.xgwc.serviceProvider.entity.dto.MarketEmployeesCountDto;
import com.xgwc.serviceProvider.entity.dto.MarketEmployeesDto;
import com.xgwc.serviceProvider.entity.vo.MarketEmployeesQueryVo;
import com.xgwc.serviceProvider.entity.vo.MarketEmployeesVo;
import com.xgwc.serviceProvider.service.MarketEmployeesService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;


@Service
@Slf4j
public class MarketEmployeesServiceImpl extends BaseController implements MarketEmployeesService {
    @Resource
    private MarketEmployeesMapper marketEmployeesMapper;
/*    @Resource
    private MarketStaffMapper marketStaffMapper;*/

    // 状态：0在职，1离职
    public static final Integer STATUS_ACTIVE = 0;
    public static final Integer STATUS_RESIGNING = 2;

    /**
     * 查询员工档案
     *
     * @param employeeId 员工档案主键
     * @return 员工档案
     */
    @Override
    public MarketEmployeesDto selectEmployeesByEmployeeId(Long employeeId) {
        MarketEmployeesDto marketEmployeesDto = marketEmployeesMapper.selectEmployeesByEmployeeId(employeeId);
        if (marketEmployeesDto != null) {
            List<MarketEmpAttachments> marketEmpAttachments = marketEmployeesMapper.selectMarketEmpAttachments(marketEmployeesDto.getEmployeeId());
            // 解密 true-加密 false-解密
            extracted(marketEmployeesDto);
            marketEmployeesDto.setXgwcEmpAttachments(marketEmpAttachments);
        }
        return marketEmployeesDto;
    }

    private void extracted(MarketEmployeesDto marketEmployeesDto) {
        // 解密 true-加密 false-解密
        marketEmployeesDto.setPhone(ParamDecryptUtil.encryptField(marketEmployeesDto.getPhone(), "手机号", false));
        marketEmployeesDto.setEmail(ParamDecryptUtil.encryptField(marketEmployeesDto.getEmail(), "邮箱", false));
        marketEmployeesDto.setIdNumber(ParamDecryptUtil.encryptField(marketEmployeesDto.getIdNumber(), "身份证号", false));
        marketEmployeesDto.setAddress(ParamDecryptUtil.encryptField(marketEmployeesDto.getAddress(), "员工地址", false));
        marketEmployeesDto.setEmerPhone(ParamDecryptUtil.encryptField(marketEmployeesDto.getEmerPhone(), "紧急联系人电话", false));
        marketEmployeesDto.setAlipayAccount(ParamDecryptUtil.encryptField(marketEmployeesDto.getAlipayAccount(), "支付宝账号", false));
        marketEmployeesDto.setAccountNumber(ParamDecryptUtil.encryptField(marketEmployeesDto.getAccountNumber(), "银行卡号", false));
    }

    /**
     * 查询员工档案列表
     *
     * @param employees 员工档案
     * @return 员工档案
     */
    @Override
    public MarketEmployeesCountDto selectEmployeesList(MarketEmployeesQueryVo employees) {
/*        Date[] entryDate = employees.getEntryDate();
        Integer dateFlag = employees.getDateFlag();
        if (dateFlag != null) {
            EmployeesDateFlag textByCode = EmployeesDateFlag.getTextByCode(dateFlag);
            if (textByCode != null) {
                extracted(employees, entryDate, textByCode);
            }
        }*/
        String name = employees.getName();
        boolean letterOrChinese = isLetterOrChinese(name);
        if (!letterOrChinese) {
            // 对敏感入参加密 加密之后匹配数据库中加密字段
            employees.setName(ParamDecryptUtil.encryptField(name, "/手机号/银行卡号/支付宝账号/身份证号", true));
        }
        //startPage();
        PageHelper.startPage(employees.getPageNum(), employees.getPageSize());
        Long marketId = SecurityUtils.getSysUser().getMarketId();
        employees.setMarketId(marketId);
        List<MarketEmployeesDto> marketEmployeesDtos = marketEmployeesMapper.selectEmployeesList(employees);
        for (MarketEmployeesDto marketEmployeesDto : marketEmployeesDtos) {
            // 解密 true-加密 false-解密
            marketEmployeesDto.setPhone(ParamDecryptUtil.encryptField(marketEmployeesDto.getPhone(), "手机号", false));
        }
        ApiResult dataTable = getDataTable(marketEmployeesDtos);
        MarketEmployeesQueryVo marketEmployeesQueryVo = new MarketEmployeesQueryVo();
        marketEmployeesQueryVo.setMarketId(marketId);
        List<MarketEmployeesDto> marketEmployeesDtos1 = marketEmployeesMapper.selectEmployeesList(marketEmployeesQueryVo);
        MarketEmployeesCountDto marketEmployeesCountDto = getCurrentEmployeesCount(marketEmployeesDtos1);
        marketEmployeesCountDto.setEmployeesDtos(dataTable);
        return marketEmployeesCountDto;
    }

    /**
     * 列表页签统计
     */
    private MarketEmployeesCountDto getCurrentEmployeesCount(List<MarketEmployeesDto> employees) {
        MarketEmployeesCountDto result = new MarketEmployeesCountDto();

        for (MarketEmployeesDto e : employees) {
            updateStatusCount(result, e.getStatus());
            updateEntryCount(result, e.getEntryDate());
            updateExitCount(result, e.getResignationDate());
            updateContractCount(result, e.getContractEndDate());
        }

        return result;
    }

    private void updateStatusCount(MarketEmployeesCountDto result, Integer status) {
        if (STATUS_ACTIVE.equals(status)) {
            result.incrementCurrentCount();
        } else if (STATUS_RESIGNING.equals(status)) {
            result.incrementResignationCount();
        }
    }

    private void updateEntryCount(MarketEmployeesCountDto result, Date entryDate) {
        if (DateUtils.isDateInCurrentMonth(entryDate)) {
            result.incrementMonthlyEntryCount();
        }
    }

    private void updateExitCount(MarketEmployeesCountDto result, Date resignationDate) {
        if (DateUtils.isDateInCurrentMonth(resignationDate)) {
            result.incrementMonthlyExitCount();
        }
    }

    private void updateContractCount(MarketEmployeesCountDto result, Date contractEndDate) {
        if (DateUtils.isWithinNextThreeMonths(contractEndDate)) {
            result.incrementContractMonthsCount();
        }
    }

    private static boolean isLetterOrChinese(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }
        // 正则规则：仅由字母或中文组成
        return str.matches("^[a-zA-Z\\p{Script=Han}]+$");
    }

    private static void extracted(MarketEmployeesQueryVo employees, Date[] entryDate, EmployeesDateFlag textByCode) {
        switch (textByCode) {
            case CONTRACT_END_DATE -> employees.setContractEndDate(entryDate);
            case RESIGNATION_DATE -> employees.setResignationDate(entryDate);
            case BIRTHDATE -> employees.setBirthdate(entryDate);
            default -> employees.setEntryDate(entryDate);
        }
    }

    /**
     * 新增员工档案
     *
     * @param vo 员工档案
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult insertEmployees(MarketEmployeesVo vo) {
        // 参数校验前置
        if (vo == null) {
            log.error("员工信息录入失败：输入参数不能为空");
            throw new IllegalArgumentException("员工信息不能为空");
        }

        validateEmployee(vo);  // 实体校验

        // 参数唯一性校验
        try {
            String validated = validateUniqueFieldsAsync(vo);
            if (validated != null) {
                return ApiResult.error(validated);
            }
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
            // ================= 员工信息处理 =================
            MarketEmployees marketEmployees = processEmployeeInfo(vo);
            marketEmployees.setMarketId(SecurityUtils.getSysUser().getMarketId());
            marketEmployees.setCreateBy(SecurityUtils.getNickName());
            int employeesResult = marketEmployeesMapper.insertEmployees(marketEmployees);
            /*MarketStaff staff = new MarketStaff();
            staff.setId(marketEmployees.getStaffId());
            staff.setArchiveStatus(1);
            marketStaffMapper.updateMarketStaff(staff);*/
            if (employeesResult <= 0) {
                log.error("员工信息新增失败");
                throw new ApiException("员工信息新增失败");
            }

            // ================= 账户信息处理 =================
            MarketEmpAccounts marketEmpAccounts = processAccountInfo(vo);
            marketEmpAccounts.setCreateBy(SecurityUtils.getNickName());
            marketEmpAccounts.setEmployeeId(marketEmployees.getEmployeeId());
            int accountsResult = marketEmployeesMapper.insertMarketEmpAccounts(marketEmpAccounts);
            if (accountsResult <= 0) {
                log.error("账户信息新增失败");
                throw new ApiException("账户信息新增失败");
            }

            // ================= 附件信息处理 =================
            List<MarketEmpAttachments> marketEmpAttachmentsList = vo.getXgwcEmpAttachments();
            if (!CollectionUtils.isEmpty(marketEmpAttachmentsList)) {
                for (MarketEmpAttachments empAttachments : marketEmpAttachmentsList) {
                    empAttachments.setEmployeeId(marketEmployees.getEmployeeId());
                    empAttachments.setCreateBy(SecurityUtils.getNickName());
                    int attachmentsResult = marketEmployeesMapper.insertMarketEmpAttachments(empAttachments);
                    if (attachmentsResult <= 0) {
                        log.error("附件信息新增失败");
                        throw new ApiException("附件信息新增失败");
                    }
                }
            }

            log.info("员工信息录入成功 ID:[{}] 姓名:[{}]",
                    marketEmployees.getStaffId(),
                    vo.getName());
            return ApiResult.ok();
        } catch (Exception e) {  // 全局异常捕获
            log.error("系统异常 员工:[{}] 错误信息:{}",
                    vo.getEmployeeId(), e.getMessage(), e);
            throw new ApiException("系统处理异常");
        }
    }

    private String validateUniqueFieldsAsync(MarketEmployeesVo vo) throws ExecutionException, InterruptedException {

        MarketEmployees marketEmployees = processEmployeeInfo(vo);
        MarketEmpAccounts marketEmpAccounts = processAccountInfo(vo);

        CompletableFuture<String> idNumberFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniquenesAsync(marketEmployees.getIdNumber(), "身份证号", marketEmployeesMapper::existsByIdNumber)
        );

        CompletableFuture<String> phoneFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniquenesAsync(marketEmployees.getPhone(), "手机号", marketEmployeesMapper::existsByPhone)
        );

        CompletableFuture<String> accountNumberFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniquenesAsync(marketEmpAccounts.getAccountNumber(), "银行卡号", marketEmployeesMapper::existsByAccountNumber)
        );

        CompletableFuture<String> alipayAccountFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniquenesAsync(marketEmpAccounts.getAlipayAccount(), "支付宝账号", marketEmployeesMapper::existsByAlipayAccount)
        );

        // 等待所有异步任务完成，并收集结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(idNumberFuture, phoneFuture, accountNumberFuture, alipayAccountFuture);
        allFutures.get(); // 阻塞直到所有任务完成

        // 收集所有校验结果
        List<String> errorMessages = new ArrayList<>();

        String idNumberResult = idNumberFuture.get();
        if (idNumberResult != null) {
            errorMessages.add(idNumberResult);
        }

        String phoneResult = phoneFuture.get();
        if (phoneResult != null) {
            errorMessages.add(phoneResult);
        }

        String accountNumberResult = accountNumberFuture.get();
        if (accountNumberResult != null) {
            errorMessages.add(accountNumberResult);
        }

        String alipayAccountResult = alipayAccountFuture.get();
        if (alipayAccountResult != null) {
            errorMessages.add(alipayAccountResult);
        }

        // 拼接所有错误信息
        if (!errorMessages.isEmpty()) {
            return String.join("，", errorMessages);
        }
        return null;
    }

    private String validateFieldUniquenesAsync(
            String fieldValue,
            String fieldName,
            Function<String, Boolean> existsChecker) {
        if (fieldValue != null && !fieldValue.trim().isEmpty() && existsChecker.apply(fieldValue)) {
            log.error("========================={}已存在", fieldName);
            return (fieldName + "已存在");
        }
        return null;
    }

    // 员工信息处理（抽取方法）
    private MarketEmployees processEmployeeInfo(MarketEmployeesVo dto) {
        MarketEmployees entity = BeanUtil.copyProperties(dto, MarketEmployees.class);

        // 字段加密 true-加密 false-解密
        entity.setPhone(ParamDecryptUtil.encryptField(dto.getPhone(), "手机号", true));
        entity.setEmail(ParamDecryptUtil.encryptField(dto.getEmail(), "邮箱", true));
        entity.setIdNumber(ParamDecryptUtil.encryptField(dto.getIdNumber(), "身份证号", true));
        entity.setAddress(ParamDecryptUtil.encryptField(dto.getAddress(), "员工住址", true));
        entity.setEmerPhone(ParamDecryptUtil.encryptField(dto.getEmerPhone(), "紧急联系人手机号", true));
        return entity;
    }

    // 账户信息处理（抽取方法）
    private MarketEmpAccounts processAccountInfo(MarketEmployeesVo dto) {
        MarketEmpAccounts account = BeanUtil.copyProperties(dto, MarketEmpAccounts.class);

        // 字段加密 true-加密 false-解密
        account.setAccountNumber(ParamDecryptUtil.encryptField(dto.getAccountNumber(), "银行账号", true));
        account.setAlipayAccount(ParamDecryptUtil.encryptField(dto.getAlipayAccount(), "支付宝账号", true));
        return account;
    }

    // 员工实体校验
    private void validateEmployee(MarketEmployeesVo employee) {
        if (StringUtils.isBlank(employee.getIdNumber())) {
            throw new ApiException("身份证号不能为空");
        }
        if (StringUtils.isBlank(employee.getPhone())) {
            throw new ApiException("手机号不能为空");
        }
        if (StringUtils.isBlank(employee.getAccountNumber())) {
            throw new ApiException("银行账号不能为空");
        }
        if (StringUtils.isBlank(employee.getAlipayAccount())) {
            throw new ApiException("支付宝账号不能为空");
        }
    }


    /**
     * 修改员工档案
     *
     * @param dto 员工档案
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateEmployees(MarketEmployeesVo dto) {

        // 参数校验前置
        if (dto == null) {
            log.error("员工信息录入失败：输入参数不能为空");
            throw new IllegalArgumentException("员工信息不能为空");
        }

        MarketEmployeesDto employees = selectEmployeesByEmployeeId(dto.getStaffId());
        if(employees ==  null){
            return ApiResult.error("员工信息不存在");
        }

        if (Objects.equals(employees.getPhone(), dto.getPhone())) {
            dto.setPhone(null);
        }
        if (Objects.equals(employees.getIdNumber(), dto.getIdNumber())) {
            dto.setIdNumber(null);
        }
        if (Objects.equals(employees.getAccountNumber(), dto.getAccountNumber())) {
            dto.setAccountNumber(null);
        }
        if (Objects.equals(employees.getAlipayAccount(), dto.getAlipayAccount())) {
            dto.setAlipayAccount(null);
        }

        // 参数唯一性校验
        try {
            String validated = validateUniqueFieldsAsync(dto);
            if (validated != null) {
                return ApiResult.error(validated);
            }
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
            // ================= 员工信息处理 =================
            MarketEmployees marketEmployees = processEmployeeInfo(dto);
            marketEmployees.setUpdateBy(SecurityUtils.getNickName());
            int employeesResult = marketEmployeesMapper.updateEmployees(marketEmployees);
            if (employeesResult <= 0) {
                log.error("账户信息修改失败");
                throw new ApiException("账户信息修改失败");
            }

            // ================= 账户信息处理 =================
            MarketEmpAccounts marketEmpAccounts = processAccountInfo(dto);
            marketEmpAccounts.setUpdateBy(SecurityUtils.getNickName());
            marketEmpAccounts.setEmployeeId(marketEmployees.getEmployeeId());
            int accountsResult = marketEmployeesMapper.updateMarketEmpAccounts(marketEmpAccounts);
            if (accountsResult <= 0) {
                log.error("账户信息修改失败");
                throw new ApiException("账户信息修改失败");
            }

            // ================= 附件信息处理 =================
            List<MarketEmpAttachments> marketEmpAttachmentsList = dto.getXgwcEmpAttachments();
            marketEmployeesMapper.deleteMarketEmpAttachments(dto.getEmployeeId());
            if(!CollectionUtils.isEmpty(marketEmpAttachmentsList)){
                for (MarketEmpAttachments empAttachments : marketEmpAttachmentsList) {
                    empAttachments.setEmployeeId(marketEmployees.getEmployeeId());
                    empAttachments.setUpdateBy(SecurityUtils.getNickName());
                    int updAttachmentsResult = marketEmployeesMapper.insertMarketEmpAttachments(empAttachments);
                    if (updAttachmentsResult <= 0) {
                        log.error("附件信息修改失败");
                        throw new ApiException("附件信息修改失败");
                    }
                }
            }

            log.info("员工信息修改成功 ID:[{}] 姓名:[{}]",
                    marketEmployees.getStaffId(),
                    dto.getName());
            return ApiResult.ok();
        } catch (Exception e) {  // 全局异常捕获
            log.error("系统异常 员工:[{}] 错误信息:{}",
                    dto.getStaffId(), e.getMessage(), e);
            throw new ApiException("系统处理异常");
        }
    }

    @Override
    public int resignationsEmployees(MarketEmployeesVo employees) {
        return 0;
    }
}
