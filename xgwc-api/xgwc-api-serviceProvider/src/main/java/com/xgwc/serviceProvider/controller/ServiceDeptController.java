package com.xgwc.serviceProvider.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.serviceProvider.entity.dto.ServiceDeptDto;
import com.xgwc.serviceProvider.entity.param.ServiceDeptParam;
import com.xgwc.serviceProvider.entity.vo.ServiceDeptInfo;
import com.xgwc.serviceProvider.service.ServiceDeptService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: -parent
 * @BelongsPackage: com..order.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 服务商部门管理
 */
@RestController
@RequestMapping("/serviceDept")
@Slf4j
public class ServiceDeptController extends BaseController {

    @Resource
    private ServiceDeptService serviceDeptService;

    /**
     * @param serviceDeptParam 查询条件
     * @return 部门管理列表
     * 查询部门管理列表
     */
    @MethodDesc("查询部门管理列表")
    @PreAuthorize("@ss.hasPermission('serviceDept:serviceDept:list')")
    @PostMapping("/getServiceDeptList")
    public ApiResult<ServiceDeptInfo> getServiceDeptList(@RequestBody ServiceDeptParam serviceDeptParam) {
        startPage();
        return getDataTable(serviceDeptService.getServiceDeptList(serviceDeptParam));
    }

    /**
     * 查询部门管理树
     *
     * @return 部门管理列表
     */
    @GetMapping("/getServiceDeptDropDown")
    public ApiResult getServiceDeptListInfo() {
        try {
            ServiceDeptParam serviceDeptParam = new ServiceDeptParam();
            serviceDeptParam.setStatus(0);
            List<ServiceDeptInfo> result = serviceDeptService.getServiceDeptList(serviceDeptParam);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取服务商部门管理树失败", e);
            return ApiResult.error("获取服务商部门管理树失败");
        }
    }

    /**
     * @param serviceDeptDto 新增部门管理信息
     * @return 插入结果
     * 新增部门管理信息
     */
    @MethodDesc("新增部门管理信息")
    @Submit(fileds = "userId")
    @PreAuthorize("@ss.hasPermission('serviceDept:serviceDept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveServiceDept")
    public ApiResult saveServiceDept(@RequestBody @Valid ServiceDeptDto serviceDeptDto) {
        return serviceDeptService.saveServiceDept(serviceDeptDto);
    }

    /**
     * @param deptId 部门管理id
     * @return 部门管理信息
     * 根据id查询部门管理信息
     */
    @MethodDesc("根据id查询部门管理信息")
    @PreAuthorize("@ss.hasPermission('serviceDept:serviceDept:query')")
    @GetMapping("/getServiceDeptById/{deptId}")
    public ApiResult getServiceDeptById(@PathVariable("deptId") Long deptId) {
        return serviceDeptService.getServiceDeptById(deptId);
    }

    /**
     * @param serviceDeptDto 修改信息
     * @return 修改结果
     * 修改部门管理信息
     */
    @MethodDesc("修改部门管理信息")
    @Submit(fileds = "userId")
    @PreAuthorize("@ss.hasPermission('serviceDept:serviceDept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateServiceDept")
    public ApiResult updateServiceDeptById(@RequestBody ServiceDeptDto serviceDeptDto) {
        return serviceDeptService.updateServiceDeptById(serviceDeptDto);
    }

    /**
     * @param deptId 部门id
     * @return 品牌信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('serviceDept:serviceDept:editStatusById')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "deptId") Integer deptId,
                                      @RequestParam(value = "status") Integer status) {
        return serviceDeptService.updateStatusById(deptId, status);
    }

    /**
     * 查询回显部门详情信息-负责人、助理、是否排班
     *
     * @param deptId 部门id
     * @return 部门详情信息-负责人、助理、是否排班
     */
    @MethodDesc("查询回显部门详情信息-负责人、助理、是否排班")
    @PreAuthorize("@ss.hasPermission('serviceDept:serviceDept:query')")
    @GetMapping("/getXgwcBrandDeptInfo")
    public ApiResult getXgwcDeptStaffInfo(@RequestParam(value = "deptId") Integer deptId) {
        return serviceDeptService.getXgwcDeptStaffInfo(deptId);
    }

    /**
     * 修改部门详情信息-负责人、助理、是否排班
     *
     * @param serviceDeptDto 部门详情信息-负责人、助理、是否排班
     * @return 部门详情信息-负责人、助理、是否排班
     */
    @MethodDesc("修改部门详情信息-负责人、助理、是否排班")
    @PreAuthorize("@ss.hasPermission('serviceDept:serviceDept:edit')")
    @PostMapping("/getXgwcDeptStaffSchedule")
    public ApiResult getXgwcDeptStaffSchedule(@RequestBody ServiceDeptDto serviceDeptDto) {
        return serviceDeptService.updateXgwcDeptStaffInfo(serviceDeptDto);
    }

}
