<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.activiti.dao.FlowHistoryMapper">
    

    <select id="selectListByExecutionId" parameterType="Long" resultType="com.xgwc.activiti.entity.FlowHistory">
        select *from (
         SELECT
             id,
             task_id_,
             execution_id_,
             task_name,
             task_type,
             create_time,
             flow_tables,
             deal_time,
             deal_used,
             assignees,
             signature,
             status
         FROM
             xgwc_flow_history
         WHERE
             execution_id_ =  #{executionId}
         union all
         select
             null id,
             task_id_,
             execution_id_,
             task_name,
             task_type,
             create_time,
             flow_tables,
             null deal_time,
             null deal_used,
             null assignees,
             null signature,
             null status
         from xgwc_flow_task where execution_id_ =  #{executionId}
     ) t
    order by t.create_time, t.task_type
    </select>


    <select id="selectFlowHistoryLastByExecutionId" parameterType="Long" resultType="com.xgwc.activiti.entity.FlowHistory">
        SELECT
            id,
            task_id_,
            execution_id_,
            task_name,
            task_type,
            create_time,
            flow_tables,
            deal_time,
            deal_used,
            deal_user_id,
            assignees,
            signature,
            status
        FROM
            xgwc_flow_history
        WHERE
            execution_id_ =  #{executionId}
            and task_type = 1
        order by create_time desc
        limit 1
    </select>

    <insert id="insertFlowHistory" parameterType="com.xgwc.activiti.entity.FlowHistory">
        insert into xgwc_flow_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskId != null">task_id_,</if>
            <if test="executionId != null">execution_id_,</if>
            <if test="taskName != null">task_name,</if>
            <if test="taskType != null">task_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="flowTables != null">flow_tables,</if>
            <if test="dealTime != null">deal_time,</if>
            <if test="dealUsed != null">deal_used,</if>
            <if test="dealUserId != null">deal_user_id,</if>
            <if test="assignees != null">assignees,</if>
            <if test="signature != null">signature,</if>
            <if test="childNode != null">child_node,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="executionId != null">#{executionId},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="flowTables != null">#{flowTables},</if>
            <if test="dealTime != null">#{dealTime},</if>
            <if test="dealUsed != null">#{dealUsed},</if>
            <if test="dealUserId != null">#{dealUserId},</if>
            <if test="assignees != null">#{assignees},</if>
            <if test="signature != null">#{signature},</if>
            <if test="childNode != null">#{childNode},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

</mapper>