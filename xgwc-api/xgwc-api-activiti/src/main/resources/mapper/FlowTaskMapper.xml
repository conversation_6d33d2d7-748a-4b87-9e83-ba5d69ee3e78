<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.activiti.dao.FlowTaskMapper">
    

    <sql id="selectFlowTaskVo">
        SELECT
            task_id_,
            execution_id_,
            task_name,
            task_type,
            create_time,
            flow_tables,
            child_node,
            backable,
            signable,
            assignable,
            multiInstance_approval_type,
            flow_node_self_auditor_type,
            flow_node_no_auditor_type
        FROM
            xgwc_flow_task
    </sql>

    <select id="selectFlowTaskList" parameterType="com.xgwc.activiti.entity.vo.FlowTaskVo" resultType="com.xgwc.activiti.entity.dto.FlowTaskQueryDto">
        select
            t.task_id_,
            t.execution_id_,
            t.task_name,
            t.task_type,
            e.title,
            e.business_key,
            e.brand_name,
            e.franchise_name,
            e.dept_name,
            e.status,
            e.flow_value,
            e.flow_name,
            e.next_assignees,
            e.create_time,
            e.create_name
        from
        xgwc_flow_task t
        left join xgwc_flow_execution e on t.execution_id_ = e.id
        <where>
            <if test="flowValue != null and flowValue != ''" >
                and e.flow_value = #{flowValue}
            </if>
            <if test="franchiseId != null and franchiseId != ''" >
                and e.franchise_id = #{franchiseId}
            </if>
            <if test="brandId != null and brandId != ''" >
                and e.brand_id = #{brandId}
            </if>
            <if test="userType != null">
                <if test="userType != 2 and userType != 5" >
                    and exists ( select 1 from xgwc_flow_task_assignees ta where t.task_id_ = ta.task_id_ and ta.assignees = #{userId})
                </if>
                <if test="userType == 2 or userType == 5" >
                    <if test="deptId != null and deptId != ''" >
                        and e.dept_id = #{deptId}
                    </if>
                    and exists (
                    select 1 from xgwc_flow_task_assignees ta
                    left join ( select '2' assignee_type, 0 layer_type,0 dept_id, id user_id from sys_user_middle where user_type = 2 ) u
                        on ta.assignee_type = u.assignee_type and ta.layer_type = u.layer_type
                    left join ( select '2' assignee_type, 1 layer_type,dept_id, bind_user_id from xgwc_franchise_staff where is_principal = 0 ) u2
                        on ta.assignee_type = u2.assignee_type and ta.layer_type = u2.layer_type
                    left join ( select '2' assignee_type, 2 layer_type,dept_id, bind_user_id from xgwc_franchise_staff where is_schedule = 0 ) u3
                        on ta.assignee_type = u3.assignee_type and ta.layer_type = u3.layer_type
                    where  t.task_id_ = ta.task_id_
                    and (
                        ta.assignees = #{userId}
                        or u.user_id = #{userId}
                        or ( u2.bind_user_id = #{userId} and e.dept_id = u2.dept_id)
                        or ( u3.bind_user_id = #{userId} and e.dept_id = u3.dept_id)
                        )
                    )
                </if>
            </if>
        </where>
        order by e.create_time desc
    </select>

    <select id="selectFlowExecutionList" parameterType="com.xgwc.activiti.entity.vo.FlowTaskQueryVo" resultType="com.xgwc.activiti.entity.dto.FlowTaskQueryDto">
        select t.*
        from (
                 select
                     t.task_id_,
                     e.id execution_id_,
                     t.task_name,
                     t.task_type,
                     e.title,
                     e.business_key,
                     e.brand_id,
                     e.brand_name,
                     e.franchise_id,
                     e.franchise_name,
                     e.dept_name,
                     e.status,
                     e.flow_value,
                     e.flow_name,
                     e.next_assignees,
                     e.create_time,
                     e.create_by,
                     e.create_name,
                     e.end_time
                 from
                     xgwc_flow_execution e
                 left join xgwc_flow_task t on t.execution_id_ = e.id
                 where e.create_by = #{userId}
                 union all
                 select
                     null as task_id_,
                     t.execution_id as execution_id_,
                     null as task_name,
                     null as task_type,
                     t.order_no as title,
                     t.id as business_key,
                     t.brand_id,
                     xbo.company_name as brand_name,
                     t.franchise_id,
                     f.franchise_name as franchise_name,
                     fd.dept_name as dept_name,
                     t.apply_status as status,
                     "AfterSales" as flow_value,
                     "售后流程" as flow_name,
                     null as next_assignees,
                     t.create_time,
                     t.apply_user_id as create_by,
                     t.apply_user_name as create_name,
                     null as end_time
                 from order_refund_apply t
                  left join xgwc_brand_owner xbo on xbo.brand_id = t.brand_id
                  left join franchise f on f.id = t.franchise_id
                  left join xgwc_franchise_staff xfs on xfs.bind_user_id = t.apply_user_id
                  left join franchise_dept fd on fd.dept_id = xfs.dept_id
                 where t.apply_user_id = #{userId}
                   and t.after_check_status in (0,2)
                   and t.execution_id is null
             ) as t
        <where>
            <if test="flowValue != null and flowValue != ''" >
                and t.flow_value = #{flowValue}
            </if>
            <if test="flowName != null and flowName != ''" >
                and t.flow_name like concat('%', #{flowName}, '%')
            </if>
            <if test="franchiseId != null" >
                and t.franchise_id = #{franchiseId}
            </if>
            <if test="brandId != null" >
                and t.flow_name = #{brandId}
            </if>
            <if test="status != null" >
                and t.status = #{status}
            </if>
            <if test="titles != null" >
                and  (
                    t.business_key like concat('%', #{titles}, '%')
                    or t.title like concat('%', #{titles}, '%')
                    or EXISTS (
                        select 1 from xgwc_flow_exec_data d
                        where  t.execution_id_ = d.execution_id_
                        and d.key_ = 'customerNo'
                        and d.value_ like concat('%', #{titles}, '%')
                    )
                )
            </if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectFlowTaskByTaskId" parameterType="Long" resultType="com.xgwc.activiti.entity.dto.FlowTaskDto">
        <include refid="selectFlowTaskVo"/>
        where task_id_ = #{taskId}
    </select>

    <insert id="insertFlowTask" parameterType="com.xgwc.activiti.entity.FlowTask">
        insert into xgwc_flow_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id_,</if>
            <if test="executionId != null">execution_id_,</if>
            <if test="taskName != null">task_name,</if>
            <if test="taskType != null">task_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="flowTables != null">flow_tables,</if>
            <if test="childNode != null">child_node,</if>
            <if test="backable != null">backable,</if>
            <if test="signable != null">signable,</if>
            <if test="assignable != null">assignable,</if>
            <if test="multiinstanceApprovalType != null">multiInstance_approval_type,</if>
            <if test="flowNodeSelfAuditorType != null">flow_node_self_auditor_type,</if>
            <if test="flowNodeNoAuditorType != null">flow_node_no_auditor_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="executionId != null">#{executionId},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="flowTables != null">#{flowTables},</if>
            <if test="childNode != null">#{childNode},</if>
            <if test="backable != null">#{backable},</if>
            <if test="signable != null">#{signable},</if>
            <if test="assignable != null">#{assignable},</if>
            <if test="multiinstanceApprovalType != null">#{multiinstanceApprovalType},</if>
            <if test="flowNodeSelfAuditorType != null">#{flowNodeSelfAuditorType},</if>
            <if test="flowNodeNoAuditorType != null">#{flowNodeNoAuditorType},</if>
         </trim>
    </insert>

    <insert id="insertAssigneesList" parameterType="com.xgwc.activiti.entity.FlowTaskAssignees">
        insert into xgwc_flow_task_assignees(task_id_,assignee_type,assignees,layer_type)
        values
        <foreach item="item" collection="list" separator=",">
            ( #{item.taskId},#{item.assigneeType},#{item.assignees}, #{item.layerType} )
        </foreach>
    </insert>

    <select id="selectAssigneesList" resultType="com.xgwc.activiti.entity.FlowTaskAssignees">
        select *from xgwc_flow_task_assignees
        where task_id_ = #{taskId}
    </select>

    <delete id="deleteFlowTaskByTaskId" parameterType="Long">
        delete from xgwc_flow_task where task_id_ = #{taskId}
    </delete>

    <delete id="deleteFlowTaskAssigneesByTaskId" parameterType="Long">
        delete from xgwc_flow_task_assignees where task_id_ = #{taskId}
    </delete>


</mapper>