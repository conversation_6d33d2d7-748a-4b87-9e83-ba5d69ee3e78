<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.activiti.dao.FlowExecutionDataMapper">
    


    <select id="selectListByExecutionId"  resultType="com.xgwc.activiti.entity.FlowExecutionData">
        select key_, value_ from xgwc_flow_exec_data
        where execution_id_ = #{executionId}
    </select>

    <select id="selectAfterSalesExecData" resultType="com.xgwc.activiti.entity.AfterSalesExecData">
        select key_name, key_value from xgwc_after_sales_exec_data
        where business_id = #{businessId}
    </select>


    <insert id="insertFlowExecutionData" parameterType="com.xgwc.activiti.entity.FlowExecutionData">
        insert into xgwc_flow_exec_data
            (execution_id_, key_, value_)
        values
        <foreach item="item" collection="list" separator="," >
            (#{item.executionId},#{item.key},#{item.value})
        </foreach>
    </insert>


</mapper>