<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.activiti.dao.FlowTypeMapper">
    

    <sql id="selectFlowTypeVo">
        select
            ft.id,
            ft.flow_name,
            ft.flow_value,
            ft.flow_type,
            ft.create_by,
            ft.create_time,
            ft.update_by,
            ft.update_time,
            ifnull( fd.version_, 0 ) version
        from xgwc_flow_type ft
        left join (
            select
                flow_value,
                MAX(version_) version_
            from
                xgwc_flow_def
            <where>
                <if test="brandId != null">
                    and brand_id = #{brandId}
                </if>
            </where>
            group by
                flow_value
        ) fd on ft.flow_value = fd.flow_value
    </sql>

    <select id="selectFlowTypeList" parameterType="com.xgwc.activiti.entity.vo.FlowTypeQueryVo" resultType="com.xgwc.activiti.entity.dto.FlowTypeDto">
        <include refid="selectFlowTypeVo"/>
        <where>
            <if test="id != null "> and ft.id = #{id}</if>
            <if test="flowName != null  and flowName != ''"> and ( ft.flow_name like concat('%', #{flowName}, '%') or  ft.flow_value like concat('%', #{flowName}, '%'))</if>
            <if test="flowValue != null  and flowValue != ''"> and ft.flow_value = #{flowValue}</if>
            <if test="flowType != null  and flowType != ''"> and ft.flow_type = #{flowType}</if>
        </where>
    </select>
    
    <select id="selectFlowTypeById" parameterType="Long" resultType="com.xgwc.activiti.entity.dto.FlowTypeDto">
        <include refid="selectFlowTypeVo"/>
        where  ft.id = #{id}
    </select>

    <select id="selectFlowTypeByFlowValue" parameterType="String" resultType="com.xgwc.activiti.entity.dto.FlowTypeDto">
        <include refid="selectFlowTypeVo"/>
        where  ft.flow_value = #{flowValue}
    </select>

    <select id="selectFlowDataByTypeId" resultType="com.xgwc.activiti.entity.dto.FlowDataDto">
        select
            id,
            type_id,
            label,
            placeholder,
            type,
            name,
            focus,
            required,
            summary,
            options
        from
            xgwc_flow_data
        where
            type_id = #{id}
        order by order_num
    </select>

    <insert id="insertFlowType" parameterType="com.xgwc.activiti.entity.FlowType">
        insert into xgwc_flow_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="flowName != null">flow_name,</if>
            <if test="flowValue != null">flow_value,</if>
            <if test="flowType != null">flow_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="flowName != null">#{flowName},</if>
            <if test="flowValue != null">#{flowValue},</if>
            <if test="flowType != null">#{flowType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
        <selectKey keyProperty="id" resultType="long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <insert id="insertFlowData">
        insert into xgwc_flow_data
            (type_id,label,placeholder,type,name,focus,required,summary,options,order_num)
        values
        <foreach item="item" index="index" collection="list" separator=",">
           ( #{flowTypeId},#{item.label},#{item.placeholder},#{item.type},#{item.name},#{item.focus},#{item.required},#{item.summary},#{item.options},#{index})
        </foreach>
    </insert>

    <update id="updateFlowType" parameterType="com.xgwc.activiti.entity.FlowType">
        update xgwc_flow_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="flowName != null">flow_name = #{flowName},</if>
            <if test="flowValue != null">flow_value = #{flowValue},</if>
            <if test="flowType != null">flow_type = #{flowType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteFlowTypeById" parameterType="Long">
        update xgwc_flow_type set is_del = 1 where id = #{id}
    </update>


    <delete id="deleteFlowDataByTypeId">
        delete from xgwc_flow_data where type_id = #{id}
    </delete>

    <update id="deleteFlowTypeByIds" parameterType="String">
        update xgwc_flow_type set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>