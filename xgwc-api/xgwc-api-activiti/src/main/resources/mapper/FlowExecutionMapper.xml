<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.activiti.dao.FlowExecutionMapper">
    

    <sql id="selectFlowExecutionVo">
        select id, def_id, def_data, title, business_key, brand_id, brand_name, franchise_id, franchise_name, dept_id, dept_name, version_, status, create_time,create_by,create_name, end_time, cancelable, flow_value, flow_name from xgwc_flow_execution
    </sql>

    <select id="selectFlowExecutionList" parameterType="com.xgwc.activiti.entity.vo.FlowExecutionQueryVo" resultType="com.xgwc.common.entity.dto.FlowExecutionDto">
        <include refid="selectFlowExecutionVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="defId != null "> and def_id = #{defId}</if>
            <if test="defData != null  and defData != ''"> and def_data like concat('%', #{defData}, '%')</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="businessKey != null  and businessKey != ''"> and business_key like concat('%', #{businessKey}, '%')</if>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="brandName != null  and brandName != ''"> and brand_name like concat('%', #{brandName}, '%')</if>
            <if test="franchiseId != null "> and franchise_id = #{franchiseId}</if>
            <if test="franchiseName != null  and franchiseName != ''"> and franchise_name like concat('%', #{franchiseName}, '%')</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="version != null "> and version_ = #{version}</if>
            <if test="status != null  and status != ''"> and status like concat('%', #{status}, '%')</if>
            <if test="createTimeStart != null and createTimeEnd != null">
                and create_time between #{createTimeStart} and #{createTimeEnd}
            </if>
            <if test="endTimeStart != null and endTimeEnd != null">
                and end_time between #{endTimeStart} and #{endTimeEnd}
            </if>
            <if test="cancelable != null "> and cancelable = #{cancelable}</if>
            <if test="flowValue != null  and flowValue != ''"> and flow_value like concat('%', #{flowValue}, '%')</if>
            <if test="flowName != null  and flowName != ''"> and flow_name like concat('%', #{flowName}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
        </where>
    </select>
    
    <select id="selectFlowExecutionById" parameterType="Long" resultType="com.xgwc.common.entity.dto.FlowExecutionDto">
        <include refid="selectFlowExecutionVo"/>
        where id = #{id}
    </select>

    <select id="selectFlowWorkByExecId" parameterType="Long" resultType="com.xgwc.activiti.entity.dto.FlowWorkDto">
        select
            id,
            title,
            business_key,
            brand_name,
            franchise_name,
            dept_name,
            status,
            create_time,
            create_name,
            end_time,
            flow_value,
            flow_name
        from
            xgwc_flow_execution
        where id = #{id}
    </select>
    <select id="selectFlowWorkByBizId" parameterType="Long" resultType="com.xgwc.activiti.entity.dto.FlowWorkDto">
        select
            id,
            title,
            business_key,
            brand_name,
            franchise_name,
            dept_name,
            status,
            create_time,
            create_name,
            end_time,
            flow_value,
            flow_name
        from
            xgwc_flow_execution
        where business_key = #{bizId}
    </select>
    <select id="selectFlowWorkIngByBizId" resultType="java.lang.Integer">
        select
            count(1)
        from
            xgwc_flow_execution
        where business_key = #{bizId}
        and flow_value = #{flowValue}
        and status = 'ING'
    </select>




    <insert id="insertFlowExecution" parameterType="com.xgwc.activiti.entity.FlowExecution">
        insert into xgwc_flow_execution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="defId != null">def_id,</if>
            <if test="defData != null">def_data,</if>
            <if test="title != null">title,</if>
            <if test="businessKey != null">business_key,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="franchiseName != null">franchise_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="version != null">version_,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createName != null">create_name,</if>
            <if test="endTime != null">end_time,</if>
            <if test="cancelable != null">cancelable,</if>
            <if test="flowValue != null">flow_value,</if>
            <if test="flowName != null">flow_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="defId != null">#{defId},</if>
            <if test="defData != null">#{defData},</if>
            <if test="title != null">#{title},</if>
            <if test="businessKey != null">#{businessKey},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="franchiseName != null">#{franchiseName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="version != null">#{version},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createName != null">#{createName},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="cancelable != null">#{cancelable},</if>
            <if test="flowValue != null">#{flowValue},</if>
            <if test="flowName != null">#{flowName},</if>
         </trim>
    </insert>

    <update id="updateFlowExecution" parameterType="com.xgwc.activiti.entity.FlowExecution">
        update xgwc_flow_execution
        set status = #{status},end_time = #{endTime}
        where id = #{id}
    </update>

    <update id="setNextAssignees">
        update xgwc_flow_execution
        set next_assignees = #{assignees}
        where id = #{id}
    </update>

    <update id="deleteFlowExecutionById" parameterType="Long">
        update xgwc_flow_execution set is_del = 1 where id = #{id}
    </update>

    <update id="deleteFlowExecutionByIds" parameterType="String">
        update xgwc_flow_execution set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>