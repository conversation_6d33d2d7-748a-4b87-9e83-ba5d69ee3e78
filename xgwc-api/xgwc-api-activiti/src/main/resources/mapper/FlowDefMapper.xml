<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.activiti.dao.FlowDefMapper">
    

    <sql id="selectFlowDefVo">
        select brand_id, brand_name, create_by, create_time, flow_name, flow_value, id, status, version_ from xgwc_flow_def
    </sql>

    <select id="selectFlowDefList" parameterType="com.xgwc.activiti.entity.vo.FlowDefQueryVo" resultType="com.xgwc.activiti.entity.dto.FlowDefDto">
        <include refid="selectFlowDefVo"/>
        <where>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="flowName != null  and flowName != ''"> and flow_name like concat('%', #{flowName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by flow_value, version_ desc
    </select>

    <select id="selectFlowDefById" parameterType="Long" resultType="com.xgwc.activiti.entity.dto.FlowDefDto">
        select brand_id, brand_name, create_by, create_time, definition_, flow_name, flow_value, id, status, version_ from xgwc_flow_def
        where id = #{id}
    </select>

    <select id="selectFlowDefByFlowValue" resultType="com.xgwc.activiti.entity.dto.FlowDefDto">
        select brand_id, brand_name, create_by, create_time, definition_, flow_name, flow_value, id, status, version_ from xgwc_flow_def
        where flow_value = #{flowValue} and status = 0 and  brand_id = #{brandId}
        order by version_ desc
        limit 1
    </select>


    <select id="maxVersionByFlowValue" resultType="java.lang.Integer">
        select max(version_) from xgwc_flow_def
        where flow_value = #{flowValue} and  brand_id = #{brandId}
    </select>

    <select id="selectBrandNameByBrandId" parameterType="Long" resultType="String">
        select
            ifnull(company_simple_name, company_name) brand_name
        from xgwc_brand_owner
        where brand_id = #{brandId}
        limit 1
    </select>

    <insert id="insertFlowDef" parameterType="com.xgwc.activiti.entity.FlowDef">
        insert into xgwc_flow_def
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brandId != null">brand_id,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="definition != null">definition_,</if>
            <if test="flowName != null">flow_name,</if>
            <if test="flowValue != null">flow_value,</if>
            <if test="id != null">id,</if>
            <if test="status != null">status,</if>
            <if test="version != null">version_,</if>
            <if test="cancelable != null">cancelable,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brandId != null">#{brandId},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="definition != null">#{definition},</if>
            <if test="flowName != null">#{flowName},</if>
            <if test="flowValue != null">#{flowValue},</if>
            <if test="id != null">#{id},</if>
            <if test="status != null">#{status},</if>
            <if test="version != null">#{version},</if>
            <if test="cancelable != null">#{cancelable},</if>
         </trim>
    </insert>


    <update id="updateStatusById">
        update xgwc_flow_def
        set status = #{status}
        where id = #{id}
    </update>

    <update id="updateStatusByVersion">
        update xgwc_flow_def
        set status = 1
        where version_ &lt; #{version}
            and brand_id = #{brandId}
            and flow_value = #{flowValue}
    </update>

    <delete id="deleteFlowDefByIds" parameterType="String">
        delete  from xgwc_flow_def where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getBrandStaff" parameterType="Long" resultType="com.xgwc.activiti.entity.vo.UserVo">
        select
            ifnull(s.stage_name,s.name) name,
            s.bind_user_id user_id,
            s.dept_id,
            d.dept_name
        from xgwc_staff s
        left join xgwc_brand_dept d on s.dept_id = d.dept_id
        where s.brand_id = #{brandId}
        and s.status = 0
        and ifnull(s.bind_user_id, 0) > 0
    </select>
    <select id="getBrandStaffByUseId" parameterType="Long" resultType="com.xgwc.activiti.entity.vo.UserVo">
        select
            ifnull( s.stage_name, s.name ) name,
            s.bind_user_id user_id,
            s.dept_id,
            d.dept_name ,
            ifnull(bo.company_simple_name, bo.company_name) company_name
        from
            xgwc_staff s
        left join xgwc_brand_dept d on s.dept_id = d.dept_id
        left join xgwc_brand_owner bo on s.brand_id = bo.brand_id
        where s.bind_user_id = #{userId}
        and s.status = 0
    </select>



    <select id="getServiceStaff" parameterType="Long" resultType="com.xgwc.activiti.entity.vo.UserVo">
        select
            ifnull(s.stage_name,s.name) name,
            s.bind_user_id user_id,
            s.dept_id,
            d.dept_name
        from xgwc_service_staff s
        left join xgwc_service_authorize a on s.service_owner_id = a.service_id
        left join service_dept d on s.dept_id = d.dept_id
        where a.brand_id = #{brandId}
          and s.status = 0
          and a.status = 0
          and ifnull(s.bind_user_id, 0) > 0
    </select>
    <select id="getServiceStaffByUseId" parameterType="Long" resultType="com.xgwc.activiti.entity.vo.UserVo">
        select
            ifnull(s.stage_name,s.name) name,
            s.bind_user_id user_id,
            s.dept_id,
            d.dept_name,
            so.company_name
        from xgwc_service_staff s
        left join service_dept d on s.dept_id = d.dept_id
        left join xgwc_service_owner so on s.service_owner_id = so.id
        where s.bind_user_id = #{userId}
          and s.status = 0
    </select>


    <select id="getBrandAdmin" parameterType="Long" resultType="com.xgwc.activiti.entity.vo.UserVo">
        select
            ifnull( s.stage_name, s.name ) name,
            s.bind_user_id user_id,
            d.dept_id,
            d.dept_name,
            ifnull(bo.company_simple_name, bo.company_name) company_name
        from
            xgwc_staff s
        left join sys_user_middle um on s.bind_user_id = um.id
        left join xgwc_brand_dept d on s.dept_id = d.dept_id
        left join xgwc_brand_owner bo on s.brand_id = bo.brand_id
        where
            um.user_type = 1
          and s.status = 0
          and  s.brand_id = #{brandId}
    </select>

    <select id="getFranchiseAdmin" parameterType="Long" resultType="com.xgwc.activiti.entity.vo.UserVo">
        select
            ifnull( fs.stage_name, fs.name ) name,
            fs.bind_user_id user_id,
            fs.dept_id,
            fd.dept_name,
            f.franchise_name company_name
        from
            xgwc_franchise_staff fs
        left join sys_user_middle um on fs.bind_user_id = um.id
        left join franchise f on fs.franchise_id = f.id
        left join franchise_dept fd on fs.dept_id = fd.dept_id
        where
            um.user_type = 2
            and fs.status = 0
            and fs.franchise_id = #{franchiseid}
    </select>

    <select id="getFranchisePrincipal" parameterType="Long" resultType="com.xgwc.activiti.entity.vo.UserVo">
        select
            ifnull( fs.stage_name, fs.name ) name,
            fs.bind_user_id user_id,
            fs.dept_id,
            fd.dept_name,
            f.franchise_name company_name
        from
            xgwc_franchise_staff fs
        left join franchise f on fs.franchise_id = f.id
        left join franchise_dept fd on fs.dept_id = fd.dept_id
        where  fs.dept_id = #{deptid}
          and fs.is_principal = 0
          and fs.status = 0
          and ifnull(fs.bind_user_id, 0) > 0
    </select>
    <select id="getFranchiseSchedule" parameterType="Long" resultType="com.xgwc.activiti.entity.vo.UserVo">
        select
            ifnull( fs.stage_name, fs.name ) name,
            fs.bind_user_id user_id,
            fs.dept_id,
            fd.dept_name,
            f.franchise_name company_name
        from
            xgwc_franchise_staff fs
                left join franchise f on fs.franchise_id = f.id
                left join franchise_dept fd on fs.dept_id = fd.dept_id
        where  fs.dept_id = #{deptid}
          and fs.is_schedule = 0
          and fs.status = 0
          and ifnull(fs.bind_user_id, 0) > 0
    </select>

</mapper>