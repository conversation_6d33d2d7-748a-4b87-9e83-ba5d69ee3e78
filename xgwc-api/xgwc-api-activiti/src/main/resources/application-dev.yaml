management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
spring:
  cloud:
    nacos:
      discovery: # 【配置中心】配置项
        namespace: oyh # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
  security:
    user:
      name: admin
      password: U4eQZbFLqdw9eGMC1aFq0mP4iJtIaFo8
      roles: ACTUATOR_ADMIN
  #连接池配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
    username: root
    password: ",uPkdLhlC1,t"
  data:
    redis:
      host: *************
      port: 6379
      database: 0
      password: TqbZ4tpJA153LN5Boax4
    lettuce:
      pool:
        max-idle: 20
        min-idle: 5
        max-active: 50
        adaptiveRefreshTriggersTimeout: 5
        enablePeriodicRefresh: 10
#mybatis配置
mybatis:
  type-aliases-package: com.xgwc.user.entity
  mapper-locations: classpath:mapper/*.xml
logging:
  level:
    com.xgwc.order.dao: debug
