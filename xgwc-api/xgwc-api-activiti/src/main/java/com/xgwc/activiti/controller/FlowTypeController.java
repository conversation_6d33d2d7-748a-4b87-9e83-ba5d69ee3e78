package com.xgwc.activiti.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.activiti.entity.vo.FlowTypeVo;
import com.xgwc.activiti.entity.dto.FlowTypeDto;
import com.xgwc.activiti.entity.vo.FlowTypeQueryVo;
import com.xgwc.activiti.service.IFlowTypeService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/flow/type")
public class FlowTypeController extends BaseController {
    @Autowired
    private IFlowTypeService flowTypeService;

    /**
     * 查询流程类型列表
     */
    @MethodDesc("查询流程类型列表")
    @GetMapping("/list")
    public ApiResult<FlowTypeDto> list(FlowTypeQueryVo flowType) {
        startPage();
        List<FlowTypeDto> list = flowTypeService.selectFlowTypeList(flowType);
        return getDataTable(list);
    }

    /**
     * 获取流程类型详细信息
     */
    @MethodDesc("获取流程类型详细信息")
    @GetMapping(value = "/{flowValue}")
    public ApiResult<FlowTypeDto> getInfo(@PathVariable("flowValue") String flowValue) {
        return success(flowTypeService.selectFlowTypeByFlowValue(flowValue));
    }

    /**
     * 新增流程类型
     */
    @MethodDesc("新增流程类型")
    @PreAuthorize("@ss.hasPermission('flow:type:add')")
    @Log(title = "流程类型", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody FlowTypeVo flowType) {
        return toAjax(flowTypeService.insertFlowType(flowType));
    }

    /**
     * 修改流程类型
     */
    @MethodDesc("修改流程类型")
    @PreAuthorize("@ss.hasPermission('flow:type:edit')")
    @Log(title = "流程类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody FlowTypeVo flowType) {
        return toAjax(flowTypeService.updateFlowType(flowType));
    }

    /**
     * 删除流程类型
     */
    @MethodDesc("删除流程类型")
    @PreAuthorize("@ss.hasPermission('flow:type:remove')")
    @Log(title = "流程类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(flowTypeService.deleteFlowTypeByIds(ids));
    }
}
