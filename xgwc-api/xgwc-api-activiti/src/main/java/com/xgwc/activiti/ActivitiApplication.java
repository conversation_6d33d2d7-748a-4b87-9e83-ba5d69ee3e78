package com.xgwc.activiti;

import com.xgwc.activiti.feign.api.ExecutionFeign;
import com.xgwc.order.feign.api.TaskOrderFeign;
import com.xgwc.user.feign.api.MessageFeign;
import com.xgwc.user.feign.api.UserDetailFeign;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;


@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.xgwc.*"})
@ComponentScan("com.xgwc")
@MapperScan("com.xgwc.activiti.dao")
@EnableFeignClients(clients = {UserDetailFeign.class, ExecutionFeign.class, MessageFeign.class, TaskOrderFeign.class})
@Slf4j
public class ActivitiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ActivitiApplication.class, args);
        log.info("项目启动成功!");
    }

}
