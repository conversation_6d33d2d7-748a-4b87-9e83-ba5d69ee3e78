package com.xgwc.activiti.controller;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.activiti.entity.vo.UserVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.activiti.entity.vo.FlowDefVo;
import com.xgwc.activiti.entity.dto.FlowDefDto;
import com.xgwc.activiti.entity.vo.FlowDefQueryVo;
import com.xgwc.activiti.service.IFlowDefService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/flow/def")
public class FlowDefController extends BaseController {
    @Autowired
    private IFlowDefService flowDefService;

    /**
     * 查询流程定义列表
     */
    @MethodDesc("查询流程定义列表")
    @PreAuthorize("@ss.hasPermission('flow:def:list')")
    @GetMapping("/list")
    public ApiResult<FlowDefDto> list(FlowDefQueryVo flowDef) {
        startPage();
        List<FlowDefDto> list = flowDefService.selectFlowDefList(flowDef);
        return getDataTable(list);
    }


    /**
     * 获取流程定义详细信息
     */
    @MethodDesc("获取流程定义详细信息")
    @GetMapping(value = "/{id}")
    public ApiResult<FlowDefDto> getInfo(@PathVariable("id") Long id) {
        return success(flowDefService.selectFlowDefById(id));
    }

    /**
     * 新增流程定义
     */
    @MethodDesc("新增流程定义")
    @PreAuthorize("@ss.hasPermission('flow:def:add')")
    @Log(title = "流程定义", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody JSONObject object) {
        JSONObject jsonObject = object.getJSONObject("workFlowDef");
        FlowDefVo flowDef = new FlowDefVo();
        flowDef.setDefinition(object.toJSONString());
        flowDef.setFlowName(jsonObject.getString("name"));
        flowDef.setFlowValue(jsonObject.getString("flowValue"));
        flowDef.setCancelable(jsonObject.getInteger("cancelable"));
        return toAjax(flowDefService.insertFlowDef(flowDef));
    }

    /**
     * 修改流程定义状态
     */
    @MethodDesc("修改流程定义")
    @PreAuthorize("@ss.hasPermission('flow:def:edit')")
    @Log(title = "流程定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody FlowDefVo flowDef) {
        return toAjax(flowDefService.updateStatusById(flowDef.getId(), flowDef.getStatus()));
    }

    /**
     * 删除流程定义
     */
    @MethodDesc("删除流程定义")
    @PreAuthorize("@ss.hasPermission('flow:def:remove')")
    @Log(title = "流程定义", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(flowDefService.deleteFlowDefByIds(ids));
    }


    @MethodDesc("查询品牌商员工")
    @GetMapping("/brand/staff")
    public ApiResult<List<UserVo>> getBrandStaff() {
        return success(flowDefService.getBrandStaff());
    }

    @MethodDesc("查询品牌商员工")
    @GetMapping("/service/staff")
    public ApiResult<List<UserVo>> getServiceStaff() {
        return success(flowDefService.getServiceStaff());
    }
}
