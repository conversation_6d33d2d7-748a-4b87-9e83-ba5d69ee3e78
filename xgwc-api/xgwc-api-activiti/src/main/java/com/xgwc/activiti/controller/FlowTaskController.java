package com.xgwc.activiti.controller;

import com.xgwc.activiti.entity.dto.FlowTaskQueryDto;
import com.xgwc.activiti.entity.dto.FlowWorkDto;
import com.xgwc.activiti.entity.vo.FlowTaskQueryVo;
import com.xgwc.activiti.entity.vo.FlowTaskVo;
import com.xgwc.activiti.feign.api.FlowTaskFeign;
import com.xgwc.activiti.feign.entity.FlowExecutionVo;
import com.xgwc.activiti.service.IFlowExecutionService;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/flow/task")
public class FlowTaskController extends BaseController implements FlowTaskFeign {

    @Autowired
    private IFlowExecutionService flowTaskService;

    /**
     * 查询流程任务列表
     */
    @MethodDesc("查询代办任务列表")
    @GetMapping("/list")
    public ApiResult<FlowTaskQueryDto> list(FlowTaskQueryVo flowTask) {
        startPage();
        List<FlowTaskQueryDto> list = flowTaskService.selectFlowTaskList(flowTask);
        return getDataTable(list);
    }

    @MethodDesc("查询我的申请任务列表")
    @GetMapping("/mine")
    public ApiResult<FlowTaskQueryDto> mine(FlowTaskQueryVo flowTask) {
        startPage();
        List<FlowTaskQueryDto> list = flowTaskService.selectFlowMineList(flowTask);
        return getDataTable(list);
    }

    /**
     * 获取流程任务详细信息
     */
    @MethodDesc("获取代办任务审核信息")
    @GetMapping(value = "/{taskId}")
    public ApiResult<FlowWorkDto> getInfo(@PathVariable("taskId") Long taskId) {
        return success(flowTaskService.selectFlowWorkByTaskId(taskId));
    }

    @MethodDesc("获取代办任务所有详细信息")
    @GetMapping(value = "/biz/{bizId}")
    public ApiResult<List<FlowWorkDto>> getInfoByBizId(@PathVariable("bizId") Long bizId) {
        return success(flowTaskService.selectFlowWorkByBizId(bizId));
    }

    @MethodDesc("获取代办任务详细信息")
    @GetMapping(value = "/exec/{execId}")
    public ApiResult<FlowWorkDto> getInfoByExecId(@PathVariable("execId") Long execId) {
        return success(flowTaskService.selectFlowWorkByExecId(execId));
    }

    @MethodDesc("审批流程")
    @Log(title = "流程任务", businessType = BusinessType.GRANT)
    @PutMapping
    public ApiResult review(@RequestBody FlowTaskVo flowTask) {
        return toAjax(flowTaskService.reviewFlowTask(flowTask));
    }


    @MethodDesc("撤销流程")
    @Log(title = "流程任务", businessType = BusinessType.UPDATE)
    @PutMapping("/withdraw/{taskId}")
    public ApiResult withdraw(@PathVariable("taskId") Long taskId) {
        return success(flowTaskService.withdraw(taskId));
    }


    @MethodDesc("终止流程")
    @PreAuthorize("@ss.hasPermission('flow:task:cancel')")
    @Log(title = "流程任务", businessType = BusinessType.CLEAN)
	@PutMapping("/cancel/{taskId}")
    public ApiResult cancel(@PathVariable("taskId") Long taskId) {
        return success(flowTaskService.cancel(taskId, false));
    }

    @PutMapping("/feignCancel/{taskId}")
    public ApiResult feignCancel(@PathVariable("taskId") Long taskId) {
        return success(flowTaskService.cancel(taskId, true));
    }

    @MethodDesc("启动流程")
    @Log(title = "流程任务", businessType = BusinessType.INSERT)
    @PostMapping("/start")
    public ApiResult start(@RequestBody FlowExecutionVo vo) {
        return success(flowTaskService.insertFlowExecution(vo));
    }

    @MethodDesc("系统自动流程")
    @Log(title = "流程任务", businessType = BusinessType.CLEAN)
    @PostMapping("/automatic")
    public ApiResult automatic(@RequestBody FlowExecutionVo vo) {
        return success(flowTaskService.automatic(vo));
    }
}
