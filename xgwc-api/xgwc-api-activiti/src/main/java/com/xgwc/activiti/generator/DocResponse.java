package com.xgwc.activiti.generator;



import java.util.ArrayList;
import java.util.List;

@lombok.Data
public class DocResponse  implements Doc {

    private String title;

    private int row = 0;
    private int col = 3;

    private List<ResponseData> datas;

    public void setDatas(ResponseData data) {
        if(this.datas == null) {
            this.datas = new ArrayList<>();
            this.datas.add(new ResponseData("字段名","字段类型","字段说明"));
            this.row++;
        }
        this.datas.add(data);
        this.row++;
    }

    @Override
    public String getTitle() {
        return title;
    }

    @Override
    public int getRow() {
        return row;
    }

    @Override
    public int getCol() {
        return col;
    }

    @Override
    public List<ResponseData> getDatas() {
        return datas;
    }
}

@lombok.Data
class ResponseData implements DocData {

    private String field;
    private String type;
    private String describe;

    public ResponseData(String field, String type,String describe) {
        this.field = field;
        this.type = type;
        this.describe = describe;
    }

    @Override
    public List<String> getData() {
        List<String> list = new ArrayList<>();
        list.add(field);
        list.add(type);
        list.add(describe);
        return list;
    }
}