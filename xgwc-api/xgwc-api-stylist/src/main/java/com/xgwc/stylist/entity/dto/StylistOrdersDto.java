package com.xgwc.stylist.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StylistOrdersDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long orderId;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("设计师id")
    private Long stylistId;

    @FieldDesc("设计师名称")
    private String stylistName;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String customerId;

    @FieldDesc("客户名称")
    @Excel(name = "客户名称")
    private String customerName;

    @FieldDesc("所属品牌商id")
    @Excel(name = "所属品牌商id")
    private Long brandOwnerId;

    @FieldDesc("品牌商名称")
    @Excel(name = "品牌商名称")
    private String brandOwnerName;

    @FieldDesc("佣金（保留两位小数点）")
    @Excel(name = "佣金（保留两位小数点）")
    private BigDecimal commission;

    @FieldDesc("接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接单时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderTime;

    @FieldDesc("约定初稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "约定初稿日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scheduledDate;

    @FieldDesc("订单状态（0-未发货、1-已完成、2-退款）")
    @Excel(name = "订单状态（0-未发货、1-已完成、2-退款）")
    private Integer orderStatus;

    @FieldDesc("交稿状态（0-制作中、1-已交初稿、2-已交定稿、3-定稿被驳回）")
    private Integer archiveType;

    @FieldDesc("是否提供发票：0-否 1-是")
    @Excel(name = "是否提供发票：0-否 1-是")
    private Integer isInvoice;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId",getOrderId())
            .append("customerId",getCustomerId())
            .append("customerName",getCustomerName())
            .append("brandOwnerId",getBrandOwnerId())
            .append("commission",getCommission())
            .append("orderTime",getOrderTime())
            .append("scheduledDate",getScheduledDate())
            .append("orderStatus",getOrderStatus())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
        .toString();
    }
}
