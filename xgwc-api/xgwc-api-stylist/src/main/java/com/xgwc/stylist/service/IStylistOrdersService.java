package com.xgwc.stylist.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.feign.entity.OrderPlanVo;
import com.xgwc.stylist.entity.vo.StylistOrdersQueryVo;
import com.xgwc.stylist.entity.vo.StylistOrdersVo;

import java.util.List;

/**
 * 设计师接单-Service接口
 */
public interface IStylistOrdersService  {
    /**
     * 查询设计师交稿状态
     *
     * @param orderId 设计师接单主键
     * @return 设计师交稿状态
     */
    ApiResult selectStylistOrdersByOrderId(Long orderId);

    /**
     * 查询设计师接单列表
     *
     * @param stylistOrders 设计师接单
     * @return 设计师接单集合
     */
    ApiResult selectStylistOrdersList(StylistOrdersQueryVo stylistOrders);

    /**
     * 新增设计师交稿
     *
     * @param plans 设计师交稿
     * @return 结果
     */
    ApiResult insertStylistOrderSubmit(List<OrderPlanVo> plans);

    /**
     * 删除设计师接单信息
     *
     * @param submitId 设计师接单主键
     * @return 结果
     */
    int deleteStylistOrdersByOrderId(Long submitId);

    /**
     * 设计师接单
     * @param dto 设计师
     * @return 结果
     */
    ApiResult stylistPullOrder(StylistOrdersVo dto);
}
