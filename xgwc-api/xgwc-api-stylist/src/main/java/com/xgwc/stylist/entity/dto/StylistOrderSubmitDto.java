package com.xgwc.stylist.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class StylistOrderSubmitDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("交稿状态id")
    @Excel(name = "交稿状态id")
    private Long submitId;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long orderId;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("交稿状态（0-制作中、1-已交初稿、2-已交定稿、3-定稿待审核、4-定稿被驳回）")
    @Excel(name = "交稿状态（0-制作中、1-已交初稿、2-已交定稿、3-定稿待审核、4-定稿被驳回）")
    private Long submitStatus;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("submitId",getSubmitId())
            .append("orderId",getOrderId())
            .append("submitStatus",getSubmitStatus())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
        .toString();
    }
}
