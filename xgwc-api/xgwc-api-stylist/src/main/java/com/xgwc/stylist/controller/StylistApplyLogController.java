package com.xgwc.stylist.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.stylist.service.StylistApplyLogService;
import com.xgwc.user.feign.entity.DesignerDto;
import com.xgwc.user.feign.entity.DesignerQueryDto;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-05-27  16:41
 */

/**
 * 设计师申请记录-Controller
 */
@RestController
@RequestMapping("/applyLog")
public class StylistApplyLogController {

    @Resource
    private StylistApplyLogService stylistApplyLogService;

    /**
     * 设计师申请记录-列表
     * @return 列表
     */
    @MethodDesc("设计师申请记录-列表")
    @GetMapping("/list")
    public ApiResult list(DesignerQueryDto designer) {
        return stylistApplyLogService.list(designer);
    }

    /**
     * 设计师申请记录-详情
     * @return 详情
     */
    @MethodDesc("设计师申请记录-详情")
    @GetMapping("/{designerId}")
    public ApiResult getApplyLogById(@PathVariable("designerId") Long designerId) {
        return stylistApplyLogService.getApplyLogById(designerId);
    }

    /**
     * 设计师申请记录-重新申请
     * @return 申请结果
     */
    @MethodDesc("设计师申请记录-重新申请")
    @PostMapping("/reapply")
    public ApiResult reapply(@RequestBody DesignerDto designer) {
        return stylistApplyLogService.reapply(designer);
    }

    /**
     * 设计师申请记录-加入的品牌商
     * @return 品牌商下拉框
     */
    @MethodDesc("设计师申请记录-加入的品牌商")
    @GetMapping("/joinBrandDropDown")
    public ApiResult getInJoinBrandDropDown() {
        return stylistApplyLogService.getInJoinBrandDropDown();
    }
}
