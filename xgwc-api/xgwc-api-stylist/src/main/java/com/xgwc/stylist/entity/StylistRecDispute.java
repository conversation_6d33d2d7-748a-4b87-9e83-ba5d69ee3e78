package com.xgwc.stylist.entity;

import lombok.Data;

import java.util.Date;

/**
 *  订单异议表(stylist_rec_dispute)实体类
 */
@Data
public class StylistRecDispute {

private static final long serialVersionUID=1L;

    /** 账单ID */
    private Long billId;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 异议说明 */
    private String disputeDescription;

    /** 异议ID */
    private Long disputeId;

    /** 上传凭证文件路径 */
    private String evidenceFile;

    /** 行修改时间 */
    private Date modifyTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;



}