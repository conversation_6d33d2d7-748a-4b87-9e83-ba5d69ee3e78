package com.xgwc.stylist.entity;

import lombok.Data;

import java.util.Date;

/**
 * 交稿状态表(stylist_order_submit)实体类
 */

@Data
public class StylistOrderSubmit {

private static final long serialVersionUID=1L;

    /** 交稿状态id */
    private Long submitId;

    /** 订单id */
    private Long orderId;

    /** 交稿状态（0-制作中、1-已交初稿、2-已交定稿、3-定稿待审核、4-定稿被驳回) */
    private Long submitStatus;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;



}