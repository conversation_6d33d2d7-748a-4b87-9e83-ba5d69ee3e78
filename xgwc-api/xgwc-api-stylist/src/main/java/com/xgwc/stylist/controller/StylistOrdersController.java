package com.xgwc.stylist.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.order.feign.entity.OrderPlanVo;
import com.xgwc.stylist.entity.vo.StylistOrdersQueryVo;
import com.xgwc.stylist.entity.vo.StylistOrdersVo;
import com.xgwc.stylist.service.IStylistOrdersService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 设计师接单-Controller
 */
@RestController
@RequestMapping("/stylistOrders")
public class StylistOrdersController extends BaseController{
    @Resource
    private IStylistOrdersService stylistOrdersService;

    /**
     * 设计师接单
     */
    @MethodDesc("设计师接单")
    @PostMapping ("/pullOrder")
    public ApiResult stylistPullOrder(@RequestBody StylistOrdersVo dto) {
        return stylistOrdersService.stylistPullOrder(dto);
    }

    /**
     * 查询设计师接单列表
     */
    @MethodDesc("查询设计师接单列表")
    //@PreAuthorize("@ss.hasPermission('stylistOrders:stylistOrders:list')")
    @PostMapping("/list")
    public ApiResult list(@RequestBody StylistOrdersQueryVo stylistOrders) {
        return stylistOrdersService.selectStylistOrdersList(stylistOrders);
    }

    /**
     * 获取设计师交稿状态详细信息
     */
    @MethodDesc("获取设计师交稿状态详细信息")
    //@PreAuthorize("@ss.hasPermission('stylistOrders:stylistOrders:query')")
    @GetMapping(value = "/{orderId}")
    public ApiResult getInfo(@PathVariable("orderId") Long orderId) {
        return stylistOrdersService.selectStylistOrdersByOrderId(orderId);
    }

    /**
     * 新增设计师交稿
     */
    @MethodDesc("新增设计师交稿")
    //@PreAuthorize("@ss.hasPermission('stylistOrders:stylistOrders:add')")
    @Log(title = "设计师接单", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody List<OrderPlanVo> plans) {
        return stylistOrdersService.insertStylistOrderSubmit(plans);
    }

    /**
     * 删除设计师交稿状态
     */
/*    @MethodDesc("删除设计师交稿状态")
    //@PreAuthorize("@ss.hasPermission('stylistOrders:stylistOrders:remove')")
    @Log(title = "设计师接单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{submitId}")
    public ApiResult remove(@PathVariable Long submitId) {
        return toAjax(stylistOrdersService.deleteStylistOrdersByOrderId(submitId));
    }*/
}
