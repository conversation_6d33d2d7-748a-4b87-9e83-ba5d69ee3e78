package com.xgwc.stylist.service;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.service
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-27  16:42
 */

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.feign.entity.DesignerDto;
import com.xgwc.user.feign.entity.DesignerQueryDto;


/**
 * 设计师申请记录-Service接口
 */
public interface StylistApplyLogService {

    /**
     * 设计师申请记录列表
     *
     * @param designer 查询参数
     * @return 列表
     */
    ApiResult list(DesignerQueryDto designer);

    /**
     * 根据id查询设计师申请记录
     *
     * @param designerId 主键
     * @return 设计师申请记录
     */
    ApiResult getApplyLogById(Long designerId);

    /**
     * 设计师重新申请
     *
     * @param designer 参数
     * @return 影响行数
     */
    ApiResult reapply(DesignerDto designer);

    /**
     * 根据手机号查询加入的品牌商
     *
     * @return 品牌商
     */
    ApiResult getInJoinBrandDropDown();
}
