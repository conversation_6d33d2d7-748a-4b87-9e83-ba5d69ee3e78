package com.xgwc.stylist.service.impl;

import com.github.pagehelper.PageHelper;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.feign.api.OrderFeign;
import com.xgwc.order.feign.entity.OrderDto;
import com.xgwc.order.feign.entity.OrderPlanVo;
import com.xgwc.stylist.dao.StylistOrdersMapper;
import com.xgwc.stylist.entity.dto.StylistOrderStatus;
import com.xgwc.stylist.entity.dto.StylistOrdersDto;
import com.xgwc.stylist.entity.vo.StylistOrdersQueryVo;
import com.xgwc.stylist.entity.vo.StylistOrdersVo;
import com.xgwc.stylist.service.IStylistOrdersService;
import feign.FeignException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 设计师接单-Service业务层处理
 */
@Service
@Slf4j
public class StylistOrdersServiceImpl extends BaseController implements IStylistOrdersService  {
    @Resource
    private StylistOrdersMapper stylistOrdersMapper;
    @Resource
    private OrderFeign orderFeign;

    /**
     * 查询设计师交稿状态
     *
     * @param orderId 设计师接单主键
     * @return 设计师交稿状态
     */
    @Override
    public ApiResult selectStylistOrdersByOrderId(Long orderId) {
        try {
            ApiResult plans = orderFeign.getPlans(orderId);
            if (plans == null) {
                log.error("获取交稿进度失败，返回结果为空，orderId: {}", orderId);
                return ApiResult.error("获取交稿进度失败");
            }
            if (Objects.equals(plans.getMessage(), "OK")) {

                return plans;
            } else {
                String errorMessage = "获取交稿进度失败：" + plans.getMessage();
                log.warn(errorMessage + "，orderId: {}", orderId);
                return ApiResult.error("获取交稿进度失败");
            }
        } catch (Exception e) {
            log.error("获取交稿进度发生异常，orderId: {}", orderId, e);
            throw new  ApiException("获取交稿进度失败");
        }
    }

    /**
     * 查询设计师接单列表
     *
     * @param stylistOrders 设计师接单
     * @return 设计师接单
     */
    @Override
    public ApiResult selectStylistOrdersList(StylistOrdersQueryVo stylistOrders) {
        PageHelper.startPage(stylistOrders.getPageNum(), stylistOrders.getPageSize());
        Long designerId = SecurityUtils.getSysUser().getUserId();
        stylistOrders.setManagerUserId(designerId);
        List<StylistOrdersDto> stylistOrdersDtos = stylistOrdersMapper.selectStylistOrdersList(stylistOrders);

        StylistOrderStatus stylistOrderStatus;
        /*if(StringUtils.isNotEmpty(stylistOrders.getOrderNo()) ||
            StringUtils.isNotEmpty(stylistOrders.getCustomerId()) ||
            stylistOrders.getBrandOwnerId() !=null ||
            stylistOrders.getOrderStatus() != null ||
            stylistOrders.getStylistId() != null ||
            StringUtils.isNotEmpty(stylistOrders.getOrderTime())){
            stylistOrderStatus = calculateStatusCounts(stylistOrdersDtos);
        }else {*/
            stylistOrders.setSubmitStatus(null);
            List<StylistOrdersDto> stylistOrdersCount = stylistOrdersMapper.selectStylistOrdersList(stylistOrders);
            stylistOrderStatus = calculateStatusCounts(stylistOrdersCount);
        /*}*/

        stylistOrderStatus.setStylistOrdersDto(getDataTable(stylistOrdersDtos));
        return ApiResult.ok(stylistOrderStatus);
    }

    public StylistOrderStatus calculateStatusCounts(List<StylistOrdersDto> rows) {
        // 使用 ConcurrentHashMap 和 AtomicInteger 确保线程安全
        Map<Integer, AtomicInteger> statusMap = new ConcurrentHashMap<>();
        statusMap.put(1, new AtomicInteger(0)); // 制作中
        statusMap.put(2, new AtomicInteger(0)); // 已交初稿
        statusMap.put(3, new AtomicInteger(0)); // 定稿待审核
        statusMap.put(4, new AtomicInteger(0)); // 定稿被驳回
        statusMap.put(5, new AtomicInteger(0)); // 已交定稿

        // 使用 Stream API 简化统计逻辑
        rows.stream()
                .map(StylistOrdersDto::getArchiveType)
                .filter(Objects::nonNull)
                .filter(statusMap::containsKey)
                .forEach(status -> statusMap.get(status).incrementAndGet());

        // 创建 StylistOrderStatus 对象并设置状态计数
        StylistOrderStatus statusCount = new StylistOrderStatus();
        statusCount.setInitialDraftCount(statusMap.get(1).get());
        statusCount.setInitialReviewCount(statusMap.get(2).get());
        statusCount.setFinalDraftCount(statusMap.get(3).get());
        statusCount.setRejectedCount(statusMap.get(4).get());
        statusCount.setFinalReviewCount(statusMap.get(5).get());

        return statusCount;
    }

    /**
     * 新增设计师交稿状态及关联文件
     * 
     * @param plans 设计师交稿状态
     * @return 结果
     */
    @Override
    public ApiResult insertStylistOrderSubmit(List<OrderPlanVo> plans) {
        try {
            ApiResult apiResult = orderFeign.savePlan(plans);
            if (apiResult == null) {
                log.error("新增交稿进度失败：API返回结果为空");
                return ApiResult.error("新增交稿进度失败");
            }
            if (Objects.equals(apiResult.getMessage(), "OK")) {
                return apiResult;
            } else {
                String errorMessage = apiResult.getMessage();
                log.warn("新增交稿进度失败：{}", errorMessage);
                return ApiResult.error("新增交稿进度失败");
            }
        } catch (Exception e) {
            log.error("新增交稿进度发生异常", e);
            return ApiResult.error("新增交稿进度失败");
        }
    }


    /**
     * 删除设计师接单信息
     * 
     * @param submitId 设计师接单主键
     * @return 结果
     */
    @Override
    public int deleteStylistOrdersByOrderId(Long submitId) {
        return stylistOrdersMapper.deleteStylistOrdersByOrderId(submitId);
    }

    /**
     * 设计师接单
     *
     * @param dto 设计师
     * @return 结果
     */
    @Override
    public ApiResult stylistPullOrder(StylistOrdersVo dto) {
        try {
            // 调用远程接口获取订单信息
            ApiResult apiResult = orderFeign.designerOrders(dto.getOrderNo());
            String message = apiResult.getMessage();

            if (!StringUtils.equals(message, "success")) {
                return ApiResult.error(message);
            }

            OrderDto orderDto = (OrderDto) apiResult.getData();

            // 设置订单相关信息
            setOrderInfo(dto, orderDto);

            // 插入订单
            Long managerUserId = stylistOrdersMapper.selectmanagerUserId(dto.getStylistId());
            dto.setManagerUserId(managerUserId);
            int insertOrders = stylistOrdersMapper.insertOrders(dto);
            if (insertOrders <= 0) {
                return ApiResult.error("接单失败");
            }

            return ApiResult.ok("接单成功");
        } catch (FeignException.ServiceUnavailable e) {
            log.error("Service unavailable: {}", e.getMessage(), e);
            return ApiResult.error("无法连接到订单服务，请稍后再试。");
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage(), e);
            return ApiResult.error("发生未知错误，请联系管理员。");
        }
    }

    /**
     * 设置订单基础信息
     */
    private void setOrderInfo(StylistOrdersVo dto, OrderDto orderDto) {
        dto.setOrderId(orderDto.getId());
        dto.setOrderNo(orderDto.getOrderNo());
        Long designerId = orderDto.getDesignerId();
        dto.setStylistId(designerId);
        dto.setStylistName(orderDto.getDesignerName());
        dto.setCustomerId(orderDto.getTaobaoId());
        dto.setBrandOwnerId(stylistOrdersMapper.selectDesignerJoinBrandById(designerId));
        dto.setCommission(orderDto.getMoney());
        dto.setOrderTime(new Date());
        dto.setCreateBy(SecurityUtils.getNickName());
        dto.setScheduledDate(orderDto.getArchiveAppointTime());
    }

}
