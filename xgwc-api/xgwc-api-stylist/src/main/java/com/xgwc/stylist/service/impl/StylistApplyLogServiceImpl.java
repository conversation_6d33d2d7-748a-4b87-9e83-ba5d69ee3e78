package com.xgwc.stylist.service.impl;


import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.stylist.service.StylistApplyLogService;
import com.xgwc.user.feign.api.DesignerFeign;
import com.xgwc.user.feign.entity.DesignerDto;
import com.xgwc.user.feign.entity.DesignerQueryDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-05-27  16:43
 */
/**
 * 设计师申请记录-Service业务处理
 */
@Service
@Slf4j
public class StylistApplyLogServiceImpl implements StylistApplyLogService {

    @Resource
    private DesignerFeign designerFeign;

    /**
     * 设计师申请记录-列表
     *
     * @param designer 入参
     * @return 结果
     */
    @Override
    public ApiResult list(DesignerQueryDto designer) {
        designer.setDesignerId(SecurityUtils.getSysUser().getUserId());
        return designerFeign.list(
                designer.getName(),
                designer.getBrandId(),
                designer.getGoodBusiness(),
                designer.getStatus(),
                designer.getDesignerId(),
                designer.getManagerPhone(),
                designer.getPageSize(),
                designer.getPageNumber());
    }

    /**
     * 设计师申请记录-详情
     * @param designerId 主键
     * @return 详情
     */
    @Override
    public ApiResult getApplyLogById(Long designerId) {
        return designerFeign.getDesignerById(designerId);
    }

    /**
     * 设计师申请记录-重新申请
     * @param designer 入参
     * @return 结果
     */
    @Override
    public ApiResult reapply(DesignerDto designer) {
        return designerFeign.reapply(designer);
    }

    /**
     * 设计师申请记录-加入的品牌商
     * @return 品牌商
     */
    @Override
    public ApiResult getInJoinBrandDropDown() {
        return designerFeign.getInJoinBrandDropDown();
    }
}
