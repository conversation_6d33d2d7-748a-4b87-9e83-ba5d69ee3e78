package com.xgwc.stylist.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.LineChartType;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.stylist.dao.StylistIndexMapper;
import com.xgwc.stylist.dao.StylistOrdersMapper;
import com.xgwc.stylist.dao.StylistReconciliationMapper;
import com.xgwc.stylist.entity.StylistOrders;
import com.xgwc.stylist.entity.dto.*;
import com.xgwc.stylist.entity.vo.StylistIndexVo;
import com.xgwc.stylist.entity.vo.StylistOrdersQueryVo;
import com.xgwc.stylist.entity.vo.StylistReconciliationQueryVo;
import com.xgwc.stylist.service.StylistIndexService;
import com.xgwc.user.feign.api.NoticeFeign;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-05-26  17:55
 */

/**
 * 设计师首页-Service业务层处理
 */
@Service
@Slf4j
public class StylistIndexServiceImpl implements StylistIndexService {

    @Resource
    private StylistIndexMapper stylistIndexMapper;
    @Resource
    private StylistOrdersMapper stylistOrdersMapper;
    @Resource
    private StylistReconciliationMapper stylistReconciliationMapper;
    @Resource
    private StylistOrdersServiceImpl stylistOrdersService;
    @Resource
    private NoticeFeign noticeFeign;

    private static final ExecutorService ASYNC_EXECUTOR = Executors.newFixedThreadPool(8, r -> {
        Thread t = new Thread(r);
        t.setName("stylist-async-" + t.getId());
        t.setDaemon(false); // 设置为非守护线程
        t.setPriority(Thread.NORM_PRIORITY); // 设置优先级
        // 添加未捕获异常处理器
        t.setUncaughtExceptionHandler((thread, ex) -> {
            log.error("线程 {} 未捕获异常", thread.getName(), ex);
        });
        return t;
    });

    /**
     * 关闭资源时关闭线程池
     */
    @PreDestroy
    public void destroy() {
        ASYNC_EXECUTOR.shutdown();
    }

    /**
     * 查询设计师代办事项数量统计
     *
     * @return 设计师接单代办事项数量统计
     */
    @Override
    public ApiResult selectStylistOrderCount(StylistIndexVo stylistIndexVo) {
        StylistIndexDto stylistIndexDto = new StylistIndexDto();
        setStylistId(stylistIndexVo);

        try {
            // 使用CompletableFuture并行处理多个任务
            CompletableFuture<List<StylistOrdersDto>> ordersFuture =
                    CompletableFuture.supplyAsync(() -> fetchStylistOrders(stylistIndexVo), ASYNC_EXECUTOR);
            CompletableFuture<List<StylistReconciliationDto>> reconciliationFuture =
                    CompletableFuture.supplyAsync(() -> fetchStylistReconciliations(stylistIndexVo), ASYNC_EXECUTOR);

            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(ordersFuture, reconciliationFuture);
            allFutures.join();

            // 处理订单数据
            List<StylistOrdersDto> stylistOrdersDtos = ordersFuture.get();
            if (!stylistOrdersDtos.isEmpty()) {
                StylistOrderStatus stylistOrderStatusCount = stylistOrdersService.calculateStatusCounts(stylistOrdersDtos);
                stylistIndexDto.setStylistOrderStatus(stylistOrderStatusCount);
            }

            // 处理对账数据
            List<StylistReconciliationDto> reconciliationDtos = reconciliationFuture.get();
            if (!reconciliationDtos.isEmpty()) {
                StylistReconciliationStatus stylistReconciliationStatus = calculateStatusCounts(reconciliationDtos);
                stylistIndexDto.setStylistReconciliationStatus(stylistReconciliationStatus);
            }

            // 异步计算订单统计（不影响主流程）
            CompletableFuture.runAsync(() -> {
                StylistOrdersStatistics statistics = calculateOrderStatistics(stylistIndexVo);
                stylistIndexDto.setStylistOrdersStatistics(statistics);
            }, ASYNC_EXECUTOR);

            // 异步生成折线图数据
            CompletableFuture.runAsync(() -> {
                List<Map<String, Number>> lineChartData = generateLineChartData(stylistIndexVo, stylistOrdersDtos, reconciliationDtos);
                stylistIndexDto.setStylistLineChart(lineChartData);
            }, ASYNC_EXECUTOR);

            // 同步获取通知（因为需要立即返回）
            designerSelfNotice(stylistIndexVo, stylistIndexDto);

        } catch (Exception e) {
            log.error("查询设计师代办事项数量统计失败", e);
            return ApiResult.error("查询失败");
        }

        return ApiResult.ok(stylistIndexDto);
    }


    private void setStylistId(StylistIndexVo stylistIndexVo) {
        stylistIndexVo.setManagerUserId(SecurityUtils.getSysUser().getUserId());
    }

    /**
     * 设计师通知
     */
    private void designerSelfNotice(StylistIndexVo stylistIndexVo, StylistIndexDto stylistIndexDto) {
        ApiResult selfNotice = noticeFeign.getDesignerSelfNotice(stylistIndexVo.getAllNoticeBrandOwner());

        if (selfNotice != null) {
            Object data = selfNotice.getData();
            if (data != null) {
                try {
                    // 直接将 data 转换为 JSONObject
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(data);
                    if (jsonObject.containsKey("noticeList")) {
                        JSONArray rowsArray = jsonObject.getJSONArray("noticeList");
                        // 直接将 JSONArray 转换为 List
                        List<StylistNoticeDto> rowsList = rowsArray.toJavaList(StylistNoticeDto.class);
                        stylistIndexDto.setStylistNoticeDto(rowsList);
                    }
                } catch (Exception e) {
                    // 记录日志，防止非法 JSON 导致程序崩溃
                    log.error("解析通知数据失败", e);
                }
            }
        }
    }

    /**
     * 获取设计师首页-订单概览统计
     *
     * @return 订单概览统计
     */
    @Override
    public ApiResult selectStylistOrderoverviewCount(StylistIndexVo stylistIndexVo) {
        if (stylistIndexVo == null) {
            return ApiResult.error("参数不能为空");
        }

        try {
            setStylistId(stylistIndexVo);

            List<StylistOrdersDto> stylistOrdersDtos = fetchStylistOrders(stylistIndexVo);
            List<StylistReconciliationDto> reconciliationDtos = fetchStylistReconciliations(stylistIndexVo);

            List<Map<String, Number>> lineChartData = generateLineChartData(
                    stylistIndexVo,
                    Optional.ofNullable(stylistOrdersDtos).orElse(Collections.emptyList()),
                    Optional.ofNullable(reconciliationDtos).orElse(Collections.emptyList())
            );

            StylistIndexDto stylistIndexDto = new StylistIndexDto();
            stylistIndexDto.setStylistLineChart(lineChartData);

            return ApiResult.ok(stylistIndexDto);
        } catch (Exception e) {
            throw new ApiException("查询失败：" + e);
        }
    }


    /**
     * 获取设计师首页-全部通知
     *
     * @return 全部通知
     */
    @Override
    public ApiResult selectStylistAllNotice(StylistIndexVo stylistIndexVo) {
        StylistIndexDto stylistIndexDto = new StylistIndexDto();
        setStylistId(stylistIndexVo);
        designerSelfNotice(stylistIndexVo, stylistIndexDto);
        return ApiResult.ok(stylistIndexDto);
    }

    /**
     * 获取设计师首页-接单概率
     *
     * @return 近半年概览/累计数据
     */
    @Override
    public ApiResult selectStylistOrderOverview(StylistIndexVo stylistIndexVo) {
        if (stylistIndexVo == null) {
            return ApiResult.error("参数不能为空");
        }

        setStylistId(stylistIndexVo);

        StylistOrdersStatistics stylistOrdersStatistics = calculateOrderStatistics(stylistIndexVo);
        StylistIndexDto stylistIndexDto = new StylistIndexDto();
        stylistIndexDto.setStylistOrdersStatistics(stylistOrdersStatistics);

        return ApiResult.ok(stylistIndexDto);
    }


    /**
     * 获取设计师接单信息
     */
    private List<StylistOrdersDto> fetchStylistOrders(StylistIndexVo stylistIndexVo) {
        StylistOrdersQueryVo stylistOrders = new StylistOrdersQueryVo();
        stylistOrders.setBrandOwnerId(stylistIndexVo.getOrderOverviewBrandOwner());
        stylistOrders.setYear(stylistIndexVo.getYear());
        stylistOrders.setStylistId(stylistIndexVo.getStylistId());
        stylistOrders.setManagerUserId(stylistIndexVo.getManagerUserId());
        return stylistOrdersMapper.selectStylistOrdersList(stylistOrders);
    }

    /**
     * 获取设计师对账数据
     */
    private List<StylistReconciliationDto> fetchStylistReconciliations(StylistIndexVo stylistIndexVo) {
        StylistReconciliationQueryVo queryVo = new StylistReconciliationQueryVo();
        queryVo.setBrandOwnerId(stylistIndexVo.getOrderOverviewBrandOwner());
        queryVo.setYear(stylistIndexVo.getYear());
        queryVo.setStylistId(stylistIndexVo.getStylistId());
        queryVo.setManagerUserId(stylistIndexVo.getManagerUserId());
        return stylistReconciliationMapper.selectStylistReconciliationList(queryVo);
    }

    /**
     * 获取近半年接单/累计接单数据
     */
    private StylistOrdersStatistics calculateOrderStatistics(StylistIndexVo stylistIndexVo) {
        StylistOrdersStatistics statistics = new StylistOrdersStatistics();
        int isOverview = (stylistIndexVo.getIsOverview() == null) ? 0 : stylistIndexVo.getIsOverview();
        Long stylistId = stylistIndexVo.getManagerUserId();

        // 使用CompletableFuture并行获取数据
        CompletableFuture<List<StylistOrdersDto>> ordersFuture =
                CompletableFuture.supplyAsync(() ->
                                // 0-近半年概览，1-累计概览
                                (isOverview == 0)
                                        ? stylistIndexMapper.selectStylistOrdersHalfYearList(stylistId)
                                        : stylistIndexMapper.selectStylistOrdersAllYearList(stylistId),
                        ASYNC_EXECUTOR);

        CompletableFuture<List<StylistReconciliationDto>> reconciliationFuture =
                CompletableFuture.supplyAsync(() ->
                                // 0-近半年概览，1-累计概览
                                (isOverview == 0)
                                        ? stylistIndexMapper.selectStylistReconciliationHalfYearList(stylistId)
                                        : stylistIndexMapper.selectselectStylistReconciliationAllYearList(stylistId),
                        ASYNC_EXECUTOR);

        try {
            List<StylistOrdersDto> ordersDtos = ordersFuture.get();
            List<StylistReconciliationDto> reconciliationList = reconciliationFuture.get();

            statistics.setOrderCount(ordersDtos.size());
            statistics.setRefundOrderCount(countRefundOrders(ordersDtos));
            statistics.setServedCustomerCount(countUniqueCustomers(ordersDtos));
            statistics.setSettledCommission(calculateSettledCommission(reconciliationList));
            statistics.setPendingCommission(calculatePendingCommission(ordersDtos, statistics.getSettledCommission()));

        } catch (Exception e) {
            log.error("计算订单统计失败", e);
        }

        return statistics;
    }

    /**
     * 统计退款订单数
     */
    private int countRefundOrders(List<StylistOrdersDto> ordersDtos) {
        if (ordersDtos.isEmpty()) return 0;

        // 批量查询订单ID
        List<Long> orderIds = ordersDtos.stream()
                .map(StylistOrdersDto::getOrderId)
                .collect(Collectors.toList());

        // 使用批量查询替代循环查询
        List<StylistOrders> refundOrders = stylistOrdersMapper.selectXgwcOrdersBatch(orderIds);
        return refundOrders.size();
    }

    /**
     * 统计服务客户数
     */
    private int countUniqueCustomers(List<StylistOrdersDto> ordersDtos) {
        return (int) ordersDtos.stream()
                .map(StylistOrdersDto::getCustomerId)
                .distinct()
                .count();
    }

    /**
     * 统计已结算佣金
     */
    private BigDecimal calculateSettledCommission(List<StylistReconciliationDto> reconciliationList) {
        return reconciliationList.stream()
                .filter(dto -> dto.getSettlementStatus() != null && dto.getSettlementStatus() == 3)
                .map(StylistReconciliationDto::getTotalCommission)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 统计待结算佣金
     */
    private BigDecimal calculatePendingCommission(List<StylistOrdersDto> ordersDtos, BigDecimal settledCommission) {
        BigDecimal sum = ordersDtos.stream()
                .map(StylistOrdersDto::getCommission)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.subtract(settledCommission);
    }

    /**
     * 生成折线图数据
     *
     * @param stylistIndexVo     查询参数
     * @param stylistOrdersDtos  接单概览
     * @param reconciliationDtos 对账概览
     * @return 折线图数据
     */
    private List<Map<String, Number>> generateLineChartData(StylistIndexVo stylistIndexVo,
                                                            List<StylistOrdersDto> stylistOrdersDtos,
                                                            List<StylistReconciliationDto> reconciliationDtos) {
        Integer isFlag = stylistIndexVo.getIsFlag();
        if (isFlag == null) {
            isFlag = 0;
        }
        LineChartType lineChartType = LineChartType.fromValue(isFlag);

        // 根据类型选择不同的异步计算方式
        return switch (lineChartType) {
            case YEARLY -> stylistOrdersMapper.selectLineChartYear(stylistIndexVo);
            case MONTHLY_ORDER -> stylistOrdersMapper.selectXgwcOrdersBatchAll(stylistOrdersDtos);
            case MONTHLY_CUSTOMER -> countUniqueCustomersByMonth(stylistOrdersDtos);
            case MONTHLY_SETTLEMENT -> countSettlementStatusThreeByMonth(reconciliationDtos);
            default -> Collections.emptyList();
        };
    }

    /**
     * 生成折线图数据
     */
    private List<Map<String, Number>> generateMonthlyStatistics(List<?> dataList, Function<Object, Integer> monthExtractor, Function<Object, Integer> valueExtractor) {
        Map<Integer, Integer> monthlyCount = new HashMap<>();

        for (Object item : dataList) {
            int month = monthExtractor.apply(item);
            if (month != -1) { // 假设 -1 表示无效月份
                Integer value = valueExtractor.apply(item);
                if (value != null) {
                    monthlyCount.put(month, monthlyCount.getOrDefault(month, 0) + value);
                }
            }
        }

        // 将结果转换为List<Map<String, Number>>
        List<Map<String, Number>> result = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : monthlyCount.entrySet()) {
            Map<String, Number> monthlyData = new HashMap<>();
            monthlyData.put("month", entry.getKey());
            monthlyData.put("orderCount", entry.getValue());
            result.add(monthlyData);
        }

        // 按月份排序
        result.sort(Comparator.comparingInt(o -> ((Number) o.get("month")).intValue()));

        return result;
    }

    /**
     * 统计每月的服务客户数量
     */
    private List<Map<String, Number>> countUniqueCustomersByMonth(List<StylistOrdersDto> stylistOrdersDtos) {
        Map<Integer, Set<String>> monthlyUniqueCustomers = new HashMap<>();

        for (StylistOrdersDto dto : stylistOrdersDtos) {
            if (dto.getCreateTime() != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(dto.getCreateTime());
                int month = calendar.get(Calendar.MONTH) + 1;

                // 获取或创建该月份的customerId集合
                monthlyUniqueCustomers.putIfAbsent(month, new HashSet<>());
                monthlyUniqueCustomers.get(month).add(dto.getCustomerId());
            }
        }

        // 将结果转换为List<Map<String, Number>>
        List<Map<String, Number>> result = new ArrayList<>();
        for (Map.Entry<Integer, Set<String>> entry : monthlyUniqueCustomers.entrySet()) {
            Map<String, Number> monthlyData = new HashMap<>();
            monthlyData.put("month", entry.getKey());
            monthlyData.put("orderCount", entry.getValue().size());
            result.add(monthlyData);
        }

        // 按月份排序
        result.sort(Comparator.comparingInt(o -> ((Number) o.get("month")).intValue()));

        return result;
    }

    /**
     * 按月份统计每月结算数
     */
    private List<Map<String, Number>> countSettlementStatusThreeByMonth(List<StylistReconciliationDto> reconciliationDtos) {
        return generateMonthlyStatistics(reconciliationDtos, item -> {
            StylistReconciliationDto dto = (StylistReconciliationDto) item;
            if (dto.getCreateTime() != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(dto.getCreateTime());
                return calendar.get(Calendar.MONTH) + 1;
            }
            return -1;
        }, item -> {
            StylistReconciliationDto dto = (StylistReconciliationDto) item;
            return (dto.getSettlementStatus() != null && dto.getSettlementStatus() == 3) ? 1 : 0;
        });
    }

    private StylistReconciliationStatus calculateStatusCounts(List<StylistReconciliationDto> reconciliationDtos) {
        StylistReconciliationStatus statusCount = new StylistReconciliationStatus();

        // 使用并行流进行统计
        Map<Integer, Long> statusMap = reconciliationDtos.parallelStream()
                .collect(Collectors.groupingBy(
                        StylistReconciliationDto::getSettlementStatus,
                        Collectors.counting()
                ));

        //结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)
        statusCount.setPendingConfCount(statusMap.getOrDefault(0, 0L).intValue());
        statusCount.setDisputedCount(statusMap.getOrDefault(1, 0L).intValue());
        statusCount.setPendingSetCount(statusMap.getOrDefault(2, 0L).intValue());
        statusCount.setInvoiceRejectionCount(statusMap.getOrDefault(5, 0L).intValue());

        return statusCount;
    }
}
