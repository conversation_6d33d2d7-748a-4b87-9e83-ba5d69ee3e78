package com.xgwc.stylist.entity.dto;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.entity.dto
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-26  18:38
 */

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 接单概览统计
 */
@Data
public class StylistOrdersStatistics {

    // 接单数
    @FieldDesc("接单数")
    private Integer orderCount;

    // 退款订单数
    @FieldDesc("退款订单数")
    private Integer refundOrderCount;

    // 服务客户数
    @FieldDesc("服务客户数")
    private Integer servedCustomerCount;

    // 未结算预估佣金（元）
    @FieldDesc("未结算预估佣金（元）")
    private BigDecimal pendingCommission;

    // 已结算佣金（元）
    @FieldDesc("已结算佣金（元）")
    private BigDecimal settledCommission;
}
