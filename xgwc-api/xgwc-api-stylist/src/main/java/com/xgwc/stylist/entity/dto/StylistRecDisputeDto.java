package com.xgwc.stylist.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class StylistRecDisputeDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键ID")
    @Excel(name = "主键ID")
    private Long id;

    @FieldDesc("账单ID")
    @Excel(name = "账单ID")
    private Long billId;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("异议说明")
    @Excel(name = "异议说明")
    private String disputeDescription;

    @FieldDesc("异议ID")
    @Excel(name = "异议ID")
    private Long disputeId;

    @FieldDesc("上传凭证文件路径")
    @Excel(name = "上传凭证文件路径")
    private String evidenceFile;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;



    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("billId",getBillId())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("disputeDescription",getDisputeDescription())
            .append("disputeId",getDisputeId())
            .append("evidenceFile",getEvidenceFile())
            .append("modifyTime",getModifyTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
