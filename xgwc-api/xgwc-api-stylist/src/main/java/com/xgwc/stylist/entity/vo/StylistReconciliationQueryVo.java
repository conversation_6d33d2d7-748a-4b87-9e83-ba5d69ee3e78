package com.xgwc.stylist.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StylistReconciliationQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("账单ID")
    private Long billId;

    @FieldDesc("账单周期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  billPeriodEnd;

    @FieldDesc("账单周期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  billPeriodStart;

    @FieldDesc("出账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  billingTime;

    @FieldDesc("结算品牌商")
    private Long brandOwnerId;

    @FieldDesc("佣金追回")
    private BigDecimal commissionBack;

    @FieldDesc("确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  confirmationTime;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("罚款金额")
    private BigDecimal fineAmount;

    @FieldDesc("是否锁定：0锁定，1未锁定")
    private Long isLock;

    @FieldDesc("管理员用户id")
    private Long managerUserId;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;

    @FieldDesc("年度")
    private Integer year;

    @FieldDesc("无票扣款")
    private BigDecimal noInvoice;

    @FieldDesc("账单订单数")
    private Long orderCount;

    @FieldDesc("结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)")
    private Long settlementStatus;

    @FieldDesc("结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  settlementTime;

    @FieldDesc("状态：0正常，其他非正常")
    private Long status;

    @FieldDesc("设计师id")
    private Long stylistId;

    @FieldDesc("设计师名称")
    private String stylistName;

    @FieldDesc("账单佣金合计")
    private BigDecimal totalCommission;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;



}
