package com.xgwc.stylist.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.List;
import java.util.Map;
/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.entity.dto
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-26  18:05
 */

/**
 * 设计师首页统计信息
 */
@Data
public class StylistIndexDto {

    @FieldDesc("接单待处理")
    private StylistOrderStatus stylistOrderStatus;

    @FieldDesc("对账待处理")
    private StylistReconciliationStatus stylistReconciliationStatus;

    @FieldDesc("接单概览统计")
    private StylistOrdersStatistics stylistOrdersStatistics;

    @FieldDesc("折线图统计")
    private List<Map<String, Number>> stylistLineChart;

    @FieldDesc("全部通知")
    private List<StylistNoticeDto> stylistNoticeDto;
}
