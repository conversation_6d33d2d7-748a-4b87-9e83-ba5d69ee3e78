package com.xgwc.stylist.entity.dto;

import com.xgwc.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

// 基本信息类
@Data
public class OrderBasicInfo {
    @Excel(name = "结算品牌商", sort = 1)
    private String brandName;
    
    @Excel(name = "结算设计师", sort = 2)
    private String stylistName;
    
    @Excel(name = "账单周期", sort = 3)
    private String billCycle;
    
    @Excel(name = "总金额", sort = 4)
    private BigDecimal totalAmount;
}