package com.xgwc.stylist.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ExcelExportUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.stylist.dao.StylistReconciliationMapper;
import com.xgwc.stylist.entity.dto.*;
import com.xgwc.stylist.entity.vo.StylistRecDisputeVo;
import com.xgwc.stylist.entity.vo.StylistRecOtherDisputeVo;
import com.xgwc.stylist.entity.vo.StylistReconciliationQueryVo;
import com.xgwc.stylist.service.StylistReconciliationService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-05-26  11:27
 */

/**
 * 设计师对账-Service业务层处理
 */
@Service
@Slf4j
public class StylistReconciliationServiceImpl implements StylistReconciliationService {

    @Resource
    private StylistReconciliationMapper stylistReconciliationMapper;

    /**
     * 查询设计师对账列表
     *
     * @param stylistReconciliation 查询参数
     * @return 设计师对账
     */
    @Override
    public List<StylistReconciliationDto> selectStylistReconciliationList(StylistReconciliationQueryVo stylistReconciliation) {
        stylistReconciliation.setManagerUserId(SecurityUtils.getSysUser().getUserId());
        List<StylistReconciliationDto> stylistReconciliationDtos = stylistReconciliationMapper.selectStylistReconciliationList(stylistReconciliation);
        stylistReconciliationDtos.forEach(dto -> {
            BigDecimal totalCommission = dto.getTotalCommission() != null ? dto.getTotalCommission() : BigDecimal.ZERO;
            BigDecimal commissionBack = dto.getCommissionBack() != null ? dto.getCommissionBack() : BigDecimal.ZERO;
            BigDecimal fineAmount = dto.getFineAmount() != null ? dto.getFineAmount() : BigDecimal.ZERO;

            // 计算应发金额：amount = totalCommission - commissionBack - fineAmount
            BigDecimal amount = totalCommission.subtract(commissionBack).subtract(fineAmount);
            dto.setAmount(amount);
        });
        return stylistReconciliationDtos;
    }

    /**
     * 设计师账单确认
     *
     * @param id 账单id
     * @return 结果
     */
    @Override
    public ApiResult stylistReconciliationNotarize(Integer id) {
        if (id == null) {
            log.warn("账单确认失败：id为空");
            return ApiResult.error("账单确认失败");
        }

        int rowsAffected = stylistReconciliationMapper.stylistReconciliationNotarize(id);
        if (rowsAffected == 1) {
            log.info("账单确认成功，id: {}", id);
            return ApiResult.ok("账单确认成功");
        } else {
            log.warn("账单确认失败，未更新任何记录，id: {}", id);
            return ApiResult.error("账单确认失败");
        }
    }


    /**
     * 设计师下载账单
     *
     * @param stylistReconciliation 查询参数
     * @return 账单
     */
    @Override
    public ApiResult stylistReconciliationDownload(StylistReconciliationQueryVo stylistReconciliation) {
        return null;
    }

    /**
     * 订单异议回显
     *
     * @param id 订单id
     * @return 订单信息
     */
    @Override
    public ApiResult stylistUpdateDisputeInfoById(Integer id) {
        if (id == null || id <= 0) {
            log.warn("ID不能为空且必须大于0: {}", id);
            return ApiResult.error("ID不能为空且必须大于0");
        }

        try {
            // 并行执行数据库查询
            Future<StylistRecOtherDisputeDto> future1 = CompletableFuture.supplyAsync(() ->
                    stylistReconciliationMapper.selectStyRecOtherDispute(id)
            );

            Future<List<BillOrderDto>> future2 = CompletableFuture.supplyAsync(() ->
                    stylistReconciliationMapper.stylistRecDisputeById(id)
            );

            // 等待查询结果
            StylistRecOtherDisputeDto stylistRecOtherDisputeDto =
                    future1.get() != null ? future1.get() : new StylistRecOtherDisputeDto();
            List<BillOrderDto> stylistRecDisputeDto = future2.get();

            stylistRecOtherDisputeDto.setStylistRecDisputeDto(!CollectionUtils.isEmpty(stylistRecDisputeDto)
                    ? stylistRecDisputeDto.stream().filter(ss -> ss.getOrderId() != null).toList()
                    : Collections.emptyList());
            return ApiResult.ok(stylistRecOtherDisputeDto);
        } catch (Exception e) {
            log.error("获取设计师对帐信息时发生错误 id: {}", id, e);
            return ApiResult.error("系统异常，请联系管理员");
        }
    }


    /**
     * 订单异议提交
     *
     * @param stylistRecDisputeVo 订单信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateStylistDispute(StylistRecOtherDisputeVo stylistRecDisputeVo) {
        if (stylistRecDisputeVo == null) {
            return ApiResult.error("提交数据不能为空");
        }

        try {
            // 设置更新人
            String updater = SecurityUtils.getNickName();
            stylistRecDisputeVo.setUpdateBy(updater);

            List<StylistRecDisputeVo> innerVo = stylistRecDisputeVo.getStylistRecDisputeVo();
            // 执行两个更新操作
            int updated = stylistReconciliationMapper.updateStylistRecDispute(innerVo);
            int otherDispute = stylistReconciliationMapper.updateStylistRecOtherDispute(stylistRecDisputeVo);

            if (updated > 0 && otherDispute > 0) {
                return ApiResult.ok("订单异议修改成功");
            } else {
                throw new ApiException("未找到匹配的记录，修改失败");
            }
        } catch (ApiException e) {
            log.error("更新订单异议失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("系统异常，更新订单异议失败", e);
            throw new ApiException("系统异常，请稍后重试");
        }
    }


    /**
     * 订单异议新增
     *
     * @param stylistRecDisputeVo 账单参数
     * @return ApiResult
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult insertStylistDispute(StylistRecOtherDisputeVo stylistRecDisputeVo) {
        if (stylistRecDisputeVo == null) {
            return ApiResult.error("提交数据不能为空");
        }

        try {
            stylistRecDisputeVo.setCreateBy(SecurityUtils.getNickName());
            stylistRecDisputeVo.setUpdateBy(SecurityUtils.getNickName());
            int insertOtherStyle = stylistReconciliationMapper.upsertStylistRecOtherDispute(stylistRecDisputeVo);

            // 先根据账单id删除现有异议信息做新增操作
            int deleted = stylistReconciliationMapper.deleteRecDispute(stylistRecDisputeVo.getBillId());
            if (deleted < 0) {
                log.error("清空现有异议失败：异议id{}", stylistRecDisputeVo.getDisputeId());
                throw new ApiException("提交异议失败");
            }
            if (CollectionUtils.isEmpty(stylistRecDisputeVo.getStylistRecDisputeVo())) {
                return ApiResult.ok();
            }

            int insertStyle = stylistReconciliationMapper.upsertStylistRecDispute(stylistRecDisputeVo);
            if (insertStyle <= 0 || insertOtherStyle <= 0) {
                log.warn("插入订单异议失败，billId: {}", stylistRecDisputeVo.getBillId());
                throw new ApiException("未找到匹配的记录，提交失败");
            }

            int updateCount = stylistReconciliationMapper.updateStyRecStatus(stylistRecDisputeVo.getBillId());
            if (updateCount <= 0) {
                log.warn("更新账单状态失败，billId: {}", stylistRecDisputeVo.getBillId());
                throw new ApiException("账单状态更新失败");
            }

            return ApiResult.ok("订单异议提交成功");

        } catch (ApiException e) {
            log.error("业务异常: {}, billId: {}", e.getMessage(), stylistRecDisputeVo.getBillId(), e);
            throw e;
        } catch (Exception e) {
            log.error("系统异常，提交订单异议失败, billId: {}", stylistRecDisputeVo.getBillId(), e);
            throw new ApiException("系统异常，请稍后重试");
        }
    }

    @Override
    public ApiResult selecetStylistReconciliationDetail(Long id) {
        if (id == null || id <= 0) {
            log.warn("ID不能为空且必须大于0: {}", id);
            return ApiResult.error("ID不能为空且必须大于0");
        }
        List<BillOrderDto> billOrderDtos = stylistReconciliationMapper.stylistReconciliationInfoById(id);
        if (CollectionUtils.isEmpty(billOrderDtos)){
            return ApiResult.ok(billOrderDtos);
        }
        BigDecimal sumMoney = billOrderDtos.stream()
                .map(BillOrderDto::getMoney)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Map<String, Object> result = new HashMap<>();
        result.put("sumMoney", sumMoney);
        result.put("list", billOrderDtos);
        return ApiResult.ok(result);
    }

    @Override
    public void downloadBill(HttpServletResponse response, Long billId) {
        try {
            // 参数校验
            if (billId == null) {
                throw new ApiException("账单ID不能为空");
            }

            // 查询账单基本信息
            StylistReconciliationQueryVo query = new StylistReconciliationQueryVo();
            query.setBillId(billId);
            query.setManagerUserId(SecurityUtils.getSysUser().getUserId());

            List<StylistReconciliationDto> reconciliationList = stylistReconciliationMapper.selectStylistReconciliationList(query);
            if (CollectionUtils.isEmpty(reconciliationList)) {
                throw new ApiException("未找到对应的账单信息");
            }
            StylistReconciliationDto stylistReconciliationDto = reconciliationList.get(0);

            // 构建订单基础信息
            OrderBasicInfo orderBasicInfo = new OrderBasicInfo();
            orderBasicInfo.setBrandName(stylistReconciliationDto.getBrandName());
            orderBasicInfo.setStylistName(stylistReconciliationDto.getStylistName());
            orderBasicInfo.setBillCycle(
                    DateUtils.formatDate(stylistReconciliationDto.getBillPeriodStart()) + "-" +
                            DateUtils.formatDate(stylistReconciliationDto.getBillPeriodEnd())
            );
            orderBasicInfo.setTotalAmount(stylistReconciliationDto.getTotalCommission());

            // 查询并构建订单详情信息
            List<BillOrderDto> billOrderDtos = stylistReconciliationMapper.stylistReconciliationInfoById(billId);
            if (CollectionUtils.isEmpty(billOrderDtos)) {
                throw new ApiException("未找到对应的账单信息");
            }
            List<OrderDetail> orderDetails = new ArrayList<>();
            billOrderDtos.forEach(billOrderDto -> {
                OrderDetail detail = new OrderDetail();
                detail.setOrderNo(billOrderDto.getOrderNo());
                detail.setArchiveTime(billOrderDto.getArchiveTime());
                // TODO 获取订单完成时间
                detail.setFinishedTime(null);
                detail.setDealTime(billOrderDto.getDealTime());
                detail.setMoney(billOrderDto.getMoney());
                detail.setTaobaoId(billOrderDto.getTaobaoId());
                detail.setDesignerBusiness(billOrderDto.getDesignerBusiness());
                detail.setSubBillId(billId);
                orderDetails.add(detail);
            });

            // 导出Excel
            ExcelExportUtil.exportExcelToResponse(
                    "账单",
                    "账单详情信息",
                    orderBasicInfo,
                    orderDetails,
                    response,
                    "设计师账单"
            );
        } catch (ApiException e) {
            log.error("设计师工作台-导出设计师账单失败, billId: {}, 错误: {}", billId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("设计师工作台-导出设计师账单异常, billId: {}", billId, e);
            throw new ApiException("下载账单失败，请稍后重试");
        }
    }
}
