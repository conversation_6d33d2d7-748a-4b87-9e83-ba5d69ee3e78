package com.xgwc.stylist.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 其他异议表
 */
@Data
public class StylistRecOtherDisputeDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键ID")
    private Long id;

    @FieldDesc("账单ID")
    private Long billId;

    @FieldDesc("异议ID")
    private Long disputeId;

    @FieldDesc("其他异议说明")
    private String otherRemarks;

    @FieldDesc("其他异议凭证")
    private String otherFile;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("账单异议")
    private List<BillOrderDto> stylistRecDisputeDto;


}
