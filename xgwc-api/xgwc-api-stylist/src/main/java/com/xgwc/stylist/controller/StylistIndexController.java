package com.xgwc.stylist.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.stylist.entity.vo.StylistIndexVo;
import com.xgwc.stylist.service.StylistIndexService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.controller
 * @Author: kouwen<PERSON>huo
 * @CreateTime: 2025-05-26  17:53
 */

/**
 * 设计师接单首页-Controller
 */
@RestController
@RequestMapping("/index")
public class StylistIndexController {

    @Resource
    private StylistIndexService stylistIndexService;

    /**
     * 首页
     * @param stylistIndexVo param
     * @return 统计
     */
    @MethodDesc("首页")
    @PostMapping("/list")
    public ApiResult list(@RequestBody StylistIndexVo stylistIndexVo) {
        return stylistIndexService.selectStylistOrderCount(stylistIndexVo);
    }

    /**
     * 获取接单概览
     * @param stylistIndexVo param
     * @return 近半年概览/累计数据
     */
    @MethodDesc("首页-接单概览")
    @GetMapping("/orderOverview")
    public ApiResult orderOverview(StylistIndexVo stylistIndexVo) {
        return stylistIndexService.selectStylistOrderOverview(stylistIndexVo);
    }

    /**
     * 首页-接单概览-折线图统计
     * @param stylistIndexVo param
     * @return 折线图统计
     */
    @MethodDesc("首页-接单概览-折线图统计")
    @GetMapping("/overviewLineChart")
    public ApiResult overview(StylistIndexVo stylistIndexVo) {
        return stylistIndexService.selectStylistOrderoverviewCount(stylistIndexVo);
    }

    /**
     * 首页-全部通知
     * @param stylistIndexVo param
     * @return 设计师通知列表
     */
    @MethodDesc("首页-全部通知")
    @GetMapping("/stylistAllNotice")
    public ApiResult stylistAllNotice(StylistIndexVo stylistIndexVo) {
        return stylistIndexService.selectStylistAllNotice(stylistIndexVo);
    }

}
