package com.xgwc.stylist.entity;

import lombok.Data;

import java.util.Date;


/**
 * 设计师接单文件表(stylist_order_files)实体类
 */
@Data
public class StylistOrderFiles {

private static final long serialVersionUID=1L;

    /** 文件ID */
    private Long fileId;

    /** 订单id（关联订单表） */
    private Long orderId;

    /** 定稿名称 */
    private String finalizeName;

    /** 预览图名称 */
    private String pictureName;

    /** 预览图路径 */
    private String picturePath;

    /** 定稿源文件名 */
    private String fileName;

    /** 定稿源文件路径 */
    private String filePath;

    /** 上传时间 */
    private Date uploadTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;



}