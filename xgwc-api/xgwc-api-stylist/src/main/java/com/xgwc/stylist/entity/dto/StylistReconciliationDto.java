package com.xgwc.stylist.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StylistReconciliationDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("账单ID")
    @Excel(name = "账单ID")
    private Long billId;

    @FieldDesc("账单周期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单周期结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date billPeriodEnd;

    @FieldDesc("账单周期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单周期开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date billPeriodStart;

    @FieldDesc("出账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "出账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date billingTime;

    @FieldDesc("结算品牌商")
    @Excel(name = "结算品牌商")
    private Long brandOwnerId;

    @FieldDesc("结算品牌商名称")
    @Excel(name = "结算品牌商名称")
    private String brandName;

    @FieldDesc("佣金追回")
    @Excel(name = "佣金追回")
    private BigDecimal commissionBack;

    @FieldDesc("确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmationTime;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @FieldDesc("罚款金额")
    @Excel(name = "罚款金额")
    private BigDecimal fineAmount;

    @FieldDesc("是否锁定：0锁定，1未锁定")
    @Excel(name = "是否锁定：0锁定，1未锁定")
    private Long isLock;

    @FieldDesc("管理员用户id")
    @Excel(name = "管理员用户id")
    private Long managerUserId;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @FieldDesc("无票扣款")
    @Excel(name = "无票扣款")
    private BigDecimal noInvoice;

    @FieldDesc("账单订单数")
    @Excel(name = "账单订单数")
    private Long orderCount;

    @FieldDesc("结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)")
    @Excel(name = "结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)")
    private Integer settlementStatus;

    @FieldDesc("结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结算时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date settlementTime;

    @FieldDesc("状态：0正常，其他非正常")
    @Excel(name = "状态：0正常，其他非正常")
    private Integer status;

    @FieldDesc("设计师id")
    @Excel(name = "设计师id")
    private Long stylistId;

    @FieldDesc("设计师名称")
    @Excel(name = "设计师名称")
    private String stylistName;

    @FieldDesc("账单佣金合计")
    @Excel(name = "账单佣金合计")
    private BigDecimal totalCommission;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @FieldDesc("是否提供发票：0-否 1-是")
    @Excel(name = "是否提供发票：0-否 1-是")
    private Integer isInvoice;

    @FieldDesc("应发金额")
    @Excel(name = "应发金额")
    private BigDecimal amount;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("billId",getBillId())
            .append("billPeriodEnd",getBillPeriodEnd())
            .append("billPeriodStart",getBillPeriodStart())
            .append("billingTime",getBillingTime())
            .append("brandOwnerId",getBrandOwnerId())
            .append("commissionBack",getCommissionBack())
            .append("confirmationTime",getConfirmationTime())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("fineAmount",getFineAmount())
            .append("isLock",getIsLock())
            .append("managerUserId",getManagerUserId())
            .append("modifyTime",getModifyTime())
            .append("noInvoice",getNoInvoice())
            .append("orderCount",getOrderCount())
            .append("settlementStatus",getSettlementStatus())
            .append("settlementTime",getSettlementTime())
            .append("status",getStatus())
            .append("stylistId",getStylistId())
            .append("stylistName",getStylistName())
            .append("totalCommission",getTotalCommission())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
