package com.xgwc.stylist.service;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.service
 * @Author: kou<PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-05-26  17:55
 */

import com.xgwc.common.entity.ApiResult;
import com.xgwc.stylist.entity.vo.StylistIndexVo;

/**
 * 设计师首页-Service接口
 */
public interface StylistIndexService {

    /**
     * 设计师首页-查询订单数量
     * @return count订单数量
     */
    ApiResult selectStylistOrderCount(StylistIndexVo stylistIndexVo);

    /**
     * 设计师首页-查询接单概览
     * @return 折线图数据
     */
    ApiResult selectStylistOrderoverviewCount(StylistIndexVo stylistIndexVo);

    /**
     * 设计师首页-查询全部通知
     * @return 设计师通知列表
     */
    ApiResult selectStylistAllNotice(StylistIndexVo stylistIndexVo);

    /**
     * 获取接单概览
     * @param stylistIndexVo param
     * @return 近半年概览/累计数据
     */
    ApiResult selectStylistOrderOverview(StylistIndexVo stylistIndexVo);
}
