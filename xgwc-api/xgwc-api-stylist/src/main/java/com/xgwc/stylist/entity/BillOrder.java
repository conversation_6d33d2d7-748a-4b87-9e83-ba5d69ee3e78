package com.xgwc.stylist.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.entity
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-14  18:20
 */
@Data
public class BillOrder {

    private Long id;                  // 主键
    private Long subBillId;          // 账单id
    private Long orderId;            // 订单id
    private Long saleManId;          // 谈单人员
    private String saleManName;      // 谈单人员名称
    private Long deptId;             // 录入部门编码
    private String deptName;         // 录入部门名称
    private Integer transferState;   // 订单类型：0正常单，1转化单,2协作单，3拍错单
    private Date orderDate;          // 下单日期
    private Long storeId;            // 订单来源ID (店铺id)
    private String storeName;        // 订单来源名称 (店铺名称)
    private Long brandId;            // 品牌商id
    private Integer payChannel;      // 支付方式 1:淘宝 2: 微信 3: 支付宝
    private Integer shType;          // 订单状态 (0：未发货 1：完成 2：退款 3：部分退款)
    private String orderNo;          // 订单编号
    private String taobaoId;         // 客户ID
    private BigDecimal orderAmount;      // 订单金额
    private BigDecimal amount;           // 实收金额
    private BigDecimal nowAmount;    // 当前金额：当有退款发生时有值
    private Integer payType;         // 付款方式:1全款/2阶段付
    private String allotRemark;      // 派单需求
    private Integer allotNum;        // 派单设计师数量
    private Long allotUserId;        // 派单人
    private String allotUserName;    // 派单人
    private Long pid;                // 母订单id
    private Long designerId;         // 设计师ID
    private String designerName;     // 设计师名称
    private String designerBusiness; // 设计师业务类型
    private BigDecimal money;            // 佣金金额
    private BigDecimal nowMoney;     // 现佣金：当有退款发生时有值
    private Date createTime;         // 创建时间
    private Long franchiseeId;       // 加盟商id
    private Date modifyTime;         // 行更新时间
    private Integer archiveType;     // 交稿状态:0 未接单, 1 制作中, 2 已交初稿, 3 定稿待审核
    private Date dealTime;           // 成交时间：结算时取值该时间，需满足：已归档，已交搞，没有售后中三个条件
    private Date archiveTime;        // 提交定稿日期(订单完成时间)
}
