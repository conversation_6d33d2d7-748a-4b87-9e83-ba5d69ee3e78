package com.xgwc.stylist.entity;

import lombok.Data;

import java.util.Date;


/**
 * 设计师接单表(stylist_orders)实体类
 */

@Data
public class StylistOrders {

private static final long serialVersionUID=1L;

    /** 订单id */
    private Long orderId;

    /** 订单编号 */
    private String orderNo;

    /** 设计师id */
    private Long stylistId;

    /** 客户ID */
    private String customerId;

    /** 客户名称 */
    private String customerName;

    /** 所属品牌商id */
    private Long brandOwnerId;

    /** 佣金（保留两位小数点） */
    private Long commission;

    /** 接单时间 */
    private Date orderTime;

    /** 约定初稿日期 */
    private Date scheduledDate;

    /** 订单状态（0-未发货、1-已完成、2-退款） */
    private Integer orderStatus;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;



}