package com.xgwc.stylist.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.entity
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-14  18:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BillOrderDto extends StylistRecDisputeDto{

    @FieldDesc("id")
    private Long id;                  // 主键

    @FieldDesc("账单id")
    private Long subBillId;          // 账单id

    @FieldDesc("订单id（bill_order主键）")
    private Long orderId;            // 订单id

    @FieldDesc("谈单人员")
    private Long saleManId;          // 谈单人员

    @FieldDesc("谈单人员名称")
    private String saleManName;      // 谈单人员名称

    @FieldDesc("录入部门id")
    private Long deptId;             // 录入部门编码

    @FieldDesc("录入部门名称")
    private String deptName;         // 录入部门名称

     @FieldDesc("订单类型：0正常单，1转化单,2协作单，3拍错单")
    private Integer transferState;   // 订单类型：0正常单，1转化单,2协作单，3拍错单

    @FieldDesc("下单日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;          // 下单日期

    @FieldDesc("订单来源（店铺id）")
    private Long storeId;            // 订单来源ID (店铺id)

    @FieldDesc("订单来源名称 (店铺名称)")
    private String storeName;        // 订单来源名称 (店铺名称)

    @FieldDesc("品牌商id")
    private Long brandId;            // 品牌商id

    @FieldDesc("支付方式 1:淘宝 2: 微信 3: 支付宝")
    private Integer payChannel;      // 支付方式 1:淘宝 2: 微信 3: 支付宝

    @FieldDesc("订单状态 (0：未发货 1：完成 2：退款 3：部分退款)")
    private Integer shType;          // 订单状态 (0：未发货 1：完成 2：退款 3：部分退款)

    @FieldDesc("订单编号")
    private String orderNo;          // 订单编号

    @FieldDesc("客户ID")
    private String taobaoId;         // 客户ID

    @FieldDesc("订单金额")
    private BigDecimal orderAmount;      // 订单金额

    @FieldDesc("实收金额")
    private BigDecimal amount;           // 实收金额

    @FieldDesc("当前金额")
    private BigDecimal nowAmount;    // 当前金额：当有退款发生时有值

    @FieldDesc("付款类型")
    private Integer payType;         // 付款方式:1全款/2阶段付

    @FieldDesc("派单需求")
    private String allotRemark;      // 派单需求

    @FieldDesc("派单设计师数量")
    private Integer allotNum;        // 派单设计师数量

    @FieldDesc("派单人id")
    private Long allotUserId;        // 派单人

    @FieldDesc("派单人")
    private String allotUserName;    // 派单人

    @FieldDesc("母订单id")
    private Long pid;                // 母订单id

    @FieldDesc("设计师ID")
    private Long designerId;         // 设计师ID

    @FieldDesc("设计师名称")
    private String designerName;     // 设计师名称

    @FieldDesc("设计师业务类型")
    private String designerBusiness; // 设计师业务类型

    @FieldDesc("佣金金额")
    private BigDecimal money;            // 佣金金额

    @FieldDesc("现佣金")
    private BigDecimal nowMoney;     // 现佣金：当有退款发生时有值

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;         // 创建时间

    @FieldDesc("加盟商id")
    private Long franchiseId;       // 加盟商id

    @FieldDesc("交稿状态:0 未接单, 1 制作中, 2 已交初稿, 3 定稿待审核")
    private Integer archiveType;     // 交稿状态:0 未接单, 1 制作中, 2 已交初稿, 3 定稿待审核

    @FieldDesc("成交时间：结算时取值该时间，需满足：已归档，已交搞，没有售后中三个条件")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dealTime;           // 成交时间：结算时取值该时间，需满足：已归档，已交搞，没有售后中三个条件

    @FieldDesc("提交定稿时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveTime;        // 提交定稿日期

    @FieldDesc("结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)")
    @Excel(name = "结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)")
    private Integer settlementStatus;

}
