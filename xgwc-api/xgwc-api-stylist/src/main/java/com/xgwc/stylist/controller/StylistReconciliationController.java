package com.xgwc.stylist.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.stylist.entity.dto.StylistReconciliationDto;
import com.xgwc.stylist.entity.vo.StylistRecOtherDisputeVo;
import com.xgwc.stylist.entity.vo.StylistReconciliationQueryVo;
import com.xgwc.stylist.service.StylistReconciliationService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.controller
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-26  11:26
 */
/**
 * 设计师对账-Controller
 */
@RestController
@RequestMapping("/reconciliation")
@Slf4j
public class StylistReconciliationController extends BaseController {

    @Resource
    private StylistReconciliationService stylistReconciliationService;

    /**
     * 查询设计师对账列表
     * @param stylistReconciliation 查询参数
     * @return ApiResult
     */
    @MethodDesc("查询设计师对账列表")
    @GetMapping("/list")
    public ApiResult<List<StylistReconciliationDto>> list(StylistReconciliationQueryVo stylistReconciliation) {
        startPage();
        return getDataTable(stylistReconciliationService.selectStylistReconciliationList(stylistReconciliation));
    }

    /**
     * 设计师账单确认
     * @param id 账单id
     * @return ApiResult
     */
    @MethodDesc("设计师账单确认")
    @PostMapping("/notarize/{id}")
    public ApiResult notarize(@PathVariable("id") Integer id) {
        return stylistReconciliationService.stylistReconciliationNotarize(id);
    }

    /**
     * 设计师下载账单
     * @param billId 账单id
     */
    @MethodDesc("设计师下载账单")
    @GetMapping("/downloadBill")
    public void downloadBill(HttpServletResponse request , @RequestParam(value = "billId") Long billId) {
        stylistReconciliationService.downloadBill(request,billId);
    }

    /**
     * 设计师异议回显
     * @param billId 账单id
     * @return ApiResult
     */
    @MethodDesc("订单异议回显")
    @GetMapping("/getInfoById")
    public ApiResult getInfoById(@RequestParam(value = "billId") Integer billId) {
        return stylistReconciliationService.stylistUpdateDisputeInfoById(billId);
    }

    /**
     * 设计师异议修改
     * @param stylistRecDisputeVo 账单参数
     * @return ApiResult
     */
    @MethodDesc("订单异议修改")
    @PostMapping("/update")
    public ApiResult update(@RequestBody StylistRecOtherDisputeVo stylistRecDisputeVo) {
        return stylistReconciliationService.updateStylistDispute(stylistRecDisputeVo);
    }

    /**
     * 订单异议新增
     * @param stylistRecDisputeVo 账单参数
     * @return ApiResult
     */
    @MethodDesc("订单异议新增")
    @PostMapping("/insert")
    public ApiResult insert(@RequestBody StylistRecOtherDisputeVo stylistRecDisputeVo) {
        return stylistReconciliationService.insertStylistDispute(stylistRecDisputeVo);
    }

    /**
     * 账单详情
     * @param id 账单id
     * @return ApiResult
     */
    @MethodDesc("账单详情")
    @GetMapping("/detail")
    public ApiResult detail(@RequestParam Long id) {
        return stylistReconciliationService.selecetStylistReconciliationDetail(id);
    }



}
