package com.xgwc.stylist.entity.dto;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.entity.dto
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-24  17:10
 */

import com.xgwc.common.entity.ApiResult;
import lombok.Data;

/**
 * 对账-结算状态统计
 */
@Data
public class StylistReconciliationStatus {

    //结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)

    private Integer pendingConfCount; // 待确认账单 (0)

    private Integer disputedCount;    // 有异议账单 (1)

    private Integer pendingSetCount;  // 待结算账单 (2)

    private Integer invoiceRejectionCount;     // 发票被驳回 (5)


    /** 接单列表*/
    private ApiResult<StylistOrdersDto> stylistOrdersDto;
}
