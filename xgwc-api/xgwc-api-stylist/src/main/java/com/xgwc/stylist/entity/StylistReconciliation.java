package com.xgwc.stylist.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设计师对账表(bill_designer)实体类
 * 账单信息表(StylistReconciliation)实体类
 */
@Data
public class StylistReconciliation {

private static final long serialVersionUID=1L;

    /** 账单ID */
    private Long billId;

    /** 账单周期结束日期 */
    private Date billPeriodEnd;

    /** 账单周期开始日期 */
    private Date billPeriodStart;

    /** 出账时间 */
    private Date billingTime;

    /** 结算品牌商 */
    private Long brandOwnerId;

    /** 佣金追回 */
    private BigDecimal commissionBack;

    /** 确认时间 */
    private Date confirmationTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 罚款金额 */
    private BigDecimal fineAmount;

    /** 是否锁定：0锁定，1未锁定 */
    private Long isLock;

    /** 管理员用户id */
    private Long managerUserId;

    /** 行修改时间 */
    private Date modifyTime;

    /** 无票扣款 */
    private BigDecimal noInvoice;

    /** 账单订单数 */
    private Long orderCount;

    /** 结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败) */
    private Long settlementStatus;

    /** 结算时间 */
    private Date settlementTime;

    /** 状态：0正常，其他非正常 */
    private Long status;

    /** 设计师id */
    private Long stylistId;

    /** 设计师名称 */
    private String stylistName;

    /** 账单佣金合计 */
    private BigDecimal totalCommission;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;



}