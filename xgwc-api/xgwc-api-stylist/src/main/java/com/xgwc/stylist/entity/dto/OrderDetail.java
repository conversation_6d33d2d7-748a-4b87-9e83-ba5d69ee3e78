package com.xgwc.stylist.entity.dto;

import com.xgwc.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

// 数据项类
@Data
public class OrderDetail {

    @Excel(name = "账单id", sort = 1)
    private Long subBillId;

    @Excel(name = "订单编号", sort = 2)
    private String orderNo;

    @Excel(name = "客户ID", sort = 3)
    private String taobaoId;

    @Excel(name = "业务分类", sort = 4)
    private String designerBusiness;

    @Excel(name = "交定稿时间", sort = 5, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date archiveTime;

    @Excel(name = "订单完成时间", sort = 6, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date finishedTime;

    @Excel(name = "订单成交时间", sort = 7, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dealTime;

    @Excel(name = "佣金", sort = 8)
    private BigDecimal money;


}