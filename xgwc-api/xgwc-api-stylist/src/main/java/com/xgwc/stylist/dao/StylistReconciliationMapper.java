package com.xgwc.stylist.dao;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.dao
 * @Author: k<PERSON><PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-05-26  11:31
 */

import com.xgwc.stylist.entity.dto.BillOrderDto;
import com.xgwc.stylist.entity.dto.StylistRecOtherDisputeDto;
import com.xgwc.stylist.entity.dto.StylistReconciliationDto;
import com.xgwc.stylist.entity.vo.StylistRecDisputeVo;
import com.xgwc.stylist.entity.vo.StylistRecOtherDisputeVo;
import com.xgwc.stylist.entity.vo.StylistReconciliationQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设计师对账-Mapper接口
 */
public interface StylistReconciliationMapper {

    /**
     * 设计师账单确认
     * @param id 账单id
     * @return 影响行数
     */
    int stylistReconciliationNotarize(@Param("id") Integer id);

    /**
     * 设计师账单异议详情
     *
     * @param id 账单id
     * @return 详情
     */
    List<BillOrderDto> stylistRecDisputeById(@Param("id") Integer id);

    /**
     * 设计师账单异议修改
     * @param stylistRecDisputeVo 账单信息
     * @return 影响行数
     */
    int updateStylistRecDispute(@Param("stylistRecDisputeVo") List<StylistRecDisputeVo> stylistRecDisputeVo);

    /**
     * 设计师账单异议新增
     * @param stylistRecOtherDisputeVo 账单信息
     * @return 影响行数
     */
    int upsertStylistRecDispute(@Param("stylistRecOtherDisputeVo") StylistRecOtherDisputeVo stylistRecOtherDisputeVo);

    /**
     * 设计师对账列表
     * @param stylistReconciliation 查询参数
     * @return 列表
     */
    List<StylistReconciliationDto> selectStylistReconciliationList(@Param("stylistReconciliation") StylistReconciliationQueryVo stylistReconciliation);

    /**
     * 修改对账单状态为有异议
     * @param billId 账单id
     * @return 影响行数
     */
    int updateStyRecStatus(@Param("billId") Long billId);

    /**
     * 添加设计师账单其他异议
     * @param stylistRecDisputeVo   其他异议信息
     * @return 影响行数
     */
    int upsertStylistRecOtherDispute(@Param("stylistRecDisputeVo") StylistRecOtherDisputeVo stylistRecDisputeVo);

    /**
     * 修改设计师账单其他异议
     * @param stylistRecDisputeVo   其他异议信息
     * @return 影响行数
     */
    int updateStylistRecOtherDispute(@Param("stylistRecDisputeVo") StylistRecOtherDisputeVo stylistRecDisputeVo);

    /**
     * 查询设计师账单其他异议
     *
     * @param id 账单id
     * @return 详情
     */
    StylistRecOtherDisputeDto selectStyRecOtherDispute(Integer id);

    /**
     * 设计师账单详情
     *
     * @param id 账单id
     * @return 账单详情
     */
    List<BillOrderDto> stylistReconciliationInfoById(@Param("id") Long id);

    /**
     * 删除设计师账异议
     *
     * @param billId 账单id
     */
    int deleteRecDispute(@Param("billId") Long billId);
}
