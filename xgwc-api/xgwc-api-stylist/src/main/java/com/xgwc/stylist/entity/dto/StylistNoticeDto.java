package com.xgwc.stylist.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.entity.dto
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-29  09:32
 */
@Data
public class StylistNoticeDto {

    /**
     * 通知分类ID
     */
    @FieldDesc("通知分类ID")
    private Integer id;

    /**
     * 通知类型：1站内信，2短信，3企微
     */
    @FieldDesc("通知类型：1站内信，2短信，3企微")
    private Integer noticeType;

    /**
     * 分类id
     */
    @FieldDesc("分类id")
    private String classifyId;

    /**
     * 分类名称
     */
    @FieldDesc("分类名称")
    private String classifyName;

    /**
     * 品牌商名称
     */
    @FieldDesc("品牌商名称")
    private String brandName;

    /**
     * 品牌商ID
     */
    @FieldDesc("品牌商ID")
    private Long brandId;

    /**
     * 标题
     */
    @FieldDesc("标题")
    private String title;

    /**
     * 内容
     */
    @FieldDesc("内容")
    private String content;

    /**
     * 附件
     */
    @FieldDesc("附件")
    private String attachment;


    /**
     * 发布时间
     */
    @FieldDesc("发布时间")
    private String publishTime;

    /**
     * 是否已读
     */
    @FieldDesc("是否已读")
    private Integer isRead;

    /**
     * 发布人
     */
    @FieldDesc("发布人")
    private String createBy;

    /**
     * 来源：1品牌商，2加盟商
     */
    @FieldDesc("来源：1品牌商，2加盟商")
    private Integer source;

    /**
     * 来源ID
     */
    @FieldDesc("来源ID")
    private Long sourceId;
}


