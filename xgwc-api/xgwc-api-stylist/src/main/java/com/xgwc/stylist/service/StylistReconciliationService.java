package com.xgwc.stylist.service;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.service
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-26  11:27
 */

import com.xgwc.common.entity.ApiResult;
import com.xgwc.stylist.entity.dto.StylistReconciliationDto;
import com.xgwc.stylist.entity.vo.StylistRecOtherDisputeVo;
import com.xgwc.stylist.entity.vo.StylistReconciliationQueryVo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 设计师对账-Service接口
 */
public interface StylistReconciliationService {

    /**
     * 查询设计师对账列表
     *
     * @param stylistReconciliation 查询参数
     * @return 结果
     */
    List<StylistReconciliationDto> selectStylistReconciliationList(StylistReconciliationQueryVo stylistReconciliation);

    /**
     * 设计师账单确认
     * @param id 账单id
     * @return 结果
     */
    ApiResult stylistReconciliationNotarize(Integer id);

    /**
     * 设计师下载账单
     * @param stylistReconciliation 查询参数
     * @return 结果
     */
    ApiResult stylistReconciliationDownload(StylistReconciliationQueryVo stylistReconciliation);

    /**
     * 设计师异议详情
     * @param id 账单id
     * @return 结果
     */
    ApiResult stylistUpdateDisputeInfoById(Integer id);

    /**
     * 修改设计师对账异议信息
     * @param stylistRecDisputeVo 修改参数
     * @return 结果
     */
    ApiResult updateStylistDispute(StylistRecOtherDisputeVo stylistRecDisputeVo);

    /**
     * 新增设计师对账异议信息
     * @param stylistReconciliation 新增参数
     * @return 结果
     */
    ApiResult insertStylistDispute(StylistRecOtherDisputeVo stylistReconciliation);

    /**
     * 账单详情
     *
     * @param id 订单id
     * @return 订单详情
     */
    ApiResult selecetStylistReconciliationDetail(Long id);

    /**
     * 设计师账单下载
     *
     * @param request  request
     * @param billId  账单id
     */
    void downloadBill(HttpServletResponse request, Long billId);
}
