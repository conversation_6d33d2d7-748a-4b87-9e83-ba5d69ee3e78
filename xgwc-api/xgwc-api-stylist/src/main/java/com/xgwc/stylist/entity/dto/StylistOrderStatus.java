package com.xgwc.stylist.entity.dto;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist.entity.dto
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-24  17:10
 */

import com.xgwc.common.entity.ApiResult;
import lombok.Data;

/**
 * 接单-交稿状态统计
 */
@Data
public class StylistOrderStatus {

    private Integer initialDraftCount;    // 制作中 (1)

    private Integer initialReviewCount;   // 已交初稿 (2)

    private Integer finalDraftCount;      // 定稿待审核 (3)

    private Integer rejectedCount;        // 定稿被驳回 (4)

    private Integer finalReviewCount;     // 已交定稿 (5)

    /** 接单列表*/
    private ApiResult<StylistOrdersDto> stylistOrdersDto;
}
