package com.xgwc.stylist;

import com.xgwc.order.feign.api.OrderFeign;
import com.xgwc.user.feign.api.DesignerFeign;
import com.xgwc.user.feign.api.NoticeFeign;
import com.xgwc.user.feign.api.UserDetailFeign;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.stylist
 * @Author: kouwenzhuo
 * @CreateTime: 2025-05-24  18:22
 */
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.xgwc.*"})
@ComponentScan("com.xgwc")
@MapperScan("com.xgwc.stylist.dao")
@EnableFeignClients(clients = {UserDetailFeign.class, DesignerFeign.class,
        OrderFeign.class, NoticeFeign.class})
@Slf4j
public class StylistApplication {

    public static void main(String[] args) {
        SpringApplication.run(StylistApplication.class, args);
        log.info("项目启动成功!");
    }
}
