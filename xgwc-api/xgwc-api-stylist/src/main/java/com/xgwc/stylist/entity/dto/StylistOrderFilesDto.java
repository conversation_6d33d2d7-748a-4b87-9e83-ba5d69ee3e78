package com.xgwc.stylist.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

@Data
public class StylistOrderFilesDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("文件ID")
    @Excel(name = "文件ID")
    private Long fileId;

    @FieldDesc("订单id（关联订单表）")
    @Excel(name = "订单id（关联订单表）")
    private Long orderId;

    @FieldDesc("定稿名称")
    @Excel(name = "定稿名称")
    private String finalizeName;

    @FieldDesc("预览图名称")
    @Excel(name = "预览图名称")
    private String pictureName;

    @FieldDesc("预览图路径")
    @Excel(name = "预览图路径")
    private String picturePath;

    @FieldDesc("定稿源文件名")
    @Excel(name = "定稿源文件名")
    private String fileName;

    @FieldDesc("定稿源文件路径")
    @Excel(name = "定稿源文件路径")
    private String filePath;

    @FieldDesc("上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadTime;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("交稿状态")
    private List<StylistOrderSubmitDto> stylistOrderSubmitDtos;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("fileId",getFileId())
            .append("orderId",getOrderId())
            .append("finalizeName",getFinalizeName())
            .append("pictureName",getPictureName())
            .append("picturePath",getPicturePath())
            .append("fileName",getFileName())
            .append("filePath",getFilePath())
            .append("uploadTime",getUploadTime())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
        .toString();
    }
}
