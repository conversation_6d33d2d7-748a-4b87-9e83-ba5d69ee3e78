spring:
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  config:
    import: "nacos:xgwc-api-stylist.yml"
  application:
    name: xgwc-api-stylist
  cloud:
    nacos:
      server-addr: 192.168.1.189:8848
      username: nacos
      password: 2BEheJdWQ9tzHfYmy9FH
      config:
        namespace: 2a0d71a6-f9f3-44a0-81bb-26215a7a6fc3
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
server:
  port: 8083
mybatis:
  type-aliases-package: com.xgwc.stylist.entity
  mapper-locations: classpath:mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
logging:
  level:
    com.xgwc.stylist.dao: debug