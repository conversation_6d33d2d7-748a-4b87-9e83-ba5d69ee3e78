<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.stylist.dao.StylistIndexMapper">

    <select id="selectStylistOrdersHalfYearList" resultType="com.xgwc.stylist.entity.dto.StylistOrdersDto">
        select
            so.order_id as orderId,
            so.stylist_id as stylistId,
            so.customer_id as customerId,
            ifnull(xo.now_money, so.commission) as commission
        from stylist_orders so
                 left join xgwc_order xo on so.order_id = xo.id
        where so.create_time >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
            and so.manager_user_id = #{stylistId}
    </select>

    <select id="selectStylistReconciliationHalfYearList"
            resultType="com.xgwc.stylist.entity.dto.StylistReconciliationDto">
        select
            sr.bill_id as billId,
            sr.result_amount as totalCommission,
            sr.settlement_status as settlementStatus
        from bill_designer sr
        where sr.create_time >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
            and sr.manager_user_id = #{stylistId}
    </select>
    <select id="selectXgwcOrders" resultType="com.xgwc.stylist.entity.StylistOrders">
        SELECT id AS orderId,
               create_time as createTime
        FROM xgwc_order
        WHERE id = #{orderId}
          AND sh_type = 2
    </select>
    <select id="selectStylistOrdersAllYearList" resultType="com.xgwc.stylist.entity.dto.StylistOrdersDto">
        select
            so.order_id as orderId,
            so.stylist_id as stylistId,
            so.customer_id as customerId,
            ifnull(xo.now_money, so.commission) as commission
        from stylist_orders so
                 left join xgwc_order xo on so.order_id = xo.id
        where so.manager_user_id = #{stylistId}
    </select>
    <select id="selectselectStylistReconciliationAllYearList"
            resultType="com.xgwc.stylist.entity.dto.StylistReconciliationDto">
        select
            sr.bill_id as billId,
            sr.result_amount as totalCommission,
            sr.settlement_status as settlementStatus
        from bill_designer sr
        where sr.manager_user_id = #{stylistId}
    </select>
    <select id="selectManagerUserId" resultType="java.lang.Long">
        select
            designer_id as id
        from xgwc_designer
        where manager_user_id = #{userId}
    </select>
</mapper>