<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.stylist.dao.StylistOrdersMapper">
    <delete id="deleteStylistOrdersByOrderId">
        DELETE FROM stylist_order_submit WHERE submit_id = #{submitId}
    </delete>

    <select id="selectStylistOrdersList" parameterType="com.xgwc.stylist.entity.vo.StylistOrdersQueryVo" resultType="com.xgwc.stylist.entity.dto.StylistOrdersDto">
        SELECT
        so.order_id as orderId,
        so.order_no as orderNo,
        so.stylist_id as stylistId,
        so.stylist_name as stylistName,
        so.customer_id as customerId,
        so.customer_name as customerName,
        so.brand_owner_id as brandOwnerId,
        xbo.company_name as brandOwnerName,
        ifnull(xop.now_money, so.commission) as commission,
        so.commission,
        so.order_time as orderTime,
        so.scheduled_date as scheduledDate,
        so.order_status as orderStatus,
        xop.archive_type as archiveType,
        so.create_time as createTime
        FROM
        stylist_orders so
        LEFT JOIN xgwc_brand_owner xbo ON so.brand_owner_id = xbo.brand_id
        LEFT JOIN (
        SELECT
        xop1.id,
        xop1.archive_type as archive_type,
        xop1.now_money as now_money
        FROM
        xgwc_order xop1
        ) xop ON so.order_id = xop.id and xop.archive_type > 0
        <where>
            so.manager_user_id = #{stylistOrders.managerUserId}

            <if test="stylistOrders.orderNo != null and stylistOrders.orderNo != ''">
                and (so.order_no = #{stylistOrders.orderNo} or so.customer_id = #{stylistOrders.orderNo})
            </if>
            <if test="stylistOrders.submitStatus != null ">
                and xop.archive_type = #{stylistOrders.submitStatus}
            </if>
            <if test="stylistOrders.customerName != null  and stylistOrders.customerName != ''">
                and so.customer_name = #{stylistOrders.customerName}
            </if>
            <if test="stylistOrders.stylistName != null  and stylistOrders.stylistName != ''">
                and so.stylist_name = #{stylistOrders.stylistName}
            </if>
            <if test="stylistOrders.brandOwnerId != null ">
                and so.brand_owner_id = #{stylistOrders.brandOwnerId}
            </if>
            <if test="stylistOrders.commission != null ">
                and so.commission = #{stylistOrders.commission}
            </if>
            <if test="stylistOrders.year != null ">
                AND YEAR(so.create_time) = #{stylistOrders.year}
            </if>
            <if test="stylistOrders.orderTime != null ">
                and so.order_time between #{stylistOrders.orderTime[0]} and #{stylistOrders.orderTime[1]}
            </if>
            <if test="stylistOrders.scheduledDate != null ">
                and so.scheduled_date between #{stylistOrders.scheduledDate[0]} and #{stylistOrders.scheduledDate[1]}
            </if>
            <if test="stylistOrders.orderStatus != null">
                and so.order_status = #{stylistOrders.orderStatus}
            </if>
        ORDER BY so.order_time DESC
        </where>
    </select>
    
    <select id="selectStylistOrdersSubmitByOrderId" parameterType="Long" resultType="com.xgwc.stylist.entity.dto.StylistOrderSubmitDto">
        SELECT
            sos.submit_id as submitId,
            sos.order_id as orderId,
            sos.submit_status as submitStatus,
            sos.create_time as createTime
            from stylist_order_submit sos
        where sos.order_id = #{orderId}
    </select>
    <select id="selectStylistOrdersFileByOrderId"
            resultType="com.xgwc.stylist.entity.dto.StylistOrderFilesDto">
        select
            sof.file_id as fileId,
            so.order_id as orderId,
            so.customer_id as customerId,
            so.customer_name as customerName,
            so.brand_owner_id as brandOwnerId,
            so.scheduled_date as scheduledDate,
            sof.finalize_name as finalizeName,
            sof.picture_name as pictureName,
            sof.picture_path as picturePath,
            sof.file_name as fileName,
            sof.file_path as filePath
        from stylist_order_files sof
                LEFT JOIN stylist_orders so ON so.order_id = sof.order_id
        where
            sof.order_id = #{orderId}
    </select>

    <select id="selectLineChartYear">
        SELECT MONTH(order_time) AS `month`, COUNT(*) AS orderCount
        FROM stylist_orders
        <where>
            AND manager_user_id = #{stylistIndexVo.managerUserId}

            <if test="stylistIndexVo.year != null">
                AND YEAR(order_time) = #{stylistIndexVo.year}
            </if>
            <if test="stylistIndexVo.orderOverviewBrandOwner != null">
                AND brand_owner_id = #{stylistIndexVo.orderOverviewBrandOwner}
            </if>
        </where>
        GROUP BY `month`
        ORDER BY `month`
    </select>

    <select id="selectXgwcOrdersBatch" resultType="com.xgwc.stylist.entity.StylistOrders">
        SELECT id AS orderId
        FROM xgwc_order
        WHERE id IN
        <foreach collection="orderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
          AND refund_status in (2, 1)
          AND create_time >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
    </select>
    <select id="selectStylistOrderById" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM stylist_orders
        WHERE order_id = #{id}
    </select>
    <select id="selectmanagerUserId" resultType="java.lang.Long">
        SELECT manager_user_id AS managerUserId
        FROM xgwc_designer
        WHERE designer_id = #{stylistId}
    </select>
    <select id="selectDesignerJoinBrandById" resultType="java.lang.Long">
        SELECT brand_id AS brandId
        FROM xgwc_designer
        WHERE designer_id = #{designerId}
    </select>
    <select id="selectXgwcOrdersBatchAll">
        SELECT
        MONTH(create_time) AS `month`,
        COUNT(*) AS orderCount
        FROM xgwc_order
        WHERE id IN
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item.orderId}
        </foreach>
        AND refund_status in (2, 1)
        GROUP BY `month`
        ORDER BY `month`
    </select>

    <insert id="insertStylistOrderSubmit" parameterType="com.xgwc.stylist.entity.StylistOrderSubmit">
        insert into stylist_orders
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="submitId != null">submit_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="submitId != null">#{stylistOrders.submitId},</if>
            <if test="orderId != null">#{stylistOrders.orderId},</if>
            <if test="submitStatus != null">#{stylistOrders.submitStatus},</if>
            <if test="createBy != null">#{stylistOrders.createBy},</if>
            now()
         </trim>
    </insert>
    <insert id="insertStylistOrderFiles" parameterType="com.xgwc.stylist.entity.StylistOrderFiles">
        insert into stylist_order_files
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">file_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="finalizeName != null">finalize_name,</if>
            <if test="pictureName != null">picture_name,</if>
            <if test="picturePath != null">picture_path,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path</if>
            upload_time,
            create_time,
            <if test="createBy != null">create_by</if>
        </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">#{fileId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="finalizeName != null">#{finalizeName},</if>
            <if test="pictureName != null">#{pictureName},</if>
            <if test="picturePath != null">#{picturePath},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            now(),
            now(),
            <if test="createBy != null">#{createBy}</if>
            </trim>
    </insert>
    <insert id="insertOrders" parameterType="com.xgwc.stylist.entity.vo.StylistOrdersVo">
        insert into stylist_orders
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dto.orderId != null">order_id,</if>
            <if test="dto.orderNo != null">order_no,</if>
            <if test="dto.stylistId != null">stylist_id,</if>
            <if test="dto.stylistName != null">stylist_name,</if>
            <if test="dto.managerUserId != null">manager_user_id,</if>
            <if test="dto.customerId != null">customer_id,</if>
            <if test="dto.customerName != null">customer_name,</if>
            <if test="dto.brandOwnerId != null">brand_owner_id,</if>
            <if test="dto.commission != null">commission,</if>
            <if test="dto.orderTime != null">order_time,</if>
            <if test="dto.scheduledDate != null">scheduled_date,</if>
            <if test="dto.orderStatus != null">order_status,</if>
            <if test="dto.createBy != null">create_by,</if>
            create_time,
        </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dto.orderId != null">#{dto.orderId},</if>
            <if test="dto.orderNo != null">#{dto.orderNo},</if>
            <if test="dto.stylistId != null">#{dto.stylistId},</if>
            <if test="dto.stylistName != null">#{dto.stylistName},</if>
            <if test="dto.managerUserId != null">#{dto.managerUserId},</if>
            <if test="dto.customerId != null">#{dto.customerId},</if>
            <if test="dto.customerName != null">#{dto.customerName},</if>
            <if test="dto.brandOwnerId != null">#{dto.brandOwnerId},</if>
            <if test="dto.commission != null">#{dto.commission},</if>
            <if test="dto.orderTime != null">#{dto.orderTime},</if>
            <if test="dto.scheduledDate != null">#{dto.scheduledDate},</if>
            <if test="dto.orderStatus != null">#{dto.orderStatus},</if>
            <if test="dto.createBy != null">#{dto.createBy},</if>
            now()
            </trim>
    </insert>

    <insert id="insertOrderSubmit" parameterType="com.xgwc.stylist.entity.vo.StylistOrderSubmitVo">
        insert into stylist_order_submit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dto.orderId != null">order_id,</if>
            <if test="dto.orderStatus != null">submit_status,</if>
            <if test="dto.createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dto.orderId != null">#{dto.orderId},</if>
            <if test="dto.orderStatus != null">0,</if>
            <if test="dto.createBy != null">#{dto.createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateStylistOrders" parameterType="com.xgwc.stylist.entity.StylistOrders">
        update stylist_orders
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="brandOwnerId != null">brand_owner_id = #{brandOwnerId},</if>
            <if test="commission != null">commission = #{commission},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="scheduledDate != null">scheduled_date = #{scheduledDate},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = #{updateTime}
        </trim>
        where order_id = #{orderId}
    </update>


</mapper>