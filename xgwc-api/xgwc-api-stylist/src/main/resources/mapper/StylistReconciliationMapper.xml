<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.stylist.dao.StylistReconciliationMapper">

    <insert id="upsertStylistRecDispute" >
        INSERT INTO `xgwc_sass`.`stylist_rec_dispute`
        (`id`,`dispute_id`, `bill_id`, `order_id`, `dispute_description`, `evidence_file`, `create_by`, `create_time`, `update_by`, `update_time`)
        VALUES
        <foreach collection="stylistRecOtherDisputeVo.stylistRecDisputeVo" item="item" separator=",">
            (#{item.id},#{stylistRecOtherDisputeVo.id}, #{stylistRecOtherDisputeVo.billId}, #{item.orderId}, #{item.disputeDescription}, #{item.evidenceFile},
            #{stylistRecOtherDisputeVo.createBy}, now(), #{stylistRecOtherDisputeVo.updateBy}, now())
        </foreach>
        ON DUPLICATE KEY UPDATE
                              `dispute_description` = VALUES(`dispute_description`),
                              `evidence_file` = VALUES(`evidence_file`),
                              `update_by` = VALUES(`update_by`),
                              `update_time` = VALUES(`update_time`)
    </insert>

    <insert id="upsertStylistRecOtherDispute" useGeneratedKeys="true" keyProperty="stylistRecDisputeVo.id">
        INSERT INTO `xgwc_sass`.`stylist_rec_other_dispute`
        (`id`, `bill_id`, `other_remarks`, `other_file`, `create_by`, `create_time`, `update_by`, `update_time`)
        VALUES
            (#{stylistRecDisputeVo.disputeId}, #{stylistRecDisputeVo.billId}, #{stylistRecDisputeVo.otherRemarks},
             #{stylistRecDisputeVo.otherFile}, #{stylistRecDisputeVo.createBy}, now(), #{stylistRecDisputeVo.updateBy}, now())
            ON DUPLICATE KEY UPDATE
                                 `other_remarks` = VALUES(`other_remarks`),
                                 `other_file` = VALUES(`other_file`),
                                 `update_by` = VALUES(`update_by`),
                                 `update_time` = VALUES(`update_time`)
    </insert>


    <update id="stylistReconciliationNotarize">
        update bill_designer
        set settlement_status = 2
        where bill_id = #{id}
    </update>

    <update id="updateStylistRecDispute" parameterType="com.xgwc.stylist.entity.dto.StylistRecDisputeDto">
        <foreach collection="stylistRecDisputeVo" item="item" separator=",">
            UPDATE `xgwc_sass`.`stylist_rec_dispute`
            SET
            `dispute_description` = #{item.disputeDescription},
            `evidence_file` = #{item.evidenceFile},
            `update_by` = #{item.updateBy},
            `update_time` = now()
            WHERE `bill_id` = #{item.billId}
        </foreach>
    </update>
    <update id="updateStyRecStatus">
        UPDATE `xgwc_sass`.`bill_designer`
        SET `settlement_status` = 1
        WHERE `bill_id` = #{billId};
    </update>
    <update id="updateStylistRecOtherDispute">
        UPDATE `xgwc_sass`.`stylist_rec_other_dispute`
        SET
            `other_remarks` = #{stylistRecDisputeVo.otherRemarks},
            `other_file` = #{stylistRecDisputeVo.otherFile},
            `update_by` = #{stylistRecDisputeVo.updateBy},
            `update_time` = now()
        WHERE `bill_id` = #{stylistRecDisputeVo.billId}
    </update>
    <delete id="deleteRecDispute">
        DELETE FROM `xgwc_sass`.`stylist_rec_dispute`
        WHERE `bill_id` = #{billId}
    </delete>

    <select id="stylistRecDisputeById" resultType="com.xgwc.stylist.entity.dto.BillOrderDto">
        select
            bo.id as orderId,
            bo.order_no as orderNo,
            bo.taobao_id as taobaoId,
            bo.designer_business as designerBusiness,
            bo.archive_time as archiveTime,
            bo.deal_time as dealTime,
            bo.money,
            bds.settlement_status as settlementStatus,
            srd.id,
            srd.bill_id as billId,
            srd.dispute_id as disputeId,
            srd.dispute_description as disputeDescription,
            srd.evidence_file as evidenceFile
        from bill_designer_sub bds
                 left join bill_order bo on bds.sub_bill_id = bo.sub_bill_id
                 left join stylist_rec_dispute srd on bo.id = srd.order_id
        where bds.bill_id = #{id}
    </select>
    <select id="selectStylistReconciliationList" resultType="com.xgwc.stylist.entity.dto.StylistReconciliationDto">
        select
        sr.bill_id as billId,
        sr.bill_period_start as billPeriodStart,
        sr.bill_period_end as billPeriodEnd,
        sr.order_count as orderCount,
        SUM(bo.money) as totalCommission,
        sr.no_invoice as noInvoice,
        sr.commission_back as commissionBack,
        sr.fine_amount as fineAmount,
        sr.brand_owner_id as brandOwnerId,
        ifnull(xbo.company_simple_name, xbo.company_name) as brandName,
        sr.stylist_id as stylistId,
        sr.stylist_name as stylistName,
        sr.billing_time as billingTime,
        sr.is_invoice as isInvoice,
        sr.confirmation_time as confirmationTime,
        sr.settlement_time as settlementTime,
        sr.settlement_status as settlementStatus
        from bill_designer sr
        left join xgwc_brand_owner xbo on sr.brand_owner_id = xbo.brand_id
        left join bill_designer_sub bds on sr.bill_id = bds.bill_id
        left join bill_order bo on bds.sub_bill_id = bo.sub_bill_id
        <where>
            sr.manager_user_id = #{stylistReconciliation.managerUserId}

            <if test="stylistReconciliation.brandOwnerId != null">
                and sr.brand_owner_id = #{stylistReconciliation.brandOwnerId}
            </if>
            <if test="stylistReconciliation.stylistId != null">
                and sr.stylist_id = #{stylistReconciliation.stylistId}
            </if>
            <if test="stylistReconciliation.year != null">
                AND YEAR(sr.create_time) = #{stylistReconciliation.year}
            </if>
            <if test="stylistReconciliation.settlementStatus != null">
                and sr.settlement_status = #{stylistReconciliation.settlementStatus}
            </if>
            <if test="stylistReconciliation.billId != null">
                and sr.bill_id = #{stylistReconciliation.billId}
            </if>
        </where>
        GROUP BY
        sr.bill_id
    </select>
    <select id="selectStyRecOtherDispute" resultType="com.xgwc.stylist.entity.dto.StylistRecOtherDisputeDto">
        select
            srd.id as disputeId,
            srd.bill_id as billId,
            srd.other_remarks as otherRemarks,
            srd.other_file as otherFile
        from stylist_rec_other_dispute srd
        where srd.bill_id = #{billId}
    </select>
    <select id="stylistReconciliationInfoById" resultType="com.xgwc.stylist.entity.dto.BillOrderDto">
        select
            bo.id as orderId,
            bo.order_no as orderNo,
            bo.taobao_id as taobaoId,
            b.business_name as designerBusiness,
            bo.archive_time as archiveTime,
            bo.deal_time as dealTime,
            bo.money,
            bds.settlement_status as settlementStatus
        from bill_designer_sub bds
        left join bill_order bo on bds.sub_bill_id = bo.sub_bill_id
        left join xgwc_designer d on bo.designer_id = d.designer_id
        left join xgwc_business b on d.good_business = b.business_id
        where bds.bill_id = #{id}
    </select>
</mapper>