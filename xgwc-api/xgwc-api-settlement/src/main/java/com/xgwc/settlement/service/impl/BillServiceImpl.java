package com.xgwc.settlement.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.enums.PaymentTypeEnums;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.settlement.dao.*;
import com.xgwc.settlement.entity.*;
import com.xgwc.settlement.entity.dto.BillOrderDto;
import com.xgwc.settlement.entity.dto.OrderDto;
import com.xgwc.settlement.entity.vo.OrderVo;
import com.xgwc.settlement.service.BillService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillServiceImpl implements BillService {

    @Resource
    private XgwcPaymentDaysMapper xgwcPaymentDaysMapper;

    @Resource
    private XgwcOrderMapper xgwcOrderMapper;

    @Resource
    private XgwcDesignerMapper xgwcDesignerMapper;

    @Resource
    private BillDesignerMapper billDesignerMapper;

    @Resource
    private BillOrderMapper billOrderMapper;

    @Resource
    private BrandOwnerMapper brandOwnerMapper;

    @Override
    public void publishBill() {
        //第一步 获取所有品牌商
        List<BrandOwner> brandOwners = brandOwnerMapper.getAllBrandOwnerList();
        if (brandOwners != null && !brandOwners.isEmpty()) {
            for (BrandOwner brandOwner : brandOwners) {
                publishBrandOrderBill(brandOwner);
            }
        }
    }


    @Override
    public void publishBill(Long brandId, Long periodId) {
        if(brandId !=null && periodId != null) {
            BillPeriod billPeriod = xgwcPaymentDaysMapper.selectBillPeriodById(periodId);
            if(billPeriod != null) {
                List<OrderDto> orderDtos = selectFinishedMainOrderListInBill(brandId, billPeriod);
                if (!orderDtos.isEmpty()) {
                    Map<Long, List<OrderDto>> designerIdMap = orderDtos.stream().collect(Collectors.groupingBy(OrderDto::getDesignerId));
                    for (Map.Entry<Long, List<OrderDto>> entry : designerIdMap.entrySet()) {
                        BillDesigner billDesigner = handleDesignerBill(entry.getValue(), billPeriod);
                        //处理子账单
                        generateDesignerBillSub(entry.getValue(), billDesigner);
                    }
                }
            }
        }
    }

    @Transactional
    public void publishBrandOrderBill(BrandOwner brandOwner){
        Long brandId = brandOwner.getBrandId();
        //查看是否到账期
        BillPeriod billPeriod = isProductNewBill(brandOwner.getBrandId());
        if(billPeriod == null){
            log.info("品牌商id:{}, 品牌商名称:{},没有到账单日", brandId, brandOwner.getCompanySimpleName());
            return;
        }
        //查询所有完成的主订单（已完成，没有售后，成交时间在周期内）
        List<OrderDto> orderDtos = selectFinishedMainOrderListInBill(brandId, billPeriod);
        if(!orderDtos.isEmpty()){
            Map<Long, List<OrderDto>> designerIdMap = orderDtos.stream().collect(Collectors.groupingBy(OrderDto::getDesignerId));
            for(Map.Entry<Long, List<OrderDto>> entry : designerIdMap.entrySet()){
                BillDesigner billDesigner = handleDesignerBill(entry.getValue(), billPeriod);
                //处理子账单
                generateDesignerBillSub(entry.getValue(), billDesigner);
            }
        }
    }

    @Transactional
    protected void generateDesignerBillSub(List<OrderDto> orderDtos, BillDesigner billDesigner){
        //按照主体进行分组
        Map<Long, List<OrderDto>> companyIdMap = orderDtos.stream().collect(Collectors.groupingBy(OrderDto::getCompanyInfoId));
        //按照公司主体构建子账单
        for(Map.Entry<Long, List<OrderDto>> entry : companyIdMap.entrySet()){
            List<OrderDto> orderDtoList = entry.getValue();
            BillDesignerSub billDesignerSub = buildDesignerBillSub(billDesigner, orderDtoList);
            orderDtoList.forEach(x->{
                x.setBillStart(billDesigner.getBillPeriodStart());
                x.setBillEnd(billDesigner.getBillPeriodEnd());
                x.setSubBillId(billDesignerSub.getSubBillId());
                x.setCompanyInfoId(billDesignerSub.getCompanyInfoId());
                x.setBillId(billDesigner.getBillId());
                x.setOrderId(x.getId());
            });
            try {
                List<Long> existOrderIds = billOrderMapper.existOrderIds(orderDtoList.stream().map(OrderDto::getOrderId).collect(Collectors.toList()));
                if(existOrderIds != null && !existOrderIds.isEmpty()){
                    log.error("生成子帐单,部分订单已经结算，请检查:{}", JSONObject.toJSONString(existOrderIds));
                    //去掉已经存在的数据
                    orderDtoList = orderDtoList.stream().filter(orderDto -> !existOrderIds.contains(orderDto.getOrderId())).collect(Collectors.toList());
                }
                if(!orderDtoList.isEmpty()) {
                    billOrderMapper.batchInsertBillOrder(orderDtoList);
                }
            }catch (Exception e){
                log.error("批量插入订单数据失败:{}, e", e.getMessage(), e);
            }
        }
    }

    /**
     * 构建子账单
     */
    @Transactional
    public BillDesignerSub buildDesignerBillSub(BillDesigner billDesigner, List<OrderDto> orderDtoList){
        OrderDto orderDto = orderDtoList.get(0);
        BillDesignerSub billDesignerSub = new BillDesignerSub();
        billDesignerSub.setBillingTime(billDesigner.getBillingTime());
        billDesignerSub.setBillPeriodStart(billDesigner.getBillPeriodStart());
        billDesignerSub.setBillPeriodEnd(billDesigner.getBillPeriodEnd());
        billDesignerSub.setBillId(billDesigner.getBillId());
        billDesignerSub.setIsLock(0);
        billDesignerSub.setStylistId(billDesigner.getStylistId());
        billDesignerSub.setStylistName(billDesigner.getStylistName());
        billDesignerSub.setManagerUserId(billDesigner.getManagerUserId());
        billDesignerSub.setBrandOwnerId(billDesigner.getBrandOwnerId());
        billDesignerSub.setSettlementStatus(0);
        billDesignerSub.setCompanyInfoId(orderDto.getCompanyInfoId());
        billDesignerSub.setCommissionBack(new BigDecimal(0));
        billDesignerSub.setStatus(0);
        billDesignerSub.setCreateBy("sys");
        billDesignerSub.setFranchiseId(orderDto.getFranchiseId());
        billDesignerSub.setNoInvoice(new BigDecimal("0"));
        billDesignerSub.setOrderCount(orderDtoList.size());
        BigDecimal totalCommission = orderDtoList.stream()
                .map(BillOrderDto::getMoney
                ).reduce(BigDecimal.ZERO, BigDecimal::add);
        billDesignerSub.setTotalCommission(totalCommission);
        billDesignerSub.setFineAmount(new BigDecimal(0));
        billDesignerMapper.insertBillDesignerSub(billDesignerSub);
        return billDesignerSub;
    }

    /**
     * 处理设计师账期
     */
    @Transactional
    protected BillDesigner handleDesignerBill(List<OrderDto> orderDtos, BillPeriod billPeriod){
        OrderDto orderDto = orderDtos.get(0);
        BillDesigner billDesigner = new BillDesigner();
        billDesigner.setBillPeriodStart(billPeriod.getLastBillStart());
        billDesigner.setBillPeriodEnd(billPeriod.getLastBillEnd());
        billDesigner.setBillingTime(DateUtils.getLongDateStr());
        billDesigner.setCreateBy("sys");
        billDesigner.setStylistId(orderDto.getDesignerId());
        billDesigner.setCreateTime(DateUtils.getLongDateStr());
        billDesigner.setNoInvoice(new BigDecimal("0"));
        billDesigner.setFineAmount(new BigDecimal("0"));
        billDesigner.setCommissionBack(new BigDecimal("0"));
        billDesigner.setOrderCount(orderDtos.size());
        billDesigner.setIsLock(0);
        billDesigner.setStatus(0);
        XgwcDesignerDto xgwcDesignerDto = xgwcDesignerMapper.getDesignerManagementUserId(orderDto.getDesignerId());
        if(xgwcDesignerDto != null) {
            billDesigner.setManagerUserId(xgwcDesignerDto.getManagementUserId());
        }
        BigDecimal totalCommission = orderDtos.stream()
                .map(BillOrderDto::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        billDesigner.setTotalCommission(totalCommission);
        billDesigner.setStylistName(orderDto.getDesignerName());
        billDesigner.setSettlementStatus(0);
        billDesigner.setBrandOwnerId(orderDto.getBrandId());
        billDesignerMapper.insertBillDesigner(billDesigner);
        return billDesigner;

    }

    /**
     * 获取在账期内完成的订单
     */
    @Transactional
    protected List<OrderDto> selectFinishedMainOrderListInBill(Long brandId, BillPeriod billPeriod){
        OrderVo orderVo = new OrderVo();
        orderVo.setBrandId(brandId);
        orderVo.setDealTimeStart(billPeriod.getLastBillStart());
        orderVo.setDealTimeEnd(billPeriod.getLastBillEnd());
        List<OrderDto> orderDtos = xgwcOrderMapper.selectFinishedMainOrderListInBill(orderVo);
        if(orderDtos != null && !orderDtos.isEmpty()){
            log.info("品牌商{},账期:{},有:{}条主账单符合条件", brandId, billPeriod.getLastBillStart()+ "-" +billPeriod.getLastBillEnd(), orderDtos.size());
            int limit = 100;
            if(orderDtos.size() > limit){
                List<List<OrderDto>> lists = ListUtils.partition(orderDtos, limit);
                for(List<OrderDto> list : lists){
                    List<OrderDto> subList = getSubOrderList(list);
                    if(!subList.isEmpty()){
                        orderDtos.addAll(getSubOrderList(subList));
                    }
                }
            }else{
                orderDtos.addAll(getSubOrderList(orderDtos));
            }
        }else{
            log.info("品牌商{},账期:{},没有主账单符合条件", brandId, billPeriod.getLastBillStart()+ "-" +billPeriod.getLastBillEnd());
            return new ArrayList<>();
        }
        log.info("品牌商{},账期:{},一共处理订单数:{}", brandId, billPeriod.getLastBillStart()+ "-" +billPeriod.getLastBillEnd(), orderDtos.size());
        return orderDtos;
    }

    /**
     * 获取子账单
     * @return 账单列表
     */
    @Transactional
    protected List<OrderDto> getSubOrderList(List<OrderDto> list){
        List<Long> orderIds = list.stream().map(OrderDto::getId).collect(Collectors.toList());
        List<OrderDto> orderDtos = xgwcOrderMapper.selectSubOrderList(orderIds);
        List<OrderDto> result = new ArrayList<>();
        if(orderDtos != null && !orderDtos.isEmpty()){
            Map<Long, List<OrderDto>> pidMap = orderDtos.stream().collect(Collectors.groupingBy(OrderDto::getPid));
            for(Map.Entry<Long, List<OrderDto>> entry : pidMap.entrySet()){
                List<OrderDto> filterList = entry.getValue().stream().filter(x->(x.getArchiveType() == null || x.getArchiveType() != 5)).toList();
                if(!filterList.isEmpty()){
                    log.error("子账单处于未交稿状态，该子账单:{}, 对应主账单以及下面的子账单均不能结算:{}", entry.getKey(), JSONObject.toJSONString(filterList));
                }else{
                    //子账单加进来
                    result.addAll(entry.getValue());
                }
            }

        }
        return result;
    }

    /**
     * 是否生成新的账单
     */
    @Transactional
    protected BillPeriod isProductNewBill(Long brandId) {
        //第一步判断品牌商设计师是否到了账单日
        String lastBillEnd = "";
        BillPeriod billPeriod = xgwcPaymentDaysMapper.selectBillPeriodByBrandId(brandId, PaymentTypeEnums.DESIGNER.getType());
        if(billPeriod != null){
            lastBillEnd = billPeriod.getLastBillEnd();
        }
        XgwcPaymentDays xgwcPaymentDays = xgwcPaymentDaysMapper.selectByBrandId(brandId, PaymentTypeEnums.DESIGNER.getType());
        BillPeriod newBillPeriod = new BillPeriod();
        if(xgwcPaymentDays != null){
            Integer cycleType = xgwcPaymentDays.getCycleType();
            LocalDate date = LocalDate.now();
            if(cycleType == 1){
                newBillPeriod = generateWeeklyPeriod(date, lastBillEnd);
            }else if(cycleType == 2){
                newBillPeriod = generateBiweeklyPeriod(date, lastBillEnd);
            }else if(cycleType == 3){
                newBillPeriod = generateMonthlyPeriod(date, lastBillEnd);
            }else if(cycleType == 4){
                newBillPeriod = generateQuarterlyPeriod(date, lastBillEnd);
            }
        }
        //存在没有设置账期的情况
        if(newBillPeriod != null && newBillPeriod.getLastBillStart() != null){
            //生成新的订单周期
            newBillPeriod.setBrandId(brandId);
            newBillPeriod.setPaymentType(1);
            xgwcPaymentDaysMapper.insertBillPeriod(newBillPeriod);
            return newBillPeriod;
        }
        return null;
    }

    public static BillPeriod generateWeeklyPeriod(LocalDate date, String lastBillEnd) {
       /* if (date.getDayOfWeek() != DayOfWeek.MONDAY) {
            log.info("一周账期: 当前不是周一，不生成上周账期,当前时间:{}", date.getDayOfWeek());
            return null;
        }*/
        LocalDate end = date.minusDays(1); // 上周日
        LocalDate start = end.minusDays(6); // 上周一
        return buildBillPeriod(date, start, end, lastBillEnd);
    }

    // 生成半月账期，仅在当前是1号或16号时生成
    public static BillPeriod generateBiweeklyPeriod(LocalDate date, String lastBillEnd) {
        int dayOfMonth = date.getDayOfMonth();
        /*if (dayOfMonth != 1 && dayOfMonth != 16) {
            log.info("半月账期: 当前不是1号或16号，不生成上期账期,当前日期:{}", date);
            return null;
        }*/

        LocalDate start, end;
        if (dayOfMonth == 1) {
            // 生成上月下半月账期(16号-月底)
            LocalDate lastMonth = date.minusMonths(1);
            start = lastMonth.withDayOfMonth(16);
            end = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
        } else { // dayOfMonth == 16
            // 生成本月上半月账期(1号-15号)
            start = date.withDayOfMonth(1);
            end = date.withDayOfMonth(15);
        }
        return buildBillPeriod(date, start, end, lastBillEnd);
    }

    //生成月度账单
    public static BillPeriod generateMonthlyPeriod(LocalDate date, String lastBillEnd) {
        /*if (date.getDayOfMonth() != 1) {
            log.warn("一月账期: 当前不是1号，不生成上月账期,当前日期:{}", date);
            return null;
        }*/
        LocalDate lastMonth = date.minusMonths(1);
        LocalDate start = lastMonth.withDayOfMonth(1);
        LocalDate end = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
        return buildBillPeriod(date, start, end, lastBillEnd);
    }

    //一个季度
    public static BillPeriod generateQuarterlyPeriod(LocalDate date, String lastBillEnd) {
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();
        // 检查是否是季度第一个月的1号 (1月1日, 4月1日, 7月1日, 10月1日)
        if (day != 1 || (month % 3) != 1) {
            log.warn("季度账期: 当前不是季度第一个月的1号，不生成上季度账期, 当前时间:{}", date);
            return null;
        }
        LocalDate start = date.minusMonths(3).withDayOfMonth(1);
        LocalDate end = date.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        return buildBillPeriod(date, start, end, lastBillEnd);
    }

    /**
     * 构建账单日期
     */
    private static BillPeriod buildBillPeriod(LocalDate date, LocalDate start, LocalDate end,String lastBillEnd){
        BillPeriod billPeriod = new BillPeriod();
        if(StringUtils.isEmpty(lastBillEnd)) {
            billPeriod.setLastBillStart(DateUtils.convertToDateStr(start));
            billPeriod.setLastBillEnd(DateUtils.convertToDateStr(end));
        }else{
            LocalDate lastBillLocalDate = LocalDate.parse(lastBillEnd);
            long diffDay = ChronoUnit.DAYS.between(date, lastBillLocalDate);
            if(diffDay >= -1){
                log.error("距离上次账期相差小于1天，上次时间:{}", lastBillEnd);
                return null;
            }
            billPeriod.setLastBillEnd(DateUtils.convertToDateStr(end));
            //上一次账单最后时间到当前一天所有时间
            billPeriod.setLastBillStart(DateUtils.convertToDateStr(lastBillLocalDate.plusDays(1L)));
            if(DateUtils.isBefore(billPeriod.getLastBillStart(), billPeriod.getLastBillEnd()) < 0){
                log.info("账期开始时间:{}, 不能大于结束时间:{}", billPeriod.getLastBillStart(), billPeriod.getLastBillEnd());
                return null;
            }
        }
        return billPeriod;
    }
}
