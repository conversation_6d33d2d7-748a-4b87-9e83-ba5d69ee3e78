package com.xgwc.settlement.entity;

import lombok.Data;

/**
 * 数据库相关信息
 * <AUTHOR>
 * @date 2023-08-23
 */
@Data
public class DataBase {

    /**
     * 数据库host
     */
    private String host;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 表
     */
    private String tables;

    /**
     * 数据库名
     */
    private String databaseName;

    /**
     * kafka 地址
     */
    private String kafkaUrl;

    /**
     * kafka topicId
     */
    private String kafkaTopic;

    /**
     * SASL认证配置
     */
    private String jaasConfig;
}
