package com.xgwc.settlement.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillOrderDto {

    /**
     * 账单开始
     */
    private String billStart;

    /**
     * 账单结束
     */
    private String billEnd;

    /**
     * 主键
     */
    private Long id;

    /**
     * 主账单ID
     */
    private Long billId;

    /**
     * 账单id
     */
    private Long subBillId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 谈单人员
     */
    @JSONField(name = "sale_man_id")
    private Long saleManId;

    /**
     * 谈单人员名称
     */
    @JSONField(name = "sale_man_name")
    private String saleManName;

    /**
     * 录入部门编码
     */
    @JSONField(name = "dept_id")
    private Long deptId;

    /**
     * 录入部门名称
     */
    @JSONField(name = "dept_name")
    private String deptName;

    /**
     * 订单类型：0正常单，1转化单,2协作单，3拍错单
     */
    @JSONField(name = "transfer_state")
    private Integer transferState;

    /**
     * 下单日期
     */
    @JSONField(name = "order_date")
    private String orderDate;

    /**
     * 订单来源ID（店铺id）
     */
    @JSONField(name = "store_id")
    private Long storeId;

    /**
     * 订单来源名称（店铺名称）
     */
    @JSONField(name = "store_name")
    private String storeName;

    /**
     * 所属业务编号-字典
     */
    @JSONField(name = "state_dic_code")
    private String stateDicCode;

    /**
     * 所属业务名称-字典
     */
    @JSONField(name = "state_dic_name")
    private String stateDicName;

    /**
     * 品牌商id
     */
    @JSONField(name = "brand_id")
    private Long brandId;

    /**
     * 支付方式  1:淘宝  2：微信  3：支付宝
     */
    @JSONField(name = "pay_channel")
    private Integer payChannel;

    /**
     * 订单状态（0：未发货 1：完成  2：退款 3：部分退款）
     */
    @JSONField(name = "sh_type")
    private Integer shType;

    /**
     * 订单编号
     */
    @JSONField(name = "order_no")
    private String orderNo;

    /**
     * 客户ID
     */
    @JSONField(name = "taobao_id")
    private String taobaoId;

    /**
     * 订单金额
     */
    @JSONField(name = "order_amount")
    private BigDecimal orderAmount;

    /**
     * 实收金额
     */
    @JSONField(name = "amount")
    private BigDecimal amount;

    /**
     * 当前金额：当有退款发生时有值
     */
    @JSONField(name = "now_amount")
    private BigDecimal nowAmount;

    /**
     * 付款方式:1全款/2阶段付
     */
    @JSONField(name = "pay_type")
    private Long payType;

    /**
     * 派单需求
     */
    @JSONField(name = "allot_remark")
    private String allotRemark;

    /**
     * 派单设计师数量
     */
    @JSONField(name = "allot_num")
    private Long allotNum;

    /**
     * 派单人
     */
    @JSONField(name = "allot_user_id")
    private Long allotUserId;

    /**
     * 派单人
     */
    @JSONField(name = "allot_user_name")
    private String allotUserName;

    /**
     * 母订单id
     */
    private Long pid;

    /**
     * 设计师ID
     */
    @JSONField(name = "designer_id")
    private Long designerId;

    /**
     * 设计师名称
     */
    @JSONField(name = "designer_name")
    private String designerName;

    /**
     * 设计师业务类型
     */
    @JSONField(name = "designer_business")
    private String designerBusiness;

    /**
     * 佣金金额
     */
    private BigDecimal money;

    /**
     * 创建时间
     */
    @JSONField(name = "create_time")
    private String createTime;

    /**
     * 加盟商id
     */
    @JSONField(name = "franchise_id")
    private Long franchiseId;

    /**
     * 行更新时间
     */
    @JSONField(name = "modify_time")
    private String modifyTime;

    /**
     * 公司主体ID
     */
    @JSONField(name = "company_info_id")
    private Long companyInfoId;

    /**
     * 成交时间
     */
    @JSONField(name = "deal_time")
    private String dealTime;

    /**
     * 提交定稿时间
     */
    @JSONField(name = "archive_time")
    private String archiveTime;

    /**
     * 锁定：0未锁定，1锁定
     */
    @JSONField(name = "is_lock")
    private Integer isLock;

    /**
     * 锁定时间
     */
    @JSONField(name = "lock_time")
    private String lockTime;

    /**
     * 剩余佣金追回
     */
    @JSONField(name = "commission_back_remaining")
    private BigDecimal commissionBackRemaining;

}
