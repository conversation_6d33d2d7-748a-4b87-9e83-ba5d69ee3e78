package com.xgwc.settlement.dao;

import com.xgwc.settlement.entity.BillPeriod;
import com.xgwc.settlement.entity.XgwcPaymentDays;
import org.apache.ibatis.annotations.Param;

public interface XgwcPaymentDaysMapper {

    /**
     * 根据品牌商ID获取账期记录
     * @param brandId 品牌商ID
     * @return 账期设置
     */
    XgwcPaymentDays selectByBrandId(@Param(value = "brandId") Long brandId, @Param(value = "paymentType") Integer paymentType);

    /**
     * 查看账期
     * @param brandId 品牌ID
     * @return 账期
     */
    BillPeriod selectBillPeriodByBrandId(@Param(value = "brandId") Long brandId, @Param(value = "paymentType") Integer paymentType);

    /**
     * 查看账期
     * @param periodId 账期id
     * @return 账期
     */
    BillPeriod selectBillPeriodById(@Param(value = "periodId") Long periodId);

    /**
     * 插入最新的账期
     * @param billPeriod 账期时间
     * @return 是否成功
     */
    int insertBillPeriod(BillPeriod billPeriod);

    /**
     * 获取上一次锁单的日期
     * @param brandId 品牌ID
     * @return 周期
     */
    BillPeriod selectByLastesLockPeriod(@Param(value = "brandId") Long brandId, @Param(value = "paymentType") Integer paymentType);
}
