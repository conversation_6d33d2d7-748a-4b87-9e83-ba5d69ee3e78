package com.xgwc.settlement.util;

import com.github.shyiko.mysql.binlog.event.EventType;

/**
 * <AUTHOR>
 */
public class BinlogUtils {

    /**
     * 是否是修改语句
     */
    public static boolean isUpdate(EventType eventType) {
        return eventType == EventType.PRE_GA_UPDATE_ROWS || eventType == EventType.UPDATE_ROWS || eventType == EventType.EXT_UPDATE_ROWS;
    }

    /**
     * 是否是插入语句
     */
    public static boolean isInsert(EventType eventType) {
        return eventType == EventType.PRE_GA_WRITE_ROWS || eventType == EventType.WRITE_ROWS || eventType == EventType.EXT_WRITE_ROWS;
    }

    /**
     * 是否是删除语句
     */
    public static boolean isDelete(EventType eventType) {
        return eventType == EventType.PRE_GA_DELETE_ROWS || eventType == EventType.DELETE_ROWS || eventType == EventType.EXT_DELETE_ROWS;
    }

    /**
     * 是否是旋转语句
     * 当 MySQL 在创建新的 binlog 文件时，会生成一个旋转事件，标识当前 binlog 的切换
     */
    public static boolean isRotate(EventType eventType) {
        return eventType == EventType.ROTATE;
    }
}
