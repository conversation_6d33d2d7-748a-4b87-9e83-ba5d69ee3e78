package com.xgwc.settlement.dao;

import com.xgwc.settlement.entity.dto.OrderDto;
import com.xgwc.settlement.entity.vo.OrderVo;

import java.util.List;

public interface XgwcOrderMapper {

        /**
         * 根据账单时间周期获取该账单内所有账单涉及的设计师id
         * 过滤条件：1.成交时间在账单时间范围内，2.未结算的订单,3.满足（已交稿，已归档，未售后）
         * @param orderVo 参数
         * @return 设计师id
         */
        List<Long> selectDesignerList(OrderVo orderVo);


        /**
         * 非账期内的订单，产生售后
         */
        List<Long> selectDesignerListNotInBill(OrderVo orderVo);

        /**
         * 根据设计师id获取符合条件的订单
         * 过滤条件：1.成交时间在账单时间范围内，2.未结算的订单,3.满足（已交稿，已归档，未售后）
         * @param orderVo 参数
         * @return 订单信息
         */
        List<OrderDto> selectOrderListByCondition(OrderVo orderVo);


        /**
         * 非账期内的订单，产生售后的订单 成交在账期之前，但是修改时间是在账期之后的单子
         */
        List<OrderDto> selectOrderListNotInBillByCondition(OrderVo orderVo);


        /**
         * 获取所有已成交且修改时间范围内的订单数据
         * @param orderVo 参数
         * @return 订单数据
         */
        List<OrderDto> selectModifyOrderListByModifyTime(OrderVo orderVo);


        /**
         * 获取账期内已经完成的主账单
         * @param orderVo 参数
         * @return 账单id
         */
        List<OrderDto> selectFinishedMainOrderListInBill(OrderVo orderVo);

        /**
         * 根据主账单获取所有子账单
         * @param orderPidList 账单列表
         * @return 账单列表
         */
        List<OrderDto> selectSubOrderList(List<Long> orderPidList);

        /**
         * 根据主账单获取所有子账单
         * @param orderIdList 账单列表
         * @return 账单列表
         */
        List<OrderDto> selectOrderListByOrderIds(List<Long> orderIdList);

        /**
         * 根据id获取订单信息
         * @param orderId 账单列表
         * @return 账单列表
         */
        OrderDto selectOrderByOrderId(Long orderId);

}
