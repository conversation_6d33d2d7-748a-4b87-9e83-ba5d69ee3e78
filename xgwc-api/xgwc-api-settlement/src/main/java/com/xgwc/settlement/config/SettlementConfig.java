package com.xgwc.settlement.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "settlementconfig")
@RefreshScope
public class SettlementConfig {

    /**
     * 忽略当前日期
     */
    private boolean ignoreDate;
}
