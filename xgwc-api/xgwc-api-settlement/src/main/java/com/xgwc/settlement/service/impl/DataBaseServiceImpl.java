package com.xgwc.settlement.service.impl;

import com.xgwc.settlement.config.DataBaseConfig;
import com.xgwc.settlement.entity.DataColnum;
import com.xgwc.settlement.service.DataBaseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataBaseServiceImpl implements DataBaseService {

    @Resource
    private DataBaseConfig dataBaseConfig;

    @Override
    public Map<String, List<String>> getTableColumList() {
        Map<String, List<DataColnum>> tableMap = getDataColnumList();
        Map<String, List<String>> resultTableMap = new HashMap<>();
        if(tableMap.size() > 0){
            for(Map.Entry<String, List<DataColnum>> map : tableMap.entrySet()){
                String tableName = map.getKey();
                List<String> colNameList = map.getValue().stream().map(DataColnum::getColName).collect(Collectors.toList());
                resultTableMap.put(tableName, colNameList);
            }
        }
        return resultTableMap;
    }

    /**
     * 获取数据库表字段列表
     */
    private Map<String, List<DataColnum>> getDataColnumList(){
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection connection = null;
        try {
            String[] tableArray = dataBaseConfig.getTables().split(",");
            String placeholderStr = null;
            StringBuilder placeholders = new StringBuilder();
            for(String table : tableArray){
                placeholders.append("'").append(table).append("',");
            }
            if(placeholders.length() > 0){
                placeholderStr = placeholders.substring(0, placeholders.length() - 1);
            }
            Class.forName("com.mysql.jdbc.Driver");
            // 保存当前注册的表的column信息
            connection = DriverManager.getConnection("jdbc:mysql://" + dataBaseConfig.getHost() + ":"
                    + dataBaseConfig.getPort(), dataBaseConfig.getUserName(), dataBaseConfig.getPasswd());
            // 执行sql
            String preSql = "SELECT TABLE_NAME tableName,COLUMN_NAME colName " +
                    " FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '" + dataBaseConfig.getDatabaseName()  + "' and TABLE_NAME in (" + placeholderStr + ");";
            ps = connection.prepareStatement(preSql);
            rs = ps.executeQuery();
            List<DataColnum> dataColnums = new ArrayList<>();
            while (rs.next()) {
                String tableName = rs.getString("tableName");
                String colName = rs.getString("colName");
                DataColnum dataColnum = new DataColnum(tableName, colName);
                dataColnums.add(dataColnum);
            }
            ps.close();
            rs.close();
            return dataColnums.stream().collect(Collectors.groupingBy(DataColnum::getTableName));
        } catch (Exception e) {
            log.error("读取表字段信息失败, db:{}, tables:{}", dataBaseConfig.getDatabaseName(), dataBaseConfig.getTables(), e);
        }finally {
            if(ps != null){
                try {
                    ps.close();
                }catch (Exception e){
                    log.error("ps close error", e);
                }
            }
            if(rs != null){
                try {
                    rs.close();
                }catch (Exception e){
                    log.error("rs close error", e);
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    log.error("connection close error", e);
                }
            }
        }
        return new HashMap<>(1);
    }
}
