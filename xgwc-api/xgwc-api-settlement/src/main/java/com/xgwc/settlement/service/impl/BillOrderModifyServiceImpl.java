package com.xgwc.settlement.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.settlement.dao.*;
import com.xgwc.settlement.entity.*;
import com.xgwc.settlement.entity.dto.BillComfirmDto;
import com.xgwc.settlement.service.BillOrderModifyService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillOrderModifyServiceImpl implements BillOrderModifyService {

    @Resource
    private BillOrderModifyMapper billOrderModifyMapper;

    @Resource
    private BillDesignerMapper billDesignerMapper;

    @Resource
    private XgwcPaymentDaysMapper xgwcPaymentDaysMapper;

    @Resource
    private BillComfirmMapper billComfirmMapper;

    @Resource
    private BillOrderModifyRecordMapper billOrderModifyRecordMapper;


    //重新统计佣金
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void preStaticsCommission(BillDesigner billDesigner){
        List<BillDesignerSub> billDesignerSubs = billDesignerMapper.sumCommissionBack(billDesigner.getBillId());
        if(billDesignerSubs != null && !billDesignerSubs.isEmpty()) {
            BigDecimal sumCommission = BigDecimal.ZERO;
            for(BillDesignerSub billDesignerSub : billDesignerSubs){
                sumCommission = sumCommission.add(billDesignerSub.getTotalCommission());
                billDesignerMapper.updateBillDesignerSub(billDesignerSub);
            }
            billDesigner.setTotalCommission(sumCommission);
        }
        billDesignerMapper.updateBillDesigner(billDesigner);


    }

    @Async("asynExecutor")
    //统计佣金追回
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void preStaticsCommissionBack(Long billId){
        if(billId != null){
            BillDesigner billDesigner = billDesignerMapper.selectBillDesignerById(billId);
            preStaticsCommissionBack(billDesigner);
        }
    }

    //重新统计佣金追回
    @Transactional(rollbackFor = Exception.class)
    void preStaticsCommissionBack(BillDesigner billDesigner){
        if(billDesigner != null && (billDesigner.getIsLock() != null && billDesigner.getIsLock() != 1)) {
            //第二步获取子订单
            List<BillDesignerSub> billDesignerSubs = billDesignerMapper.selectDesignerBillSubListByBillId(billDesigner.getBillId());
            //处理各类金额
            handleAllTypeAmount(billDesigner, billDesignerSubs);
            //根据设计师获取所有未结算的订单
            List<BillOrderModifyRecord> recordList = new ArrayList<>();
            for(BillDesignerSub designerBillSubDto : billDesignerSubs) {
                log.info("开始处理子订单:{}", JSON.toJSONString(designerBillSubDto));
                recordList.addAll(comfirmBillDesignerSub(designerBillSubDto));
            }
            //批量插入扣除记录
            if(!recordList.isEmpty()) {
                billOrderModifyRecordMapper.batchInsertBillOrderModifyRecordList(recordList);
            }
            preStaticsBillDesigner(billDesignerSubs, billDesigner);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    protected void preStaticsBillDesigner(List<BillDesignerSub> billDesignerSubs, BillDesigner billDesigner) {
        BigDecimal commissionBack = BigDecimal.ZERO;
        BigDecimal payableAmount = BigDecimal.ZERO;
        for(BillDesignerSub designerBillSubDto : billDesignerSubs) {
            BigDecimal commissionBackSub = designerBillSubDto.getCommissionBack() == null ? BigDecimal.ZERO : designerBillSubDto.getCommissionBack();
            BigDecimal payableAmountSub = designerBillSubDto.getPayableAmount() == null ? BigDecimal.ZERO : designerBillSubDto.getPayableAmount();
            commissionBack = commissionBack.add(commissionBackSub);
            payableAmount = payableAmount.add(payableAmountSub);
        }
        billDesigner.setCommissionBack(commissionBack);
        billDesigner.setPayableAmount(payableAmount);
        billDesignerMapper.updateBillDesigner(billDesigner);
    }

    @Transactional(rollbackFor = Exception.class)
    protected List<BillOrderModifyRecord> comfirmBillDesignerSub(BillDesignerSub billDesignerSub){
        List<BillOrderModifyRecord> recordList = new ArrayList<>();
        List<BillOrderModify> billOrderModifyList = billOrderModifyMapper.getUnsettledBillOrderModifyList(billDesignerSub.getStylistId(), billDesignerSub.getBrandId());
        if(billOrderModifyList != null) {
            //可用金额 = 应发金额(如果为空，则为总金额-无票金额)
            BigDecimal availableAmount = billDesignerSub.getPayableAmount() == null ? billDesignerSub.getTotalCommission().subtract(billDesignerSub.getNoInvoice() == null ? BigDecimal.ZERO : billDesignerSub.getNoInvoice()) : billDesignerSub.getPayableAmount();
            //可用金额为0，则不再扣除
            if(availableAmount.compareTo(BigDecimal.ZERO) <= 0){
                log.info("账单:{},可用金额为0,不再扣除:{}", availableAmount, JSONObject.toJSONString(billDesignerSub));
                return recordList;
            }
            BigDecimal commissionBack = BigDecimal.ZERO;
            //售后订单中的佣金退回需要扣除佣金
            for (BillOrderModify billOrderModify : billOrderModifyList) {
                //首先绑定账期
                BigDecimal money = billOrderModify.getMoney() == null ? BigDecimal.ZERO : billOrderModify.getMoney();
                //需追回的佣金,如果待追回金额已经算出来，则用待追回，如果没有则重新开始计算
                BigDecimal needBack = billOrderModify.getCommissionBackRemaining() == null ? money : billOrderModify.getCommissionBackRemaining();
                //如果没有产生佣金变化
                if (needBack.equals(BigDecimal.ZERO)) {
                    //设置无需追回
                    billOrderModify.setBackStatus(3);
                } else {
                    //本次退回
                    BigDecimal thisTimeBack;
                    if (availableAmount.compareTo(needBack) >= 0) {
                        //如果总佣金大于需要追回的
                        billOrderModify.setCommissionBacked(needBack);
                        billOrderModify.setCommissionBackRemaining(BigDecimal.ZERO);
                        billOrderModify.setBackStatus(2);
                        thisTimeBack = needBack;
                        //扣减总佣金
                        availableAmount = availableAmount.subtract(needBack);
                        commissionBack = commissionBack.add(needBack);
                    } else {
                        //本次 = 需要的金额-总金额
                        thisTimeBack = availableAmount;
                        //如果总佣金小于需要追回的
                        billOrderModify.setCommissionBacked(availableAmount);
                        //剩余金额 = 需追回-总佣金
                        billOrderModify.setCommissionBackRemaining(needBack.subtract(availableAmount));
                        billOrderModify.setBackStatus(1);
                        commissionBack = commissionBack.add(availableAmount);
                        //全部扣完
                        availableAmount = BigDecimal.ZERO;

                    }
                    BillOrderModifyRecord billOrderModifyRecord = buildBillOrderModifyRecord(billDesignerSub, billOrderModify, thisTimeBack);
                    recordList.add(billOrderModifyRecord);
                    log.info("更新扣除信息:{}", JSONObject.toJSONString(billOrderModifyRecord));
                    //更新扣除信息
                    billOrderModifyMapper.updateBillOrderModify(billOrderModify);
                    //可用金额小于等于0就退出循环
                    if(availableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                }
            }
            //佣金扣除 =
            billDesignerSub.setCommissionBack(commissionBack);
            billDesignerSub.setPayableAmount(availableAmount);
            billDesignerMapper.updateBillDesignerSub(billDesignerSub);
        }
        return recordList;
    }

    //构建参数
    private BillOrderModifyRecord buildBillOrderModifyRecord(BillDesignerSub billDesignerSub, BillOrderModify billOrderModify, BigDecimal thisTimeBack) {
        BillOrderModifyRecord billOrderModifyRecord = new BillOrderModifyRecord();
        billOrderModifyRecord.setModifyId(billOrderModify.getId());
        billOrderModifyRecord.setBillStart(billDesignerSub.getBillPeriodStart());
        billOrderModifyRecord.setBillEnd(billDesignerSub.getBillPeriodEnd());
        billOrderModifyRecord.setCommissionBack(thisTimeBack);
        billOrderModifyRecord.setOrderId(billOrderModify.getOrderId());
        billOrderModifyRecord.setSubBillId(billDesignerSub.getSubBillId());
        billOrderModifyRecord.setBillId(billDesignerSub.getBillId());
        return billOrderModifyRecord;
    }

    /**
     * 处理各类金额
     */
    @Transactional(rollbackFor = Exception.class)
    protected void handleAllTypeAmount(BillDesigner billDesigner, List<BillDesignerSub> billDesignerSubs){
        log.info("开始处理账单金额,账单id:{}", billDesigner.getBillId());
        BillComfirmDto billComfirmDto = billComfirmMapper.selectBillComfirmByBillId(billDesigner.getBillId());
        if(billComfirmDto != null) {
            if(billComfirmDto.getProvideInvoice() == 1) {
                log.info("账单:{},提供发票不需要进行无票扣款", billDesigner.getBillId());
            }else{
                log.info("账单:{},没有提供发票需要进行无票扣款", billDesigner.getBillId());
                handleNoInvoice(billDesigner, billDesignerSubs);
            }
        }
    }

    /**
     * 处理无票扣款
     */
    @Transactional(rollbackFor = Exception.class)
    protected void handleNoInvoice(BillDesigner billDesigner, List<BillDesignerSub> billDesignerSubs){
        //查看扣款费率
        XgwcPaymentDays xgwcPaymentDays = xgwcPaymentDaysMapper.selectByBrandId(billDesigner.getBrandOwnerId(), 1);
        if(xgwcPaymentDays != null) {
            BigDecimal noInvoiceRate = xgwcPaymentDays.getTaxMoney() == null ? BigDecimal.ZERO : xgwcPaymentDays.getTaxMoney();
            log.info("品牌商:{},扣款费率为:{}", billDesigner.getBrandOwnerId(), noInvoiceRate);
            billDesigner.setNoInvoice(billDesigner.getTotalCommission().multiply(noInvoiceRate).multiply(new BigDecimal("0.01")));
            billDesignerSubs.forEach(x-> {
                x.setNoInvoice(x.getTotalCommission().multiply(noInvoiceRate).multiply(new BigDecimal("0.01")));
            });
        }else{
            log.info("品牌商:{},未设置无票扣款", billDesigner.getBrandOwnerId());
        }
    }
}
