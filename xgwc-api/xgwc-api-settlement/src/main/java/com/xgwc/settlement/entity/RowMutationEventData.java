package com.xgwc.settlement.entity;

import com.github.shyiko.mysql.binlog.event.DeleteRowsEventData;
import com.github.shyiko.mysql.binlog.event.EventData;
import com.github.shyiko.mysql.binlog.event.UpdateRowsEventData;
import com.github.shyiko.mysql.binlog.event.WriteRowsEventData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-09-06
 */
@Data
public class RowMutationEventData {

    /**
     * 表ID
     */
    private long tableId;

    /**
     * 插入的行数据
     */
    private List<Serializable[]> insertRows;

    /**
     * 删除的行数据
     */
    private List<Serializable[]> deleteRows;

    /**
     * 更新的行数据
     */
    private List<Map.Entry<Serializable[], Serializable[]>> updateRows;

    public RowMutationEventData(EventData eventData) {

        if (eventData instanceof UpdateRowsEventData) {
            UpdateRowsEventData updateRowsEventData = (UpdateRowsEventData) eventData;
            this.tableId = updateRowsEventData.getTableId();
            this.updateRows = updateRowsEventData.getRows();
            return;
        }

        if (eventData instanceof WriteRowsEventData) {
            WriteRowsEventData writeRowsEventData = (WriteRowsEventData) eventData;
            this.tableId = writeRowsEventData.getTableId();
            this.insertRows = writeRowsEventData.getRows();
            return;
        }

        if (eventData instanceof DeleteRowsEventData) {
            DeleteRowsEventData deleteRowsEventData = (DeleteRowsEventData) eventData;
            this.tableId = deleteRowsEventData.getTableId();
            this.deleteRows = deleteRowsEventData.getRows();
        }
    }
}
