package com.xgwc.settlement.schedule;

import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 账单定时任务
 */
@Configuration
public class BillDesignerSchedule {

    @Bean
    public JobDetail getBillDesignerJob() {
        return JobBuilder.newJob(BillDesignerJob.class)
                .withIdentity("billDesignerJob", "billDesigner")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger getBillDesignerJobTrigger() {
        //每日凌晨执行任务
        return TriggerBuilder.newTrigger()
                .forJob(getBillDesignerJob())
                .withIdentity("billDesignerTrigger", "billDesignerTriggerGroup")
                .startNow()
                .withSchedule(CronScheduleBuilder.cronSchedule("0 0 0 * * ?"))
                .build();
    }
}
