package com.xgwc.settlement.util;

import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.settlement.entity.BillPeriod;
import com.xgwc.settlement.entity.dto.OrderDto;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

@Slf4j
public class BillUtil {

    /**
     * 首先查看订单是否满足条件
     * 条件：已经交稿，没有售后，已经归档
     */
    public static boolean isMeetTheConditions(OrderDto orderDto) {
        return orderDto.getShType() == 1 && orderDto.getIsAfterSale() == 0;
    }

}
