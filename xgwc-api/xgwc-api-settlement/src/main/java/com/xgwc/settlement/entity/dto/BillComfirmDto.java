package com.xgwc.settlement.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillComfirmDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 账单开始
     */
    private String billStart;

    /**
     * 是否提供发票
     */
    private Integer provideInvoice;

    /**
     * 账单结束
     */
    private String billEnd;

    /**
     * 设计师ID
     */
    private Long designerId;

    /**
     * 设计师名称
     */
    private String designerName;

    /**
     * 佣金合计
     */
    private BigDecimal totalCommission;

    /**
     * 追回佣金
     */
    private BigDecimal backCommission;

    /**
     * 发票json:发票内容，用json存储， amount金额，voiceImg表示发票图片
     */
    private String invoices;

    /**
     * 罚款金额
     */
    private BigDecimal fineAmount;

    /**
     * 无票金额扣款费率
     */
    private BigDecimal noInvoiceRate;

    /**
     * 状态：0正常，1非正常
     */
    private Integer status;

    /**
     * 审核结果：0待审核，1审核成功，2.审核失败
     */
    private Integer checkStatus;

    /**
     * 备注
     */
    private String comment;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private String updateTime;

}
