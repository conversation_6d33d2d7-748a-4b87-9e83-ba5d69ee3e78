package com.xgwc.settlement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-09-06
 */
@RefreshScope
@ConfigurationProperties(prefix = "binlog.database")
@Component
@Data
public class DataBaseConfig {

    /**
     * 数据库host
     */
    private String host;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 表
     */
    private String tables;

    /**
     * 数据库名
     */
    private String databaseName;

}
