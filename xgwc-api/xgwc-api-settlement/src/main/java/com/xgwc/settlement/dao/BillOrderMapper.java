package com.xgwc.settlement.dao;

import com.xgwc.settlement.entity.dto.OrderDto;
import com.xgwc.settlement.entity.vo.OrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BillOrderMapper {

    /**
     * 根据账单日期和订单ID查询账单
     * @param orderId 订单ID
     * @return 订单信息
     */
    OrderDto getOrderDtoByOrderIdAndBillDate(@Param(value = "orderId") Long orderId, @Param(value = "billStart") String billStart, @Param(value = "billEnd") String billEnd);

    /**
     * 删除账单订单记录
     * @param orderId 订单ID
     * @return 是否删除成功
     */
    int deleteBillOrder(@Param(value = "orderId") Long orderId, @Param(value = "billId") Long billId);

    /**
     * 删除账单订单记录
     * @param id 订单ID
     * @return 是否删除成功
     */
    int deleteBillOrderByBillId(@Param(value = "id") Long id);

    /**
     * 获取设计师账期内的所有订单
     * 根据品牌id，设计师id，账期开始，账期结束
     */
    List<OrderDto> getOrderListByBillDate(OrderVo orderVo);

    /**
     * 批量插入账单对应订单
     * @param orderDtos 订单列表
     * @return 是否成功
     */
    int batchInsertBillOrder(List<OrderDto> orderDtos);


    List<OrderDto> exsitOrderDto(@Param(value = "orderId") Long orderId, @Param(value = "billIds") List<Long> billIds);

    /**
     * 修改bill_order, 按照bill_id和order_id
     * @param orderDto 订单
     * @return 是否成功
     */
    int updateBillOrderDto(OrderDto orderDto);

    /**
     * 查询已经存在的订单ID列表（实际不允许重复，订单不允许重复结算）
     * @param orderIds 订单ID列表
     * @return
     */
    List<Long> existOrderIds(@Param(value = "orderIds") List<Long> orderIds);

    /**
     * 查询已经存在的订单ID列表（实际不允许重复，订单不允许重复结算）
     * @param orderId 订单ID
     */
    List<Long> existOrderId(@Param(value = "orderId") Long orderId);

}
