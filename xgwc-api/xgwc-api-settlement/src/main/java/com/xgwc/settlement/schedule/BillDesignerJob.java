package com.xgwc.settlement.schedule;

import com.xgwc.settlement.service.BillService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class BillDesigner<PERSON><PERSON>  extends QuartzJobBean {

    @Resource
    private BillService billService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("账单定时任务开始执行");
        billService.publishBill();
    }
}
