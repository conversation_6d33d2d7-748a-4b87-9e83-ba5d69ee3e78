package com.xgwc.settlement.dao;

import com.xgwc.settlement.entity.BillDesigner;
import com.xgwc.settlement.entity.BillDesignerSub;
import com.xgwc.settlement.entity.dto.OrderDto;
import com.xgwc.settlement.entity.vo.OrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BillDesignerMapper {

    /**
     * 根据账单ID获取账单信息
     * @param billId 账单ID
     * @return 账单信息
     */
    BillDesigner selectBillDesignerById(Long billId);

    /**
     * 插入设计师子账单
     * @param billDesignerSub 账单
     * @return 是否成功
     */
    int insertBillDesignerSub(BillDesignerSub billDesignerSub);

    /**
     * 批量插入设计师子账单
     * @param billDesignerSubs 账单
     * @return 是否成功
     */
    int batchInsertBillDesignerSubs(List<BillDesignerSub> billDesignerSubs);

    /**
     * 插入主账单
     * @param billDesigner 设计师主账单
     * @return 是否成功
     */
    int insertBillDesigner(BillDesigner billDesigner);

    /**
     * 修改
     * @param billDesigner 设计师账单
     * @return 是否成功
     */
    int updateBillDesigner(BillDesigner billDesigner);

    /**
     * 批量插入账单对应订单
     * @param orderDtos 订单列表
     * @return 是否成功
     */
    int batchInsertBillOrder(List<OrderDto> orderDtos);


    /**
     * 查询主账单信息
     * 根据品牌id，设计师id，账期开始，账期结束
     */
    BillDesigner selectBillDesignerByDesignerId(OrderVo orderVo);

    /**
     * 获取上一个账单
     * 根据品牌id，设计师id，账期开始，账期结束
     */
    BillDesigner selectPreBillDesignerByDesignerId(OrderVo orderVo);

    /**
     * 查询主账单信息
     * 根据品牌id，设计师id，账期开始，账期结束 主体id
     */
    BillDesignerSub selectBillDesignerSubByDesignerIdAndCompanyInfoId(OrderVo orderVo);

    /**
     * 修改子账单
     * @param billDesignerSub 参数
     * @return 是否成功
     */
    int updateBillDesignerSub(BillDesignerSub billDesignerSub);

    /**
     * 查询子账单信息
     * 根据品牌id，设计师id，账期开始，账期结束
     */
    List<BillDesignerSub> selectBillDesignerSubByDesignerId(OrderVo orderVo);

    /**
     * 根据设计师ID获取待结算的订单
     * @param designerId 设计师ID
     * @return 结算订单
     */
    BillDesigner getPendingSettlementBillDesignerByDesignerId(Long designerId);

    /**
     * 获取设计师在该品牌内未结算的账单，可能未结清的有多个
     */
    List<BillDesigner> getNotSettlementBillDesignerByDesignerId(@Param(value = "designerId") Long designerId, @Param(value = "brandId") Long brandId);

    /**
     * 汇总佣金
     */
    List<BillDesignerSub> sumCommissionBack(Long billId);

    /**
     * 根据主账单id获取子帐单列表
     * @param billId 主帐单id
     * @return 子帐单列表
     */
    List<BillDesignerSub> selectDesignerBillSubListByBillId(Long billId);
}
