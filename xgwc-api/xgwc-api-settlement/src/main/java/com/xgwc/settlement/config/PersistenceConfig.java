package com.xgwc.settlement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-09-06
 */
@RefreshScope
@ConfigurationProperties(prefix = "binlog.persistence")
@Component
@Data
public class PersistenceConfig {

    /**
     * 是否持久化：当为1时开启持久化，
     * 注意：当open开启，重启机器时会从记录的binlog文件和位置开始读取信息，position并不是实时的，只有当binlog文件重新生成时才会更新，所以会产生跟之前的重复信息
     *      当open关闭，重启机器时会从最新的binlog文件和位置开始读取信息
     */
    private int open;
}
