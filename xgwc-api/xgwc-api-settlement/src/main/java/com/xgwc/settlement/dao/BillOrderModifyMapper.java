package com.xgwc.settlement.dao;

import com.xgwc.settlement.entity.BillOrderModify;
import com.xgwc.settlement.entity.dto.OrderDto;
import com.xgwc.settlement.entity.vo.OrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BillOrderModifyMapper {

    /**
     * 插入账单订单
     * @param orderDto 订单信息
     * @return 是否成功
     */
    int insertBillOrderModify(OrderDto orderDto);

    /**
     * 根据订单id查询最近的订单最新的成交数据
     * @return 订单信息
     */
    OrderDto getRecentlyOrderDto(Long orderId);

    /**
     * 获取设计师账期内的修改所有订单
     * 根据品牌id，设计师id，账期开始，账期结束
     */
    List<OrderDto> getOrderModifyListByBillDate(OrderVo orderVo);

    /**
     * 根据updateTime获取新增的数据
     * @param updateTimeStart 开始时间
     * @param updateTimeEnd 结束时间
     * @return 订单数据
     */
    List<BillOrderModify> getBillModifyListByBillDate(@Param(value = "updateTimeStart") String updateTimeStart, @Param(value = "updateTimeEnd") String updateTimeEnd);


    /**
     * 获取未结算的售后订单
     * @param designerId 设计师id
     * @return 订单
     */
    List<BillOrderModify> getUnsettledBillOrderModifyList(@Param(value = "designerId") Long designerId, @Param(value = "brandId") Long brandId);

    /**
     * 修改订单修改记录
     * @param billOrderModify 参数
     * @return 是否成功
     */
    int updateBillOrderModify(BillOrderModify billOrderModify);
}
