package com.xgwc.settlement.controller;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.settlement.service.BillOrderModifyService;
import com.xgwc.settlement.service.BillService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("bill")
@RestController
public class BillController {

    @Resource
    private BillService billService;

    @Resource
    private BillOrderModifyService billOrderModifyService;


    /**
     * 生成账单
     */
    @RequestMapping("publishBill")
    public ApiResult publishBill(){
        log.info("生成账期");
        billService.publishBill();
        return ApiResult.ok();
    }

    /**
     * 谨慎调用，调用前确保该品牌商该账期下没有生成数据
     * 生成账单
     * @param brandId 品牌商id
     * @param periodId 账期id
     */
    @RequestMapping("publishBill2")
    public ApiResult publishBill2(Long brandId, Long periodId){
        log.info("生成账期,参数:{},{}", brandId, periodId);
        billService.publishBill(brandId, periodId);
        return ApiResult.ok();
    }

    /**
     * 触发订单佣金扣除（无票的时候直接调用，有发票的时候审核通过时调用）
     * @param billId 订单id
     * @return 订单信息
     */
    @RequestMapping("preStaticsCommissionBack")
    public ApiResult preStaticsCommissionBack(Long billId){
        billOrderModifyService.preStaticsCommissionBack(billId);
        return ApiResult.ok();
    }
}
