package com.xgwc.settlement.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillOrderModify {

    private Long id;
    private Long orderId;
    private Long saleManId;
    private String saleManName;
    private Long deptId;
    private String deptName;
    private Integer transferState;
    private String orderDate;
    private Long storeId;
    private String storeName;
    private Long brandId;
    private Integer payChannel;
    private Integer shType;
    private String orderNo;
    private String taobaoId;
    private Double orderAmount;
    private Double amount;
    private BigDecimal nowAmount;
    private Long companyInfoId;
    private Integer payType;
    private String allotRemark;
    private Integer allotNum;
    private Long allotUserId;
    private String allotUserName;
    private Long pid;
    private Long designerId;
    private String designerName;
    private String designerBusiness;
    private BigDecimal money;
    private BigDecimal commissionBacked;
    private BigDecimal commissionBackRemaining;
    private Integer backStatus;
    private String createTime;
    private Long franchiseId;
    private String modifyTime;
    private String updateTime;
    //流程id
    private Long applyId;
}
