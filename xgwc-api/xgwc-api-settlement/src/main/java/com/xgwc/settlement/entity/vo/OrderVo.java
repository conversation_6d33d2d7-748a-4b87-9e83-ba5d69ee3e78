package com.xgwc.settlement.entity.vo;

import lombok.Data;

@Data
public class OrderVo {

    /**
     * 设计师id
     */
    private Long designerId;

    /**
     * 成交时间
     */
    private String dealTimeStart;

    /**
     * 成交时间
     */
    private String dealTimeEnd;

    /**
     * 行修改时间
     */
    private String modifyTimeStart;

    /**
     * 行更新时间
     */
    private String modifyTimeEnd;

    /**
     * 品牌商ID
     */
    private Long brandId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 账单开始时间
     */
    private String billStart;

    /**
     * 账单结束时间
     */
    private String billEnd;

    /**
     * 主体ID
     */
    private Long companyInfoId;
}
