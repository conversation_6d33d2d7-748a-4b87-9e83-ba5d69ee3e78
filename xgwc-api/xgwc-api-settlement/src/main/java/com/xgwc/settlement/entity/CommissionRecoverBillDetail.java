package com.xgwc.settlement.entity;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class CommissionRecoverBillDetail {

    /** 主键 */
    private Long id;

    /** 订单ID */
    private Long orderId;

    /** 订单编号 */
    private String orderNo;

    /** 客户ID */
    private String customerNo;

    /** 谈单人ID */
    private Long saleManId;

    /** 谈单人名称 */
    private String saleManName;

    /** 加盟商ID */
    private Long franchiseId;

    /** 品牌商ID */
    private Long brandId;

    /** 设计师ID */
    private Long designerId;

    /** 原佣金 */
    private BigDecimal preCommission;

    /** 现佣金 */
    private BigDecimal nowCommission;

    /** 需追回佣金 */
    private BigDecimal needRecoverCommission;

    /** 已追回佣金 */
    private BigDecimal recoveredCommission;

    /** 最后追回时间 */
    private String lastRecoverTime;

    /** 追回状态：0.待追回，1.无需追回，2.已追回，3部分追回 */
    private Integer recoverStatus;

    /** 状态：0正常，1非正常 */
    private Integer status;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /** 账单开始时间 */
    private String billStart;

    /** 账单结束时间 */
    private String billEnd;

}