package com.xgwc.settlement.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class OrderDto extends BillOrderDto{

    /**
     * 交稿状态： 交稿状态:0 未接单, 1 制作中, 2 已交初稿, 3 定稿待审核, 4 定稿被驳回, 5 已交定稿
     */
    @JSONField(name = "archive_type")
    private Integer archiveType;

    /**
     * 是否归档：0未归档  1已归档
     */
    @JSONField(name = "is_archiving")
    private Integer isArchiving;

    /**
     * 是否售后：0未售后 1售后中
     */
    @JSONField(name = "is_after_sale")
    private Integer isAfterSale;

    /**
     * 是否已经结算
     */
    private Integer settlement;

}
