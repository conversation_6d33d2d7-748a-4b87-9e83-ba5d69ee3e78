package com.xgwc.settlement.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.util.StringUtils;
import com.xgwc.settlement.dao.BillDesignerMapper;
import com.xgwc.settlement.dao.BillOrderMapper;
import com.xgwc.settlement.dao.BillOrderModifyMapper;
import com.xgwc.settlement.dao.XgwcOrderMapper;
import com.xgwc.settlement.entity.BillDesigner;
import com.xgwc.settlement.entity.JsonTableData;
import com.xgwc.settlement.entity.dto.OrderDto;
import com.xgwc.settlement.service.BillOrderModifyService;
import com.xgwc.settlement.service.BinLogOrderService;
import com.xgwc.settlement.util.BillUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;

@Slf4j
@Service
public class BinLogOrderServiceImpl implements BinLogOrderService {

    private final String database = "xgwc_sass";

    private final String tableName = "xgwc_order";

    private final String UPDATE = "update";

    private ScheduledExecutorService executor = Executors.newScheduledThreadPool(10);

    /**
     * 阻塞队列, 设置队列长度为5000
     */
    private BlockingQueue<JSONArray> blockingQueue = new ArrayBlockingQueue<>(50000);

    @Resource
    private XgwcOrderMapper xgwcOrderMapper;

    @Resource
    private BillOrderModifyMapper billOrderModifyMapper;

    @Resource
    private BillDesignerMapper billDesignerMapper;

    @Resource
    private BillOrderModifyService billOrderModifyService;

    @Resource
    private BillOrderMapper billOrderMapper;



    public void handleOrder(JSONArray jsonArray) {
        String uuid = StringUtils.remove(UUID.randomUUID().toString(), '-');
        log.info("{}-开始处理订单数据:{},队列数量:{}", uuid, jsonArray, blockingQueue.size());
        if(jsonArray !=null && !jsonArray.isEmpty()) {
            JSONObject jsonObject = jsonArray.getJSONObject(0);
            JSONObject before = jsonObject.getJSONObject("before");
            JSONObject after = jsonObject.getJSONObject("after");
            if(before != null){
                OrderDto beforeOrderDto = JSONObject.toJavaObject(before, OrderDto.class);
                OrderDto afterOrderDto = JSONObject.toJavaObject(after, OrderDto.class);
                //判断是否已经锁定或者已经结算, 算钱的差额
                BigDecimal diffMoney = moneyChange(beforeOrderDto, afterOrderDto);
                try {
                    if ((afterOrderDto.getIsLock() != null && afterOrderDto.getIsLock() == 1)
                            || (afterOrderDto.getSettlement() != null && afterOrderDto.getSettlement() == 1)) {
                        handleLockedOrSettlementOrder(uuid, afterOrderDto, diffMoney);
                    } else {
                        //没有被锁定
                        handleNotLockedOrSettlementOrder(uuid, afterOrderDto, diffMoney);
                    }
                }catch (Exception e) {
                    log.info("{}-订单处理失败", uuid, e);
                }
            }
        }
    }


    /**
     * 处理锁定或者结算的订单
     */
    private void handleLockedOrSettlementOrder(String uuid, OrderDto afterOrderDto, BigDecimal diffMoney){
        log.info("{}-订单处于锁定状态:{}", uuid, afterOrderDto.getOrderId());
        //有金额变更
        if(diffMoney.compareTo(BigDecimal.ZERO) != 0){
            //写入到bill_order_modify中, 设置佣金剩余追回
            afterOrderDto.setOrderId(afterOrderDto.getId());
            afterOrderDto.setId(null);
            afterOrderDto.setCommissionBackRemaining(diffMoney);
            billOrderModifyMapper.insertBillOrderModify(afterOrderDto);
            List<BillDesigner> billDesignerList = billDesignerMapper.getNotSettlementBillDesignerByDesignerId(afterOrderDto.getDesignerId(), afterOrderDto.getBrandId());
            List<BillDesigner> pengdingList = getPengdingBillDesignerList(billDesignerList);
            if(pengdingList != null && !pengdingList.isEmpty()) {
                //重新进行佣金追回
                pengdingList.forEach(billDesigner -> billOrderModifyService.preStaticsCommissionBack(billDesigner.getBillId()));
            }
        }
    }

    /**
     * 处理没有锁定和计算的订单
     */
    private void handleNotLockedOrSettlementOrder(String uuid, OrderDto afterOrderDto, BigDecimal diffMoney){
        log.info("{}-订单处于未锁定状态:{}", uuid, afterOrderDto.getOrderId());
        //首先判断在不在账期里，不在账期可以随意变化
        BillDesigner billDesigner = getOrderDesigner(afterOrderDto.getId());
        if(billDesigner != null){
            log.info("{}-订单:{}在账期内,账期id:{},账期处于状态:{}", uuid, afterOrderDto.getOrderId(), billDesigner.getBillId(), billDesigner.getSettlementStatus());
            //在账期内，判断账单状态 如果是待确认
            if(billDesigner.getSettlementStatus() == null || billDesigner.getSettlementStatus() == 0 || billDesigner.getSettlementStatus() == 1
                    || billDesigner.getSettlementStatus() == 4 || billDesigner.getSettlementStatus() == 5){
                log.info("{}-订单:{}在账期内,账期id:{},账期处于待确认状态", uuid, afterOrderDto.getOrderId(), billDesigner.getBillId());
                boolean isFinished = orderIsFinished(afterOrderDto);
                if(isFinished){
                    log.info("{}-订单:{}在账期内,账期id:{},订单已完成", uuid, afterOrderDto.getOrderId(), billDesigner.getBillId());
                    //如果有金额变化
                    if(diffMoney.compareTo(BigDecimal.ZERO) != 0){
                        //修改账单订单
                        afterOrderDto.setOrderId(afterOrderDto.getId());
                        afterOrderDto.setBillId(billDesigner.getBillId());
                        billOrderMapper.updateBillOrderDto(afterOrderDto);
                        //重新统计
                        billOrderModifyService.preStaticsCommission(billDesigner);
                    }
                    //没有金额变化不错处理
                }else{
                    log.info("{}-订单:{}在账期内,账期id:{},订单未完成", uuid, afterOrderDto.getOrderId(), billDesigner.getBillId());
                    //变成了未完成状态
                    billOrderMapper.deleteBillOrder(afterOrderDto.getId(), billDesigner.getBillId());
                    //重新统计
                    billOrderModifyService.preStaticsCommission(billDesigner);
                }
            }else{
                log.info("{}-订单:{}在账期内,账期id:{},账期处于确认状态", uuid, afterOrderDto.getOrderId(), billDesigner.getBillId());
                //如果订单是确认状态
                if(diffMoney.compareTo(BigDecimal.ZERO) != 0){
                    //修改账单订单
                    afterOrderDto.setOrderId(afterOrderDto.getId());
                    afterOrderDto.setCommissionBackRemaining(diffMoney);
                    afterOrderDto.setId(null);
                    billOrderModifyMapper.insertBillOrderModify(afterOrderDto);
                    //执行佣金追回
                    billOrderModifyService.preStaticsCommissionBack(billDesigner.getBillId());
                }
            }
        }else{
            log.info("{}-订单:{}不在账期内，直接忽略", uuid, afterOrderDto.getOrderId());
            //不在账期内可以随意操作
        }
    }

    private BillDesigner getOrderDesigner(Long orderId){
        List<Long> billIds = billOrderMapper.existOrderId(orderId);
        if(billIds != null && !billIds.isEmpty()) {
            //在账期里
            if (billIds.size() > 1) {
                log.error("订单:{}存在与多个账期:{}内,请检查!", orderId, billIds);
            }
            Long billId = billIds.get(0);
            //获取账期
            return billDesignerMapper.selectBillDesignerById(billId);
        }
        return null;
    }
    protected List<BillDesigner> getPengdingBillDesignerList(List<BillDesigner> billDesignerList){
        if(billDesignerList != null && !billDesignerList.isEmpty()) {
            //判断是由否已确认待结算的订单
            List<BillDesigner> pengdingList = billDesignerList.stream().filter(x->x.getSettlementStatus() != null && x.getSettlementStatus() == 2).toList();
            if(!pengdingList.isEmpty()) {
                return pengdingList;
            }
        }
        return null;
    }

    private JSONArray getJsonArrayData(String jsonData){
        JsonTableData jtd = JSONObject.parseObject(jsonData, JsonTableData.class);
        if(jtd.getDatabaseName().equals(database) && jtd.getTableName().equals(tableName) && jtd.getMethod().equals(UPDATE)){
            return jtd.getData();
        }
        return null;
    }

    /**
     * 判断整个订单是否完成
     */
    private boolean orderIsFinished(OrderDto orderDto) {
        if(orderDto.getPid() == null || orderDto.getPid() == 0){
            orderDto = xgwcOrderMapper.selectOrderByOrderId(orderDto.getPid());
        }
        //获取母订单的完成状况
        if(orderDto.getShType() == 1 && orderDto.getIsAfterSale() == 0){
            return true;
        }
        return false;
    }

    /**
     * 判断是否有金额变化
     * @param beforeOrderDto 修改之前订单信息
     * @param afterOrderDto 修改只会订单信息
     */
    private BigDecimal moneyChange(OrderDto beforeOrderDto, OrderDto afterOrderDto){
        BigDecimal beforeMoney = beforeOrderDto.getMoney() == null ? BigDecimal.ZERO : beforeOrderDto.getMoney();
        BigDecimal afterMoney = afterOrderDto.getMoney() == null ? BigDecimal.ZERO : afterOrderDto.getMoney();
        return beforeMoney.subtract(afterMoney);
    }

    protected List<OrderDto> mergeSubOrderList(OrderDto orderDto){
        List<OrderDto> result = new ArrayList<>();
        result.add(orderDto);
        List<OrderDto> orderDtos = xgwcOrderMapper.selectSubOrderList(Collections.singletonList(orderDto.getId()));
        if(orderDtos != null && !orderDtos.isEmpty()) {
            result.addAll(orderDtos);
        }
        return result;
    }

    protected boolean isMeetCondition(OrderDto orderDto, List<OrderDto> allOrderList){
        boolean isMeetCondition = BillUtil.isMeetTheConditions(orderDto);
        if(isMeetCondition){
            //子订单全部交稿才算真正完成
            List<OrderDto> filterList = allOrderList.stream().filter(x->(x.getArchiveType() == null || x.getArchiveType() != 5)).toList();
            if(!filterList.isEmpty()) {
                log.info("主账单:{}满足成交条件,子账单不满足成交条件", JSONObject.toJSONString(orderDto));
                return false;
            }
            log.info("主账单:{}满足成交条件,子账单满足成交条件", orderDto.getId());
            return true;
        }
        log.info("主账单:{}不满足成交条件", JSONObject.toJSONString(orderDto));
        return false;
    }

    @Override
    public void handleOrderBinLog(JSONObject orderBinLog) {
        try {
            JSONArray jsonArray = getJsonArrayData(orderBinLog.toJSONString());
            if(jsonArray != null && jsonArray.size() > 0) {
                blockingQueue.put(jsonArray);
                executor.schedule(()->{
                    try {
                        JSONArray jsonArray1 = blockingQueue.take();
                        handleOrder(jsonArray1);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }, 10, TimeUnit.SECONDS);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
