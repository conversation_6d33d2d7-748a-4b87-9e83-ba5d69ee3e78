package com.xgwc.settlement.service.impl;

import com.xgwc.settlement.service.BinLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class BinlogRunner implements ApplicationRunner {

    @Resource
    private BinLogService binLogService;

    @Override
    public void run(ApplicationArguments args) {
        log.info("binlog监听服务初始化监听任务开始");
        binLogService.receiveBinLogs();
    }
}
