<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.settlement.dao.XgwcPaymentDaysMapper">

   <select id="selectBillPeriodByBrandId" resultType="com.xgwc.settlement.entity.BillPeriod">
      SELECT
         *
      FROM
         bill_period t
      WHERE
         t.brand_id = #{brandId}
        AND t.payment_type = #{paymentType}
       order by t.last_bill_end desc limit 1
   </select>

   <select id="selectBillPeriodById" resultType="com.xgwc.settlement.entity.BillPeriod">
      SELECT
         *
      FROM
         bill_period t
      WHERE
         t.id = #{periodId}
   </select>

   <select id="selectByBrandId" resultType="com.xgwc.settlement.entity.XgwcPaymentDays">
      SELECT
         brand_id brandId, payment_type paymentType,cycle_type cycleType,tax_money taxMoney
      FROM
         xgwc_payment_days t
      WHERE
         t.brand_id = #{brandId}
        AND t.payment_type = #{paymentType}
   </select>

   <insert id="insertBillPeriod">
        insert into bill_period(last_bill_start, last_bill_end, brand_id, payment_type, status, create_time, update_time) values
           (#{lastBillStart}, #{lastBillEnd}, #{brandId}, #{paymentType}, 0, now(), now())
   </insert>

   <select id="selectByLastesLockPeriod" resultType="com.xgwc.settlement.entity.BillPeriod">
      SELECT
         *
      FROM
         bill_period t
      WHERE
         t.brand_id = #{brandId}
        and t.payment_type = #{paymentType}
        AND t.is_lock = 1
      ORDER BY
         t.lock_time DESC
         LIMIT 1
   </select>
</mapper>