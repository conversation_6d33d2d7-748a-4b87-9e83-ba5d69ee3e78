<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.settlement.dao.BrandOwnerMapper">

    <select id="getAllBrandOwnerList" resultType="com.xgwc.settlement.entity.BrandOwner">
        SELECT
            t.brand_id brandId,
            t.company_name companyName,
            ifnull(t.company_simple_name, t.company_name) companySimpleName
        FROM
            `xgwc_brand_owner` t
        WHERE
            t.`status` = 0
          AND t.is_del = 0
    </select>
</mapper>