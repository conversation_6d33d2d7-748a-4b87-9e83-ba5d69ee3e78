<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.settlement.dao.XgwcOrderMapper">
   <sql id="selectXgwcOrderVo">
      select allot_file, allot_num, allot_remark, allot_type, allot_urgency, allot_user_id, allot_user_name, amount, archive_appoint_time, archive_expect_time, archive_time, archive_type, brand_id,
             create_by, create_time, deal_time, dept_id, dept_name, designer_business, designer_id, designer_name, designer_phone, franchise_id, id, is_after_sale, is_archiving, is_del, modify_time,
             money, ifnull(now_amount,amount) now_amount, order_amount, order_date, order_no, pay_channel, pay_type, pid, remark, sale_man_id, sale_man_name, settlement, settlement_time, sh_type, state_dic_code,
             state_dic_name, store_id, store_name, taobao_id, transfer_state, update_by, update_time, is_lock, lock_time from xgwc_order t
   </sql>

   <sql id="selectXgwcOrderVo2">
      select t.allot_file, t.allot_num, t.allot_remark, t.allot_type, t.allot_urgency, t.allot_user_id, t.allot_user_name, t.amount, t.archive_appoint_time, t.archive_expect_time, t.archive_time, t.archive_type, t.brand_id,
             t.create_by, t.create_time, t.deal_time, t.dept_id, t.dept_name, t.designer_business, t.designer_id, t.designer_name, t.designer_phone, t.franchise_id, t.id, t.is_after_sale, t.is_archiving, t.is_del, t.modify_time,
             t.money, ifnull(t.now_amount,t.amount) now_amount, t.order_amount, t.order_date, t.order_no, t.pay_channel, t.pay_type, t.pid, t.remark, t.sale_man_id, t.sale_man_name, t.settlement, t.settlement_time, t.sh_type, t.state_dic_code,
             t.state_dic_name, t.store_id, t.store_name, t.taobao_id, t.transfer_state, t.update_by, t.update_time, t.is_lock, t.lock_time
   </sql>

   <select id="selectDesignerList" resultType="java.lang.Long">
      SELECT
         t.designer_id
      FROM
      xgwc_order t
      WHERE
      t.deal_time &gt;= #{dealTimeStart}
      AND t.deal_time &lt;= #{dealTimeEnd}
      and t.archive_type = 5
      and t.is_archiving = 1
      and t.is_after_sale = 0
      and t.brand_id = #{brandId}
      and t.settlement = 0
      and t.is_del = 0
      GROUP BY t.designer_id
   </select>

   <select id="selectOrderListByCondition" resultType="com.xgwc.settlement.entity.dto.OrderDto">
      <include refid="selectXgwcOrderVo2"/>
       , s.company_info_id companyInfoId
      FROM
         xgwc_order t
         left join xgwc_shop s on s.shop_id = t.store_id
      WHERE
         t.deal_time &gt;= #{dealTimeStart}
        AND t.deal_time &lt;= #{dealTimeEnd}
      and t.archive_type = 5
      and is_archiving = 1
      and is_after_sale = 0
      <if test="designerId != null">
         and t.designer_id = #{designerId}
      </if>
       and t.settlement = 0
       and t.is_del = 0
      and t.brand_id = #{brandId}
   </select>

   <select id="selectDesignerListNotInBill" resultType="java.lang.Long">
      SELECT
         t.designer_id
      FROM
         xgwc_order t
      WHERE
         t.deal_time &lt;= #{dealTimeStart}
        and t.archive_type = 5
        and t.is_archiving = 1
        and t.is_after_sale = 0
        and t.brand_id = #{brandId}
        and t.modify_time &gt;= #{modifyTimeEnd}
        and t.settlement = 1
        and t.is_del = 0
      GROUP BY t.designer_id
   </select>

   <select id="selectOrderListNotInBillByCondition" resultType="com.xgwc.settlement.entity.dto.OrderDto">
       <include refid="selectXgwcOrderVo2"/>
       , s.company_info_id companyInfoId
      FROM
      xgwc_order t
      left join xgwc_shop s on s.shop_id = t.store_id
      WHERE
      t.deal_time &lt;= #{dealTimeStart}
      and t.archive_type = 5
      and is_archiving = 1
      and is_after_sale = 0
      <if test="designerId != null">
         and t.designer_id = #{designerId}
      </if>
      and t.modify_time &gt;= #{modifyTimeEnd}
      and t.settlement = 1
      and t.is_del = 0
      and t.brand_id = #{brandId}
   </select>

   <select id="selectModifyOrderListByModifyTime" resultType="com.xgwc.settlement.entity.dto.OrderDto">
       <include refid="selectXgwcOrderVo2"/>
       , s.company_info_id companyInfoId
       FROM
       xgwc_order t
       left join xgwc_shop s on s.shop_id = t.store_id
      WHERE
        t.brand_id = #{brandId}
        and t.modify_time &gt;= #{modifyTimeStart} and t.modify_time &lt;= #{modifyTimeEnd} and t.is_del = 0
   </select>

    <select id="selectFinishedMainOrderListInBill" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        <include refid="selectXgwcOrderVo2"/>
        , s.company_info_id companyInfoId
        FROM
        xgwc_order t
        left join xgwc_shop s on s.shop_id = t.store_id
        WHERE
            t.pid = 0
          AND t.sh_type = 1
          AND t.is_after_sale = 0
          AND t.brand_id = #{brandId}
          AND t.deal_time &gt;= #{dealTimeStart}
          AND t.deal_time &lt;= #{dealTimeEnd}
          and t.settlement = 0
          and t.is_lock = 0
          and t.is_del = 0
    </select>

    <select id="selectSubOrderList" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        <include refid="selectXgwcOrderVo2"/>
        , s.company_info_id companyInfoId
        FROM
        xgwc_order t
        left join xgwc_shop s on s.shop_id = t.store_id
        where t.pid in
        <foreach item="id" collection="orderPidList" open="(" separator="," close=")">
            #{id}
        </foreach>
        and t.is_del = 0
    </select>

    <select id="selectOrderByOrderId" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        <include refid="selectXgwcOrderVo2"/>
        , s.company_info_id companyInfoId
        FROM
        xgwc_order t
        left join xgwc_shop s on s.shop_id = t.store_id
        where t.id = #{orderId}
        and t.is_del = 0
    </select>

    <select id="selectOrderListByOrderIds" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        <include refid="selectXgwcOrderVo2"/>
        , s.company_info_id companyInfoId
        FROM
        xgwc_order t
        left join xgwc_shop s on s.shop_id = t.store_id
        where t.id in
        <foreach item="id" collection="orderIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
        and t.is_del = 0
    </select>

</mapper>