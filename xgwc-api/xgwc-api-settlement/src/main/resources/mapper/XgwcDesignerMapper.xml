<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.settlement.dao.XgwcDesignerMapper">

   <select id="getDesignerManagementUserId" resultType="com.xgwc.settlement.entity.XgwcDesignerDto">
      SELECT
         t.designer_id designerId,
         t.`name` designerName,
         t.manager_user_id managementUserId,
         t.brand_id brandId
      FROM
         xgwc_designer t
      WHERE
         t.designer_id = #{designerId}
   </select>
</mapper>