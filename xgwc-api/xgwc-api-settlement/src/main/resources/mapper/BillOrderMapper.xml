<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.settlement.dao.BillOrderMapper">

    <sql id="selectBillOrderVo">
        select allot_num, allot_remark, allot_user_id, allot_user_name, amount, brand_id, create_time, dept_id, dept_name, designer_business, designer_id, designer_name, franchise_id, id, modify_time, money,
               now_amount, order_amount, order_date, order_id, order_no, pay_channel, pay_type, pid, sale_man_id, sale_man_name, sh_type, store_id, store_name, sub_bill_id, bill_id, company_info_id,
               taobao_id, transfer_state from bill_order t
    </sql>

    <insert id="batchInsertBillOrder" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO bill_order (
            sub_bill_id,
            bill_id,
            order_id,
            bill_start,
            bill_end,
            sale_man_id,
            sale_man_name,
            dept_id,
            dept_name,
            transfer_state,
            order_date,
            store_id,
            store_name,
            brand_id,
            pay_channel,
            sh_type,
            order_no,
            taobao_id,
            order_amount,
            amount,
            now_amount,
            pay_type,
            allot_remark,
            allot_num,
            allot_user_id,
            allot_user_name,
            pid,
            designer_id,
            designer_name,
            designer_business,
            money,
            create_time,
            franchise_id,
            company_info_id,
            deal_time,
            archive_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.subBillId,jdbcType=BIGINT},
            #{item.billId,jdbcType=BIGINT},
            #{item.orderId,jdbcType=BIGINT},
            #{item.billStart},
            #{item.billEnd},
            #{item.saleManId,jdbcType=BIGINT},
            #{item.saleManName,jdbcType=VARCHAR},
            #{item.deptId,jdbcType=BIGINT},
            #{item.deptName,jdbcType=VARCHAR},
            #{item.transferState,jdbcType=INTEGER},
            #{item.orderDate,jdbcType=DATE},
            #{item.storeId,jdbcType=BIGINT},
            #{item.storeName,jdbcType=VARCHAR},
            #{item.brandId,jdbcType=BIGINT},
            #{item.payChannel,jdbcType=INTEGER},
            #{item.shType,jdbcType=INTEGER},
            #{item.orderNo,jdbcType=VARCHAR},
            #{item.taobaoId,jdbcType=VARCHAR},
            #{item.orderAmount,jdbcType=DOUBLE},
            #{item.amount,jdbcType=DOUBLE},
            #{item.nowAmount,jdbcType=DECIMAL},
            #{item.payType,jdbcType=INTEGER},
            #{item.allotRemark,jdbcType=VARCHAR},
            #{item.allotNum,jdbcType=INTEGER},
            #{item.allotUserId,jdbcType=BIGINT},
            #{item.allotUserName,jdbcType=VARCHAR},
            #{item.pid,jdbcType=BIGINT},
            #{item.designerId,jdbcType=BIGINT},
            #{item.designerName,jdbcType=VARCHAR},
            #{item.designerBusiness,jdbcType=VARCHAR},
            #{item.money,jdbcType=DOUBLE},
            now(),
            #{item.franchiseId,jdbcType=BIGINT},
            #{item.companyInfoId},
            #{item.dealTime},
            #{item.archiveTime}
            )
        </foreach>
    </insert>

    <select id="getOrderListByBillDate" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        <include refid="selectBillOrderVo"/>
        WHERE
        t.designer_id = #{designerId}
        and t.brand_id = #{brandId}
        and t.bill_start= #{billStart}
        and t.bill_end = #{billEnd}
        order by t.create_time desc
    </select>

    <select id="getOrderDtoByOrderIdAndBillDate" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        <include refid="selectBillOrderVo"/>
        WHERE
        t.order_id = #{orderId}
        and t.bill_start= #{billStart}
        and t.bill_end = #{billEnd} limit 1
    </select>

    <delete id="deleteBillOrder">
        delete from bill_order where order_id = #{orderId} and t.bill_id = #{billId}
    </delete>

    <delete id="deleteBillOrderByBillId">
        delete from bill_order where id = #{id}
    </delete>

    <select id="exsitOrderDto" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        select * from bill_order t where t.order_id = #{orderId} and t.bill_id in
        <foreach item="item" collection="billIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateBillOrderDto" parameterType="com.xgwc.settlement.entity.dto.OrderDto">
        update bill_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="allotNum != null">allot_num = #{allotNum},</if>
            <if test="allotRemark != null">allot_remark = #{allotRemark},</if>
            <if test="allotUserId != null">allot_user_id = #{allotUserId},</if>
            <if test="allotUserName != null">allot_user_name = #{allotUserName},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="designerBusiness != null">designer_business = #{designerBusiness},</if>
            <if test="designerId != null">designer_id = #{designerId},</if>
            <if test="designerName != null">designer_name = #{designerName},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="money != null">money = #{money},</if>
            <if test="nowAmount != null">now_amount = #{nowAmount},</if>
            <if test="orderAmount != null">order_amount = #{orderAmount},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="payChannel != null">pay_channel = #{payChannel},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="saleManId != null">sale_man_id = #{saleManId},</if>
            <if test="saleManName != null">sale_man_name = #{saleManName},</if>
            <if test="shType != null">sh_type = #{shType},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="subBillId != null">sub_bill_id = #{subBillId},</if>
            <if test="taobaoId != null">taobao_id = #{taobaoId},</if>
            <if test="transferState != null">transfer_state = #{transferState},</if>
        </trim>
        where order_id = #{orderId} and bill_id = #{billId}
    </update>

    <select id="existOrderIds" resultType="java.lang.Long">
        SELECT
            order_id
        FROM
            bill_order t
        WHERE
            t.order_id IN
        <foreach item="item" collection="orderIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="existOrderId" resultType="java.lang.Long">
        SELECT
            t.bill_id
        FROM
            bill_order t
        WHERE
            t.order_id = #{orderId}
    </select>
</mapper>