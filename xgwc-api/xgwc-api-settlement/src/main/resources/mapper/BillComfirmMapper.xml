<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.settlement.dao.BillComfirmMapper">
    <sql id="selectBillComfirmVo">
        select check_status, check_time, provide_invoice, total_invoice_amount, comment, create_by, create_time, id, invoices, modify_time, bill_id, status, update_by, update_time, user_id from bill_comfirm
    </sql>

    <select id="selectBillComfirmByBillId" parameterType="Long" resultType="com.xgwc.settlement.entity.dto.BillComfirmDto">
        <include refid="selectBillComfirmVo"/>
        where bill_id = #{billId}
    </select>

</mapper>