<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.settlement.dao.BillDesignerMapper">

    <insert id="insertBillDesigner" parameterType="com.xgwc.settlement.entity.BillDesigner" useGeneratedKeys="true" keyProperty="billId">
        INSERT INTO bill_designer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stylistId != null">stylist_id,</if>
            <if test="stylistName != null">stylist_name,</if>
            <if test="managerUserId != null">manager_user_id,</if>
            <if test="billPeriodStart != null">bill_period_start,</if>
            <if test="billPeriodEnd != null">bill_period_end,</if>
            <if test="orderCount != null">order_count,</if>
            <if test="totalCommission != null">total_commission,</if>
            <if test="brandOwnerId != null">brand_owner_id,</if>
            <if test="fineAmount != null">fine_amount,</if>
            <if test="noInvoice != null">no_invoice,</if>
            <if test="commissionBack != null">commission_back,</if>
            <if test="billingTime != null">billing_time,</if>
            <if test="confirmationTime != null">confirmation_time,</if>
            <if test="settlementTime != null">settlement_time,</if>
            <if test="settlementStatus != null">settlement_status,</if>
            <if test="isLock != null">is_lock,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="stylistId != null">#{stylistId,jdbcType=BIGINT},</if>
            <if test="stylistName != null">#{stylistName,jdbcType=VARCHAR},</if>
            <if test="managerUserId != null">#{managerUserId,jdbcType=BIGINT},</if>
            <if test="billPeriodStart != null">#{billPeriodStart,jdbcType=DATE},</if>
            <if test="billPeriodEnd != null">#{billPeriodEnd,jdbcType=DATE},</if>
            <if test="orderCount != null">#{orderCount,jdbcType=INTEGER},</if>
            <if test="totalCommission != null">#{totalCommission,jdbcType=DECIMAL},</if>
            <if test="brandOwnerId != null">#{brandOwnerId,jdbcType=BIGINT},</if>
            <if test="fineAmount != null">#{fineAmount,jdbcType=DECIMAL},</if>
            <if test="noInvoice != null">#{noInvoice,jdbcType=DECIMAL},</if>
            <if test="commissionBack != null">#{commissionBack,jdbcType=DECIMAL},</if>
            <if test="billingTime != null">#{billingTime,jdbcType=TIMESTAMP},</if>
            <if test="confirmationTime != null">#{confirmationTime,jdbcType=TIMESTAMP},</if>
            <if test="settlementTime != null">#{settlementTime,jdbcType=TIMESTAMP},</if>
            <if test="settlementStatus != null">#{settlementStatus,jdbcType=TINYINT},</if>
            <if test="isLock != null">#{isLock,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            now(),
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            now()
        </trim>
    </insert>

    <insert id="insertBillDesignerSub" useGeneratedKeys="true" keyColumn="subBillId" keyProperty="subBillId">
        INSERT INTO bill_designer_sub
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billId != null">bill_id,</if>
            <if test="stylistId != null">stylist_id,</if>
            <if test="stylistName != null">stylist_name,</if>
            <if test="managerUserId != null">manager_user_id,</if>
            <if test="billPeriodStart != null">bill_period_start,</if>
            <if test="billPeriodEnd != null">bill_period_end,</if>
            <if test="orderCount != null">order_count,</if>
            <if test="totalCommission != null">total_commission,</if>
            <if test="brandOwnerId != null">brand_owner_id,</if>
            <if test="fineAmount != null">fine_amount,</if>
            <if test="noInvoice != null">no_invoice,</if>
            <if test="commissionBack != null">commission_back,</if>
            <if test="billingTime != null">billing_time,</if>
            <if test="confirmationTime != null">confirmation_time,</if>
            <if test="settlementTime != null">settlement_time,</if>
            <if test="settlementStatus != null">settlement_status,</if>
            <if test="isLock != null">is_lock,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            <if test="franchiseId != null">franchise_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="companyInfoId != null">company_info_id,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="billId != null">#{billId,jdbcType=BIGINT},</if>
            <if test="stylistId != null">#{stylistId,jdbcType=BIGINT},</if>
            <if test="stylistName != null">#{stylistName,jdbcType=VARCHAR},</if>
            <if test="managerUserId != null">#{managerUserId,jdbcType=BIGINT},</if>
            <if test="billPeriodStart != null">#{billPeriodStart,jdbcType=DATE},</if>
            <if test="billPeriodEnd != null">#{billPeriodEnd,jdbcType=DATE},</if>
            <if test="orderCount != null">#{orderCount,jdbcType=INTEGER},</if>
            <if test="totalCommission != null">#{totalCommission,jdbcType=DECIMAL},</if>
            <if test="brandOwnerId != null">#{brandOwnerId,jdbcType=BIGINT},</if>
            <if test="fineAmount != null">#{fineAmount,jdbcType=DECIMAL},</if>
            <if test="noInvoice != null">#{noInvoice,jdbcType=DECIMAL},</if>
            <if test="commissionBack != null">#{commissionBack,jdbcType=DECIMAL},</if>
            <if test="billingTime != null">#{billingTime,jdbcType=TIMESTAMP},</if>
            <if test="confirmationTime != null">#{confirmationTime,jdbcType=TIMESTAMP},</if>
            <if test="settlementTime != null">#{settlementTime,jdbcType=TIMESTAMP},</if>
            <if test="settlementStatus != null">#{settlementStatus,jdbcType=TINYINT},</if>
            <if test="isLock != null">#{isLock,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            now(),
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            now(),
            <if test="franchiseId != null">#{franchiseId,jdbcType=BIGINT},</if>
            <if test="brandId != null">#{brandId,jdbcType=BIGINT},</if>
            <if test="companyInfoId != null">#{companyInfoId,jdbcType=BIGINT},</if>
        </trim>
    </insert>

    <insert id="batchInsertBillDesignerSubs">
        INSERT INTO bill_designer_sub (
        bill_id,
        stylist_id,
        stylist_name,
        manager_user_id,
        bill_period_start,
        bill_period_end,
        order_count,
        total_commission,
        brand_owner_id,
        fine_amount,
        no_invoice,
        commission_back,
        billing_time,
        confirmation_time,
        settlement_time,
        settlement_status,
        is_lock,
        status,
        create_by,
        create_time,
        update_by,
        update_time,
        franchise_id,
        brand_id,
        company_info_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.billId,jdbcType=BIGINT},
            #{item.stylistId,jdbcType=BIGINT},
            #{item.stylistName,jdbcType=VARCHAR},
            #{item.managerUserId,jdbcType=BIGINT},
            #{item.billPeriodStart,jdbcType=DATE},
            #{item.billPeriodEnd,jdbcType=DATE},
            #{item.orderCount,jdbcType=INTEGER},
            #{item.totalCommission,jdbcType=DECIMAL},
            #{item.brandOwnerId,jdbcType=BIGINT},
            #{item.fineAmount,jdbcType=DECIMAL},
            #{item.noInvoice,jdbcType=DECIMAL},
            #{item.commissionBack,jdbcType=DECIMAL},
            #{item.billingTime,jdbcType=TIMESTAMP},
            #{item.confirmationTime,jdbcType=TIMESTAMP},
            #{item.settlementTime,jdbcType=TIMESTAMP},
            #{item.settlementStatus,jdbcType=TINYINT},
            #{item.isLock,jdbcType=TINYINT},
            #{item.status,jdbcType=TINYINT},
            #{item.createBy,jdbcType=VARCHAR},
            now(),
            #{item.updateBy,jdbcType=VARCHAR},
            now(),
            #{item.franchiseId,jdbcType=BIGINT},
            #{item.brandId,jdbcType=BIGINT},
            #{item.companyInfoId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    
    <insert id="batchInsertBillOrder" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO bill_order (
            sub_bill_id,
            order_id,
            sale_man_id,
            sale_man_name,
            dept_id,
            dept_name,
            transfer_state,
            order_date,
            store_id,
            store_name,
            brand_id,
            pay_channel,
            sh_type,
            order_no,
            taobao_id,
            order_amount,
            amount,
            now_amount,
            pay_type,
            allot_remark,
            allot_num,
            allot_user_id,
            allot_user_name,
            pid,
            designer_id,
            designer_name,
            designer_business,
            money,
            create_time,
            franchise_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.subBillId,jdbcType=BIGINT},
            #{item.orderId,jdbcType=BIGINT},
            #{item.saleManId,jdbcType=BIGINT},
            #{item.saleManName,jdbcType=VARCHAR},
            #{item.deptId,jdbcType=BIGINT},
            #{item.deptName,jdbcType=VARCHAR},
            #{item.transferState,jdbcType=INTEGER},
            #{item.orderDate,jdbcType=DATE},
            #{item.storeId,jdbcType=BIGINT},
            #{item.storeName,jdbcType=VARCHAR},
            #{item.brandId,jdbcType=BIGINT},
            #{item.payChannel,jdbcType=INTEGER},
            #{item.shType,jdbcType=INTEGER},
            #{item.orderNo,jdbcType=VARCHAR},
            #{item.taobaoId,jdbcType=VARCHAR},
            #{item.orderAmount,jdbcType=DOUBLE},
            #{item.amount,jdbcType=DOUBLE},
            #{item.nowAmount,jdbcType=DECIMAL},
            #{item.payType,jdbcType=INTEGER},
            #{item.allotRemark,jdbcType=VARCHAR},
            #{item.allotNum,jdbcType=INTEGER},
            #{item.allotUserId,jdbcType=BIGINT},
            #{item.allotUserName,jdbcType=VARCHAR},
            #{item.pid,jdbcType=BIGINT},
            #{item.designerId,jdbcType=BIGINT},
            #{item.designerName,jdbcType=VARCHAR},
            #{item.designerBusiness,jdbcType=VARCHAR},
            #{item.money,jdbcType=DOUBLE},
            now(),
            #{item.franchiseId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <update id="updateBillDesigner" parameterType="com.xgwc.settlement.entity.BillDesigner">
        update bill_designer
        <trim prefix="SET" suffixOverrides=",">
            <if test="stylistId != null">stylist_id = #{stylistId},</if>
            <if test="stylistName != null">stylist_name = #{stylistName},</if>
            <if test="managerUserId != null">manager_user_id = #{managerUserId},</if>
            <if test="billPeriodStart != null">bill_period_start = #{billPeriodStart},</if>
            <if test="billPeriodEnd != null">bill_period_end = #{billPeriodEnd},</if>
            <if test="orderCount != null">order_count = #{orderCount},</if>
            <if test="totalCommission != null">total_commission = #{totalCommission},</if>
            <if test="brandOwnerId != null">brand_owner_id = #{brandOwnerId},</if>
            <if test="fineAmount != null">fine_amount = #{fineAmount},</if>
            <if test="noInvoice != null">no_invoice = #{noInvoice},</if>
            <if test="payableAmount != null">payable_amount = #{payableAmount},</if>
            <if test="commissionBack != null">commission_back = #{commissionBack},</if>
            <if test="billingTime != null">billing_time = #{billingTime},</if>
            <if test="confirmationTime != null">confirmation_time = #{confirmationTime},</if>
            <if test="settlementTime != null">settlement_time = #{settlementTime},</if>
            <if test="settlementStatus != null">settlement_status = #{settlementStatus},</if>
            <if test="isLock != null">is_lock = #{isLock},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where bill_id = #{billId}
    </update>

    <select id="selectBillDesignerByDesignerId" resultType="com.xgwc.settlement.entity.BillDesigner">
        SELECT
            *
        FROM
            bill_designer t
        WHERE
            t.stylist_id = #{designerId}
          AND t.brand_owner_id = #{brandId}
          AND t.bill_period_start = #{billStart}
          AND t.bill_period_end = #{billEnd}
          AND t.`status` = 0
    </select>

    <select id="selectPreBillDesignerByDesignerId" resultType="com.xgwc.settlement.entity.BillDesigner">
        SELECT
            *
        FROM
            bill_designer t
        WHERE
            t.stylist_id = #{designerId}
          AND t.brand_owner_id = #{brandId}
          AND t.`status` = 0
          order by bill_period_end desc limit 1
    </select>

    <select id="selectBillDesignerSubByDesignerIdAndCompanyInfoId" resultType="com.xgwc.settlement.entity.BillDesignerSub">
        SELECT
            *
        FROM
            bill_designer_sub t
        WHERE
            t.stylist_id = #{designerId}
          AND t.brand_owner_id = #{brandId}
          AND t.bill_period_start = #{billStart}
          AND t.bill_period_end = #{billEnd}
          and t.company_info_id = #{companyInfoId}
          AND t.`status` = 0
    </select>

    <select id="selectBillDesignerSubByDesignerId" resultType="com.xgwc.settlement.entity.BillDesignerSub">
        SELECT
            *
        FROM
            bill_designer_sub t
        WHERE
            t.stylist_id = #{designerId}
          AND t.brand_owner_id = #{brandId}
          AND t.bill_period_start = #{billStart}
          AND t.bill_period_end = #{billEnd}
          AND t.`status` = 0
    </select>

    <update id="updateBillDesignerSub" parameterType="com.xgwc.settlement.entity.BillDesignerSub">
        update bill_designer_sub
        <trim prefix="SET" suffixOverrides=",">
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="billPeriodEnd != null">bill_period_end = #{billPeriodEnd},</if>
            <if test="billPeriodStart != null">bill_period_start = #{billPeriodStart},</if>
            <if test="billingTime != null">billing_time = #{billingTime},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="brandOwnerId != null">brand_owner_id = #{brandOwnerId},</if>
            <if test="commissionBack != null">commission_back = #{commissionBack},</if>
            <if test="companyInfoId != null">company_info_id = #{companyInfoId},</if>
            <if test="confirmationTime != null">confirmation_time = #{confirmationTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="fineAmount != null">fine_amount = #{fineAmount},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="isLock != null">is_lock = #{isLock},</if>
            <if test="managerUserId != null">manager_user_id = #{managerUserId},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="noInvoice != null">no_invoice = #{noInvoice},</if>
            <if test="orderCount != null">order_count = #{orderCount},</if>
            <if test="settlementStatus != null">settlement_status = #{settlementStatus},</if>
            <if test="settlementTime != null">settlement_time = #{settlementTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="stylistId != null">stylist_id = #{stylistId},</if>
            <if test="stylistName != null">stylist_name = #{stylistName},</if>
            <if test="totalCommission != null">total_commission = #{totalCommission},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where sub_bill_id = #{subBillId}
    </update>

    <select id="selectBillDesignerById" resultType="com.xgwc.settlement.entity.BillDesigner">
        SELECT
            *
        FROM
            bill_designer t where t.bill_id = #{billId}
    </select>

    <select id="getPendingSettlementBillDesignerByDesignerId" resultType="com.xgwc.settlement.entity.BillDesigner">
        SELECT
            *
        FROM
            bill_designer t
        WHERE
            t.stylist_id = #{designerId} and t.settlement_status = 2
    </select>

    <select id="getNotSettlementBillDesignerByDesignerId" resultType="com.xgwc.settlement.entity.BillDesigner">
        SELECT
            *
        FROM
            bill_designer t
        WHERE
            t.stylist_id = #{designerId}
          AND t.brand_owner_id = #{brandId}
          AND t.is_lock = 0
          and t.settlement_status !=3
    </select>

    <select id="sumCommissionBack" resultType="com.xgwc.settlement.entity.BillDesignerSub">
        SELECT
            t.sub_bill_id subBillId,
            sum( t.money ) totalCommission
        FROM
            bill_order t
        WHERE
            t.bill_id = #{billId}
        GROUP BY
            t.sub_bill_id
    </select>

    <select id="selectDesignerBillSubListByBillId" resultType="com.xgwc.settlement.entity.BillDesignerSub">
        SELECT
            *
        FROM
            bill_designer t
        WHERE
            t.bill_id = #{billId}
    </select>

</mapper>