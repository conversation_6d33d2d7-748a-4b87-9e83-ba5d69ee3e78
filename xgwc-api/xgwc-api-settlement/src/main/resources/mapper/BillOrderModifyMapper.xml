<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.settlement.dao.BillOrderModifyMapper">

    <sql id="selectBillOrderModifyVo">
        select allot_num, allot_remark, allot_user_id, allot_user_name, amount, brand_id, create_time, dept_id, dept_name, designer_business, designer_id, designer_name, franchise_id, id,
               modify_time, money, ifnull(now_amount, amount) nowAmount, order_amount, order_date, order_id, order_no, pay_channel, pay_type, pid, sale_man_id, sale_man_name, sh_type, store_id, store_name,
            taobao_id, transfer_state,company_info_id from bill_order_modify t
    </sql>

    <select id="getRecentlyOrderDto" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        <include refid="selectBillOrderModifyVo"/>
        WHERE
            t.order_id = #{orderId}
        ORDER BY
            t.update_time DESC limit 1
    </select>

    <select id="getOrderModifyListByBillDate" resultType="com.xgwc.settlement.entity.dto.OrderDto">
        <include refid="selectBillOrderModifyVo"/>
        WHERE
        t.designer_id = #{designerId}
        and t.brand_id = #{brandId}
        and  (back_status !=2 or back_status is null)
        order by t.create_time desc
    </select>

    <insert id="insertBillOrderModify" parameterType="com.xgwc.settlement.entity.dto.OrderDto">
        insert into bill_order_modify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billStart != null">bill_start,</if>
            <if test="billEnd != null">bill_end,</if>
            <if test="allotNum != null">allot_num,</if>
            <if test="allotRemark != null">allot_remark,</if>
            <if test="allotUserId != null">allot_user_id,</if>
            <if test="allotUserName != null">allot_user_name,</if>
            <if test="amount != null">amount,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="designerBusiness != null">designer_business,</if>
            <if test="designerId != null">designer_id,</if>
            <if test="designerName != null">designer_name,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="id != null">id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="money != null">money,</if>
            <if test="nowAmount != null">now_amount,</if>
            <if test="orderAmount != null">order_amount,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="payChannel != null">pay_channel,</if>
            <if test="payType != null">pay_type,</if>
            <if test="pid != null">pid,</if>
            <if test="saleManId != null">sale_man_id,</if>
            <if test="saleManName != null">sale_man_name,</if>
            <if test="shType != null">sh_type,</if>
            <if test="storeId != null">store_id,</if>
            <if test="storeName != null">store_name,</if>
            <if test="subBillId != null">sub_bill_id,</if>
            <if test="taobaoId != null">taobao_id,</if>
            <if test="transferState != null">transfer_state,</if>
            <if test="companyInfoId != null">company_info_id,</if>
            <if test="commissionBackRemaining != null">commission_back_remaining,</if>
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billStart != null">#{billStart},</if>
            <if test="billEnd != null">#{billEnd},</if>
            <if test="allotNum != null">#{allotNum},</if>
            <if test="allotRemark != null">#{allotRemark},</if>
            <if test="allotUserId != null">#{allotUserId},</if>
            <if test="allotUserName != null">#{allotUserName},</if>
            <if test="amount != null">#{amount},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="designerBusiness != null">#{designerBusiness},</if>
            <if test="designerId != null">#{designerId},</if>
            <if test="designerName != null">#{designerName},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="id != null">#{id},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="money != null">#{money},</if>
            <if test="nowAmount != null">#{nowAmount},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="payChannel != null">#{payChannel},</if>
            <if test="payType != null">#{payType},</if>
            <if test="pid != null">#{pid},</if>
            <if test="saleManId != null">#{saleManId},</if>
            <if test="saleManName != null">#{saleManName},</if>
            <if test="shType != null">#{shType},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="storeName != null">#{storeName},</if>
            <if test="subBillId != null">#{subBillId},</if>
            <if test="taobaoId != null">#{taobaoId},</if>
            <if test="transferState != null">#{transferState},</if>
            <if test="companyInfoId != null">#{companyInfoId},</if>
            <if test="commissionBackRemaining != null">#{commissionBackRemaining},</if>
            now()
        </trim>
    </insert>

    <select id="getBillModifyListByBillDate" resultType="com.xgwc.settlement.entity.BillOrderModify">
        select * from bill_order_modify t where t.update_time &gt;= #{updateTimeStart} and t.update_time &lt;= #{updateTimeEnd}
    </select>

    <select id="getUnsettledBillOrderModifyList" resultType="com.xgwc.settlement.entity.BillOrderModify">
        SELECT
            t.id,
            t.amount,
            t.now_amount,
            t.money,
            t.commission_backed,
            t.commission_back_remaining
        FROM
            bill_order_modify t
        WHERE
            t.designer_id = #{designerId} and t.brand_id = #{brandId} and t.back_status !=2 and t.back_status != 3 order by t.update_time asc
    </select>

    <update id="updateBillOrderModify" parameterType="com.xgwc.settlement.entity.BillOrderModify">
        update bill_order_modify
        <trim prefix="SET" suffixOverrides=",">
            <if test="amount != null">amount = #{amount},</if>
            <if test="backStatus != null">back_status = #{backStatus},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="commissionBackRemaining != null">commission_back_remaining = #{commissionBackRemaining},</if>
            <if test="commissionBacked != null">commission_backed = #{commissionBacked},</if>
            <if test="companyInfoId != null">company_info_id = #{companyInfoId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="designerBusiness != null">designer_business = #{designerBusiness},</if>
            <if test="designerId != null">designer_id = #{designerId},</if>
            <if test="designerName != null">designer_name = #{designerName},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="money != null">money = #{money},</if>
            <if test="nowAmount != null">now_amount = #{nowAmount},</if>
            <if test="orderAmount != null">order_amount = #{orderAmount},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="saleManId != null">sale_man_id = #{saleManId},</if>
            <if test="saleManName != null">sale_man_name = #{saleManName},</if>
            <if test="taobaoId != null">taobao_id = #{taobaoId},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>