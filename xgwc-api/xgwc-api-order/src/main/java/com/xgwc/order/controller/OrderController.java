package com.xgwc.order.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.enums.OrderStatusEnums;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.entity.OrderTrace;
import com.xgwc.order.entity.dto.OrderArchiveDto;
import com.xgwc.order.entity.dto.OrderCompanyDto;
import com.xgwc.order.entity.dto.OrderDto;
import com.xgwc.order.entity.dto.OrderPlanDto;
import com.xgwc.order.entity.dto.PlatformOrderDto;
import com.xgwc.order.entity.vo.*;
import com.xgwc.order.service.IOrderService;
import com.xgwc.order.service.impl.OrderQueryService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/order")
public class OrderController extends BaseController {
    @Autowired
    private IOrderService orderService;
    @Autowired
    private OrderQueryService orderQueryService;

    /**
     * 查询订单管理列表
     */
    @MethodDesc("查询订单管理列表")
//    @PreAuthorize("@ss.hasPermission('order:order:list')")
    @GetMapping("/list")
    public ApiResult<OrderDto> list(OrderQueryVo order) {
        startPage();
        List<OrderDto> list = orderService.selectOrderList(order);
        return getDataTable(list);
    }


    /**
     * 导出订单管理列表
     */
    @MethodDesc("导出订单管理列表")
    @PreAuthorize("@ss.hasPermission('order:order:export')")
    @Log(title = "订单管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderQueryVo order) {
    }

    /**
     * 获取订单管理详细信息
     */
    @MethodDesc("获取订单管理详细信息")
//    @PreAuthorize("@ss.hasPermission('order:order:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<OrderDto> getInfo(@PathVariable("id") Long id) {
        return success(orderService.selectOrderById(id));
    }

    @MethodDesc("订单痕迹")
//    @PreAuthorize("@ss.hasPermission('order:order:query')")
    @GetMapping(value = "/trace/{orderId}")
    public ApiResult<List<OrderTrace>> trace(@PathVariable("orderId") Long orderId) {
        startPage();
        List<OrderTrace> list = orderService.selectOrderTraceById(orderId);
        return getDataTable(list);
    }

    /**
     * 获取订单管理详细信息
     */
    @MethodDesc("获取平台订单信息")
    @GetMapping(value = "/getByPlatformTreadNo")
    public ApiResult<PlatformOrderDto> getByPlatformTreadNo(@RequestParam("tradeNo") String tradeNo) {
        return success(orderQueryService.getByPlatformTreadNo(tradeNo));
    }

    /**
     * 新增订单管理
     */
    @MethodDesc("新增订单管理")
    @PreAuthorize("@ss.hasPermission('order:order:add')")
    @Log(title = "订单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody @Valid OrderVo order) {
        return toAjax(orderService.insertOrder(order));
    }

    /**
     * 修改订单管理
     */
    @MethodDesc("修改订单管理")
    @PreAuthorize("@ss.hasPermission('order:order:edit')")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody @Valid OrderVo order) {
        return toAjax(orderService.updateOrder(order));
    }

    /**
     * 删除订单管理
     */
    @MethodDesc("删除订单管理")
    @PreAuthorize("@ss.hasPermission('order:order:remove')")
    @Log(title = "订单管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(orderService.deleteOrderByIds(ids));
    }

    @MethodDesc("编辑订单进度")
    @PreAuthorize("@ss.hasPermission('order:plan:edit')")
    @Log(title = "编辑订单进度", businessType = BusinessType.UPDATE)
    @PostMapping("/plan")
    public ApiResult savePlan(@RequestBody @Valid List<OrderPlanVo> plans) {
        return toAjax(orderService.savePlan(plans));
    }

    @MethodDesc("查询订单进度")
    @PreAuthorize("@ss.hasPermission('order:plan:query')")
    @GetMapping("/plan/{orderId}")
    public ApiResult<OrderPlanDto> getPlans(@PathVariable("orderId") Long orderId) {
        return getDataTable(orderService.selectOrderPlanByOrderId(orderId));
    }

    @MethodDesc("订单交稿查询")
    @PreAuthorize("@ss.hasPermission('order:archive:query')")
    @GetMapping("/archive/{orderId}")
    public ApiResult<OrderArchiveDto> getArchives(@PathVariable("orderId") Long orderId) {
        return success(orderService.selectOrderArchiveByOrderId(orderId));
    }

    @MethodDesc("订单交稿审批")
    @PreAuthorize("@ss.hasPermission('order:archive:review')")
    @PostMapping("/archive")
    public ApiResult<OrderArchiveDto> archiveReview(@RequestBody OrderArchiveVo archiveVo) {
        return toAjax(orderService.archiveReview(archiveVo));
    }

    /**
     * 设计师接单
     *
     * @param orderNo 订单编号
     * @return success
     */
    @MethodDesc("设计师接单")
    @GetMapping("/designerOrders")
    public ApiResult designerOrders(@RequestParam String orderNo) {
        return orderService.designerOrders(orderNo);
    }

    @MethodDesc("跟换设计师")
    @PutMapping("/designer/update")
    public ApiResult designerUpdate(@RequestBody OrderDesignerUpdateVo vo) {
        return orderService.designerUpdate(vo);
    }

    @MethodDesc("历史订单查询")
//    @PreAuthorize("@ss.hasPermission('order:order:list')")
    @GetMapping("/history")
    public ApiResult<OrderDto> historyList(OrderHistoryQueryVo order) {
        startPage();
        List<OrderDto> list = orderService.selectHistoryOrderList(order);
        return getDataTable(list);
    }

    /**
     * 根据订单编号获取基础信息
     * @param orderNo 订单编号
     */
    @GetMapping("getOrderInfoByOrderNo")
    public ApiResult getOrderInfoByOrderNo(String orderNo) {
        if(StringUtils.isEmpty(orderNo)) {
            return ApiResult.error("参数为空");
        }
        OrderCompanyDto orderCompanyDto = orderService.selectOrderCompanyByOrderNo(orderNo);
        return success(orderCompanyDto);
    }

    /**
     * 修改订单状态
     *
     * @param code
     * @return
     */
    @RequestMapping("updateOrderStatus")
    public ApiResult updateOrderStatus(Long orderId, Integer code){
        if(orderId == null || code == null) {
            return ApiResult.error("参数缺失");
        }
        OrderStatusEnums orderStatusEnum = null;
        OrderStatusEnums[] orderStatusEnums = OrderStatusEnums.values();
        for(OrderStatusEnums orderStatusEnums1 : orderStatusEnums) {
            if(orderStatusEnums1.getType().equals(code)) {
                orderStatusEnum = orderStatusEnums1;
                break;
            }
        }
        orderService.updateOrderStatus(orderId, orderStatusEnum);
        return ApiResult.ok();
    }
}
