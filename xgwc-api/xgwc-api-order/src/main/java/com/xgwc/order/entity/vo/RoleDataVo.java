package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RoleDataVo {

    /**
     * 数据范围（0-全部数据 1-指定部门数据 2-本部门数据）
     */
    private Long dataScope;
    private Long deptId;
    /**
     * 限时（0-不限制时间 1-最近2个月 2-最近4个月 3-最近6个月）
     */
    private Long timeLimit;

    /**
     * 数据脱敏 （0-脱敏 1-不脱敏）
     */
    private Long dataMasking;


    private List<Long> deptIds;

    private Date createTime;

    private Long brandId;

    private Long franchiseId;

    private Boolean isDesensitization;
}
