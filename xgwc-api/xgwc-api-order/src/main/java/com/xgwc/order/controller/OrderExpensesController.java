package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.OrderExpensesApply;
import com.xgwc.order.entity.dto.OrderExpensesApplyDto;
import com.xgwc.order.entity.dto.OrderExpensesDto;
import com.xgwc.order.entity.dto.OrderInvoiceDto;
import com.xgwc.order.entity.vo.OrderExpensesQueryVo;
import com.xgwc.order.entity.vo.OrderInvoiceQueryVo;
import com.xgwc.order.service.OrderExpensesService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("order/expenses")
public class OrderExpensesController extends BaseController {

    @Resource
    private OrderExpensesService orderExpensesService;

    @PostMapping("addOrderExpenses")
    public ApiResult addOrderExpenses(@RequestBody OrderExpensesApply orderExpensesApply){
        return orderExpensesService.insertOrderExpensesApply(orderExpensesApply);
    }

    @PostMapping("updateOrderExpenses")
    public ApiResult updateFranchiseOrderExpenses(@RequestBody OrderExpensesApply orderExpensesApply){
        int result = orderExpensesService.updateOrderExpensesApply(orderExpensesApply);
        return result > 0 ? ApiResult.ok() :ApiResult.error("");
    }

    @GetMapping("getDetailById")
    public ApiResult getDetailById(Long id){
        if(id == null){
            return ApiResult.error("参数为空");
        }
        OrderExpensesApplyDto  orderExpensesApplyDto = orderExpensesService.selectOrderExpensesApplyById(id);
        return ApiResult.ok(orderExpensesApplyDto);
    }

    @MethodDesc("全部审批列表")
    @GetMapping("/getAllList")
    public ApiResult<OrderExpensesDto> getAllList(OrderExpensesQueryVo orderExpensesQueryVo){
        startPage();
        List<OrderExpensesDto> list = orderExpensesService.selectAllOrderExpensesApply(orderExpensesQueryVo);
        return getDataTable(list);
    }

}
