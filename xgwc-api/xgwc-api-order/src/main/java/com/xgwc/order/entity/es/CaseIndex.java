package com.xgwc.order.entity.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;

import java.util.Date;

/**
 * 案例索引
 */
@Data
@IndexName("xgwc-case-index")
public class CaseIndex{

    /**
     * 主键id
     */
    @IndexId
    private String id;

    /**
     * 业务主键
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long caseId;

    /**
     * 佣金
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private Double money;

    /**
     * 等级
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer level;

    /**
     * 标题
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String title;

    /**
     * 关键词
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String keyword;

    @IndexField(
            fieldType = FieldType.TEXT,
            analyzer = "hierarchy_analyzer",  // 自定义分词器
            searchAnalyzer = "hierarchy_analyzer"
    )
    private String typeCodes;

    @IndexField(fieldType = FieldType.TEXT)
    private String filePath;

    @IndexField(fieldType = FieldType.TEXT)
    private String coverUrl;

    @IndexField(fieldType = FieldType.LONG)
    private Long brandId;

    @IndexField(fieldType = FieldType.LONG)
    private Long franchiseId;

    @IndexField(fieldType = FieldType.LONG)
    private Long downloadCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date create_time;


}
