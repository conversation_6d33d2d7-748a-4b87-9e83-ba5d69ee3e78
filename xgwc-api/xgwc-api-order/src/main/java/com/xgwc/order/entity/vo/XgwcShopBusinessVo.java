package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: k<PERSON><PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-04-22  16:24
 */

/**
 * 服务管理-店铺-业务中间表
 */
@Data
public class XgwcShopBusinessVo {


    /** 主键 */
    @FieldDesc("主键")
    private Long id;

    /** 业务id */
    @FieldDesc("业务id")
    private Long businessId;
    private Long pbusinessId;

    /** 业务名称 */
    @FieldDesc("业务名称")
    private String businessName;

    /** 部门id */
    @FieldDesc("部门id")
    private Long deptId;

    /** 部门名称 */
    @FieldDesc("部门名称")
    private String deptName;

    /** 店铺id */
    @FieldDesc("店铺id")
    private Long shopId;

    /** 店铺名称 */
    @FieldDesc("店铺名称")
    private String shopName;

    /** 品牌id */
    @FieldDesc("品牌id")
    private Long brandId;

    /** 默认品牌商派单 0-是 1-否 */
    @FieldDesc("默认品牌商派单 0-是 1-否")
    private String isBrandOrder;

    /** 是否删除：0正常，1删除 */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
