package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.order.entity.vo.XgwcShopBusinessVo;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:22
 */


/**
 * 服务管理-店铺管理
 */
@Data
public class XgwcShopDto {

    /** 店铺id */
    @FieldDesc("店铺id")
    private Long shopId;

    /** 店铺名称 */
    @FieldDesc("店铺名称")
    private String shopName;

    /** 品牌id */
    @FieldDesc("品牌id")
    private Long brandId;

    /** 品牌名称 */
    @FieldDesc("品牌名称")
    private String brandName;

    /** 品牌商id */
    @FieldDesc("品牌商id")
    private Long brandOwnerId;

    /** 品牌商名称 */
    @FieldDesc("品牌商名称")
    private String brandOwnerName;

    /** 渠道id */
    @FieldDesc("渠道id")
    private Long channelId;

    /** 渠道名称 */
    @FieldDesc("渠道名称")
    private String channelName;

    /** 部门id */
    @FieldDesc("部门id")
    private Long deptId;

    /** 部门名称 */
    @FieldDesc("部门名称")
    private String deptName;

    /** 负责人id */
    @FieldDesc("负责人id")
    private Long managerId;

    /** 负责人花名 */
    @FieldDesc("负责人花名")
    private String managerName;

    /** 默认支付方式 1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫  */
    @FieldDesc("默认支付方式 1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫 ")
    private Integer payType;

    // 所属加盟商id
    @FieldDesc("所属加盟商id")
    private Long franchiseId;

    // 所属加盟商主键id
    @FieldDesc("所属加盟商id（franchise_owner主键）")
    private Long franchiseOwnerId;

    @FieldDesc("所属加盟商名称")
    private String companyName;

    // 平台店铺ID
    @FieldDesc("平台店铺ID")
    private String platformShopId;

    // 店铺保证金
    @FieldDesc("店铺保证金")
    private BigDecimal shopDeposit;

    // 经营主体
    @FieldDesc("经营主体id")
    private Long companyInfoId;

    /** 状态：0正常，1禁用 */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    /** 是否删除：0正常，1删除 */
    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /** 业务信息 */
    private List<XgwcShopBusinessVo> xgwcShopBusinessVos;

    /** 业务名称 */
    private String businessNames;
}
