package com.xgwc.order.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

@Data
public class ChatRecordVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌id")
    private Long brandId;

    @FieldDesc("聊天记录")
    private String chatContent;

    @FieldDesc("分类")
    @NotNull(message = "分类不能为空")
    private String classify;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("来源id")
    @NotNull(message = "来源id不能为空")
    private String sourceId;

    @FieldDesc("聊天标题")
    @NotNull(message = "聊天标题不能为空")
    private String title;



}
