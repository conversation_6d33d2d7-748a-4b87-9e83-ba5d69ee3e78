package com.xgwc.order.service.impl;

import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.XgwcPlatformTradeMapper;
import com.xgwc.order.dao.XgwcShopMapper;
import com.xgwc.order.entity.XgwcPlatformTrade;
import com.xgwc.order.entity.XgwcShop;
import com.xgwc.order.entity.dto.PlatformOrderDto;
import com.xgwc.order.util.OrderUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;


@Service
@Slf4j
public class OrderQueryService {
    @Resource
    private XgwcShopMapper xgwcShopMapper;
    @Resource
    private XgwcPlatformTradeMapper xgwcPlatformTradeMapper;
    @Resource
    private XgwcPlatformServiceImpl xgwcPlatformServiceImpl;


    public PlatformOrderDto getByPlatformTreadNo(String tradeNo) {
        List<Long> brandOwnerIdList = this.getBrandOwnerIdList();
        XgwcPlatformTrade platformTrade = xgwcPlatformTradeMapper.getByTradeNo(tradeNo, brandOwnerIdList);
        if (platformTrade == null) {
            platformTrade = xgwcPlatformServiceImpl.pullByTradeNo(tradeNo, brandOwnerIdList);
            if (platformTrade == null) {
                throw new ApiException("您输入的平台订单编号不存在,请核对后重新输入");
            }
        }
        PlatformOrderDto platformOrderDto = new PlatformOrderDto();
        platformOrderDto.setOrderNo(tradeNo);
        platformOrderDto.setOrderDate(DateUtils.formatDate(platformTrade.getPayTime()));
        XgwcShop shop = xgwcShopMapper.getByPlatformShopId(platformTrade.getPlatformShopId(), brandOwnerIdList);
        if (shop != null) {
            platformOrderDto.setStoreId(shop.getShopId());
        }
        platformOrderDto.setPayChannel(OrderUtils.platformIdToPayChannel(platformTrade.getPlatformId()));
        platformOrderDto.setAmount(platformTrade.getRealAmount());
        platformOrderDto.setOrderAmount(platformTrade.getOrderAmount());
        platformOrderDto.setRemark(platformTrade.getRemark());
        return platformOrderDto;
    }

    private List<Long> getBrandOwnerIdList() {
        String brandIds = SecurityUtils.getSysUser().getBrandIds();
        if (StringUtils.isBlank(brandIds)) {
            throw new ApiException("尚未加盟到任何品牌商, 无法核对平台订单编号");
        }
        return Arrays.stream(brandIds.split(",")).map(Long::valueOf).distinct().toList();
    }

}
