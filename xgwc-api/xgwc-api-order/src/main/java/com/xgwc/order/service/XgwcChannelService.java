package com.xgwc.order.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcChannelDto;
import com.xgwc.order.entity.param.XgwcChannelParam;
import com.xgwc.order.entity.vo.XgwcChannelVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhu<PERSON>
 * @CreateTime: 2025-04-22  18:15
 */
public interface XgwcChannelService {

    /**
     * 获取渠道列表
     * @param xgwcChannelParam 查询条件
     * @return 渠道列表
     */
    List<XgwcChannelVo> getXgwcChannelList(XgwcChannelParam xgwcChannelParam);

    /**
     * 保存渠道
     * @param xgwcChannelDto 渠道信息
     * @return 保存结果
     */
    ApiResult saveXgwcChannel(XgwcChannelDto xgwcChannelDto);

    /**
     * 获取渠道详情
     * @param channelId 渠道id
     * @return 渠道详情
     */
    ApiResult getXgwcChannelById(Integer channelId);

    /**
     * 更新渠道
     * @param xgwcChannelDto 渠道信息
     * @return 更新结果
     */
    ApiResult updateXgwcChannel(XgwcChannelDto xgwcChannelDto);

    /**
     * 更新渠道状态
     * @param channelId 渠道id
     * @param status 状态
     * @return 更新结果
     */
    ApiResult updateStatusById(Integer channelId, Integer status);
}
