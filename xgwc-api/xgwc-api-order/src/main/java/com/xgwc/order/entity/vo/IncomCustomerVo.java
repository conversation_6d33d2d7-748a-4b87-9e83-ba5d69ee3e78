package com.xgwc.order.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

@Data
public class IncomCustomerVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("客户id")
    @NotNull(message = "客户id不能为空")
    private String taobaoId;

    @FieldDesc("联系电话")
    private String tel;

    @FieldDesc("省")
    private String province;

    @FieldDesc("市")
    private String city;

    @FieldDesc("区")
    private String area;

    private String region;

    @FieldDesc("详细地址")
    private String address;

}
