package com.xgwc.order.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderInvoiceApply {

    /** 主键 */
    private Long id;

    /** 订单编号 */
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    /** 开票企业 */
    @NotNull(message = "开票企业不能为空")
    private String invoicingCompany;

    /** 加盟商id */
    private Long franchiseId;

    /** 抬头类型：1企业，2个人 */
    @NotNull(message = "抬头类型不能为空")
    private Integer titleType;

    /** 发票类型：1.电子普票，2.纸质普票，3.电子专票，4.纸质专票 */
    @NotNull(message = "发票类型不能为空")
    private Integer invoiceType;

    /** 购方企业名称 */
    @NotNull(message = "购方名称不能为空")
    private String buyer;

    /** 购方税号 */
    @NotNull(message = "购方税号不能为空")
    private String buyerTax;

    /** 企业注册地址 */
    @NotNull(message = "注册地址不能为空")
    private String registerAddress;

    /** 企业注册电话 */
    @NotNull(message = "注册手机号不能为空")
    private String registerMobile;

    /** 开户行 */
    @NotNull(message = "开户行不能为空")
    private String bank;

    /** 对公账户 */
    //@NotNull(message = "对公账户不能为空")
    private String corporateAccount;

    /** 开票金额 */
    @NotNull(message = "开票金额不能为空")
    private BigDecimal invoiceAmount;

    /** 开票内容 */
    private String remark;

    /** 专票费用 */
    private Integer specialInvoiceFee;

    /** 快递费用 */
    private Integer courierFee;

    /** 附件 */
    @NotNull(message = "付款截图不能为空")
    private String attachment;

    /** 接收人 */
    //@NotNull(message = "收件人不能为空")
    private String receiveUser;

    /** 接收人手机号 */
    //@NotNull(message = "收件人电话不能为空")
    private String receiveMobile;

    /** 省_code */
    private String receiveProCode;

    /** 市_code */
    private String receiveCityCode;

    /** 区_code */
    private String receiveZoneCode;

    /** 省名称 */
    //@NotNull(message = "省不能为空")
    private String receiveProName;

    /** 市名称 */
    //@NotNull(message = "市不能为空")
    private String receiveCityName;

    /** 区名称 */
    //@NotNull(message = "区不能为空")
    private String receiveZoneName;

    /** 详细地址 */
    //@NotNull(message = "详细地址不能为空")
    private String detailsAddress;

    /**
     * 实收金额
     */
    private BigDecimal actualAmount;
    /**
     * 客户ID
     */
    private String customerNo;

    /** 申请人用户id */
    private Long applyUserId;

    /** 申请人姓名 */
    private String applyUserName;

    /** 状态：0正常1非正常 */
    private Long status;

    /** 品牌商id：用于存储订单对应的品牌商 */
    private Long brandId;

    /** 以审批流定义的为准 */
    private String applyStatus;

    /** 最后审批时间 */
    private String lastApproveTime;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /** 流程实例id */
    private Long executionId;
}
