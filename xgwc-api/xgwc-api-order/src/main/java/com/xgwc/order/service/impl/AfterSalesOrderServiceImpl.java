package com.xgwc.order.service.impl;

import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.AfterSalesOrderMapper;
import com.xgwc.order.entity.dto.AfterSalesOrderDto;
import com.xgwc.order.entity.vo.AfterSalesOrderQueryVo;
import com.xgwc.order.service.IAfterSalesOrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class AfterSalesOrderServiceImpl implements IAfterSalesOrderService {

    @Resource
    private AfterSalesOrderMapper afterSalesOrderMapper;

    @Override
    public List<AfterSalesOrderDto> list(AfterSalesOrderQueryVo queryVo) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null) return List.of();
        queryVo.setBrandId(brandId);
        List<AfterSalesOrderDto> list = afterSalesOrderMapper.list(queryVo);
        return list;
    }

    @Override
    public HashMap<String, Integer> count() {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser.getBrandId() == null) return new HashMap<>();
        HashMap<String, Integer> map = new HashMap<>();
        map.put("all", afterSalesOrderMapper.count(sysUser.getBrandId()));
        map.put("willRefundCount", afterSalesOrderMapper.countWillRefund(sysUser.getBrandId()));
        map.put("timeoutUnreceivedCount", afterSalesOrderMapper.countTimeoutUnreceived(sysUser.getBrandId()));
        map.put("receivedCount", afterSalesOrderMapper.countReceived(sysUser.getUserId()));
        return map;
    }
}
