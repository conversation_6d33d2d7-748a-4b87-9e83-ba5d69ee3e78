package com.xgwc.order.controller;

import com.xgwc.common.annotation.Submit;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.SeasRuleDto;
import com.xgwc.order.service.SeasRuleService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/seas_rule")
@RestController
public class SeasRuleController {

    @Resource
    private SeasRuleService seasRuleService;

    /**
     * 保存公海配置
     */
    @Submit(fileds = "userId", expire = 1)
    @PostMapping("/save_rule")
    @PreAuthorize("@ss.hasPermission('incomLine:seas:rule')")
    public ApiResult saveSeasRule(@RequestBody @Valid SeasRuleDto seasRuleDto){
        if(seasRuleDto.getDayLimitOpen() !=null && seasRuleDto.getDayLimitOpen()>0 && seasRuleDto.getDayLimit() == null){
            return ApiResult.error("开启每日领取限制,需要填写限制值");
        }
        if(seasRuleDto.getIntervalEnabled() !=null && seasRuleDto.getIntervalEnabled()>0 && seasRuleDto.getIntervalMinuter() == null){
            return ApiResult.error("开启连续领取限制,需要填写限制值");
        }
        seasRuleDto.setStaffReceive(seasRuleDto.getStaffReceive() == null ? 1 : seasRuleDto.getStaffReceive());
        seasRuleDto.setDeptReceive(seasRuleDto.getDeptReceive() == null ? 1 : seasRuleDto.getDeptReceive());
        seasRuleDto.setDayLimitOpen(seasRuleDto.getDayLimitOpen() == null ? 1 : seasRuleDto.getDayLimitOpen());
        seasRuleDto.setIntervalEnabled(seasRuleDto.getIntervalEnabled() == null ? 1 : seasRuleDto.getIntervalEnabled());
        seasRuleService.saveSeasRule(seasRuleDto);
        return ApiResult.ok();
    }

    @RequestMapping("get_seasrule")
    public ApiResult getSeasRule(){
        SeasRuleDto seasRuleDto = seasRuleService.getSeasRuleDto();
        return ApiResult.ok(seasRuleDto);
    }

}
