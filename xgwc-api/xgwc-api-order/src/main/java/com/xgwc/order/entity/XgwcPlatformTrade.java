package com.xgwc.order.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class XgwcPlatformTrade {

    private Long id;
    /**
     * 平台订单编号
     */
    private String tradeNo;
    /**
     * 订单金额
     */
    private BigDecimal orderAmount;
    /**
     * 买家备注
     */
    private String remark;
    /**
     * 商家实收金额
     */
    private BigDecimal realAmount;
    /**
     * 买家实付金额
     */
    private BigDecimal payAmount;
    /**
     * 退款状态： 0.无退款 1.申请退款 2.部分退款 3.全部退款
     */
    private String refundStatus;
    /**
     * 平台订单状态：30已支付 50已发货 70已完成 80已退款 90已关闭(付款前取消)
     */
    private String tradeStatus;
    /**
     * 平台id
     */
    private Long platformId;

    /**
     * 平台店铺id
     */
    private String platformShopId;

    /**
     * 下单时间
     */
    private Date tradeTime;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 品牌商id
     */
    private Long brandId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


    // 文档地址 https://open.wangdian.cn/Y/open/apidoc/doc?path=vip_api_trade_query_Y
    /*
     {
                "currency": "",
                "delivery_term": 1,
                "discount": "0.0",
                "end_time": "2025-07-10 18:19:13",
                "goods_amount": "0.0",
                "goods_count": "5.0",
                "goods_list": [
                    {
                        "end_time": "2025-07-10 18:19:13",
                        "goods_id": "674235965810",
                        "goods_name": "代做工程预算造价广联达套定额安装水电市政土建建模装饰算量计价",
                        "goods_no": "",
                        "modified": "2025-07-25 17:58:23",
                        "num": "5.0",
                        "oid": "4624262713356996707",
                        "platform_id": "1",
                        "refund_status": 5,
                        "remark": "",
                        "share_amount": "500.0",
                        "spec_id": "",
                        "spec_name": "",
                        "spec_no": "",
                        "status": "80",
                        "tid": "4624262713356996707"
                    }
                ],
                "guarantee_mode": 1,
                "modified": "2025-07-25 17:58:23",
                "paid": "0.0",
                "pay_method": "1",
                "pay_status": 0,
                "pay_time": "2025-07-10 17:54:21",
                "platform_id": 1,
                "post_amount": "0.0",
                "receivable": "0.0",
                "received": "0.0",
                "receiver_address": "浔*镇**路美伦山景叠苑**号**单元",
                "receiver_city": "350500",
                "receiver_district": "350526",
                "receiver_mobile": "***********",
                "receiver_name": "小**",
                "receiver_province": "350000",
                "refund_status": "3",
                "remark": "糯米",
                "remark_flag": 1,
                "shop_id": 2,
                "tid": "4624262713356996707",
                "trade_status": 80
            }
     */

    public static XgwcPlatformTrade initByJson(JSONObject jsonObject) {
        XgwcPlatformTrade platformTrade = new XgwcPlatformTrade();
        platformTrade.tradeNo = jsonObject.getString("tid");
        platformTrade.realAmount = jsonObject.getBigDecimal("received");
        platformTrade.orderAmount = jsonObject.getBigDecimal("receivable");
        platformTrade.payAmount = jsonObject.getBigDecimal("paid");
        platformTrade.platformId = jsonObject.getLong("platform_id");
        platformTrade.platformShopId = jsonObject.getString("shop_id");
        platformTrade.remark = jsonObject.getString("buyer_message");
        platformTrade.tradeTime = jsonObject.getDate("trade_time");
        platformTrade.payTime = jsonObject.getDate("pay_time");
        platformTrade.tradeStatus = jsonObject.getString("trade_status");
        platformTrade.refundStatus = jsonObject.getString("refund_status");
        platformTrade.createTime = new Date();
        platformTrade.updateTime = jsonObject.getDate("modified");
        return platformTrade;
    }

    public boolean isValidTrade() {
        return this.payTime != null;
    }


    public Integer convertPayChannel() {
        // 1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫
        return this.platformId == 39 ? 5 : 1;
    }


}