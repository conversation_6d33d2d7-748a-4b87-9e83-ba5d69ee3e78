package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.dto.IncomLineTotalDto;
import com.xgwc.order.entity.vo.IncomLineTransferVo;
import com.xgwc.order.entity.vo.IncomLineVo;
import com.xgwc.order.entity.dto.IncomLineDto;
import com.xgwc.order.entity.vo.IncomLineQueryVo;

public interface IIncomLineService  {
    /**
     * 查询进线管理
     * 
     * @param id 进线管理主键
     * @return 进线管理
     */
    public IncomLineDto selectIncomLineById(Long id);

    /**
     * 查询进线管理列表
     * 
     * @param incomLine 进线管理
     * @return 进线管理集合
     */
    public List<IncomLineDto> selectIncomLineList(IncomLineQueryVo incomLine);

    IncomLineTotalDto selectIncomLineTotal(IncomLineQueryVo incomLine);

    /**
     * 新增进线管理
     * 
     * @param incomLine 进线管理
     * @return 结果
     */
    public int insertIncomLine(IncomLineVo incomLine);

    /**
     * 修改进线管理
     * 
     * @param incomLine 进线管理
     * @return 结果
     */
    public int updateIncomLine(IncomLineVo incomLine);


    public int transferIncomLine(IncomLineTransferVo transferVo);
    /**
     * 批量删除进线管理
     * 
     * @param ids 需要删除的进线管理主键集合
     * @return 结果
     */
    public int deleteIncomLineByIds(Long[] ids);

    /**
     * 删除进线管理信息
     * 
     * @param id 进线管理主键
     * @return 结果
     */
    public int deleteIncomLineById(Long id);
}
