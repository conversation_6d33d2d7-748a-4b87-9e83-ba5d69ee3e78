package com.xgwc.order.service;

import java.util.List;
import java.util.Map;

import com.xgwc.order.entity.dto.DispatchPageDto;
import com.xgwc.order.entity.vo.DispatchVo;
import com.xgwc.order.entity.dto.DispatchDto;
import com.xgwc.order.entity.vo.DispatchQueryVo;

public interface IDispatchService  {
    /**
     * 查询派单工作台
     * 
     * @param id 派单工作台主键
     * @return 派单工作台
     */
    public DispatchDto selectDispatchById(Long id);

    /**
     * 查询派单工作台列表
     * 
     * @param dispatch 派单工作台
     * @return 派单工作台集合
     */
    public List<DispatchPageDto> selectDispatchList(DispatchQueryVo dispatch);

    /**
     * 新增派单工作台
     * 
     * @param dispatch 派单工作台
     * @return 结果
     */
    public int insertDispatch(DispatchVo dispatch);

    /**
     * 修改派单工作台
     * 
     * @param dispatch 派单工作台
     * @return 结果
     */
    public int updateDispatch(DispatchVo dispatch);

    /**
     * 批量删除派单工作台
     * 
     * @param ids 需要删除的派单工作台主键集合
     * @return 结果
     */
    public int deleteDispatchByIds(Long[] ids);

    /**
     * 删除派单工作台信息
     * 
     * @param id 派单工作台主键
     * @return 结果
     */
    public int deleteDispatchById(Long id);

    Map<String, Integer> statistics();

    /**
     * 根据订单号查询派单信息
     * @param orderId 订单号
     * @return 派单信息
     */
    DispatchDto selectDispatchByOrderId(Long orderId);
}
