package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AlreadyDispatchVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("订单id")
    @NotNull(message = "订单id必填")
    private Long id;

    @FieldDesc("待派单Id")
    @NotNull(message = "待派单Id必填")
    private Long agencyDispatchId;

    @FieldDesc("谈单人员")
    @NotNull(message = "谈单人员必填")
    private Long saleManId;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "下单日期必填")
    private Date orderDate;

    @FieldDesc("订单编号")
    @NotNull(message = "下单日期必填")
    private String orderNo;

    @FieldDesc("母订单id")
    private Long pid;

    @FieldDesc("设计师ID")
    private Long designerId;

    @FieldDesc("派单备注")
    private String dispatchRemark;

    @FieldDesc("设计师信息")
    private List<OrderDesignerVo> orderDesigners;

}
