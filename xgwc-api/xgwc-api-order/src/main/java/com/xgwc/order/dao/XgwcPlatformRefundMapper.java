package com.xgwc.order.dao;

import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.dto.XgwcPlatformRefundDto;
import com.xgwc.order.entity.vo.XgwcPlatformRefundQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface XgwcPlatformRefundMapper {

    /**
     * 新增平台售后单
     *
     * @param xgwcPlatformRefundList 平台售后单
     * @return 结果
     */
    int insertList(@Param("xgwcPlatformRefundList") List<XgwcPlatformRefund> xgwcPlatformRefundList);

    void deleteByRefundNo(@Param("refundNoList") List<String> refundNoList, @Param("brandOwnerId") Long brandOwnerId);

    XgwcPlatformRefund getByRefundNo(@Param("refundNo") String refundNo, @Param("brandOwnerId") Long brandOwnerId);

    List<XgwcPlatformRefundDto> selectNotUseList(XgwcPlatformRefundQueryVo refundQueryVo);

    List<XgwcPlatformRefund> listByTradeNo(@Param("tradeNoList") List<String> tradeNoList, @Param("brandOwnerId") Long brandOwnerId);

    List<XgwcPlatformRefund> listByRefundNo(@Param("refundNoList") List<String> tradeNoList, @Param("brandOwnerId") Long brandOwnerId);

}