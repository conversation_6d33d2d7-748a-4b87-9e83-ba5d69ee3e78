package com.xgwc.order.entity.cmd;

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.order.entity.NightConfigRange;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class BrandNightPerformanceConfigUpdateCmd {

    @NotNull
    @FieldDesc("ID")
    private Long id;

    @Valid
    @NotNull
    @NotEmpty
    @FieldDesc("晚间时间段配置")
    private List<NightConfigRange> rangeList;

}
