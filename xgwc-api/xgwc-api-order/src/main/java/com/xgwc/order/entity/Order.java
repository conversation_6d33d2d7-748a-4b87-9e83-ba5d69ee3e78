package com.xgwc.order.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
public class Order implements Serializable {

    /** 订单id */
    private Long id;

    /** 谈单人员 */
    private Long saleManId;

    /** 订单归类: 1平台订单, 2线下订单 */
    private Integer orderCategory;

    /** 谈单人员名称 */
    private String saleManName;

    /** 录入部门编码 */
    private Long deptId;

    /** 录入部门名称 */
    private String deptName;

    /** 订单类型：0正常单，1转化单 */
    private Integer transferState;

    /** 下单日期 */
    private String orderDate;

    /** 订单来源ID（店铺id） */
    private Long storeId;

    /** 订单来源名称（店铺名称） */
    private String storeName;

    /** 品牌商id */
    private Long brandId;

    /** 加盟商id */
    private Long franchiseId;

    /** 支付方式  1:淘宝  2：微信  3：支付宝 */
    private Integer payChannel;

    /** 订单状态（0：未发货 1：完成  2：退款 3：部分退款） */
    private Integer shType;

    /** 订单编号 */
    private String orderNo;

    /** 客户ID */
    private String taobaoId;

    /** 订单金额 */
    private BigDecimal orderAmount;

    /** 实收金额 */
    private BigDecimal amount;

    /** 付款方式:1全款/2阶段付 */
    private Integer payType;

    /** 期望初稿日期：yyyy-MM-dd HH:00:00 */
    private Date archiveExpectTime;

    /** 派单类型 */
    private Integer allotType;

    /** 是否紧急：0否 1是 */
    private Integer allotUrgency;

    /** 派单需求 */
    private String allotRemark;

    /** 派单设计师数量 */
    private Long allotNum;

    /** 派单人id */
    private Long allotUserId;

    /** 派单人 */
    private String allotUserName;

    /** 协助文件 */
    private String allotFile;

    /** 母订单id */
    private Long pid;

    /** 设计师ID */
    private Long designerId;

    /** 设计师名称 */
    private String designerName;

    /** 设计师电话 */
    private String designerPhone;

    private String designerBusiness;

    /** 佣金金额 */
    private BigDecimal money;

    /** 约定初稿日期 */
    private Date archiveAppointTime;

    /** 提交定稿日期 */
    private Date archiveTime;

    /** 交稿状态：0未交稿 1交初稿 2交定稿 */
    private Integer archiveType;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    private Long stateDicCode;

    private String stateDicName;

    /**
     * 现实收金额， 退款之后
     */
    private BigDecimal nowAmount;

    /**
     * 现佣金，退款之后
     */
    private BigDecimal nowMoney;

    /**
     * 是否有售后：0无,1有
     */
    private Integer isAfterSale;

    /**
     * 成交时间
     */
    private String dealTime;

    /**
     * 是否结算：0未结算，1已结算
     */
    private Integer settlement;

    /**
     * 结算时间
     */
    private String settlementTime;

    /**
     * 是否归档:0 未归档，1归档
     */
    private Integer isArchiving;

    /**
     * 是否转介绍：0否 1是
     */
    private Integer isTransfer;

    /**
     * 是否回购：0否1是
     */
    private Integer isBuyback;
}