package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class IncomCustomerQueryVo {

    private static final long serialVersionUID=1L;


    @FieldDesc("客户ID")
    private String taobaoId;

    @FieldDesc("客户等级-字典")
    private String taobaoLv;

    @FieldDesc("联系电话")
    private String tel;

    @FieldDesc("添加微信0否 1是")
    private Long wechatTag;

    @FieldDesc("添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  addTime;

    @FieldDesc("多久未跟进-字典")
    private String lastTarck;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("进线id")
    private Long id;
}
