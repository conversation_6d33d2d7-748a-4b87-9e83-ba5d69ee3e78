package com.xgwc.order.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.order.entity.dto.XgwcCaseDto;
import com.xgwc.order.entity.es.CaseVo;
import com.xgwc.order.entity.vo.XgwcCaseQueryVo;
import com.xgwc.order.entity.vo.XgwcCaseUpStatusVo;
import com.xgwc.order.service.IXgwcCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 案例表Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("/xgwcCase")
public class XgwcCaseController extends BaseController {
    @Autowired
    private IXgwcCaseService xgwcCaseService;

    /**
     * 获取案例表详细信息
     */
    @MethodDesc("获取案例表详细信息")
    @PreAuthorize("@ss.hasPermission('xgwcCase:xgwcCase:query')")
    @GetMapping(value = "/{caseId}")
    public ApiResult<XgwcCaseDto> getInfo(@PathVariable("caseId") Long caseId) {
        return success(xgwcCaseService.selectXgwcCaseByCaseId(caseId));
    }

    @MethodDesc("案例列表")
    @PreAuthorize("@ss.hasPermission('xgwcCase:xgwcCase:list')")
    @GetMapping(value = "/list")
    public ApiResult<XgwcCaseDto> list(XgwcCaseQueryVo queryVo) {
        startPage();
        return getDataTable(xgwcCaseService.selectXgwcCaseList(queryVo));
    }

    @MethodDesc("停用/启用")
    @PreAuthorize("@ss.hasPermission('xgwcCase:xgwcCase:update')")
    @Log(title = "修改案例", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/update/status")
    public ApiResult upStatus(@RequestBody XgwcCaseUpStatusVo vo) {
        return success(xgwcCaseService.upStatus(vo));
    }

    @MethodDesc("删除案例")
    @PreAuthorize("@ss.hasPermission('xgwcCase:xgwcCase:remove')")
    @Log(title = "删除案例", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(xgwcCaseService.deleteByIds(ids));
    }

    /**
     * 下载案例
     *
     * @param caseId 案例表主键
     * @param isFlag 0-品牌商，1-加盟商
     */
    @MethodDesc("下载案例")
    @PreAuthorize("@ss.hasPermission('xgwcCase:xgwcCase:download')")
    @GetMapping("/downloadCase")
    public ApiResult downloadCase(@RequestParam("caseId") Long caseId,
                                  @RequestParam(value = "isFlag") Integer isFlag,
                                  @RequestParam(value = "brandId") Long brandId) {
        return xgwcCaseService.downloadCase(caseId, isFlag, brandId);
    }

    /**
     *  查询品牌商案例库最大下载限制/已下载次数
     *
     *  @param isFlag 0-品牌商，1-加盟商
     */
    @MethodDesc("查询案例库最大下载限制/已下载次数")
    @GetMapping("/getMaxDownloadLimit")
    public ApiResult getMaxDownloadLimit(@RequestParam(value = "isFlag") Integer isFlag){
        return xgwcCaseService.getMaxDownloadLimit(null, isFlag);
    }

    /**
     * 大搜索案例库
     */
    @MethodDesc("大搜索案例库")
    @PreAuthorize("@ss.hasPermission('xgwcCase:xgwcCase:search')")
    @GetMapping(value = "/search")
    public ApiResult search(CaseVo queryVo) {
        return ApiResult.ok(xgwcCaseService.search(queryVo));
    }
}

