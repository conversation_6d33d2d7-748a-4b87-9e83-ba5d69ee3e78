package com.xgwc.order.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.OrderStatusEnums;
import com.xgwc.order.entity.OrderTrace;
import com.xgwc.order.entity.dto.OrderArchiveDto;
import com.xgwc.order.entity.dto.OrderCompanyDto;
import com.xgwc.order.entity.dto.OrderDto;
import com.xgwc.order.entity.dto.OrderPlanDto;
import com.xgwc.order.entity.dto.SubOrderDto;
import com.xgwc.order.entity.vo.OrderArchiveVo;
import com.xgwc.order.entity.vo.OrderDesignerUpdateVo;
import com.xgwc.order.entity.vo.OrderDesignerVo;
import com.xgwc.order.entity.vo.OrderHistoryQueryVo;
import com.xgwc.order.entity.vo.OrderPlanVo;
import com.xgwc.order.entity.vo.OrderQueryVo;
import com.xgwc.order.entity.vo.OrderVo;

import java.util.List;

public interface IOrderService {

    /**
     * 查询订单管理
     * 
     * @param id 订单管理主键
     * @return 订单管理
     */
    public OrderDto selectOrderById(Long id);
    public List<OrderTrace> selectOrderTraceById(Long id);

    /**
     * 查询订单管理列表
     * 
     * @param order 订单管理
     * @return 订单管理集合
     */
    public List<OrderDto> selectOrderList(OrderQueryVo order);

    /**
     * 新增订单管理
     * 
     * @param order 订单管理
     * @return 结果
     */
    public int insertOrder(OrderVo order);

    /**
     * 保存设计师
     * @param order
     * @return
     */
    public int saveSubOrder(OrderDesignerVo order);

    /**
     * 修改订单管理
     * 
     * @param order 订单管理
     * @return 结果
     */
    public int updateOrder(OrderVo order);

    /**
     * 批量删除订单管理
     * 
     * @param ids 需要删除的订单管理主键集合
     * @return 结果
     */
    public int deleteOrderByIds(Long[] ids);

    /**
     * 删除订单管理信息
     * 
     * @param id 订单管理主键
     * @return 结果
     */
    public int deleteOrderById(Long id);


    /**
     * 保存订单进度
     * @param plans
     * @return
     */
    public int savePlan( List<OrderPlanVo> plans);

    /**
     * 通过订单id查询进度
     * @param orderId
     * @return
     */
    public List<OrderPlanDto> selectOrderPlanByOrderId(Long orderId);

    /**
     * 通过订单id查询交稿信息
     * @param orderId
     * @return
     */
    public OrderArchiveDto selectOrderArchiveByOrderId(Long orderId);

    /**
     * 交稿审批
     * @param archiveVo
     * @return
     */
    public int archiveReview(OrderArchiveVo archiveVo);


    /**
     * 设计师接单
     *
     * @param orderNo 订单编号
     * @return
     */
    public ApiResult designerOrders(String orderNo);

    /**
     * 更换设计师
     * @param vo
     * @return
     */
    public ApiResult designerUpdate(OrderDesignerUpdateVo vo);

    /**
     * 查询历史订单列表
     * @param order 查询条件
     * @return 订单列表
     */
    List<OrderDto> selectHistoryOrderList(OrderHistoryQueryVo order);

    /*
     * 根据订单号查询订单信息 包括公司主体信息
     */
    OrderCompanyDto selectOrderCompanyByOrderNo(String orderNo);

    /**
     * 更新订单状态(交稿，售后，归档)
     * @param orderId 订单ID
     * @param orderStatusEnums 状态
     * @return 是否成功
     */
    int updateOrderStatus(Long orderId, OrderStatusEnums orderStatusEnums);

    /**
     * 订单是否已确认
     * @param orderId 订单ID
     * @return 是否确认
     */
    boolean isTheOrderConfirmed(Long orderId);

    /**
     * 订单是否已出账
     * @param orderId 订单ID
     * @return 是否确认
     */
    boolean isTheOrderBilled(Long orderId);

    /**
     * 批量修改退款状态
     * @param orderIds 订单ID集合
     * @param refundStatus 退款状态：0 未退款，1 部分退款，2 全额退款
     * @return 是否成功
     */
    int batchUpdateRefundStatus(List<Long> orderIds, Integer refundStatus);

    /**
     * 根据pid查询子订单列表
     * @param pid 订单ID
     * @return 子订单列表
     */
    List<SubOrderDto> selectSubOrderListByPId(Long pid);

}
