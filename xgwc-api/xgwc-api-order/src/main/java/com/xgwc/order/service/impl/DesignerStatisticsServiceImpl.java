package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.DesignerStatisticsMapper;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.dao.OrderScoreMapper;
import com.xgwc.order.entity.DesignerTarck;
import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.DesignerStatisticsQueryVo;
import com.xgwc.order.entity.vo.DesignerStatisticsScoreQueryVo;
import com.xgwc.order.entity.vo.DesignerTarckVo;
import com.xgwc.order.entity.vo.OrderQueryVo;
import com.xgwc.order.service.IDesignerStatisticsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DesignerStatisticsServiceImpl implements IDesignerStatisticsService {

    @Resource
    private DesignerStatisticsMapper designerStatisticsMapper;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderScoreMapper orderScoreMapper;

    @Override
    public List<DesignerStatisticsDto> list(DesignerStatisticsQueryVo queryVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(1 == sysUser.getUserType() || 4 == sysUser.getUserType()){
            queryVo.setBrandId(sysUser.getBrandId());
        }
        if(queryVo.getLastAcceptTime() != null){
            switch (queryVo.getLastAcceptTime()) {
                case "a_year":
                    queryVo.setOrderLastTime(DateUtils.addYear(-1));
                    break;
                case "half_year":
                    queryVo.setOrderLastTime(DateUtils.addMonth(-6));
                    break;
                case "three_months":
                    queryVo.setOrderLastTime(DateUtils.addMonth(-3));
                    break;
                case "one_month":
                    queryVo.setOrderLastTime(DateUtils.addMonth(-1));
                    break;
                case "half_month":
                    queryVo.setOrderLastTime(DateUtils.addDays(-15));
                    break;
                case "a_week":
                    queryVo.setOrderLastTime(DateUtils.addWeek(-1));
                    break;
            }
        }

        List<DesignerStatisticsDto> list = designerStatisticsMapper.selectDesignerStatistics(queryVo);
        list.forEach(l -> {
            if(StringUtils.isNotEmpty(l.getDesignerPhone()))
                l.setDesignerPhone(ParamDecryptUtil.decryptPhone(l.getDesignerPhone()));
        });
        return list;
    }

    @Override
    public DesignerStatisticsDto selectById(Long designerId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        DesignerStatisticsDto dto = designerStatisticsMapper.selectDesignerStatisticsById(designerId);
        if(dto != null){
            dto.setScores(designerStatisticsMapper.selectDesignerStatisticsScore(designerId,brandId));
            dto.setYearScores(designerStatisticsMapper.selectDesignerStatisticsYearScore(designerId,brandId));
            dto.setDesignerPhone(ParamDecryptUtil.decryptPhone(dto.getDesignerPhone()));
            dto.setEmergencyPhone(ParamDecryptUtil.decryptPhone(dto.getEmergencyPhone()));
        }
        return dto;
    }

    @Override
    public List<DesignerTarckDto> tarckList(Long designerId) {
        return designerStatisticsMapper.selectDesignerTarck(designerId);
    }

    @Override
    public int tarckAdd(DesignerTarckVo designerTarckVo) {
        DesignerTarck designerTarck = BeanUtil.copyProperties(designerTarckVo, DesignerTarck.class);
        designerTarck.setCreateBy(SecurityUtils.getNickName());
        designerTarck.setCreateTime(new Date());
        designerStatisticsMapper.updateTarck(designerTarck);
        return designerStatisticsMapper.insertTarck(designerTarck);
    }

    @Override
    public List<OrderDto> noArchive(Long designerId) {
        OrderQueryVo orderQueryVo = new OrderQueryVo();
        orderQueryVo.setDesignerId(designerId);
        orderQueryVo.setArchiveType(1);
        SysUser sysUser = SecurityUtils.getSysUser();
        if(1 == sysUser.getUserType() || 4 == sysUser.getUserType()){
            orderQueryVo.setBrandId(sysUser.getBrandId());
        }
        List<OrderDto> list = orderMapper.selectOrderList(orderQueryVo);
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(list != null && !list.isEmpty()){
            list.forEach(order -> {
                if(order.getBrandId() != brandId){
                    order.encryption();
                }
            });
        }
        return list;
    }

    @Override
    public List<OrderDto> refund(Long designerId) {
        OrderQueryVo orderQueryVo = new OrderQueryVo();
        orderQueryVo.setDesignerId(designerId);
        orderQueryVo.setRefundStatuss(Arrays.asList(1L, 2L));
        SysUser sysUser = SecurityUtils.getSysUser();
        if(1 == sysUser.getUserType() || 4 == sysUser.getUserType()){
            orderQueryVo.setBrandId(sysUser.getBrandId());
        }
        List<OrderDto> list = orderMapper.selectOrderList(orderQueryVo);
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(list != null && !list.isEmpty()){
            list.forEach(order -> {
                if(order.getBrandId() != brandId){
                    order.encryption();
                }
            });
        }
        return list;
    }

    @Override
    public DesignerStatisticsScoreAllDto scoreAll(DesignerStatisticsScoreQueryVo queryVo) {
        return designerStatisticsMapper.selectDesignerStatisticsScoreAll(queryVo.getDesignerId(), queryVo.getScoreType());
    }

    @Override
    public List<DesignerStatisticsOrderDto> scoreList(DesignerStatisticsScoreQueryVo queryVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(1 == sysUser.getUserType() || 4 == sysUser.getUserType()){
            queryVo.setBrandId(sysUser.getBrandId());
        }
        List<DesignerStatisticsOrderDto> orders = designerStatisticsMapper.selectOrder(queryVo);
        if(orders != null && !orders.isEmpty()){
            List<Long> orderIds = orders.stream().mapToLong(DesignerStatisticsOrderDto::getId).boxed().collect(Collectors.toList());
            List<OrderScoreDto> scores = orderScoreMapper.selectOrderScoreByOrderIds(orderIds);
            Long brandId = SecurityUtils.getSysUser().getBrandId();
            orders.stream().forEach(order -> {
                if(order.getBrandId() != brandId){
                    order.setOrderNo("****");
                }
                List<OrderScoreDto> list = scores.stream().filter(s-> s.getOderId().equals(order.getId())).peek(s -> {
                    if(order.getBrandId() != brandId){
                        s.setCreateBy("****");
                    }
                }).collect(Collectors.toList());
                if(!list.isEmpty()){
                    order.setScoreList(list);
                }
            });
        }
        return orders;
    }
}
