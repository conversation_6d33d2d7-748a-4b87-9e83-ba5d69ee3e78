package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderPayDto {

    @FieldDesc("支付方式编号")
    @Excel(name = "支付方式编号")
    private Long id;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long oderId;

    @FieldDesc("付款方式:1全款/2定价/3过程款/4尾款")
    @Excel(name = "付款方式:1全款/2定价/3过程款/4尾款")
    private Integer payType;

    @FieldDesc("付款截图")
    @Excel(name = "付款截图")
    private String payImg;

    @FieldDesc("实收金额")
    @Excel(name = "实收金额")
    private BigDecimal amount;

    /**
     * 发生退款后有值
     */
    @FieldDesc("当前金额")
    @Excel(name = "当前金额")
    private BigDecimal nowAmount;

    @FieldDesc("支付方式  1:淘宝  2：微信  3：支付宝")
    @Excel(name = "支付方式  1:淘宝  2：微信  3：支付宝")
    private Integer payChannel;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("收款编号")
    @Excel(name = "收款编号")
    private String collectionNo;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    // 可退款金额
    public BigDecimal getRefundableAmount() {
        return this.nowAmount != null ? this.nowAmount : this.amount;
    }

    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("oderId",getOderId())
            .append("payType",getPayType())
            .append("payImg",getPayImg())
            .append("amount",getAmount())
            .append("payChannel",getPayChannel())
            .append("remark",getRemark())
            .append("collectionNo",getCollectionNo())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
