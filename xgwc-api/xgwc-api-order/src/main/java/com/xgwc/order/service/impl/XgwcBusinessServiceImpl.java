package com.xgwc.order.service.impl;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.XgwcBusinessMapper;
import com.xgwc.order.entity.dto.BusinessSettingDto;
import com.xgwc.order.entity.dto.XgwcBusinessDto;
import com.xgwc.order.entity.param.XgwcBusinessParam;
import com.xgwc.order.entity.vo.XgwcBusinessInfo;
import com.xgwc.order.entity.vo.XgwcBusinessTreeVo;
import com.xgwc.order.entity.vo.XgwcBusinessVo;
import com.xgwc.order.service.XgwcBusinessService;
import com.xgwc.redis.constants.LockCacheKey;
import com.xgwc.redis.util.RedisUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  09:34
 */
@Service
@Slf4j
public class XgwcBusinessServiceImpl implements XgwcBusinessService {

    @Resource
    private XgwcBusinessMapper xgwcBusinessMapper;

    @Resource
    private RedisUtil redisUtil;

    public static final Long PARENT_ID = 0L;


    /**
     * 获取业务列表
     *
     * @param xgwcBusinessParam 业务参数
     * @return 业务列表
     */
    @Override
    public List<XgwcBusinessInfo> getXgwcBusinessList(XgwcBusinessParam xgwcBusinessParam) {
        // 获取所有业务信息
        Long brandOwnerId = xgwcBusinessParam.getBrandOwnerId();
        xgwcBusinessParam.setBrandOwnerId(
                brandOwnerId != null
                        ? brandOwnerId
                        : SecurityUtils.getSysUser().getBrandId()
        );
        List<XgwcBusinessVo> allBusinesses = xgwcBusinessMapper.getXgwcBusinessList(xgwcBusinessParam);
        if (CollectionUtils.isEmpty(allBusinesses)) {
            return null;
        }
        if (allBusinesses.stream().anyMatch(business -> Objects.isNull(business.getPid()))) {
            log.error("获取业务信息树失败，参数不完整缺少pid");
            return null;
        }
        if ((StringUtils.isNotEmpty(xgwcBusinessParam.getBusinessName())
                || xgwcBusinessParam.getStatus() != null)
                && xgwcBusinessParam.getIsFlag() == 0) {

            return allBusinesses.stream()
                    .map(xgwcBusinessVo -> {
                        XgwcBusinessInfo childInfo = new XgwcBusinessInfo();
                        BeanUtils.copyProperties(xgwcBusinessVo, childInfo);
                        childInfo.setLevelNum(1);
                        return childInfo;
                    })
                    .collect(Collectors.toList());
        }

        // 构建ID到业务对象的映射，便于快速查找
        Map<Long, XgwcBusinessVo> businessMap = allBusinesses.stream()
                .collect(Collectors.toMap(XgwcBusinessVo::getBusinessId, Function.identity()));

        // 按父ID分组
        Map<Long, List<XgwcBusinessVo>> pidToChildren = allBusinesses.stream()
                .collect(Collectors.groupingBy(XgwcBusinessVo::getPid));

        // 构建树形结构，从顶级节点开始，层级为1
        return allBusinesses.stream()
                .filter(item -> PARENT_ID.equals(item.getPid())) // 筛选顶级节点
                .map(item -> buildBusinessTree(item, businessMap, pidToChildren, 1)) // 初始层级为1
                .collect(Collectors.toList());
    }

    private XgwcBusinessInfo buildBusinessTree(XgwcBusinessVo source,
                                               Map<Long, XgwcBusinessVo> businessMap,
                                               Map<Long, List<XgwcBusinessVo>> pidToChildren,
                                               int level) {
        XgwcBusinessInfo target = new XgwcBusinessInfo();
        BeanUtils.copyProperties(source, target);
        target.setLevelNum(level); // 设置当前节点的层级

        // 递归构建子树，层级+1
        List<XgwcBusinessVo> children = pidToChildren.getOrDefault(source.getBusinessId(), Collections.emptyList());
        target.setChiledrenList(children.stream()
                .map(child -> buildBusinessTree(child, businessMap, pidToChildren, level + 1))
                .collect(Collectors.toList()));

        return target;
    }

    /**
     * 保存业务
     *
     * @param xgwcBusinessDto 业务信息
     * @return 保存结果
     */
    @Override
    public ApiResult saveXgwcBusiness(XgwcBusinessDto xgwcBusinessDto) {
        // 参数校验
        if (xgwcBusinessDto == null) {
            log.warn("业务保存失败：参数不能为空");
            return ApiResult.error("业务信息不能为空");
        }

        try {
            // 执行保存操作
            xgwcBusinessDto.setCreateBy(SecurityUtils.getNickName());
            xgwcBusinessDto.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());

            String pinyin = PinyinUtil.getPinyin(xgwcBusinessDto.getBusinessName(), "");

            String cacheKey = LockCacheKey.BUSINESS_SAVE
                    + xgwcBusinessDto.getBusinessName()
                    + "--" + SecurityUtils.getUserId();

            String lockValue = UUID.randomUUID().toString();

            try {
                boolean lockAcquired = redisUtil.tryLock(cacheKey, lockValue, 3000);
                if (!lockAcquired) {
                    log.warn("业务新增-获取分布式锁失败，业务名称={}", xgwcBusinessDto.getBusinessName());
                    return ApiResult.error("系统繁忙，请稍后再试");
                }
                if (xgwcBusinessDto.getPid() == 0) {
                    xgwcBusinessDto.setLevel(xgwcBusinessDto.getBrandOwnerId() + "_" + pinyin);
                } else {
                    xgwcBusinessDto.setLevel(getSequenceInParent(xgwcBusinessDto.getPid(),
                            xgwcBusinessDto.getBusinessId(),
                            false));
                }
            } finally {
                try {
                    redisUtil.unlock(cacheKey, lockValue);
                } catch (Exception unlockEx) {
                    log.error("释放分布式锁异常，cacheKey={}, 错误信息：{}",
                            cacheKey, unlockEx.getMessage(), unlockEx);
                }
            }
            int affectedRows = xgwcBusinessMapper.saveXgwcBusiness(xgwcBusinessDto);
            if (affectedRows <= 0) {
                log.error("业务保存失败，未影响任何行，业务名称={}", xgwcBusinessDto.getBusinessName());
                return ApiResult.error("业务保存失败");
            }

            log.info("业务保存成功，业务ID={}, 业务名称={}",
                    xgwcBusinessDto.getBusinessId(), xgwcBusinessDto.getBusinessName());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("业务保存异常，业务名称={}, 错误信息：{}",
                    xgwcBusinessDto.getBusinessName(), e.getMessage(), e);
            return ApiResult.error("系统异常，业务保存失败");
        }
    }

    private String getSequenceInParent(Long pid, Long businessId, boolean isUpdate) {

        if (isUpdate && businessId != null) {
            XgwcBusinessVo xgwcBusinessById = xgwcBusinessMapper.getXgwcBusinessById(businessId, null);
            Long pid1 = xgwcBusinessById.getPid();
            if (pid1 != null && pid1.equals(pid)) {
                return null; // 不需要修改level
            }
        }
        // 根据pid查询同级别数据
        List<XgwcBusinessVo> xgwcBusinessVos = xgwcBusinessMapper.selectXgwcBusinessById(pid);

        // 根据pid查询父级数据
        XgwcBusinessVo xgwcBusinessById = xgwcBusinessMapper.getXgwcBusinessById(null, pid);
        if (CollectionUtils.isEmpty(xgwcBusinessVos)) {
            return xgwcBusinessById.getLevel() + "_" + 1; // 如果列表为空，返回 "父级level_1"
        }

        int maxNum = -1; // 初始化最大值为 -1
        String prefix = ""; // 初始化前缀为空

        for (XgwcBusinessVo vo : xgwcBusinessVos) {
            String level = vo.getLevel();
            if (level == null || level.isEmpty()) continue;

            // 找到最后一个 '_' 的位置
            int lastDashIndex = level.lastIndexOf('_');
            if (lastDashIndex != -1 && lastDashIndex < level.length() - 1) {
                try {
                    // 提取最后一个 '_' 后面的数字部分
                    int num = Integer.parseInt(level.substring(lastDashIndex + 1));
                    if (num > maxNum) {
                        maxNum = num;
                        // 前缀是最后一个 '_' 之前的部分
                        prefix = level.substring(0, lastDashIndex);
                    }
                } catch (NumberFormatException e) {
                    // 忽略非数字部分
                }
            }
        }

        // 构造新的 level：前缀 + "_" + (maxNum + 1)
        if (prefix.isEmpty()) {
            return xgwcBusinessById.getLevel() + "_" + (maxNum + 1); // 如果没有前缀，直接返回 ”父级level_1“
        } else {
            return prefix + "_" + (maxNum + 1);
        }
    }

    /**
     * 根据业务ID获取业务信息
     *
     * @param businessId 业务ID
     * @return 业务信息
     */
    @Override
    public ApiResult getXgwcBusinessById(Long businessId) {
        // 参数校验
        if (businessId == null || businessId <= 0) {
            log.warn("查询业务失败：业务ID非法，ID={}", businessId);
            return ApiResult.error("业务ID不能为空或非法");
        }

        try {
            // 查询主业务数据
            XgwcBusinessVo xgwcBusinessDto = xgwcBusinessMapper.getXgwcBusinessById(businessId, null);
            if (xgwcBusinessDto == null) {
                log.warn("未找到主业务数据，业务ID={}", businessId);
                return ApiResult.error("未找到主业务数据");
            }

            // 查询父级业务数据（允许父级为空）
            XgwcBusinessVo xgwcBusinessParent = xgwcBusinessMapper
                    .getXgwcBusinessById(null, xgwcBusinessDto.getPid());
            if (xgwcBusinessParent != null) {
                xgwcBusinessDto.setParentBusinessName(xgwcBusinessParent.getBusinessName());
                xgwcBusinessDto.setParentBusinessId(xgwcBusinessParent.getBusinessId());
            } else {
                log.info("未找到父级业务数据，PID={}", xgwcBusinessDto.getPid());
            }

            log.info("查询业务成功，业务ID={}", businessId);
            return ApiResult.ok(xgwcBusinessDto);

        } catch (Exception e) {
            log.error("查询业务异常，业务ID={}, 错误信息：{}", businessId, e.getMessage(), e);
            return ApiResult.error("系统异常，查询业务失败");
        }
    }


    /**
     * 更新业务信息
     *
     * @param xgwcBusinessDto 业务信息
     * @return 更新结果
     */
    @Override
    public ApiResult updateXgwcBusinessById(XgwcBusinessDto xgwcBusinessDto) {
        // 参数校验
        if (xgwcBusinessDto == null || xgwcBusinessDto.getBusinessId() == null) {
            log.warn("更新业务失败：参数或业务ID为空");
            return ApiResult.error("业务信息不完整");
        }

        try {
            String cacheKey = LockCacheKey.BUSINESS_UPDATE
                    + xgwcBusinessDto.getBusinessName()
                    + "--" + SecurityUtils.getUserId();

            String lockValue = UUID.randomUUID().toString();

            String pinyin = PinyinUtil.getPinyin(xgwcBusinessDto.getBusinessName(), "");

            xgwcBusinessDto.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());

            try {
                boolean lockAcquired = redisUtil.tryLock(cacheKey, lockValue, 3000);
                if (!lockAcquired) {
                    log.warn("业务修改-获取分布式锁失败，业务名称={}", xgwcBusinessDto.getBusinessName());
                    return ApiResult.error("系统繁忙，请稍后再试");
                } else {
                    if (xgwcBusinessDto.getPid() == 0) {
                        xgwcBusinessDto.setLevel(xgwcBusinessDto.getBrandOwnerId() + "_" + pinyin);
                    } else {
                        xgwcBusinessDto.setLevel(getSequenceInParent(xgwcBusinessDto.getPid(),
                                xgwcBusinessDto.getBusinessId(),
                                true));
                    }
                }
            } finally {
                try {
                    redisUtil.unlock(cacheKey, lockValue);
                } catch (Exception unlockEx) {
                    log.error("释放分布式锁异常，cacheKey={}, 错误信息：{}",
                            cacheKey, unlockEx.getMessage(), unlockEx);
                }
            }
            // 执行更新操作
            xgwcBusinessDto.setUpdateBy(SecurityUtils.getNickName());
            int affectedRows = xgwcBusinessMapper.updateXgwcBusinessById(xgwcBusinessDto);
            if (affectedRows <= 0) {
                log.error("更新业务失败，未影响任何行，业务ID={}", xgwcBusinessDto.getBusinessId());
                return ApiResult.error("更新业务失败，可能ID不存在");
            }

            log.info("更新业务成功，业务ID={}", xgwcBusinessDto.getBusinessId());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新业务异常，业务ID={}, 错误信息：{}",
                    xgwcBusinessDto.getBusinessId(), e.getMessage(), e);
            return ApiResult.error("系统异常，更新业务失败");
        }
    }

    /**
     * 更新业务状态
     *
     * @param businessId 业务ID
     * @param status     状态
     * @return 更新结果
     */
    @Override
    public ApiResult updateStatusById(Integer businessId, Integer status) {
        // 参数校验
        if (businessId == null) {
            log.warn("更新业务状态失败：业务ID或状态为空");
            return ApiResult.error("参数不完整");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新品牌状态失败");
        }
        try {
            // 执行状态更新
            int affectedRows = xgwcBusinessMapper.updateStatusById(businessId, status);
            if (affectedRows <= 0) {
                log.error("更新业务状态失败，未影响任何行，业务ID={}", businessId);
                return ApiResult.error("更新状态失败，可能ID不存在");
            }

            log.info("更新业务状态成功，业务ID={}, 新状态={}", businessId, status);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新业务状态异常，业务ID={}, 错误信息：{}", businessId, e.getMessage(), e);
            return ApiResult.error("系统异常，更新状态失败");
        }
    }

    /**
     * 查询业务树（一级目录为品牌商）
     *
     * @param brandOwnerId 品牌商ID
     * @return 业务树
     */
    @Override
    public List<XgwcBusinessInfo> getBusinessTreeByBrandOwnerId(List<Long> brandOwnerId) {
        List<XgwcBusinessVo> typeInfos = xgwcBusinessMapper.getBusinessTreeByBrandOwnerId(brandOwnerId);
        if (typeInfos.stream().anyMatch(business -> Objects.isNull(business.getPid()))) {
            log.error("获取业务信息失败，参数不完整缺少pid");
            return null;
        }

        // 收集所有唯一的品牌商ID
        Set<Long> brandOwnerIds = typeInfos.stream()
                .map(XgwcBusinessVo::getBrandOwnerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 创建品牌商作为一级节点
        List<XgwcBusinessInfo> collect = brandOwnerIds.stream()
                .map(brandId -> {
                    XgwcBusinessVo companyVo = typeInfos.stream()
                            .filter(vo -> brandId.equals(vo.getBrandOwnerId()) && vo.getPid() == 0)
                            .findFirst()
                            .orElse(null);

                    if (companyVo == null) {
                        return null;
                    }

                    XgwcBusinessInfo companyInfo = new XgwcBusinessInfo();
                    BeanUtils.copyProperties(companyVo, companyInfo);

                    // 设置品牌商作为一级节点，pid设为0
                    companyInfo.setPid(0L);
                    companyInfo.setBusinessId(companyVo.getBrandOwnerId()); // 使用brandOwnerId作为节点ID
                    companyInfo.setBusinessName(companyVo.getBrandOwnerName()); // 使用品牌商名称作为节点名称

                    // 查找属于该品牌商的业务（pid等于brandOwnerId的节点）
                    List<XgwcBusinessVo> businessList = typeInfos.stream()
                            .filter(vo -> brandId.equals(vo.getBrandOwnerId()) && vo.getPid() == 0)
                            .collect(Collectors.toList());

                    // 构建业务树
                    companyInfo.setChiledrenList(buildDeptTree(businessList, typeInfos));
                    return companyInfo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return collect;
    }

    @Override
    public XgwcBusinessVo getXgwcBusinessByIdFeign(Long businessId) {
        XgwcBusinessVo xgwcBusinessVo = xgwcBusinessMapper.getXgwcBusinessById(businessId, null);
        return xgwcBusinessVo;
    }

    private List<XgwcBusinessInfo> buildDeptTree(List<XgwcBusinessVo> businessList, List<XgwcBusinessVo> allItems) {
        if (businessList.isEmpty()) {
            return Collections.emptyList();
        }

        // 按pid分组
        Map<Long, List<XgwcBusinessVo>> pidToChildren = allItems.stream()
                .collect(Collectors.groupingBy(XgwcBusinessVo::getPid));

        return businessList.stream()
                .map(businessVo -> {
                    XgwcBusinessInfo businessInfo = new XgwcBusinessInfo();
                    BeanUtils.copyProperties(businessVo, businessInfo);

                    // 递归构建子树
                    List<XgwcBusinessInfo> children = buildDeptTree(pidToChildren.getOrDefault(businessVo.getBusinessId(),
                            Collections.emptyList()), allItems);
                    businessInfo.setChiledrenList(children);
                    return businessInfo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<XgwcBusinessTreeVo> getBusinessRelation(Long businessId) {
        // 1. 获取当前业务信息
        XgwcBusinessVo current = xgwcBusinessMapper.getXgwcBusinessById(businessId, null);
        if (current == null) {
            return Collections.emptyList();
        }
        String level = current.getLevel();
        // 2. 计算层级（下划线数量）
        int underscoreCount = countUnderscores(level);
        // 3. 根据层级查询节点
        List<XgwcBusinessTreeVo> nodeList = new ArrayList<>();
        if (underscoreCount == 1) { // 一级节点
            // 查询所有子节点（包括二级和三级）
            nodeList = xgwcBusinessMapper.getDescendantsByLevel(level + "_%");
            // 添加当前节点
            nodeList.add(convertToTreeVo(current));
        } else if (underscoreCount == 2) { // 二级节点
            // 查询父节点（一级）
            XgwcBusinessVo parent = xgwcBusinessMapper.getParentByPid(current.getPid());
            if (parent != null) {
                nodeList.add(convertToTreeVo(parent));
            }
            // 添加当前节点
            nodeList.add(convertToTreeVo(current));
            // 查询所有子节点（三级）
            nodeList.addAll(xgwcBusinessMapper.getChildrenByPid(current.getBusinessId()));
        } else if (underscoreCount == 3) { // 三级节点
            // 查询父节点（二级）
            XgwcBusinessVo parent = xgwcBusinessMapper.getParentByPid(current.getPid());
            if (parent != null) {
                // 查询祖父节点（一级）
                XgwcBusinessVo grandParent = xgwcBusinessMapper.getParentByPid(parent.getPid());
                if (grandParent != null) {
                    nodeList.add(convertToTreeVo(grandParent));
                }
                nodeList.add(convertToTreeVo(parent));
            }
            // 添加当前节点
            nodeList.add(convertToTreeVo(current));
        }
        // 4. 构建树形结构
        return buildTree(nodeList);
    }

    @Override
    public List<XgwcBusinessVo> getBusinessOnlyFirst() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        return xgwcBusinessMapper.getBusinessOnlyFirst(PARENT_ID,brandId);
    }

    @Override
    public List<BusinessSettingDto> getBusinessSettingList(String businessName) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if (extracted(sysUser)) return null;
        return xgwcBusinessMapper.getBusinessSettingList(businessName, sysUser.getBrandId());
    }

    @Override
    public BusinessSettingDto getBusinessSettingById(Long businessId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if (extracted(sysUser)) return null;
        return xgwcBusinessMapper.getBusinessSettingById(businessId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateBusinessSetting(BusinessSettingDto businessSettingDto) {
        xgwcBusinessMapper.delBusinessSetting(businessSettingDto.getBusinessId());
        businessSettingDto.setCreateBy(SecurityUtils.getNickName());
        int updated = xgwcBusinessMapper.updateBusinessSetting(businessSettingDto);
        if (updated <= 0) {
            log.error("更新失败，更新{}条，入参{}", updated, JSONObject.toJSON(businessSettingDto));
            throw new ApiException("更新失败，请稍后重试");
        }
        return ApiResult.ok();
    }

    private static boolean extracted(SysUser sysUser) {
        Long brandId = sysUser.getBrandId();
        if (brandId == null){
            log.info("当前非品牌商账号，禁止获取数据,请求信息{}",
                    JSONObject.toJSON(sysUser));
            return true;
        }
        return false;
    }

    // 计算下划线数量
    private int countUnderscores(String str) {
        int count = 1;
        for (char c : str.toCharArray()) {
            if (c == '_') count++;
        }
        return count;
    }

    // 转换为树形VO
    private XgwcBusinessTreeVo convertToTreeVo(XgwcBusinessVo vo) {
        XgwcBusinessTreeVo treeVo = new XgwcBusinessTreeVo();
        treeVo.setBusinessId(vo.getBusinessId());
        treeVo.setBusinessName(vo.getBusinessName());
        treeVo.setParentBusinessId(vo.getPid()); // 使用pid作为父ID
        return treeVo;
    }

    // 构建树形结构
    private List<XgwcBusinessTreeVo> buildTree(List<XgwcBusinessTreeVo> nodes) {
        Map<Long, XgwcBusinessTreeVo> nodeMap = new HashMap<>();
        List<XgwcBusinessTreeVo> rootNodes = new ArrayList<>();
        // 第一遍：创建所有节点映射并初始化children
        for (XgwcBusinessTreeVo node : nodes) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getBusinessId(), node);
        }
        // 第二遍：建立父子关系
        for (XgwcBusinessTreeVo node : nodes) {
            Long parentId = node.getParentBusinessId();
            if (parentId == null || parentId == 0) {
                rootNodes.add(node);
            } else {
                XgwcBusinessTreeVo parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }

        return rootNodes;
    }
}
