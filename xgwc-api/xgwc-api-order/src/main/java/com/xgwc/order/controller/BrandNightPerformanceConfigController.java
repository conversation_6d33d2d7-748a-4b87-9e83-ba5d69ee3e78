package com.xgwc.order.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.order.entity.cmd.BrandNightPerformanceConfigCmd;
import com.xgwc.order.entity.cmd.BrandNightPerformanceConfigUpdateCmd;
import com.xgwc.order.entity.dto.BrandNightPerformanceConfigDto;
import com.xgwc.order.entity.vo.BrandNightPerformanceConfigQueryVo;
import com.xgwc.order.service.IBrandNightPerformanceConfigService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/brandNightPerformanceConfig")
public class BrandNightPerformanceConfigController extends BaseController {

    @Resource
    private IBrandNightPerformanceConfigService brandNightPerformanceConfigService;

    /**
     * 查询品牌商晚间业绩配置列表
     */
    @MethodDesc("查询品牌商晚间业绩配置列表")
    @PreAuthorize("@ss.hasPermission('brandNightPerformanceConfig:brandNightPerformanceConfig:list')")
    @GetMapping("/listPage")
    public ApiResult<ApiListResult<BrandNightPerformanceConfigDto>> listPage(@RequestBody BrandNightPerformanceConfigQueryVo queryVo) {
        return ApiResult.ok(brandNightPerformanceConfigService.listPage(queryVo));
    }

    /**
     * 获取品牌商晚间业绩配置详细信息
     */
    @MethodDesc("获取品牌商晚间业绩配置详细信息")
    @PreAuthorize("@ss.hasPermission('brandNightPerformanceConfig:brandNightPerformanceConfig:query')")
    @GetMapping(value = "/get")
    public ApiResult<BrandNightPerformanceConfigDto> getInfo(@RequestParam("id") Long id) {
        return success(brandNightPerformanceConfigService.get(id));
    }

    /**
     * 新增品牌商晚间业绩配置
     */
    @MethodDesc("新增品牌商晚间业绩配置")
    @PreAuthorize("@ss.hasPermission('brandNightPerformanceConfig:brandNightPerformanceConfig:add')")
    @Log(title = "品牌商晚间业绩配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ApiResult add(@RequestBody @Valid BrandNightPerformanceConfigCmd cmd) {
        return toAjax(brandNightPerformanceConfigService.add(cmd));
    }

    /**
     * 修改品牌商晚间业绩配置
     */
    @MethodDesc("修改品牌商晚间业绩配置")
    @PreAuthorize("@ss.hasPermission('brandNightPerformanceConfig:brandNightPerformanceConfig:edit')")
    @Log(title = "品牌商晚间业绩配置", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public ApiResult update(@RequestBody @Valid BrandNightPerformanceConfigUpdateCmd cmd) {
        return toAjax(brandNightPerformanceConfigService.update(cmd));
    }

    /**
     * 删除品牌商晚间业绩配置
     */
    @MethodDesc("删除品牌商晚间业绩配置")
    @PreAuthorize("@ss.hasPermission('brandNightPerformanceConfig:brandNightPerformanceConfig:remove')")
    @Log(title = "品牌商晚间业绩配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public ApiResult remove(@RequestBody Long[] ids) {
        return toAjax(brandNightPerformanceConfigService.remove(ids));
    }
}
