package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单报销申请
 */
@Data
public class OrderExpensesApply {

    /**
     * 主键
     */
    private Long id;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 实收金额
     */
    private BigDecimal actualAmount;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 客户id
     */
    private String customerNo;

    /**
     * 用户ID
     */
    private Long applyUserId;

    /**
     * 申请人用户名
     */
    private String applyUserName;

    /**
     * 最后审批时间
     */
    private String lastApproveTime;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 备注
     */
    private String remark;

    /**
     * 来源ID
     */
    private Long franchiseId;

    /**
     * 品牌商id
     */
    private Long brandId;

    /**
     * 状态：0正常，1非正常
     */
    private Integer status;

    /**
     * 申请状态
     */
    private String applyStatus;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    /** 流程实例id */
    private Long executionId;
}
