package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.XgwcPlatformGoods;
import com.xgwc.order.entity.XgwcPlatformGoodsSpec;
import com.xgwc.order.entity.vo.XgwcPlatformGoodsVo;
import com.xgwc.order.entity.dto.XgwcPlatformGoodsDto;
import com.xgwc.order.entity.vo.XgwcPlatformGoodsQueryVo;

public interface IXgwcPlatformGoodsService {

    void saveXgwcPlatformGoods(List<XgwcPlatformGoods> xgwcPlatformGoodsList, List<XgwcPlatformGoodsSpec> xgwcPlatformGoodsSpecList, Long brandOwnerId);

    XgwcPlatformGoodsDto selectByGoodsId(String goodsId);

    /**
     * 查询品牌商商品表列表
     *
     * @param xgwcBrandGoods 品牌商商品表
     * @return 品牌商商品表集合
     */
    List<XgwcPlatformGoodsDto> selectXgwcPlatformGoodsList(XgwcPlatformGoodsQueryVo xgwcBrandGoods);


    /**
     * 修改品牌商商品表
     *
     * @param xgwcBrandGoods 品牌商商品表
     * @return 结果
     */
    int updateXgwcPlatformGoods(XgwcPlatformGoodsVo xgwcBrandGoods);

}
