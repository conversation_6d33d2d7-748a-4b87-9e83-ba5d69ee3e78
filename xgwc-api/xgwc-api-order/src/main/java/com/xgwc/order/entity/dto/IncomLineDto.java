package com.xgwc.order.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class IncomLineDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("编号")
    @Excel(name = "编号")
    private Long id;

    @FieldDesc("进线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "进线时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inTime;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String taobaoId;

    @FieldDesc("添加微信0否 1是")
    @Excel(name = "添加微信0否 1是")
    private Long wechatTag;

    @FieldDesc("联系电话")
    @Excel(name = "联系电话")
    private String tel;

    @FieldDesc("来源店铺id-店铺查询")
    @Excel(name = "来源店铺id-店铺查询")
    private Long shopId;

    @FieldDesc("来源店铺名称-店铺查询")
    @Excel(name = "来源店铺名称-店铺查询")
    private String shopName;

    @FieldDesc("有效性编号-字典")
    @Excel(name = "有效性编号-字典")
    private String validDicCode;

    @FieldDesc("有效性名称-字典")
    @Excel(name = "有效性名称-字典")
    private String validDicName;


    @FieldDesc("有效性编号-字典2")
    private String validDicCode2;

    @FieldDesc("有效性名称-字典2")
    private String validDicName2;

    @FieldDesc("关联单号id-订单查询")
    @Excel(name = "关联单号id-订单查询")
    private Long orderId;

    @FieldDesc("关联单号编号-订单查询")
    @Excel(name = "关联单号编号-订单查询")
    private String orderNo;

    @FieldDesc("所属业务编号-字典")
    @Excel(name = "所属业务编号-字典")
    private String stateDicCode;

    @FieldDesc("所属业务名称-字典")
    @Excel(name = "所属业务名称-字典")
    private String stateDicName;

    @FieldDesc("回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回访时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nextTime;

    @FieldDesc("需求")
    @Excel(name = "需求")
    private String needInfo;

    @FieldDesc("销售ID")
    @Excel(name = "销售ID")
    private Long saleId;

    @FieldDesc("销售名称")
    @Excel(name = "销售名称")
    private String saleName;

    @FieldDesc("部门id")
    @Excel(name = "部门id")
    private Long deptId;

    @FieldDesc("部门姓名")
    @Excel(name = "部门姓名")
    private String deptName;

    @FieldDesc("跟进状态")
    @Excel(name = "跟进状态")
    private String trackName;
    private String trackCode;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;

    @FieldDesc("品牌商id")
    @Excel(name = "品牌商id")
    private Long brandId;

    @FieldDesc("品牌商名称")
    private String brandName;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("预算")
    private BigDecimal planMoney;

    private Date recycleTime;

    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("inTime",getInTime())
            .append("taobaoId",getTaobaoId())
            .append("wechatTag",getWechatTag())
            .append("tel",getTel())
            .append("shopId",getShopId())
            .append("shopName",getShopName())
            .append("validDicCode",getValidDicCode())
            .append("validDicName",getValidDicName())
            .append("orderId",getOrderId())
            .append("orderNo",getOrderNo())
            .append("stateDicCode",getStateDicCode())
            .append("stateDicName",getStateDicName())
            .append("nextTime",getNextTime())
            .append("needInfo",getNeedInfo())
            .append("saleId",getSaleId())
            .append("saleName",getSaleName())
            .append("deptId",getDeptId())
            .append("deptName",getDeptName())
            .append("trackName",getTrackName())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("isDel",getIsDel())
            .append("brandId",getBrandId())
            .append("franchiseId",getFranchiseId())
        .toString();
    }
}
