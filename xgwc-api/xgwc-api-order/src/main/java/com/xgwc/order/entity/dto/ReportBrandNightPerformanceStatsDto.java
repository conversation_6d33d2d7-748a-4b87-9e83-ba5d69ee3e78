package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReportBrandNightPerformanceStatsDto {

    @FieldDesc("主键ID")
    @Excel(name = "主键ID")
    private Long id;

    @FieldDesc("品牌商ID")
    @Excel(name = "品牌商ID")
    private Long brandId;

    @FieldDesc("统计维度: 1加盟商月, 2加盟商部门月, ")
    @Excel(name = "统计维度: 1加盟商月, 2加盟商部门月, ")
    private Long statDimension;

    @FieldDesc("统计开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "统计开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statStartTime;

    @FieldDesc("统计截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "统计截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statEndTime;

    @FieldDesc("加盟商ID")
    @Excel(name = "加盟商ID")
    private Long franchiseeId;

    @FieldDesc("部门ID")
    @Excel(name = "部门ID")
    private Long departmentId;

    @FieldDesc("业绩单量")
    @Excel(name = "业绩单量")
    private Long orderCount;

    @FieldDesc("订单金额")
    @Excel(name = "订单金额")
    private BigDecimal orderAmount;

    @FieldDesc("实收金额")
    @Excel(name = "实收金额")
    private BigDecimal actualAmount;

    @JsonIgnore
    @FieldDesc("其他内容JSON")
    @Excel(name = "其他内容JSON")
    private String otherBizJson;

    @FieldDesc("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedTime;


}