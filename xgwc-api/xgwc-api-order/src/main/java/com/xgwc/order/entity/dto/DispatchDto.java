package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class DispatchDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("已派设计师数量")
    @Excel(name = "已派设计师数量")
    private Integer dispatchNumber;

    @FieldDesc("派单状态：1 未领取 2 已领取 3 已派单")
    @Excel(name = "派单状态：1 未领取 2 已领取 3 已派单")
    private Integer dispatchStatus;

    @FieldDesc("1 普通单 2 紧急单")
    @Excel(name = "1 普通单 2 紧急单")
    private String dispatchType;

    @FieldDesc("")
    @Excel(name = "")
    private Long id;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("需派设计师数")
    @Excel(name = "需派设计师数")
    private Integer needDispatchNumber;

    @FieldDesc("订单Id")
    @Excel(name = "订单Id")
    private Long orderId;

    @FieldDesc("领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "领取时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiveTime;

    @FieldDesc("退回次数")
    @Excel(name = "退回次数")
    private Integer returnNumber;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("dispatchNumber",getDispatchNumber())
            .append("dispatchStatus",getDispatchStatus())
            .append("dispatchType",getDispatchType())
            .append("id",getId())
            .append("modifyTime",getModifyTime())
            .append("needDispatchNumber",getNeedDispatchNumber())
            .append("orderId",getOrderId())
            .append("receiveTime",getReceiveTime())
            .append("returnNumber",getReturnNumber())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
