package com.xgwc.order.entity.param;

import lombok.Data;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  16:52
 */


/**
 * 服务管理-店铺管理参数
 */
@Data
public class XgwcShopParam {

    /** 店铺名称 */
    private String shopName;

    /** 品牌id */
    private Long brandId;

    /** 渠道id */
    private Long channelId;

    /** 部门id */
    private Long deptId;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 品牌商id */
    private Long brandOwnerId;

    /** 加盟商id */
    private Long franchiseId;

    /** 店铺负责人 */
    private Long managerId;

    /** 服务商被授权的品牌商id */
    private List<Long> brandIds;

}
