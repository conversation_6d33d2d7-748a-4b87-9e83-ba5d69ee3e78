package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.xgwc.activiti.feign.api.ExecutionFeign;
import com.xgwc.activiti.feign.api.FlowTaskFeign;
import com.xgwc.activiti.feign.entity.FlowExecutionVo;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.json.JsonUtils;
import com.xgwc.order.dao.AfterAgencyAuditMapper;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.dao.OrderPayMapper;
import com.xgwc.order.dao.OrderRefundApplyMapper;
import com.xgwc.order.entity.OrderPay;
import com.xgwc.order.entity.OrderRefundCommision;
import com.xgwc.order.entity.OrderRefundPay;
import com.xgwc.order.entity.OrderRefundPayExt;
import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.dto.OrderDto;
import com.xgwc.order.entity.dto.OrderRefundApplyApproveIngDto;
import com.xgwc.order.entity.dto.SubOrderDto;
import com.xgwc.order.entity.vo.AfterAgencyAuditVo;
import com.xgwc.order.entity.vo.AfterSalesAuditVo;
import com.xgwc.order.entity.vo.OrderRefundApplyVo;
import com.xgwc.order.entity.vo.OrderRefundAuditVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class XgwcPlatformRefundDomainService {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private FlowTaskFeign flowTaskFeign;
    @Resource
    private OrderPayMapper orderPayMapper;
    @Resource
    private ExecutionFeign executionFeign;
    @Resource
    private AfterAgencyAuditMapper afterAgencyAuditMapper;
    @Resource
    private OrderRefundApplyMapper orderRefundApplyMapper;
    @Resource
    private AfterAgencyAuditServiceImpl afterAgencyAuditServiceImpl;
    @Resource
    private OrderRefundApplyDomainService orderRefundApplyDomainService;

    @Async("scheduledExecutorService")
    public void autoCancelAndRefundedHandle(Long brandOwnerId, Set<String> orderNoSet, Set<XgwcPlatformRefund> autoRefundedSet) {
        List<OrderRefundApplyApproveIngDto> approveIngList = orderRefundApplyMapper.listApproveIngByOrderNo(orderNoSet.stream().toList(), brandOwnerId);
        approveIngList.forEach(approveIng -> {
            // 流程
            if ("ING".equals(approveIng.getApplyStatus()) && approveIng.getTaskId() != null) {
                ApiResult apiResult = flowTaskFeign.feignCancel(approveIng.getTaskId());
                log.info("flowTaskFeign.feignCancel taskId={}, 结果: {}", approveIng.getTaskId(), JSONObject.toJSONString(apiResult));
            } else if (approveIng.getAfterCheckStatus() == 0) {
                if (approveIng.getAgencyAuditId() != null) {
                    // 更新售后审核单状态
                    afterAgencyAuditMapper.updateAfterAgencyAuditStatus(approveIng.getAgencyAuditId(), 1, 1L, "售后单变动，系统自动退回！");
                }
                OrderRefundAuditVo vo = new OrderRefundAuditVo();
                vo.setId(approveIng.getId());
                vo.setApplyStatus("WITHDRAW");
                vo.setAfterCheckStatus(2);
                orderRefundApplyMapper.updateOrderRefundApplyCheckStatus(vo);
            }
        });
        if (autoRefundedSet.isEmpty()) {
            return;
        }
        autoRefundedSet.forEach(autoRefunded -> {
            String orderNo = autoRefunded.getTradeNo();
            // 可能仅仅是一个支付流水.而不是主单.
            List<OrderPay> orderPayList = orderPayMapper.listByCollectionNo(ImmutableList.of(brandOwnerId), ImmutableList.of(orderNo));
            if (orderPayList.isEmpty()) {
                log.error("系统没找到自动退款对应的订单: orderNo={}, RefundNo={}", orderNo, autoRefunded.getRefundNo());
                return;
            }
            OrderPay orderPay = orderPayList.get(0);
            OrderDto orderDto = orderMapper.selectOnlyOrderById(orderPay.getOderId());
            if (!brandOwnerId.equals(orderDto.getBrandId())) {
                log.error("订单所属品牌商异常: brandOwnerId={}, order.getBrandId()={}", brandOwnerId, orderDto.getBrandId());
                return;
            }
            // orderNo 不是老系统的单才做处理
            if (!orderDto.isOldSysOrder()) {
                // 自动生成业务退款申请单
                OrderRefundApplyVo vo = new OrderRefundApplyVo();
                vo.setRefundType(1);
                vo.setRefundReasonCode("1");
                vo.setRefundReason("平台自动退款-系统自动申请");
                vo.setPreId(orderDto.getId());
                vo.setOrderNo(orderNo);
                vo.setFranchiseId(orderDto.getFranchiseId());
                vo.setApplyUserId(orderDto.getSaleManId());
                vo.setApplyUserName(orderDto.getSaleManName());

                OrderRefundPayExt payExt = new OrderRefundPayExt();
                payExt.setOpenQianniu(0);

                List<OrderRefundPay> orderRefundPayCmdList = Lists.newArrayList();
                OrderRefundPay orderRefundPay = new OrderRefundPay();
                orderRefundPay.setPreId(orderPay.getId());
                orderRefundPay.setRefundPayChannel(orderPay.getPayChannel());
                orderRefundPay.setRefundConfig(JsonUtils.toJsonString(payExt));
                if (orderNo.equals(orderPay.getCollectionNo())) {
                    orderRefundPay.setRefundAmount(orderPay.getRefundableAmount());
                }
                orderRefundPayCmdList.add(orderRefundPay);
                vo.setRefundPayJson(JsonUtils.toJsonString(orderRefundPayCmdList));

                List<OrderRefundCommision> refundCommisionList = Lists.newArrayList();
                List<SubOrderDto> subOrders = orderMapper.selectSubOrderList(orderDto.getId());
                if (orderDto.getDesignerId() != null) {
                    subOrders.add(0, BeanUtil.copyProperties(orderDto, SubOrderDto.class));
                }
                if (subOrders != null && !subOrders.isEmpty()) {
                    refundCommisionList = subOrders.stream().map(x -> {
                        OrderRefundCommision orderRefundCommision = new OrderRefundCommision();
                        orderRefundCommision.setSubOrderNo(x.getOrderNo());
                        orderRefundCommision.setPreId(x.getId());
                        orderRefundCommision.setCommisionAmount(x.getMoney());
                        orderRefundCommision.setDesignerUserId(x.getDesignerId());
                        orderRefundCommision.setDesignerUserName(x.getDesignerName());
                        orderRefundCommision.setDesignerPhone(ParamDecryptUtil.decryptParam(x.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
                        orderRefundCommision.setDesignerBusiness(x.getDesignerBusiness());
                        return orderRefundCommision;
                    }).collect(Collectors.toList());
                }
                vo.setRefundCommisionJson(JsonUtils.toJsonString(refundCommisionList));
                ApiResult result = orderRefundApplyDomainService.insertOrderRefundApply(vo, orderDto.getSaleManId(), orderDto.getSaleManName());
                if (result.getStatus() != 1) {
                    log.error("orderRefundApplyDomainService.insertOrderRefundApply vo={}, 结果:{}", JsonUtils.toJsonString(vo), JSONObject.toJSONString(result));
                    throw new ApiException(result.getMessage());
                }

                AfterAgencyAuditVo auditVo = new AfterAgencyAuditVo();
                auditVo.setApplyId(vo.getId());
                auditVo.setOrderNo(orderNo);
                auditVo.setFranchiseId(orderDto.getFranchiseId());
                Long id = afterAgencyAuditServiceImpl.insertAfterAgencyAudit(auditVo, orderDto.getBrandId(), orderDto.getSaleManId());
                if (id == null) {
                    throw new ApiException("自动领取售后单异常.");
                }
                // 自动通过
                FlowExecutionVo feVo = new FlowExecutionVo();
                feVo.setTitle(orderNo);
                feVo.setFlowValue("AfterSales");
                feVo.setBusinessKey(vo.getId());
                feVo.setBrandId(brandOwnerId);
                feVo.setCreateBy(orderDto.getSaleManId());
                feVo.setCreateName(orderDto.getSaleManName());
                feVo.setBrandName("品牌商");
                ApiResult<Long> apiResult = executionFeign.automaticFlowExecution(feVo);
                if (apiResult.getStatus() != 1) {
                    log.error("executionFeign.automaticFlowExecution vo={}, 结果:{}", JsonUtils.toJsonString(feVo), JSONObject.toJSONString(apiResult));
                    throw new ApiException(result.getMessage());
                }

                AfterSalesAuditVo afterSalesAuditVo = new AfterSalesAuditVo();
                afterSalesAuditVo.setAgencyAuditId(id);
                afterSalesAuditVo.setAuditStatus(0);
                afterSalesAuditVo.setRemark("自动领取后自动审批通过");
                afterSalesAuditVo.setId(vo.getId());
                afterSalesAuditVo.setOrderNo(orderNo);
                afterSalesAuditVo.setBrandId(orderDto.getBrandId());
                afterSalesAuditVo.setFranchiseId(orderDto.getFranchiseId());
                int i = afterAgencyAuditServiceImpl.auditAfterSalesOrder(afterSalesAuditVo, orderDto.getSaleManId(), orderDto.getSaleManName(), apiResult.getData());
                if (i == 0) {
                    log.error("afterAgencyAuditServiceImpl.auditAfterSalesOrder vo={}", JsonUtils.toJsonString(afterSalesAuditVo));
                    throw new ApiException(result.getMessage());
                }
            }
        });
    }

}