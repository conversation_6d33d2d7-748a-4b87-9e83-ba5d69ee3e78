package com.xgwc.order.service;

import com.xgwc.common.entity.dto.FlowExecutionDto;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.OrderInvoiceApply;
import com.xgwc.order.entity.dto.OrderInvoiceApplyDto;
import com.xgwc.order.entity.dto.OrderInvoiceDto;
import com.xgwc.order.entity.vo.OrderInvoiceQueryVo;

import java.util.List;

public interface OrderInvoiceService {

    /**
     * 插入申请数据
     * @param orderInvoiceApply 申请数据
     * @return 是否成功
     */
    ApiResult insertOrderInvoiceApply(OrderInvoiceApply orderInvoiceApply);

    /**
     * 更新申请数据
     * @param orderInvoiceApply 申请数据
     * @return 是否成功
     */
    int updateOrderInvoiceApply(OrderInvoiceApply orderInvoiceApply);

    /**
     * 根据主键查询报销订单详情
     * @param id 主键
     * @return 报销订单详情
     */
    OrderInvoiceApplyDto selectOrderInvoiceApplyById(Long id);

    /**
     * 处理发票流程
     * @param execution 流程实例
     */
    void executeInvoice(FlowExecutionDto execution);

    /**
     * 查询所有申请数据
     * @return 所有申请数据
     */
    List<OrderInvoiceDto> selectAllOrderInvoiceApply(OrderInvoiceQueryVo orderInvoiceVo);
}
