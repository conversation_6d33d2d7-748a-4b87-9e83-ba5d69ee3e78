package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderDesignerVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("订单id")
    private Long id;

    @FieldDesc("订单pid")
    private Long pid;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("派单人id")
    private Long allotUserId;

    @FieldDesc("派单人")
    private String allotUserName;

    @FieldDesc("设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    private String designerPhone;

    @FieldDesc("设计师业务类型")
    private String designerBusiness;

    @FieldDesc("佣金金额")
    private BigDecimal money;

    @FieldDesc("约定初稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveAppointTime;

    @FieldDesc("提交初稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveFirstTime;

    @FieldDesc("提交定稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveEndTime;

    @FieldDesc("交稿状态：0未交稿 1交初稿 2交定稿")
    private Integer archiveType;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("是否有责：1有 0无")
    private Integer isDuty;

    @FieldDesc("被更换的设计师ID")
    private Long oldDesignerId;

    @FieldDesc("被更换的设计师名称")
    private String oldDesignerName;

    @FieldDesc("被更换的设计师电话")
    private String oldDesignerPhone;

    @FieldDesc("被更换的设计师业务类型")
    private String oldDesignerBusiness;

    @FieldDesc("评分")
    private List<OrderScoreVo> scoreVo;


}
