package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class AgencyDispatchVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("派单工作台Id")
    private Long dispatchId;

    @FieldDesc("订单Id")
    private Long orderId;

    @FieldDesc("需派设计师数")
    private Integer needDispatchNumber;

    @FieldDesc("领取的设计师数量")
    private Integer dispatchNumber;

    @FieldDesc("派单备注")
    private String dispatchRemark;

    @FieldDesc("派单状态：1 待派单 2 已派单")
    private Integer dispatchStatus;

    @FieldDesc("派单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dispatchTime;

    @FieldDesc("派单员")
    private Long dispatchUserId;

    @FieldDesc("主键")
    private Long id;




}
