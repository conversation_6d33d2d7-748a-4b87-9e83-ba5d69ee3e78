package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class AfterSalesExecData {

private static final long serialVersionUID=1L;

    /** 业务id */
    private Long businessId;

    /** 主键 */
    private Long id;

    /** 参数名 */
    private String keyName;

    /** 参数值 */
    private String keyValue;

    /** 创建人 */
    private String createBy;

}