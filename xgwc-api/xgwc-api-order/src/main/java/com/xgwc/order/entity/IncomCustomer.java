package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class IncomCustomer {

    private static final long serialVersionUID=1L;

    /** 编号 */
    private Long id;

    /** 客户ID */
    private String taobaoId;

    /** 客户等级-字典 */
    private String taobaoLv;

    /** 联系电话 */
    private String tel;

    /** 最后一次跟进时间 */
    private Date lastTime;

    /** 跟进结论 */
    private String trackName;

    /** 进线次数 */
    private Long lineNum;

    /** 跟进数 */
    private Long tarckNum;

    /** 订单总数 */
    private Long orderNum;

    /** 性别 */
    private String sex;

    private Long wechatTag;


    private String address;

    private String province;

    private String city;
    private String region;

    private String area;

    /** 品牌商id */
    private Long brandId;

    /** 加盟商id */
    private Long franchiseId;

    private String createBy;

    private Date createTime;

    private String updateBy;

    private Date updateTime;
}