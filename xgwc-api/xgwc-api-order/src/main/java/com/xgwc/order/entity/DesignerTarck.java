package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class DesignerTarck {

private static final long serialVersionUID=1L;

    /** 跟进人 */
    private String createBy;

    /** 跟进时间 */
    private Date createTime;

    /** 设计师id */
    private Long designerId;

    /** 跟进说明 */
    private String details;

    /**  */
    private Long id;

    /** 下次回访时间 */
    private Date nextTime;

    /** 跟进状态编码-字典 */
    private String trackCode;

    /** 跟进状态名称-字典 */
    private String trackName;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;



}