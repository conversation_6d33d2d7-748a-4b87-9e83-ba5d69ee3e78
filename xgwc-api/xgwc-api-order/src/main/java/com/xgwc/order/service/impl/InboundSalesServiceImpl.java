package com.xgwc.order.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.ExcelExportUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.entity.vo.InboundSalesBuilderVo;
import com.xgwc.order.entity.vo.InboundSalesParamVo;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.InboundSalesMapper;
import com.xgwc.order.service.IInboundSalesService;
import com.xgwc.order.entity.InboundSales;
import com.xgwc.order.entity.vo.InboundSalesVo;
import com.xgwc.order.entity.dto.InboundSalesDto;
import com.xgwc.order.entity.vo.InboundSalesQueryVo;

@Slf4j
@Service
public class InboundSalesServiceImpl implements IInboundSalesService {
    @Resource
    private InboundSalesMapper inboundSalesMapper;

    /**
     * 查询销售进线
     * @param id 销售进线主键
     * @return 销售进线
     */
    @Override
    public InboundSalesDto selectInboundSalesById(Long id) {
        return inboundSalesMapper.selectInboundSalesById(id);
    }

    /**
     * 查询销售进线列表
     * @param inboundSales 销售进线
     * @return 销售进线
     */
    @Override
    public List<InboundSalesDto> selectInboundSalesList(InboundSalesQueryVo inboundSales) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId!= null) return List.of();
        inboundSales.setBrandId(brandId);
        return inboundSalesMapper.selectInboundSalesList(inboundSales);
    }

    /**
     * 新增销售进线
     * @param dto 销售进线
     * @return 结果
     */
    @Override
    public int insertInboundSales(InboundSalesParamVo dto) {
        List<InboundSales> inboundSalesList = new ArrayList<>();
        if(!dto.getBuilderList().isEmpty()){
            throw new ApiException("请添加进线人员信息");
        }
        Date nowDate = DateUtils.getNowDate();
        SysUser sysUser = SecurityUtils.getSysUser();
        for (InboundSalesBuilderVo item : dto.getBuilderList()){
            InboundSales inboundSales = new InboundSales();
            inboundSales.setFranchiseId(dto.getFranchiseId());
            inboundSales.setBrandId(sysUser.getBrandId());
            inboundSales.setDeptId(sysUser.getDeptId());
            inboundSales.setProceedDate(dto.getProceedDate());
            inboundSales.setUserId(item.getUserId());
            inboundSales.setInquiriesNumber(item.getInquiriesNumber());
            inboundSales.setOnlineDuration(item.getOnlineDuration());
            inboundSales.setCreateById(sysUser.getUserId());
            inboundSales.setCreateBy(sysUser.getUserName());
            inboundSales.setCreateTime(nowDate);
            inboundSalesList.add(inboundSales);
        }
        return inboundSalesMapper.batchInsert(inboundSalesList);
    }

    /**
     * 修改销售进线
     * @param dto 销售进线
     * @return 结果
     */
    @Override
    public int updateInboundSales(InboundSalesVo dto) {
        if(inboundSalesMapper.selectInboundSalesById(dto.getId()) == null){
            throw new ApiException("数据不存在");
        }
        SysUser sysUser = SecurityUtils.getSysUser();
        InboundSales inboundSales = BeanUtil.copyProperties(dto, InboundSales.class);
        inboundSales.setUpdateById(sysUser.getUserId());
        inboundSales.setUpdateBy(sysUser.getUserName());
        inboundSales.setUpdateTime(DateUtils.getNowDate());
        return inboundSalesMapper.updateInboundSales(inboundSales);
    }

    /**
     * 删除销售进线信息
     * @param id 销售进线主键
     * @return 结果
     */
    @Override
    public int deleteInboundSalesById(Long id) {
        return inboundSalesMapper.deleteInboundSalesById(id);
    }

    @Override
    public void export(HttpServletResponse response, InboundSalesQueryVo inboundSales) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId!= null) return;
        inboundSales.setBrandId(brandId);
        List<InboundSalesDto> list = inboundSalesMapper.selectInboundSalesList(inboundSales);
        try {
            ExcelExportUtil.exportExcelToResponse("销售进线",null,null,
                    list,response,"销售进线");
        } catch (IOException e) {
            log.error("导出销售进线失败", e);
            throw new ApiException("导出销售进线失败");
        }
    }
}
