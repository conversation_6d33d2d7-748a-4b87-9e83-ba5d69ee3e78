package com.xgwc.order.dao;

import com.xgwc.order.entity.XgwcPlatformGoodsSpec;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface XgwcPlatformGoodsSpecMapper {

    /**
     * 新增平台货品规格
     *
     * @return 结果
     */
    int insertList(@Param("xgwcPlatformGoodsSpecList") List<XgwcPlatformGoodsSpec> xgwcPlatformGoodsSpecList);


    void deleteByGoodsId(@Param("goodsIdList") List<String> goodsIdList, @Param("brandOwnerId") Long brandOwnerId);

    List<XgwcPlatformGoodsSpec> listByGoodsSpecId(@Param("goodsSpecIdList") List<String> goodsSpecIdList, @Param("brandOwnerId") Long brandOwnerId);

}
