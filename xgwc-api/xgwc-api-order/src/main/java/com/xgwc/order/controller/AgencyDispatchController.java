package com.xgwc.order.controller;

import java.util.List;
import java.util.UUID;

import com.xgwc.order.entity.dto.AgencyDispatchInfoDto;
import com.xgwc.order.entity.dto.AgencyDispatchPageDto;
import com.xgwc.order.entity.dto.AgencyRecordPageDto;
import com.xgwc.order.entity.vo.*;
import com.xgwc.redis.constants.LockCacheKey;
import com.xgwc.redis.util.RedisUtil;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.order.service.IAgencyDispatchService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

@RestController
@RequestMapping("/agencyDispatch")
public class AgencyDispatchController extends BaseController {
    @Autowired
    private IAgencyDispatchService agencyDispatchService;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 查询我的待派单
     */
    @MethodDesc("我的待派单")
    @PreAuthorize("@ss.hasPermission('agencyDispatch:agencyDispatch:list')")
    @GetMapping("/list")
    public ApiResult<AgencyDispatchPageDto> selectMyAgencyDispatchList(AgencyDispatchQueryVo agencyDispatch) {
        startPage();
        List<AgencyDispatchPageDto> list = agencyDispatchService.selectMyAgencyDispatchList(agencyDispatch);
        return getDataTable(list);
    }


    /**
     * 获取派单表详细信息
     */
    @MethodDesc("获取派单表详细信息")
    @GetMapping(value = "/{id}")
    public ApiResult<AgencyDispatchInfoDto> getInfo(@PathVariable("id") Long id) {
        return success(agencyDispatchService.selectAgencyDispatchById(id));
    }

    /**
     * 分配
     */
    @MethodDesc("分配")
    @PreAuthorize("@ss.hasPermission('agencyDispatch:agencyDispatch:add')")
    @Log(title = "派单表", businessType = BusinessType.INSERT)
    @PostMapping("/distribution")
    public ApiResult distribution(@RequestBody @Valid DistributionVo distributionVo) {
        String lockKey = LockCacheKey.ORDER_DISPATCH+distributionVo.getOrderId();
        String lockValue = UUID.randomUUID().toString();
        long expireMillis =  5_000L; // 锁5秒
        boolean locked = redisUtil.tryLock(lockKey, lockValue, expireMillis);
        if (!locked) {
            return ApiResult.error("操作过快，请稍后重试");
        }
        try {
            return toAjax(agencyDispatchService.distribution(distributionVo));
        } finally {
            redisUtil.unlock(lockKey, lockValue);
        }
    }

    /**
     * 领取
     */
    @MethodDesc("领取")
    @PreAuthorize("@ss.hasPermission('agencyDispatch:agencyDispatch:receive')")
    @Log(title = "派单表", businessType = BusinessType.UPDATE)
    @PostMapping("/receive")
    public ApiResult receive(@RequestBody @Valid ReceiveVo receiveVo) {
        String lockKey = LockCacheKey.ORDER_DISPATCH+receiveVo.getOrderId();
        String lockValue = UUID.randomUUID().toString();
        long expireMillis = 5_000L; // 锁5秒
        boolean locked = redisUtil.tryLock(lockKey, lockValue, expireMillis);
        if (!locked) {
            return ApiResult.error("操作频繁，请稍后重试");
        }
        try {
            return toAjax(agencyDispatchService.receive(receiveVo));
        } finally {
            redisUtil.unlock(lockKey, lockValue);
        }
    }

    /**
     * 退回
     */
    @MethodDesc("退回")
    @PreAuthorize("@ss.hasPermission('agencyDispatch:agencyDispatch:return')")
    @Log(title = "派单表", businessType = BusinessType.UPDATE)
    @PutMapping("/return")
    public ApiResult returnAgencyDispatch(@RequestBody @Valid ReturnAgencyDispatchVo returnAgencyDispatchVo) {
        return toAjax(agencyDispatchService.returnAgencyDispatch(returnAgencyDispatchVo));
    }

    /**
     * 修改备注
     */
    @MethodDesc("修改备注")
    @PreAuthorize("@ss.hasPermission('agencyDispatch:agencyDispatch:remark')")
    @Log(title = "派单表", businessType = BusinessType.UPDATE)
    @PutMapping("/updateRemark")
    public ApiResult updateRemark(@RequestBody @Valid UpdateRemarkVo updateRemarkVo) {
        return toAjax(agencyDispatchService.updateRemark(updateRemarkVo));
    }


    @MethodDesc("未派单记录")
    @PreAuthorize("@ss.hasPermission('agencyDispatch:agencyDispatch:list')")
    @GetMapping("/agencyList")
    public ApiResult<AgencyRecordPageDto> agencyList(AgencyRecordQueryVo params) {
        startPage();
        List<AgencyRecordPageDto> list = agencyDispatchService.agencyRecordList(params);
        return getDataTable(list);
    }

    @MethodDesc("统计紧急待派单数量")
    @PreAuthorize("@ss.hasPermission('agencyDispatch:agencyDispatch:list')")
    @GetMapping("/countEmergency")
    public ApiResult<Integer> countEmergency() {
        return success(agencyDispatchService.countEmergency());
    }
}
