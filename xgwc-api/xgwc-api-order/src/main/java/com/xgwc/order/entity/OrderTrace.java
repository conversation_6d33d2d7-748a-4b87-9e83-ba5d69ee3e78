package com.xgwc.order.entity;

import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class OrderTrace {

    @FieldDesc("谈单人")
    private String saleManName;

    @FieldDesc("订单类型")
    private String transferState;

    @FieldDesc("下单日期")
    private String orderDate;

    @FieldDesc("支付方式")
    private String payChannel;

    @FieldDesc("订单状态")
    private String shType;

    @FieldDesc("客户ID")
    private String taobaoId;

    @FieldDesc("订单金额")
    private String orderAmount;

    @FieldDesc("期望初稿日期")
    private String archiveExpectTime;

    @FieldDesc("付款方式")
    private String payType;

    @FieldDesc("派单人")
    private String allotUserName;

    @FieldDesc("更换设计师")
    private String designerName;

    @FieldDesc("佣金")
    private String money;

    @FieldDesc("约定初稿日期")
    private String archiveAppointTime;

    @FieldDesc("删除子订单")
    private String delOrder;

    @FieldDesc("修改页面：1:订单修改  2：派单工作台")
    private Integer type;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    private Date updateTime;

    @FieldDesc("所属公司")
    private String companyName;

    private Long brandId;

    private Long franchiseId;

    @FieldDesc("订单id")
    private Long orderId;
}
