package com.xgwc.order.service;



import com.xgwc.order.entity.dto.AfterSalesOrderDto;
import com.xgwc.order.entity.vo.AfterSalesOrderQueryVo;

import java.util.HashMap;
import java.util.List;

public interface IAfterSalesOrderService {


    /**
     * 查询售后订单列表
     * @param queryVo 查询条件
     * @return 结果
     */
    List<AfterSalesOrderDto> list(AfterSalesOrderQueryVo queryVo);

    /**
     * 统计售后订单数量
     * @return 数量
     */
    HashMap<String, Integer> count();
}
