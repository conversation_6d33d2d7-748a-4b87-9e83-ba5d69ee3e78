package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class InboundSalesVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("客服id")
    private Long userId;

    @FieldDesc("进线咨询人数")
    private Integer inquiriesNumber;

    @FieldDesc("晚间上线时长")
    private Integer onlineDuration;

    @FieldDesc("进行日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date proceedDate;

    @FieldDesc("创建人id")
    private Long createById;

    @FieldDesc("修改人id")
    private Long updateById;

}
