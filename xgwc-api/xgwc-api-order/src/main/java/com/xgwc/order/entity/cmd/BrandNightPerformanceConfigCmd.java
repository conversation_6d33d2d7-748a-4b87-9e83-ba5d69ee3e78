package com.xgwc.order.entity.cmd;

import com.xgwc.order.entity.NightConfigRange;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

import java.util.List;

@Data
public class BrandNightPerformanceConfigCmd {

    @NotNull
    @FieldDesc("加盟商ID")
    private Long franchiseeId;

    @NotNull
    @NotEmpty
    @FieldDesc("部门ID")
    private List<Long> departmentIds;

    @Valid
    @NotNull
    @NotEmpty
    @FieldDesc("晚间时间段配置")
    private List<NightConfigRange> rangeList;

}
