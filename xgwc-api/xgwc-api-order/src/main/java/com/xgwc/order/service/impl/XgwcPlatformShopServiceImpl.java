package com.xgwc.order.service.impl;

import com.xgwc.order.dao.XgwcPlatformShopMapper;
import com.xgwc.order.entity.XgwcPlatformShop;
import com.xgwc.order.service.IXgwcPlatformShopService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class XgwcPlatformShopServiceImpl implements IXgwcPlatformShopService {

    @Resource
    private XgwcPlatformShopMapper xgwcPlatformShopMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveXgwcPlatformShop(List<XgwcPlatformShop> shopList, Long brandOwnerId) {
        xgwcPlatformShopMapper.deleteByBrandOwnerId(brandOwnerId);
        shopList.forEach(XgwcPlatformShop -> xgwcPlatformShopMapper.insertXgwcPlatformShop(XgwcPlatformShop));
    }

}
