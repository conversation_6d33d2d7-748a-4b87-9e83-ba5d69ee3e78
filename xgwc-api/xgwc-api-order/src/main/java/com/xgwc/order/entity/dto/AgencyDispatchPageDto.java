package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class AgencyDispatchPageDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("派单Id")
    @Excel(name = "派单Id")
    private Long dispatchId;

    @FieldDesc("录单时长")
    @Excel(name = "录单时长")
    private String minuteTime;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String taobaoId;

    @FieldDesc("谈单人员名称")
    @Excel(name = "谈单人员名称")
    private String saleManName;

    @FieldDesc("加盟商")
    @Excel(name = "加盟商")
    private String companyName;

    @FieldDesc("订单金额")
    @Excel(name = "订单金额")
    private Long orderAmount;

    @FieldDesc("店铺名称")
    @Excel(name = "店铺名称")
    private String storeName;

    @FieldDesc("业务id")
    @Excel(name = "业务id")
    private String stateDicCode;

    @FieldDesc("业务名称")
    @Excel(name = "业务名称")
    private String stateDicName;


    @FieldDesc("要求")
    @Excel(name = "要求")
    private String allotRemark;

    @FieldDesc("领取的设计师数量")
    @Excel(name = "领取的设计师数量")
    private Integer dispatchNumber;

    @FieldDesc("派单备注")
    @Excel(name = "派单备注")
    private String dispatchRemark;

    @FieldDesc("1 普通单 2 紧急单")
    @Excel(name = "1 普通单 2 紧急单")
    private String dispatchType;

    @FieldDesc("派单状态：1 待派单 2 派单中")
    @Excel(name = "派单状态：1 待派单 2 派单中")
    private Integer dispatchStatus;

    @FieldDesc("派单员")
    @Excel(name = "派单员")
    private Long dispatchUserId;

    @FieldDesc("需派设计师数")
    @Excel(name = "需派设计师数")
    private Integer needDispatchNumber;

    @FieldDesc("订单Id")
    @Excel(name = "订单Id")
    private Long orderId;

    @FieldDesc("已分配数量")
    @Excel(name = "已分配数量")
    private Integer assignedQuantity;

    @FieldDesc("退回次数")
    @Excel(name = "退回次数")
    private Integer returnNumber;

    @FieldDesc("初稿时间")
    @Excel(name = "初稿时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date archiveExpectTime;
}
