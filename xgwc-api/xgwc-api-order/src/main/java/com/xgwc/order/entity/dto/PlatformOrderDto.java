package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PlatformOrderDto {

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String orderDate;

    @FieldDesc("订单来源ID（店铺id）")
    private Long storeId;

    @FieldDesc("支付方式  1:淘宝  2：微信  3：支付宝")
    private Integer payChannel;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("订单金额")
    private BigDecimal orderAmount;

    @FieldDesc("实收金额")
    private BigDecimal amount;

    @FieldDesc("备注")
    private String remark;

}