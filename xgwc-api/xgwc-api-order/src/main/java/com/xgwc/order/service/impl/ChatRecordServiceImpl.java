package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.ChatRecordMapper;
import com.xgwc.order.dao.XgwcBusinessMapper;
import com.xgwc.order.entity.ChatRecord;
import com.xgwc.order.entity.dto.ChatRecordDto;
import com.xgwc.order.entity.es.ChatIndex;
import com.xgwc.order.entity.es.ChatVo;
import com.xgwc.order.entity.vo.ChatRecordQueryVo;
import com.xgwc.order.entity.vo.ChatRecordStatusVo;
import com.xgwc.order.entity.vo.ChatRecordVo;
import com.xgwc.order.entity.vo.XgwcBusinessVo;
import com.xgwc.order.service.EsService;
import com.xgwc.order.service.IChatRecordService;
import com.xgwc.redis.constants.LockCacheKey;
import com.xgwc.redis.util.RedisUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


@Service
@Slf4j
public class ChatRecordServiceImpl implements IChatRecordService  {
    @Resource
    private ChatRecordMapper chatRecordMapper;
    @Resource
    private EsService esService;
    @Resource
    private XgwcBusinessMapper xgwcBusinessMapper;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 查询聊天记录
     * 
     * @param id 聊天记录主键
     * @return 聊天记录
     */
    @Override
    public ChatRecordDto selectChatRecordById(Long id) {
        ChatRecordDto chatRecordDto = chatRecordMapper.selectChatRecordById(id);
        if(StringUtils.isNotEmpty(chatRecordDto.getClassify())){
            chatRecordDto.setBusinessName(getBusinessName(Long.valueOf(chatRecordDto.getClassify())));
        }
        return chatRecordDto;
    }

    /**
     * 查询聊天记录列表
     * 
     * @param chatRecord 聊天记录
     * @return 聊天记录
     */
    @Override
    public List<ChatRecordDto> selectChatRecordList(ChatRecordQueryVo chatRecord) {
        chatRecord.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
        chatRecord.setBrandId(SecurityUtils.getSysUser().getBrandId());
        List<ChatRecordDto> chatRecordDtos = chatRecordMapper.selectChatRecordList(chatRecord);
        if(!chatRecordDtos.isEmpty()){
            for (ChatRecordDto item : chatRecordDtos) {
                if(StringUtils.isNotEmpty(item.getClassify())){
                    item.setBusinessName(getBusinessName(Long.valueOf(item.getClassify())));
                }
            }
        }
        return chatRecordDtos;
    }

    public String getBusinessName(Long businessId) {
        List<String> nameList = new ArrayList<>();
        buildPath(businessId, nameList);
        // 反转顺序（从顶层到当前层）
        Collections.reverse(nameList);
        return String.join("/", nameList);
    }

    private void buildPath(Long businessId, List<String> nameList) {
        if (businessId == null) return;
        XgwcBusinessVo business = xgwcBusinessMapper.getParentByPid(businessId);
        if (business != null) {
            nameList.add(business.getBusinessName()); // 加入当前名称
            if (business.getPid() != null && business.getPid() != 0) {
                buildPath(business.getPid(), nameList); // 递归查父级
            }
        }
    }

    /**
     * 新增聊天记录
     * 
     * @param dto 聊天记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertChatRecord(ChatRecordVo dto) {
        int result = 0;
        ChatRecord chatRecord = BeanUtil.copyProperties(dto, ChatRecord.class);
        SysUser sysUser = SecurityUtils.getSysUser();
        chatRecord.setFranchiseId(sysUser.getFranchiseId());
        XgwcBusinessVo xgwcBusinessById = xgwcBusinessMapper.getXgwcBusinessById(Long.parseLong(chatRecord.getClassify()), null);
        if(xgwcBusinessById !=null){
            chatRecord.setBrandId(xgwcBusinessById.getBrandOwnerId());
        }
        chatRecord.setCreateBy(sysUser.getUserName());
        chatRecord.setCreateTime(DateUtils.getNowDate());
        result = chatRecordMapper.insertChatRecord(chatRecord);
        ChatIndex chatIndex = new ChatIndex();
        chatIndex.setChatId(chatRecord.getId());
        chatIndex.setTitle(chatRecord.getTitle());
        chatIndex.setChatContent(chatRecord.getChatContent());
        assert xgwcBusinessById != null;
        chatIndex.setTypeCodes(xgwcBusinessById.getLevel());
        chatIndex.setBrandId(chatRecord.getBrandId());
        chatIndex.setFranchiseId(chatRecord.getFranchiseId());
        chatIndex.setCreate_time(DateUtils.getLongDateStr());
        esService.addChatData(chatIndex);
        return result;
    }

    /**
     * 修改聊天记录
     * 
     * @param dto 聊天记录
     * @return 结果
     */
    @Override
    public int updateChatRecord(ChatRecordVo dto) {
        ChatRecord chatRecord = BeanUtil.copyProperties(dto, ChatRecord.class);
        chatRecord.setUpdateTime(DateUtils.getNowDate());
        return chatRecordMapper.updateChatRecord(chatRecord);
    }

    /**
     * 批量删除聊天记录
     * 
     * @param ids 需要删除的聊天记录主键
     * @return 结果
     */
    @Override
    public int deleteChatRecordByIds(Long[] ids) {
        return chatRecordMapper.deleteChatRecordByIds(ids);
    }

    /**
     * 删除聊天记录信息
     * 
     * @param id 聊天记录主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteChatRecordById(Long id) {
        int result = chatRecordMapper.deleteChatRecordById(id);
        ChatIndex chatIndex = new ChatIndex();
        chatIndex.setChatId(id);
        esService.deleteChatData(chatIndex);
        return result;
    }

    @Override
    public int updateChatRecordStatus(ChatRecordStatusVo req) {
        validateExists(req.getId());
        ChatRecord chatRecord = new ChatRecord();
        chatRecord.setId(req.getId());
        chatRecord.setStatus(req.getStatus());
        return chatRecordMapper.updateChatRecord(chatRecord);
    }

    /**
     * 查询聊天记录列表
     *
     * @param chatRecord 聊天记录
     * @return 聊天记录集合
     */
    @Override
    public ApiListResult selectChatRecordBigList(ChatRecordQueryVo chatRecord) {
        chatRecord.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
        chatRecord.setBrandId(SecurityUtils.getSysUser().getBrandId());
        ApiListResult rspData = new ApiListResult();

        // 处理分类ID，查询业务层级
        String classify = chatRecord.getClassify();
        String businessLevels = null;
        if (!StringUtils.isEmpty(classify)) {
            List<Integer> integerRoleIds = Arrays.stream(classify.split(","))
                    .map(Integer::parseInt)
                    .toList();
            businessLevels = xgwcBusinessMapper.getBusinessLevelById(integerRoleIds);
        }

        // 构造 ES 查询参数
        ChatVo chatVo = new ChatVo();
        chatVo.setContent(chatRecord.getTitle());
        chatVo.setBranchId(chatRecord.getBrandId());
        chatVo.setFranchiseId(chatRecord.getFranchiseId());
        chatVo.setPageNum(chatRecord.getPageNum());
        chatVo.setPageSize(chatRecord.getPageSize());
        chatVo.setTypeCodes(businessLevels);

        // 调用 ES 查询
        JSONObject jsonObject = esService.searchChatData(chatVo);
        if (jsonObject == null) {
            return rspData; // 返回空分页结果
        }

        // 提取 total 和 rows
        Long total = jsonObject.getLong("total"); // 获取总记录数
        JSONArray rows = jsonObject.getJSONArray("rows"); // 获取当前页数据

        // 反序列化 rows 为 DTO 列表
        List<ChatRecordDto> resultList = new ArrayList<>();
        if (rows != null) {
            for (int i = 0; i < rows.size(); i++) {
                ChatIndex chatIndex = rows.getObject(i, ChatIndex.class);
                ChatRecordDto dto = convertToChatRecordDto(chatIndex);
                resultList.add(dto);
            }

            // 1. 收集所有 franchiseId 和 brandId 和 业务level
            List<Long> franchiseIds = new ArrayList<>();
            List<Long> brandIds = new ArrayList<>();
            List<String> classifys = new ArrayList<>();
            for (ChatRecordDto dto : resultList) {
                if (dto.getFranchiseId() != null) {
                    franchiseIds.add(dto.getFranchiseId());
                }
                if (dto.getBrandId() != null) {
                    brandIds.add(dto.getBrandId());
                }
                if (!StringUtils.isEmpty(dto.getClassify())) {
                    classifys.add(dto.getClassify());
                }
            }

            // 2. 批量查询 franchiseName 和 brandName
            Map<Long, String> franchiseNameMap = new HashMap<>();
            Map<Long, String> brandNameMap = new HashMap<>();
            Map<String, String> businessNameMap = new HashMap<>();

            // 查询加盟商名称
            if (!franchiseIds.isEmpty()) {
                List<ChatRecordDto> franchises = chatRecordMapper.selectFranchiseNamesByIds(franchiseIds);
                for (ChatRecordDto franchise : franchises) {
                    franchiseNameMap.put(franchise.getFranchiseId(), franchise.getFranchiseName());
                }
            }

            // 查询品牌名称
            if (!brandIds.isEmpty()) {
                List<ChatRecordDto> brands = chatRecordMapper.selectBrandNamesByIds(brandIds);
                for (ChatRecordDto brand : brands) {
                    brandNameMap.put(brand.getBrandId(), brand.getBrandName());
                }
            }

            // 查询业务名称
            if (!classifys.isEmpty()) {
                List<ChatRecordDto> businesses = chatRecordMapper.selectBusinessNamesByIds(classifys);
                for (ChatRecordDto business : businesses) {
                    businessNameMap.put(business.getClassify(), business.getBusinessName());
                }
            }

            // 3. 关联数据
            for (ChatRecordDto dto : resultList) {
                if (dto.getFranchiseId() != null) {
                    dto.setFranchiseName(franchiseNameMap.get(dto.getFranchiseId()));
                }
                if (dto.getBrandId() != null) {
                    dto.setBrandName(brandNameMap.get(dto.getBrandId()));
                }
                if (!StringUtils.isEmpty(dto.getClassify())) {
                    dto.setBusinessName(businessNameMap.get(dto.getClassify()));
                }
            }
        }
        rspData.setTotal(total);
        rspData.setRows(resultList);
        return rspData;
    }

    /**
     * 获取聊天记录详细信息
     *
     * @param id 聊天记录主键
     * @return 聊天记录
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult getInfoBigById(Long id) {
        String cacheKey = "chat_record:" + id;
        ChatRecordDto chatRecordDto = redisUtil.get(cacheKey, ChatRecordDto.class);
        if (chatRecordDto == null) {
            chatRecordDto = chatRecordMapper.selectChatRecordById(id);
            if (chatRecordDto == null) {
                return ApiResult.error("记录不存在");
            }
            String jsonString = JSON.toJSONString(chatRecordDto);
            redisUtil.set(cacheKey, jsonString, 60);
        }

        String lockCacheKey = LockCacheKey.CHAT_RECORD_VIEW_COUNT + "--" + id;
        String lockValue = UUID.randomUUID().toString();

        try {
            boolean lockAcquired = redisUtil.tryLock(lockCacheKey, lockValue, 3000);
            if (!lockAcquired) {
                log.warn("获取分布式锁失败，聊天记录id={}", id);
                throw new ApiException("系统繁忙，请稍后再试");
            }

            Long newViewCount = chatRecordDto.getViewCount() + 1;

            int result = chatRecordMapper.updateChatRecordViewCount(id, newViewCount);
            if (result > 0) {

                // 更新 ES
                ChatIndex chatIndex = new ChatIndex();
                chatIndex.setChatId(id);
                chatIndex.setViewCount(newViewCount);
                boolean chatData = esService.updateChatData(chatIndex);
                if (!chatData) {
                    log.error("大搜索--更新聊天记录浏览次数到ES失败：{}", chatRecordDto.getTitle());
                    throw new ApiException("系统繁忙，请稍后再试");
                }

                // 更新成功，将新的浏览次数保存到 Redis 中
                chatRecordDto.setViewCount(newViewCount);
                redisUtil.set(cacheKey, JSON.toJSONString(chatRecordDto), 60);
                return ApiResult.ok(chatRecordDto);
            }
            throw new ApiException("系统繁忙，请稍后再试");
        } catch (Exception e) {
            log.error("获取聊天记录详情异常，id={}, 错误信息：{}", id, e.getMessage(), e);
            throw (RuntimeException) e;
        } finally {
            try {
                redisUtil.unlock(lockCacheKey, lockValue);
            } catch (Exception unlockEx) {
                log.error("释放分布式锁异常，lockCacheKey={}, 错误信息：{}",
                        lockCacheKey, unlockEx.getMessage(), unlockEx);
            }
        }
    }

    private ChatRecordDto convertToChatRecordDto(ChatIndex chatIndex) {
        ChatRecordDto dto = new ChatRecordDto();
        dto.setId(chatIndex.getChatId());
        dto.setTitle(chatIndex.getTitle());
        //dto.setChatContent(chatIndex.getChatContent());
        dto.setClassify(chatIndex.getTypeCodes());
        dto.setCreateTime(DateUtils.parseDate(chatIndex.getCreate_time()));
        dto.setBrandId(chatIndex.getBrandId());
        dto.setFranchiseId(chatIndex.getFranchiseId());
        dto.setViewCount(chatIndex.getViewCount());
        return dto;
    }

    private void validateExists(Long id) {
        if (chatRecordMapper.selectChatRecordById(id) == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
    }
}
