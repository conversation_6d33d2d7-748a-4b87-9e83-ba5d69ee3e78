package com.xgwc.order.service.impl;

import java.util.Date;
import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.IncomLineTarckMapper;
import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.IncomLineVo;
import com.xgwc.order.entity.vo.RoleDataVo;
import com.xgwc.order.service.IcommonService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.IncomCustomerMapper;
import com.xgwc.order.service.IIncomCustomerService;
import com.xgwc.order.entity.IncomCustomer;
import com.xgwc.order.entity.vo.IncomCustomerVo;
import com.xgwc.order.entity.vo.IncomCustomerQueryVo;


@Service
public class IncomCustomerServiceImpl implements IIncomCustomerService  {
    @Resource
    private IncomCustomerMapper incomCustomerMapper;

    @Resource
    private IncomLineTarckMapper incomLineTarckMapper;
    @Autowired
    private IcommonService icommonService;

    /**
     * 查询客户列表列表
     * 
     * @param incomCustomer 客户列表
     * @return 客户列表
     */
    @Override
    public List<IncomCustomerDto> selectIncomCustomerList(IncomCustomerQueryVo incomCustomer) {
        RoleDataVo vo = icommonService.getRoleData(36);
        if(incomCustomer.getFranchiseId() == null)
            incomCustomer.setFranchiseId(vo.getFranchiseId());
        incomCustomer.setBrandId(vo.getBrandId());
        Boolean isDesensitization = vo.getIsDesensitization();
        List<IncomCustomerDto> list = incomCustomerMapper.selectIncomCustomerList(incomCustomer);
        if(!isDesensitization) {
            for (IncomCustomerDto d : list) {
                if(StringUtils.isNotBlank(d.getTel())) d.setTel(ParamDecryptUtil.decryptParam(d.getTel(), ParamDecryptUtil.PHONE_KEY));
            }
        }
        return list;
    }

    @Override
    public IncomCustomerBasicDto basic(String taobaoId) {
        IncomCustomerQueryVo incomCustomer = new IncomCustomerQueryVo();
        SysUser user = SecurityUtils.getSysUser();
        switch (user.getUserType()) {
            case 1:
            case 4:
                incomCustomer.setBrandId(user.getBrandId());
                break;
            case 2:
            case 5:
                incomCustomer.setFranchiseId(user.getFranchiseId());
                break;
        }
        incomCustomer.setTaobaoId(taobaoId);
        IncomCustomerBasicDto dto = incomCustomerMapper.selectIncomCustomerOne(incomCustomer);
        if(StringUtils.isNotBlank(dto.getTel())) {
            dto.setTel(ParamDecryptUtil.decryptParam(dto.getTel(),ParamDecryptUtil.PHONE_KEY));
        }
        return dto;
    }

    @Override
    public List<IncomCustomerStatisDto> statis(String taobaoId) {
        IncomCustomerQueryVo incomCustomer = new IncomCustomerQueryVo();
        SysUser user = SecurityUtils.getSysUser();
        switch (user.getUserType()) {
            case 1:
            case 4:
                incomCustomer.setBrandId(user.getBrandId());
                break;
            case 2:
            case 5:
                incomCustomer.setFranchiseId(user.getFranchiseId());
                break;
        }
        incomCustomer.setTaobaoId(taobaoId);
        List<IncomCustomerStatisDto> list = incomCustomerMapper.selectIncomCustomerStatisList(incomCustomer);
        list.stream().forEach(s -> {
            if("4".equals(s.getModel())) {
                String typeName = s.getTypeName();
                if(StringUtils.isNotBlank(typeName)) {
                    String phone = "";
                    if(typeName.indexOf("@") > -1) {
                        String[] tmp = typeName.split("@");
                        phone =tmp[0] + "-" + ParamDecryptUtil.decryptPhone(tmp[1]);
                    } else {
                        phone = ParamDecryptUtil.decryptPhone(typeName);
                    }
                    s.setTypeName(phone);
                }
            }
        });
        return list;
    }


    @Override
    public List<IncomLineTarckDto> tarckHis(String taobaoId) {
        IncomCustomerQueryVo incomCustomer = new IncomCustomerQueryVo();
        SysUser user = SecurityUtils.getSysUser();
        switch (user.getUserType()) {
            case 1:
            case 4:
                incomCustomer.setBrandId(user.getBrandId());
                break;
            case 2:
            case 5:
                incomCustomer.setFranchiseId(user.getFranchiseId());
                break;
        }
        incomCustomer.setTaobaoId(taobaoId);
        return incomLineTarckMapper.selectIncomLineTarckListByCustomer(incomCustomer);
    }

    @Override
    public List<IncomCustomerChatDto> chatHis(String taobaoId) {
        return null;
    }

    /**
     * 新增客户列表
     * 
     * @return 结果
     */
    @Override
    public int insertIncomCustomer(String type, IncomLineVo dto) {
        IncomCustomerDto customer = incomCustomerMapper.selectIncomCustomerByTaobaoId(dto.getTaobaoId(), dto.getBrandId(), dto.getFranchiseId());
        if(customer != null){
            IncomCustomer update = new IncomCustomer();
            update.setId(customer.getId());
            update.setTaobaoId(dto.getTaobaoId());
            update.setBrandId(dto.getBrandId());
            update.setFranchiseId(dto.getFranchiseId());
            switch (type) {
                case "line":
                    update.setLineNum(1L);
                    break;
                case "tarck":
                    update.setTarckNum(1L);
                    update.setLastTime(new Date());
                    update.setTrackName(dto.getTrackName());
                    update.setWechatTag(dto.getWechatTag());
                    if(StringUtils.isNotBlank(dto.getTel())) update.setTel(dto.getTel());
                    break;
                default:
                    return 1;
            }
            incomCustomerMapper.updateIncomCustomer(update);
        } else {
            IncomCustomer insert = new IncomCustomer();
            switch (type) {
                case "line":
                    insert.setLineNum(1L);
                    break;
            }
            insert.setTaobaoId(dto.getTaobaoId());
            insert.setTel(dto.getTel());
            insert.setWechatTag(dto.getWechatTag());
            insert.setTrackName(dto.getTrackName());
            insert.setCreateBy(SecurityUtils.getNickName());
            insert.setCreateTime(new Date());
            insert.setFranchiseId(dto.getFranchiseId());
            insert.setBrandId(dto.getBrandId());
            incomCustomerMapper.insertIncomCustomer(insert);
        }
        return 1;
    }

    /**
     * 修改客户列表
     *
     * @param dto 客户列表
     * @return 结果
     */
    @Override
    public int updateIncomCustomer(IncomCustomerVo dto) {
        IncomCustomer incomCustomer = BeanUtil.toBean(dto, IncomCustomer.class);
        incomCustomer.setUpdateBy(SecurityUtils.getNickName());
        incomCustomer.setUpdateTime(DateUtils.getNowDate());
        if (StringUtils.isNotBlank(incomCustomer.getTel())) {
            incomCustomer.setTel(ParamDecryptUtil.encrypt(incomCustomer.getTel(), ParamDecryptUtil.PHONE_KEY));
        }
        SysUser user = SecurityUtils.getSysUser();
        switch (user.getUserType()) {
            case 1:
            case 4:
                incomCustomer.setBrandId(user.getBrandId());
                break;
            case 2:
            case 5:
                incomCustomer.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
                break;
        }
      return incomCustomerMapper.updateIncomCustomer(incomCustomer);
    }

}
