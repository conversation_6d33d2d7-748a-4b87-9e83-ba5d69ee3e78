package com.xgwc.order.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.AgencyDispatchMapper;
import com.xgwc.order.dao.SendBackMapper;
import com.xgwc.order.entity.dto.DispatchPageDto;
import com.xgwc.order.entity.dto.SendBackDto;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.DispatchMapper;
import com.xgwc.order.service.IDispatchService;
import com.xgwc.order.entity.Dispatch;
import com.xgwc.order.entity.vo.DispatchVo;
import com.xgwc.order.entity.dto.DispatchDto;
import com.xgwc.order.entity.vo.DispatchQueryVo;


@Service
@Slf4j
public class DispatchServiceImpl implements IDispatchService  {
    @Resource
    private DispatchMapper dispatchMapper;
    @Resource
    private AgencyDispatchMapper agencyDispatchMapper;
    @Resource
    private SendBackMapper sendBackMapper;

    /**
     * 查询派单工作台
     * 
     * @param id 派单工作台主键
     * @return 派单工作台
     */
    @Override
    public DispatchDto selectDispatchById(Long id) {
        return dispatchMapper.selectDispatchById(id);
    }

    /**
     * 查询派单工作台列表
     *
     * @param dispatch 派单工作台
     * @return 派单工作台
     */
    @Override
    public List<DispatchPageDto> selectDispatchList(DispatchQueryVo dispatch) {
        //dispatch.setFranchiseIds(SecurityUtils.getSysUser().getFranchiseIds());
        dispatch.setBrandId(SecurityUtils.getSysUser().getBrandId());
        List<DispatchPageDto> list = dispatchMapper.selectDispatchList(dispatch);
        if (!list.isEmpty()) {
            try {
                Map<Long, SendBackDto> sendBackMap = sendBackMapper.findLatestRemarksByDispatchIds(
                        list.stream().map(DispatchPageDto::getId).toList()
                ).stream().collect(Collectors.toMap(SendBackDto::getDispatchId, sendBackDto -> sendBackDto));
                for (DispatchPageDto dispatchPageDto : list) {
                    Long dispatchId = dispatchPageDto.getId();
                    if (sendBackMap.containsKey(dispatchId)) {
                        dispatchPageDto.setLatestSendBackRemark(sendBackMap.get(dispatchId).getExplanatory());
                    }
                }
            } catch (Exception e) {
                log.error("获取派单备注 latestSendBackRemark error", e);
            }
        }
        return list;
    }

    /**
     * 新增派单工作台
     * 
     * @param dto 派单工作台
     * @return 结果
     */
    @Override
    public int insertDispatch(DispatchVo dto) {
        Dispatch dispatch = BeanUtil.copyProperties(dto, Dispatch.class);
        dispatch.setCreateTime(DateUtils.getNowDate());
        return dispatchMapper.insertDispatch(dispatch);
    }

    /**
     * 修改派单工作台
     * 
     * @param dto 派单工作台
     * @return 结果
     */
    @Override
    public int updateDispatch(DispatchVo dto) {
        Dispatch dispatch = BeanUtil.copyProperties(dto, Dispatch.class);
        dispatch.setUpdateTime(DateUtils.getNowDate());
        return dispatchMapper.updateDispatch(dispatch);
    }

    /**
     * 批量删除派单工作台
     * 
     * @param ids 需要删除的派单工作台主键
     * @return 结果
     */
    @Override
    public int deleteDispatchByIds(Long[] ids) {
        return dispatchMapper.deleteDispatchByIds(ids);
    }

    /**
     * 删除派单工作台信息
     * 
     * @param id 派单工作台主键
     * @return 结果
     */
    @Override
    public int deleteDispatchById(Long id) {
        return dispatchMapper.deleteDispatchById(id);
    }

    @Override
    public Map<String, Integer> statistics() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        Map<String, Integer> map = new HashMap<>();
        // 未派单数量
        map.put("dispatchCount", dispatchMapper.countDispatch(brandId));
        // 紧急单数量
        map.put("urgentCount", dispatchMapper.countUrgent(brandId));
        // 困难单数量
        map.put("difficultCount", dispatchMapper.countDifficult(brandId));
        // 今日领取数量
        map.put("todayReceiveCount", agencyDispatchMapper.countTodayReceive(SecurityUtils.getUserId()));
        // 今日派单数量
        map.put("todayDispatchCount", agencyDispatchMapper.countTodayDispatch(SecurityUtils.getUserId()));
        return map;
    }

    @Override
    public DispatchDto selectDispatchByOrderId(Long orderId) {
        return dispatchMapper.selectDispatchByOrderId(orderId);
    }
}
