package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  18:06
 */

/**
 * 品牌管理-品牌表级联
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XgwcBusinessInfo extends XgwcBusinessVo{

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<XgwcBusinessInfo> chiledrenList;
}
