package com.xgwc.order.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  16:19
 */

/**
 * 服务管理-渠道表
 */
@Data
public class XgwcChannel {

    /** 渠道id */
    private Long channelId;

    /** 渠道名称 */
    private String channelName;

    /** 排序：越小越前 */
    private Integer sort;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 是否删除：0正常，1删除 */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
