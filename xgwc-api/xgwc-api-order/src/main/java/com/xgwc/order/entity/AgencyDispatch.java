package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class AgencyDispatch {

private static final long serialVersionUID=1L;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 派单Id */
    private Long dispatchId;

    /** 领取的设计师数量 */
    private Integer dispatchNumber;

    /** 派单备注 */
    private String dispatchRemark;

    /** 派单状态：1 待派单 2 派单中 */
    private Integer dispatchStatus;

    /** 派单时间 */
    private Date dispatchTime;

    /** 派单员 */
    private Long dispatchUserId;

    /**  */
    private Long id;

    /** 行更新时间 */
    private Date modifyTime;

    /** 需派设计师数 */
    private Integer needDispatchNumber;

    /** 订单Id */
    private Long orderId;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 已分配数量 */
    private Integer assignedQuantity;

    /** 品牌商Id */
    private Long brandId;

}