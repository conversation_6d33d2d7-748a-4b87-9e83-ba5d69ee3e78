package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class XgwcPlatformTradeByBrandVO {

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("来自加盟商id")
    private Long franchiseId;
    @FieldDesc("来自加盟商名称")
    private String franchiseName;

    @FieldDesc("来自店铺id")
    private Long shopId;
    @FieldDesc("来自店铺名称")
    private String shopName;

    @FieldDesc("谈单人id")
    private String saleManId;
    @FieldDesc("谈单人名称")
    private String saleManName;

    @FieldDesc("收款平台订单编号")
    private String tradeNo;

    @FieldDesc("买家实付金额")
    private BigDecimal payAmount;

    @FieldDesc("收款平台id")
    private Long platformId;
    @FieldDesc("收款平台名称")
    private String platformName;

    @FieldDesc("收款平台店铺id")
    private String platformShopId;

    @FieldDesc("收款平台店铺名称")
    private String platformShopName;

    @FieldDesc("归档结果: 1已录 0未录")
    private String archiveResult;

}