package com.xgwc.order.dao;

import com.xgwc.order.entity.XgwcPlatformRefundDetails;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface XgwcPlatformRefundDetailsMapper {

    /**
     * 新增平台售后单明细
     *
     * @param xgwcPlatformRefundDetailsList 平台售后单明细
     * @return 结果
     */
    int insertList(@Param("xgwcPlatformRefundDetailsList") List<XgwcPlatformRefundDetails> xgwcPlatformRefundDetailsList);


    List<XgwcPlatformRefundDetails> listByRefundNo(@Param("refundNo") String refundNo, @Param("brandOwnerId") Long brandOwnerId);

    void deleteByRefundNo(@Param("refundNoList") List<String> refundNoList, @Param("brandOwnerId") Long brandOwnerId);

}