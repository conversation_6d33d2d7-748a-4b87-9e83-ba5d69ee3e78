package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class ChatRecordDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌id")
    @Excel(name = "品牌id")
    private Long brandId;

    @FieldDesc("品牌商名称")
    @Excel(name = "品牌商名称")
    private String brandName;

    @FieldDesc("聊天记录")
    @Excel(name = "聊天记录")
    private String chatContent;

    @FieldDesc("分类")
    @Excel(name = "分类")
    private String classify;

    @FieldDesc("业务名称")
    @Excel(name = "业务名称")
    private String businessName;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商名称")
    @Excel(name = "加盟商名称")
    private String franchiseName;

    @FieldDesc("查看次数")
    @Excel(name = "查看次数")
    private Long viewCount;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("是否删除：0正常，1删除")
    @Excel(name = "是否删除：0正常，1删除")
    private Long isDel;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("状态：0正常，1禁用")
    @Excel(name = "状态：0正常，1禁用")
    private Long status;

    @FieldDesc("聊天标题")
    @Excel(name = "聊天标题")
    private String title;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("来源id")
    @Excel(name = "来源id")
    private String sourceId;

}
