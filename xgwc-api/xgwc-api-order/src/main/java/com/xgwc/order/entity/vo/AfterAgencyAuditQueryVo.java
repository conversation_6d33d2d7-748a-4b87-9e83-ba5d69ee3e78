package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class AfterAgencyAuditQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("领取人")
    private Long receiverId;

    @FieldDesc("其它：订单编号/客户id/客服")
    private String other;

}
