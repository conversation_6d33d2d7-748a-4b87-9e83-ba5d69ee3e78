package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 售后订单dto
 */
@Data
public class AfterSalesOrderDto {

    @FieldDesc("退款申请id")
    private Long id;

    @FieldDesc("申请时长")
    private String minuteTime;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("客户id")
    private String taobaoId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("客服")
    private String saleManName;

    @FieldDesc("实付金额")
    private BigDecimal amount;

    @FieldDesc("业务名称")
    private String businessName;

    @FieldDesc("退款类型 1.全额退款，2.部分退款，3.退垫付")
    private Integer refundType;

    @FieldDesc("总退款金额")
    private BigDecimal lastAmount;

    @FieldDesc("可退金额")
    private BigDecimal preAmount;

    @FieldDesc("退款原因")
    private String refundReason;

    @FieldDesc("退款说明")
    private String remark;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("客户手机号")
    private String customerPhone;

    @FieldDesc("平台退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date platformRefundTime;

    @FieldDesc("即将默认退款:1是/0否")
    private Integer willAutoRefund;

    @FieldDesc("超时未领取:1是/0否")
    private Integer timeoutUnreceived;
}
