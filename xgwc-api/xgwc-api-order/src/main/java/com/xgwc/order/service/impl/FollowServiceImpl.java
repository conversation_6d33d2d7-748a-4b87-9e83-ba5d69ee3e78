package com.xgwc.order.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.util.SecurityUtils;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.FollowMapper;
import com.xgwc.order.service.IFollowService;
import com.xgwc.order.entity.Follow;
import com.xgwc.order.entity.vo.FollowVo;
import com.xgwc.order.entity.dto.FollowDto;


@Service
public class FollowServiceImpl implements IFollowService {
    @Resource
    private FollowMapper followMapper;

    @Override
    public List<FollowDto> selectFollowListByAgencyAuditId(Long agencyAuditId) {
        return followMapper.findFollowListByAgencyAuditId(agencyAuditId);
    }

    @Override
    public int insertFollow(FollowVo dto) {
        Follow follow = BeanUtil.copyProperties(dto, Follow.class);
        follow.setFollowUserId(SecurityUtils.getUserId());
        follow.setCreateTime(DateUtils.getNowDate());
        follow.setCreateBy(SecurityUtils.getNickName());
        return followMapper.insertFollow(follow);
    }

    @Override
    public List<FollowDto> findFollowDtoByAgencyAuditIds(List<Long> agencyAuditIds) {
        return followMapper.findFollowDtoByAgencyAuditIds(agencyAuditIds);
    }

}
