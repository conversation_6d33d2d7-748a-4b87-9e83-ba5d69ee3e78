package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class XgwcPlatformTradeQueryVo {

    @FieldDesc("搜索关键字")
    private String keyword;

    @FieldDesc("来自加盟商id")
    private Long franchiseId;

    @FieldDesc("来自店铺id")
    private Long shopId;

    @FieldDesc("平台id")
    private Long platformId;

    @FieldDesc("收款平台店铺id")
    private String platformShopId;

    @FieldDesc("归档结果: 1已录 0未录")
    private String archiveResult;

    @FieldDesc("下单日期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date crateTimeStart;

    @FieldDesc("下单日期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @FieldDesc("品牌商id")
    private Long brandId;
    @FieldDesc("品牌商id列表")
    private List<Long> brandIdList;

}