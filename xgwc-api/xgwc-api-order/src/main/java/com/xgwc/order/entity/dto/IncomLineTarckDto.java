package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class IncomLineTarckDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("编号")
    @Excel(name = "编号")
    private Long id;

    @FieldDesc("进线id")
    @Excel(name = "进线id")
    private Long inLineId;

    @FieldDesc("跟进状态编码-字典")
    @Excel(name = "跟进状态编码-字典")
    private String trackCode;

    @FieldDesc("跟进状态名称-字典")
    @Excel(name = "跟进状态名称-字典")
    private String trackName;

    @FieldDesc("跟进状态编码-字典")
    @Excel(name = "跟进状态编码-字典")
    private String trackCode2;

    @FieldDesc("跟进状态名称-字典")
    @Excel(name = "跟进状态名称-字典")
    private String trackName2;

    @FieldDesc("下次回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下次回访时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nextTime;

    @FieldDesc("关联单号id-订单查询")
    @Excel(name = "关联单号id-订单查询")
    private Long orderId;

    @FieldDesc("关联单号编号-订单查询")
    @Excel(name = "关联单号编号-订单查询")
    private String orderNo;

    @FieldDesc("跟进人")
    @Excel(name = "跟进人")
    private String trackBy;

    @FieldDesc("跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "跟进时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date trackTime;

    @FieldDesc("跟进概述")
    @Excel(name = "跟进概述")
    private String briefIntroduction;

    @FieldDesc("跟进详情")
    @Excel(name = "跟进详情")
    private String details;

    @FieldDesc("附件")
    @Excel(name = "附件")
    private String resource;

    @FieldDesc("部门id")
    @Excel(name = "部门id")
    private Long deptId;

    @FieldDesc("部门名称")
    @Excel(name = "部门名称")
    private String deptName;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;

    @FieldDesc("所属业务编号-字典")
    @Excel(name = "所属业务编号-字典")
    private String stateDicCode;

    @FieldDesc("所属业务名称-字典")
    @Excel(name = "所属业务名称-字典")
    private String stateDicName;



    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("inLineId",getInLineId())
            .append("trackCode",getTrackCode())
            .append("trackName",getTrackName())
            .append("nextTime",getNextTime())
            .append("orderId",getOrderId())
            .append("orderNo",getOrderNo())
            .append("trackBy",getTrackBy())
            .append("trackTime",getTrackTime())
            .append("briefIntroduction",getBriefIntroduction())
            .append("details",getDetails())
            .append("resource",getResource())
            .append("deptId",getDeptId())
            .append("deptName",getDeptName())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("isDel",getIsDel())
        .toString();
    }
}
