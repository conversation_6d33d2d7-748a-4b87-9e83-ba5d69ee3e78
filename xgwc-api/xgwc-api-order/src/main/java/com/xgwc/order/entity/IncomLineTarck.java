package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class IncomLineTarck {

private static final long serialVersionUID=1L;

    /** 编号 */
    private Long id;

    /** 进线id */
    private Long inLineId;

    /** 跟进状态编码-字典 */
    private String trackCode;

    /** 跟进状态名称-字典 */
    private String trackName;

    /** 跟进状态编码-字典 */
    private String trackCode2;

    /** 跟进状态名称-字典 */
    private String trackName2;

    /** 下次回访时间 */
    private Date nextTime;

    /** 关联单号id-订单查询 */
    private Long orderId;

    /** 关联单号编号-订单查询 */
    private String orderNo;

    /** 跟进人 */
    private String trackBy;

    /** 跟进时间 */
    private Date trackTime;

    /** 跟进概述 */
    private String briefIntroduction;

    /** 跟进详情 */
    private String details;

    /** 附件 */
    private String resource;

    /** 部门id */
    private Long deptId;

    /** 部门名称 */
    private String deptName;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 是否删除 */
    private Integer isDel;



}