package com.xgwc.order.service.impl;

import java.util.*;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.dao.SeasRuleMapper;
import com.xgwc.order.dao.XgwcShopMapper;
import com.xgwc.order.entity.dto.IncomLineTotalDto;
import com.xgwc.order.entity.dto.OrderDto;
import com.xgwc.order.entity.dto.SeasRuleDto;
import com.xgwc.order.entity.vo.*;
import com.xgwc.order.service.IIncomCustomerService;
import com.xgwc.order.service.IcommonService;
import com.xgwc.user.feign.api.UserDeptFeign;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.IncomLineMapper;
import com.xgwc.order.service.IIncomLineService;
import com.xgwc.order.entity.IncomLine;
import com.xgwc.order.entity.dto.IncomLineDto;


@Service
public class IncomLineServiceImpl implements IIncomLineService  {
    @Resource
    private IncomLineMapper incomLineMapper;
    @Resource
    private XgwcShopMapper xgwcShopMapper;
    @Resource
    private IIncomCustomerService incomCustomerService;
    @Resource
    private UserDeptFeign userDeptFeign;
    @Resource
    private SeasRuleMapper seasRuleMapper;
    @Autowired
    private IcommonService icommonService;
    /**
     * 查询进线管理
     * 
     * @param id 进线管理主键
     * @return 进线管理
     */
    @Override
    public IncomLineDto selectIncomLineById(Long id) {
        return incomLineMapper.selectIncomLineById(id);
    }

    /**
     * 查询进线管理列表
     * 
     * @param incomLine 进线管理
     * @return 进线管理
     */
    @Override
    public List<IncomLineDto> selectIncomLineList(IncomLineQueryVo incomLine) {
        RoleDataVo vo = icommonService.getRoleData(36);
        incomLine.setFranchiseId(vo.getFranchiseId());
        incomLine.setBrandId(vo.getBrandId());
        incomLine.setInTime(vo.getCreateTime());
        incomLine.setDeptIds(vo.getDeptIds());
        if(StringUtils.isNotEmpty(incomLine.getTel()))
            incomLine.setTel(ParamDecryptUtil.encryptPhone(incomLine.getTel()));

        Boolean isDesensitization = vo.getIsDesensitization();
        List<IncomLineDto>  incomLines = incomLineMapper.selectIncomLineList(incomLine);
        if(!isDesensitization) {
            for (IncomLineDto d : incomLines) {
                if(StringUtils.isNotBlank(d.getTel())) d.setTel(ParamDecryptUtil.decryptParam(d.getTel(), ParamDecryptUtil.PHONE_KEY));
            }
        }
        return incomLines;
    }

    @Override
    public IncomLineTotalDto selectIncomLineTotal(IncomLineQueryVo incomLine) {
        RoleDataVo vo = icommonService.getRoleData(40);
        incomLine.setFranchiseId(vo.getFranchiseId());
        incomLine.setBrandId(vo.getBrandId());
        incomLine.setInTime(vo.getCreateTime());
        incomLine.setDeptIds(vo.getDeptIds());
        return incomLineMapper.selectIncomLineTotal(incomLine);
    }

    /**
     * 新增进线管理
     * 
     * @param dto 进线管理
     * @return 结果
     */
    @Override
    public int insertIncomLine(IncomLineVo dto) {

        XgwcShopVo shop = xgwcShopMapper.getXgwcShopById(dto.getShopId());
        if(shop == null) throw new ApiException("订单来源异常，请先确定来源是否异常");
        dto.setBrandId(shop.getBrandOwnerId());
        dto.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());

        IncomLine incomLine = BeanUtil.copyProperties(dto, IncomLine.class);
        incomLine.setSaleId(SecurityUtils.getUserId());
        incomLine.setSaleName(SecurityUtils.getNickName());
        incomLine.setShopName(shop.getShopName());
        incomLine.setCreateTime(DateUtils.getNowDate());
        incomLine.setCreateBy(SecurityUtils.getNickName());
        if(StringUtils.isNotBlank(dto.getTel())){
            dto.setTel(ParamDecryptUtil.encrypt(dto.getTel(), ParamDecryptUtil.PHONE_KEY));
        }
        Long userId = SecurityUtils.getUserId();
        ApiResult result = userDeptFeign.getUserDeptByUserId(userId,"franchise");
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(result.getData()));
        JSONObject json = jsonObject.getJSONObject("data");
        if(json != null && json.getLong("deptId") == null) throw new ApiException("员工信息缺失，请完善员工信息");
        incomLine.setDeptId(json.getLong("deptId"));
        incomLine.setDeptName(json.getString("deptName"));

        //获取回收规则
        SeasRuleDto rule = seasRuleMapper.getSeasRule(dto.getBrandId());
        Date recycleTime = DateUtils.yearsLater(99);
        if(rule != null ){
            if(incomLine.getRecycleNum() == null) incomLine.setRecycleNum(0);
            if((rule.getRecycleRule() == 1 && incomLine.getRecycleNum() == 0) ||  rule.getRecycleRule() == 2) {
                incomLine.setRecycleType(0);
                recycleTime = DateUtils.addHour(new Date(),rule.getRecycleNoEff() < rule.getRecycleNoDeal()*24 ? rule.getRecycleNoEff() : rule.getRecycleNoDeal()*24);
            } else {
                incomLine.setRecycleType(1);
            }
        }

        incomLine.setRecycleTime(recycleTime);
        incomCustomerService.insertIncomCustomer("line",dto);
        return incomLineMapper.insertIncomLine(incomLine);
    }

    /**
     * 修改进线管理
     * 
     * @param dto 进线管理
     * @return 结果
     */
    @Override
    public int updateIncomLine(IncomLineVo dto) {

        IncomLine incomLine = BeanUtil.copyProperties(dto, IncomLine.class);
        incomLine.setUpdateTime(DateUtils.getNowDate());
        incomLine.setUpdateBy(SecurityUtils.getNickName());
        if(StringUtils.isNotBlank(dto.getTel())){
            dto.setTel(ParamDecryptUtil.encrypt(dto.getTel(), ParamDecryptUtil.PHONE_KEY));
        }
        String validDicCode = dto.getValidDicCode();
        IncomLineDto lineDto = incomLineMapper.selectIncomLineById(incomLine.getId());
        //获取回收规则
        SeasRuleDto rule = seasRuleMapper.getSeasRule(dto.getBrandId());
        Date recycleTime = lineDto.getRecycleTime();
        if("deal".equals(validDicCode)) recycleTime = DateUtils.yearsLater(99);
        else
            if(rule != null && StringUtils.isNotEmpty(rule.getBehaviorType()) && rule.getRecycleRule() != 3){
                //1.新增跟进,2.变更添加微信,3.补充联系电话,4.补充预算,5.更新回访时间,6.更新需求
                String[] behaviorType = rule.getBehaviorType().split(",");
                Integer anyOrAll = rule.getAnyOrAll();
                boolean flag = false;
                List<Boolean> list = new ArrayList<>();
                for(String behavior : behaviorType){
                    boolean f = false;
                    if("1".equals(behavior)){
                        if("add".equals(dto.getTrackType()))
                            f = true;
                    } else if("2".equals(behavior)){
                        if (dto.getWechatTag() == 1L && lineDto.getWechatTag() == 0L)
                            f = true;
                    } else if("3".equals(behavior)){
                        if (dto.getTel() != null && lineDto.getTel() == null)
                            f = true;
                    } else if("4".equals(behavior)){
                        if (dto.getPlanMoney() != null && dto.getPlanMoney().compareTo(lineDto.getPlanMoney()) != 0)
                            f = true;
                    } else if("5".equals(behavior)){
                        if(dto.getNextTime() != null && dto.getNextTime().compareTo(lineDto.getNextTime()) != 0)
                            f = true;
                    } else if("6".equals(behavior)){
                        if(StringUtils.isNotEmpty(dto.getNeedInfo()) && !dto.getNeedInfo().equals(lineDto.getNeedInfo()))
                            f = true;
                    }
                    if(f){
                        flag = true;
                        if(anyOrAll == 1) {
                            break;
                        }
                        list.add(true);
                    }
                }
                if(anyOrAll == 2) {
                    for (Boolean b : list) {
                        if(!b) flag = false;
                    }
                }
                if(flag) {
                    Date dealTime = DateUtils.addHour(lineDto.getCreateTime(), rule.getRecycleNoDeal()*24);
                    recycleTime = DateUtils.addHour(recycleTime,rule.getRecycleNoEff());
                    if(dealTime.compareTo(recycleTime) < 0) recycleTime = dealTime;
                }
            }
        incomLine.setRecycleTime(recycleTime);
        return incomLineMapper.updateIncomLine(incomLine);
    }

    @Override
    public int transferIncomLine(IncomLineTransferVo dto) {
        for(Long id: dto.getIds()) {
            IncomLine incomLine = BeanUtil.copyProperties(dto, IncomLine.class);
            incomLine.setId(id);
            incomLine.setUpdateTime(DateUtils.getNowDate());
            incomLineMapper.updateIncomLine(incomLine);
        }
        return 1;
    }


    /**
     * 批量删除进线管理
     * 
     * @param ids 需要删除的进线管理主键
     * @return 结果
     */
    @Override
    public int deleteIncomLineByIds(Long[] ids) {
        return incomLineMapper.deleteIncomLineByIds(ids);
    }

    /**
     * 删除进线管理信息
     * 
     * @param id 进线管理主键
     * @return 结果
     */
    @Override
    public int deleteIncomLineById(Long id) {
        return incomLineMapper.deleteIncomLineById(id);
    }
}
