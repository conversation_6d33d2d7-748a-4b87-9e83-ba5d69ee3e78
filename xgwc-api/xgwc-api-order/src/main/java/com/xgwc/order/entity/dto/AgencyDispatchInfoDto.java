package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class AgencyDispatchInfoDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("派单Id")
    @Excel(name = "派单Id")
    private Long dispatchId;

    @FieldDesc("订单Id")
    @Excel(name = "订单Id")
    private Long orderId;

    @FieldDesc("领取的设计师数量")
    @Excel(name = "领取的设计师数量")
    private Integer dispatchNumber;

    @FieldDesc("已分配数量")
    @Excel(name = "已分配数量")
    private Integer assignedQuantity;

    @FieldDesc("派单备注")
    @Excel(name = "派单备注")
    private String dispatchRemark;

    @FieldDesc("设计师Id")
    @Excel(name = "设计师Id")
    private Long designerId;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private String orderDate;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String taobaoId;

    @FieldDesc("谈单人员id")
    @Excel(name = "谈单人员id")
    private Long saleManId;

    @FieldDesc("谈单人员名称")
    @Excel(name = "谈单人员名称")
    private String saleManName;

    @FieldDesc("加盟商名称")
    @Excel(name = "加盟商名称")
    private String franchiseName;

    @FieldDesc("店铺名称")
    @Excel(name = "店铺名称")
    private String storeName;

    @FieldDesc("业务id")
    @Excel(name = "业务id")
    private Long stateDicCode;

    @FieldDesc("业务名称")
    @Excel(name = "业务名称")
    private String stateDicName;

    @FieldDesc("订单金额")
    @Excel(name = "订单金额")
    private BigDecimal orderAmount;

    @FieldDesc("实收金额")
    @Excel(name = "实收金额")
    private BigDecimal amount;

    @FieldDesc("期望初稿日期：yyyy-MM-dd HH:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "期望初稿日期：yyyy-MM-dd HH:00:00", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveExpectTime;

    @FieldDesc("派单需求")
    @Excel(name = "派单需求")
    private String allotRemark;

    @FieldDesc("协助文件")
    @Excel(name = "协助文件")
    private String allotFile;

    public List<SubOrderDto> subOrderDtoList;

}
