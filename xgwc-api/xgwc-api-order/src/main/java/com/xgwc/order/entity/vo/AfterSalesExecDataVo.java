package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class AfterSalesExecDataVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("业务id")
    private Long businessId;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("参数名")
    private String key;

    @FieldDesc("参数值")
    private String value;



}
