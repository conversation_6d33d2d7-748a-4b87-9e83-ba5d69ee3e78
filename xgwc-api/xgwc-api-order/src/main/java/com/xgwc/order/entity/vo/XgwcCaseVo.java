package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

import java.math.BigDecimal;

@Data
public class XgwcCaseVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("实收金额")
    private BigDecimal amount;

    @FieldDesc("上传定稿源文件")
    private String archiveFiles;

    @FieldDesc("定稿预览图")
    private String archiveImg;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("业务id")
    private Long businessId;

    @FieldDesc("业务名称")
    private String businessName;

    @FieldDesc("主键")
    private Long caseId;

    @FieldDesc("等级：1高 2中 3低")
    private Integer caseLevel;

    @FieldDesc("设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    private String designerName;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("关键字")
    private String keyword;

    @FieldDesc("链接")
    private String linkUrl;

    @FieldDesc("佣金金额")
    private BigDecimal money;

    @FieldDesc("订单id")
    private Long oderId;

    @FieldDesc("标题")
    private String title;

    @FieldDesc("下载次数")
    private Integer downloadCount;

    private Long archiveId;

}
