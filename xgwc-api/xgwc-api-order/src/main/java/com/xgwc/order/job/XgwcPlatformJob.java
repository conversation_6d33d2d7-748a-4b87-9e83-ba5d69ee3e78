package com.xgwc.order.job;

import cn.hutool.core.thread.ThreadUtil;
import com.google.common.collect.Sets;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.XgwcPlatformConfigMapper;
import com.xgwc.order.entity.XgwcPlatformConfig;
import com.xgwc.order.entity.XgwcPlatformDataTypeEnum;
import com.xgwc.order.entity.XgwcPlatformGoods;
import com.xgwc.order.entity.XgwcPlatformGoodsSpec;
import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.XgwcPlatformRefundDetails;
import com.xgwc.order.entity.XgwcPlatformShop;
import com.xgwc.order.entity.XgwcPlatformTrade;
import com.xgwc.order.entity.XgwcPlatformTradeDetails;
import com.xgwc.order.entity.vo.Second;
import com.xgwc.order.service.IXgwcPlatformGoodsService;
import com.xgwc.order.service.IXgwcPlatformRefundService;
import com.xgwc.order.service.IXgwcPlatformShopService;
import com.xgwc.order.service.IXgwcPlatformTradeService;
import com.xgwc.order.service.XgwcPlatformService;
import com.xgwc.order.util.WdtClient;
import com.xgwc.redis.util.RedisUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class XgwcPlatformJob {

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private XgwcPlatformService xgwcPlatformService;
    @Resource
    private IXgwcPlatformShopService xgwcPlatformShopService;
    @Resource
    private IXgwcPlatformGoodsService xgwcPlatformGoodsService;
    @Resource
    private IXgwcPlatformTradeService xgwcPlatformTradeService;
    @Resource
    private IXgwcPlatformRefundService xgwcPlatformRefundService;
    @Resource
    private XgwcPlatformConfigMapper xgwcPlatformConfigMapper;

    /**
     * 周期更新平台店铺
     */
    public void cycleUpdatePlatformShop() {
        this.updatePlatformData(Sets.newHashSet(), XgwcPlatformDataTypeEnum.shopData);
    }

    /**
     * 周期更新平台货品
     */
    public void cycleUpdatePlatformGoods() {
        this.updatePlatformData(Sets.newHashSet(), XgwcPlatformDataTypeEnum.goodsData);
    }

    /**
     * 周期更新平台交易单
     */
    public void cycleUpdatePlatformTrade() {
        this.updatePlatformData(Sets.newHashSet(), XgwcPlatformDataTypeEnum.tradeData);
    }

    /**
     * 周期更新平台售后单
     */
    public void cycleUpdatePlatformRefund() {
        this.updatePlatformData(Sets.newHashSet(), XgwcPlatformDataTypeEnum.refundData);
    }

    private void saveShopData(WdtClient wdtClient, Long brandOwnerId) throws IOException {
        List<XgwcPlatformShop> shopList = xgwcPlatformService.pullShop(wdtClient, brandOwnerId);
        xgwcPlatformShopService.saveXgwcPlatformShop(shopList, brandOwnerId);
    }

    private void saveGoodsData(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException {
        Second<List<XgwcPlatformGoods>, List<XgwcPlatformGoodsSpec>> goodsData = xgwcPlatformService.pullGoods(startTime, endTime, wdtClient, brandOwnerId);
        xgwcPlatformGoodsService.saveXgwcPlatformGoods(goodsData.getFirst(), goodsData.getSecond(), brandOwnerId);
    }

    private void saveTradeData(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException {
        Second<List<XgwcPlatformTrade>, List<XgwcPlatformTradeDetails>> tradeData = xgwcPlatformService.pullTrade(startTime, endTime, wdtClient, brandOwnerId);
        xgwcPlatformTradeService.saveXgwcPlatformTrade(tradeData.getFirst(), tradeData.getSecond(), brandOwnerId);
    }

    private void saveRefundData(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException {
        Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> refundData = xgwcPlatformService.pullRefund(startTime, endTime, wdtClient, brandOwnerId);
        xgwcPlatformRefundService.saveXgwcPlatformRefund(refundData.getFirst(), refundData.getSecond(), brandOwnerId);
    }

    /**
     * @param brandOwnerIdSet 需要拉取的品牌商id集合
     * @param dataType        拉取数据的类型
     */
    public void updatePlatformData(Set<Long> brandOwnerIdSet, XgwcPlatformDataTypeEnum dataType) {
        List<XgwcPlatformConfig> configList = xgwcPlatformConfigMapper.selectXgwcPlatformConfigList(brandOwnerIdSet.stream().toList());
        if (configList.isEmpty()) {
            log.error("缺少旺店通配置, 请检查... {}", dataType);
            return;
        }
        if (!brandOwnerIdSet.isEmpty()) {
            if (brandOwnerIdSet.size() != configList.size()) {
                Set<Long> dbConfigList = configList.stream().map(XgwcPlatformConfig::getId).collect(Collectors.toSet());
                log.warn("以下品牌商id缺少旺店通配置: {} --- {}", dataType, brandOwnerIdSet.stream().filter(dbConfigList::contains).collect(Collectors.toList()));
            }
        }

        log.info("开始拉取旺店通{}数据, 涉及{}个品牌商", dataType, configList.size());
        configList.forEach(config -> {
            try {
                WdtClient wdtClient = new WdtClient(config);
                if (XgwcPlatformDataTypeEnum.shopData.equals(dataType)) {
                    this.saveShopData(wdtClient, config.getId());
                } else {
                    String redisKey = XgwcPlatformConfig.getRedisKey(config.getId(), dataType);
                    String redisValue = redisUtil.get(redisKey);
                    if (StringUtils.isEmpty(redisValue)) {
                        log.error("{}缺少{}初始化开始时间, 请检查... ", config.getId(), dataType);
                        return;
                    }
                    Date startTime = DateUtils.parseDate(redisValue);
                    Date endTime = new Date();
                    log.info("----------开始拉取{}的{}, startTime={}, endTime={}", config.getId(), dataType, DateUtils.formatDateTime(startTime), DateUtils.formatDateTime(endTime));
                    if (XgwcPlatformDataTypeEnum.goodsData.equals(dataType)) {
                        this.saveGoodsData(startTime, endTime, wdtClient, config.getId());
                    } else if (XgwcPlatformDataTypeEnum.tradeData.equals(dataType)) {
                        this.saveTradeData(startTime, endTime, wdtClient, config.getId());
                    } else if (XgwcPlatformDataTypeEnum.refundData.equals(dataType)) {
                        this.saveRefundData(startTime, endTime, wdtClient, config.getId());
                    } else {
                        throw new ApiException("异常的业务数据类型" + dataType);
                    }
                    redisUtil.set(redisKey, DateUtils.formatDateTime(endTime));
                    log.info("----------拉取保存{}的{}完毕, 耗时:{}", config.getId(), dataType, new Date().getTime() - endTime.getTime());
                }
            } catch (Exception e) {
                log.error("拉取品牌商{}的{}数据出现异常", config.getId(), dataType, e);
            }
            ThreadUtil.sleep(1000);
        });
    }

}