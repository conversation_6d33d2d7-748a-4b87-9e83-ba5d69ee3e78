package com.xgwc.order.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单退款申请
 */
@Data
public class OrderRefundApplyVo {

    /** 主键 */
    private Long id;

    /** 1.全额退款，2.部分退款，3.退垫付 */
    @NotNull(message = "退款类型不能为空")
    private Integer refundType;

    /** 退款原因code, 字典配置 */
    @NotNull(message = "退款原因字典码不能为空")
    private String refundReasonCode;

    /** 退款原因 */
    @NotNull(message = "退款原因不能为空")
    private String refundReason;

    /**
     * 订单号对应ID
     */
    private Long preId;

    /** 订单编号 */
    @NotNull(message = "订单ID不能为空")
    private String orderNo;

    /** 退款说明 */
    private String remark;

    /** 申请人ID */
    private Long applyUserId;

    /** 申请人用户ID */
    private String applyUserName;

    /**
     * 退款json
     */
    @NotNull(message = "退款参数不能为空")
    private String refundPayJson;

    /*
    佣金json
     */
    @NotNull(message = "佣金参数不能为空")
    private String refundCommisionJson;

    /**
     * 退垫付Json
     */
    private String refundAdvanceJson;

    /**
     * 最后金额
     */
    private BigDecimal lastAmount;

    /**
     * 原金额
     */
    private BigDecimal preAmount;

    /**
     * 原佣金
     */
    private BigDecimal preCommision;

    /**
     * 最后的佣金
     */
    private BigDecimal lastCommision;

    /** 加盟商ID */
    private Long franchiseId;

    /** 品牌商ID */
    private Long brandId;

    /** 最后审核时间 */
    private String lastApplyTime;

    /** 审核状态：值由审批流定 */
    private String applyStatus;

    /** 状态：0正常，1非正常 */
    private Integer status;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /** 是否开启千牛：0关闭，1开启 */
    private Integer isOpenQianniu;

    /** 流程实例Id */
    private Long executionId;

    /** 客户手机号 */
    private String customerPhone;

    /** 是否有需线下退款：0 否，1 是 */
    private Integer isFinanceCheck;

    /** 是否垫付：0 否，1 是 */
    private Integer isAdvance;

    /** 平台发起退款时间(多个子单一起退时取最早的) */
    private Date platformRefundTime;

    private String orderDate;

    private String customerNo;

    private String saleManName;
}
