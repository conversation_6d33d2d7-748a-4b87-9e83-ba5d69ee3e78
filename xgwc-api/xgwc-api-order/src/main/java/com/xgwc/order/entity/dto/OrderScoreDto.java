package com.xgwc.order.entity.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class OrderScoreDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("评分id")
    @Excel(name = "评分id")
    private Long id;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long oderId;

    @FieldDesc("交稿id")
    @Excel(name = "交稿id")
    private Long archiveId;

    @FieldDesc("设计师ID")
    @Excel(name = "设计师ID")
    private Long designerId;

    @FieldDesc("评分模块")
    @Excel(name = "评分模块")
    private String scoreModel;

    @FieldDesc("评分分值")
    @Excel(name = "评分分值")
    private Long scoreNum;

    @FieldDesc("评分分类")
    private Long type;

    @FieldDesc("评论人")
    private String createBy;

    @FieldDesc("评分项")
    @Excel(name = "评分项")
    private List<String> scoreItem;

    public void setScoreItem(String scoreItem) {
        if(StringUtils.isNotBlank(scoreItem)){
            this.scoreItem =  Arrays.asList(scoreItem.split(","));
        } else {
            this.scoreItem = new ArrayList<>();
        }
    }

}
