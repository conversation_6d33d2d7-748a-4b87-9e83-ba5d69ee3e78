package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@Builder
public class BrandNightPerformanceConfigQueryVo {

    @FieldDesc("品牌商ID")
    private Long brandId;

    @FieldDesc("加盟商ID")
    private Long franchiseeId;

    @FieldDesc("加盟商部门ID")
    private Long departmentId;
    @FieldDesc("部门ID")
    private List<Long> departmentIds;

}