package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DesignerStatisticsDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("平均佣金")
    @Excel(name = "平均佣金")
    private BigDecimal avgMoney;

    @FieldDesc("所属品牌商id")
    private Long brandId;

    @FieldDesc("所属品牌商名称")
    @Excel(name = "所属品牌商")
    private String brandName;

    @FieldDesc("擅长业务（业务类型编码）")
    private Long businessId;

    @FieldDesc("擅长业务（业务类型名称）")
    @Excel(name = "擅长业务（业务类型名称）")
    private String businessName;

    @FieldDesc("设计师id")
    @Excel(name = "设计师id")
    private Long designerId;

    @FieldDesc("设计师等级")
    @Excel(name = "设计师等级")
    private String designerLevel;

    @FieldDesc("设计师名称")
    @Excel(name = "设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    @Excel(name = "设计师电话")
    private String designerPhone;

    @FieldDesc("企业微信")
    @Excel(name = "企业微信")
    private String designerWechat;

    @FieldDesc("跟进说明")
    @Excel(name = "跟进说明")
    private String details;

    @FieldDesc("特长描述")
    private String description;

    @FieldDesc("紧急联系人")
    @Excel(name = "紧急联系人")
    private String emergencyName;

    @FieldDesc("紧急手机号")
    @Excel(name = "紧急手机号")
    private String emergencyPhone;

    @FieldDesc("近一年好评率")
    @Excel(name = "近一年好评率")
    private BigDecimal goodNum;

    @FieldDesc("历史订单")
    @Excel(name = "历史订单")
    private String hisOrderBusiness;

    @FieldDesc("未交稿订单数")
    @Excel(name = "未交稿订单数")
    private Long noArchive;

    @FieldDesc("最后接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后接单时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderLastTime;

    @FieldDesc("累计接单量")
    @Excel(name = "累计接单量")
    private Long orderNum;

    @FieldDesc("近一年接单量")
    @Excel(name = "近一年接单量")
    private Long orderYearNum;

    @FieldDesc("近一年退款率")
    @Excel(name = "近一年退款率")
    private BigDecimal refundNum;

    @FieldDesc("累计评分")
    private List<DesignerStatisticsScoreDto> scores;
    @FieldDesc("近一年评分")
    private List<DesignerStatisticsScoreDto> yearScores;

    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("avgMoney",getAvgMoney())
            .append("brandId",getBrandId())
            .append("businessId",getBusinessId())
            .append("businessName",getBusinessName())
            .append("designerId",getDesignerId())
            .append("designerLevel",getDesignerLevel())
            .append("designerName",getDesignerName())
            .append("designerPhone",getDesignerPhone())
            .append("designerWechat",getDesignerWechat())
            .append("details",getDetails())
            .append("emergencyName",getEmergencyName())
            .append("emergencyPhone",getEmergencyPhone())
            .append("goodNum",getGoodNum())
            .append("hisOrderBusiness",getHisOrderBusiness())
            .append("noArchive",getNoArchive())
            .append("orderLastTime",getOrderLastTime())
            .append("orderNum",getOrderNum())
            .append("orderYearNum",getOrderYearNum())
            .append("refundNum",getRefundNum())
        .toString();
    }
}
