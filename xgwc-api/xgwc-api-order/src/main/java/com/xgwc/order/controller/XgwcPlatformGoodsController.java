package com.xgwc.order.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.order.entity.dto.XgwcPlatformGoodsDto;
import com.xgwc.order.entity.vo.XgwcPlatformGoodsQueryVo;
import com.xgwc.order.entity.vo.XgwcPlatformGoodsVo;
import com.xgwc.order.service.IXgwcPlatformGoodsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/xgwcPlatformGoods")
public class XgwcPlatformGoodsController extends BaseController {

    @Autowired
    private IXgwcPlatformGoodsService xgwcPlatformGoodsService;

    /**
     * 查询品牌商商品表列表
     */
    @MethodDesc("查询品牌商商品表列表")
    @PreAuthorize("@ss.hasPermission('order:platformGoods:list')")
    @GetMapping("/list")
    public ApiResult<XgwcPlatformGoodsDto> list(XgwcPlatformGoodsQueryVo xgwcPlatformGoods) {
        List<XgwcPlatformGoodsDto> list = xgwcPlatformGoodsService.selectXgwcPlatformGoodsList(xgwcPlatformGoods);
        return getDataTable(list);
    }

    /**
     * 获取品牌商商品表详细信息
     */
    @MethodDesc("获取品牌商商品表详细信息")
    @GetMapping(value = "/{goodsId}")
    @PreAuthorize("@ss.hasPermission('order:platformGoods:view')")
    public ApiResult<XgwcPlatformGoodsDto> getInfo(@PathVariable("goodsId") String goodsId) {
        return success(xgwcPlatformGoodsService.selectByGoodsId(goodsId));
    }


    /**
     * 修改品牌商商品表
     */
    @MethodDesc("修改品牌商商品表")
    @Log(title = "品牌商商品表", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermission('order:platformGoods:edit')")
    @PutMapping
    public ApiResult edit(@RequestBody @Valid XgwcPlatformGoodsVo xgwcPlatformGoods) {
        return toAjax(xgwcPlatformGoodsService.updateXgwcPlatformGoods(xgwcPlatformGoods));
    }


}
