package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class IncomLine {

private static final long serialVersionUID=1L;

    /** 编号 */
    private Long id;

    /** 进线时间 */
    private Date inTime;

    /** 客户ID */
    private String taobaoId;

    /** 添加微信0否 1是 */
    private Long wechatTag;

    /** 联系电话 */
    private String tel;

    /** 来源店铺id-店铺查询 */
    private Long shopId;

    /** 来源店铺名称-店铺查询 */
    private String shopName;

    /** 有效性编号-字典 */
    private String validDicCode;

    /** 有效性名称-字典 */
    private String validDicName;

    /** 有效性编号-字典 */
    private String validDicCode2;

    /** 有效性名称-字典 */
    private String validDicName2;

    /** 关联单号id-订单查询 */
    private Long orderId;

    /** 关联单号编号-订单查询 */
    private String orderNo;

    /** 所属业务编号-字典 */
    private String stateDicCode;

    /** 所属业务名称-字典 */
    private String stateDicName;


    /** 回访时间 */
    private Date nextTime;

    /** 需求 */
    private String needInfo;

    /** 销售ID */
    private Long saleId;

    /** 销售名称 */
    private String saleName;

    /** 部门id */
    private Long deptId;

    /** 部门姓名 */
    private String deptName;

    /** 跟进状态 */
    private String trackName;
    private String trackCode;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 是否删除 */
    private Integer isDel;

    /** 品牌商id */
    private Long brandId;

    /** 加盟商id */
    private Long franchiseId;

    /** 预算 */
    private BigDecimal planMoney;

    /** 回收类型:0回收 1不回收 */
    private Integer recycleType;

    /** 回收时间 */
    private Date recycleTime;

    /** 回收次数 */
    private Integer recycleNum;

    /** 领取人 */
    private String receiver;

    /** 领取时间 */
    private Date receiveTime;
}