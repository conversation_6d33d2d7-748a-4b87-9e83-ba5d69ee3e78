package com.xgwc.order.service.impl;

import com.google.common.collect.ImmutableList;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.XgwcPlatformConfigMapper;
import com.xgwc.order.entity.XgwcPlatformConfig;
import com.xgwc.order.entity.XgwcPlatformDataTypeEnum;
import com.xgwc.redis.util.RedisUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class XgwcPlatformConfigService {

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private XgwcPlatformConfigMapper xgwcPlatformConfigMapper;

    public XgwcPlatformConfig getCurrentConfig() {
        if (!SecurityUtils.getSysUser().isBrandUser()) {
            throw new ApiException("非品牌商用户不能操作");
        }
        return this.getByBrandId(SecurityUtils.getSysUser().getBrandId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(XgwcPlatformConfig config) {
        if (!SecurityUtils.getSysUser().isBrandUser()) {
            throw new ApiException("非品牌商用户不能操作");
        }
        if (config.getStartTime().getTime() > new Date().getTime()) {
            throw new ApiException("订单同步起始时间不能大于当前时间");
        }
        Date lastTime = DateUtils.addYear(-1);
        if (config.getStartTime().getTime() < DateUtils.addYear(-1).getTime()) {
            throw new ApiException("订单同步起始时间不能早于一年之前" + DateUtils.formatDate(lastTime));
        }
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        XgwcPlatformConfig dbConfig = this.getByBrandId(brandId);
        if (dbConfig != null) {
            config.setId(dbConfig.getId());
            xgwcPlatformConfigMapper.update(config);
        } else {
            config.setId(brandId);
            xgwcPlatformConfigMapper.insert(config);
        }
        for (XgwcPlatformDataTypeEnum dataType : XgwcPlatformDataTypeEnum.values()) {
            if (!XgwcPlatformDataTypeEnum.shopData.equals(dataType)) {
                String redisKey = XgwcPlatformConfig.getRedisKey(config.getId(), dataType);
                redisUtil.set(redisKey, DateUtils.formatDateTime(config.getStartTime()));
            }
        }
    }


    private XgwcPlatformConfig getByBrandId(Long brandId) {
        List<XgwcPlatformConfig> platformConfigList = xgwcPlatformConfigMapper.selectXgwcPlatformConfigList(ImmutableList.of(brandId));
        if (!platformConfigList.isEmpty()) {
            return platformConfigList.get(0);
        }
        return null;
    }

}