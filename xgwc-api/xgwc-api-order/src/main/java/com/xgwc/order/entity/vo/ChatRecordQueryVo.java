package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class ChatRecordQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌id")
    private Long brandId;

    @FieldDesc("分类")
    private String classify;

    @FieldDesc("开始时间")
    private String startTime;

    @FieldDesc("结束时间")
    private String endTime;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    private Long isDel;

    @FieldDesc("状态：0正常，1禁用")
    private Long status;

    @FieldDesc("聊天标题")
    private String title;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页条数
     */
    private Integer pageSize;

}
