package com.xgwc.order.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.xgwc.order.feign.entity.ShopFranchiseVo;
import com.xgwc.order.feign.entity.XgwcShopFranchiseBusinessDto;
import com.xgwc.order.feign.entity.XgwcShopFranchiseBusinessVo;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.xgwc.order.feign.entity.XgwcShopFranchiseDto;
import com.xgwc.order.entity.vo.XgwcShopFranchiseQueryVo;
import com.xgwc.order.service.IXgwcShopFranchiseService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/shop/franchise")
public class XgwcShopFranchiseController extends BaseController {
    @Autowired
    private IXgwcShopFranchiseService xgwcShopFranchiseService;

    @MethodDesc("查询加盟商店铺列表")
    @GetMapping("/list")
    public ApiResult<XgwcShopFranchiseDto> list(XgwcShopFranchiseQueryVo xgwcShopFranchise) {
        startPage();
        List<XgwcShopFranchiseDto> list = xgwcShopFranchiseService.selectXgwcShopFranchiseList(xgwcShopFranchise);
        return getDataTable(list);
    }

    @MethodDesc("查询加盟商店铺业务分类列表")
    @GetMapping("/shopFranchiseBusiness")
    public ApiResult<XgwcShopFranchiseBusinessDto> shopFranchiseBusiness(@RequestParam(name = "userId", required = false) Long userId) {
        List<XgwcShopFranchiseBusinessDto> list = xgwcShopFranchiseService.shopFranchiseBusiness(userId);
        if(list == null) list = new ArrayList<>();
        return success(list);
    }

    @MethodDesc("获取加盟商店铺详细信息")
    @GetMapping(value = "/{shopId}")
    public ApiResult<XgwcShopFranchiseDto> getInfo(@PathVariable("shopId") Long shopId) {
        return success(xgwcShopFranchiseService.selectXgwcShopFranchiseByShopId(shopId));
    }

    @MethodDesc("编辑经营业务")
    @PostMapping(value = "/update/business")
    public ApiResult<Objects> updateBusiness(@RequestBody List<XgwcShopFranchiseBusinessVo> business) {
        return success(xgwcShopFranchiseService.updateBusiness(business));
    }
}
