package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class OrderRefundCommision {


    /** 主键 */
    private Long id;

    /**
     * 原ID
     */
    private Long preId;

    /**
     * 申请ID
     */
    private Long applyId;

    /** 订单编号 */
    private String subOrderNo;

    /** 设计师ID */
    private Long designerUserId;

    /** 设计师名称 */
    private String designerUserName;

    /** 设计师业务类型 */
    private String designerBusiness;

    /** 设计师手机号 */
    private String designerPhone;

    /** 佣金 */
    private BigDecimal commisionAmount;

    /** 修改后佣金 */
    private BigDecimal afterCommisionAmount;

    /** 状态：0正常，1非正常 */
    private Integer status;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /**
     * 创建者
     */
    private String createBy;


}