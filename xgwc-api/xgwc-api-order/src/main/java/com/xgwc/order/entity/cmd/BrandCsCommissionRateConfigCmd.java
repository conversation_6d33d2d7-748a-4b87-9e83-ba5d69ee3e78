package com.xgwc.order.entity.cmd;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class BrandCsCommissionRateConfigCmd extends BrandCsCommissionRateConfigDetailCmd{

    @NotNull
    @FieldDesc("配置类型: 1 基础, 2 团队负责人, 3 单人")
    private Long type;

    @NotNull
    @FieldDesc("加盟商ID")
    private List<Long> franchiseeIds;

    @NotBlank
    @FieldDesc("档位名称")
    private String gearName;

    @FieldDesc("加盟商员工ID")
    private List<Long> staffIds;

    @FieldDesc("加盟商部门ID")
    private List<Long> deptIds;

    @NotNull
    @FieldDesc("业务ID")
    private List<Long> businessIds;

}