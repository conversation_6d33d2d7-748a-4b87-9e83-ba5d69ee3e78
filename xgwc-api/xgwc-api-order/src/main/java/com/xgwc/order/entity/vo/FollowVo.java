package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

@Data
public class FollowVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键ID")
    private Long id;

    @FieldDesc("售后待审核ID：xgwc_after_agency_audit主键")
    private Long agencyAuditId;

    @FieldDesc("跟进人Id")
    private Long followUserId;

    @FieldDesc("协商状态:1待协商/2协商中/3协商成功/4协商失败")
    private Integer consultType;

    @FieldDesc("跟进说明")
    private String remark;

    @FieldDesc("回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnTime;

}
