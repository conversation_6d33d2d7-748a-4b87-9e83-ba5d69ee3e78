package com.xgwc.order.entity.cmd;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.order.entity.CsCommissionRateConfigRange;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class BrandCsCommissionRateConfigDetailCmd {

    @NotNull
    @FieldDesc("生效周期类型: 1 长期有效, 2 指定时间")
    private Long effectiveCycleType;

    @FieldDesc("生效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @NotNull
    @FieldDesc("是否新业务(0:否 1:是)")
    private Long isNewBusiness;

    @FieldDesc("新业务额外前x月")
    private Long extraMonthCount;

    @FieldDesc("额外类型: 1本月生效, 2次月生效")
    private Long extraType;

    @FieldDesc("额外加x比例")
    private BigDecimal extraCommissionRate;

    @FieldDesc("是否包含设定区间最小值")
    private Long isContainsMin;

    @Valid
    @NotNull
    @NotEmpty
    @FieldDesc("区间配置")
    private List<CsCommissionRateConfigRange> rangeList;

}