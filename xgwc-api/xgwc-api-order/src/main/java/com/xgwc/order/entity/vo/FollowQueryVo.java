package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class FollowQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("售后待审核ID：xgwc_after_agency_audit主键")
    private Long agencyAuditId;

    @FieldDesc("协商状态:1待协商/2协商中/3协商成功/4协商失败")
    private Integer consultType;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @FieldDesc("创建时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @FieldDesc("主键ID")
    private Long id;

    @FieldDesc("跟进说明")
    private String remark;

    @FieldDesc("回访时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnTimeStart;

    @FieldDesc("回访时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnTimeEnd;

    @FieldDesc("状态：0正常，1非正常")
    private Long status;

    @FieldDesc("更新时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeStart;

    @FieldDesc("更新时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeEnd;



}
