package com.xgwc.order.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class IncomCustomerDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("编号")
    @Excel(name = "编号")
    private Long id;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String taobaoId;

    @FieldDesc("客户等级-字典")
    @Excel(name = "客户等级-字典")
    private String taobaoLv;

    @FieldDesc("联系电话")
    @Excel(name = "联系电话")
    private String tel;

    @FieldDesc("最后一次跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后一次跟进时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastTime;

    @FieldDesc("跟进结论")
    @Excel(name = "跟进结论")
    private String trackName;

    @FieldDesc("进线次数")
    @Excel(name = "进线次数")
    private Long lineNum;

    @FieldDesc("跟进数")
    @Excel(name = "跟进数")
    private Long tarckNum;

    @FieldDesc("订单总数")
    @Excel(name = "订单总数")
    private Long orderNum;

    @FieldDesc("订单总额")
    @Excel(name = "订单总额")
    private BigDecimal amount;

    @FieldDesc("客单价")
    @Excel(name = "订单总数")
    private BigDecimal unitPrice;

    @FieldDesc("添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "添加时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addTime;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("品牌商名字")
    private String brandName;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("性别")
    private String sex;

    @FieldDesc("是否添加微信：0否 1是")
    private Long wechatTag;

    @FieldDesc("详细地址")
    private String address;

    @FieldDesc("省")
    private String province;

    @FieldDesc("市")
    private String city;

    @FieldDesc("区")
    private String area;

    @FieldDesc("首次添加人")
    private String createBy;

    @FieldDesc("添加时间")
    private Date createTime;

    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("taobaoId",getTaobaoId())
            .append("taobaoLv",getTaobaoLv())
            .append("tel",getTel())
            .append("lastTime",getLastTime())
            .append("trackName",getTrackName())
            .append("lineNum",getLineNum())
            .append("tarckNum",getTarckNum())
            .append("orderNum",getOrderNum())
        .toString();
    }
}
