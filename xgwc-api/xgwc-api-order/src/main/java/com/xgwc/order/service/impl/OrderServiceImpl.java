package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.OrderStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.*;
import com.xgwc.order.entity.*;
import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.DispatchVo;
import com.xgwc.order.entity.vo.IncomLineVo;
import com.xgwc.order.entity.vo.OrderArchiveVo;
import com.xgwc.order.entity.vo.OrderDesignerUpdateVo;
import com.xgwc.order.entity.vo.OrderDesignerVo;
import com.xgwc.order.entity.vo.OrderHistoryQueryVo;
import com.xgwc.order.entity.vo.OrderPlanVo;
import com.xgwc.order.entity.vo.OrderQueryVo;
import com.xgwc.order.entity.vo.OrderScoreVo;
import com.xgwc.order.entity.vo.OrderVo;
import com.xgwc.order.entity.vo.RoleDataVo;
import com.xgwc.order.entity.vo.XgwcCaseVo;
import com.xgwc.order.entity.vo.XgwcShopVo;
import com.xgwc.order.service.IDispatchService;
import com.xgwc.order.service.IIncomCustomerService;
import com.xgwc.order.service.IOrderService;
import com.xgwc.order.service.IXgwcCaseService;
import com.xgwc.order.service.IcommonService;
import com.xgwc.order.util.OrderUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.SerializationUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class OrderServiceImpl implements IOrderService  {
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderPayMapper orderPayMapper;
    @Resource
    private OrderPlanMapper orderPlanMapper;
    @Resource
    private OrderArchiveMapper orderArchiveMapper;
    @Resource
    private OrderArchiveLogMapper orderArchiveLogMapper;
    @Resource
    private OrderScoreMapper orderScoreMapper;
    @Resource
    private XgwcShopMapper xgwcShopMapper;
    @Resource
    private XgwcPlatformTradeMapper xgwcPlatformTradeMapper;
    @Resource
    private IDispatchService dispatchService;
    @Resource
    private XgwcCaseMapper xgwcCaseMapper;
    @Resource
    private IIncomCustomerService incomCustomerService;
    @Resource
    private IXgwcCaseService xgwcCaseService;
    @Resource
    private BillDesignerMapper billDesignerMapper;
    @Resource
    private OrderExpensesApplyMapper orderExpensesApplyMapper;
    @Resource
    private OrderInvoiceApplyMapper orderInvoiceApplyMapper;
    /**
     * 订单制作中
     */
    public static final Integer InProduction = 1;

    private static final Integer ReviewArchive = 3;
    @Autowired
    private IcommonService icommonService;

    /**
     * 查询订单管理
     *
     * @param id 订单管理主键
     * @return 订单管理
     */
    @Override
    public OrderDto selectOrderById(Long id) {
        OrderDto orderDto = orderMapper.selectOrderById(id);
        if(orderDto==null){
            return null;
        }
        Long pid = orderDto.getPid();
        if(pid == 0) {
            pid = orderDto.getId();
            List<SubOrderDto> subOrders = orderMapper.selectSubOrderList(orderDto.getId());
            if(orderDto.getDesignerId() != null) subOrders.add(0,BeanUtil.copyProperties(orderDto, SubOrderDto.class));
            orderDto.setSubOrders(subOrders);
        } else {
            SubOrderDto dto = BeanUtil.copyProperties(orderDto, SubOrderDto.class);
            List<SubOrderDto> subOrders = new ArrayList<>();
            subOrders.add(dto);
            orderDto.setSubOrders(subOrders);
        }
        List<OrderPayDto> orderPays = orderPayMapper.selectOrderPayByOrderId(pid);
        //如果是全款付 update by yangzp 2025-06-13
        if(orderDto.getPayType() != null && orderDto.getPayType() == 1L){
            if(orderPays != null && !orderPays.isEmpty()){
                orderDto.setPayImg(orderPays.get(0).getPayImg());
                orderDto.setCollectionNo(orderPays.get(0).getCollectionNo());
            }
        }else {
            orderDto.setOrderPays(orderPays);
        }
        if(orderDto.getAllotType() == 1){
            DispatchDto dispatchDto = dispatchService.selectDispatchByOrderId(orderDto.getId());
            if(dispatchDto != null) {
                orderDto.setNeedDispatchNumber(dispatchDto.getNeedDispatchNumber());
            }
        }
        return orderDto;
    }

    @Override
    public List<OrderTrace> selectOrderTraceById(Long id) {
        OrderDto orderDto = orderMapper.selectOrderById(id);
        if(orderDto==null){
            throw new ApiException("订单不存在");
        }
        Long pid = orderDto.getPid();

        return orderMapper.selectOrderTraceById(pid > 0 ? pid : id);
    }

    private List<Long> getBrandOwnerIdList() {
        String brandIds = SecurityUtils.getSysUser().getBrandIds();
        if (StringUtils.isBlank(brandIds)) {
            throw new ApiException("尚未加盟到任何品牌商, 无法核对平台订单编号");
        }
        return Arrays.stream(brandIds.split(",")).map(Long::valueOf).distinct().toList();
    }

    /**
     * 查询订单管理列表
     *
     * @param order 订单管理
     * @return 订单管理
     */
    @Override
    public List<OrderDto> selectOrderList(OrderQueryVo order) {
        RoleDataVo vo = icommonService.getRoleData(40);
        order.setFranchiseId(vo.getFranchiseId());
        order.setBrandId(vo.getBrandId());
        order.setCreateTime(vo.getCreateTime());
        order.setDeptIds(vo.getDeptIds());
        Boolean isDesensitization = vo.getIsDesensitization();
        List<OrderDto> orderDtoList = orderMapper.selectOrderList(order);
        if(!isDesensitization) {
            for (OrderDto orderDto : orderDtoList) {
                if(StringUtils.isNotBlank(orderDto.getDesignerPhone())) orderDto.setDesignerPhone(ParamDecryptUtil.decryptParam(orderDto.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
            }
        }
        return orderDtoList;
    }

    /**
     * 新增订单管理
     *
     * @param dto 订单管理
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOrder(OrderVo dto) {
        if(SecurityUtils.getSysUser().getFranchiseId() == null){
            throw new ApiException("非加盟商不允许添加订单");
        }
        XgwcPlatformTrade trade = null;
        // 平台订单校验
        if (dto.getOrderCategory() == 1) {
            trade = this.checkPlatformOrderNo(dto.getOrderNo());
        } else if (dto.getPayChannel() != 1L) {
            dto.setOrderNo(createOrderNo());
        }
        //全款金额设置
        orderPay(dto, trade);
        Order order = BeanUtil.copyProperties(dto, Order.class);
        order.setCreateTime(DateUtils.getNowDate());
        order.setCreateBy(SecurityUtils.getNickName());
        //判断是否添加设计师
        orderDesigner(dto, order);

        orderCheck(order);

        XgwcShopVo shop = xgwcShopMapper.getXgwcShopById(order.getStoreId());
        if(shop == null) throw new ApiException("订单来源异常，请先确定来源是否异常");
        order.setBrandId(shop.getBrandOwnerId());
//        XgwcShopFranchiseDto shopFranchise = xgwcShopFranchiseMapper.selectXgwcShopFranchiseByShopId(order.getStoreId());
//        if(shopFranchise == null) throw new ApiException("未找到订单来源授权的加盟商");
        order.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());

        // 产品讨论需求: 平台订单, 全款的自动归档, 非全款的可能还有线下流水
        if (dto.getOrderCategory() == 1 && dto.getPayType() == 1) {
            order.setIsArchiving(1);
        }
        //判断是否回购
        List<OrderDto> dtos = orderMapper.selectOrderByTaobaoId(dto.getTaobaoId());
        if(dtos != null && !dtos.isEmpty())
            order.setIsBuyback(1);
        orderMapper.insertOrder(order);
        //保存付款方式
        saveOrderPay(dto.getOrderPays(), order.getId(), order.getBrandId());
        //保存子订单
        if(dto.getOrderDesigners() != null) {
            createOrderNo(order.getOrderNo(), dto.getOrderDesigners());
            List<Order> list = new ArrayList<>();
            for (OrderDesignerVo vo: dto.getOrderDesigners()) {
                if(vo.getAllotUserId() != null) continue;
                Order o = setSubOrder(vo,order);
                if(o != null) {
                    o.setPid(order.getId());
                    list.add(o);
                }
            }
            if(!list.isEmpty()) orderMapper.insertOrders(list);
        }
        if (dto.getAllotType() == 1) {
            // 品牌商派单 添加到派单工作台
            DispatchVo dispatchVo = new DispatchVo();
            dispatchVo.setOrderId(order.getId());
            if(order.getAllotUrgency() == 0) {
                dispatchVo.setDispatchType(1);
            }else {
                dispatchVo.setDispatchType(2);
            }
            dispatchVo.setNeedDispatchNumber(order.getAllotNum().intValue());
            dispatchVo.setBrandId(order.getBrandId());
            dispatchService.insertDispatch(dispatchVo);
        }

        IncomLineVo lineVo = new IncomLineVo();
        lineVo.setTaobaoId(dto.getTaobaoId());
        lineVo.setFranchiseId(order.getFranchiseId());
        lineVo.setBrandId(order.getBrandId());
        incomCustomerService.insertIncomCustomer("order",lineVo);

        return 1;
    }

    private XgwcPlatformTrade checkPlatformOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new ApiException("平台订单必须填写平台订单编号");
        }
        List<Long> brandOwnerIdList = this.getBrandOwnerIdList();
        XgwcPlatformTrade trade = xgwcPlatformTradeMapper.getByTradeNo(orderNo, brandOwnerIdList);
        if (trade == null) {
            throw new ApiException("您输入的平台订单编号不存在,请核对后重新输入");
        }
        List<OrderPay> orderPayList = orderPayMapper.listByCollectionNo(brandOwnerIdList, ImmutableList.of(orderNo));
        if (!orderPayList.isEmpty()) {
            throw new ApiException("您输入的平台订单编号已被录入,请勿重复录入");
        }
        return trade;
    }


    /**
     * 修改订单管理
     *
     * @param dto 订单管理
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrder(OrderVo dto) {
        if(SecurityUtils.getSysUser().getFranchiseId() == null){
            throw new ApiException("非加盟商不允许编辑订单");
        }
        OrderDto orderDto = orderMapper.selectOrderById(dto.getId());
        if(orderDto == null || orderDto.getIsAfterSale() == 1) throw new ApiException("订单不存在或处于售后中，无法修改");
        //全款金额设置
        orderPay(dto, null);
        Order order = BeanUtil.copyProperties(dto, Order.class);

        order.setUpdateTime(DateUtils.getNowDate());
        order.setUpdateBy(SecurityUtils.getNickName());

        //判断是否添加设计师
        orderDesigner(dto, order);

        orderCheck(order);

        XgwcShopVo shop = xgwcShopMapper.getXgwcShopById(order.getStoreId());
        if(shop == null) throw new ApiException("订单来源异常，请先确定来源是否异常");
        order.setBrandId(shop.getBrandOwnerId());
        order.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());

        //保存付款方式
        saveOrderPay(dto.getOrderPays(), order.getId(), order.getBrandId());
        //删除其他付款方式
        orderPayMapper.deleteOrderPayByOrderIdType(order.getId(), order.getPayType() > 1 ? 0 : 1);

        //保存子订单
        if(dto.getOrderDesigners() != null) {
            createOrderNo(order.getOrderNo(), dto.getOrderDesigners());
            List<Order> list = new ArrayList<>();
            for (OrderDesignerVo vo: dto.getOrderDesigners()) {
                Order o = setSubOrder(vo, order);
                if(o != null) {
                    o.setPid(order.getId());
                    list.add(o);
                }
            }
            if(!list.isEmpty()) orderMapper.insertOrders(list);
        }
        if (dto.getAllotType() == 1 && order.getPid() !=null && order.getPid() == 0) {
            // 品牌商派单 添加到派单工作台
            DispatchDto dispatchDto = dispatchService.selectDispatchByOrderId(order.getId());
            if(dispatchDto == null) {
                DispatchVo dispatchVo = new DispatchVo();
                dispatchVo.setOrderId(order.getId());
                if(order.getAllotUrgency() == 0) {
                    dispatchVo.setDispatchType(1);
                }else {
                    dispatchVo.setDispatchType(2);
                }
                dispatchVo.setNeedDispatchNumber(order.getAllotNum().intValue());
                dispatchVo.setBrandId(order.getBrandId());
                dispatchService.insertDispatch(dispatchVo);
            }
        }
        //当前步骤不修改交稿状态
        order.setArchiveType(null);
        int result = orderMapper.updateOrder(order);
        //修改痕迹
        orderTrace(dto);
        if (result>0) {
            Long id = orderMapper.selectStylistOrdersById(order.getId());
            if (id != null) {
                int updated = orderMapper.updateStylistOrders(order);
                if (updated <= 0) {
                    log.error("更新设计师接单信息失败,订单id为{}", order.getId());
                    throw new ApiException("更新订单失败");
                }
            }
        }
        return result;
    }

    public void orderTrace(OrderVo vo) {
        OrderDto old  = selectOrderById(vo.getId());
        OrderTrace trace = old.compare(vo);
        trace.setUpdateBy(SecurityUtils.getNickName());
        trace.setUpdateTime(DateUtils.getNowDate());
        Integer userType = SecurityUtils.getSysUser().getUserType();
        if(userType == 1 || userType == 4)
            trace.setBrandId(SecurityUtils.getSysUser().getBrandId());
        else if(userType == 2 || userType == 5)
            trace.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
        orderMapper.insertOrderTrace(trace);

    }

    private void orderCheck(Order order) {
        if(order.getPayChannel() == 1 && StringUtils.isBlank(order.getOrderNo())) {
            throw new ApiException("订单编号不能为空");
        }
        OrderDto dto = orderMapper.selectOrderByOrderNo(order.getOrderNo());

        if(dto != null) {
            if(!dto.getId().equals(order.getId())) {
                throw new ApiException("订单编号已存在");
            }
            if(!dto.getAllotType().equals(order.getAllotType()) && dto.getAllotType() == 1) {
                throw new ApiException("已是品牌商派单，不允许再修改派单类型");
            }
        }
    }

    private String createOrderNo() {
        return createOrderNo(null);
    }
    private String createOrderNo(String orderNo) {
        String no = "000"+DateUtils.timestamp() +  String.valueOf((int) (Math.random() * 9000) + 1000); // 生成4位随机验证码;
        if(StringUtils.isNotBlank(orderNo)){
            List<SubOrderDto>  list = orderMapper.selectSubOrderListByPno(orderNo);
            int index = list.size() + 1;
            if(orderNo.startsWith("000")) {
                no =(index > 100 ? index : index > 10 ? "0"+index: "00"+index) + orderNo.substring(3);
            } else {
                no = (800+index) + orderNo;
            }
        }
        return no;
    }
    private void createOrderNo(String orderNo,List<OrderDesignerVo> list) {
        if(StringUtils.isNotBlank(orderNo) && list != null){
            List<SubOrderDto>  sub = orderMapper.selectSubOrderListByPno(orderNo);
            if(sub == null) return;
            int index = sub.size() + 1;
            for (OrderDesignerVo vo: list) {
                if(StringUtils.isNotBlank(vo.getOrderNo())){
                    continue;
                }
                String no = "";
                if(orderNo.startsWith("000")) {
                    no =(index > 100 ? index : index > 10 ? "0"+index: "00"+index) + orderNo.substring(3);
                } else {
                    no = (800+index) + orderNo;
                }
                vo.setOrderNo(no);
                index++;
            }
        }
    }

    private void orderPay(OrderVo dto, XgwcPlatformTrade trade) {
        BigDecimal amount = new BigDecimal(0);
        if(dto.getPayType()== 1L && dto.getId() == null) {
            OrderPayDto orderPayDto = new OrderPayDto();
            orderPayDto.setPayType(dto.getPayType());
            if (trade != null) {
                // 全款的只有一个付款流水
                orderPayDto.setPayChannel(OrderUtils.platformIdToPayChannel(trade.getPlatformId()));
                orderPayDto.setCollectionNo(trade.getTradeNo());
                orderPayDto.setAmount(trade.getRealAmount());
                List<OrderPayDto> list = Lists.newArrayList();
                list.add(orderPayDto);
                dto.setOrderPays(list);
                amount = trade.getRealAmount();
            } else {
                orderPayDto.setPayImg(dto.getPayImg());
                orderPayDto.setPayChannel(dto.getPayChannel());
                if(dto.getPayChannel() == 1L) {
                    orderPayDto.setCollectionNo(dto.getOrderNo());
                } else {
                    orderPayDto.setCollectionNo(DateUtils.timestamp() +  String.valueOf((int) (Math.random() * 9000) + 1000));
                }
                orderPayDto.setAmount(dto.getOrderAmount());
                List<OrderPayDto> list = dto.getOrderPays();
                if(list == null) list = new ArrayList<>();
                list.add(0,orderPayDto);
                dto.setOrderPays(list);
                amount = dto.getOrderAmount();
            }
        } else if(dto.getPayType()== 1L ){
            List<OrderPayDto> list = orderPayMapper.selectOrderPayByOrderId(dto.getId());
            list = list.stream().filter(l -> l.getPayType() == 1L).toList();
            if(list == null || list.isEmpty()) {
                list = new ArrayList<>();
                OrderPayDto orderPayDto = new OrderPayDto();
                list.add(orderPayDto);
            }
            list.forEach(l -> {
                l.setPayImg(dto.getPayImg());
                l.setPayChannel(dto.getPayChannel());
                l.setCollectionNo(dto.getCollectionNo());
                l.setAmount(dto.getOrderAmount());
            });
            dto.setOrderPays(list);
            amount = dto.getOrderAmount();
        } else {
            for (OrderPayDto orderPayDto : dto.getOrderPays()) {
                //计算金额
                amount = amount.add(orderPayDto.getAmount());
                //设置流水号
                //update by yangzp 处理 orderPayDto.getPayType为空的问题
                if(dto.getPayChannel() == 1L) {
                    if(StringUtils.isBlank(dto.getCollectionNo())) throw new ApiException("收款编号不能为空");
                    orderPayDto.setCollectionNo(dto.getCollectionNo());
                } else {
                    orderPayDto.setCollectionNo(DateUtils.timestamp() +  String.valueOf((int) (Math.random() * 9000) + 1000));
                }
//                if(dto.getPayType() == 2L) {
//                    orderPayDto.setCollectionNo(dto.getOrderNo());
//                } else {
//
//                }
            }
            long index = dto.getOrderPays().stream().map(OrderPayDto::getCollectionNo).distinct().count();
            if(index < dto.getOrderPays().size()) {
                throw new ApiException("收款编号不能相同");
            }
            if(dto.getOrderAmount() == null || dto.getOrderAmount().compareTo(amount) < 0) {
                throw new ApiException("实收金额应小于等于订单金额");
            }
        }


        dto.setAmount(amount);
    }

    private void saveOrderPay(List<OrderPayDto> dto,Long orderId, Long brandId) {
        if(dto == null || dto.isEmpty()) return;

        List<OrderPayDto> platformPayList = dto.stream().filter(x -> OrderUtils.isPlatformPayByChannel(x.getPayChannel())).toList();
        List<String> platformPayCollectionNoList = platformPayList.stream().map(OrderPayDto::getCollectionNo).filter(StringUtils::isNotBlank).distinct().toList();
        if (platformPayCollectionNoList.size() != platformPayList.size()) {
            throw new ApiException("存在异常的平台收款编号");
        }
        Map<String, OrderPay> dbPlatformPayMap = Maps.newHashMap();
        if (!platformPayCollectionNoList.isEmpty()) {
            dbPlatformPayMap = orderPayMapper.listByCollectionNo(ImmutableList.of(brandId), platformPayCollectionNoList).stream().collect(Collectors.toMap(OrderPay::getCollectionNo, x -> x));
        }

        List<OrderPay> list = new ArrayList<>();
        for (OrderPayDto orderPayDto : dto) {
            OrderPay orderPay = BeanUtil.copyProperties(orderPayDto, OrderPay.class);
            orderPay.setOderId(orderId);
            OrderPay dbOrderPay = dbPlatformPayMap.get(orderPay.getCollectionNo());
            if (dbOrderPay != null && !dbOrderPay.getId().equals(orderPay.getId())) {
                throw new ApiException(String.format("收款编号%s已被订单%s使用, 不能重复使用哦", orderPay.getCollectionNo(), orderMapper.selectOnlyOrderById(dbOrderPay.getOderId()).getOrderNo()));
            }
            if(orderPay.getId() == null){
                orderPay.setCreateBy(SecurityUtils.getNickName());
                list.add(orderPay);
            } else {
                orderPay.setUpdateBy(SecurityUtils.getNickName());
                orderPay.setUpdateTime(new Date());
                orderPayMapper.updateOrderPay(orderPay);
            }
        }
        if(!list.isEmpty())  orderPayMapper.insertOrderPays(list);
    }

    public void orderDesigner(OrderVo dto,Order order) {
        if(dto.getOrderDesigners()!=null && !dto.getOrderDesigners().isEmpty()){
            long count = dto.getOrderDesigners().stream().map(OrderDesignerVo::getDesignerId).distinct().count();
            if(dto.getOrderDesigners().size() > count){
                throw new ApiException("一个订单不能派单相同的设计师");
            }

            OrderDesignerVo vo = dto.getOrderDesigners().get(0);
            if(order.getId() != null){
                List<OrderDesignerVo> ds = dto.getOrderDesigners().stream().filter(d -> d.getId() != null && d.getId().equals(order.getId())).collect(Collectors.toList());
                if(!ds.isEmpty()){
                    vo = ds.get(0);
                }
            }
            BigDecimal money = new BigDecimal(0);
            for (OrderDesignerVo designerVo : dto.getOrderDesigners()) {
                if(designerVo.getMoney() != null){
                    money = money.add(designerVo.getMoney());
                }
            }
            if(order.getOrderAmount().multiply(new BigDecimal(0.8)).compareTo(money) < 0){
                throw new ApiException("设计师佣金应小于等于订单金额的80%");
            }

            if(vo == null) throw new ApiException("订单异常");


            List<Long> ids = dto.getOrderDesigners().stream().mapToLong(OrderDesignerVo::getDesignerId).boxed().collect(Collectors.toList());
            List<Long> business = orderMapper.getDesignersBusiness(ids);
            if(order.getStateDicCode() == null) throw new ApiException("请选择订单来源");
            if(business == null || business.size() > 1 || !business.get(0).equals(Long.valueOf(order.getStateDicCode()))){
                throw new ApiException("设计师擅长业务与订单来源不匹配！");
            }

            order.setDesignerId(vo.getDesignerId());
            order.setDesignerName(vo.getDesignerName());
            if(StringUtils.isNotBlank(vo.getDesignerPhone())){
                order.setDesignerPhone(ParamDecryptUtil.encrypt(vo.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
            }

            order.setMoney(vo.getMoney());
            order.setArchiveAppointTime(vo.getArchiveAppointTime());
            order.setRemark(vo.getRemark());
            dto.getOrderDesigners().remove(vo);

            OrderDesignerVo finalVo = vo;
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try{
                        if(finalVo.getOldDesignerId() != null){
                            OrderDesignerUpdateVo updateVo = BeanUtil.copyProperties(finalVo, OrderDesignerUpdateVo.class);
                            if(StringUtils.isNotBlank(updateVo.getOldDesignerPhone())){
                                updateVo.setOldDesignerPhone(ParamDecryptUtil.encrypt(updateVo.getOldDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
                            }
                            updateVo.setOderId(order.getId());
                            updateVo.setId(null);
                            designerUpdate(updateVo);
                        }
                    } finally {
                    }
                }
            });
        }
    }

    public Order setSubOrder(OrderDesignerVo vo,Order order) {
        Order subOrder =  SerializationUtils.<Order>clone(BeanUtil.copyProperties(order, Order.class));

        subOrder.setId(vo.getId());
        subOrder.setOrderNo(vo.getOrderNo());
        subOrder.setPid(order.getId());
        subOrder.setDesignerId(vo.getDesignerId());
        subOrder.setDesignerName(vo.getDesignerName());
        subOrder.setDesignerPhone(vo.getDesignerPhone());
        subOrder.setMoney(vo.getMoney());
        subOrder.setArchiveAppointTime(vo.getArchiveAppointTime());
        subOrder.setRemark(vo.getRemark());
        subOrder.setCreateTime(DateUtils.getNowDate());
        subOrder.setCreateBy(SecurityUtils.getNickName());
        if(StringUtils.isNotBlank(subOrder.getDesignerPhone())){
            subOrder.setDesignerPhone(ParamDecryptUtil.encrypt(subOrder.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
        }

        if(subOrder.getId() == null) {
            return subOrder;
        } else {
            //存在派单人 不修改
            if(vo.getAllotUserId() != null) return null;
            orderMapper.updateOrder(subOrder);
            OrderDesignerUpdateVo updateVo = BeanUtil.copyProperties(vo, OrderDesignerUpdateVo.class);
            updateVo.setOderId(subOrder.getId());
            updateVo.setId(null);
            designerUpdate(updateVo);
        }
        return null;
    }

    @Override
    public int saveSubOrder(OrderDesignerVo order) {
        Order subOrder = BeanUtil.copyProperties(order, Order.class);
        subOrder.setCreateTime(DateUtils.getNowDate());
        subOrder.setCreateBy(SecurityUtils.getNickName());

        if(StringUtils.isNotBlank(subOrder.getDesignerPhone())){
            subOrder.setDesignerPhone(ParamDecryptUtil.encrypt(subOrder.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
        }

        if(subOrder.getId() == null)
            orderMapper.insertOrder(subOrder);
//        else
//            orderMapper.updateOrder(subOrder);
        return 1;
    }
    /**
     * 批量删除订单管理
     *
     * @param ids 需要删除的订单管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteOrderByIds(Long[] ids) {
        //删除阶段款
//        orderPayMapper.deleteOrderPayByOrderIds(ids);
        for (Long id : ids) {
            deleteOrderById(id);
        }
        return 1;
    }

    /**
     * 删除订单管理信息
     *
     * @param id 订单管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteOrderById(Long id) {
        OrderDto orderDto = orderMapper.selectOrderById(id);
        if(orderDto == null)
            throw new ApiException("订单不存在");
        if(orderDto.getIsAfterSale() == 1)
            throw new ApiException("该母订单有申请流程不能删除");
        OrderExpensesApplyDto orderExpensesApplyDto = orderExpensesApplyMapper.getOrderRefundApplyByOrderNo(orderDto.getOrderNo());
        if(orderExpensesApplyDto != null)
            throw new ApiException("该母订单有申请流程不能删除");
        OrderInvoiceApply orderInvoiceApply = orderInvoiceApplyMapper.getPassByOrderNo(orderDto.getOrderNo());
        if(orderInvoiceApply != null)
            throw new ApiException("该母订单有申请流程不能删除");

        if(orderDto.getArchiveType() == 5 ) throw new ApiException("该母/子订单设计师已交定稿不可删除");
        List<SubOrderDto> sub = orderMapper.selectSubOrderList(id);
        if(!sub.isEmpty()) throw new ApiException("该母订单下有子订单,删除所有子订单后方可删除");

        //清空进度
        orderPlanMapper.delectOrderPlanByOrderId(id);
        //删除接单记录
        orderMapper.deleteStylistOrdersByOrderId(id);
        //删除pay
        orderPayMapper.deleteOrderPayByOrderIdType(orderDto.getId(), null);
        orderPayMapper.deleteFinanceOfflinePaymentsByOrderId(orderDto.getId());

        return orderMapper.deleteOrderById(id);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public int savePlan(List<OrderPlanVo> plans) {
        Date now = DateUtils.getNowDate();
        Long orderId = plans.get(0).getOderId();
        Integer archiveType = 0;
        List<OrderPlanDto>  planDtos =  orderPlanMapper.selectOrderPlanByOrderId(orderId);
        for (OrderPlanVo plan : plans) {
            OrderPlan orderPlan = BeanUtil.copyProperties(plan, OrderPlan.class);
            orderPlan.setArchiveTime(now);
            long index = planDtos.stream().filter(p -> p.getArchiveType().equals(plan.getArchiveType()) && plan.getId() == null).count();
            if (index > 0) throw new ApiException("一个进度只允许新增一个");
            if (plan.getId() == null) {
                orderPlan.setCreateTime(now);
                orderPlan.setCreateBy(SecurityUtils.getNickName());
                orderPlanMapper.insertOrderPlan(orderPlan);
            } else {
                orderPlan.setUpdateTime(now);
                orderPlan.setUpdateBy(SecurityUtils.getNickName());
                orderPlanMapper.updateOrderPlan(orderPlan);
            }
            if(plan.getArchiveType() == ReviewArchive) {
                OrderArchiveDto old = orderArchiveMapper.selectOrderArchiveByOrderId(orderId);
                OrderArchive orderArchive =  BeanUtil.copyProperties(plan, OrderArchive.class);
                if(old != null) {
                    orderArchive.setId(old.getId());
                    orderArchive.setUpdateTime(DateUtils.getNowDate());
                    orderArchive.setUpdateBy(SecurityUtils.getNickName());
                    orderArchive.setReviewMsg(null);
                    orderArchive.setReviewStatus(null);
                    orderArchiveMapper.updateOrderArchive(orderArchive);
                } else {
                    orderArchive.setCreateBy(SecurityUtils.getNickName());
                    orderArchive.setCreateTime(now);
                    orderArchiveMapper.insertOrderArchive(orderArchive);
                }
            }
            archiveType = plan.getArchiveType();
        }
        OrderDto orderDto = orderMapper.selectOrderById(orderId);
        Order order = BeanUtil.copyProperties(orderDto, Order.class);
        order.setUpdateTime(now);
        order.setUpdateBy(SecurityUtils.getNickName());
        order.setArchiveTime(now);
        order.setArchiveType(archiveType);
        orderMapper.updateOrder(order);
        return 1;
    }

    @Override
    public List<OrderPlanDto> selectOrderPlanByOrderId(Long orderId) {
        List<OrderPlanDto>  plans = orderPlanMapper.selectOrderPlanByOrderId(orderId);
        for (OrderPlanDto plan : plans) {
            if(plan.getArchiveType() >= ReviewArchive) {
                OrderArchiveDto old = orderArchiveMapper.selectOrderArchiveByOrderId(orderId);
                if (old != null)  plan.setArchive(old);
            }
        }
        return plans;
    }

    @Override
    public OrderArchiveDto selectOrderArchiveByOrderId(Long orderId) {
        OrderArchiveDto dto = orderArchiveMapper.selectOrderArchiveByOrderId(orderId);
        if(dto == null) return null;
        List<OrderArchiveLogDto> logs = orderArchiveLogMapper.selectOrderArchiveLogByArchiveId(dto.getId());
        if(logs != null) dto.setLogs(logs);
        List<OrderScoreDto> scores = orderScoreMapper.selectOrderScoreByArchiveId(dto.getId());
        if(logs != null) dto.setScores(scores);
        return dto;
    }

    @Override
    @Transactional
    public int archiveReview(OrderArchiveVo archiveVo) {
        OrderDto dto = orderMapper.selectOrderById(archiveVo.getOderId());

        OrderArchiveLog log = BeanUtil.copyProperties(archiveVo, OrderArchiveLog.class);
        log.setId(null);
        if(log.getReviewStatus() == 0)
            log.setReviewStatus(dto.getArchiveType() == 5 ? 2 : 0);
        log.setArchiveId(archiveVo.getId());
        log.setReview(SecurityUtils.getNickName());
        log.setReviewTime(DateUtils.getNowDate());
        orderArchiveLogMapper.insertOrderArchiveLog(log);

        OrderArchive orderArchive = BeanUtil.copyProperties(archiveVo, OrderArchive.class);
        orderArchive.setUpdateBy(SecurityUtils.getNickName());
        Order order = BeanUtil.copyProperties(dto, Order.class);
        order.setUpdateTime(DateUtils.getNowDate());
        order.setUpdateBy(SecurityUtils.getNickName());
        order.setArchiveType(orderArchive.getReviewStatus() == 0 ? 5 : 4);
        if(order.getArchiveType() == 5) {
            updateOrderStatus(order.getId(), OrderStatusEnums.ARCHIVED);
        } else {
            orderMapper.updateOrder(order);
        }
        orderArchiveMapper.updateOrderArchive(orderArchive);
        if(orderArchive.getReviewStatus() == 0) {
            int caseLevel = 3;
           for (OrderScoreVo vo :archiveVo.getScoreVo()) {
               OrderScore orderScore = BeanUtil.copyProperties(vo, OrderScore.class);
               orderScore.setArchiveId(orderArchive.getId());
               orderScore.setDesignerId(dto.getDesignerId());
               orderScore.setOderId(dto.getId());

               if("evaluation".equals(vo.getScoreModel())) {
                   if(vo.getScoreNum() > 3) caseLevel = 1;
                   else if(vo.getScoreNum() == 3 ) caseLevel = 2;
               }

               if (orderScore.getId() == null) {
                   orderScore.setCreateBy(SecurityUtils.getNickName());
                   orderScoreMapper.insertOrderScore(orderScore);
               } else {
                   orderScore.setUpdateBy(SecurityUtils.getNickName());
                   orderScoreMapper.updateOrderScore(orderScore);
               }
           }
            XgwcCase xgwcCase =xgwcCaseMapper.checkXgwcCaseByArchiveId(orderArchive.getId());
            if(xgwcCase == null) {
                xgwcCase = new XgwcCase();
            }
            xgwcCase.setArchiveId(orderArchive.getId());
            xgwcCase.setTitle(archiveVo.getArchiveName());
            xgwcCase.setAmount(dto.getAmount());
            xgwcCase.setCaseLevel(caseLevel);
            xgwcCase.setDesignerId(dto.getDesignerId());
            xgwcCase.setDesignerName(dto.getDesignerName());
            xgwcCase.setDesignerPhone(dto.getDesignerPhone());
            xgwcCase.setOderId(dto.getId());
            xgwcCase.setBrandId(dto.getBrandId());
            xgwcCase.setFranchiseId(dto.getFranchiseId());
            xgwcCase.setArchiveImg(archiveVo.getArchiveImg());
            xgwcCase.setArchiveFiles(archiveVo.getArchiveFiles());
            xgwcCase.setLinkUrl(archiveVo.getLinkUrl());
            xgwcCase.setCreateBy(SecurityUtils.getNickName());
            xgwcCase.setCreateTime(DateUtils.getNowDate());
            xgwcCase.setUpdateBy(SecurityUtils.getNickName());
            xgwcCase.setUpdateTime(DateUtils.getNowDate());
            xgwcCase.setArchiveId(orderArchive.getId());
            xgwcCase.setBusinessId(orderArchive.getBusinessId());
            xgwcCase.setBusinessName(orderArchive.getBusinessName());
            xgwcCase.setMoney(dto.getMoney());
            XgwcCaseVo xgwcCaseVo = BeanUtil.copyProperties(xgwcCase, XgwcCaseVo.class);
            if(xgwcCase.getCaseId() == null)
                xgwcCaseService.insertXgwcCase(xgwcCaseVo);
            else xgwcCaseService.updateXgwcCase(xgwcCaseVo);
        }

        return 1;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult designerOrders(String orderNo) {

        Map<Long,Long> designerMap = SecurityUtils.getSysUser().getDesignerMap();
        if (designerMap == null) return ApiResult.error("该账号不是设计师账号");

        // 查询订单
        OrderDto dto = orderMapper.selectOrderByOrderNo(orderNo);
        if (dto == null) return ApiResult.error("订单编号输入错误");

        if (!Objects.equals(designerMap.get(dto.getBrandId()), dto.getDesignerId())) {
            return ApiResult.error("您不是该订单的设计师，无法接该订单，有疑问请咨询对应客服");
        }

        if (dto.getArchiveType() != null && dto.getArchiveType() > 0) {
            return ApiResult.error("该订单已接单,无需重复接单");
        }

        // 更新订单状态
        Order order = BeanUtil.copyProperties(dto, Order.class);
        order.setArchiveType(InProduction);

        int updateResult = orderMapper.updateOrder(order);
        if (updateResult <= 0) {
            log.error("订单状态更新失败");
            throw new ApiException("接单异常");
        }

        String operator = SecurityUtils.getNickName();
        Date now = new Date();

        OrderPlan orderPlan = new OrderPlan();
        orderPlan.setOderId(order.getId());
        orderPlan.setArchiveType(InProduction);
        orderPlan.setArchiveTime(now);
        orderPlan.setCreateBy(operator);
        orderPlan.setCreateTime(now);

        int insertResult = orderPlanMapper.insertOrderPlan(orderPlan);
        if (insertResult <= 0) {
            log.error("订单计划记录插入失败");
            throw new ApiException("接单异常");
        }

        return ApiResult.ok("success", dto);
    }


    @Override
    @Transactional
    public ApiResult designerUpdate(OrderDesignerUpdateVo vo) {

        if(StringUtils.isNotBlank(vo.getOldDesignerPhone())){
            vo.setOldDesignerPhone(ParamDecryptUtil.encrypt(vo.getOldDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        if(StringUtils.isNotBlank(vo.getDesignerPhone())){
            vo.setDesignerPhone(ParamDecryptUtil.encrypt(vo.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
        }

        OrderDto orderDto = orderMapper.selectOrderById(vo.getOderId());
        if(orderDto == null || orderDto.getIsAfterSale() == 1) throw new ApiException("订单不存在或处于售后中，无法修改");
        OrderDto pdto = null;
        if(orderDto.getPid() != null && orderDto.getPid() > 0){
            pdto = selectOrderById(orderDto.getPid());
        } else {
            pdto = selectOrderById(orderDto.getId());
        }
        if(vo.getDesignerId() != null && pdto.getDesignerId().equals(vo.getDesignerId()) && !vo.getOderId().equals(pdto.getId())){
            throw new ApiException("一个订单不能派单相同的设计师");
        }
        long count = pdto.getSubOrders().stream().filter(p -> p.getDesignerId().equals(vo.getDesignerId()) && !vo.getOderId().equals(p.getId())).count();
        if(count > 0 ){
            throw new ApiException("一个订单不能派单相同的设计师");
        }
        BigDecimal money = vo.getMoney();
        for (SubOrderDto designerVo : pdto.getSubOrders()) {
            if(designerVo.getMoney() != null && !designerVo.getId().equals(vo.getOderId())){
                money = money.add(designerVo.getMoney());
            }
        }
        if(pdto.getOrderAmount().multiply(new BigDecimal(0.8)).compareTo(money) < 0){
            throw new ApiException("设计师佣金应小于等于订单金额的80%");
        }

        if(orderDto.getArchiveType() == 5) throw new ApiException("已交稿订单无法修改设计师");
        Order order = BeanUtil.copyProperties(orderDto, Order.class);
        order.setId(vo.getOderId());
        order.setArchiveType(0);
        order.setDesignerId(vo.getDesignerId());
        order.setDesignerName(vo.getDesignerName());
        order.setDesignerPhone(vo.getDesignerPhone());
        order.setDesignerBusiness(vo.getDesignerBusiness());
        order.setMoney(vo.getMoney());
        order.setArchiveAppointTime(vo.getArchiveAppointTime());
        order.setRemark(vo.getRemark());
        try{
            order.setUpdateBy(SecurityUtils.getNickName());
        } catch (Exception e){

        }
        order.setUpdateTime(new Date());
        orderMapper.updateOrder(order);

       if(vo.getOldDesignerId() != null && !vo.getOldDesignerId().equals(vo.getDesignerId())){
           //清空进度
           orderPlanMapper.delectOrderPlanByOrderId(vo.getOderId());
           //删除接单记录
           orderMapper.deleteStylistOrdersByOrderId(vo.getOderId());
       }

        OrderDesignerUpdate designerUpdate = BeanUtil.copyProperties(vo, OrderDesignerUpdate.class);
        orderMapper.designerUpdate(designerUpdate);


        if(vo.getIsDuty() != null && vo.getIsDuty() == 1) {
            for (OrderScoreVo scoreVo :vo.getScoreVo()) {
                OrderScore orderScore = BeanUtil.copyProperties(scoreVo, OrderScore.class);
                orderScore.setDesignerId(vo.getOldDesignerId());
                orderScore.setOderId(vo.getOderId());
                orderScore.setType(1);
                try {
                    orderScore.setCreateBy(SecurityUtils.getNickName());
                    orderScore.setUpdateBy(SecurityUtils.getNickName());
                } catch (Exception e){

                }
                if (orderScore.getId() == null)
                    orderScoreMapper.insertOrderScore(orderScore);
                else
                    orderScoreMapper.updateOrderScore(orderScore);
            }
        }
        return ApiResult.ok();
    }

    @Override
    public List<OrderDto> selectHistoryOrderList(OrderHistoryQueryVo order) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null) return Collections.emptyList();
        order.setBrandId(brandId);
        return orderMapper.findHistoryOrderList(order);
    }

    @Override
    public OrderCompanyDto selectOrderCompanyByOrderNo(String orderNo) {
        return orderMapper.selectOrderCompanyByOrderNo(orderNo);
    }

    public int updateOrderStatus(Long orderId, OrderStatusEnums orderStatusEnums) {
        if(orderStatusEnums == OrderStatusEnums.ARCHIVED){
            updateArchivedStatus(orderId);
        }
        OrderDto orderDto1 = selectOrderById(orderId);
        if(orderDto1.getPid() != null && orderDto1.getPid() > 0){
            orderDto1 = selectOrderById(orderDto1.getPid());
        }
        //是否有售后
        Integer isAfterSale = orderDto1.getIsAfterSale() == null ? 0 : orderDto1.getIsAfterSale();
        //是否归档
        Integer isArchiving = orderDto1.getIsArchiving() == null ? 0 : orderDto1.getIsArchiving();
        //是否已定稿
        boolean archived = allArchived(orderDto1);
        Order order = new Order();
        order.setId(orderDto1.getId());
        order.setUpdateBy(SecurityUtils.getUserSupportNotLogin().getUserName());
        order.setUpdateTime(new Date());
        order.setDealTime(orderDto1.getDealTime());
        boolean isRef = true;
        switch (orderStatusEnums){
            case ARCHIVED -> {
                boolean meetTheConditions = meetTheConditions(isAfterSale, isArchiving, archived);
                if(StringUtils.isEmpty(orderDto1.getDealTime()) && meetTheConditions){
                    order.setDealTime(DateUtils.getLongDateStr());
                } else {
                    isRef = false;
                }
            }
            case AFTER_SALE_START -> {
                order.setIsAfterSale(orderStatusEnums.getCode());
                if(StringUtils.isNotEmpty(orderDto1.getDealTime())){
                    //如果没有结算也没有锁定
                    if(orderDto1.getIsLock() == 0 && orderDto1.getSettlement() == 0){
                        BillDesigner billDesigner = billDesignerMapper.getBillDesignerByOrderId(orderId);
                        if(billDesigner != null){
                            //如果订单没有确认
                            if(billDesigner.getSettlementStatus() == 0 || billDesigner.getSettlementStatus() == 1 || billDesigner.getSettlementStatus() == 4 || billDesigner.getSettlementStatus() == 5){
                                order.setDealTime(null);
                            }
                        }
                    }
                }
            }
            case AFTER_SALE_END -> {
                order.setIsAfterSale(orderStatusEnums.getCode());
                orderDto1.setIsAfterSale(orderStatusEnums.getCode());
                boolean meetTheConditions = meetTheConditions(orderStatusEnums.getCode(), isArchiving, archived);
                //重新生成成交时间
                if(StringUtils.isEmpty(orderDto1.getDealTime()) && meetTheConditions){
                    order.setDealTime(DateUtils.getLongDateStr());
                }
            }
            case IS_ARCHIVING -> {
                order.setIsArchiving(orderStatusEnums.getCode());
                boolean meetTheConditions = meetTheConditions(isAfterSale, orderStatusEnums.getCode(), archived);
                //满足条件则需要删除成交时间
                if(StringUtils.isEmpty(orderDto1.getDealTime()) && meetTheConditions){
                    order.setDealTime(DateUtils.getLongDateStr());
                }
            }
        }
        return isRef ? orderMapper.updateOrderStatus(order) : 1;
    }

    //处理交稿
    public int updateArchivedStatus(Long orderId){
        Order order = new Order();
        order.setId(orderId);
        order.setUpdateBy(SecurityUtils.getNickName());
        order.setUpdateTime(new Date());
        order.setArchiveType(5);
        order.setArchiveTime(new Date());
        //先修改定稿状态
        orderMapper.updateOrderStatus(order);
        return 0;
    }

    /**
     * 是否全部交稿
     * @param orderDto 订单id
     * @return 是否全部交稿
     */
    private boolean allArchived(OrderDto orderDto){
        if(orderDto != null){
            if(orderDto.getArchiveType() != 5){
                return false;
            }
            List<SubOrderDto> orderDtos = orderDto.getSubOrders();
            if(orderDtos != null && !orderDtos.isEmpty()){
                for(SubOrderDto subOrderDto : orderDtos){
                    if(subOrderDto.getArchiveType() != 5){
                        return false;
                    }
                }
            }
        }
        return true;
    }

    @Transactional
    @Override
    public boolean isTheOrderConfirmed(Long orderId) {
        BillDesigner billDesigner = billDesignerMapper.getBillDesignerByOrderId(orderId);
        if(billDesigner == null){
            return false;
        }
        return (billDesigner.getSettlementStatus() == 2 || billDesigner.getSettlementStatus() == 3 || billDesigner.getSettlementStatus() == 6);
    }

    @Override
    public boolean isTheOrderBilled(Long orderId) {
        BillDesigner billDesigner = billDesignerMapper.getBillDesignerByOrderId(orderId);
        return billDesigner != null;
    }

    @Override
    public int batchUpdateRefundStatus(List<Long> orderIds, Integer refundStatus) {
        return orderMapper.batchUpdateRefundStatus(orderIds, refundStatus);
    }

    @Override
    public List<SubOrderDto> selectSubOrderListByPId(Long pid) {
        return orderMapper.selectSubOrderList(pid);
    }

    //是否满足成交条件，已交稿，未售后，已归档
    private boolean meetTheConditions(Integer isAfterSale, Integer isArchiving, boolean archived){
        return isAfterSale.equals(OrderStatusEnums.AFTER_SALE_END.getCode())
                && isArchiving.equals(OrderStatusEnums.IS_ARCHIVING.getCode()) && archived;
    }

}
