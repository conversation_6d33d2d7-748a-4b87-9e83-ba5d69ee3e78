package com.xgwc.order.controller;

import java.util.List;

import com.xgwc.order.entity.dto.IncomLineTotalDto;
import com.xgwc.order.entity.vo.IncomLineTransferVo;
import com.xgwc.user.feign.api.UserDeptFeign;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.order.entity.vo.IncomLineVo;
import com.xgwc.order.entity.dto.IncomLineDto;
import com.xgwc.order.entity.vo.IncomLineQueryVo;
import com.xgwc.order.service.IIncomLineService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

@RestController
@RequestMapping("/incomLine")
public class IncomLineController extends BaseController {
    @Autowired
    private IIncomLineService incomLineService;

    @Resource
    private UserDeptFeign userDeptFeign;
    /**
     * 查询进线管理列表
     */
    @MethodDesc("查询进线管理列表")
//    @PreAuthorize("@ss.hasPermission('incomLine:incomLine:list')")
    @GetMapping("/list")
    public ApiResult<IncomLineDto> list(IncomLineQueryVo incomLine) {
        startPage();
        List<IncomLineDto> list = incomLineService.selectIncomLineList(incomLine);
        return getDataTable(list);
    }

    @MethodDesc("查询进线管理列表统计")
    @GetMapping("/total")
    public ApiResult<IncomLineTotalDto> total(IncomLineQueryVo incomLine) {
        return success(incomLineService.selectIncomLineTotal(incomLine));
    }

    /**
     * 导出进线管理列表
     */
    @MethodDesc("导出进线管理列表")
    @PreAuthorize("@ss.hasPermission('incomLine:incomLine:export')")
    @Log(title = "进线管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IncomLineQueryVo incomLine) {
    }

    /**
     * 获取进线管理详细信息
     */
    @MethodDesc("获取进线管理详细信息")
//    @PreAuthorize("@ss.hasPermission('incomLine:incomLine:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<IncomLineDto> getInfo(@PathVariable("id") Long id) {
        return success(incomLineService.selectIncomLineById(id));
    }

    /**
     * 新增进线管理
     */
    @MethodDesc("新增进线管理")
    @PreAuthorize("@ss.hasPermission('incomLine:incomLine:add')")
    @Log(title = "进线管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody @Valid IncomLineVo incomLine) {
        return toAjax(incomLineService.insertIncomLine(incomLine));
    }

    /**
     * 修改进线管理
     */
    @MethodDesc("修改进线管理")
    @PreAuthorize("@ss.hasPermission('incomLine:incomLine:edit')")
    @Log(title = "进线管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody @Valid IncomLineVo incomLine) {
        return toAjax(incomLineService.updateIncomLine(incomLine));
    }

    @MethodDesc("转移进线管理")
    @PreAuthorize("@ss.hasPermission('incomLine:incomLine:transfer')")
    @Log(title = "进线管理", businessType = BusinessType.UPDATE)
    @PutMapping("/transfer")
    public ApiResult Transfer(@RequestBody IncomLineTransferVo incomLine) {
//        ApiResult result = userDeptFeign.getUserDeptByDeptId(incomLine.getDeptId().intValue(), incomLine.getDeptType());
//        JSONObject dept = JSONObject.parseObject(result.getData().toString());
//        incomLine.setDeptName(dept.getString("deptName"));
        return toAjax(incomLineService.transferIncomLine(incomLine));
    }

    /**
     * 删除进线管理
     */
    @MethodDesc("删除进线管理")
    @PreAuthorize("@ss.hasPermission('incomLine:incomLine:remove')")
    @Log(title = "进线管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(incomLineService.deleteIncomLineByIds(ids));
    }
}
