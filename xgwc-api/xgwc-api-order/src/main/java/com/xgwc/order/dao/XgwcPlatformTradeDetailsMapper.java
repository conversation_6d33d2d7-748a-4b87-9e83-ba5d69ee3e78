package com.xgwc.order.dao;

import com.xgwc.order.entity.XgwcPlatformTradeDetails;
import com.xgwc.order.entity.vo.XgwcPlatformTradeDetailsQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface XgwcPlatformTradeDetailsMapper {

    /**
     * 查询平台交易单明细列表
     *
     * @param detailsQueryVo 平台交易单明细
     * @return 平台交易单明细集合
     */
    List<XgwcPlatformTradeDetails> selectList(XgwcPlatformTradeDetailsQueryVo detailsQueryVo);

    /**
     * 新增平台交易单明细
     *
     * @param xgwcPlatformTradeDetailsList 平台交易单明细
     * @return 结果
     */
    int insertList(@Param("xgwcPlatformTradeDetailsList") List<XgwcPlatformTradeDetails> xgwcPlatformTradeDetailsList);


    void deleteByTradeNo(@Param("tradeNoList") List<String> tradeNoList, @Param("brandOwnerId") Long brandOwnerId);

}