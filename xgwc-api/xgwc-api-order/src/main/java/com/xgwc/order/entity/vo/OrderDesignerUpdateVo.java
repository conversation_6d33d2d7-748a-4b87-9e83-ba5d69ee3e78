package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderDesignerUpdateVo implements Serializable {

    private static final long serialVersionUID=1L;

    @FieldDesc("id")
    private Long id;

    @FieldDesc("是否有责：1有 0无")
    private Integer isDuty;

    @FieldDesc("订单id")
    private Long oderId;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("设计师ID")
    private Long oldDesignerId;

    @FieldDesc("设计师名称")
    private String oldDesignerName;

    @FieldDesc("设计师电话")
    private String oldDesignerPhone;

    @FieldDesc("设计师业务类型")
    private String oldDesignerBusiness;

    @FieldDesc("设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    private String designerPhone;

    @FieldDesc("设计师业务类型")
    private String designerBusiness;

    @FieldDesc("佣金金额")
    private BigDecimal money;

    @FieldDesc("约定初稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveAppointTime;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("评分")
    private List<OrderScoreVo> scoreVo;


}
