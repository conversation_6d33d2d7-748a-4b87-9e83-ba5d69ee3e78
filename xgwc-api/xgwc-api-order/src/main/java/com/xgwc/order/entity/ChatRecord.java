package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class ChatRecord {

private static final long serialVersionUID=1L;

    /** 品牌id */
    private Long brandId;

    /** 聊天记录 */
    private String chatContent;

    /** 分类 */
    private String classify;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 加盟商id */
    private Long franchiseId;

    /** 主键 */
    private Long id;

    /** 是否删除：0正常，1删除 */
    private Integer isDel;

    /** 行更新时间 */
    private Date modifyTime;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 聊天标题 */
    private String title;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 来源id */
    private String sourceId;

}