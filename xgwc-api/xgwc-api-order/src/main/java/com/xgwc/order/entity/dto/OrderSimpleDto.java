package com.xgwc.order.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderSimpleDto {

    @FieldDesc("谈单人员")
    private Long saleManId;

    @FieldDesc("订单来源ID（店铺id）")
    private Long storeId;

    @FieldDesc("下单日期")
    private Date orderTime;

    @FieldDesc("订单金额")
    private BigDecimal orderAmount;

    @FieldDesc("实收金额")
    private BigDecimal amount;

}