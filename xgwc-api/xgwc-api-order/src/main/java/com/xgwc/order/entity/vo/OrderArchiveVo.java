package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

import java.util.List;

@Data
public class OrderArchiveVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("定稿id")
    private Long id;

    @FieldDesc("订单id")
    private Long oderId;

    @FieldDesc("定稿名称")
    private String archiveName;

    @FieldDesc("定稿预览图")
    private String archiveImg;

    @FieldDesc("上传定稿源文件")
    private String archiveFiles;

    /** 业务id */
    @FieldDesc("业务id")
    private Long businessId;

    /** 业务名称 */
    @FieldDesc("业务名称")
    private String businessName;

    @FieldDesc("链接")
    private String linkUrl;

    @FieldDesc("审批状态: 0同意，1拒绝,2修改")
    private Integer reviewStatus;

    @FieldDesc("审批结果")
    private String reviewMsg;

    @FieldDesc("评分")
    private List<OrderScoreVo> scoreVo;


}
