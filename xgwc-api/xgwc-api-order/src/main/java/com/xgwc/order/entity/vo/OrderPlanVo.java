package com.xgwc.order.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

@Data
public class OrderPlanVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("编号")
    private Long id;

    @FieldDesc("订单id")
    @NotNull(message = "订单id不能为空")
    private Long oderId;

    @FieldDesc("交稿状态")
    @NotNull(message = "交稿状态不能为空")
    private Integer archiveType;

    @FieldDesc("是否删除：0否 1是，默认0")
    private Integer isDel;

    @FieldDesc("定稿名称")
    private String archiveName;

    @FieldDesc("定稿预览图")
    private String archiveImg;

    @FieldDesc("链接")
    private String linkUrl;

    @FieldDesc("上传定稿源文件")
    private String archiveFiles;

    /** 业务id */
    @FieldDesc("业务id")
    private Long businessId;

    /** 业务名称 */
    @FieldDesc("业务名称")
    private String businessName;
}
