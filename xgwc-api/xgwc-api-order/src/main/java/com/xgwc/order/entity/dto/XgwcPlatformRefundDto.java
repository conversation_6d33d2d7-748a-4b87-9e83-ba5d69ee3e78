package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.util.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class XgwcPlatformRefundDto {

    private Long id;

    @FieldDesc("平台订单编号")
    @Excel(name = "平台订单编号")
    private String tradeNo;

    @FieldDesc("付款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "付款时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @FieldDesc("平台退款编号")
    @Excel(name = "平台退款编号")
    private String refundNo;

    @FieldDesc("客户id")
    @Excel(name = "客户id")
    private String customerId;

    @FieldDesc("实付金额")
    @Excel(name = "实付金额")
    private BigDecimal payAmount;

    @FieldDesc("退款状态码")
    @Excel(name = "退款状态码")
    private String status;

    @FieldDesc("退款状态名称")
    @Excel(name = "退款状态名称")
    private String statusName;

    @FieldDesc("买家申请退款金额")
    @Excel(name = "买家申请退款金额")
    private BigDecimal refundAmount;

    @FieldDesc("实际退款")
    @Excel(name = "实际退款")
    private BigDecimal actualRefundAmount;

    @FieldDesc("退款申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款申请时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    @FieldDesc("退款申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "自动退款时间", width = 60, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date autoRefundTime;

    @FieldDesc("平台id")
    private String platformId;
    @FieldDesc("平台名称")
    private String platformName;
    @FieldDesc("平台店铺id")
    private String platformShopId;
    @FieldDesc("平台店铺名称")
    private String platformShopName;

    public void init() {
        //  退款状态 ：1取消退款,2已申请退款,3等待退货,4等待收货,5退款成功 */
        switch (this.status) {
            case "1" -> this.statusName = "取消退款";
            case "2" -> this.statusName = "已申请退款";
            case "3" -> this.statusName = "等待退货";
            case "4" -> this.statusName = "等待收货";
            case "5" -> this.statusName = "退款成功";
            default -> this.statusName = this.status;
        }
        if (this.refundTime != null) {
            this.autoRefundTime = DateUtils.addDays(this.refundTime, 2);
        }
    }

}