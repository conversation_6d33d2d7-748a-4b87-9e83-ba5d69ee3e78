package com.xgwc.order.controller;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.OrderRefundApply;
import com.xgwc.order.entity.dto.OrderRefundApplyDto;
import com.xgwc.order.entity.vo.OrderRefundApplyVo;
import com.xgwc.order.service.OrderRefundService;
import com.xgwc.order.util.StringUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

/**
 * 订单退款
 */
@RequestMapping("order/refund")
@RestController
public class OrderRefundController {

    @Resource
    private OrderRefundService orderRefundService;

    @PostMapping("addOrderRefundApply")
    public ApiResult addOrderRefundApply(@Valid @RequestBody OrderRefundApplyVo orderRefundApplyVo) {
        return orderRefundService.insertOrderRefundApply(orderRefundApplyVo);
    }

    @PostMapping("updateOrderRefundApply")
    public ApiResult updateOrderRefundApply(@RequestBody OrderRefundApply orderRefundApply) {
        if(orderRefundApply.getId() == null){
            return ApiResult.error("参数缺失");
        }
        int result = orderRefundService.updateOrderRefundApply(orderRefundApply);
        return result > 0 ? ApiResult.ok() :ApiResult.error("");
    }

    @GetMapping("getOrderRefundApply")
    public ApiResult getOrderRefundApply(@RequestParam Long applyId) {
        if(applyId == null) {
            return ApiResult.error("id不能为空");
        }
        OrderRefundApplyDto orderRefundApplyDto = orderRefundService.getOrderRefundApplyByApplyId(applyId);
        return ApiResult.ok(orderRefundApplyDto);
    }

    @RequestMapping("getOrderRefundInfoByOrderNo")
    public ApiResult getOrderRefundInfoByOrderNo(String orderNo) {
        if(StringUtils.isEmpty(orderNo)) {
            return ApiResult.error("id不能为空");
        }
        OrderRefundApplyDto orderRefundApplyDto = orderRefundService.getOrderRefundInfoByOrderNo(orderNo);
        return ApiResult.ok(orderRefundApplyDto);
    }
}
