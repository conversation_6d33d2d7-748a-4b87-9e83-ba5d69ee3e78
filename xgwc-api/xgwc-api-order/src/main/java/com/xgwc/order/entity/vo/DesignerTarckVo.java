package com.xgwc.order.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class DesignerTarckVo {

    @FieldDesc("设计师id")
    @NotNull(message = "设计师id不能为空")
    private Long designerId;

    @FieldDesc("跟进说明")
    private String details;

    @FieldDesc("下次回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "下次回访时间不能为空")
    private Date nextTime;

    @FieldDesc("跟进状态编码-字典")
    @NotNull(message = "跟进状态编码不能为空")
    private String trackCode;

    @FieldDesc("跟进状态名称-字典")
    @NotNull(message = "跟进状态名称不能为空")
    private String trackName;



}
