package com.xgwc.order.service.impl;

import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xgwc.common.util.DateUtils;
import com.xgwc.order.dao.CommonMapper;
import com.xgwc.order.dao.ReportBrandNightPerformanceStatsMapper;
import com.xgwc.order.entity.BrandNightPerformanceConfig;
import com.xgwc.order.entity.NightConfigRange;
import com.xgwc.order.entity.ReportBrandNightPerformanceStats;
import com.xgwc.order.entity.dto.DeptDto;
import com.xgwc.order.entity.dto.OrderSimpleDto;
import com.xgwc.order.entity.dto.ReportBrandNightPerformanceStatsDto;
import com.xgwc.order.entity.dto.StaffDto;
import com.xgwc.order.entity.vo.ReportBrandNightPerformanceStatsQueryVo;
import com.xgwc.order.entity.vo.Second;
import com.xgwc.order.service.IReportBrandNightPerformanceStatsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReportBrandNightPerformanceStatsServiceImpl implements IReportBrandNightPerformanceStatsService {

    @Resource
    private CommonMapper commonMapper;
    @Resource
    private ReportBrandNightPerformanceStatsMapper reportBrandNightPerformanceStatsMapper;


    /**
     * 查询品牌商晚间业绩统计列表
     *
     * @param reportBrandNightPerformanceStats 品牌商晚间业绩统计
     * @return 品牌商晚间业绩统计
     */
    @Override
    public List<ReportBrandNightPerformanceStatsDto> listPage(ReportBrandNightPerformanceStatsQueryVo reportBrandNightPerformanceStats) {
        return reportBrandNightPerformanceStatsMapper.listByQuery(reportBrandNightPerformanceStats);
    }


    @Transactional(rollbackFor = Exception.class)
    public void initStatsData(Long brandId, Long franchiseeId, List<BrandNightPerformanceConfig> configList, Date month) {
        Date monthStartTime = DateUtils.getStartOfMonth(month);
        Date monthEndTime = DateUtils.getEndOfMonth(month);

        Map<Long, Long> deptFinalPidMap = this.getDeptFinalPidMap(franchiseeId);
        Map<Long, Long> userFinalDeptIdMap = commonMapper.listFranchiseStaffByIds(null, franchiseeId, null).stream().collect(Collectors.toMap(StaffDto::getBindUserId, x -> deptFinalPidMap.get(x.getDeptId())));
        Map<Long, BrandNightPerformanceConfig> deptConfigMap = configList.stream().collect(Collectors.toMap(BrandNightPerformanceConfig::getDepartmentId, x -> x));

        int pageNum = 1;
        Date queryEndTime = DateUtils.addHour(monthEndTime, 12);
        Map<String, ReportBrandNightPerformanceStats> statsMap = Maps.newHashMap();
        Map<String, ReportBrandNightPerformanceStats> stats1Map = Maps.newHashMap();
        while (true) {
            PageMethod.startPage(pageNum, 1000);
            List<OrderSimpleDto> orderList = reportBrandNightPerformanceStatsMapper.queryOrderData(brandId, franchiseeId, monthStartTime, queryEndTime);
            if (orderList.isEmpty()) {
                break;
            }
            orderList.forEach(order -> {
                Long deptId = userFinalDeptIdMap.get(order.getSaleManId());
                BrandNightPerformanceConfig config = deptConfigMap.get(deptId);
                if (config == null) {
                    return;
                }
                Date date = order.getOrderTime();
                if (config.isCrossedDay()) {
                    Second<Boolean, LocalDate> result = this.getAdjustedDateWithCrossFlag(order.getOrderTime());
                    if (result.getFirst()) {
                        date = Date.from(result.getSecond().atStartOfDay(ZoneId.systemDefault()).toInstant());
                    }
                }
                if (!this.isInRange(date, config)) {
                    return;
                }
//                ReportBrandNightPerformanceStats stats = statsMap.computeIfAbsent(orderTime, k -> new ReportBrandNightPerformanceStats());
            });
            pageNum++;
        }

        List<ReportBrandNightPerformanceStats> statsDataList = Lists.newArrayList();
        reportBrandNightPerformanceStatsMapper.deleteByCond(brandId, franchiseeId, monthStartTime, monthEndTime);
        reportBrandNightPerformanceStatsMapper.insertList(statsDataList);
    }

    private boolean isInRange(Date date, BrandNightPerformanceConfig config) {
        for (NightConfigRange range : config.getRangeList()) {
            if (range.getMaxTime() < 999) {
                return true;
            }
        }
        return false;
    }

    private Map<Long, Long> getDeptFinalPidMap(Long franchiseeId) {
        List<DeptDto> deptList = commonMapper.listFranchiseDeptByIds(null, franchiseeId, null);
        Map<Long, Long> deptPidMap = deptList.stream().collect(Collectors.toMap(DeptDto::getDeptId, DeptDto::getPid));
        Map<Long, Long> deptFinalPidMap = Maps.newHashMap();
        deptList.forEach(dept -> deptFinalPidMap.put(dept.getDeptId(), this.getFinalPid(dept.getDeptId(), deptPidMap)));
        return deptFinalPidMap;
    }

    private Long getFinalPid(Long deptId, Map<Long, Long> deptPidMap) {
        Long pid = deptPidMap.get(deptId);
        if (pid == 0) {
            return deptId;
        }
        return this.getFinalPid(pid, deptPidMap);
    }

    private Second<Boolean, LocalDate> getAdjustedDateWithCrossFlag(Date date) {
        ZonedDateTime zdt = date.toInstant().atZone(ZoneId.systemDefault());
        LocalDate originalDate = zdt.toLocalDate();
        if (zdt.toLocalTime().isBefore(LocalTime.NOON)) {
            LocalDate adjustedDate = originalDate.minusDays(1);
            return new Second<>(true, adjustedDate);
        }
        return new Second<>(false, originalDate);
    }

}