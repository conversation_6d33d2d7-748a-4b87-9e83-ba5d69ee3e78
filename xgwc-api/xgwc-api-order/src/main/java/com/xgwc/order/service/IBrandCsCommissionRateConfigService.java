package com.xgwc.order.service;

import com.xgwc.common.entity.ApiListResult;
import com.xgwc.order.entity.cmd.BrandCsCommissionRateConfigCmd;
import com.xgwc.order.entity.cmd.BrandCsCommissionRateConfigUpdateCmd;
import com.xgwc.order.entity.dto.BrandCsCommissionRateConfigDto;
import com.xgwc.order.entity.vo.BrandCsCommissionRateConfigQueryVo;

public interface IBrandCsCommissionRateConfigService {
    /**
     * 查询客服提成完成率配置表
     *
     * @param id 客服提成完成率配置表主键
     * @return 客服提成完成率配置表
     */
    BrandCsCommissionRateConfigDto get(Long id);

    /**
     * 查询客服提成完成率配置表列表
     *
     * @param brandCsCommissionRateConfig 客服提成完成率配置表
     * @return 客服提成完成率配置表集合
     */
    ApiListResult<BrandCsCommissionRateConfigDto> listPage(BrandCsCommissionRateConfigQueryVo brandCsCommissionRateConfig);

    /**
     * 新增客服提成完成率配置表
     *
     * @param cmd 客服提成完成率配置表
     * @return 结果
     */
    int add(BrandCsCommissionRateConfigCmd cmd);

    /**
     * 修改客服提成完成率配置表
     *
     * @param cmd 客服提成完成率配置表
     * @return 结果
     */
    int update(BrandCsCommissionRateConfigUpdateCmd cmd);

    /**
     * 批量删除客服提成完成率配置表
     *
     * @param ids 需要删除的客服提成完成率配置表主键集合
     * @return 结果
     */
    int remove(Long[] ids);

}
