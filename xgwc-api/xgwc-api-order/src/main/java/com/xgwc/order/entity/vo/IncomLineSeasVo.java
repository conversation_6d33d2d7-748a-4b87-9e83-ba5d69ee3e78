package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class IncomLineSeasVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("编号")
    private Long id;

    @FieldDesc("进线客户id")
    private Long inLineId;

    @FieldDesc("进线客户id-最开始的")
    private Long hisInLineId;

    @FieldDesc("领取人")
    private String receiver;

    @FieldDesc("领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;



}
