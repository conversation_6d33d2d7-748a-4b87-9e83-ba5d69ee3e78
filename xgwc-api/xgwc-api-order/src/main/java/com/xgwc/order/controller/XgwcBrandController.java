package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcBrandDto;
import com.xgwc.order.entity.param.XgwcBrandParam;
import com.xgwc.order.service.XgwcBrandService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 品牌管理-品牌列表
 */
@RestController
@RequestMapping("/xgwcBrand")
@Slf4j
public class XgwcBrandController extends BaseController {

    @Resource
    private XgwcBrandService xgwcBrandService;

    /**
     * @param xgwcBrandParam 查询条件
     * @return 品牌列表
     * 查询品牌列表
     */
    @MethodDesc("查询品牌列表")
    @PreAuthorize("@ss.hasPermission('order:brand:list')")
    @PostMapping("/getXgwcBrandList")
    public ApiResult<XgwcBrandDto> getXgwcBrandList(@RequestBody XgwcBrandParam xgwcBrandParam) {
        startPage();
        List<XgwcBrandDto> xgwcBrandList = xgwcBrandService.getXgwcBrandList(xgwcBrandParam, new ArrayList<>());
        return getDataTable(xgwcBrandList);
    }

    /**
     * 获取品牌下拉列表
     *
     * @return 品牌列表
     */
    @MethodDesc("获取品牌下拉列表")
    @GetMapping("/getBrandDropDown")
    public ApiResult getXgwcBrandListInfo() {
        try {
            XgwcBrandParam xgwcBrandParam = new XgwcBrandParam();
            xgwcBrandParam.setStatus(0);
            List<XgwcBrandDto> result = xgwcBrandService.getXgwcBrandList(xgwcBrandParam, new ArrayList<>());
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取品牌下拉列表失败", e);
            return ApiResult.error("获取品牌下拉列表失败");
        }
    }

    /**
     * @param brandIds 查询条件
     * @return 品牌列表
     * 查询品牌列表
     */
    @MethodDesc("查询品牌列表")
    @PreAuthorize("@ss.hasPermission('order:brand:list')")
    @PostMapping("/getXgwcBrandListFindByIds")
    public List<XgwcBrandDto> getXgwcBrandListInfo(@RequestBody List<String> brandIds) {
        return xgwcBrandService.getXgwcBrandList(new XgwcBrandParam(),brandIds);
    }



    /**
     * @param xgwcBrandDto 新增品牌信息
     * @return 插入结果
     * 新增品牌信息
     */
    @MethodDesc("新增品牌信息")
    @PreAuthorize("@ss.hasPermission('order:brand:insert')")
    @Submit(fileds = "userId")
    @PostMapping("/saveXgwcBrand")
    public ApiResult saveXgwcBrand(@RequestBody XgwcBrandDto xgwcBrandDto) {
        return xgwcBrandService.saveXgwcBrand(xgwcBrandDto);
    }

    /**
     * @param brandId 品牌id
     * @return 品牌信息
     * 根据id查询品牌信息
     */
    @MethodDesc("根据id查询品牌信息")
    @PreAuthorize("@ss.hasPermission('order:brand:update')")
    @GetMapping("/getXgwcBrandById/{brandId}")
    public ApiResult getXgwcBrandById(@PathVariable Integer brandId) {
        return xgwcBrandService.getXgwcBrandById(brandId);
    }

    /**
     * @param xgwcBrandDto 修改信息
     * @return 修改结果
     * 修改品牌信息
     */
    @MethodDesc("修改品牌信息")
    @PreAuthorize("@ss.hasPermission('order:brand:update')")
    @Submit(fileds = "userId")
    @PostMapping("/updateXgwcBrand")
    public ApiResult updateXgwcBrandById(@RequestBody XgwcBrandDto xgwcBrandDto) {
        return xgwcBrandService.updateXgwcBrandById(xgwcBrandDto);
    }

    /**
     * @param brandId 品牌id
     * @return 品牌信息
     * 根据id修改状态
     */
    @MethodDesc("修改品牌状态")
    @PreAuthorize("@ss.hasPermission('order:brand:updateStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "brandId") Integer brandId,
                                 @RequestParam(value = "status") Integer status) {
        return xgwcBrandService.updateStatusById(brandId,status);
    }

}
