package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class ReportBrandNightPerformanceStatsQueryVo {

    @FieldDesc("品牌商ID")
    private Long brandId;

    @FieldDesc("统计维度: 1加盟商月, 2加盟商部门月, ")
    private Long statDimension;

    @FieldDesc("统计开始时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statStartTimeStart;

    @FieldDesc("统计开始时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statStartTimeEnd;

    @FieldDesc("统计截止时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statEndTimeStart;

    @FieldDesc("统计截止时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statEndTimeEnd;

    @FieldDesc("加盟商ID")
    private Long franchiseeId;

    @FieldDesc("部门ID")
    private Long departmentId;

}