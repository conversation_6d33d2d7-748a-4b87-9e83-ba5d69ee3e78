package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import java.util.Date;

@Data
public class FollowDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键ID")
    @Excel(name = "主键ID")
    private Long id;

    @FieldDesc("售后待审核ID：xgwc_after_agency_audit主键")
    @Excel(name = "售后待审核ID：xgwc_after_agency_audit主键")
    private Long agencyAuditId;

    @FieldDesc("协商状态:1待协商/2协商中/3协商成功/4协商失败")
    @Excel(name = "协商状态:1待协商/2协商中/3协商成功/4协商失败")
    private Integer consultType;

    @FieldDesc("跟进人")
    @Excel(name = "跟进人")
    private String followUserName;

    @FieldDesc("跟进说明")
    @Excel(name = "跟进说明")
    private String remark;

    @FieldDesc("回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回访时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date returnTime;

    @FieldDesc("跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "跟进时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

}
