package com.xgwc.order.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class IncomLineTarckVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("编号")
    private Long id;

    @FieldDesc("进线id")
    @NotNull(message = "进线id不能为空")
    private Long inLineId;

    @FieldDesc("跟进状态编码-字典")
    @NotNull(message = "跟进状态编码不能为空")
    private String trackCode;

    @FieldDesc("跟进状态名称-字典")
    @NotNull(message = "跟进状态名称不能为空")
    private String trackName;

    @FieldDesc("跟进状态编码-字典")
    private String trackCode2;

    @FieldDesc("跟进状态名称-字典")
    private String trackName2;

    @FieldDesc("下次回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "下次回访时间不能为空")
    private Date nextTime;

    @FieldDesc("关联单号id-订单查询")
    private Long orderId;

    @FieldDesc("关联单号编号-订单查询")
    private String orderNo;

    @FieldDesc("跟进人")
    private String trackBy;

    @FieldDesc("跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date trackTime;

    @FieldDesc("跟进概述")
    private String briefIntroduction;

    @FieldDesc("跟进详情")
    private String details;

    @FieldDesc("附件")
    private String resource;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("部门名称")
    private String deptName;



}
