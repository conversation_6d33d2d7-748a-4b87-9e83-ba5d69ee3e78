package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ReturnAgencyDispatchVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    @FieldDesc("派单工作台Id")
    @NotNull(message = "派单工作台Id不能为空")
    private Long dispatchId;

    @FieldDesc("订单Id")
    @NotNull(message = "订单Id不能为空")
    private Long orderId;

    @FieldDesc("退回备注")
    @NotNull(message = "退回备注不能为空")
    private String explanatory;

}
