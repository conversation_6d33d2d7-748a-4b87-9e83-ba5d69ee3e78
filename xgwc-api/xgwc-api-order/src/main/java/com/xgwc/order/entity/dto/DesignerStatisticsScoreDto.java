package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@Data
public class DesignerStatisticsScoreDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("所属品牌商")
    @Excel(name = "所属品牌商")
    private Long brandId;

    @FieldDesc("设计师id")
    @Excel(name = "设计师id")
    private Long designerId;

    @FieldDesc("中评数量")
    @Excel(name = "中评数量")
    private Long generalNum;

    @FieldDesc("好评数量")
    @Excel(name = "好评数量")
    private Long goodNum;

    @FieldDesc("差评数量")
    @Excel(name = "差评数量")
    private Long poorNum;

    @FieldDesc("评分模块")
    @Excel(name = "评分模块")
    private String scoreModel;

    @FieldDesc("评分模块总评分数量")
    @Excel(name = "总评分数量")
    private Long scoreNum;

    @FieldDesc("总评分")
    @Excel(name = "总评分")
    private Long scoreTotal;


    @FieldDesc("平均分")
    @Excel(name = "平均分")
    private Long avgTotal;


    @FieldDesc("评分类型:0 加盟商 1被换评价")
    @Excel(name = "评分类型:0 加盟商 1被换评价")
    private Long scoreType;

    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("avgTotal",getAvgTotal())
            .append("brandId",getBrandId())
            .append("designerId",getDesignerId())
            .append("generalNum",getGeneralNum())
            .append("goodNum",getGoodNum())
            .append("poorNum",getPoorNum())
            .append("scoreModel",getScoreModel())
            .append("scoreNum",getScoreNum())
            .append("scoreTotal",getScoreTotal())
            .append("scoreType",getScoreType())
        .toString();
    }
}
