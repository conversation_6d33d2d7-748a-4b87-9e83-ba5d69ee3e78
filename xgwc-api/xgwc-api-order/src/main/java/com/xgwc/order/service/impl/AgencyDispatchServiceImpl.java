package com.xgwc.order.service.impl;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.*;
import com.xgwc.order.entity.Dispatch;
import com.xgwc.order.entity.SendBack;
import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.*;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.order.service.IAgencyDispatchService;
import com.xgwc.order.entity.AgencyDispatch;
import org.springframework.transaction.annotation.Transactional;


@Service
public class AgencyDispatchServiceImpl implements IAgencyDispatchService {
    @Resource
    private AgencyDispatchMapper agencyDispatchMapper;
    @Resource
    private DispatchMapper dispatchMapper;
    @Resource
    private SendBackMapper sendBackMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private AlreadyDispatchMapper alreadyDispatchMapper;

    /**
     * 查询派单表
     * 
     * @param id 派单表主键
     * @return 派单表
     */
    @Override
    public AgencyDispatchInfoDto selectAgencyDispatchById(Long id) {
        AgencyDispatchDto agencyDispatchDto = agencyDispatchMapper.selectAgencyDispatchById(id);
        if(agencyDispatchDto == null || agencyDispatchDto.getOrderId() == null){
            throw new ApiException("代派单记录不存在");
        }
        AgencyDispatchInfoDto agencyDispatchInfoDto = BeanUtil.copyProperties(agencyDispatchDto, AgencyDispatchInfoDto.class);
        OrderDto orderDto = orderMapper.selectOrderById(agencyDispatchDto.getOrderId());
        if(orderDto ==null){
            return null;
        }
//        OrderDto p = new OrderDto();
//        if(orderDto.getPid() == 0) {
//            BeanUtil.copyProperties(orderDto, p);
//            List<SubOrderDto> subOrders = orderMapper.selectSubOrderList(p.getId());
//            if(p.getDesignerId() != null) subOrders.add(0,BeanUtil.copyProperties(orderDto, SubOrderDto.class));
//            p.setSubOrders(subOrders);
//        } else {
//            p = orderMapper.selectOrderById(orderDto.getPid());
//            List<SubOrderDto> subOrderDtos = new ArrayList<>();
//            subOrderDtos.add(BeanUtil.copyProperties(orderDto, SubOrderDto.class));
//            p.setSubOrders(subOrderDtos);
//        }
        agencyDispatchInfoDto.setDispatchId(orderDto.getDesignerId());
        agencyDispatchInfoDto.setOrderDate(orderDto.getOrderDate());
        agencyDispatchInfoDto.setOrderNo(orderDto.getOrderNo());
        agencyDispatchInfoDto.setTaobaoId(orderDto.getTaobaoId());
        agencyDispatchInfoDto.setSaleManId(orderDto.getSaleManId());
        agencyDispatchInfoDto.setSaleManName(orderDto.getSaleManName());
        agencyDispatchInfoDto.setFranchiseName(orderDto.getFranchiseName());
        agencyDispatchInfoDto.setStoreName(orderDto.getStoreName());
        agencyDispatchInfoDto.setOrderAmount(orderDto.getOrderAmount());
        agencyDispatchInfoDto.setAmount(orderDto.getAmount());
        agencyDispatchInfoDto.setArchiveExpectTime(orderDto.getArchiveExpectTime());
        agencyDispatchInfoDto.setAllotRemark(orderDto.getAllotRemark());
        agencyDispatchInfoDto.setAllotFile(orderDto.getAllotFile());
        agencyDispatchInfoDto.setStateDicCode(orderDto.getStateDicCode());
        agencyDispatchInfoDto.setStateDicName(orderDto.getStateDicName());
        agencyDispatchInfoDto.setSubOrderDtoList(alreadyDispatchMapper.findSubOrderByDispatchId(id,agencyDispatchDto.getDispatchUserId()));
//        if(!p.getSubOrders().isEmpty()){
//            p.setSubOrders(p.getSubOrders().stream()
//                    .filter(subOrder -> Optional.ofNullable(subOrder.getAllotUserId())
//                            .filter(userId -> userId.equals(SecurityUtils.getUserId()))
//                            .isPresent())
//                    .collect(Collectors.toList()));
//            agencyDispatchInfoDto.setSubOrderDtoList(p.getSubOrders());
//        }
        return agencyDispatchInfoDto;
    }

    /**
     * 查询我的待派单列表
     * @param agencyDispatch 派单表
     * @return 派单表
     */
    @Override
    public List<AgencyDispatchPageDto> selectMyAgencyDispatchList(AgencyDispatchQueryVo agencyDispatch) {
        agencyDispatch.setDispatchUserId(SecurityUtils.getUserId());
        return agencyDispatchMapper.findMyAgencyDispatchList(agencyDispatch);
    }

    /**
     * 新增派单表
     * 
     * @param dto 派单表
     * @return 结果
     */
    @Override
    public int insertAgencyDispatch(AgencyDispatchVo dto) {
        AgencyDispatch agencyDispatch = BeanUtil.copyProperties(dto, AgencyDispatch.class);
        agencyDispatch.setCreateTime(DateUtils.getNowDate());
        agencyDispatch.setBrandId(SecurityUtils.getSysUser().getBrandId());
        return agencyDispatchMapper.insertAgencyDispatch(agencyDispatch);
    }

    /**
     * 修改派单表
     * 
     * @param dto 派单表
     * @return 结果
     */
    @Override
    public int updateAgencyDispatch(AgencyDispatchVo dto) {
        AgencyDispatch agencyDispatch = BeanUtil.copyProperties(dto, AgencyDispatch.class);
        agencyDispatch.setUpdateTime(DateUtils.getNowDate());
        return agencyDispatchMapper.updateAgencyDispatch(agencyDispatch);
    }

    /**
     * 批量删除派单表
     * 
     * @param ids 需要删除的派单表主键
     * @return 结果
     */
    @Override
    public int deleteAgencyDispatchByIds(Long[] ids) {
        return agencyDispatchMapper.deleteAgencyDispatchByIds(ids);
    }

    /**
     * 删除派单表信息
     * 
     * @param id 派单表主键
     * @return 结果
     */
    @Override
    public int deleteAgencyDispatchById(Long id) {
        return agencyDispatchMapper.deleteAgencyDispatchById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int distribution(DistributionVo dto) {
        int newDispatchNumber = dto.getDispatchNumber();
        // 查询订单当前已派设计师数量
        DispatchDto dispatchDto = dispatchMapper.selectDispatchById(dto.getDispatchId());
        List<AgencyDispatchDto> list = agencyDispatchMapper.selectAgencyDispatchByOrderId(dto.getOrderId());
        // 累加当前总已派人数
        int currentTotalDispatched = list.stream()
                .mapToInt(AgencyDispatchDto::getDispatchNumber)
                .sum();
        int totalAfterInsert = currentTotalDispatched + newDispatchNumber;
        if (totalAfterInsert > dispatchDto.getNeedDispatchNumber()) {
            throw new ApiException("已派设计师数量已达上限");
        }
        // 插入派单记录
        AgencyDispatch agencyDispatch = BeanUtil.copyProperties(dto, AgencyDispatch.class);
        agencyDispatch.setUpdateTime(DateUtils.getNowDate());
        agencyDispatch.setBrandId(SecurityUtils.getSysUser().getBrandId());
        int result = agencyDispatchMapper.insertAgencyDispatch(agencyDispatch);
        // 更新主派单表的已派人数
        Dispatch dispatch = BeanUtil.copyProperties(dispatchDto, Dispatch.class);
        dispatch.setDispatchNumber(dispatchDto.getDispatchNumber() + newDispatchNumber);
        dispatchMapper.updateDispatch(dispatch);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int receive(ReceiveVo dto) {
        Long orderId = dto.getOrderId();
        Long dispatchId = dto.getDispatchId();
        int receiveCount = dto.getDispatchNumber();
        // 查询已派列表 + 当前派单信息
        List<AgencyDispatchDto> existing = agencyDispatchMapper.selectAgencyDispatchByOrderId(orderId);
        DispatchDto dispatchDto = dispatchMapper.selectDispatchById(dispatchId);
        if (dispatchDto == null) {
            throw new ApiException("派单不存在");
        }
        // 计算累计领取数量
        int totalAfterReceive = receiveCount;
        if (!existing.isEmpty()) {
            totalAfterReceive += existing.stream().mapToInt(AgencyDispatchDto::getDispatchNumber).sum();
        }
        if (totalAfterReceive > dispatchDto.getNeedDispatchNumber()) {
            throw new ApiException("已派设计师数量已达上限");
        }
        // 插入领取记录
        AgencyDispatch agencyDispatch = BeanUtil.copyProperties(dto, AgencyDispatch.class);
        SysUser sysUser = SecurityUtils.getSysUser();
        agencyDispatch.setUpdateTime(DateUtils.getNowDate());
        agencyDispatch.setDispatchUserId(sysUser.getUserId());
        agencyDispatch.setBrandId(sysUser.getBrandId());
        int insertResult = agencyDispatchMapper.insertAgencyDispatch(agencyDispatch);
        // 更新派单主表
        Dispatch dispatch = BeanUtil.copyProperties(dispatchDto, Dispatch.class);
        dispatch.setDispatchNumber(dispatchDto.getDispatchNumber() + receiveCount);
        dispatchMapper.updateDispatch(dispatch);
        return insertResult;
    }


    @Override
    public int returnAgencyDispatch(ReturnAgencyDispatchVo dto) {
        AgencyDispatchDto agencyDispatchDto = agencyDispatchMapper.selectAgencyDispatchById(dto.getId());
        if(agencyDispatchDto == null){
            throw new ApiException("派单记录不存在");
        }
        DispatchDto dispatchDto = dispatchMapper.selectDispatchById(dto.getDispatchId());
        if(dispatchDto == null){
            throw new RuntimeException("派单不存在");
        }
        int result = 0;
        int dispatchNumber = 0;
        if(agencyDispatchDto.getAssignedQuantity() == 0){ // 等于0 代表还没未派单
            dispatchNumber = agencyDispatchDto.getDispatchNumber();
            result = agencyDispatchMapper.deleteAgencyDispatchById(dto.getId());
        } else {
            dispatchNumber = agencyDispatchDto.getDispatchNumber() - agencyDispatchDto.getAssignedQuantity();
            AgencyDispatch agencyDispatch = new AgencyDispatch();
            agencyDispatch.setId(dto.getId());
            agencyDispatch.setDispatchNumber(agencyDispatchDto.getDispatchNumber() - dispatchNumber);
            result = agencyDispatchMapper.updateAgencyDispatch(agencyDispatch);
        }
        // 更新派单表
        Dispatch dispatch = BeanUtil.copyProperties(dispatchDto, Dispatch.class);
        dispatch.setDispatchNumber(dispatchDto.getDispatchNumber() - dispatchNumber);
        dispatch.setReturnNumber(dispatchDto.getReturnNumber()+1);
        dispatchMapper.updateDispatch(dispatch);
        // 添加退回记录
        SendBack sendBack = new SendBack();
        sendBack.setDispatchId(dto.getDispatchId());
        sendBack.setOrderId(dto.getOrderId());
        sendBack.setSendTime(DateUtils.getNowDate());
        sendBack.setCreateBy(SecurityUtils.getNickName());
        sendBack.setExplanatory(dto.getExplanatory());
        sendBackMapper.insertSendBack(sendBack);
        return result;
    }

    @Override
    public int updateRemark(UpdateRemarkVo updateRemarkVo) {
        AgencyDispatchDto agencyDispatchDto = agencyDispatchMapper.selectAgencyDispatchById(updateRemarkVo.getId());
        if(agencyDispatchDto == null){
            throw new ApiException("派单记录不存在");
        }
        AgencyDispatch record = new AgencyDispatch();
        record.setId(updateRemarkVo.getId());
        record.setDispatchRemark(updateRemarkVo.getDispatchRemark());
        return agencyDispatchMapper.updateAgencyDispatch(record);
    }

    @Override
    public List<AgencyRecordPageDto> agencyRecordList(AgencyRecordQueryVo params) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null) return List.of();
        params.setBrandId(brandId);
        return agencyDispatchMapper.findAgencyRecordList(params);
    }

    @Override
    public int countEmergency() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null){
            return 0;
        }
        return agencyDispatchMapper.countEmergency(brandId);
    }
}
