package com.xgwc.order.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.order.entity.vo.FollowVo;
import com.xgwc.order.service.IFollowService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

/**
 * 跟进Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("/follow")
public class FollowController extends BaseController {
    @Autowired
    private IFollowService followService;

    /**
     * 新增跟进
     */
    @MethodDesc("新增跟进")
    @PreAuthorize("@ss.hasPermission('follow:follow:add')")
    @Log(title = "跟进", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody FollowVo follow) {
        return toAjax(followService.insertFollow(follow));
    }


}
