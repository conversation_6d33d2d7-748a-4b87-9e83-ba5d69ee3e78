package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.OrderRefundApplyApproveDto;
import com.xgwc.order.entity.dto.OrderRefundApplyInfoDto;
import com.xgwc.order.entity.vo.OrderRefundApplyApproveQueryVo;
import com.xgwc.order.service.IOrderRefundApplyService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/orderRefundApply")
@RestController
public class OrderRefundApplyController extends BaseController {

    @Resource
    private IOrderRefundApplyService orderRefundApplyService;

    @MethodDesc("退款申请 - 全部审批列表")
    @PreAuthorize("@ss.hasPermission('refund:flow:all')")
    @GetMapping("/allApprove")
    public ApiResult<List<OrderRefundApplyApproveDto>> allApprove(OrderRefundApplyApproveQueryVo queryVo) {
        return getDataTable(orderRefundApplyService.allApprove(queryVo));
    }

    @MethodDesc("退款申请 - 退款详情")
    @PreAuthorize("@ss.hasPermission('refund:flow:view')")
    @GetMapping("/brand/getInfo/{applyId}")
    public ApiResult<OrderRefundApplyInfoDto> getInfo(@PathVariable Long applyId) {
        return ApiResult.ok(orderRefundApplyService.getInfo(applyId));
    }

}