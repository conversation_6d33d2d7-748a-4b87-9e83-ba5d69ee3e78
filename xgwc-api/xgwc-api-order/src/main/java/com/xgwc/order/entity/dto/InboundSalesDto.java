package com.xgwc.order.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;
import java.util.List;

@Data
public class InboundSalesDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("进线咨询人数")
    @Excel(name = "进线咨询人数")
    private Integer inquiriesNumber;

    @FieldDesc("晚间上线时长")
    @Excel(name = "晚间上线时长")
    private Integer onlineDuration;

    @FieldDesc("部门id")
    @Excel(name = "部门id")
    private Long deptId;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("品牌商id")
    @Excel(name = "品牌商id")
    private Long brandId;

    @FieldDesc("进行日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "进行日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date proceedDate;

    @FieldDesc("客服id")
    @Excel(name = "客服id")
    private Long userId;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("加盟商名称")
    @Excel(name = "加盟商名称")
    private String franchiseName;

    @FieldDesc("客服名称")
    @Excel(name = "客服名称")
    private String staffName;

    @FieldDesc("部门名称")
    @Excel(name = "部门")
    private String deptName;

}
