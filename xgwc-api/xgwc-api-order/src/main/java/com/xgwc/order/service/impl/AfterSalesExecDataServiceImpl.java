package com.xgwc.order.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.entity.vo.OrderRefundApplyVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.AfterSalesExecDataMapper;
import com.xgwc.order.service.IAfterSalesExecDataService;
import com.xgwc.order.entity.AfterSalesExecData;
import com.xgwc.order.entity.vo.AfterSalesExecDataVo;
import com.xgwc.order.entity.dto.AfterSalesExecDataDto;
import com.xgwc.order.entity.vo.AfterSalesExecDataQueryVo;


@Service
public class AfterSalesExecDataServiceImpl implements IAfterSalesExecDataService {
    @Resource
    private AfterSalesExecDataMapper afterSalesExecDataMapper;

    /**
     * 查询售后表单数据
     *
     * @param id 售后表单数据主键
     * @return 售后表单数据
     */
    @Override
    public AfterSalesExecDataDto selectAfterSalesExecDataById(Long id) {
        return afterSalesExecDataMapper.selectAfterSalesExecDataById(id);
    }

    @Override
    public void insertAfterSalesExecData(OrderRefundApplyVo dto) {
        List<AfterSalesExecData> list = new ArrayList<>();
        JSONObject data = JSONObject.parseObject(JSON.toJSONString(dto));
        for (String key : data.keySet()) {
            AfterSalesExecData exData = new AfterSalesExecData();
            exData.setBusinessId(dto.getId());
            exData.setKeyName(key);
            exData.setKeyValue(data.getString(key));
            exData.setCreateBy(dto.getApplyUserName());
            list.add(exData);
        }
        // 计算佣金
        String refundCommisionJson = data.getString("refundCommisionJson");
        if (StringUtils.isNotBlank(refundCommisionJson)) {
            BigDecimal totalOld = BigDecimal.ZERO;
            BigDecimal totalNew = BigDecimal.ZERO;
            JSONArray refundArr = JSONArray.parseArray(refundCommisionJson);
            for (int i = 0; i < refundArr.size(); i++) {
                JSONObject obj = refundArr.getJSONObject(i);
                totalOld = totalOld.add(obj.getBigDecimal("commisionAmount"));
                totalNew = totalNew.add(new BigDecimal(obj.getString("afterCommisionAmount")));
            }
            AfterSalesExecData oldData = new AfterSalesExecData();
            oldData.setBusinessId(dto.getId());
            oldData.setKeyName("preCommission");
            oldData.setKeyValue(totalOld.toPlainString());
            oldData.setCreateBy(dto.getApplyUserName());
            list.add(oldData);
            AfterSalesExecData newData = new AfterSalesExecData();
            newData.setBusinessId(dto.getId());
            newData.setKeyName("lastCommission");
            newData.setKeyValue(totalNew.toPlainString());
            newData.setCreateBy(dto.getApplyUserName());
            list.add(newData);
        }
        // 批量插入
        afterSalesExecDataMapper.insertAfterSalesExecData(list);
    }



}
