package com.xgwc.order.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcBrandDto;
import com.xgwc.order.entity.param.XgwcBrandParam;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  09:41
 */
public interface XgwcBrandService {

    /**
     * 获取品牌列表
     *
     * @param xgwcBrandParam 查询参数
     * @param brandIds       品牌id列表
     * @return 品牌列表
     */
    List<XgwcBrandDto> getXgwcBrandList(XgwcBrandParam xgwcBrandParam, List<String> brandIds);

    /**
     * 保存品牌
     * @param xgwcBrandDto 品牌信息
     * @return 保存结果
     */
    ApiResult saveXgwcBrand(XgwcBrandDto xgwcBrandDto);

    /**
     * 根据id获取品牌信息
     * @param brandId 品牌id
     * @return 品牌信息
     */
    ApiResult getXgwcBrandById(Integer brandId);

    /**
     * 更新品牌信息
     * @param xgwcBrandDto 品牌信息
     * @return 更新结果
     */
    ApiResult updateXgwcBrandById(XgwcBrandDto xgwcBrandDto);

    /**
     * 更新品牌状态
     * @param brandId 品牌id
     * @param status 状态
     * @return 更新结果
     */
    ApiResult updateStatusById(Integer brandId, Integer status);
}
