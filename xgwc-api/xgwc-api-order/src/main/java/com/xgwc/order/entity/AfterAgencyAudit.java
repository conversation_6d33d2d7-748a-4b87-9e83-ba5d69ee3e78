package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class AfterAgencyAudit {

private static final long serialVersionUID=1L;

    /** 申请记录ID：order_refund_apply主键 */
    private Long applyId;

    /** 品牌商id */
    private Long brandId;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 加盟商id */
    private Long franchiseId;

    /** 主键ID */
    private Long id;

    /** 订单编号 */
    private String orderNo;

    /** 领取人 */
    private Long receiverId;

    /** 状态：0正常，1非正常 */
    private Integer status;

    /** 更新时间 */
    private Date updateTime;

    /** 审核状态：0待审核，1已审核 */
    private Integer checkStatus;

    /** 审核人 */
    private Long checkId;

}