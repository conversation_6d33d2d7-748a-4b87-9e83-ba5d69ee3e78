package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UpdateRemarkVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    @FieldDesc("退回备注")
    @NotNull(message = "退回备注不能为空")
    private String dispatchRemark;

}
