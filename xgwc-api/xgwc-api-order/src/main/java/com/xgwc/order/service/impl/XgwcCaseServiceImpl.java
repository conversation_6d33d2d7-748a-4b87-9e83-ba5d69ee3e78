package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.dao.XgwcCaseMapper;
import com.xgwc.order.entity.XgwcCase;
import com.xgwc.order.entity.dto.StaffDto;
import com.xgwc.order.entity.dto.XgwcCaseDto;
import com.xgwc.order.entity.es.CaseIndex;
import com.xgwc.order.entity.es.CaseVo;
import com.xgwc.order.entity.vo.XgwcCaseQueryVo;
import com.xgwc.order.entity.vo.XgwcCaseUpStatusVo;
import com.xgwc.order.entity.vo.XgwcCaseVo;
import com.xgwc.order.service.EsService;
import com.xgwc.order.service.IXgwcCaseService;
import com.xgwc.order.service.XgwcBusinessService;
import com.xgwc.redis.constants.LockCacheKey;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.user.feign.api.StaffFeign;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.xgwc.common.constants.CommonConstant.FLAG_BRAND;
import static com.xgwc.common.constants.CommonConstant.FLAG_FRANCHISE;


@Service
@Slf4j
public class XgwcCaseServiceImpl implements IXgwcCaseService  {
    @Resource
    private XgwcCaseMapper xgwcCaseMapper;
    @Resource
    private EsService esService;
    @Resource
    private XgwcBusinessService xgwcBusinessService;
    @Resource
    private StaffFeign staffFeign;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private OrderMapper orderMapper;

    @Override
    public List<XgwcCaseDto> selectXgwcCaseList(XgwcCaseQueryVo xgwcCaseQueryVo) {
        SysUser user = SecurityUtils.getSysUser();
        //1：品牌商，2：加盟商，3：设计师，4:品牌商员工，5：加盟商员工
        switch (user.getUserType()) {
            case 1:
            case 4:
                xgwcCaseQueryVo.setBrandId(user.getBrandId());
                break;
            case 2:
            case 5:
                xgwcCaseQueryVo.setFranchiseId(user.getFranchiseId());
                break;
            default:
                xgwcCaseQueryVo.setBrandId(0L);
                xgwcCaseQueryVo.setFranchiseId(0L);
                break;
        }
        return xgwcCaseMapper.selectXgwcCaseList(xgwcCaseQueryVo);
    }

    /**
     * 查询案例表
     * @param caseId 案例表主键
     * @return 案例表
     */
    @Override
    public XgwcCaseDto selectXgwcCaseByCaseId(Long caseId) {
        return xgwcCaseMapper.selectXgwcCaseByCaseId(caseId);
    }

    /**
     * 新增案例表
     * @param dto 案例表
     * @return 结果
     */
    @Override
    public int insertXgwcCase(XgwcCaseVo dto) {
        XgwcCase xgwcCase = BeanUtil.copyProperties(dto, XgwcCase.class);
        xgwcCase.setCreateTime(DateUtils.getNowDate());
        int i = xgwcCaseMapper.insertXgwcCase(xgwcCase);
        if (i > 0) {
            // 异步更新ES
            CompletableFuture.runAsync(() -> anyCaseIndex(xgwcCase));
        }
        return i;
    }

    private void anyCaseIndex(XgwcCase xgwcCase) {
        try {
            //todo 案例目前没有业务类型
//            XgwcBusinessVo xgwcBusiness = xgwcBusinessService.getXgwcBusinessByIdFeign(xgwcCase.getBusinessId());
            CaseIndex caseIndex = new CaseIndex();
            caseIndex.setCaseId(xgwcCase.getCaseId());
            caseIndex.setTitle(xgwcCase.getTitle());
            caseIndex.setKeyword(xgwcCase.getKeyword());
            caseIndex.setTypeCodes(orderMapper.findBusinessLevelByOrderId(xgwcCase.getOderId()));
            caseIndex.setFilePath(xgwcCase.getArchiveFiles());
            caseIndex.setCoverUrl(xgwcCase.getArchiveImg());
            caseIndex.setMoney(xgwcCase.getMoney().doubleValue());
            caseIndex.setLevel(xgwcCase.getCaseLevel());
            caseIndex.setBrandId(xgwcCase.getBrandId());
            caseIndex.setFranchiseId(xgwcCase.getFranchiseId());
            caseIndex.setCreate_time(new Date());
            esService.addCaseData(caseIndex);
        } catch (Exception e) {
            log.error("异步添加案例到ES失败：{}", e.getMessage());
        }
    }

    /**
     * 修改案例表
     * @param dto 案例表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateXgwcCase(XgwcCaseVo dto) {
        XgwcCase xgwcCase = BeanUtil.copyProperties(dto, XgwcCase.class);
        xgwcCase.setUpdateTime(DateUtils.getNowDate());
        int result = xgwcCaseMapper.updateXgwcCase(xgwcCase);
        if (result > 0) {
            // 异步更新ES
            CompletableFuture.runAsync(() -> anyCaseIndex(xgwcCase));
        }
        return result;
    }

    @Override
    public int upStatus(XgwcCaseUpStatusVo vo) {
        return xgwcCaseMapper.upStatus(vo);
    }

    @Override
    public int deleteByIds(Long[] ids) {
        return xgwcCaseMapper.deleteByIds(ids);
    }

    /**
     * 下载案例
     *
     * @param caseId  案例表主键
     * @param isFlag  0-品牌商 1-加盟商
     * @param brandId 品牌商ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult downloadCase(Long caseId, Integer isFlag, Long brandId) {
        // 参数校验
        if (caseId == null || caseId <= 0) {
            return ApiResult.error("案例ID无效");
        }

        // 查询案例是否存在
        XgwcCaseDto xgwcCaseDto = xgwcCaseMapper.selectXgwcCaseByCaseId(caseId);
        if (xgwcCaseDto == null) {
            return ApiResult.error("案例不存在");
        }

        // 获取员工信息
        Long userId = SecurityUtils.getSysUser().getUserId();
        // 判断当前用户是否为管理员
        StaffDto staffDto = null;
        boolean admin = xgwcCaseMapper.selectAdmin(userId);
        if (!admin) {
            ApiResult staffResult = getMaxDownloadLimit(xgwcCaseDto.getBrandId(),isFlag);
            if (!("OK").equals(staffResult.getMessage())) {
                return staffResult;
            }
            staffDto = (StaffDto) staffResult.getData();

            // 检查下载次数
            Integer maxDownloadCount = staffDto.getMaxDownloadCount();
            if (staffDto.getDownloadCount() >= maxDownloadCount) {
                return ApiResult.error("下载失败，已超过今日最大限制次数");
            }
        }

        // 获取分布式锁
        String cacheKey = LockCacheKey.DOWNLOAD_CASE + ":" + caseId;
        String lockValue = UUID.randomUUID().toString();
        try {
            boolean lockAcquired = redisUtil.tryLock(cacheKey, lockValue, 3000);
            if (!lockAcquired) {
                log.warn("案例库下载-获取分布式锁失败，案例id={}", caseId);
                return ApiResult.error("系统繁忙，请稍后再试");
            }

            if (!admin) {
                // 再次获取最新下载次数
                ApiResult freshResult = getMaxDownloadLimit(xgwcCaseDto.getBrandId(), isFlag);
                StaffDto freshStaffDto = (StaffDto) freshResult.getData();
                if (freshStaffDto == null) {
                    log.warn("刷新员工信息为空，userId={}", userId);
                    return ApiResult.error("员工信息异常");
                }

                Integer freshDownloadCount = freshStaffDto.getDownloadCount();
                if (freshDownloadCount >= staffDto.getMaxDownloadCount()) {
                    return ApiResult.error("下载失败，已超过今日最大限制次数");
                }

                // 更新下载次数
                int updated = 0;
                if (Objects.equals(isFlag, FLAG_BRAND)) {
                    freshStaffDto.setDownloadCount(staffDto.getDownloadCount() + 1);
                    updated = xgwcCaseMapper.updateBrandStaffDownloadCount(freshStaffDto);
                }
                if (Objects.equals(isFlag, FLAG_FRANCHISE)) {
                    StaffDto downloadCount = xgwcCaseMapper.selectFranchiseStaffDownloadCount(xgwcCaseDto.getBrandId(), freshStaffDto.getId());
                    freshStaffDto.setDownloadCount(downloadCount != null ? downloadCount.getDownloadCount() + 1 : 1);
                    freshStaffDto.setBrandId(xgwcCaseDto.getBrandId());
                    freshStaffDto.setCreateBy(SecurityUtils.getNickName());
                    freshStaffDto.setUpdateBy(SecurityUtils.getNickName());
                    updated = xgwcCaseMapper.updateFranchiseStaffDownloadCount(freshStaffDto);
                }
                if (updated<=0) {
                    log.error("更新员工下载次数失败，staffVo={}", freshStaffDto);
                    throw new ApiException("下载失败");
                }
            }

            // 更新案例下载次数
            Long downloadCounts = (long) (xgwcCaseDto.getDownloadCount() + 1);
            int updatedResult = xgwcCaseMapper.updateCaseDownloadCount(downloadCounts, caseId);
            if (updatedResult <= 0) {
                log.error("更新案例下载次数到数据库失败，案例ID={}", caseId);
                throw new ApiException("下载失败");
            }

            // 更新ES
            CaseIndex caseIndex = new CaseIndex();
            caseIndex.setCaseId(caseId);
            caseIndex.setDownloadCount(downloadCounts);
            boolean caseData = esService.updateCaseData(caseIndex);
            if (!caseData) {
                log.error("更新案例下载次数到ES失败，案例ID={}", caseId);
                throw new ApiException("下载失败");
            }

            log.info("案例下载成功，案例ID={}, 用户ID={}", caseId, userId);
            return ApiResult.ok(xgwcCaseDto);

        } catch (Exception e) {
            log.error("案例下载过程中发生异常，caseId={}, userId={}", caseId, userId, e);
            return ApiResult.error("系统异常，请稍后再试");
        } finally {
            try {
                redisUtil.unlock(cacheKey, lockValue);
            } catch (Exception unlockEx) {
                log.error("释放分布式锁异常，cacheKey={}, 错误信息：{}", cacheKey, unlockEx.getMessage(), unlockEx);
            }
        }
    }

    @Override
    public ApiResult getMaxDownloadLimit(Long brandId, Integer isFlag) {
        Long userId = SecurityUtils.getSysUser().getUserId();

        ApiResult staffResult = staffFeign.getStaffByUserId(userId, isFlag, brandId);

        if (!"OK".equals(staffResult.getMessage())) {
            return ApiResult.error("获取员工信息失败");
        }

        Object data = staffResult.getData();
        if (data == null) {
            log.warn("员工信息为空，userId={}", userId);
            return ApiResult.error("员工信息异常");
        }

        StaffDto staffDto;
        if (data instanceof StaffDto) {
            staffDto = (StaffDto) data;
        } else if (data instanceof Map) {
            ObjectMapper objectMapper = new ObjectMapper();
            staffDto = objectMapper.convertValue(data, StaffDto.class);
        } else {
            log.warn("无法识别的数据类型，userId={}", userId);
            return ApiResult.error("员工信息异常");
        }

        if (staffDto == null) {
            log.warn("员工信息转换失败，userId={}", userId);
            return ApiResult.error("员工信息异常");
        }
        return ApiResult.ok(staffDto);
    }

    @Override
    public ApiListResult search(CaseVo queryVo) {
        queryVo.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
        queryVo.setBranchId(SecurityUtils.getSysUser().getBrandId());
        ApiListResult rspData = new ApiListResult();
        JSONObject esResult = esService.searchCaseData(queryVo);
        if (esResult == null) {
            return rspData;
        }
        Long total = esResult.getLong("total");
        JSONArray rows = esResult.getJSONArray("rows");
        if (rows == null || rows.isEmpty()) {
            rspData.setTotal(0L);
            rspData.setRows(Collections.emptyList());
            return rspData;
        }
        List<XgwcCaseDto> resultList = new ArrayList<>(rows.size());
        List<Long> caseIds = new ArrayList<>();
        for (int i = 0; i < rows.size(); i++) {
            JSONObject row = rows.getJSONObject(i);
            XgwcCaseDto dto = new XgwcCaseDto();
            dto.setCaseId(row.getLong("caseId"));
            dto.setMoney(row.getBigDecimal("money"));
            dto.setTitle(row.getString("title"));
            dto.setKeyword(row.getString("keyword"));
            dto.setBrandId(row.getLong("brandId"));
            dto.setFranchiseId(row.getLong("franchiseId"));
            dto.setCreateTime(row.getDate("create_time"));
            dto.setArchiveFiles(row.getString("filePath"));
            dto.setArchiveImg(row.getString("coverUrl"));
            dto.setDownloadCount(row.getInteger("downloadCount"));
            resultList.add(dto);
            caseIds.add(dto.getCaseId());
        }
        // 从数据库补充字段
        if (!caseIds.isEmpty()) {
            XgwcCaseQueryVo caseQuery = new XgwcCaseQueryVo();
            caseQuery.setCaseIds(caseIds);
            List<XgwcCaseDto> dbCaseList = xgwcCaseMapper.selectXgwcCaseList(caseQuery);
            // 构建以 caseId 为 key 的 Map，便于快速查找
            Map<Long, XgwcCaseDto> dbCaseMap = dbCaseList.stream()
                    .collect(Collectors.toMap(XgwcCaseDto::getCaseId, item -> item));
            // 合并字段
            for (XgwcCaseDto esDto : resultList) {
                XgwcCaseDto dbDto = dbCaseMap.get(esDto.getCaseId());
                if (dbDto != null) {
                    esDto.setAmount(dbDto.getAmount());
                    esDto.setArchiveFiles(dbDto.getArchiveFiles());
                    esDto.setArchiveImg(dbDto.getArchiveImg());
                    esDto.setBrandName(dbDto.getBrandName());
                    esDto.setBusinessName(dbDto.getBusinessName());
                    esDto.setCreateBy(dbDto.getCreateBy());
                    esDto.setCreateTime(dbDto.getCreateTime());
                    esDto.setDesignerName(dbDto.getDesignerName());
                    esDto.setDesignerPhone(dbDto.getDesignerPhone());
                    esDto.setFranchiseName(dbDto.getFranchiseName());
                    esDto.setMoney(dbDto.getMoney());
                    esDto.setCaseLevel(dbDto.getCaseLevel());
                    esDto.setDesignerBusinessName(dbDto.getBusinessName());
                }
            }
        }
        rspData.setTotal(total);
        rspData.setRows(resultList);
        return rspData;
    }

}
