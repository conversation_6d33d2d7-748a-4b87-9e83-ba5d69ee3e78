package com.xgwc.order.controller;

import com.xgwc.common.controller.BaseController;
import com.xgwc.order.feign.api.XgwcPlatformJobFeign;
import com.xgwc.order.job.XgwcPlatformJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/xgwcPlatformJob")
public class XgwcPlatformJobController extends BaseController implements XgwcPlatformJobFeign {

    @Autowired
    private XgwcPlatformJob xgwcPlatformJob;

    @GetMapping("/cycleUpdatePlatformShop")
    @Override
    public void cycleUpdatePlatformShop() {
        xgwcPlatformJob.cycleUpdatePlatformShop();
    }

    @GetMapping("/cycleUpdatePlatformGoods")
    @Override
    public void cycleUpdatePlatformGoods() {
        xgwcPlatformJob.cycleUpdatePlatformGoods();
    }

    @GetMapping("/cycleUpdatePlatformTrade")
    @Override
    public void cycleUpdatePlatformTrade() {
        xgwcPlatformJob.cycleUpdatePlatformTrade();
    }

    @GetMapping("/cycleUpdatePlatformRefund")
    @Override
    public void cycleUpdatePlatformRefund() {
        xgwcPlatformJob.cycleUpdatePlatformRefund();
    }

}