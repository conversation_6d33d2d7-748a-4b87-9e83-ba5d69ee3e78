package com.xgwc.order.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class IncomCustomerBasicDto extends IncomCustomerDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("员工")
    private String saleName;

    @FieldDesc("来源")
    private String source;

    private String region;

    @FieldDesc("成交概率")
    private BigDecimal succRate;
}
