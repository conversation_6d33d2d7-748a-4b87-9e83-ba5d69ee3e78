package com.xgwc.order.entity;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class CsCommissionRateConfigRange {

    @Min(0)
    @NotNull
    @FieldDesc("起始")
    private BigDecimal minRate;

    @NotNull
    @FieldDesc("截止")
    private BigDecimal maxRate;

    @NotNull
    @FieldDesc("提成比例")
    private BigDecimal rate;

}