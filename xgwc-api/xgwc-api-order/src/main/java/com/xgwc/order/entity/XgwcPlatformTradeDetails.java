package com.xgwc.order.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class XgwcPlatformTradeDetails {

    /**  */
    private Long id;

    /** 平台订单编号 */
    private String tradeNo;

    /** 平台子单号 */
    private String oid;

    /** 平台货品id */
    private String goodsId;

    /** 平台货品规格id */
    private String goodsSpecId;

    /** 下单数量 */
    private Long num;
    /**
     * 退款状态： 0.无退款 1.申请退款 2.部分退款 3.全部退款
     */
    private String refundStatus;
    /**
     * 平台子单状态：10未确认 20待尾款 30待发货 40部分发货 50已发货 60已签收 70已完成 80已退款 90已关闭
     */
    private String status;

    /** 分摊应收金额 */
    private BigDecimal shareAmount;

    /** 品牌商id */
    private Long brandId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date updateTime;

    /*
    {
        "end_time": "2025-07-11 20:31:03",
        "goods_id": "674235965810",
        "goods_name": "代做工程预算造价广联达套定额安装水电市政土建建模装饰算量计价",
        "goods_no": "",
        "modified": "2025-07-25 17:58:46",
        "num": "3.0",
        "oid": "4608986871101075946",
        "platform_id": "1",
        "refund_status": 0,
        "remark": "",
        "share_amount": "300.0",
        "spec_id": "",
        "spec_name": "",
        "spec_no": "",
        "status": "70",
        "tid": "4608986871101075946"
    }
     */

    public static XgwcPlatformTradeDetails initByJson(JSONObject goodsObject) {
        XgwcPlatformTradeDetails tradeDetails = new XgwcPlatformTradeDetails();
        tradeDetails.tradeNo = goodsObject.getString("tid");
        tradeDetails.oid = goodsObject.getString("oid");
        tradeDetails.goodsId = goodsObject.getString("goods_id");
        tradeDetails.goodsSpecId = goodsObject.getString("spec_id");
        tradeDetails.num = goodsObject.getLong("num");
        tradeDetails.shareAmount = goodsObject.getBigDecimal("share_amount");
        tradeDetails.refundStatus = goodsObject.getString("refund_status");
        tradeDetails.status = goodsObject.getString("status");
        tradeDetails.createTime = new Date();
        tradeDetails.updateTime = goodsObject.getDate("modified");
        return tradeDetails;
    }

}