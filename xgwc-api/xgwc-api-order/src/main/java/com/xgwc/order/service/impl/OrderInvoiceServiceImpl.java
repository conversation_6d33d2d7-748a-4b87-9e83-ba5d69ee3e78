package com.xgwc.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.xgwc.activiti.feign.api.ExecutionFeign;
import com.xgwc.common.entity.dto.FlowExecutionDto;
import com.xgwc.activiti.feign.entity.FlowExecutionVo;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.AutoIdUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.OrderInvoiceApplyMapper;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.entity.OrderInvoiceApply;
import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.OrderInvoiceQueryVo;
import com.xgwc.order.service.OrderInvoiceService;
import com.xgwc.user.feign.api.BrandOwnerFeign;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.api.ServiceAuthorizeFeign;
import com.xgwc.user.feign.api.StaffFeign;
import com.xgwc.user.feign.entity.BrandOwnerDto;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单发票
 */
@Slf4j
@Service
public class OrderInvoiceServiceImpl implements OrderInvoiceService {

    @Resource
    private OrderInvoiceApplyMapper orderInvoiceApplyMapper;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private BrandOwnerFeign brandOwnerFeign;
    @Resource
    private StaffFeign staffFeign;

    @Resource
    private ExecutionFeign executionFeign;
    @Resource
    private ServiceAuthorizeFeign serviceAuthorizeFeign;

    @Override
    public ApiResult insertOrderInvoiceApply(OrderInvoiceApply orderInvoiceApply) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        OrderDto orderDto = orderMapper.selectOrderByOrderNo(orderInvoiceApply.getOrderNo());
        if(franchiseId == null){
            return ApiResult.error("非加盟商不能发起发票申请");
        }
        if(orderInvoiceApply.getTitleType() == 2 && (orderInvoiceApply.getInvoiceType() == 3 || orderInvoiceApply.getInvoiceType() == 4) ){
            return ApiResult.error("个人只可申请普通发票，不可申请专票");
        }
        if((orderInvoiceApply.getInvoiceType() == 2 || orderInvoiceApply.getInvoiceType() == 4) && orderInvoiceApply.getCourierFee() == null){
            log.warn("纸质发票需要快递费:{}", JSONObject.toJSONString(orderInvoiceApply));
            return ApiResult.error("纸质发票需要填写快递费");
        }
        if((orderInvoiceApply.getInvoiceType() == 3 || orderInvoiceApply.getInvoiceType() == 4) && orderInvoiceApply.getSpecialInvoiceFee() == null){
            log.warn("专票需要专票费:{}", JSONObject.toJSONString(orderInvoiceApply));
            return ApiResult.error("专票需要填写专票费");
        }
        Double applyAmount = orderInvoiceApplyMapper.countApplyInvoiceByOrderNo(orderInvoiceApply.getOrderNo());
        if(applyAmount != null){
            BigDecimal invoiceAmount = BigDecimal.valueOf(applyAmount).add(orderInvoiceApply.getInvoiceAmount());
            if(invoiceAmount.compareTo(orderDto.getOrderAmount()) > 0){
                return ApiResult.error("当前订单己全额开票，不支持再次开票");
            }
        }else if(orderInvoiceApply.getInvoiceAmount().compareTo(orderDto.getOrderAmount()) > 0){
            return ApiResult.error("申请发票金额不能大于订单金额");
        }
        if(orderDto != null){
            Integer transferState = orderDto.getTransferState();
            if(transferState != null && transferState != 0 ){
                return ApiResult.error("“申请发票值支持正常订单，其他类型订单不可申请开票");
            }else if(orderDto.getPid() != null && orderDto.getPid() != 0){
                return ApiResult.error("非母订单不能申请开票");
            }
            orderInvoiceApply.setId(AutoIdUtil.getId());
            orderInvoiceApply.setApplyUserId(sysUser.getUserId());
            orderInvoiceApply.setApplyUserName(sysUser.getUserName());
            orderInvoiceApply.setFranchiseId(franchiseId);
            orderInvoiceApply.setCustomerNo(orderDto.getTaobaoId());
            orderInvoiceApply.setActualAmount(orderDto.getAmount());
            orderInvoiceApply.setBrandId(orderDto.getBrandId());
            // 启动工作流
            ApiResult<Long> apiResult = startFlow(orderInvoiceApply);
            if(apiResult.getStatus() != 1){
                return ApiResult.error(apiResult.getMessage());
            }
            Long executionId = apiResult.getData();
            orderInvoiceApply.setExecutionId(executionId);
            orderInvoiceApply.setApplyStatus("ING");
            int result = orderInvoiceApplyMapper.insertOrderInvoiceApply(orderInvoiceApply);
            return result == 1 ? ApiResult.ok() : ApiResult.error("保存失败");
        }else{
            return ApiResult.error("订单编号不存在");
        }
    }

    @Override
    public int updateOrderInvoiceApply(OrderInvoiceApply orderInvoiceApply) {
        if(orderInvoiceApply.getId() == null){
            log.warn("订单发票id不能为空:{}", JSONObject.toJSONString(orderInvoiceApply));
            return 0;
        }
        return orderInvoiceApplyMapper.updateOrderInvoiceApply(orderInvoiceApply);
    }

    @Override
    public OrderInvoiceApplyDto selectOrderInvoiceApplyById(Long id) {
        return orderInvoiceApplyMapper.selectOrderInvoiceApplyById(id);
    }

    @Override
    public void executeInvoice(FlowExecutionDto execution) {
        OrderInvoiceApply orderInvoiceApply = new OrderInvoiceApply();
        orderInvoiceApply.setId(execution.getBusinessKey());
        orderInvoiceApply.setApplyStatus(execution.getStatus());
        orderInvoiceApply.setLastApproveTime(DateFormatUtils.format(execution.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
        updateOrderInvoiceApply(orderInvoiceApply);
        if(execution.getStatus().equals("PASS")){
            // 发票申请通过后，更新订单发票状态
            int invoiceStatus = 0;
            OrderCompanyDto orderCompanyDto = orderMapper.selectOrderCompanyByOrderNo(execution.getTitle());
            // 实收金额
            BigDecimal orderAmount = orderCompanyDto.getOrderAmount();
            // 已申请金额
            BigDecimal applyAmount = BigDecimal.valueOf(orderInvoiceApplyMapper.countApplyByOrderNo(orderCompanyDto.getOrderNo()));
            // 实收金额等于已申请金额，则发票状态为全额开票，否则为部分开票
            if(orderAmount.compareTo(applyAmount) == 0){
                invoiceStatus = 2;
            }else{
                invoiceStatus = 1;
            }
            List<Long> ids = new ArrayList<>();
            Long mainOrderId = orderCompanyDto.getId();
            ids.add(mainOrderId);
            List<SubOrderDto> subOrderDtos = orderMapper.selectSubOrderList(mainOrderId);
            if (subOrderDtos != null && !subOrderDtos.isEmpty()) {
                List<Long> subOrderIds = subOrderDtos.stream() .map(SubOrderDto::getId).collect(Collectors.toList());
                ids.addAll(subOrderIds);
            }
            // 批量更新主 + 子订单的发票状态
            orderMapper.batchUpdateInvoiceStatus(ids, invoiceStatus);
        }
    }

    @Override
    public List<OrderInvoiceDto> selectAllOrderInvoiceApply(OrderInvoiceQueryVo queryVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Integer userType = sysUser.getUserType();
        if (userType == 1 || userType == 4) {
            queryVo.setBrandIdList(ImmutableList.of(sysUser.getBrandId()));
        } else if (userType == 2 || userType == 5) {
            queryVo.setFranchiseId(sysUser.getFranchiseId());
        } else if (userType == 6) {
            queryVo.setBrandIdList(serviceAuthorizeFeign.getServiceBrandIdList(sysUser.getServiceId()));
            if (queryVo.getBrandIdList().isEmpty()) {
                return Lists.newArrayList();
            }
        } else {
            return Lists.newArrayList();
        }
        List<OrderInvoiceDto> list = orderInvoiceApplyMapper.selectAllOrderInvoiceApply(queryVo);
        return list;
    }

    // 启动工作流
    private ApiResult<Long> startFlow(OrderInvoiceApply vo){
        FlowExecutionVo flowExecutionVo = new FlowExecutionVo();
        flowExecutionVo.setTitle(vo.getOrderNo());
        flowExecutionVo.setFlowValue("invoice");
        flowExecutionVo.setBusinessKey(vo.getId());
        flowExecutionVo.setBrandId(vo.getBrandId());
        flowExecutionVo.setCreateBy(vo.getApplyUserId());
        flowExecutionVo.setCreateName(vo.getApplyUserName());
        ApiResult<BrandOwnerDto> result = brandOwnerFeign.getBrandOwnerById(vo.getBrandId());
        if(result.getStatus() == 1 && result.getData() != null){
            BrandOwnerDto data = result.getData();
            flowExecutionVo.setBrandName(data.getCompanyName());
        }
        flowExecutionVo.setFranchiseId(vo.getFranchiseId());
        ApiResult<FranchiseDto> franchiseResult = franchiseFeign.getFranchiseById(vo.getFranchiseId());
        if(franchiseResult.getStatus() == 1 && franchiseResult.getData() != null){
            FranchiseDto franchiseData = franchiseResult.getData();
            flowExecutionVo.setFranchiseName(franchiseData.getFranchiseName());
        }
        ApiResult staffResult = staffFeign.getFranchiseStaffInfoByUserIdAndFranchiseId(SecurityUtils.getSysUser().getUserId(), vo.getFranchiseId());
        if (staffResult.getStatus() == 1 && staffResult.getData() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            FranchiseStaffDto franchiseStaffDto = objectMapper.convertValue(staffResult.getData(), FranchiseStaffDto.class);
            if (franchiseStaffDto != null && franchiseStaffDto.getDeptId() != null) {
                flowExecutionVo.setDeptId(franchiseStaffDto.getDeptId());
                flowExecutionVo.setDeptName(franchiseStaffDto.getDeptName());
            }
        }
        // 将整个售后审核单信息作为变量传递给工作流
        flowExecutionVo.setVariable(vo);
        return executionFeign.insertFlowExecution(flowExecutionVo);
    }
}
