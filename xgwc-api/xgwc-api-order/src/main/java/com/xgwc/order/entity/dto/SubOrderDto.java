package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SubOrderDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long id;


    @FieldDesc("派单人id")
    private Long allotUserId;

    @FieldDesc("派单人")
    private String allotUserName;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("母订单id")
    @Excel(name = "母订单id")
    private Long pid;
    @FieldDesc("母订单编号")
    private String pno;

    @FieldDesc("设计师ID")
    @Excel(name = "设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    @Excel(name = "设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    @Excel(name = "设计师电话")
    private String designerPhone;

    @FieldDesc("佣金金额")
    @Excel(name = "佣金金额")
    private BigDecimal money;

    /**
     * 退款之后有值
     */
    @FieldDesc("现佣金金额")
    @Excel(name = "现佣金金额")
    private BigDecimal nowMoney;

    @FieldDesc("约定初稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "约定初稿日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveAppointTime;

    @FieldDesc("提交定稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交定稿日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveTime;

    @FieldDesc("交稿状态：0未交稿 1交初稿 2交定稿")
    @Excel(name = "交稿状态：0未交稿 1交初稿 2交定稿")
    private Integer archiveType;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("设计师业务类型")
    private String designerBusiness;
}
