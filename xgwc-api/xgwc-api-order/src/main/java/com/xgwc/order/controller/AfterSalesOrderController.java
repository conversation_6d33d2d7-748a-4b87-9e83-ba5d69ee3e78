package com.xgwc.order.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.entity.dto.AfterSalesOrderDto;
import com.xgwc.order.entity.vo.AfterAgencyAuditVo;
import com.xgwc.order.entity.vo.AfterSalesAuditVo;
import com.xgwc.order.entity.vo.AfterSalesOrderQueryVo;
import com.xgwc.order.service.IAfterAgencyAuditService;
import com.xgwc.order.service.IAfterSalesOrderService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 售后订单接口
 */
@RequestMapping("/afterSales")
@RestController
public class AfterSalesOrderController extends BaseController{

    @Resource
    private IAfterSalesOrderService afterSalesOrderService;

    @Resource
    private IAfterAgencyAuditService afterAgencyAuditService;

    /**
     * 查询售后工作台列表
     */
    @MethodDesc("查询售后工作台列表")
    @PreAuthorize("@ss.hasPermission('afterSalesOrder:afterSalesOrder:list')")
    @GetMapping("/list")
    public ApiResult<AfterSalesOrderDto> list(AfterSalesOrderQueryVo queryVo) {
        startPage();
        List<AfterSalesOrderDto> list = afterSalesOrderService.list(queryVo);
        return getDataTable(list);
    }

    /**
     * 领取售后单
     */
    @MethodDesc("领取售后单")
    @PreAuthorize("@ss.hasPermission('afterSalesOrder:afterSalesOrder:add')")
    @Log(title = "领取售后单", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody AfterAgencyAuditVo afterAgencyAudit) {
        SysUser sysUser = SecurityUtils.getSysUser();
        return toAjax(afterAgencyAuditService.insertAfterAgencyAudit(afterAgencyAudit, sysUser.getBrandId(), sysUser.getUserId()));
    }

    /**
     * 审核售后单
     */
    @MethodDesc("审核售后单")
    @PreAuthorize("@ss.hasPermission('afterSalesOrder:afterSalesOrder:edit')")
    @Log(title = "审核售后单", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult audit(@RequestBody AfterSalesAuditVo afterSalesAuditVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        return toAjax(afterAgencyAuditService.auditAfterSalesOrder(afterSalesAuditVo, sysUser.getUserId(), sysUser.getUserName(), null));
    }

    @MethodDesc("统计数量")
    @GetMapping("/count")
    public ApiResult count() {
        return success(afterSalesOrderService.count());
    }

}