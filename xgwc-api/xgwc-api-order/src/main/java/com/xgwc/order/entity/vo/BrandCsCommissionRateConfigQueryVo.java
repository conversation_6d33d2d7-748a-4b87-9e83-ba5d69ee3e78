package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
public class BrandCsCommissionRateConfigQueryVo {

    @FieldDesc("品牌商ID")
    private Long brandId;

    @NotNull
    @FieldDesc("配置类型: 1 基础, 2 团队负责人, 3 单人")
    private Long type;

    @FieldDesc("加盟商ID")
    private Long franchiseeId;
    @FieldDesc("加盟商ID")
    private List<Long> franchiseeIds;

    @FieldDesc("档位名称")
    private String gearName;

    @FieldDesc("加盟商员工ID")
    private Long staffId;
    @FieldDesc("加盟商员工ID")
    private List<Long> staffIds;

    @FieldDesc("加盟商部门ID")
    private Long deptId;
    @FieldDesc("加盟商部门ID")
    private List<Long> deptIds;

    @FieldDesc("业务ID")
    private Long businessId;
    @FieldDesc("业务ID")
    private List<Long> businessIds;

    @FieldDesc("生效周期类型: 1 长期有效, 2 指定时间")
    private Long effectiveCycleType;

    @FieldDesc("状态: 1生效, 2失效")
    private Long status;

}