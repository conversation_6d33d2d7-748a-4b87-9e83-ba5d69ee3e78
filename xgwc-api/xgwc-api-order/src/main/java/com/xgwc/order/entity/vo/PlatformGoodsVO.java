package com.xgwc.order.entity.vo;

import lombok.Data;

@Data
public class PlatformGoodsVO {

    private Long id;

    /** 旺店通平台货品id */
    private String goodsId;
    private String specId;

    /** 商品名称 */
    private String goodsName;

    /** 平台id */
    private Long platformId;

    /** 平台店铺id */
    private String platformShopId;

    /** 品牌商id */
    private Long brandId;

    /** 业务分类 */
    private Long bizType;

    /** 0删除1在架2下架 */
    private Long status;
}
