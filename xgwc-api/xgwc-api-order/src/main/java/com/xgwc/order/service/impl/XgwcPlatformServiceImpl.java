package com.xgwc.order.service.impl;


import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.common.util.json.JsonUtils;
import com.xgwc.order.dao.XgwcPlatformConfigMapper;
import com.xgwc.order.entity.XgwcPlatformConfig;
import com.xgwc.order.entity.XgwcPlatformGoods;
import com.xgwc.order.entity.XgwcPlatformGoodsSpec;
import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.XgwcPlatformRefundDetails;
import com.xgwc.order.entity.XgwcPlatformShop;
import com.xgwc.order.entity.XgwcPlatformTrade;
import com.xgwc.order.entity.XgwcPlatformTradeDetails;
import com.xgwc.order.entity.vo.Second;
import com.xgwc.order.entity.vo.Third;
import com.xgwc.order.service.XgwcPlatformService;
import com.xgwc.order.util.WdtClient;
import com.xgwc.redis.ratelimiter.ApiRateLimiter;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class XgwcPlatformServiceImpl implements XgwcPlatformService {

    @Resource
    private XgwcPlatformConfigMapper xgwcPlatformConfigMapper;
    @Resource
    private XgwcPlatformTradeServiceImpl xgwcPlatformTradeServiceImpl;
    @Resource
    private XgwcPlatformRefundServiceImpl xgwcPlatformRefundServiceImpl;

    private final int pageSize = 100;
    // {"code":1012,"message":"频率超限，请保持1分钟内60次的频率5分钟后重新调用"}
    private final long sleepMillis = 950L;

    @Override
    public List<XgwcPlatformShop> pullShop(WdtClient wdtClient, Long brandOwnerId) throws IOException {
        return this.pullShop(wdtClient, 0, brandOwnerId);
    }

    private List<XgwcPlatformShop> pullShop(WdtClient wdtClient, int pageNo, Long brandOwnerId) throws IOException {
        List<XgwcPlatformShop> list = Lists.newArrayList();
        Map<String, String> params = this.initParamPageMap(pageNo, "wdt.shop.query");
        String response = wdtClient.executeQimen(params);
        JSONObject jsonObject = JSONObject.parseObject(response).getJSONObject("response");
        if (!"0".equals(jsonObject.getString("errorcode"))) {
            log.error("旺店通奇门店铺接口提示异常: {}", response);
            throw new ApiException("旺店通奇门店铺接口提示异常");
        }
        JSONArray shopArray = jsonObject.getJSONArray("shoplist");
        if (shopArray != null && !shopArray.isEmpty()) {
            for (Object shop : shopArray) {
                list.add(XgwcPlatformShop.init((JSONObject) shop, brandOwnerId));
            }
            if (shopArray.size() == pageSize) {
                ThreadUtil.sleep(sleepMillis);
                list.addAll(this.pullShop(wdtClient, pageNo + 1, brandOwnerId));
            }
        }
        return list;
    }

    @Override
    public Second<List<XgwcPlatformGoods>, List<XgwcPlatformGoodsSpec>> pullGoods(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException {
        Third<Set<String>, List<XgwcPlatformGoods>, List<XgwcPlatformGoodsSpec>> thirdData = new Third<>(Sets.newHashSet(), Lists.newArrayList(), Lists.newArrayList());
        this.pullGoods(startTime, endTime, wdtClient, 0, thirdData);
        thirdData.getSecond().forEach(goods -> goods.setBrandId(brandOwnerId));
        thirdData.getThird().forEach(goodsSpec -> goodsSpec.setBrandId(brandOwnerId));
        return new Second<>(thirdData.getSecond(), thirdData.getThird());
    }

    @Override
    public Second<List<XgwcPlatformTrade>, List<XgwcPlatformTradeDetails>> pullTrade(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException {
        Second<List<XgwcPlatformTrade>, List<XgwcPlatformTradeDetails>> tradeData = new Second<>(Lists.newArrayList(), Lists.newArrayList());
        //  开始时间和结束时间最大间隔为29天
        if (DateUtils.addDays(startTime, 29).getTime() < endTime.getTime()) {
            Date tempStartTime = startTime;
            Date tempEndTime = DateUtils.addDays(startTime, 29);
            do {
                this.pullTrade(tempStartTime, tempEndTime, wdtClient, 0, tradeData);
                tempStartTime = tempEndTime;
                tempEndTime = DateUtils.addDays(tempStartTime, 29);
                if (tempEndTime.getTime() > endTime.getTime()) {
                    tempEndTime = endTime;
                }
                ThreadUtil.sleep(sleepMillis);
            } while (tempStartTime.getTime() < endTime.getTime());
        } else {
            this.pullTrade(startTime, endTime, wdtClient, 0, tradeData);
        }
        tradeData.getFirst().forEach(trade -> trade.setBrandId(brandOwnerId));
        tradeData.getSecond().forEach(tradeDetails -> tradeDetails.setBrandId(brandOwnerId));
        return tradeData;
    }

    private void pullTrade(Date startTime, Date endTime, WdtClient wdtClient, int pageNo, Second<List<XgwcPlatformTrade>, List<XgwcPlatformTradeDetails>> tradeData) throws IOException {
        Map<String, String> params = this.initParamPageMap(pageNo, "wdt.vip.api.trade.query");
        this.initParamTime(params, startTime, endTime);
        String response = wdtClient.executeQimen(params);
        JSONObject jsonObject = JSONObject.parseObject(response).getJSONObject("response");
        if (!"0".equals(jsonObject.getString("errorcode"))) {
            log.error("旺店通奇门交易单接口提示异常: {}", response);
            throw new ApiException("旺店通奇门交易单接口提示异常");
        }
        JSONArray tradeArray = jsonObject.getJSONArray("trade_list");
        if (tradeArray != null && !tradeArray.isEmpty()) {
            for (Object object : tradeArray) {
                JSONObject tradeJson = (JSONObject) object;
                XgwcPlatformTrade trade = XgwcPlatformTrade.initByJson(tradeJson);
                // 过滤无效单(未支付的)
                if (trade.isValidTrade()) {
                    tradeData.getFirst().add(trade);
                    tradeData.getSecond().addAll(tradeJson.getJSONArray("goods_list").stream().map(goodsObject -> XgwcPlatformTradeDetails.initByJson((JSONObject) goodsObject)).toList());
                }
            }
            if (tradeArray.size() == pageSize) {
                ThreadUtil.sleep(sleepMillis);
                this.pullTrade(startTime, endTime, wdtClient, pageNo + 1, tradeData);
            }
        }
    }

    @Override
    public Second<XgwcPlatformTrade, List<XgwcPlatformTradeDetails>> pullTradeByTradeNo(String tradeNo, WdtClient wdtClient, Long brandOwnerId) throws IOException {
        Map<String, String> params = this.initParamPageMap(0, "wdt.vip.api.trade.query");
        params.put("tid", tradeNo);
        String response = wdtClient.executeQimen(params);
        JSONObject jsonObject = JSONObject.parseObject(response).getJSONObject("response");
        if (!"0".equals(jsonObject.getString("errorcode"))) {
            log.error("旺店通奇门交易单接口提示异常: params={}, response={}", params, response);
            throw new ApiException("前往平台校验单号时异常: " + response);
        }
        JSONArray tradeArray = jsonObject.getJSONArray("trade_list");
        if (tradeArray != null && !tradeArray.isEmpty()) {
            JSONObject tradeJson = (JSONObject) tradeArray.get(0);
            XgwcPlatformTrade trade = XgwcPlatformTrade.initByJson(tradeJson);
            if (trade.isValidTrade()) {
                List<XgwcPlatformTradeDetails> tradeDetailsList = tradeJson.getJSONArray("goods_list").stream().map(goodsObject -> XgwcPlatformTradeDetails.initByJson((JSONObject) goodsObject)).toList();
                trade.setBrandId(brandOwnerId);
                tradeDetailsList.forEach(tradeDetails -> tradeDetails.setBrandId(brandOwnerId));
                return new Second<>(trade, tradeDetailsList);
            }
        }
        return null;
    }

    @Override
    public Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> pullRefund(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException {
        Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> refundData = new Second<>(Lists.newArrayList(), Lists.newArrayList());
        //  开始时间和结束时间最大间隔为29天
        if (DateUtils.addDays(startTime, 29).getTime() < endTime.getTime()) {
            Date tempStartTime = startTime;
            Date tempEndTime = DateUtils.addDays(startTime, 29);
            do {
                this.pullRefund(tempStartTime, tempEndTime, null, wdtClient, 0, refundData);
                tempStartTime = tempEndTime;
                tempEndTime = DateUtils.addDays(tempStartTime, 29);
                if (tempEndTime.getTime() > endTime.getTime()) {
                    tempEndTime = endTime;
                }
                ThreadUtil.sleep(sleepMillis);
            } while (tempStartTime.getTime() < endTime.getTime());
        } else {
            this.pullRefund(startTime, endTime, null, wdtClient, 0, refundData);
        }
        refundData.getFirst().forEach(refund -> refund.setBrandId(brandOwnerId));
        refundData.getSecond().forEach(refundDetails -> refundDetails.setBrandId(brandOwnerId));
        return refundData;
    }


    @Override
    public Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> pullRefundByTradeNo(String tradeNo, WdtClient wdtClient, Long brandOwnerId) throws IOException {
        Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> refundData = new Second<>(Lists.newArrayList(), Lists.newArrayList());
        Date now = new Date();
        this.pullRefund(DateUtils.addDays(now, -30), now, tradeNo, wdtClient, 0, refundData);
        if (refundData.getFirst().isEmpty()) {
            return null;
        }
        refundData.getFirst().forEach(refund -> refund.setBrandId(brandOwnerId));
        refundData.getSecond().forEach(refundDetails -> refundDetails.setBrandId(brandOwnerId));
        return refundData;
    }

    private void pullRefund(Date startTime, Date endTime, String orderNo, WdtClient wdtClient, int pageNo, Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> refundData) throws IOException {
        Map<String, String> params = this.initParamPageMap(pageNo, "wdt.api.refund.query");
        this.initParamTime(params, startTime, endTime);
        if (StringUtils.isNotEmpty(orderNo)) {
            params.put("tid", orderNo);
        }
        String response = wdtClient.executeQimen(params);
        JSONObject jsonObject = JSONObject.parseObject(response).getJSONObject("response");
        if (!"0".equals(jsonObject.getString("errorcode"))) {
            log.error("旺店通慧策售后单接口提示异常: {}", response);
            throw new ApiException("旺店通慧策售后单接口提示异常");
        }
        JSONArray refundArray = jsonObject.getJSONArray("trade_list");
        if (refundArray != null && !refundArray.isEmpty()) {
            for (Object object : refundArray) {
                JSONObject refundJson = (JSONObject) object;
                refundData.getFirst().add(XgwcPlatformRefund.initByJson(refundJson));
                // 售后单明细
                refundJson.getJSONArray("goods_list").forEach(goodsObject -> refundData.getSecond().add(XgwcPlatformRefundDetails.initByJson((JSONObject) goodsObject)));
            }
            if (refundArray.size() == pageSize) {
                ThreadUtil.sleep(sleepMillis);
                this.pullRefund(startTime, endTime, orderNo, wdtClient, pageNo + 1, refundData);
            }
        }
    }

    private void pullGoods(Date startTime, Date endTime, WdtClient wdtClient, int pageNo, Third<Set<String>, List<XgwcPlatformGoods>, List<XgwcPlatformGoodsSpec>> data) throws IOException {
        Map<String, String> params = this.initParamPageMap(pageNo, null);
        this.initParamTime(params, startTime, endTime);
        String response = wdtClient.execute("vip_api_goods_query.php", params);

//        Map<String, String> params = this.initParamPageMap(pageNo, "vip_api_goods_query.php");
//        this.initParamTime(params, startTime, endTime);
//        String response = wdtClient.executeQimen(params);
        JSONObject jsonObject = JSONObject.parseObject(response);
        if (!"0".equals(jsonObject.getString("code"))) {
            log.error("旺店通慧策商品接口提示异常: {}", response);
            throw new ApiException("旺店通慧策商品接口提示异常");
        }
        JSONArray goodsArray = jsonObject.getJSONArray("goods_list");
        if (goodsArray != null && !goodsArray.isEmpty()) {
            for (Object object : goodsArray) {
                JSONObject goodsJson = (JSONObject) object;
                XgwcPlatformGoods goods = XgwcPlatformGoods.initByJson(goodsJson);
                // 以商品维度, 而非规格维度
                if (data.getFirst().add(goods.getGoodsId())) {
                    data.getSecond().add(goods);
                }
                data.getThird().add(XgwcPlatformGoodsSpec.initByJson(goodsJson));
            }
            if (goodsArray.size() == pageSize) {
                ThreadUtil.sleep(sleepMillis);
                this.pullGoods(startTime, endTime, wdtClient, pageNo + 1, data);
            }
        }
    }

    private Map<String, String> initParamPageMap(int pageNo, String method) {
        Map<String, String> params = Maps.newHashMap();
        params.put("method", method);
        params.put("page_no", String.valueOf(pageNo));
        params.put("page_size", String.valueOf(pageSize));
        return params;
    }

    private void initParamTime(Map<String, String> params, Date startTime, Date endTime) {
        params.put("start_time", DateUtils.formatDateTime(startTime));
        params.put("end_time", DateUtils.formatDateTime(endTime));
    }


    @ApiRateLimiter(limit = 1)
    public XgwcPlatformTrade pullByTradeNo(String tradeNo, List<Long> brandOwnerIdList) {
        List<XgwcPlatformConfig> configList = xgwcPlatformConfigMapper.selectXgwcPlatformConfigList(brandOwnerIdList);
        if (configList.isEmpty()) {
            throw new ApiException("加盟的品牌商都未设置平台配置, 请联系系统管理员处理");
        }
        for (int i = 0; i < configList.size(); i++) {
            XgwcPlatformConfig config = configList.get(i);
            WdtClient wdtClient = new WdtClient(config);
            try {
                Second<XgwcPlatformTrade, List<XgwcPlatformTradeDetails>> tradeData = this.pullTradeByTradeNo(tradeNo, wdtClient, config.getId());
                if (tradeData != null) {
                    xgwcPlatformTradeServiceImpl.saveXgwcPlatformTrade(ImmutableList.of(tradeData.getFirst()), tradeData.getSecond(), config.getId());
                    return tradeData.getFirst();
                }
            } catch (IOException e) {
                log.error("按平台订单编号查询异常: tradeNo={}, config={}", tradeNo, JsonUtils.toJsonString(config), e);
            }
            if (i != configList.size() - 1) {
                ThreadUtil.sleep(600);
            }
        }
        return null;
    }

    public List<XgwcPlatformRefund> pullRefundByTradeNo(String tradeNo, List<Long> brandOwnerIdList) {
        List<XgwcPlatformConfig> configList = xgwcPlatformConfigMapper.selectXgwcPlatformConfigList(brandOwnerIdList);
        if (configList.isEmpty()) {
            throw new ApiException("加盟的品牌商都未设置平台配置, 请联系系统管理员处理");
        }
        for (int i = 0; i < configList.size(); i++) {
            XgwcPlatformConfig config = configList.get(i);
            WdtClient wdtClient = new WdtClient(config);
            try {
                Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> refundData = this.pullRefundByTradeNo(tradeNo, wdtClient, config.getId());
                if (refundData != null) {
                    xgwcPlatformRefundServiceImpl.saveXgwcPlatformRefund(refundData.getFirst(), refundData.getSecond(), config.getId());
                    return refundData.getFirst();
                }
            } catch (IOException e) {
                log.error("按平台订单编号查询退款异常: tradeNo={}, config={}", tradeNo, JsonUtils.toJsonString(config), e);
            }
            if (i != configList.size() - 1) {
                ThreadUtil.sleep(600);
            }
        }
        return null;
    }

}