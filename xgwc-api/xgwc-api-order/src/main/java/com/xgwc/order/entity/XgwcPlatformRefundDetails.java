package com.xgwc.order.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class XgwcPlatformRefundDetails {

    /**  */
    private Long id;
    /** 平台订单编号 */
    private String tradeNo;
    /** 平台子单号 */
    private String oid;

    /** 平台退款编号 */
    private String refundNo;

    /** 平台货品id */
    private String goodsId;
    /** 平台货品规格id */
    private String goodsSpecId;

    /** 退款数量 */
    private Long refundNum;

    /** 明细退款金额，由主单退款金额分摊 */
    private BigDecimal refundOrderAmount;

    /** 品牌商id */
    private Long brandId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date updateTime;

    /*
    {
        "goods_id": "674235965810",
        "goods_name": "代做工程预算造价广联达套定额安装水电市政土建建模装饰算量计价",
        "goods_no": "",
        "modified": "2025-07-25 17:58:54",
        "num": "2.0",
        "oid": "2844652189478212499",
        "platform_id": 1,
        "refund_no": "222966051480219924",
        "remark": "",
        "spec_id": "",
        "spec_name": "",
        "tid": "2844652189478212499",
        "total_amount": "200.0"
    }
     */


    public static XgwcPlatformRefundDetails initByJson(JSONObject jsonObject) {
        XgwcPlatformRefundDetails refundDetails = new XgwcPlatformRefundDetails();
        refundDetails.tradeNo = jsonObject.getString("tid");
        refundDetails.oid = jsonObject.getString("oid");
        refundDetails.refundNo = jsonObject.getString("refund_no");
        refundDetails.goodsId = jsonObject.getString("goods_id");
        refundDetails.goodsSpecId = jsonObject.getString("spec_id");
        refundDetails.refundNum = jsonObject.getLong("num");
        refundDetails.refundOrderAmount = jsonObject.getBigDecimal("total_amount");
        refundDetails.createTime = new Date();
        refundDetails.updateTime = jsonObject.getDate("modified");
        return refundDetails;
    }

}