package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class OrderArchive {

private static final long serialVersionUID=1L;

    /** 定稿id */
    private Long id;

    /** 订单id */
    private Long oderId;

    /** 定稿名称 */
    private String archiveName;

    /** 定稿预览图 */
    private String archiveImg;

    /** 上传定稿源文件 */
    private String archiveFiles;

    /** 业务id */
    private Long businessId;

    /** 业务名称 */
    private String businessName;

    /** 审批状态 */
    private Integer reviewStatus;

    /** 审批结果 */
    private String reviewMsg;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    private String linkUrl;

}