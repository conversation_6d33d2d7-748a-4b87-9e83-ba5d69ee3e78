package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

import java.util.List;

@Data
public class XgwcCaseQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("案例ID/案例名称/设计师名字/设计师手机号")
    private String value;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("加盟商id(贡献方)")
    private Long franchiseId;

    @FieldDesc("业务id")
    private Long businessId;

    @FieldDesc("等级：1高 2中 3低")
    private Integer caseLevel;

    private Integer status;

    private List<Long> caseIds;

}
