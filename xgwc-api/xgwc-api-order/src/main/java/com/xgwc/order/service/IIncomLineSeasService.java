package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.dto.IncomLineDto;
import com.xgwc.order.entity.vo.IncomLineQueryVo;
import com.xgwc.order.entity.vo.IncomLineTransferVo;

public interface IIncomLineSeasService  {

    /**
     * 查询客户公海列表
     * 
     * @param incomLineSeas 客户公海
     * @return 客户公海集合
     */
    public List<IncomLineDto> selectIncomLineSeasList(IncomLineQueryVo incomLineSeas);

    public int transfer(IncomLineTransferVo transferVo);

    public int collect(Long id);
}
