package com.xgwc.order.service;

import com.xgwc.common.entity.dto.FlowExecutionDto;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.OrderExpensesApply;
import com.xgwc.order.entity.dto.OrderExpensesApplyDto;
import com.xgwc.order.entity.dto.OrderExpensesDto;
import com.xgwc.order.entity.vo.OrderExpensesQueryVo;

import java.util.List;

public interface OrderExpensesService {

    /**
     * 插入申请数据
     * @param orderExpensesApply 申请数据
     * @return 是否成功
     */
    ApiResult insertOrderExpensesApply(OrderExpensesApply orderExpensesApply);

    /**
     * 更新申请数据
     * @param orderExpensesApply 申请数据
     * @return 是否成功
     */
    int updateOrderExpensesApply(OrderExpensesApply orderExpensesApply);

    /**
     * 根据主键查询报销订单详情
     * @param id 主键
     * @return 报销订单详情
     */
    OrderExpensesApplyDto selectOrderExpensesApplyById(Long id);

    /**
     * 处理报销流程
     * @param execution 流程执行信息
     */
    void executeExpense(FlowExecutionDto execution);

    /**
     * 查询所有申请数据
     * @param orderExpensesQueryVo 查询参数
     * @return 报销订单列表
     */
    List<OrderExpensesDto> selectAllOrderExpensesApply(OrderExpensesQueryVo orderExpensesQueryVo);
}
