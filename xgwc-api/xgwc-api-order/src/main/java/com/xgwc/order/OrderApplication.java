package com.xgwc.order;

import com.xgwc.activiti.feign.api.ExecutionFeign;
import com.xgwc.activiti.feign.api.FlowTaskFeign;
import com.xgwc.settlement.feign.api.SettlementFeign;
import com.xgwc.user.feign.api.BrandOwnerFeign;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.api.OperLogFeign;
import com.xgwc.user.feign.api.ServiceAuthorizeFeign;
import com.xgwc.user.feign.api.StaffFeign;
import com.xgwc.user.feign.api.SysDictDataFeign;
import com.xgwc.user.feign.api.UserDeptFeign;
import com.xgwc.user.feign.api.UserDetailFeign;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.starter.register.EsMapperScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.xgwc.*"})
@ComponentScan("com.xgwc")
@MapperScan("com.xgwc.order.dao")
@EnableFeignClients(clients = {UserDetailFeign.class, OperLogFeign.class
        , UserDeptFeign.class, StaffFeign.class, SysDictDataFeign.class
        , ExecutionFeign.class, FranchiseFeign.class, BrandOwnerFeign.class
        , FlowTaskFeign.class, ServiceAuthorizeFeign.class, SettlementFeign.class})
@EsMapperScan("com.xgwc.order.esmapper")
@Slf4j
@EnableScheduling
@EnableAsync
public class OrderApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderApplication.class, args);
        log.info("项目启动成功!");
    }

}
