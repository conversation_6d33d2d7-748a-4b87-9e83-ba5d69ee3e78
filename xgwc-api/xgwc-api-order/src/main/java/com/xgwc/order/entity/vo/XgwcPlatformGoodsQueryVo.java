package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.List;

@Data
public class XgwcPlatformGoodsQueryVo {

    private Long id;

    @FieldDesc("旺店通平台货品id")
    private String goodsId;

    @FieldDesc("商品名称")
    private String goodsName;

    @FieldDesc("平台id")
    private Long platformId;

    @FieldDesc("平台店铺id")
    private String platformShopId;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("业务分类")
    private Long bizType;

    @FieldDesc("0删除1在架2下架")
    private Long status;

    @FieldDesc("是否已关联店铺: 1是 0否")
    private String hasShop;
    @FieldDesc("是否已添加分类: 1是 0否")
    private String hasType;
    @FieldDesc("搜索关键字")
    private String keyword;

    private List<String> keywordPlatformShopIds;

}
