package com.xgwc.order.entity.param;

import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  09:32
 */

/**
 * 服务管理-业务管理参数
 */
@Data
public class XgwcBusinessParam {

    /** 业务id */
    private Long businessId;

    /** 业务名称 */
    private String businessName;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 品牌商id */
    private Long brandOwnerId;

    private Integer isFlag;

}
