package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.OrderInvoiceApply;
import com.xgwc.order.entity.dto.OrderInvoiceApplyDto;
import com.xgwc.order.entity.dto.OrderInvoiceDto;
import com.xgwc.order.entity.vo.OrderInvoiceQueryVo;
import com.xgwc.order.service.OrderInvoiceService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xgwc.common.util.PageUtils.startPage;

@RestController
@RequestMapping("order/invoice")
public class OrderInvoiceController extends BaseController {

    @Resource
    private OrderInvoiceService orderInvoiceService;

    @PostMapping("addOrderInvoice")
    public ApiResult addOrderInvoice(@Valid @RequestBody OrderInvoiceApply orderInvoiceApply){
        return orderInvoiceService.insertOrderInvoiceApply(orderInvoiceApply);
    }

    @PostMapping("updateOrderInvoice")
    public ApiResult updateFranchiseOrderExpenses(@RequestBody OrderInvoiceApply orderInvoiceApply){
        int result = orderInvoiceService.updateOrderInvoiceApply(orderInvoiceApply);
        return result > 0 ? ApiResult.ok() :ApiResult.error("");
    }

    @GetMapping("getDetailById/{id}")
    public ApiResult getDetailById(@PathVariable("id") Long id){
        if(id == null){
            return ApiResult.error("参数为空");
        }
        OrderInvoiceApplyDto orderInvoiceApplyDto = orderInvoiceService.selectOrderInvoiceApplyById(id);
        return ApiResult.ok(orderInvoiceApplyDto);
    }

    @MethodDesc("全部审批列表")
    @GetMapping("/getAllList")
    public ApiResult<OrderInvoiceDto> getAllList(OrderInvoiceQueryVo orderInvoiceVo){
        startPage();
        List<OrderInvoiceDto> list = orderInvoiceService.selectAllOrderInvoiceApply(orderInvoiceVo);
        return getDataTable(list);
    }

}
