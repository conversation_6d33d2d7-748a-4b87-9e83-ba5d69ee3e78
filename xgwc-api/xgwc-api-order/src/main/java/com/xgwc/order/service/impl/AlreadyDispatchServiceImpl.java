package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.AgencyDispatchMapper;
import com.xgwc.order.dao.AlreadyDispatchMapper;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.dao.SendBackMapper;
import com.xgwc.order.entity.AlreadyDispatch;
import com.xgwc.order.entity.Order;
import com.xgwc.order.entity.dto.AgencyDispatchDto;
import com.xgwc.order.entity.dto.AlreadyDispatchDto;
import com.xgwc.order.entity.dto.AlreadyDispatchPageDto;
import com.xgwc.order.entity.dto.AlreadyRecordPageDto;
import com.xgwc.order.entity.dto.OrderDto;
import com.xgwc.order.entity.dto.SendBackDto;
import com.xgwc.order.entity.dto.SubOrderDto;
import com.xgwc.order.entity.vo.AlreadyDispatchQueryVo;
import com.xgwc.order.entity.vo.AlreadyDispatchVo;
import com.xgwc.order.entity.vo.AlreadyRecordQueryVo;
import com.xgwc.order.entity.vo.OrderDesignerVo;
import com.xgwc.order.service.IAlreadyDispatchService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class AlreadyDispatchServiceImpl implements IAlreadyDispatchService  {
    @Resource
    private AlreadyDispatchMapper alreadyDispatchMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private AgencyDispatchMapper agencyDispatchMapper;
    @Resource
    private SendBackMapper sendBackMapper;

    /**
     * 查询我的已派单
     * 
     * @param id 我的已派单主键
     * @return 我的已派单
     */
    @Override
    public AlreadyDispatchDto selectAlreadyDispatchById(Long id) {
        return alreadyDispatchMapper.selectAlreadyDispatchById(id);
    }

    /**
     * 查询我的已派单列表
     *
     * @param alreadyDispatch 我的已派单
     * @return 我的已派单
     */
    @Override
    public List<AlreadyDispatchPageDto> selectAlreadyDispatchList(AlreadyDispatchQueryVo alreadyDispatch) {
        alreadyDispatch.setDispatchUserId(SecurityUtils.getUserId());
        return alreadyDispatchMapper.selectAlreadyDispatchList(alreadyDispatch);
    }

    /**
     * 新增我的已派单
     * 
     * @param dto 我的已派单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAlreadyDispatch(AlreadyDispatchVo dto) {
        List<OrderDesignerVo> designerList = dto.getOrderDesigners();
        if(dto.getOrderDesigners().isEmpty()){
            throw new ApiException("请选择设计师");
        }
        List<SubOrderDto> subOrderDtoList = orderMapper.findSubOrderByOrderId(dto.getId());
        Set<Long> existingDesignerIds = subOrderDtoList .stream() .map(SubOrderDto::getDesignerId).collect(Collectors.toSet());
        for (OrderDesignerVo designer : designerList) {
            if(StringUtils.isEmpty(designer.getOrderNo())){
                if (!existingDesignerIds.add(designer.getDesignerId())) {
                    throw new ApiException("设计师重复，请重新选择");
                }
            }
        }
        int assignCount = 0;
        OrderDto orderDto = orderMapper.selectOrderById(dto.getId());
        if(orderDto == null){
            throw new ApiException("订单不存在");
        }
        //判断母订单的佣金和派单时子订单的的佣金相加判断是否超出总订单金额的80%
        BigDecimal orderAmount = orderDto.getOrderAmount();
        List<OrderDesignerVo> orderDesigners = dto.getOrderDesigners();
        BigDecimal reduce = subOrderDtoList.stream().map(SubOrderDto::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAmount = orderDesigners.stream().map(OrderDesignerVo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add).add(reduce);
        BigDecimal threshold = orderAmount.multiply(new BigDecimal("0.8"));
        if(totalAmount.compareTo(threshold) > 0) {
            throw new ApiException("派单金额超出总订单金额的80%");
        }
        AgencyDispatchDto dispatchDto = agencyDispatchMapper.selectAgencyDispatchById(dto.getAgencyDispatchId());
        if(dispatchDto == null){
            throw new ApiException("派单失败");
        }
        boolean updatedMainOrder = false;
        for (OrderDesignerVo vo: dto.getOrderDesigners()) {
            Order subOrder = BeanUtil.copyProperties(vo, Order.class);
            subOrder.setCreateTime(DateUtils.getNowDate());
            subOrder.setCreateBy(SecurityUtils.getNickName());
            if(StringUtils.isNotBlank(subOrder.getDesignerPhone())){
                subOrder.setDesignerPhone(ParamDecryptUtil.encrypt(subOrder.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
            }
            if(orderDto.getDesignerId() == null && !updatedMainOrder){
                subOrder.setId(orderDto.getId());
                orderMapper.updateOrder(subOrder);
                AlreadyDispatch alreadyDispatch = new AlreadyDispatch();
                alreadyDispatch.setOrderId(subOrder.getId());
                alreadyDispatch.setAgencyDispatchId(dto.getAgencyDispatchId());
                alreadyDispatch.setDispatchUserId(subOrder.getAllotUserId());
                alreadyDispatchMapper.insertAlreadyDispatch(alreadyDispatch);
                assignCount++;
                updatedMainOrder = true;
            }else if(subOrder.getId() == null){
                // 新增子订单
                String orderNo = createOrderNo(dto.getOrderNo());
                subOrder.setOrderNo(orderNo);
                subOrder.setPid(dto.getId());
                addOrder(subOrder, orderDto);
                AlreadyDispatch alreadyDispatch = new AlreadyDispatch();
                alreadyDispatch.setOrderId(subOrder.getId());
                alreadyDispatch.setAgencyDispatchId(dto.getAgencyDispatchId());
                alreadyDispatch.setDispatchUserId(subOrder.getAllotUserId());
                alreadyDispatchMapper.insertAlreadyDispatch(alreadyDispatch);
                assignCount++;
            }
        }
        AgencyDispatchDto agencyDispatchDto = agencyDispatchMapper.selectAgencyDispatchById(dto.getAgencyDispatchId());
        // 修改已派单设计师数量
        assignCount = assignCount + agencyDispatchDto.getAssignedQuantity();
        agencyDispatchMapper.updateDesignateNumberById(dto.getAgencyDispatchId(), assignCount, dto.getDispatchRemark());
        if(agencyDispatchDto.getDispatchNumber() != assignCount && agencyDispatchDto.getAssignedQuantity() == 0){
            agencyDispatchMapper.updateDispatchStatusById(agencyDispatchDto.getId(), 2);
        }
        return 1;
    }

    private void addOrder(Order subOrder, OrderDto orderDto) {
        subOrder.setSaleManId(orderDto.getSaleManId());
        subOrder.setSaleManName(orderDto.getSaleManName());
        subOrder.setDeptId(orderDto.getDeptId());
        subOrder.setDeptName(orderDto.getDeptName());
        subOrder.setTransferState(orderDto.getTransferState());
        subOrder.setOrderDate(orderDto.getOrderDate());
        subOrder.setStoreId(orderDto.getStoreId());
        subOrder.setStoreName(orderDto.getStoreName());
        subOrder.setBrandId(orderDto.getBrandId());
        subOrder.setShType(0);
        subOrder.setOrderAmount(orderDto.getOrderAmount());
        subOrder.setAmount(orderDto.getAmount());
        subOrder.setTaobaoId(orderDto.getTaobaoId());
        subOrder.setAllotType(0);
        subOrder.setAllotUrgency(orderDto.getAllotUrgency());
        subOrder.setFranchiseId(orderDto.getFranchiseId());
        subOrder.setArchiveExpectTime(orderDto.getArchiveExpectTime());
        subOrder.setStateDicCode(orderDto.getStateDicCode());
        subOrder.setStateDicName(orderDto.getStateDicName());
        subOrder.setPayType(orderDto.getPayType());
        subOrder.setAllotType(orderDto.getAllotType());
        orderMapper.insertOrder(subOrder);
    }

    private String createOrderNo(String orderNo) {
        String no = "000"+DateUtils.timestamp() +  String.valueOf((int) (Math.random() * 9000) + 1000); // 生成4位随机验证码;
        if(StringUtils.isNotBlank(orderNo)){
            List<SubOrderDto>  list = orderMapper.selectSubOrderListByPno(orderNo);
            int index = list.size() + 1;
            if(orderNo.startsWith("000")) {
                no =(index > 100 ? index : index > 10 ? "0"+index: "00"+index) + orderNo.substring(3);
            } else {
                no = (800+index) + orderNo;
            }
        }
        return no;
    }

    /**
     * 修改我的已派单
     * 
     * @param dto 我的已派单
     * @return 结果
     */
    @Override
    public int updateAlreadyDispatch(AlreadyDispatchVo dto) {
        AlreadyDispatch alreadyDispatch = BeanUtil.copyProperties(dto, AlreadyDispatch.class);
        alreadyDispatch.setUpdateTime(DateUtils.getNowDate());
        return alreadyDispatchMapper.updateAlreadyDispatch(alreadyDispatch);
    }

    /**
     * 批量删除我的已派单
     * 
     * @param ids 需要删除的我的已派单主键
     * @return 结果
     */
    @Override
    public int deleteAlreadyDispatchByIds(Long[] ids) {
        return alreadyDispatchMapper.deleteAlreadyDispatchByIds(ids);
    }

    /**
     * 删除我的已派单信息
     * 
     * @param id 我的已派单主键
     * @return 结果
     */
    @Override
    public int deleteAlreadyDispatchById(Long id) {
        return alreadyDispatchMapper.deleteAlreadyDispatchById(id);
    }

    @Override
    public List<SendBackDto> getSendBackInfo(Long id) {
        AlreadyDispatchDto alreadyDispatchDto = alreadyDispatchMapper.selectAlreadyDispatchById(id);
        if(alreadyDispatchDto == null){
            throw new ApiException("数据不存在");
        }
        AgencyDispatchDto agencyDispatchDto = agencyDispatchMapper.selectAgencyDispatchById(alreadyDispatchDto.getAgencyDispatchId());
        return sendBackMapper.findSendBackListByOrderId(agencyDispatchDto.getOrderId());
    }

    @Override
    public List<AlreadyRecordPageDto> alreadyRecordList(AlreadyRecordQueryVo params) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null){
            return null;
        }
        params.setBrandId(brandId);
        return alreadyDispatchMapper.findAlreadyRecordList(params);
    }
}
