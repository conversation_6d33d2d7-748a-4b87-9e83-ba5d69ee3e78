package com.xgwc.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.PageUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.XgwcPlatformTradeDetailsMapper;
import com.xgwc.order.dao.XgwcPlatformTradeMapper;
import com.xgwc.order.entity.XgwcPlatformTrade;
import com.xgwc.order.entity.XgwcPlatformTradeDetails;
import com.xgwc.order.entity.dto.XgwcPlatformTradeByBrandVO;
import com.xgwc.order.entity.vo.XgwcPlatformTradeQueryVo;
import com.xgwc.order.service.IXgwcPlatformTradeService;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.api.ServiceAuthorizeFeign;
import com.xgwc.user.feign.api.SysDictDataFeign;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class XgwcPlatformTradeServiceImpl implements IXgwcPlatformTradeService {

    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private SysDictDataFeign sysDictDataFeign;
    @Resource
    private ServiceAuthorizeFeign serviceAuthorizeFeign;
    @Resource
    private XgwcPlatformTradeMapper xgwcPlatformTradeMapper;
    @Resource
    private XgwcPlatformTradeDetailsMapper xgwcPlatformTradeDetailsMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveXgwcPlatformTrade(List<XgwcPlatformTrade> tradeList, List<XgwcPlatformTradeDetails> tradeDetailsList, Long brandOwnerId) {
        Map<String, List<XgwcPlatformTradeDetails>> traderNoDetailsMap = tradeDetailsList.stream().collect(Collectors.groupingBy(XgwcPlatformTradeDetails::getTradeNo));
        List<String> changeLog = Lists.newArrayList();
        Lists.partition(tradeList, 200).forEach(tradeGroupList -> {
            List<String> tradeNoList = tradeGroupList.stream().map(XgwcPlatformTrade::getTradeNo).toList();
            Map<String, XgwcPlatformTrade> dbDataMap = xgwcPlatformTradeMapper.listByTradeNo(tradeNoList, brandOwnerId).stream().collect(Collectors.toMap(XgwcPlatformTrade::getTradeNo, x -> x));
            List<XgwcPlatformTradeDetails> tradeDetailsGroupList = Lists.newArrayList();
            tradeGroupList.forEach(trade -> {
                XgwcPlatformTrade dbData = dbDataMap.get(trade.getTradeNo());
                if (dbData != null) {
                    StringBuilder sb = new StringBuilder();
                    // 考虑淘宝平台退款后, 会将订单金额变动, 产品要求保留之前的
                    if (trade.getOrderAmount().compareTo(dbData.getOrderAmount()) != 0) {
                        sb.append("订单金额由").append(dbData.getOrderAmount()).append("变为").append(trade.getOrderAmount()).append(";");
                        trade.setOrderAmount(this.getMax(trade.getOrderAmount(), dbData.getOrderAmount()));
                    }
                    if (trade.getRealAmount().compareTo(dbData.getRealAmount()) != 0) {
                        sb.append("商家实收金额").append(dbData.getRealAmount()).append("变为").append(trade.getRealAmount()).append(";");
                        trade.setRealAmount(this.getMax(trade.getRealAmount(), dbData.getRealAmount()));
                    }
                    if (trade.getPayAmount().compareTo(dbData.getPayAmount()) != 0) {
                        sb.append("买家实付金额").append(dbData.getPayAmount()).append("变为").append(trade.getPayAmount()).append(";");
                        trade.setPayAmount(this.getMax(trade.getPayAmount(), dbData.getPayAmount()));
                    }
                    if (!sb.isEmpty()) {
                        changeLog.add(trade.getTradeNo() + ":" + sb);
                    }
                }
                tradeDetailsGroupList.addAll(traderNoDetailsMap.get(trade.getTradeNo()));
            });

            xgwcPlatformTradeMapper.deleteByTradeNo(tradeNoList, brandOwnerId);
            xgwcPlatformTradeDetailsMapper.deleteByTradeNo(tradeNoList, brandOwnerId);

            xgwcPlatformTradeMapper.insertList(tradeGroupList);
            xgwcPlatformTradeDetailsMapper.insertList(tradeDetailsGroupList);
        });
        if (!changeLog.isEmpty()) {
            log.info("以下平台订单存在金额变动: {}", JSON.toJSONString(changeLog));
        }
    }

    public BigDecimal getMax(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) > 0 ? a : b;
    }


    /**
     * 查询平台交易单列表
     *
     * @param tradeQueryVo 平台交易单
     * @return 平台交易单
     */
    @Override
    public List<XgwcPlatformTradeByBrandVO> brandList(XgwcPlatformTradeQueryVo tradeQueryVo) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId == null) {
            return Lists.newArrayList();
        }
        tradeQueryVo.setBrandId(brandId);
        tradeQueryVo.setBrandIdList(null);
        return this.pageList(tradeQueryVo);
    }

    private List<XgwcPlatformTradeByBrandVO> pageList(XgwcPlatformTradeQueryVo tradeQueryVo) {
        if (tradeQueryVo.getCrateTimeStart() != null) {
            tradeQueryVo.setCrateTimeStart(DateUtils.parseDate(DateUtils.getStartTime(tradeQueryVo.getCrateTimeStart())));
        }
        if (tradeQueryVo.getCreateTimeEnd() != null) {
            tradeQueryVo.setCreateTimeEnd(DateUtils.parseDate(DateUtils.getEndTime(tradeQueryVo.getCreateTimeEnd())));
        }
        PageUtils.startPage();
        List<XgwcPlatformTradeByBrandVO> xgwcPlatformTradeByBrandVOS = xgwcPlatformTradeMapper.selectListByQuery(tradeQueryVo);
        if (!xgwcPlatformTradeByBrandVOS.isEmpty()) {
            Map<Long, String> franchiseNameMap = Maps.newHashMap();
            List<Long> franchiseIdList = xgwcPlatformTradeByBrandVOS.stream().map(XgwcPlatformTradeByBrandVO::getFranchiseId).filter(Objects::nonNull).distinct().toList();
            if (!franchiseIdList.isEmpty()) {
                franchiseNameMap.putAll(franchiseFeign.listByIds(franchiseIdList).stream().collect(Collectors.toMap(FranchiseDto::getId, FranchiseDto::getFranchiseName)));
            }
            Map<String, String> platformMap = JSONObject.parseArray(JSON.toJSONString(sysDictDataFeign.dictType("wdt_platform", null).getData())).stream().collect(Collectors.toMap(x -> ((JSONObject) x).getString("dictValue"), x -> ((JSONObject) x).getString("dictLabel")));
            xgwcPlatformTradeByBrandVOS.forEach(vo -> {
                vo.setPlatformName(platformMap.get(vo.getPlatformId().toString()));
                vo.setFranchiseName(franchiseNameMap.get(vo.getFranchiseId()));
            });
        }
        return xgwcPlatformTradeByBrandVOS;
    }

    @Override
    public List<XgwcPlatformTradeByBrandVO> serviceProviderList(XgwcPlatformTradeQueryVo tradeQueryVo) {
        Long serviceId = SecurityUtils.getSysUser().getServiceId();
        if (serviceId == null) {
            return Lists.newArrayList();
        }
        List<Long> brandIdList = serviceAuthorizeFeign.getServiceBrandIdList(serviceId);
        if (brandIdList.isEmpty()) {
            return Lists.newArrayList();
        }
        if (tradeQueryVo.getBrandId() != null && !brandIdList.contains(tradeQueryVo.getBrandId())) {
            return Lists.newArrayList();
        }
        tradeQueryVo.setBrandIdList(brandIdList);
        return this.pageList(tradeQueryVo);
    }

}