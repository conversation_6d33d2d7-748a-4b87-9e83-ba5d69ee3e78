package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.vo.IncomLineTarckVo;
import com.xgwc.order.entity.dto.IncomLineTarckDto;
import com.xgwc.order.entity.vo.IncomLineTarckQueryVo;

public interface IIncomLineTarckService  {
    /**
     * 查询进线跟进
     * 
     * @param id 进线跟进主键
     * @return 进线跟进
     */
    public IncomLineTarckDto selectIncomLineTarckById(Long id);

    /**
     * 查询进线跟进列表
     * 
     * @param incomLineTarck 进线跟进
     * @return 进线跟进集合
     */
    public List<IncomLineTarckDto> selectIncomLineTarckList(IncomLineTarckQueryVo incomLineTarck);

    /**
     * 新增进线跟进
     * 
     * @param incomLineTarck 进线跟进
     * @return 结果
     */
    public int insertIncomLineTarck(IncomLineTarckVo incomLineTarck);

    /**
     * 修改进线跟进
     * 
     * @param incomLineTarck 进线跟进
     * @return 结果
     */
    public int updateIncomLineTarck(IncomLineTarckVo incomLineTarck);

    /**
     * 批量删除进线跟进
     * 
     * @param ids 需要删除的进线跟进主键集合
     * @return 结果
     */
    public int deleteIncomLineTarckByIds(Long[] ids);

    /**
     * 删除进线跟进信息
     * 
     * @param id 进线跟进主键
     * @return 结果
     */
    public int deleteIncomLineTarckById(Long id);
}
