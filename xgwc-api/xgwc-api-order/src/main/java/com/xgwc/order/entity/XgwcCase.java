package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class XgwcCase {

private static final long serialVersionUID=1L;

    /** 实收金额 */
    private BigDecimal amount;

    private Long archiveId;

    /** 上传定稿源文件 */
    private String archiveFiles;

    /** 定稿预览图 */
    private String archiveImg;

    /** 品牌商id */
    private Long brandId;

    /** 业务id */
    private Long businessId;

    /** 业务名称 */
    private String businessName;

    /** 主键 */
    private Long caseId;

    /** 等级：1高 2中 3低 */
    private Integer caseLevel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 设计师ID */
    private Long designerId;

    /** 设计师名称 */
    private String designerName;
    /** 设计师手机号 */
    private String designerPhone;

    /** 加盟商id */
    private Long franchiseId;

    /** 关键字 */
    private String keyword;

    /** 链接 */
    private String linkUrl;

    /** 佣金金额 */
    private BigDecimal money;

    /** 订单id */
    private Long oderId;

    /** 标题 */
    private String title;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 下载次数 */
    private Integer downloadCount;

}