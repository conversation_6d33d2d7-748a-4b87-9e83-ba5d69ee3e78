package com.xgwc.order.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.ChatRecordDto;
import com.xgwc.order.entity.vo.ChatRecordQueryVo;
import com.xgwc.order.entity.vo.ChatRecordStatusVo;
import com.xgwc.order.entity.vo.ChatRecordVo;

import java.util.List;

public interface IChatRecordService  {
    /**
     * 查询聊天记录
     * 
     * @param id 聊天记录主键
     * @return 聊天记录
     */
    public ChatRecordDto selectChatRecordById(Long id);

    /**
     * 查询聊天记录列表
     * 
     * @param chatRecord 聊天记录
     * @return 聊天记录集合
     */
    public List<ChatRecordDto> selectChatRecordList(ChatRecordQueryVo chatRecord);

    /**
     * 新增聊天记录
     * 
     * @param chatRecord 聊天记录
     * @return 结果
     */
    public int insertChatRecord(ChatRecordVo chatRecord);

    /**
     * 修改聊天记录
     * 
     * @param chatRecord 聊天记录
     * @return 结果
     */
    public int updateChatRecord(ChatRecordVo chatRecord);

    /**
     * 批量删除聊天记录
     * 
     * @param ids 需要删除的聊天记录主键集合
     * @return 结果
     */
    public int deleteChatRecordByIds(Long[] ids);

    /**
     * 删除聊天记录信息
     * 
     * @param id 聊天记录主键
     * @return 结果
     */
    public int deleteChatRecordById(Long id);

    /**
     * 更新聊天记录状态
     * @param req 聊天记录状态
     * @return 结果
     */
    int updateChatRecordStatus(ChatRecordStatusVo req);

    /**
     * 查询聊天记录列表
     *
     * @param chatRecord 聊天记录
     * @return 聊天记录集合
     */
    ApiListResult selectChatRecordBigList(ChatRecordQueryVo chatRecord);

    /**
     * 获取聊天记录详细信息
     *
     * @param id 聊天记录主键
     * @return 聊天记录
     */
    ApiResult getInfoBigById(Long id) throws JsonProcessingException;
}
