package com.xgwc.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.IncomLineMapper;
import com.xgwc.order.dao.IncomLineSeasMapper;
import com.xgwc.order.dao.SeasRuleMapper;
import com.xgwc.order.entity.IncomLine;
import com.xgwc.order.entity.dto.IncomLineDto;
import com.xgwc.order.entity.dto.IncomLineSeasDto;
import com.xgwc.order.entity.dto.SeasRuleDto;
import com.xgwc.order.entity.vo.IncomLineQueryVo;
import com.xgwc.order.entity.vo.IncomLineTransferVo;
import com.xgwc.order.entity.vo.RoleDataVo;
import com.xgwc.order.service.IIncomLineSeasService;
import com.xgwc.order.service.IcommonService;
import com.xgwc.user.feign.api.UserDeptFeign;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
public class IncomLineSeasServiceImpl implements IIncomLineSeasService  {
    @Resource
    private IncomLineSeasMapper incomLineSeasMapper;
    @Resource
    private IncomLineMapper incomLineMapper;
    @Resource
    private SeasRuleMapper seasRuleMapper;
    @Resource
    private UserDeptFeign userDeptFeign;
    @Autowired
    private IcommonService icommonService;

    /**
     * 查询客户公海列表
     * 
     * @param incomLineSeas 客户公海
     * @return 客户公海
     */
    @Override
    public List<IncomLineDto> selectIncomLineSeasList(IncomLineQueryVo incomLineSeas) {
        RoleDataVo vo = icommonService.getRoleData(36);
        incomLineSeas.setFranchiseId(vo.getFranchiseId());
        incomLineSeas.setBrandId(vo.getBrandId());
        incomLineSeas.setInTime(vo.getCreateTime());
        incomLineSeas.setDeptIds(vo.getDeptIds());
        Boolean isDesensitization = vo.getIsDesensitization();
        incomLineSeas.setIsSeas("true");
        List<IncomLineDto> list = incomLineMapper.selectIncomLineList(incomLineSeas);
        if(!isDesensitization) {
            for (IncomLineDto d : list) {
                if(StringUtils.isNotBlank(d.getTel())) d.setTel(ParamDecryptUtil.decryptParam(d.getTel(), ParamDecryptUtil.PHONE_KEY));
            }
        }
        return list;
    }

    @Override
    public int transfer(IncomLineTransferVo transferVo){
//        List<IncomLineSeasDto> list = incomLineSeasMapper.selectIncomLineSeasByIds(transferVo.getIds());
        for(Long id: transferVo.getIds()) {
            collect(id, transferVo.getSaleId(), transferVo.getSaleName());
        }
        return 1;
    }

    @Override
    public int collect(Long id){
        return collect(id,SecurityUtils.getUserId(), SecurityUtils.getNickName());
    }

    private int collect(Long id,Long userId,String userName) {
        IncomLineSeasDto seasDto = incomLineSeasMapper.selectIncomLineSeasById(id);
        if(seasDto == null || seasDto.getRecycleTime().compareTo(new Date()) > 0) {
            throw new ApiException("进线不存在");
        }
        if(seasDto.getReceiveTime() != null && seasDto.getReceiveTime().compareTo(seasDto.getRecycleTime()) > 0) {
            throw new ApiException("进线未回收或已被其他人领取");
        }
        IncomLine line = new IncomLine();
        line.setId(id);
        line.setSaleId(userId);
        line.setSaleName(userName);
        line.setReceiver(userName);
        line.setReceiveTime(DateUtils.getNowDate());
        ApiResult result = userDeptFeign.getUserDeptByUserId(userId,"franchise");
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(result.getData()));
        JSONObject json = jsonObject.getJSONObject("data");
        if(json != null && json.getLong("deptId") == null) throw new ApiException("员工信息缺失，请完善员工信息");
        line.setDeptId(jsonObject.getLong("deptId"));
        line.setDeptName(jsonObject.getString("deptName"));

        //获取回收规则
        SeasRuleDto rule = seasRuleMapper.getSeasRule(seasDto.getBrandId());
        Date recycleTime = DateUtils.yearsLater(99);
        if(rule != null ){
            if(line.getRecycleNum() == null) line.setRecycleNum(0);
            if(rule.getRecycleRule() == 2) {
                line.setRecycleType(0);
                recycleTime = DateUtils.addHour(new Date(),rule.getRecycleNoEff() < rule.getRecycleNoDeal()*24 ? rule.getRecycleNoEff() : rule.getRecycleNoDeal()*24);
            } else {
                line.setRecycleType(1);
            }
        }
        line.setRecycleTime(recycleTime);
        return incomLineSeasMapper.collect(line);
    }
}
