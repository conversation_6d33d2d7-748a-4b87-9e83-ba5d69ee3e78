package com.xgwc.order.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

@Data
public class XgwcPlatformGoodsSpec {


    /**  */
    private Long id;

    /** 平台货品id */
    private String goodsId;

    /** 规格id */
    private String specId;

    /** 规格名称 */
    private String specName;

    /** 品牌商id */
    private Long brandId;


    public static XgwcPlatformGoodsSpec initByJson(JSONObject jsonObject) {
        XgwcPlatformGoodsSpec goodsSpec = new XgwcPlatformGoodsSpec();
        goodsSpec.goodsId = jsonObject.getString("api_goods_id");
        goodsSpec.specId = jsonObject.getString("api_spec_id");
        goodsSpec.specName = jsonObject.getString("api_spec_name");
        return goodsSpec;
    }

}