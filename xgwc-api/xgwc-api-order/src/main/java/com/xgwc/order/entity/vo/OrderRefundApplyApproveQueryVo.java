package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
@Builder
public class OrderRefundApplyApproveQueryVo {

    @FieldDesc("搜索关键字")
    private String keyword;

    @FieldDesc("加盟商id")
    private Long franchiseId;
    /** 审批状态 OaStatus */
    private String applyStatus;
    @FieldDesc("退款类型 1.全额退款，2.部分退款，3.退垫付")
    private Integer refundType;
    // 退款原因  平台字典 reason
    private String refundReasonCode;

    @FieldDesc("申请开始时间")
    private Date crateTimeStart;
    @FieldDesc("申请结束时间")
    private Date createTimeEnd;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("品牌商id")
    private List<Long> brandIdList;

}