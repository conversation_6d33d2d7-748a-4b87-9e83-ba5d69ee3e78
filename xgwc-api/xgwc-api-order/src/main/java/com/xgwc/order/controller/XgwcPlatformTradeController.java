package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcPlatformTradeByBrandVO;
import com.xgwc.order.entity.vo.XgwcPlatformTradeQueryVo;
import com.xgwc.order.service.IXgwcPlatformTradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/xgwcPlatformTrade")
public class XgwcPlatformTradeController extends BaseController {
    @Autowired
    private IXgwcPlatformTradeService xgwcPlatformTradeService;

    /**
     * 查询平台交易单列表
     */
    @MethodDesc("品牌商查询平台收款列表")
    @PreAuthorize("@ss.hasPermission('order:XgwcPlatformTrade:brandList')")
    @GetMapping("/brandList")
    public ApiResult<XgwcPlatformTradeByBrandVO> brandList(XgwcPlatformTradeQueryVo tradeQueryVo) {
        return getDataTable(xgwcPlatformTradeService.brandList(tradeQueryVo));
    }

    /**
     * 查询平台交易单列表
     */
    @MethodDesc("服务商查询平台收款列表")
    @PreAuthorize("@ss.hasPermission('order:XgwcPlatformTrade:serviceProviderList')")
    @GetMapping("/serviceProviderList")
    public ApiResult<XgwcPlatformTradeByBrandVO> serviceProviderList(XgwcPlatformTradeQueryVo tradeQueryVo) {
        return getDataTable(xgwcPlatformTradeService.serviceProviderList(tradeQueryVo));
    }

}
