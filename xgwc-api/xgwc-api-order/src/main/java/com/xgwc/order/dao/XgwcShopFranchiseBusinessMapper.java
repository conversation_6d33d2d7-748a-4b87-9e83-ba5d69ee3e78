package com.xgwc.order.dao;

import com.xgwc.order.entity.XgwcShopFranchiseBusiness;
import com.xgwc.order.feign.entity.XgwcShopFranchiseBusinessDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface XgwcShopFranchiseBusinessMapper  {

    List<XgwcShopFranchiseBusinessDto> selectListByShopId(Long shopId);
    List<XgwcShopFranchiseBusinessDto> selectListByShopIds(Long[] shopIds);

    List<XgwcShopFranchiseBusinessDto> shopFranchiseBusiness(@Param("userId") Long userId,@Param("brandId") Long brandId, @Param("franchiseId") Long franchiseId);
    /**
     * 修改加盟商店铺业务
     * 
     * @param xgwcShopFranchiseBusiness 加盟商店铺业务
     * @return 结果
     */
    public int updateXgwcShopFranchiseBusiness(XgwcShopFranchiseBusiness xgwcShopFranchiseBusiness);

}
