package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.util.DateUtils;
import com.xgwc.order.entity.OrderTrace;
import com.xgwc.order.entity.vo.OrderVo;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class OrderDto {

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long id;

    @FieldDesc("订单归类")
    private Integer orderCategory;

    @FieldDesc("谈单人员")
    @Excel(name = "谈单人员")
    private Long saleManId;

    @FieldDesc("谈单人员名称")
    @Excel(name = "谈单人员名称")
    private String saleManName;

    @FieldDesc("录入部门编码")
    @Excel(name = "录入部门编码")
    private Long deptId;

    @FieldDesc("录入部门名称")
    @Excel(name = "录入部门名称")
    private String deptName;

    @FieldDesc("订单类型：0正常单，1转化单")
    @Excel(name = "订单类型：0正常单，1转化单")
    private Integer transferState;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private String orderDate;

    @FieldDesc("订单来源ID（店铺id）")
    @Excel(name = "订单来源ID（店铺id）")
    private Long storeId;

    @FieldDesc("订单来源名称（店铺名称）")
    @Excel(name = "订单来源名称（店铺名称）")
    private String storeName;

    @FieldDesc("所属业务编号-字典")
    private Long stateDicCode;

    @FieldDesc("所属业务名称-字典")
    private String stateDicName;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("品牌商名称")
    @Excel(name = "品牌商名称")
    private String brandName;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商名称")
    @Excel(name = "加盟商名称")
    private String franchiseName;

    @FieldDesc("支付方式  1:淘宝  2：微信  3：支付宝")
    @Excel(name = "支付方式  1:淘宝  2：微信  3：支付宝")
    private Integer payChannel;

    @FieldDesc("订单状态（0：未发货 1：完成  2：退款 3：部分退款）")
    @Excel(name = "订单状态（0：未发货 1：完成  2：退款 3：部分退款）")
    private Integer shType;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String taobaoId;

    @FieldDesc("订单金额")
    @Excel(name = "订单金额")
    private BigDecimal orderAmount;

    @FieldDesc("支付截图")
    @Excel(name = "支付截图")
    private String payImg;

    @FieldDesc("收款编号")
    @Excel(name = "收款编号")
    private String collectionNo;

    @FieldDesc("实收金额")
    @Excel(name = "实收金额")
    private BigDecimal amount;

    /**
     * 发生在退款之后
     */
    @FieldDesc("当前实收金额")
    @Excel(name = "当前实收金额")
    private BigDecimal nowAmount;

    @FieldDesc("付款方式:1全款/2阶段付")
    @Excel(name = "付款方式:1全款/2阶段付")
    private Integer payType;

    @FieldDesc("期望初稿日期：yyyy-MM-dd HH:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "期望初稿日期：yyyy-MM-dd HH:00:00", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveExpectTime;

    @FieldDesc("派单类型")
    @Excel(name = "派单类型")
    private Integer allotType;

    @FieldDesc("是否紧急：0否 1是")
    @Excel(name = "是否紧急：0否 1是")
    private Integer allotUrgency;

    @FieldDesc("派单需求")
    @Excel(name = "派单需求")
    private String allotRemark;

    @FieldDesc("派单设计师数量")
    @Excel(name = "派单设计师数量")
    private Long allotNum;

    @FieldDesc("协助文件")
    @Excel(name = "协助文件")
    private String allotFile;

    @FieldDesc("母订单id")
    @Excel(name = "母订单id")
    private Long pid;

    @FieldDesc("设计师ID")
    @Excel(name = "设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    @Excel(name = "设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    @Excel(name = "设计师电话")
    private String designerPhone;

    @FieldDesc("设计师业务类型")
    private String designerBusiness;

    @FieldDesc("佣金金额")
    @Excel(name = "佣金金额")
    private BigDecimal money;

    @FieldDesc("是否转介绍：0否 1是")
    private Integer isTransfer;

    /**
     * 发生退款之后
     */
    @FieldDesc("当前佣金金额")
    @Excel(name = "当前佣金金额")
    private BigDecimal nowMoney;

    @FieldDesc("约定初稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "约定初稿日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveAppointTime;

    @FieldDesc("交稿状态：0未交稿 1交初稿 2交定稿")
    @Excel(name = "交稿状态：0未交稿 1交初稿 2交定稿")
    private Integer archiveType;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("退款状态")
    private Long afterSaleStatus;
    @FieldDesc("退款时间")
    private Date afterSaleDate;
    @FieldDesc("退款原因")
    private String afterSaleReason;

    @FieldDesc("合同状态")
    private Long contractStatus;

    @FieldDesc("开票状态")
    private Integer invoiceStatus;

    @FieldDesc("接单时间")
    private Date stylistTime;

    @FieldDesc("派单人员ID")
    private Long allotUserId;

    @FieldDesc("是否有售后：0无,1有")
    private Integer isAfterSale;

    @FieldDesc("成交时间")
    private String dealTime;

    @FieldDesc("是否结算")
    private Integer settlement;

    @FieldDesc("结算时间")
    private String settlementTime;

    @FieldDesc("是否归档")
    private Integer isArchiving;

    private List<OrderPayDto> orderPays;

    private List<SubOrderDto> subOrders;

    @FieldDesc("派单人员名称")
    private String allotUserName;

    @FieldDesc("退款状态：0 未退款，1 部分退款，2 全额退款")
    private Integer refundStatus;

    /**
     * 是否锁定
     */
    private Integer isLock;

    @FieldDesc("需派设计师数")
    private Integer needDispatchNumber;



    // 是否老系统订单, 老系统订单会同步到新系统
    public boolean isOldSysOrder() {
        // TODO
        return false;
    }

    public OrderTrace compare(OrderVo vo) {
        String[] transferStates = {"正常单","转化单","协助单","拍错单"};
        String[] payChannels = {"","淘宝","微信","支付宝","对公","拼多多","小程序","天猫"};
        String[] shTypes = {"未发货","完成","退款","部分退款"};
        String[] payTypes = {"","全款","定金","过程款","尾款"};

        OrderTrace trace = new OrderTrace();
        if(saleManId != null && !saleManId.equals(vo.getSaleManId())) {
            trace.setSaleManName( String.format("%s -> %s",saleManName, vo.getSaleManName()));
        }
        if (transferState != null && !transferState.equals(vo.getTransferState())) {
            trace.setSaleManName( String.format("%s -> %s",transferStates[transferState], transferStates[vo.getTransferState()]));
        }
        if (orderDate != null && !orderDate.equals(vo.getOrderDate())) {
            trace.setOrderDate( String.format("%s -> %s",orderDate, vo.getOrderDate()));
        }
        if (payChannel != null && !payChannel.equals(vo.getPayChannel())) {
            trace.setPayChannel( String.format("%s -> %s",payChannels[payChannel], payChannels[vo.getPayChannel()]));
        }
        if (shType != null && !shType.equals(vo.getShType())) {
            trace.setShType( String.format("%s -> %s",shTypes[shType], shTypes[vo.getShType()]));
        }
        if (taobaoId != null && !taobaoId.equals(vo.getTaobaoId())) {
            trace.setTaobaoId( String.format("%s -> %s",taobaoId, vo.getTaobaoId()));
        }
        if (orderAmount != null && !orderAmount.equals(vo.getOrderAmount())) {
            trace.setOrderAmount( String.format("%s -> %s",orderAmount, vo.getOrderAmount()));
        }
        if (archiveExpectTime != null && vo.getArchiveExpectTime() !=null && !archiveExpectTime.equals(vo.getArchiveExpectTime())) {
            trace.setArchiveExpectTime( String.format("%s -> %s", DateUtils.formatDateTime(archiveExpectTime), DateUtils.formatDateTime( vo.getArchiveExpectTime())));
        }
        if (allotUserId != null && !allotUserId.equals(vo.getAllotUserId())) {
            trace.setAllotUserName( String.format("%s -> %s",allotUserName, vo.getAllotUserName()));
        }
        if (designerId != null && !designerId.equals(vo.getDesignerId())) {
            trace.setDesignerName( String.format("%s -> %s",designerName, vo.getDesignerName()));
        }
        if (money != null && !money.equals(vo.getMoney())) {
            trace.setMoney( String.format("%s -> %s",money, vo.getMoney()));
        }
        if (archiveAppointTime != null && vo.getArchiveAppointTime() != null && archiveAppointTime.compareTo(vo.getArchiveAppointTime()) != 0) {
            trace.setArchiveAppointTime( String.format("%s -> %s",DateUtils.formatDateTime(archiveAppointTime), DateUtils.formatDateTime( vo.getArchiveAppointTime())));
        }
        StringBuffer payType = new StringBuffer();
        if(orderPays != null) {
            for (OrderPayDto orderPay : orderPays) {
                List<OrderPayDto> nows = vo.getOrderPays().stream().filter(v-> Objects.equals(v.getPayType(), orderPay.getPayType())).toList();
                payType.append("实收").append(payTypes[orderPay.getPayType()]);
                int index = 0;
                for (OrderPayDto nowPay : nows) {
                    if(index == 0) {
                        if (orderPay.getAmount() != null && orderPay.getAmount().compareTo(nowPay.getAmount()) != 0) {
                            payType.append("金额").append( String.format("%s -> %s",orderPay.getAmount(), nowPay.getAmount()));
                        }
                        if (orderPay.getPayChannel() != null && !orderPay.getPayChannel().equals(vo.getPayChannel())) {
                            payType.append("付款方式").append( String.format("%s -> %s",payChannels[orderPay.getPayChannel()],payChannels[vo.getPayChannel()]));
                        }
                        if (orderPay.getPayImg() != null && !orderPay.getPayImg().equals(vo.getPayImg())) {
                            payType.append("付款截图").append("有更改");
                        }
                    } else {
                        if (nowPay.getAmount() != null ) {
                            payType.append("金额").append( String.format("无 -> %s", nowPay.getAmount()));
                        }
                        if (vo.getPayChannel() != null ) {
                            payType.append("付款方式").append( String.format("无 -> %s", payChannels[vo.getPayChannel()]));
                        }
                        if (vo.getPayImg() != null) {
                            payType.append("付款截图").append("有新增");
                        }
                    }
                    index++;
                }
            }
        }

        trace.setPayType(payType.toString());
        trace.setOrderId(id);
        return trace;
    }


    public void encryption() {
        setSaleManName("****");
        setDeptName("****");
        setStoreName("****");
        setBrandName("****");
        setFranchiseName("****");
        setOrderNo("****");
        setTaobaoId("****");
        setAllotRemark("****");
        setDesignerName("****");
        setDesignerPhone("****");
        setDesignerBusiness("****");
        setRemark("****");
        setCreateBy("****");
        setUpdateBy("****");
    }
}
