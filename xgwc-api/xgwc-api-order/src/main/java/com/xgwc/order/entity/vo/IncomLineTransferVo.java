package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class IncomLineTransferVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("ids")
    @NotNull
    private Long[] ids;

    @FieldDesc("销售员ID")
    @NotNull
    private Long saleId;

    @FieldDesc("销售员名称")
    @NotNull
    private String saleName;

    @FieldDesc("部门id")
    @NotNull
    private Long deptId;

    @FieldDesc("部门姓名")
    @NotNull
    private String deptName;




}
