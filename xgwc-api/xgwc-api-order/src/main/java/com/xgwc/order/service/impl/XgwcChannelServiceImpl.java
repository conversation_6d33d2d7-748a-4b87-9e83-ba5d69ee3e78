package com.xgwc.order.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.XgwcChannelMapper;
import com.xgwc.order.entity.dto.XgwcChannelDto;
import com.xgwc.order.entity.param.XgwcChannelParam;
import com.xgwc.order.entity.vo.XgwcChannelVo;
import com.xgwc.order.service.XgwcChannelService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  18:16
 */
@Service
@Slf4j
public class XgwcChannelServiceImpl implements XgwcChannelService {

    @Resource
    private XgwcChannelMapper xgwcChannelMapper;

    /**
     * @param xgwcChannelParam 查询参数
     * @return 列表信息
     * 查询渠道信息
     */
    @Override
    public List<XgwcChannelVo> getXgwcChannelList(XgwcChannelParam xgwcChannelParam) {
        xgwcChannelParam.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        return xgwcChannelMapper.getXgwcChannelList(xgwcChannelParam);
    }

    /**
     * @param xgwcChannelDto 保存参数
     * @return
     */
    @Override
    public ApiResult saveXgwcChannel(XgwcChannelDto xgwcChannelDto) {
        // 参数校验
        if (xgwcChannelDto == null) {
            log.warn("保存渠道失败：参数不能为空");
            return ApiResult.error("渠道信息不能为空");
        }

        try {
            // 执行保存操作
            xgwcChannelDto.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
            xgwcChannelDto.setCreateBy(SecurityUtils.getNickName());
            int affectedRows = xgwcChannelMapper.saveXgwcChannel(xgwcChannelDto);
            if (affectedRows <= 0) {
                log.error("保存渠道失败，未影响任何行，渠道名称={}", xgwcChannelDto.getChannelName());
                return ApiResult.error("保存渠道失败");
            }

            log.info("保存渠道成功，渠道ID={}, 渠道名称={}",
                    xgwcChannelDto.getChannelId(), xgwcChannelDto.getChannelName());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("保存渠道异常，渠道名称={}, 错误信息：{}",
                    xgwcChannelDto.getChannelName(), e.getMessage(), e);
            return ApiResult.error("系统异常，保存渠道失败");
        }
    }

    /**
     * 根据渠道ID查询渠道信息
     * @param channelId 渠道ID
     * @return 渠道信息
     */
    @Override
    public ApiResult getXgwcChannelById(Integer channelId) {
        // 参数校验
        if (channelId == null) {
            log.warn("查询渠道失败：渠道ID不能为空");
            return ApiResult.error("渠道ID不能为空");
        }

        try {
            XgwcChannelVo channelInfo = xgwcChannelMapper.getXgwcChannelById(channelId);
            if (channelInfo == null) {
                log.warn("未找到渠道信息，渠道ID={}", channelId);
                return ApiResult.error("渠道信息不存在");
            }

            log.info("查询渠道成功，渠道ID={}", channelId);
            return ApiResult.ok(channelInfo);
        } catch (Exception e) {
            log.error("查询渠道异常，渠道ID={}, 错误信息：{}", channelId, e.getMessage(), e);
            return ApiResult.error("系统异常，查询渠道失败");
        }
    }

    /**
     * 更新渠道信息
     * @param xgwcChannelDto 渠道信息DTO
     * @return 操作结果
     */
    @Override
    public ApiResult updateXgwcChannel(XgwcChannelDto xgwcChannelDto) {
        // 参数校验
        if (xgwcChannelDto == null || xgwcChannelDto.getChannelId() == null) {
            log.warn("更新渠道失败：参数或渠道ID为空");
            return ApiResult.error("渠道信息不完整");
        }

        try {
            // 执行更新操作
            xgwcChannelDto.setUpdateBy(SecurityUtils.getNickName());
            int affectedRows = xgwcChannelMapper.updateXgwcChannelById(xgwcChannelDto);
            if (affectedRows <= 0) {
                log.error("更新渠道失败，未影响任何行，渠道ID={}", xgwcChannelDto.getChannelId());
                return ApiResult.error("更新渠道失败，可能ID不存在");
            }

            log.info("更新渠道成功，渠道ID={}", xgwcChannelDto.getChannelId());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新渠道异常，渠道ID={}, 错误信息：{}",
                    xgwcChannelDto.getChannelId(), e.getMessage(), e);
            return ApiResult.error("系统异常，更新渠道失败");
        }
    }

    /**
     * 更新渠道状态
     * @param channelId 渠道ID
     * @param status 状态值
     * @return 操作结果
     */
    @Override
    public ApiResult updateStatusById(Integer channelId, Integer status) {
        // 参数校验
        if (channelId == null ) {
            log.warn("更新渠道状态失败：渠道ID或状态为空");
            return ApiResult.error("参数不完整");
        }

        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新品牌状态失败");
        }
        try {
            // 执行状态更新
            int affectedRows = xgwcChannelMapper.updateStatusById(channelId, status);
            if (affectedRows <= 0) {
                log.error("更新渠道状态失败，未影响任何行，渠道ID={}", channelId);
                return ApiResult.error("更新状态失败，可能ID不存在");
            }

            log.info("更新渠道状态成功，渠道ID={}, 新状态={}", channelId, status);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新渠道状态异常，渠道ID={}, 错误信息：{}", channelId, e.getMessage(), e);
            return ApiResult.error("系统异常，更新状态失败");
        }
    }
}
