package com.xgwc.order.entity.dto;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.order.entity.Order;
import com.xgwc.order.entity.OrderInvoiceApply;
import com.xgwc.order.entity.OrderRefundApply;
import com.xgwc.order.entity.OrderRefundCommision;
import com.xgwc.order.entity.OrderRefundPay;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Setter
@Getter
public class OrderRefundApplyInfoDto {

    /** 主键 */
    private Long id;
    /** 订单编号 */
    private String orderNo;
    /** 客户ID */
    private String taobaoId;
    /** 下单日期 */
    private String orderDate;
    /** 谈单人员名称 */
    private String saleManName;
    /** 订单金额 */
    private BigDecimal orderAmount;
    /** 实收金额 */
    private BigDecimal amount;

    /** 1.全额退款，2.部分退款，3.退垫付 */
    private Integer refundType;

    /** 退款原因code, 字典配置 */
    private String refundReasonCode;
    /** 退款原因 */
    private String refundReason;
    /** 退款说明 */
    private String remark;


    /** 可退金额 */
    private BigDecimal preAmount;
    /** 总退款金额 */
    private BigDecimal lastAmount;


    /** 原佣金 */
    private BigDecimal preCommission;
    /** 现佣金 */
    private BigDecimal lastCommission;

    /** 开票金额 */
    private BigDecimal invoiceAmount;


    private List<OrderRefundPayDto> refundPayDtoList;
    private List<OrderRefundCommisionDto> refundCommisionDtoList;
    private List<OrderInvoiceApply> orderInvoiceList;


    public static OrderRefundApplyInfoDto init(OrderRefundApply orderRefundApply, Order order, List<OrderRefundPay> refundPayList, List<OrderRefundCommision> refundCommisionList, List<OrderInvoiceApply> orderInvoiceList) {
        OrderRefundApplyInfoDto applyInfoDto = BeanUtil.copyProperties(order, OrderRefundApplyInfoDto.class);
        BeanUtil.copyProperties(orderRefundApply, applyInfoDto);
        applyInfoDto.refundPayDtoList = refundPayList.stream().map(pay -> {
            OrderRefundPayDto payDto = BeanUtil.copyProperties(pay, OrderRefundPayDto.class);
            payDto.initJson();
            return payDto;
        }).toList();
        applyInfoDto.lastAmount = refundPayList.stream().map(OrderRefundPay::getRefundAmount).filter(x -> x != null && x.compareTo(BigDecimal.ZERO) > 0).reduce(BigDecimal.ZERO, BigDecimal::add);
        applyInfoDto.refundCommisionDtoList = refundCommisionList.stream().map(commision -> BeanUtil.copyProperties(commision, OrderRefundCommisionDto.class)).toList();
        applyInfoDto.lastCommission = refundCommisionList.stream().map(OrderRefundCommision::getAfterCommisionAmount).filter(x -> x != null && x.compareTo(BigDecimal.ZERO) > 0).reduce(BigDecimal.ZERO, BigDecimal::add);
        applyInfoDto.invoiceAmount = orderInvoiceList.stream().map(OrderInvoiceApply::getInvoiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        applyInfoDto.orderInvoiceList = orderInvoiceList;
        return applyInfoDto;
    }

}