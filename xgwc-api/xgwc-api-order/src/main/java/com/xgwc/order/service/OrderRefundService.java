package com.xgwc.order.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.OrderRefundApply;
import com.xgwc.order.entity.dto.OrderRefundApplyDto;
import com.xgwc.order.entity.vo.OrderRefundApplyVo;

public interface OrderRefundService {

    /**
     * 插入申请数据
     * @param orderRefundApplyVo 申请数据
     * @return 是否成功
     */
    ApiResult insertOrderRefundApply(OrderRefundApplyVo orderRefundApplyVo);

    /**
     * 更新申请数据
     * @param orderRefundApply 申请数据
     * @return 是否成功
     */
    int updateOrderRefundApply(OrderRefundApply orderRefundApply);

    /**
     * 根据申请id获取审核详情
     * @param applyId 申请id
     * @return 申请列表
     */
    OrderRefundApplyDto getOrderRefundApplyByApplyId(Long applyId);

    /**
     * 根据订单id获取退款所需信息
     * @param orderNo 订单id
     * @return 信息
     */
    OrderRefundApplyDto getOrderRefundInfoByOrderNo(String orderNo);
}
