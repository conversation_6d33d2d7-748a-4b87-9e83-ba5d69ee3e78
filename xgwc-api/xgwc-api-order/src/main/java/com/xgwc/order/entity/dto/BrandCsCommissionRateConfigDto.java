package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.util.json.JsonUtils;
import com.xgwc.order.entity.CsCommissionRateConfigRange;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class BrandCsCommissionRateConfigDto {

    @FieldDesc("主键ID")
    @Excel(name = "主键ID")
    private Long id;

    @FieldDesc("配置类型: 1 基础, 2 团队负责人, 3 单人")
    @Excel(name = "配置类型: 1 基础, 2 团队负责人, 3 单人")
    private Long type;

    @FieldDesc("加盟商ID")
    @Excel(name = "加盟商ID")
    private Long franchiseeId;
    private String franchiseeName;

    @FieldDesc("档位名称")
    @Excel(name = "档位名称")
    private String gearName;

    @FieldDesc("加盟商员工ID")
    @Excel(name = "加盟商员工ID")
    private Long staffId;
    private String staffName;

    @FieldDesc("加盟商部门ID")
    @Excel(name = "加盟商部门ID")
    private Long deptId;
    private String deptName;

    @FieldDesc("业务ID")
    @Excel(name = "业务ID")
    private Long businessId;
    private String businessName;

    @FieldDesc("生效周期类型: 1 长期有效, 2 指定时间")
    @Excel(name = "生效周期类型: 1 长期有效, 2 指定时间")
    private Long effectiveCycleType;

    @FieldDesc("生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    @FieldDesc("生效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    @FieldDesc("状态: 1生效, 2失效")
    @Excel(name = "状态: 1生效, 2失效")
    private Long status;

    @FieldDesc("是否新业务(0:否 1:是)")
    @Excel(name = "是否新业务(0:否 1:是)")
    private Long isNewBusiness;

    @FieldDesc("新业务额外前x月")
    @Excel(name = "新业务额外前x月")
    private Long extraMonthCount;

    @FieldDesc("额外类型: 1本月生效, 2次月生效")
    @Excel(name = "额外类型: 1本月生效, 2次月生效")
    private Long extraType;

    @FieldDesc("额外加x比例")
    @Excel(name = "额外加x比例")
    private Long extraCommissionRate;

    @FieldDesc("是否包含设定区间最小值")
    @Excel(name = "是否包含设定区间最小值")
    private Long isContainsMin;

    @JsonIgnore
    @FieldDesc("区间比例配置(JSON格式)")
    @Excel(name = "区间比例配置(JSON格式)")
    private String rangeRateJson;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private Long createById;
    private String createByName;

    @FieldDesc("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @Valid
    @NotNull
    @FieldDesc("区间配置")
    private List<CsCommissionRateConfigRange> rangeList;

    public BrandCsCommissionRateConfigDto initRangeList() {
        this.rangeList = JsonUtils.toList(rangeRateJson, CsCommissionRateConfigRange.class);
        return this;
    }

}