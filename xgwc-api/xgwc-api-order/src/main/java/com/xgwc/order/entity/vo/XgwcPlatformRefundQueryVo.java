package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.util.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class XgwcPlatformRefundQueryVo {

    @FieldDesc("搜索关键字")
    private String keyword;

    @FieldDesc("平台id")
    private String platformId;

    @FieldDesc("平台店铺id")
    private String platformShopId;

    @FieldDesc("平台订单编号")
    private String tradeNo;

    @FieldDesc("平台退款编号")
    private String refundNo;

    @FieldDesc("客户id")
    private String customerId;

    @FieldDesc("退款状态")
    private String status;

    @FieldDesc("买家申请退款金额")
    private BigDecimal refundAmount;

    @FieldDesc("实际退款")
    private BigDecimal actualRefundAmount;

    @FieldDesc("付款时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeStart;

    @FieldDesc("付款时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTimeEnd;

    @FieldDesc("退款申请时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTimeStart;

    @FieldDesc("退款申请时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTimeEnd;

    @FieldDesc("品牌商id")
    private Long brandId;

    public void formatTime() {
        if (this.payTimeStart != null) {
            this.payTimeStart = DateUtils.parseDate(DateUtils.getStartTime(this.payTimeStart));
        }
        if (this.payTimeEnd != null) {
            this.payTimeEnd = DateUtils.parseDate(DateUtils.getEndTime(this.payTimeEnd));
        }
        if (this.refundTimeStart != null) {
            this.refundTimeStart = DateUtils.parseDate(DateUtils.getStartTime(this.refundTimeStart));
        }
        if (this.refundTimeEnd != null) {
            this.refundTimeEnd = DateUtils.parseDate(DateUtils.getEndTime(this.refundTimeEnd));
        }
    }

}