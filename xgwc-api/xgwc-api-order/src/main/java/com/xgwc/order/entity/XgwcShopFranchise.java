package com.xgwc.order.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;


@Data
public class XgwcShopFranchise {

private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;
    /** 业务ids */
    private String businessIds;
    /** 业务及部门 */
    private String businessName;

    /** 加盟商id */
    private Long franchiseId;

    /** 授权时间 */
    private Date reviewTime;

    /** 品牌店铺id */
    private Long shopId;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}