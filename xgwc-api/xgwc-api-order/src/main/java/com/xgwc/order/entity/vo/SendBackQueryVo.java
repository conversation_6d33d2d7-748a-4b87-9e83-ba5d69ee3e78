package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

@Data
public class SendBackQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("开始时间")
    private String startTime;

    @FieldDesc("结束时间")
    private String endTime;

    @FieldDesc("加盟商")
    private Long franchiseeId;

    @FieldDesc("业务")
    private Long businessId;

    @FieldDesc("其他:订单编号/退回人/客户ID")
    private String other;

    private Long brandId;
}
