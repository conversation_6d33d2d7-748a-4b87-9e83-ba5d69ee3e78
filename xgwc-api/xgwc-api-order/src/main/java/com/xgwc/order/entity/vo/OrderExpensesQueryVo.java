package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.List;

@Data
public class OrderExpensesQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("搜索关键字(订单编号/客户id/报销id/谈单人/申请人)")
    private String keyword;

    @FieldDesc("品牌商id")
    private List<Long> brandIdList;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("审批状态 ING：审批中/PASS：已通过/REJECT：驳回/CANCEL：终止/WITHDRAW：撤回")
    private String applyStatus;

    @FieldDesc("申请开始时间")
    private String startTime;

    @FieldDesc("申请结束时间")
    private String endTime;

}
