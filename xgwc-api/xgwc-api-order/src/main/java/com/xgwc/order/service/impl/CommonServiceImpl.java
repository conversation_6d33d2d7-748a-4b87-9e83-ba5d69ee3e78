package com.xgwc.order.service.impl;

import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.entity.vo.RoleDataVo;
import com.xgwc.order.service.IcommonService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class CommonServiceImpl implements IcommonService {

    @Resource
    private OrderMapper orderMapper;

    @Override
    public RoleDataVo getRoleData(Integer menuId) {
        RoleDataVo vo = new RoleDataVo();
        SysUser user = SecurityUtils.getSysUser();
        List<String> roleIds = new ArrayList<>(SecurityUtils.getRoles());
        List<Long> deptIds = new ArrayList<>();
        boolean isDesensitization = true;
        long timeLimit = 1L;

        //1：品牌商，2：加盟商，3：设计师，4:品牌商员工，5：加盟商员工
        switch (user.getUserType()) {
            case 1:
                deptIds = null;
                isDesensitization = false;
                vo.setBrandId(user.getBrandId());
                break;
            case 4:
                if(!roleIds.isEmpty()) {
                    List<RoleDataVo> roleDataVoList = orderMapper.brandRules(roleIds, menuId);
                    for (RoleDataVo roleData : roleDataVoList) {
                        if(roleData.getDataMasking() != null && roleData.getDataMasking() == 1L) {
                            isDesensitization = false;
                        }
                        if(roleData.getTimeLimit() != null ) {
                            if(roleData.getTimeLimit() == 0L) {
                                timeLimit = roleData.getTimeLimit();
                            }
                            if(timeLimit != 0L && timeLimit < roleData.getTimeLimit()) {
                                timeLimit = roleData.getTimeLimit();
                            }
                        }
                    }
                }
                deptIds = null;
                vo.setBrandId(user.getBrandId());
                break;
            case 2:
                deptIds = null;
                isDesensitization = false;
                vo.setFranchiseId(user.getFranchiseId());
                break;
            case 5:
                if(!roleIds.isEmpty()) {
                    List<RoleDataVo> roleDataVoList = orderMapper.franchiseRules(roleIds, menuId);
                    if(roleDataVoList != null) {
                        for (RoleDataVo roleData : roleDataVoList) {
                            if(roleData == null) continue;
                            if(roleData.getDataMasking() != null && roleData.getDataMasking() == 1L) {
                                isDesensitization = false;
                            }
                            if(roleData.getTimeLimit() != null ) {
                                if(roleData.getTimeLimit() == 0L) {
                                    timeLimit = roleData.getTimeLimit();
                                }
                                if(timeLimit != 0L && timeLimit < roleData.getTimeLimit()) {
                                    timeLimit = roleData.getTimeLimit();
                                }
                            }
                            if(roleData.getDataScope() != null ) {
                                if(deptIds != null) {
                                    if(roleData.getDataScope() == 1) {
                                        deptIds = null;
                                    } else if(roleData.getDataScope() == 2) {
                                        deptIds.add(roleData.getDeptId());
                                    } else if(roleData.getDataScope() == 3) {
                                        Long deptId = SecurityUtils.getSysUser().getDeptId();
                                        if(deptId == null)  deptId = 0L;
                                        deptIds.add(deptId);
                                    }
                                }
                            }
                        }
                    }
                }
                vo.setFranchiseId(user.getFranchiseId());
                break;
            default:
                vo.setBrandId(0L);
                vo.setFranchiseId(0L);
                break;
        }
        if(deptIds != null && !deptIds.isEmpty()) {
            vo.setDeptIds(deptIds);
        }
        if(timeLimit > 0) {
            Date createTime = new Date();
            if(timeLimit == 1) createTime = DateUtils.addMonth(createTime, -2);
            if(timeLimit == 2) createTime = DateUtils.addMonth(createTime, -4);
            if(timeLimit == 3) createTime = DateUtils.addMonth(createTime, -6);
            vo.setCreateTime(createTime);
        }
        vo.setIsDesensitization(isDesensitization);
        return vo;
    }
}
