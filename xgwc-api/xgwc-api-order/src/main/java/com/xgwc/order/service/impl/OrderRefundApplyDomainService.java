package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.xgwc.activiti.feign.api.ExecutionFeign;
import com.xgwc.activiti.feign.entity.FlowExecutionVo;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.OrderStatusEnums;
import com.xgwc.common.util.AutoIdUtil;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.dao.OrderPayMapper;
import com.xgwc.order.dao.OrderRefundApplyMapper;
import com.xgwc.order.dao.XgwcPlatformRefundMapper;
import com.xgwc.order.entity.OrderRefundApply;
import com.xgwc.order.entity.OrderRefundCommision;
import com.xgwc.order.entity.OrderRefundPay;
import com.xgwc.order.entity.OrderRefundPayExt;
import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.dto.FranchiseStaffDto;
import com.xgwc.order.entity.dto.OrderDto;
import com.xgwc.order.entity.dto.OrderPayDto;
import com.xgwc.order.entity.dto.OrderRefundApplyApproveDto;
import com.xgwc.order.entity.dto.SubOrderDto;
import com.xgwc.order.entity.vo.AfterSalesFlowLogVo;
import com.xgwc.order.entity.vo.OrderRefundApplyCopyVo;
import com.xgwc.order.entity.vo.OrderRefundApplyVo;
import com.xgwc.order.service.IAfterSalesExecDataService;
import com.xgwc.order.service.IAfterSalesFlowLogService;
import com.xgwc.order.service.IOrderService;
import com.xgwc.order.util.OrderUtils;
import com.xgwc.settlement.feign.api.SettlementFeign;
import com.xgwc.user.feign.api.BrandOwnerFeign;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.api.StaffFeign;
import com.xgwc.user.feign.entity.BrandOwnerDto;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class OrderRefundApplyDomainService {

    @Resource
    private OrderRefundApplyMapper orderRefundApplyMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderPayMapper orderPayMapper;
    @Resource
    private ExecutionFeign executionFeign;
    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private BrandOwnerFeign brandOwnerFeign;
    @Resource
    private StaffFeign staffFeign;
    @Resource
    private IOrderService orderService;
    @Resource
    private XgwcPlatformRefundMapper xgwcPlatformRefundMapper;
    @Resource
    private SettlementFeign settlementFeign;
    @Resource
    private IAfterSalesExecDataService afterSalesExecDataService;
    @Resource
    private IAfterSalesFlowLogService afterSalesFlowLogService;


    public ApiResult insertOrderRefundApply(OrderRefundApplyVo orderRefundApplyVo, Long userId, String userName) {
        String applyStatus = "AFTER_SALES_ING";
        String orderNo = orderRefundApplyVo.getOrderNo();
        OrderDto orderDto = orderMapper.selectOrderByOrderNo(orderNo);
        if (orderDto == null) {
            return ApiResult.error("订单不存在");
        }
        OrderRefundApply orderRefundApply = orderRefundApplyMapper.getOrderRefundApplyByOrderNo(orderNo, orderDto.getBrandId());
        List<OrderRefundApplyApproveDto> approveDtoList = orderRefundApplyMapper.findByOrderNo(orderNo);
        if (orderRefundApply != null && orderRefundApply.getAfterCheckStatus() == 0) {
            return ApiResult.error("订单正在售后中，不能申请退款");
        }
        if (orderDto.getPid() != null && orderDto.getPid() != 0) {
            return ApiResult.error("非母订单不能申请退款");
        }
        if (orderDto.getIsAfterSale() == 1) {
            return ApiResult.error("订单正在售后中，不能申请退款");
        }
        if(!approveDtoList.isEmpty()){
            boolean hasAdvanceRefund = approveDtoList.stream().anyMatch(x -> x.getRefundType() == 3);
            if (hasAdvanceRefund) {
                return ApiResult.error("该订单已完成退垫付，不可再次申请");
            }
        }
        orderRefundApplyVo.setBrandId(orderDto.getBrandId());
        //支付记录
        List<OrderPayDto> orderPays = orderPayMapper.selectOrderPayByOrderId(orderDto.getId());
        //关联订单记录，用于获取设计师的佣金
        List<SubOrderDto> subOrders = orderMapper.selectSubOrderList(orderDto.getId());
        if (orderDto.getDesignerId() != null)
            subOrders.add(0, BeanUtil.copyProperties(orderDto, SubOrderDto.class));
        Long id = AutoIdUtil.getId();
        orderRefundApplyVo.setId(id);
        Object orderRefundPayListObject = this.buildOrderRefundPayList(orderPays, orderRefundApplyVo, orderDto);
        List<OrderRefundPay> orderRefundPayList = new ArrayList<>();
        List<OrderRefundCommision> orderRefundCommisionList = new ArrayList<>();
        if (orderRefundPayListObject instanceof String) {
            return ApiResult.error((String) orderRefundPayListObject);
        } else if (orderRefundPayListObject instanceof List) {
            orderRefundPayList = (List<OrderRefundPay>) orderRefundPayListObject;
        }
        Object orderRefundCommisionListObject = this.buildOrderRefundComission(subOrders, orderRefundApplyVo, userName);
        if (orderRefundCommisionListObject instanceof String) {
            return ApiResult.error((String) orderRefundCommisionListObject);
        } else if (orderRefundCommisionListObject instanceof List) {
            orderRefundCommisionList = (List<OrderRefundCommision>) orderRefundCommisionListObject;
        }
        //如果是退垫付
        if (orderRefundApplyVo.getRefundType() == 3) {
            Object advanceRefundObject = handleAdvanceRefund(orderRefundApplyVo);
            if (advanceRefundObject instanceof String) {
                return ApiResult.error((String) advanceRefundObject);
            } else if (advanceRefundObject instanceof OrderRefundPay orderRefundPay) {
                orderRefundPayList.add(orderRefundPay);
            }
            // 启动工作流
            OrderRefundApplyCopyVo orderRefundApplyCopyVo = BeanUtil.copyProperties(orderRefundApplyVo, OrderRefundApplyCopyVo.class);
            ApiResult<Long> apiResult = startFlow(orderRefundApplyCopyVo, userId, userName);
            if (apiResult.getStatus() != 1) {
                return ApiResult.error(apiResult.getMessage());
            }
            Long executionId = apiResult.getData();
            settlementFeign.handleMainOrder(orderDto.getId());
            orderRefundApplyVo.setExecutionId(executionId);
            applyStatus = "ING";
            // 更新订单状态
            orderService.updateOrderStatus(orderDto.getId(), OrderStatusEnums.AFTER_SALE_START);
        }
        orderRefundApplyVo.setApplyUserId(userId);
        orderRefundApplyVo.setApplyUserName(userName);
        orderRefundApplyVo.setPreId(orderDto.getId());
        orderRefundApplyVo.setApplyStatus(applyStatus);
        List<String> refundOrderNoList = orderRefundPayList.stream().filter(x -> OrderUtils.isPlatformPayByChannel(x.getPayChannel()) && x.getRefundAmount().compareTo(BigDecimal.ZERO) > 0).map(OrderRefundPay::getCollectionNo).distinct().toList();
        if (!refundOrderNoList.isEmpty()) {
            orderRefundApplyVo.setPlatformRefundTime(xgwcPlatformRefundMapper.listByTradeNo(refundOrderNoList, orderDto.getBrandId()).stream().map(XgwcPlatformRefund::getRefundTime).min(Date::compareTo).orElse(null));
        }
        orderRefundApplyMapper.insertOrderRefundApply(orderRefundApplyVo);
        orderRefundApplyMapper.batchInsertOrderRefundPayList(orderRefundPayList);
        if (!orderRefundCommisionList.isEmpty()) {
            orderRefundApplyMapper.batchInsertOrderRefundCommisionList(orderRefundCommisionList);
        }
        afterSalesExecDataService.insertAfterSalesExecData(orderRefundApplyVo);
        addAfterSalesFlowLog(orderRefundApplyVo);
        return ApiResult.ok();
    }

    private void addAfterSalesFlowLog(OrderRefundApplyVo orderRefundApplyVo) {
        AfterSalesFlowLogVo afterSalesFlowLogVo = new AfterSalesFlowLogVo();
        afterSalesFlowLogVo.setBusinessId(orderRefundApplyVo.getId());
        afterSalesFlowLogVo.setNodeType(1);
        afterSalesFlowLogVo.setOperatorId(orderRefundApplyVo.getApplyUserId());
        afterSalesFlowLogVo.setOperatorName(orderRefundApplyVo.getApplyUserName());
        afterSalesFlowLogVo.setStatus(0);
        afterSalesFlowLogVo.setSortOrder(0);
        afterSalesFlowLogService.insertAfterSalesFlowLog(afterSalesFlowLogVo);

    }

    // 启动工作流
    private ApiResult<Long> startFlow(OrderRefundApplyCopyVo vo, Long userId, String userName) {
        FlowExecutionVo flowExecutionVo = new FlowExecutionVo();
        flowExecutionVo.setTitle(vo.getOrderNo());
        flowExecutionVo.setFlowValue("AfterSales");
        flowExecutionVo.setBusinessKey(vo.getId());
        flowExecutionVo.setBrandId(vo.getBrandId());
        flowExecutionVo.setCreateBy(userId);
        flowExecutionVo.setCreateName(userName);
        ApiResult<BrandOwnerDto> result = brandOwnerFeign.getBrandOwnerById(vo.getBrandId());
        if (result.getStatus() == 1 && result.getData() != null) {
            BrandOwnerDto data = result.getData();
            flowExecutionVo.setBrandName(data.getCompanyName());
        }
        flowExecutionVo.setFranchiseId(vo.getFranchiseId());
        ApiResult<FranchiseDto> franchiseResult = franchiseFeign.getFranchiseById(vo.getFranchiseId());
        if (franchiseResult.getStatus() == 1 && franchiseResult.getData() != null) {
            FranchiseDto franchiseData = franchiseResult.getData();
            flowExecutionVo.setFranchiseName(franchiseData.getFranchiseName());
        }
        ApiResult staffResult = staffFeign.getFranchiseStaffInfoByUserIdAndFranchiseId(userId, vo.getFranchiseId());
        if (staffResult.getStatus() == 1 && staffResult.getData() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            FranchiseStaffDto franchiseStaffDto = objectMapper.convertValue(staffResult.getData(), FranchiseStaffDto.class);
            if (franchiseStaffDto != null && franchiseStaffDto.getDeptId() != null) {
                flowExecutionVo.setDeptId(franchiseStaffDto.getDeptId());
                flowExecutionVo.setDeptName(franchiseStaffDto.getDeptName());
            }
        }
        vo.setIsAdvance(1);
        List<OrderRefundPay> orderRefundPayList = JSON.parseArray(vo.getRefundPayJson(), OrderRefundPay.class);
        BigDecimal refundAmount = BigDecimal.ZERO;
        if (!orderRefundPayList.isEmpty()) {
            for (OrderRefundPay orderRefundPay : orderRefundPayList) {
                refundAmount = refundAmount.add(orderRefundPay.getRefundAmount());
            }
            vo.setLastAmount(refundAmount);
        }
        flowExecutionVo.setVariable(vo);
        return executionFeign.insertFlowExecution(flowExecutionVo);
    }

    /**
     * 处理退垫付
     */
    private Object handleAdvanceRefund(OrderRefundApplyVo orderRefundApplyVo) {
        String advanceRefundJson = orderRefundApplyVo.getRefundAdvanceJson();
        if (StringUtils.isNotEmpty(advanceRefundJson)) {
            JSONObject jsonObject = JSON.parseObject(advanceRefundJson);
            OrderRefundPay orderRefundPay = new OrderRefundPay();
            Integer payChannel = jsonObject.getInteger("payChannel");
            BigDecimal amount = jsonObject.getBigDecimal("amount");
            String collectionNo = jsonObject.getString("collectionNo");
            String payImg = jsonObject.getString("payImg");
            if (payChannel == null || amount == null && (StringUtils.isEmpty(collectionNo) || StringUtils.isEmpty(payImg))) {
                return "退垫付缺少到账信息";
            }
            orderRefundPay.setPayType(1);
            orderRefundPay.setPayChannel(payChannel);
            orderRefundPay.setAmount(amount);
            orderRefundPay.setCollectionNo(collectionNo);
            orderRefundPay.setPayImg(payImg);
            return orderRefundPay;
        } else {
            return "退垫付缺少到账信息";
        }
    }

    /**
     * 处理付款记录
     */
    private Object buildOrderRefundPayList(List<OrderPayDto> orderPays, OrderRefundApplyVo orderRefundApplyVo, OrderDto orderDto) {
        BigDecimal resultAmount = new BigDecimal(0);
        if (orderPays == null || orderPays.isEmpty()) {
            return "该订单没有查询到支付记录";
        }
        List<OrderRefundPay> orderRefundPayCmdList = JSON.parseArray(orderRefundApplyVo.getRefundPayJson(), OrderRefundPay.class);
        if (orderRefundPayCmdList == null || orderRefundPayCmdList.isEmpty()) {
            return "退款参数不能为空";
        }
        Set<Long> payIdSet = orderPays.stream().map(OrderPayDto::getId).collect(Collectors.toSet());
        List<Long> cmdIdList = orderRefundPayCmdList.stream().map(OrderRefundPay::getPreId).filter(Objects::nonNull).distinct().toList();
        if (cmdIdList.size() != orderRefundPayCmdList.size() || cmdIdList.stream().anyMatch(x -> !payIdSet.contains(x))) {
            return "存在异常付款ID数据";
        }
        List<String> platformPayOrderNo = orderPays.stream().filter(x -> OrderUtils.isPlatformPayByChannel(x.getPayChannel())).map(OrderPayDto::getCollectionNo).toList();
        Map<String, List<XgwcPlatformRefund>> platformRefundListMap = Maps.newHashMap();
        if (!platformPayOrderNo.isEmpty()) {
            platformRefundListMap = xgwcPlatformRefundMapper.listByTradeNo(platformPayOrderNo, orderDto.getBrandId()).stream().collect(Collectors.groupingBy(XgwcPlatformRefund::getTradeNo));
        }
        Map<Long, OrderPayDto> orderPayMap = orderPays.stream().collect(Collectors.toMap(OrderPayDto::getId, dto -> dto));
        for (OrderRefundPay orderRefundPay : orderRefundPayCmdList) {
            OrderPayDto orderPayDto = orderPayMap.get(orderRefundPay.getPreId());
            if (orderPayDto == null) {
                return "付款ID匹配失败";
            }
            orderRefundPay.setRefundAmount(orderRefundPay.getRefundAmount() == null ? BigDecimal.ZERO : orderRefundPay.getRefundAmount());
            if (orderRefundPay.getRefundAmount().compareTo(orderPayDto.getRefundableAmount()) > 0) {
                return String.format("退款金额%s不能大于可退金额%s", orderRefundPay.getRefundAmount(), orderPayDto.getRefundableAmount());
            }
            // 平台收款想退款时
            if (OrderUtils.isPlatformPayByChannel(orderPayDto.getPayChannel()) && orderRefundPay.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
                List<XgwcPlatformRefund> platformRefundList = platformRefundListMap.get(orderPayDto.getCollectionNo());
                if (platformRefundList == null || platformRefundList.isEmpty()) {
                    return orderPayDto.getCollectionNo() + "没有平台售后单, 暂不允许退款";
                }
                if (orderRefundPay.getRefundAmount().compareTo(platformRefundList.stream().map(XgwcPlatformRefund::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add)) > 0) {
                    return String.format("退款金额%s不能大于平台售后退款金额%s", orderRefundPay.getRefundAmount(), orderPayDto.getRefundableAmount());
                }
            }
            OrderRefundPayExt orderRefundPayExt = convertToOrderRefundPayExt(orderRefundPay.getRefundConfig());
            //重新赋值，保证数据准确性
            orderRefundPay.setOrderNo(orderRefundApplyVo.getOrderNo());
            orderRefundPay.setApplyId(orderRefundApplyVo.getId());
            orderRefundPay.setAmount(orderPayDto.getNowAmount());
            orderRefundPay.setPayChannel(orderPayDto.getPayChannel());
            orderRefundPay.setPayType(orderPayDto.getPayType());
            orderRefundPay.setCollectionNo(orderPayDto.getCollectionNo());
            orderRefundPay.setPayImg(orderPayDto.getPayImg());
            int compareAmount = orderRefundPay.getRefundAmount().compareTo(orderRefundPay.getAmount());
            if (compareAmount > 0) {
                return "付款项中退款金额不能大于支付金额";
            }
            //如果是全额退款和退垫付
            if (orderRefundApplyVo.getRefundType() == 1 || orderRefundApplyVo.getRefundType() == 3) {
                //退款金额和实付金额一致
                orderRefundPay.setRefundAmount(orderPayDto.getNowAmount());
            }
            //如果原有支付方式是淘宝
            if (orderPayDto.getPayChannel() == 1) {
                if (orderRefundPay.getRefundPayChannel() == 4) {
                    return "淘宝不能转对公";
                }
                //淘宝转线下
                if (orderRefundPay.getRefundPayChannel() == 2 || orderRefundPay.getRefundPayChannel() == 3) {
                    if (orderRefundPayExt.getRate() == null) {
                        return "淘宝转线下需要填写费率";
                    }
                }
            }
            if (orderPayDto.getPayChannel() == 2 || orderPayDto.getPayChannel() == 3) {
                if (orderRefundPay.getRefundPayChannel() == 1 || orderRefundPay.getRefundPayChannel() == 4) {
                    return "线下收款不能转淘宝或者对公";
                }
            }
            if (orderPayDto.getPayChannel() == 4) {
                if (orderRefundPay.getRefundPayChannel() == 1) {
                    return "对公不能转淘宝";
                }
                //对公转线下
                if (orderRefundPay.getRefundPayChannel() == 2 || orderRefundPay.getRefundPayChannel() == 3) {
                    if (orderRefundPayExt.getRate() == null) {
                        return "对公转线下需要填写费率";
                    }
                }
            }
            if (orderRefundPay.getRefundPayChannel() == 1 && orderRefundPayExt.getOpenQianniu() == null) {
                return "退款方式为淘宝时请选择是否开启千牛售后通道";
            }
            if ((orderRefundPay.getRefundPayChannel() == 2 || orderRefundPay.getRefundPayChannel() == 3) && StringUtils.isEmpty(orderRefundPayExt.getReceivePayImg())) {
                return "退款方式为线下时请上传付款码";
            }
            if (orderRefundPay.getRefundPayChannel() == 4 && (StringUtils.isEmpty(orderRefundPayExt.getAccountBank())
                    || StringUtils.isEmpty(orderRefundPayExt.getAccountName()) || StringUtils.isEmpty(orderRefundPayExt.getCorporateAccount()))) {
                return "退款方式为对公时请填写开户名/开户行/对公账号";
            }
            resultAmount = resultAmount.add(orderRefundPay.getRefundAmount());
        }
        int compareAmount = resultAmount.compareTo(orderDto.getNowAmount());
        if (compareAmount > 0) {
            return "退款金额不能大于实收金额";
        }
        if (orderRefundApplyVo.getRefundType() == 1) {
            if (compareAmount != 0) {
                return "全额退款金额不等于实收金额";
            }
        }
        orderRefundApplyVo.setPreAmount(orderDto.getNowAmount());
        //最初金额-退款金额 = 剩余金额
        orderRefundApplyVo.setLastAmount(orderDto.getNowAmount().subtract(resultAmount));
        return orderRefundPayCmdList;
    }

    /**
     * 处理佣金
     */
    private Object buildOrderRefundComission(List<SubOrderDto> subOrders, OrderRefundApplyVo orderRefundApplyVo, String userName) {
        //原来总佣金
        BigDecimal preCommissionAmount = new BigDecimal(0);
        //最后总佣金
        BigDecimal lastCommissionAmount = new BigDecimal(0);
        if (subOrders != null && !subOrders.isEmpty()) {
            Map<Long, SubOrderDto> subOrderDtoMap = subOrders.stream().collect(Collectors.toMap(SubOrderDto::getId, dto -> dto, (first, second) -> first));
            String comissionJson = orderRefundApplyVo.getRefundCommisionJson();
            if (StringUtils.isNotEmpty(comissionJson)) {
                List<OrderRefundCommision> refundCommisionList = JSON.parseArray(comissionJson, OrderRefundCommision.class);
                if (refundCommisionList != null && !refundCommisionList.isEmpty() && refundCommisionList.size() == subOrderDtoMap.size()) {
                    for (OrderRefundCommision orderRefundCommision : refundCommisionList) {
                        SubOrderDto subOrderDto = subOrderDtoMap.get(orderRefundCommision.getPreId());
                        if (subOrderDto != null) {
                            //重新赋值
                            orderRefundCommision.setSubOrderNo(subOrderDto.getOrderNo());
                            orderRefundCommision.setApplyId(orderRefundApplyVo.getId());
                            orderRefundCommision.setCommisionAmount(subOrderDto.getMoney());
                            orderRefundCommision.setDesignerUserId(subOrderDto.getDesignerId());
                            orderRefundCommision.setDesignerUserName(subOrderDto.getDesignerName());
                            orderRefundCommision.setCreateBy(userName);
                            //默认赋值0
                            orderRefundCommision.setAfterCommisionAmount(orderRefundCommision.getAfterCommisionAmount() == null ? new BigDecimal(0) : orderRefundCommision.getAfterCommisionAmount());
                            preCommissionAmount = preCommissionAmount.add(subOrderDto.getMoney());
                            lastCommissionAmount = lastCommissionAmount.add(orderRefundCommision.getAfterCommisionAmount());
                        } else {
                            return "未匹配到子订单id";
                        }
                    }
                    //总佣金不能超过剩余订单金额的0.8
                    if (lastCommissionAmount.compareTo(orderRefundApplyVo.getLastAmount().multiply(new BigDecimal("0.8"))) > 0) {
                        return "总佣金不能超过" + orderRefundApplyVo.getLastAmount().multiply(new BigDecimal("0.8"));
                    }
                } else {
                    return "现有原设计师个数和原设计师个数不相等";
                }
                orderRefundApplyVo.setPreCommision(preCommissionAmount);
                orderRefundApplyVo.setLastCommision(lastCommissionAmount);
                return refundCommisionList;
            } else {
                return "佣金参数不能为空";
            }
        } else {
            return new ArrayList<OrderRefundCommision>();
        }
    }

    /**
     * 转换对象
     */
    private OrderRefundPayExt convertToOrderRefundPayExt(String payJson) {
        if (StringUtils.isNotEmpty(payJson)) {
            return JSON.parseObject(payJson, OrderRefundPayExt.class);
        }
        return new OrderRefundPayExt();
    }


}