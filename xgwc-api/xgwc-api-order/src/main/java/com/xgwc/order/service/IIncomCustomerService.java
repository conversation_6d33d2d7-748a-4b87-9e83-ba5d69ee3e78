package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.IncomCustomerVo;
import com.xgwc.order.entity.vo.IncomCustomerQueryVo;
import com.xgwc.order.entity.vo.IncomLineVo;

public interface IIncomCustomerService  {

    /**
     * 查询客户列表列表
     * 
     * @param incomCustomer 客户列表
     * @return 客户列表集合
     */
    public List<IncomCustomerDto> selectIncomCustomerList(IncomCustomerQueryVo incomCustomer);

    /**
     * 获取基本信息
     * @param taobaoId
     * @return
     */
    public IncomCustomerBasicDto basic(String taobaoId);

    /**
     * 获取购买历史
     * @param taobaoId
     * @return
     */
    public List<IncomCustomerStatisDto>  statis(String taobaoId);


    /**
     * 跟进历史
     * @param taobaoId
     * @return
     */
    public List<IncomLineTarckDto> tarckHis(String taobaoId);

    /**
     * 聊天记录
     * @param taobaoId
     * @return
     */
    public List<IncomCustomerChatDto> chatHis(String taobaoId);

    /**
     * 新增客户列表
     * 
     * @return 结果
     */
    public int insertIncomCustomer(String type, IncomLineVo dto);

    /**
     * 修改客户列表
     * 
     * @param incomCustomer 客户列表
     * @return 结果
     */
    public int updateIncomCustomer(IncomCustomerVo incomCustomer);

}
