package com.xgwc.order.service;

import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.DesignerStatisticsQueryVo;
import com.xgwc.order.entity.vo.DesignerStatisticsScoreQueryVo;
import com.xgwc.order.entity.vo.DesignerTarckVo;

import java.util.List;

public interface IDesignerStatisticsService {

    List<DesignerStatisticsDto> list(DesignerStatisticsQueryVo queryVo);

    DesignerStatisticsDto selectById(Long designerId);

    List<DesignerTarckDto> tarckList(Long designerId);

    int tarckAdd(DesignerTarckVo designerTarckVo);

    List<OrderDto> noArchive(Long designerId);

    List<OrderDto> refund(Long designerId);

    DesignerStatisticsScoreAllDto scoreAll(DesignerStatisticsScoreQueryVo queryVo);

    List<DesignerStatisticsOrderDto> scoreList(DesignerStatisticsScoreQueryVo queryVo);

}
