package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ChatRecordStatusVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @NotNull(message = "id不能为空")
    private Long id;

    @FieldDesc("状态 0-启用 1-禁用")
    @NotNull(message = "状态不能为空")
    private Integer status;



}
