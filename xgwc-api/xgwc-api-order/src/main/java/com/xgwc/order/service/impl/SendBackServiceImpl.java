package com.xgwc.order.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.util.SecurityUtils;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.SendBackMapper;
import com.xgwc.order.service.ISendBackService;
import com.xgwc.order.entity.SendBack;
import com.xgwc.order.entity.vo.SendBackVo;
import com.xgwc.order.entity.dto.SendBackDto;
import com.xgwc.order.entity.vo.SendBackQueryVo;


@Service
public class SendBackServiceImpl implements ISendBackService  {
    @Resource
    private SendBackMapper sendBackMapper;

    /**
     * 查询退回记录
     * 
     * @param id 退回记录主键
     * @return 退回记录
     */
    @Override
    public SendBackDto selectSendBackById(Long id) {
        return sendBackMapper.selectSendBackById(id);
    }

    /**
     * 查询退回记录列表
     * 
     * @param sendBack 退回记录
     * @return 退回记录
     */
    @Override
    public List<SendBackDto> selectSendBackList(SendBackQueryVo sendBack) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null){
            return null;
        }
        sendBack.setBrandId(brandId);
        return sendBackMapper.selectSendBackList(sendBack);
    }

    /**
     * 新增退回记录
     * 
     * @param dto 退回记录
     * @return 结果
     */
    @Override
    public int insertSendBack(SendBackVo dto) {

        SendBack sendBack = BeanUtil.copyProperties(dto, SendBack.class);
        sendBack.setCreateTime(DateUtils.getNowDate());
        return sendBackMapper.insertSendBack(sendBack);
    }

    /**
     * 修改退回记录
     * 
     * @param dto 退回记录
     * @return 结果
     */
    @Override
    public int updateSendBack(SendBackVo dto) {

        SendBack sendBack = BeanUtil.copyProperties(dto, SendBack.class);
        sendBack.setUpdateTime(DateUtils.getNowDate());
        return sendBackMapper.updateSendBack(sendBack);
    }

    /**
     * 批量删除退回记录
     * 
     * @param ids 需要删除的退回记录主键
     * @return 结果
     */
    @Override
    public int deleteSendBackByIds(Long[] ids) {
        return sendBackMapper.deleteSendBackByIds(ids);
    }

    /**
     * 删除退回记录信息
     * 
     * @param id 退回记录主键
     * @return 结果
     */
    @Override
    public int deleteSendBackById(Long id) {
        return sendBackMapper.deleteSendBackById(id);
    }

    @Override
    public List<SendBackDto> selectSendBackListByDispatchId(Long dispatchId) {
        return sendBackMapper.findSendBackListByDispatchId(dispatchId);
    }
}
