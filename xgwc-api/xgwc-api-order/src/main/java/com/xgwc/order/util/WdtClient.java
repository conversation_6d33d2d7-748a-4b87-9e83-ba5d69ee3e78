package com.xgwc.order.util;

import com.xgwc.order.entity.XgwcPlatformConfig;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

public class WdtClient {

    private final String sid;
    private final String appKey;
    private final String appSecret;

    private final String qimenAppKey;
    private final String qimenAppSecret;

    private final int connectTimeout = 10000;//3秒
    private final int readTimeout = 5000;//15秒

    public WdtClient(XgwcPlatformConfig config) {
        this.sid = config.getSid();

        this.appKey = config.getAppKey();
        this.appSecret = config.getAppSecret();

        this.qimenAppKey = config.getQimenAppKey();
        this.qimenAppSecret = config.getQimenAppSecret();
    }

    /**
     * 旺店通
     */
    public String execute(String relativeUrl, Map<String, String> params) throws IOException {
        params.put("appkey", this.appKey);
        params.put("sid", this.sid);
        params.put("timestamp", Long.toString(System.currentTimeMillis() / 1000));
        params.put("sign", signRequest(params, this.appSecret));
        return WebUtils.doPost("https://openapi.huice.com/openapi/" + relativeUrl, params, "UTF-8", connectTimeout, readTimeout, null);
    }


    private static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (byte aByte : bytes) {
            String hex = Integer.toHexString(aByte & 0xFF);
            if (hex.length() == 1) {
                //保证所有的16进制都是两位：00-ff，其中[80~ff]代表[-128,-1]
                sign.append("0");
            }
            sign.append(hex);
        }
        return sign.toString();
    }

    /**
     * 给TOP请求签名。
     *
     * @return 签名
     */
    public static String signRequest(Map<String, String> params, String appsecret) throws IOException {
        // 第一步：检查参数是否已经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        for (String key : keys) {
            if ("sign".equals(key)) continue;
            String value = params.get(key);
            if (com.xgwc.common.util.StringUtils.isEmpty(value)) {
                continue;
            }
            if (!query.isEmpty()) {
                query.append(';');
            }
            int len = key.length();
            query.append(String.format("%02d", len)).append('-').append(key).append(':');
            len = value.length();
            query.append(String.format("%04d", len)).append('-').append(value);
        }
        query.append(appsecret);
        // 第三步：使用MD5加密
        byte[] bytes = encryptMD5(query.toString());
        // 第四步：把二进制转化为大写的十六进制
        return byte2hex(bytes);
    }

    /**
     * 奇门
     */
    public String executeQimen(Map<String, String> params) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        params.put("sid", sid);
        params.put("app_key", qimenAppKey);
        params.put("timestamp", sdf.format(new Date()));
        params.put("format", "json");
        params.put("sign_method", "md5");
        params.put("target_app_key", "21363512");
        params.put("v", "2.0");
        params.put("sign", signTopRequestQimen(params, qimenAppSecret));
        return WebUtils.doPost("https://4edcetl6p0.api.taobao.com/router/qm", params, "UTF-8", connectTimeout, readTimeout, null);
    }

    public static String signTopRequestQimen(Map<String, String> params, String secret) throws IOException {
        // 第一步：检查参数是否已经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        query.append(secret);
        for (String key : keys) {
            if ("sign".equals(key)) {
                continue;
            }
            String value = params.get(key);
            if (StringUtils.areNotEmpty(key, value)) {
                query.append(key).append(value);
            }
        }

        // 第三步：使用MD5/HMAC加密
        byte[] bytes;
        query.append(secret);
        bytes = encryptMD5(query.toString());

        // 第四步：把二进制转化为大写的十六进制(正确签名应该为32大写字符串，此方法需要时使用)
        return byte2hexQimen(bytes);
    }

    public static byte[] encryptMD5(String data) throws IOException {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            return md.digest(data.getBytes(StandardCharsets.UTF_8));
        } catch (GeneralSecurityException gse) {
            throw new IOException(gse.toString());
        }
    }

    public static String byte2hexQimen(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (byte aByte : bytes) {
            String hex = Integer.toHexString(aByte & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

}
