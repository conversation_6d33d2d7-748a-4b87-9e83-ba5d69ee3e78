package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class XgwcPlatformGoodsDto {

    private Long id;

    @FieldDesc("旺店通平台货品id")
    @Excel(name = "旺店通平台货品id")
    private String goodsId;

    @FieldDesc("商品名称")
    @Excel(name = "商品名称")
    private String goodsName;

    @FieldDesc("平台id")
    @Excel(name = "平台id")
    private Long platformId;

    @FieldDesc("平台名称")
    @Excel(name = "平台名称")
    private String platformName;

    @FieldDesc("平台店铺id")
    @Excel(name = "平台店铺id")
    private String platformShopId;

    @FieldDesc("平台店铺名称")
    @Excel(name = "平台店铺名称")
    private String platformShopName;

    @FieldDesc("店铺id")
    @Excel(name = "店铺id")
    private Long shopId;

    @FieldDesc("店铺名称")
    @Excel(name = "店铺名称")
    private String shopName;

    @FieldDesc("品牌商id")
    @Excel(name = "品牌商id")
    private Long brandId;

    @FieldDesc("业务分类id")
    @Excel(name = "业务分类id")
    private Long bizType;
    @FieldDesc("业务分类")
    @Excel(name = "业务分类")
    private String bizTypeName;

    @FieldDesc("0删除1在架2下架")
    @Excel(name = "0删除1在架2下架")
    private Long status;
    @FieldDesc("状态")
    @Excel(name = "状态")
    private String statusName;

    @FieldDesc("是否删除：0正常，1删除")
    @Excel(name = "是否删除：0正常，1删除")
    private Long isDel;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;


    public void initStatusName() {
        this.statusName = this.status.intValue() == 0 ? "已删除" : (this.status.intValue() == 1 ? "上架" : "未上架");
    }

}
