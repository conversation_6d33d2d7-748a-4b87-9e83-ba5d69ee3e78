package com.xgwc.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.entity.es.CaseIndex;
import com.xgwc.order.entity.es.CaseVo;
import com.xgwc.order.entity.es.ChatIndex;
import com.xgwc.order.entity.es.ChatVo;
import com.xgwc.order.esmapper.CaseIndexMapper;
import com.xgwc.order.esmapper.ChatIndexMapper;
import com.xgwc.order.service.EsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EsServiceImpl implements EsService {

    @Resource
    private CaseIndexMapper caseIndexMapper;

    @Resource
    private ChatIndexMapper chatIndexMapper;

    @Override
    public JSONObject searchCaseData(CaseVo caseVo) {
        EsPageInfo<CaseIndex> pageInfo = searchCasesWithPage(caseVo);
        // 获取分页数据
        List<CaseIndex> cases = pageInfo.getList();
        JSONObject resultJson = new JSONObject();
        resultJson.put("total", pageInfo.getTotal());
        resultJson.put("rows", cases);
        return resultJson;
    }

    @Override
    public JSONObject searchChatData(ChatVo chatVo) {
        EsPageInfo<ChatIndex> pageInfo = searchChatWithPage(chatVo);
        // 获取分页数据
        List<ChatIndex> cases = pageInfo.getList();
        JSONObject resultJson = new JSONObject();
        resultJson.put("total", pageInfo.getTotal());
        resultJson.put("rows", cases);
        return resultJson;
    }

    @Override
    public boolean addCaseData(CaseIndex caseIndex) {
        if(caseIndex.getCaseId() == null){
            return false;
        }
        LambdaEsQueryWrapper<CaseIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.eq(CaseIndex::getCaseId, caseIndex.getCaseId());
        CaseIndex existing = caseIndexMapper.selectOne(queryWrapper);
        if (existing != null) {
            // 保留原ID（避免主键冲突）
            caseIndex.setId(existing.getId());
            int count = caseIndexMapper.updateById(caseIndex);
            log.info("更新案列数据:{}, 更新结果:{}", JSONObject.toJSONString(caseIndex), count > 0);
        }else{
            caseIndexMapper.insert(caseIndex);
        }
        return true;
    }

    @Override
    public boolean addChatData(ChatIndex chatIndex) {
        if(chatIndex.getChatId() == null){
            return false;
        }
        LambdaEsQueryWrapper<ChatIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.eq(ChatIndex::getChatId, chatIndex.getChatId());
        ChatIndex existing = chatIndexMapper.selectOne(queryWrapper);
        if (existing != null) {
            // 保留原ID（避免主键冲突）
            chatIndex.setId(existing.getId());
            int count = chatIndexMapper.updateById(chatIndex);
            log.info("更新聊天数据:{}, 更新结果:{}", JSONObject.toJSONString(chatIndex), count > 0);
        }else{
            chatIndexMapper.insert(chatIndex);
        }
        return true;
    }

    @Override
    public boolean deleteCaseData(CaseIndex caseIndex) {
        if(caseIndex.getCaseId() == null){
            return false;
        }
        LambdaEsQueryWrapper<CaseIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.eq(CaseIndex::getCaseId, caseIndex.getCaseId());
        CaseIndex existing = caseIndexMapper.selectOne(queryWrapper);
        if (existing != null) {
            int count = caseIndexMapper.deleteById(existing.getId());
            log.info("删除案列数据:{}, 更新结果:{}", JSONObject.toJSONString(caseIndex), count > 0);
        }
        return true;
    }

    @Override
    public boolean deleteChatData(ChatIndex chatIndex) {
        if(chatIndex.getChatId() == null){
            return false;
        }
        LambdaEsQueryWrapper<ChatIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.eq(ChatIndex::getChatId, chatIndex.getChatId());
        ChatIndex existing = chatIndexMapper.selectOne(queryWrapper);
        if (existing != null) {
            int count = chatIndexMapper.deleteById(existing.getId());
            log.info("删除聊天数据:{}, 更新结果:{}", JSONObject.toJSONString(chatIndex), count > 0);
        }
        return true;
    }

    @Override
    public boolean updateChatData(ChatIndex chatIndex) {
        if (chatIndex.getChatId() == null) {
            return false;
        }

        LambdaEsQueryWrapper<ChatIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.eq(ChatIndex::getChatId, chatIndex.getChatId());
        ChatIndex existing = chatIndexMapper.selectOne(queryWrapper);

        if (existing != null) {
            // 保留原ID（避免主键冲突）
            chatIndex.setId(existing.getId());
            int count = chatIndexMapper.updateById(chatIndex);
            log.info("更新聊天数据:{}, 更新结果:{}", JSONObject.toJSONString(chatIndex), count > 0);
            return count > 0;
        } else {
            log.info("未找到对应的聊天数据，无法更新: {}", JSONObject.toJSONString(chatIndex));
            return false;
        }
    }

    /**
     * 更新案例数据
     * @param caseIndex 数据
     * @return 更新结果
     */
    @Override
    public boolean updateCaseData(CaseIndex caseIndex) {
        if (caseIndex.getCaseId() == null) {
            return false;
        }

        LambdaEsQueryWrapper<CaseIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.eq(CaseIndex::getCaseId, caseIndex.getCaseId());
        CaseIndex existing = caseIndexMapper.selectOne(queryWrapper);

        if (existing != null) {
            // 保留原ID（避免主键冲突）
            caseIndex.setId(existing.getId());
            int count = caseIndexMapper.updateById(caseIndex);
            log.info("更新案例数据:{}, 更新结果:{}", JSONObject.toJSONString(caseIndex), count > 0);
            return count > 0;
        } else {
            log.info("未找到对应的案例数据，无法更新: {}", JSONObject.toJSONString(caseIndex));
            return false;
        }
    }

    private EsPageInfo<CaseIndex> searchCasesWithPage(CaseVo caseVo) {
        LambdaEsQueryWrapper<CaseIndex> wrapper = new LambdaEsQueryWrapper<>();
        // 1. AND 条件：其他字段
        if (StringUtils.isNotBlank(caseVo.getTitle())) {
            wrapper.match(CaseIndex::getTitle, caseVo.getTitle());  // 标题分词匹配
        }
        if (StringUtils.isNotBlank(caseVo.getKeyword())) {
            wrapper.match(CaseIndex::getKeyword, caseVo.getKeyword());  // 关键词分词匹配
        }
        if (caseVo.getBranchId() != null) {
            wrapper.eq(CaseIndex::getBrandId, caseVo.getBranchId());  // 精确匹配 branchId
        }
        if (caseVo.getFranchiseId() != null) {
            wrapper.eq(CaseIndex::getFranchiseId, caseVo.getFranchiseId());  // 精确匹配 franchiseId
        }

        // 2. OR 条件：typeCodes（匹配）
        if (StringUtils.isNotBlank(caseVo.getTypeCodes())) {
            String[] codes = caseVo.getTypeCodes().split(",");
            wrapper.and(w -> {
                for (String code : codes) {
                    w.or().match(CaseIndex::getTypeCodes, code.trim());
                }
            });
        }
        wrapper.orderByDesc(CaseIndex::getId);
        // 3. 执行分页查询
        return caseIndexMapper.pageQuery(wrapper, caseVo.getPageNum(), caseVo.getPageSize());
    }

    private EsPageInfo<ChatIndex> searchChatWithPage(ChatVo chatVo) {
        LambdaEsQueryWrapper<ChatIndex> wrapper = new LambdaEsQueryWrapper<>();
        // 1. AND 条件：其他字段
        if (StringUtils.isNotBlank(chatVo.getContent())) {
            wrapper.and(wq -> wq
                    .or(oq -> {
                        oq.match(ChatIndex::getTitle, chatVo.getContent());
                    })
                    .or(oq -> {
                        oq.match(ChatIndex::getChatContent, chatVo.getContent());
                    })
            );
        }
        if (chatVo.getBranchId() != null) {
            wrapper.eq(ChatIndex::getBrandId, chatVo.getBranchId());  // 精确匹配 branchId
        }
        if (chatVo.getFranchiseId() != null) {
            wrapper.eq(ChatIndex::getFranchiseId, chatVo.getFranchiseId());  // 精确匹配 franchiseId
        }
        // 2. OR 条件：typeCodes（匹配）
        if (StringUtils.isNotBlank(chatVo.getTypeCodes())) {
            String[] codes = chatVo.getTypeCodes().split(",");
            wrapper.and(w -> {
                for (String code : codes) {
                    w.or().match(ChatIndex::getTypeCodes, code.trim());
                }
            });
        }
        wrapper.orderByDesc(ChatIndex::getId);
        // 3. 执行分页查询
        return chatIndexMapper.pageQuery(wrapper, chatVo.getPageNum(), chatVo.getPageSize());
    }
}
