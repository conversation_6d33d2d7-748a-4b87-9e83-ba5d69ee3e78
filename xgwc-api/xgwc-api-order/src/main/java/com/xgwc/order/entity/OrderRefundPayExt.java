package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单退款支付情况
 */
@Data
public class OrderRefundPayExt {

    /**
     * 是否开启千牛
     */
    private Integer openQianniu;

    /**
     * 费率
     */
    private BigDecimal rate;

    /**
     * 收款码
     */
    private String receivePayImg;

    /**
     * 账号
     */
    private String accountName;

    /**
     * 开户行
     */
    private String accountBank;

    /**
     * 开户号
     */
    private String corporateAccount;



}
