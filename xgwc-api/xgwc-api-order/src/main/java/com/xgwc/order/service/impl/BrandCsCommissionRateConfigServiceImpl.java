package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.BizUtils;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.PageUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.BrandCsCommissionRateConfigMapper;
import com.xgwc.order.dao.CommonMapper;
import com.xgwc.order.dao.XgwcBusinessMapper;
import com.xgwc.order.entity.BrandCsCommissionRateConfig;
import com.xgwc.order.entity.cmd.BrandCsCommissionRateConfigCmd;
import com.xgwc.order.entity.cmd.BrandCsCommissionRateConfigUpdateCmd;
import com.xgwc.order.entity.dto.BrandCsCommissionRateConfigDto;
import com.xgwc.order.entity.dto.DeptDto;
import com.xgwc.order.entity.dto.StaffDto;
import com.xgwc.order.entity.vo.BrandCsCommissionRateConfigQueryVo;
import com.xgwc.order.entity.vo.XgwcBusinessTreeVo;
import com.xgwc.order.service.IBrandCsCommissionRateConfigService;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class BrandCsCommissionRateConfigServiceImpl implements IBrandCsCommissionRateConfigService {

    @Resource
    private CommonMapper commonMapper;
    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private XgwcBusinessMapper xgwcBusinessMapper;
    @Resource
    private BrandCsCommissionRateConfigMapper brandCsCommissionRateConfigMapper;

    /**
     * 新增客服提成完成率配置表
     *
     * @param cmd 客服提成完成率配置表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(BrandCsCommissionRateConfigCmd cmd) {
        BrandCsCommissionRateConfig baseConfig = this.checkCmdAndInitConfig(cmd);
        List<BrandCsCommissionRateConfig> configList = Lists.newArrayList();

        BrandCsCommissionRateConfigQueryVo queryVo = BrandCsCommissionRateConfigQueryVo.builder().brandId(SecurityUtils.getSysUser().getBrandId()).type(baseConfig.getType()).franchiseeIds(cmd.getFranchiseeIds()).businessIds(cmd.getBusinessIds()).build();
        if (baseConfig.isBaseType()) {
            cmd.getFranchiseeIds().forEach(franchiseeId -> cmd.getBusinessIds().forEach(businessId -> {
                BrandCsCommissionRateConfig config = BeanUtil.copyProperties(baseConfig, BrandCsCommissionRateConfig.class);
                config.setFranchiseeId(franchiseeId);
                config.setBusinessId(businessId);
                config.setDeptId(-1L);
                config.setStaffId(-1L);
                configList.add(config);
            }));
        } else if (baseConfig.isLeaderType()) {
            Long franchiseeId = cmd.getFranchiseeIds().get(0);
            cmd.getBusinessIds().forEach(businessId -> cmd.getDeptIds().forEach(deptId -> {
                BrandCsCommissionRateConfig config = BeanUtil.copyProperties(baseConfig, BrandCsCommissionRateConfig.class);
                config.setFranchiseeId(franchiseeId);
                config.setBusinessId(businessId);
                config.setDeptId(deptId);
                config.setStaffId(-1L);
                configList.add(config);
            }));
            queryVo.setDeptIds(cmd.getDeptIds());
        } else if (baseConfig.isSingleType()) {
            Long franchiseeId = cmd.getFranchiseeIds().get(0);
            cmd.getBusinessIds().forEach(businessId -> cmd.getStaffIds().forEach(staffId -> {
                BrandCsCommissionRateConfig config = BeanUtil.copyProperties(baseConfig, BrandCsCommissionRateConfig.class);
                config.setFranchiseeId(franchiseeId);
                config.setBusinessId(businessId);
                config.setDeptId(-1L);
                config.setStaffId(staffId);
                configList.add(config);
            }));
            queryVo.setStaffIds(cmd.getStaffIds());
        }
        List<BrandCsCommissionRateConfig> dbList = brandCsCommissionRateConfigMapper.listByQuery(queryVo);
        if (!dbList.isEmpty()) {
            // TODO 提示文案人性化
            throw new ApiException("xx 已配置, 不能重复");
        }
        return brandCsCommissionRateConfigMapper.insertList(configList);
    }

    private BrandCsCommissionRateConfig checkCmdAndInitConfig(BrandCsCommissionRateConfigCmd cmd) {
        BizUtils.assertByFlag(SecurityUtils.getSysUser().isBrandUser(), "非品牌商用户不能操作");
        Set<String> franchiseIds = StringUtils.stringToSet(SecurityUtils.getSysUser().getFranchiseIds());
        Set<String> cmdFranchiseIds = cmd.getFranchiseeIds().stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.toSet());
        BizUtils.assertByFlag(cmdFranchiseIds.size() == cmd.getFranchiseeIds().size() && franchiseIds.containsAll(cmdFranchiseIds), "存在异常加盟商id");

        BrandCsCommissionRateConfig config = BeanUtil.copyProperties(cmd, BrandCsCommissionRateConfig.class);
        if (config.isBaseType()) {

        } else if (config.isLeaderType()) {
            BizUtils.assertByFlag(cmd.getDeptIds() != null && !cmd.getDeptIds().isEmpty(), "岗位id不能为空");
        } else if (config.isSingleType()) {
            BizUtils.assertByFlag(cmd.getStaffIds() != null && !cmd.getStaffIds().isEmpty(), "员工id不能为空");
        } else {
            throw new ApiException("错误的配置类型:" + config.getType());
        }
        config.initRangeRateJson(cmd.getRangeList());
        Date now = new Date();
        if (config.getId() == null) {
            config.setStartTime(DateUtils.getStartOfMonth(now));
        }
        config.initEffectiveTime();
        config.setBrandId(SecurityUtils.getSysUser().getBrandId());
        config.setCreateById(SecurityUtils.getUserId());
        config.setCreateTime(now);
        config.setUpdateTime(config.getCreateTime());
        config.setUpdateById(config.getCreateById());
        return config;
    }

    /**
     * 查询客服提成完成率配置表列表
     *
     * @param queryVo 客服提成完成率配置表
     * @return 客服提成完成率配置表
     */
    @Override
    public ApiListResult<BrandCsCommissionRateConfigDto> listPage(BrandCsCommissionRateConfigQueryVo queryVo) {
        if (!SecurityUtils.getSysUser().isBrandUser()) {
            return ApiListResult.isOk();
        }
        queryVo.setBrandId(SecurityUtils.getSysUser().getBrandId());
        PageUtils.startPage();
        List<BrandCsCommissionRateConfig> list = brandCsCommissionRateConfigMapper.listByQuery(queryVo);
        return ApiListResult.isOk(new PageInfo<>(list).getTotal(), this.convertDto(list, queryVo.getBrandId()));
    }

    /**
     * 查询客服提成完成率配置表
     *
     * @param id 客服提成完成率配置表主键
     * @return 客服提成完成率配置表
     */
    @Override
    public BrandCsCommissionRateConfigDto get(Long id) {
        if (!SecurityUtils.getSysUser().isBrandUser()) {
            return null;
        }
        BrandCsCommissionRateConfig config = brandCsCommissionRateConfigMapper.getByIdBrandId(id, SecurityUtils.getSysUser().getBrandId());
        if (config == null) {
            return null;
        }
        return this.convertDto(ImmutableList.of(config), config.getBrandId()).get(0).initRangeList();
    }

    private List<BrandCsCommissionRateConfigDto> convertDto(List<BrandCsCommissionRateConfig> configList, Long brandId) {
        List<BrandCsCommissionRateConfigDto> dtoList = Lists.newArrayList();
        if (!configList.isEmpty()) {
            List<Long> franchiseIdList = configList.stream().map(BrandCsCommissionRateConfig::getFranchiseeId).distinct().toList();
            Map<Long, String> franchiseNameMap = franchiseFeign.listByIds(franchiseIdList).stream().collect(Collectors.toMap(FranchiseDto::getId, FranchiseDto::getFranchiseName));
            List<Long> businessIdList = configList.stream().map(BrandCsCommissionRateConfig::getBusinessId).distinct().toList();
            Map<Long, String> bizTypeMap = xgwcBusinessMapper.listByIds(brandId, businessIdList).stream().collect(Collectors.toMap(XgwcBusinessTreeVo::getBusinessId, XgwcBusinessTreeVo::getBusinessName));
            List<Long> userIdList = configList.stream().map(BrandCsCommissionRateConfig::getCreateById).distinct().toList();
            Map<Long, String> userNameMap = commonMapper.listBrandStaffByIds(userIdList).stream().collect(Collectors.toMap(StaffDto::getBindUserId, StaffDto::getName));

            Map<Long, String> deptNameMap = Maps.newHashMap();
            List<Long> deptIdList = configList.stream().map(BrandCsCommissionRateConfig::getDeptId).filter(x -> x != -1).distinct().toList();
            if (!deptIdList.isEmpty()) {
                deptNameMap.putAll(commonMapper.listFranchiseDeptByIds(deptIdList, null, null).stream().collect(Collectors.toMap(DeptDto::getDeptId, DeptDto::getDeptName)));
            }
            Map<Long, String> staffNameMap = Maps.newHashMap();
            List<Long> staffIdList = configList.stream().map(BrandCsCommissionRateConfig::getStaffId).filter(x -> x != -1).distinct().toList();
            if (!staffIdList.isEmpty()) {
                staffNameMap.putAll(commonMapper.listFranchiseStaffByIds(staffIdList, null, null).stream().collect(Collectors.toMap(StaffDto::getBindUserId, StaffDto::getName)));
            }
            configList.forEach(config -> {
                BrandCsCommissionRateConfigDto configDto = BeanUtil.copyProperties(config, BrandCsCommissionRateConfigDto.class);
                configDto.setFranchiseeName(franchiseNameMap.get(config.getFranchiseeId()));
                configDto.setBusinessName(bizTypeMap.get(config.getBusinessId()));
                configDto.setDeptName(deptNameMap.get(config.getDeptId()));
                configDto.setStaffName(staffNameMap.get(config.getStaffId()));
                configDto.setCreateByName(userNameMap.get(configDto.getCreateById()));
                dtoList.add(configDto);
            });
        }
        return dtoList;
    }

    /**
     * 修改客服提成完成率配置表
     *
     * @param cmd 客服提成完成率配置表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BrandCsCommissionRateConfigUpdateCmd cmd) {
        BizUtils.assertByFlag(cmd.getId() != null, "配置id不能为空");
        BizUtils.assertByFlag(SecurityUtils.getSysUser().isBrandUser(), "非品牌商用户不能操作");
        BrandCsCommissionRateConfig config = brandCsCommissionRateConfigMapper.getByIdBrandId(cmd.getId(), SecurityUtils.getSysUser().getBrandId());
        BizUtils.assertByFlag(config != null, "错误的配置id");
        BizUtils.copyPropertiesIgnoreNull(cmd, config);
        config.initEffectiveTime();
        config.initRangeRateJson(cmd.getRangeList());
        config.setUpdateTime(DateUtils.getNowDate());
        config.setUpdateById(SecurityUtils.getUserId());
        return brandCsCommissionRateConfigMapper.update(config);
    }

    /**
     * 批量删除客服提成完成率配置表
     *
     * @param ids 需要删除的客服提成完成率配置表主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int remove(Long[] ids) {
        return brandCsCommissionRateConfigMapper.remove(ids, SecurityUtils.getSysUser().getBrandId());
    }

}