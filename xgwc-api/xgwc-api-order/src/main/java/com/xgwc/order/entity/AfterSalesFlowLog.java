package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class AfterSalesFlowLog {

private static final long serialVersionUID=1L;

    /** 业务id */
    private Long businessId;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 主键 */
    private Long id;

    /** 节点类型（1:开始，2:售后审核） */
    private Long nodeType;

    /** 操作人ID */
    private Long operatorId;

    /** 操作人姓名 */
    private String operatorName;

    /** 备注 */
    private String remark;

    /** 顺序 */
    private Long sortOrder;

    /** 节点状态（0发起审批，1通过，2拒绝） */
    private Long status;

    /** 更新时间 */
    private Date updateTime;



}