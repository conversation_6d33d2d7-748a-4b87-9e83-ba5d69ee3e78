package com.xgwc.order.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.order.entity.dto.SendBackDto;
import com.xgwc.order.entity.vo.SendBackQueryVo;
import com.xgwc.order.service.ISendBackService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/sendBack")
public class SendBackController extends BaseController {
    @Autowired
    private ISendBackService sendBackService;

    /**
     * 查询退回记录列表
     */
    @MethodDesc("查询退回记录列表")
    @PreAuthorize("@ss.hasPermission('sendBack:sendBack:list')")
    @GetMapping("/list")
    public ApiResult<SendBackDto> list(SendBackQueryVo sendBack) {
        startPage();
        List<SendBackDto> list = sendBackService.selectSendBackList(sendBack);
        return getDataTable(list);
    }

    /**
     * 根据派单id查询退回记录列表
     */
    @MethodDesc("根据派单id查询退回记录")
    @PreAuthorize("@ss.hasPermission('sendBack:sendBack:list')")
    @GetMapping("/getByDispatchId")
    public ApiResult<SendBackDto> getByDispatchId(Long dispatchId) {
        List<SendBackDto> list = sendBackService.selectSendBackListByDispatchId(dispatchId);
        return success(list);
    }

}
