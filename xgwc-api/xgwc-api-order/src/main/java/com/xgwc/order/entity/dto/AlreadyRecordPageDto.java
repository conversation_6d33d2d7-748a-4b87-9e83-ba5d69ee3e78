package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AlreadyRecordPageDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("待派单id")
    @Excel(name = "待派单id")
    private Long agencyDispatchId;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    @FieldDesc("约定初稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "约定初稿日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveAppointTime;

    @FieldDesc("加盟商")
    @Excel(name = "加盟商")
    private String companyName;

    @FieldDesc("业务")
    @Excel(name = "业务")
    private String storeName;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String taobaoId;

    @FieldDesc("谈单人员名称")
    @Excel(name = "谈单人员名称")
    private String saleManName;

    @FieldDesc("订单金额")
    @Excel(name = "订单金额")
    private Long orderAmount;

    @FieldDesc("佣金金额")
    private BigDecimal money;

    @FieldDesc("设计师名称")
    @Excel(name = "设计师名称")
    private String designerName;

    @FieldDesc("派单人")
    @Excel(name = "派单人")
    private String allotUserName;

    @FieldDesc("要求")
    @Excel(name = "要求")
    private String allotRemark;

    @FieldDesc("资料")
    @Excel(name = "资料")
    private String allotFile;

    @FieldDesc("收货情况：0：未发货 1：完成  2：退款 3：部分退款")
    @Excel(name = "收货情况：0：未发货 1：完成  2：退款 3：部分退款")
    private Integer shType;

    @FieldDesc("母订单id")
    private Long pid;

    @FieldDesc("1 普通单 2 紧急单")
    @Excel(name = "1 普通单 2 紧急单")
    private String dispatchType;

    @FieldDesc("订单Id")
    @Excel(name = "订单Id")
    private Long orderId;

}
