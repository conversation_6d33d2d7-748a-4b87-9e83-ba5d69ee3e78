package com.xgwc.order.controller;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.esmapper.CaseIndexMapper;
import com.xgwc.order.esmapper.ChatIndexMapper;
import com.xgwc.order.entity.es.CaseIndex;
import com.xgwc.order.entity.es.CaseVo;
import com.xgwc.order.entity.es.ChatIndex;
import com.xgwc.order.entity.es.ChatVo;
import com.xgwc.order.service.EsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.toolkit.EntityInfoHelper;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("es")
@RestController
public class EsController {

    @Resource
    private EsService esService;

    @Resource
    private CaseIndexMapper caseIndexMapper;

    @Resource
    private ChatIndexMapper chatIndexMapper;

    /**
     * 创建案例库索引，创建索引前先执行分词
     * 判断是否存在索引，不存在则创建索引
     */
    @RequestMapping("createCaseIndex")
    public ApiResult createCaseIndex(){
        String indexName = EntityInfoHelper.getEntityInfo(caseIndexMapper.getEntityClass()).getIndexName();
        boolean exists = caseIndexMapper.existsIndex(indexName);
        if (!exists) {
            boolean success = caseIndexMapper.createIndex();
            log.info("创建案例库索引结果:{}", success);
        }
        return ApiResult.ok();
    }

    /**
     * 创建聊天索引，创建索引前先执行分词
     * 判断是否存在索引，不存在则创建索引
     */
    @RequestMapping("createChatIndex")
    public ApiResult createChatIndex(){
        String indexName = EntityInfoHelper.getEntityInfo(chatIndexMapper.getEntityClass()).getIndexName();
        boolean exists = chatIndexMapper.existsIndex(indexName);
        if (!exists) {
            boolean success = chatIndexMapper.createIndex();
            log.info("创建聊天索引结果:{}", success);
        }
        return ApiResult.ok();
    }

    /**
     * 添加案例库数据
     */
    @PostMapping("addCaseData")
    public ApiResult addCaseData(@RequestBody CaseIndex caseIndex){
        return esService.addCaseData(caseIndex) ? ApiResult.ok() : ApiResult.error("添加案列错误");
    }

    /**
     * 添加聊天数据
     */
    @PostMapping("addChatData")
    public ApiResult addChatData(@RequestBody ChatIndex chatIndex){
        return esService.addChatData(chatIndex) ? ApiResult.ok() : ApiResult.error("添加聊天记录错误");
    }

    /**
     * 删除案例库数据
     */
    @RequestMapping("deleteCaseData")
    public ApiResult deleteCaseData(@RequestBody CaseIndex caseIndex){
        return esService.deleteCaseData(caseIndex) ? ApiResult.ok() : ApiResult.error("删除案例失败");
    }

    /**
     * 删除聊天数据
     */
    @RequestMapping("deleteChatData")
    public ApiResult deleteChatData(@RequestBody ChatIndex chatIndex){
        return esService.deleteChatData(chatIndex) ? ApiResult.ok() : ApiResult.error("删除聊天记录失败");
    }

    @RequestMapping("searchCaseData")
    public ApiResult searchCaseData(CaseVo caseVo){
        JSONObject jsonObject = esService.searchCaseData(caseVo);
        return ApiResult.ok(jsonObject);
    }

    @RequestMapping("searchChatData")
    public ApiResult searchChatData(ChatVo chatVo){
        JSONObject jsonObject = esService.searchChatData(chatVo);
        return ApiResult.ok(jsonObject);
    }

}
