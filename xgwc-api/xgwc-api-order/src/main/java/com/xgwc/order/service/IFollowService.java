package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.vo.FollowVo;
import com.xgwc.order.entity.dto.FollowDto;
import org.apache.ibatis.annotations.Param;

public interface IFollowService {

    /**
     * 根据审核Id获取跟进列表
     * @param agencyAuditId 审核ID
     * @return 跟进集合
     */
    List<FollowDto> selectFollowListByAgencyAuditId(Long agencyAuditId);

    /**
     * 新增跟进
     * @param follow 跟进
     * @return 结果
     */
    int insertFollow(FollowVo follow);

    /**
     * 根据待审核id查询跟进记录
     * @param agencyAuditIds 待审核id集合
     * @return 跟进
     */
    List<FollowDto> findFollowDtoByAgencyAuditIds(List<Long> agencyAuditIds);

}
