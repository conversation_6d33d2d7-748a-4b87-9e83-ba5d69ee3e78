package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SendBackVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("派单工作台Id")
    private Long dispatchId;

    @FieldDesc("退回备注")
    private String explanatory;

    @FieldDesc("")
    private Long id;

    @FieldDesc("订单Id")
    private Long orderId;

    @FieldDesc("退回时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;



}
