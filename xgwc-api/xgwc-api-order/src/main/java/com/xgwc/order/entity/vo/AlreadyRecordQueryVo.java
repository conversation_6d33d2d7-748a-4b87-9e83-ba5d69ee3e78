package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class AlreadyRecordQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[] orderDate;

    @FieldDesc("其它")
    private String other;

    @FieldDesc("派单员")
    private Long dispatchUserId;

    @FieldDesc("加盟商")
    private Long franchiseeId;

    @FieldDesc("业务")
    private Long storeId;

    @FieldDesc("收货情况")
    private Integer shType;

    private Long brandId;

}
