package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtil;
import com.xgwc.order.dao.XgwcBusinessMapper;
import com.xgwc.order.dao.XgwcPlatformGoodsMapper;
import com.xgwc.order.dao.XgwcPlatformGoodsSpecMapper;
import com.xgwc.order.dao.XgwcPlatformShopMapper;
import com.xgwc.order.dao.XgwcShopMapper;
import com.xgwc.order.entity.XgwcPlatformGoods;
import com.xgwc.order.entity.XgwcPlatformGoodsSpec;
import com.xgwc.order.entity.XgwcPlatformShop;
import com.xgwc.order.entity.dto.XgwcPlatformGoodsDto;
import com.xgwc.order.entity.vo.XgwcBusinessTreeVo;
import com.xgwc.order.entity.vo.XgwcPlatformGoodsQueryVo;
import com.xgwc.order.entity.vo.XgwcPlatformGoodsVo;
import com.xgwc.order.entity.vo.XgwcPlatformShopQueryVo;
import com.xgwc.order.entity.vo.XgwcShopVo;
import com.xgwc.order.service.IXgwcPlatformGoodsService;
import com.xgwc.user.feign.api.SysDictDataFeign;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xgwc.common.util.PageUtils.startPage;

@Service
public class XgwcPlatformGoodsServiceImpl implements IXgwcPlatformGoodsService {

    @Resource
    private XgwcShopMapper xgwcShopMapper;
    @Resource
    private SysDictDataFeign sysDictDataFeign;
    @Resource
    private XgwcBusinessMapper xgwcBusinessMapper;
    @Resource
    private XgwcPlatformShopMapper xgwcPlatformShopMapper;
    @Resource
    private XgwcPlatformGoodsMapper xgwcPlatformGoodsMapper;
    @Resource
    private XgwcPlatformGoodsSpecMapper xgwcPlatformGoodsSpecMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveXgwcPlatformGoods(List<XgwcPlatformGoods> xgwcPlatformGoodsList, List<XgwcPlatformGoodsSpec> xgwcPlatformGoodsSpecList, Long brandOwnerId) {
        Map<String, List<XgwcPlatformGoodsSpec>> goodsDetailsMap = xgwcPlatformGoodsSpecList.stream().collect(Collectors.groupingBy(XgwcPlatformGoodsSpec::getGoodsId));
        Lists.partition(xgwcPlatformGoodsList, 200).forEach(goodsGroupList -> {
            List<String> goodsIdList = goodsGroupList.stream().map(XgwcPlatformGoods::getGoodsId).toList();
            List<XgwcPlatformGoodsSpec> goodsSpecGroupList = Lists.newArrayList();
            goodsGroupList.forEach(goods -> goodsSpecGroupList.addAll(goodsDetailsMap.get(goods.getGoodsId())));
            List<XgwcPlatformGoods> dbDataList = xgwcPlatformGoodsMapper.listByGoodsIds(goodsIdList, brandOwnerId);
            Map<String, XgwcPlatformGoods> dbDataMap = dbDataList.stream().collect(Collectors.toMap(XgwcPlatformGoods::getGoodsId, Function.identity()));
            goodsGroupList.forEach(data -> {
                XgwcPlatformGoods dbData = dbDataMap.get(data.getGoodsId());
                if (dbData != null) {
                    data.setBizType(dbData.getBizType());
                    data.setUpdateTime(dbData.getUpdateTime());
                }
            });
            if (!dbDataList.isEmpty()) {
                xgwcPlatformGoodsMapper.deleteByIds(dbDataList.stream().map(XgwcPlatformGoods::getId).toList());
            }
            xgwcPlatformGoodsMapper.insertList(goodsGroupList);
            xgwcPlatformGoodsSpecMapper.deleteByGoodsId(goodsIdList, brandOwnerId);
            xgwcPlatformGoodsSpecMapper.insertList(goodsSpecGroupList);
        });
    }

    /**
     * 查询品牌商商品表
     *
     * @param getGoodsId 品牌商商品表主键
     * @return 品牌商商品表
     */
    @Override
    public XgwcPlatformGoodsDto selectByGoodsId(String getGoodsId) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId == null) {
            return null;
        }
        XgwcPlatformGoodsDto xgwcPlatformGoodsDto = xgwcPlatformGoodsMapper.getByGoodsById(getGoodsId, brandId);
        if (xgwcPlatformGoodsDto != null) {
            this.convertName(brandId, Lists.newArrayList(xgwcPlatformGoodsDto));
        }
        return xgwcPlatformGoodsDto;
    }

    /**
     * 查询品牌商商品表列表
     *
     * @param xgwcBrandGoods 品牌商商品表
     * @return 品牌商商品表
     */
    @Override
    public List<XgwcPlatformGoodsDto> selectXgwcPlatformGoodsList(XgwcPlatformGoodsQueryVo xgwcBrandGoods) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId == null) {
            return Lists.newArrayList();
        }
        xgwcBrandGoods.setBrandId(brandId);
        if (StringUtil.isNotEmpty(xgwcBrandGoods.getKeyword())) {
            XgwcPlatformShopQueryVo platformShopQuery = new XgwcPlatformShopQueryVo();
            platformShopQuery.setBrandId(brandId);
            platformShopQuery.setShopName(xgwcBrandGoods.getKeyword());
            List<String> platformShopIdList = xgwcPlatformShopMapper.selectXgwcPlatformShopList(platformShopQuery).stream().map(XgwcPlatformShop::getShopId).collect(Collectors.toList());
            platformShopIdList.addAll(xgwcShopMapper.listPlatformShopIdByLikeName(brandId, xgwcBrandGoods.getKeyword()));
            xgwcBrandGoods.setKeywordPlatformShopIds(platformShopIdList);
        }
        startPage();
        List<XgwcPlatformGoodsDto> xgwcPlatformGoodsDtos = xgwcPlatformGoodsMapper.selectXgwcPlatformGoodsList(xgwcBrandGoods);
        this.convertName(brandId, xgwcPlatformGoodsDtos);
        return xgwcPlatformGoodsDtos;
    }

    private void convertName(Long brandId, List<XgwcPlatformGoodsDto> xgwcPlatformGoodsDtos) {
        if (!xgwcPlatformGoodsDtos.isEmpty()) {
            List<Long> bizTypeIdList = xgwcPlatformGoodsDtos.stream().map(XgwcPlatformGoodsDto::getBizType).filter(Objects::nonNull).distinct().toList();
            Map<Long, String> bizTypeMap = Maps.newHashMap();
            if (!bizTypeIdList.isEmpty()) {
                bizTypeMap.putAll(xgwcBusinessMapper.listByIds(brandId, bizTypeIdList).stream().collect(Collectors.toMap(XgwcBusinessTreeVo::getBusinessId, XgwcBusinessTreeVo::getBusinessName)));
            }
            Map<String, String> platformMap = JSONObject.parseArray(JSON.toJSONString(sysDictDataFeign.dictType("wdt_platform", null).getData())).stream().collect(Collectors.toMap(x -> ((JSONObject) x).getString("dictValue"), x -> ((JSONObject) x).getString("dictLabel")));
            Map<String, XgwcShopVo> shopInfoMap = xgwcShopMapper.listByPlatformShopIds(brandId, xgwcPlatformGoodsDtos.stream().map(XgwcPlatformGoodsDto::getPlatformShopId).distinct().toList()).stream().collect(Collectors.toMap(XgwcShopVo::getPlatformShopId, Function.identity(), (x, y) -> y));
            XgwcPlatformShopQueryVo platformShopQuery = new XgwcPlatformShopQueryVo();
            platformShopQuery.setBrandId(brandId);
            platformShopQuery.setShopIdList(xgwcPlatformGoodsDtos.stream().map(XgwcPlatformGoodsDto::getPlatformShopId).distinct().toList());
            Map<String, String> platformShopNameMap = xgwcPlatformShopMapper.selectXgwcPlatformShopList(platformShopQuery).stream().collect(Collectors.toMap(XgwcPlatformShop::getShopId, XgwcPlatformShop::getShopName));
            xgwcPlatformGoodsDtos.forEach(goodsDto -> {
                XgwcShopVo xgwcShopVo = shopInfoMap.get(goodsDto.getPlatformShopId());
                if (xgwcShopVo != null) {
                    goodsDto.setShopId(xgwcShopVo.getShopId());
                    goodsDto.setShopName(xgwcShopVo.getShopName());
                }
                goodsDto.initStatusName();
                goodsDto.setBizTypeName(bizTypeMap.get(goodsDto.getBizType()));
                goodsDto.setPlatformName(platformMap.get(goodsDto.getPlatformId().toString()));
                goodsDto.setPlatformShopName(platformShopNameMap.get(goodsDto.getPlatformShopId()));
            });
        }
    }


    /**
     * 修改品牌商商品表
     *
     * @param dto 品牌商商品表
     * @return 结果
     */
    @Override
    public int updateXgwcPlatformGoods(XgwcPlatformGoodsVo dto) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId == null) {
            return 0;
        }
        List<XgwcBusinessTreeVo> xgwcBusinessTreeVos = xgwcBusinessMapper.listByIds(brandId, Lists.newArrayList(dto.getBizType()));
        if (xgwcBusinessTreeVos.isEmpty()) {
            throw new ApiException("请选择正确的业务分类");
        }
        XgwcPlatformGoodsDto xgwcPlatformGoodsDto = xgwcPlatformGoodsMapper.getByGoodsById(dto.getGoodsId(), brandId);
        if (xgwcPlatformGoodsDto == null) {
            return 0;
        }
        XgwcPlatformGoods xgwcPlatformGoods = BeanUtil.copyProperties(xgwcPlatformGoodsDto, XgwcPlatformGoods.class);
        // 只修改业务分类
        xgwcPlatformGoods.setBizType(dto.getBizType());
        xgwcPlatformGoods.setUpdateTime(DateUtils.getNowDate());
        return xgwcPlatformGoodsMapper.updateXgwcPlatformGoods(xgwcPlatformGoods);
    }

}
