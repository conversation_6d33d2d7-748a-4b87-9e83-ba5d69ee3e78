package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 售后审核dto
 */
@Data
public class AfterSalesAuditVo {

    /** 主键 */
    @FieldDesc("主键")
    private Long id;

    /** 售后待审核ID */
    @FieldDesc("售后待审核ID")
    private Long agencyAuditId;

    @FieldDesc("是否开启千牛：0关闭，1开启")
    private Integer isOpenQianniu;

    /** 谈单人 */
    @FieldDesc("谈单人")
    private String saleManName;

    /** 下单日期 */
    @FieldDesc("下单日期")
    private String orderDate;

    /** 订单金额 */
    @FieldDesc("订单金额")
    private BigDecimal orderAmount;

    /** 客户id */
    @FieldDesc("客户id")
    private String customerNo;

    /** 实收金额 */
    @FieldDesc("实收金额")
    private BigDecimal amount;

    /** 1.全额退款，2.部分退款，3.退垫付 */
    @FieldDesc("退款类型 1.全额退款，2.部分退款，3.退垫付")
    private Integer refundType;

    /** 退款原因code, 字典配置 */
    @FieldDesc("退款原因code, 字典配置")
    private String refundReasonCode;

    /** 退款原因 */
    @FieldDesc("退款原因")
    private String refundReason;

    /** 订单编号对应ID */
    @FieldDesc("订单编号对应ID")
    private Long preId;

    /** 订单编号 */
    @FieldDesc("订单编号")
    private String orderNo;

    /** 可退金额 */
    @FieldDesc("可退金额")
    private BigDecimal preAmount;

    /** 总退款金额 */
    @FieldDesc("总退款金额")
    private BigDecimal lastAmount;

    /** 退款说明 */
    @FieldDesc("退款说明")
    private String remark;

    /** 原佣金 */
    @FieldDesc("原佣金")
    private BigDecimal preCommission;

    /** 现佣金 */
    @FieldDesc("现佣金")
    private BigDecimal lastCommission;

    /** 申请人ID */
    @FieldDesc("申请人ID")
    private Long applyUserId;

    /** 申请人用户名 */
    @FieldDesc("申请人用户名")
    private String applyUserName;

    /** 加盟商ID */
    @FieldDesc("加盟商ID")
    private Long franchiseId;

    /** 品牌商ID */
    @FieldDesc("品牌商ID")
    private Long brandId;

    @FieldDesc("审核状态：0通过，1拒绝")
    private Integer auditStatus;

    @FieldDesc("备注")
    private String auditRemark;

    /** 支付列表 */
    @FieldDesc("支付列表")
    private List<OrderRefundPayVo> payList;

    /** 佣金列表 */
    @FieldDesc("佣金列表")
    private List<OrderRefundCommisionVo> commisionList;

    /** 是否有需线下退款：0 否，1 是 */
    private Integer isFinanceCheck;

    /** 是否垫付：0 否，1 是 */
    private Integer isAdvance;

    /** 开票金额 */
    private BigDecimal invoiceAmount;

    /** 开票申请ID */
    private Long invoiceId;
}
