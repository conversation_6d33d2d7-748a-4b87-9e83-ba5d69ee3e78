package com.xgwc.order.entity;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.util.BizUtils;
import com.xgwc.common.util.DateUtils;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Setter
@Getter
public class BrandCsCommissionRateConfig {

    /** 主键ID */
    private Long id;

    /** 品牌商ID */
    private Long brandId;

    /** 配置类型: 1 基础, 2 团队负责人, 3 单人 */
    private Long type;

    /** 加盟商ID */
    private Long franchiseeId;

    /** 档位名称 */
    private String gearName;

    /** 加盟商员工ID */
    private Long staffId;

    /** 加盟商部门ID */
    private Long deptId;

    /** 业务ID */
    private Long businessId;

    /** 生效周期类型: 1 长期有效, 2 指定时间 */
    private Long effectiveCycleType;

    /** 生效开始时间 */
    private Date startTime;

    /** 生效截止时间 */
    private Date endTime;

    /** 状态: 1生效, 2失效 */
    private Long status;

    /** 是否新业务(0:否 1:是) */
    private Long isNewBusiness;

    /** 新业务额外前x月 */
    private Long extraMonthCount;

    /** 额外类型: 1本月生效, 2次月生效 */
    private Long extraType;

    /** 额外加x比例 */
    private BigDecimal extraCommissionRate;

    /** 是否包含设定区间最小值 */
    private Long isContainsMin;

    /** 区间比例配置(JSON格式) */
    private String rangeRateJson;

    /** 创建人 */
    private Long createById;
    /** 更新时间 */
    private Date createTime;
    /** 修改人 */
    private Long updateById;
    /** 更新时间 */
    private Date updateTime;

    public void initEffectiveTime() {
        if (this.isLongEffective()) {
            this.endTime = DateUtils.parseDate("9999-12-31 23:59:59");
        } else {
            BizUtils.assertByFlag(this.endTime != null, "指定周期时截止时间不能为空");
            this.endTime = DateUtils.getEndOfMonth(this.endTime);
        }
        if (new Date().before(this.endTime)) {
            this.status = 1L;
        } else {
            this.status = 2L;
        }
    }

    public boolean isBaseType() {
        return this.type == 1;
    }

    public boolean isLongEffective() {
        return this.effectiveCycleType == 1;
    }

    public boolean isLeaderType() {
        return this.type == 2;
    }

    public boolean isSingleType() {
        return this.type == 3;
    }

    public void initRangeRateJson(List<CsCommissionRateConfigRange> rangeList) {
        this.rangeRateJson = JSONObject.toJSONString(rangeList);
    }

}