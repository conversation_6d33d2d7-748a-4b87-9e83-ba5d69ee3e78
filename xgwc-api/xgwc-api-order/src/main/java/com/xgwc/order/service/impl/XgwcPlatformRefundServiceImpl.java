package com.xgwc.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.PageUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.XgwcBusinessMapper;
import com.xgwc.order.dao.XgwcPlatformGoodsMapper;
import com.xgwc.order.dao.XgwcPlatformRefundDetailsMapper;
import com.xgwc.order.dao.XgwcPlatformRefundMapper;
import com.xgwc.order.dao.XgwcPlatformShopMapper;
import com.xgwc.order.dao.XgwcPlatformTradeMapper;
import com.xgwc.order.dao.XgwcShopMapper;
import com.xgwc.order.entity.XgwcPlatformGoods;
import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.XgwcPlatformRefundDetails;
import com.xgwc.order.entity.XgwcPlatformShop;
import com.xgwc.order.entity.XgwcPlatformTrade;
import com.xgwc.order.entity.XgwcShop;
import com.xgwc.order.entity.dto.XgwcPlatformRefundContactDto;
import com.xgwc.order.entity.dto.XgwcPlatformRefundDto;
import com.xgwc.order.entity.vo.BusinessPrincipalInfoVO;
import com.xgwc.order.entity.vo.Second;
import com.xgwc.order.entity.vo.XgwcBusinessTreeVo;
import com.xgwc.order.entity.vo.XgwcPlatformRefundQueryVo;
import com.xgwc.order.entity.vo.XgwcPlatformShopQueryVo;
import com.xgwc.order.service.IXgwcPlatformRefundService;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.api.SysDictDataFeign;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class XgwcPlatformRefundServiceImpl implements IXgwcPlatformRefundService {

    @Resource
    private XgwcShopMapper xgwcShopMapper;
    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private SysDictDataFeign sysDictDataFeign;
    @Resource
    private XgwcBusinessMapper xgwcBusinessMapper;
    @Resource
    private XgwcPlatformShopMapper xgwcPlatformShopMapper;
    @Resource
    private XgwcPlatformTradeMapper xgwcPlatformTradeMapper;
    @Resource
    private XgwcPlatformGoodsMapper xgwcPlatformGoodsMapper;
    @Resource
    private XgwcPlatformRefundMapper xgwcPlatformRefundMapper;
    @Resource
    private XgwcPlatformRefundDetailsMapper xgwcPlatformRefundDetailsMapper;
    @Resource
    private XgwcPlatformRefundDomainService xgwcPlatformRefundDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveXgwcPlatformRefund(List<XgwcPlatformRefund> refundList, List<XgwcPlatformRefundDetails> refundDetailsList, Long brandOwnerId) {
        Map<String, List<XgwcPlatformRefundDetails>> refundNoDetailsMap = refundDetailsList.stream().collect(Collectors.groupingBy(XgwcPlatformRefundDetails::getRefundNo));
        // 需要自动驳回审批中的退款申请的订单
        Set<String> orderNoSet = Sets.newHashSet();
        Set<XgwcPlatformRefund> autoRefundedSet = Sets.newHashSet();
        List<String> changeLog = Lists.newArrayList();
        Lists.partition(refundList, 200).forEach(refundGroupList -> {
            List<String> refundNoList = refundGroupList.stream().map(XgwcPlatformRefund::getRefundNo).toList();
            List<XgwcPlatformRefundDetails> refundDetailsGroupList = Lists.newArrayList();
            Map<String, XgwcPlatformRefund> dbDataMap = xgwcPlatformRefundMapper.listByRefundNo(refundNoList, brandOwnerId).stream().collect(Collectors.toMap(XgwcPlatformRefund::getRefundNo, Function.identity()));
            refundGroupList.forEach(refund -> {
                XgwcPlatformRefund dbData = dbDataMap.get(refund.getRefundNo());
                //  逆向单 或 金额变动的, 需要检查流程中的退款申请
                if (refund.isReversal()) {
                    orderNoSet.add(refund.getRefundNo());
                    changeLog.add(String.format("%s退款单逆向取消了", refund.getRefundNo()));
                } else if (dbData != null && refund.getRefundAmount().compareTo(dbData.getRefundAmount()) != 0) {
                    orderNoSet.add(refund.getRefundNo());
                    changeLog.add(String.format("%s申请退款金额由%s变为%s", refund.getRefundNo(), dbData.getRefundAmount(), refund.getRefundAmount()));
                } else if (refund.isAutoRefunded()) {
                    autoRefundedSet.add(refund);
                    changeLog.add(String.format("%s自动退款了", refund.getRefundNo()));
                }
                refundDetailsGroupList.addAll(refundNoDetailsMap.get(refund.getRefundNo()));
            });

            xgwcPlatformRefundMapper.deleteByRefundNo(refundNoList, brandOwnerId);
            xgwcPlatformRefundDetailsMapper.deleteByRefundNo(refundNoList, brandOwnerId);

            xgwcPlatformRefundMapper.insertList(refundGroupList);
            xgwcPlatformRefundDetailsMapper.insertList(refundDetailsGroupList);
        });

        if (!changeLog.isEmpty()) {
            log.info("以下平台售后单存在变动: {}", JSON.toJSONString(changeLog));
        }
        orderNoSet.addAll(autoRefundedSet.stream().map(XgwcPlatformRefund::getTradeNo).collect(Collectors.toSet()));
        if (orderNoSet.isEmpty()) {
            return;
        }
        xgwcPlatformRefundDomainService.autoCancelAndRefundedHandle(brandOwnerId, orderNoSet, autoRefundedSet);
    }

    @Override
    public List<XgwcPlatformRefundDto> notUseList(XgwcPlatformRefundQueryVo refundQueryVo) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId == null) {
            return Lists.newArrayList();
        }
        refundQueryVo.formatTime();
        refundQueryVo.setBrandId(brandId);
        PageUtils.startPage();
        List<XgwcPlatformRefundDto> xgwcPlatformRefunds = xgwcPlatformRefundMapper.selectNotUseList(refundQueryVo);
        if (!xgwcPlatformRefunds.isEmpty()) {
            XgwcPlatformShopQueryVo platformShopQuery = new XgwcPlatformShopQueryVo();
            platformShopQuery.setShopIdList(xgwcPlatformRefunds.stream().map(XgwcPlatformRefundDto::getPlatformShopId).distinct().toList());
            platformShopQuery.setBrandId(brandId);
            Map<String, String> platformShopNameMap = xgwcPlatformShopMapper.selectXgwcPlatformShopList(platformShopQuery).stream().collect(Collectors.toMap(XgwcPlatformShop::getShopId, XgwcPlatformShop::getShopName));
            Map<String, String> platformMap = JSONObject.parseArray(JSON.toJSONString(sysDictDataFeign.dictType("wdt_platform", null).getData())).stream().collect(Collectors.toMap(x -> ((JSONObject) x).getString("dictValue"), x -> ((JSONObject) x).getString("dictLabel")));
            xgwcPlatformRefunds.forEach(goodsDto -> {
                goodsDto.setPlatformName(platformMap.get(goodsDto.getPlatformId()));
                goodsDto.setPlatformShopName(platformShopNameMap.get(goodsDto.getPlatformShopId()));
                goodsDto.init();
            });
        }
        return xgwcPlatformRefunds;
    }

    @Override
    public XgwcPlatformRefundContactDto getContact(String refundNo) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId == null) {
            return null;
        }
        XgwcPlatformRefund platformRefund = xgwcPlatformRefundMapper.getByRefundNo(refundNo, brandId);
        if (platformRefund == null) {
            return null;
        }
        XgwcPlatformTrade platformTrade = xgwcPlatformTradeMapper.getByTradeNo(platformRefund.getTradeNo(), ImmutableList.of(brandId));
        if (platformTrade == null) {
            throw new ApiException(String.format("售后单%s缺少对应的平台订单", refundNo));
        }

        XgwcPlatformRefundContactDto dto = new XgwcPlatformRefundContactDto();
        dto.setTradeNo(platformTrade.getTradeNo());
        dto.setPayAmount(platformTrade.getPayAmount());
        dto.setRefundAmount(platformRefund.getRefundAmount());

        Second<Second<Long, String>, Second<String, String>> franchiseInfo = this.getFranchiseInfo(platformTrade.getPlatformShopId(), brandId);
        Long franchiseId = franchiseInfo.getFirst().getFirst();
        String franchiseName = franchiseInfo.getFirst().getSecond();
        String franchiseManagerName = franchiseInfo.getSecond().getFirst();
        String franchiseManagerPhone = franchiseInfo.getSecond().getSecond();

        List<XgwcPlatformRefundDetails> refundDetailsList = xgwcPlatformRefundDetailsMapper.listByRefundNo(refundNo, brandId);
        List<String> goodsIdList = refundDetailsList.stream().map(XgwcPlatformRefundDetails::getGoodsId).distinct().toList();
        List<XgwcPlatformGoods> platformGoodsList = xgwcPlatformGoodsMapper.listByGoodsIds(goodsIdList, brandId);
        List<Long> businessIds = platformGoodsList.stream().map(XgwcPlatformGoods::getBizType).filter(Objects::nonNull).toList();
        Map<Long, Second<String, String>> bizPrincipalInfoMap = Maps.newHashMap();
        Map<Long, String> bizTypeNameMap = Maps.newHashMap();
        if (!businessIds.isEmpty()) {
            bizTypeNameMap.putAll(xgwcBusinessMapper.listByIds(brandId, businessIds).stream().collect(Collectors.toMap(XgwcBusinessTreeVo::getBusinessId, XgwcBusinessTreeVo::getBusinessName)));
            if (franchiseId != null) {
                for (BusinessPrincipalInfoVO bpiVO : xgwcShopMapper.listPrincipalInfoByBusinessIdList(franchiseId, businessIds)) {
                    bizPrincipalInfoMap.put(bpiVO.getBusinessId(), new Second<>(bpiVO.getName(), bpiVO.getPhone()));
                }
            }
        }
        Map<String, XgwcPlatformGoods> goodsMap = platformGoodsList.stream().collect(Collectors.toMap(XgwcPlatformGoods::getGoodsId, Function.identity()));
        dto.setGoodsDetailsContactList(refundDetailsList.stream().map(details -> {
            XgwcPlatformRefundContactDto.GoodsContactData goodsContactData = new XgwcPlatformRefundContactDto.GoodsContactData();
            goodsContactData.setRefundAmount(details.getRefundOrderAmount());
            goodsContactData.setFranchiseName(franchiseName);
            XgwcPlatformGoods goodsVO = goodsMap.get(details.getGoodsId());
            if (goodsVO != null) {
                goodsContactData.setGoodsId(goodsVO.getGoodsId());
                goodsContactData.setGoodsName(goodsVO.getGoodsName());
                goodsContactData.setBizType(goodsVO.getBizType());
                if (goodsVO.getBizType() == null) {
                    goodsContactData.setDeptPrincipalName(franchiseManagerName);
                    goodsContactData.setDeptPrincipalPhone(franchiseManagerPhone);
                } else {
                    goodsContactData.setBizTypeName(bizTypeNameMap.get(goodsVO.getBizType()));
                    Second<String, String> principalInfo = bizPrincipalInfoMap.get(goodsVO.getBizType());
                    if (principalInfo != null) {
                        goodsContactData.setDeptPrincipalName(principalInfo.getFirst());
                        goodsContactData.setDeptPrincipalPhone(principalInfo.getSecond());
                    } else {
                        goodsContactData.setDeptPrincipalName(franchiseManagerName);
                        goodsContactData.setDeptPrincipalPhone(franchiseManagerPhone);
                    }
                }
            }
            return goodsContactData;
        }).toList());
        return dto;
    }

    private Second<Second<Long, String>, Second<String, String>> getFranchiseInfo(String platformShopId, Long brandId) {
        Long franchiseId = null;
        String franchiseName = null;
        String franchiseManagerName = null;
        String franchiseManagerPhone = null;
        XgwcShop shop = xgwcShopMapper.getByPlatformShopId(platformShopId, ImmutableList.of(brandId));
        if (shop != null) {
            franchiseId = shop.getFranchiseId();
            if (franchiseId != null) {
                List<FranchiseDto> franchiseDtos = franchiseFeign.listByIds(ImmutableList.of(franchiseId));
                if (!franchiseDtos.isEmpty()) {
                    FranchiseDto franchiseDto = franchiseDtos.get(0);
                    franchiseName = franchiseDto.getFranchiseName();
                    franchiseManagerName = franchiseDto.getManagerName();
                    if (StringUtils.isNotEmpty(franchiseDto.getManagerPhone())) {
                        franchiseManagerPhone = ParamDecryptUtil.decryptParam(franchiseDto.getManagerPhone(), ParamDecryptUtil.PHONE_KEY);
                    }
                }
            }
        }
        return new Second<>(new Second<>(franchiseId, franchiseName), new Second<>(franchiseManagerName, franchiseManagerPhone));
    }

}