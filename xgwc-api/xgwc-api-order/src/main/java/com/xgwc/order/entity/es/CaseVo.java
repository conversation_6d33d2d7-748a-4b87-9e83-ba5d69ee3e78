package com.xgwc.order.entity.es;

import lombok.Data;

@Data
public class CaseVo {

    /**
     * 标题
     */
    private String title;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 业务编码
     */
    private String typeCodes;

    /**
     * 品牌ID
     */
    private Long branchId;

    /**
     * 品牌商ID
     */
    private Long franchiseId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页条数
     */
    private Integer pageSize;

    public Integer getPageSize() {
        return pageSize == null ? 10 : pageSize > 50 ? 50 : pageSize;
    }
}
