package com.xgwc.order.controller;

import java.util.List;

import com.xgwc.order.entity.vo.InboundSalesParamVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.order.entity.vo.InboundSalesVo;
import com.xgwc.order.entity.dto.InboundSalesDto;
import com.xgwc.order.entity.vo.InboundSalesQueryVo;
import com.xgwc.order.service.IInboundSalesService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

@RestController
@RequestMapping("/inboundSales")
public class InboundSalesController extends BaseController {
    @Autowired
    private IInboundSalesService inboundSalesService;

    /**
     * 查询销售进线列表
     */
    @MethodDesc("查询销售进线列表")
    @PreAuthorize("@ss.hasPermission('inboundSales:inboundSales:list')")
    @GetMapping("/list")
    public ApiResult<InboundSalesDto> list(InboundSalesQueryVo inboundSales) {
        startPage();
        List<InboundSalesDto> list = inboundSalesService.selectInboundSalesList(inboundSales);
        return getDataTable(list);
    }

    /**
     * 导出销售进线列表
     */
    @MethodDesc("导出销售进线列表")
    @PreAuthorize("@ss.hasPermission('inboundSales:inboundSales:export')")
    @Log(title = "销售进线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InboundSalesQueryVo inboundSales) {
        inboundSalesService.export(response, inboundSales);
    }

    /**
     * 获取销售进线详细信息
     */
    @MethodDesc("获取销售进线详细信息")
    @PreAuthorize("@ss.hasPermission('inboundSales:inboundSales:query')")
    @GetMapping(value = "/get")
    public ApiResult<InboundSalesDto> getInfo(@RequestParam("id") Long id) {
        return success(inboundSalesService.selectInboundSalesById(id));
    }

    /**
     * 新增销售进线
     */
    @MethodDesc("新增销售进线")
    @PreAuthorize("@ss.hasPermission('inboundSales:inboundSales:add')")
    @Log(title = "销售进线", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody InboundSalesParamVo inboundSales) {
        return toAjax(inboundSalesService.insertInboundSales(inboundSales));
    }

    /**
     * 修改销售进线
     */
    @MethodDesc("修改销售进线")
    @PreAuthorize("@ss.hasPermission('inboundSales:inboundSales:edit')")
    @Log(title = "销售进线", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody InboundSalesVo inboundSales) {
        return toAjax(inboundSalesService.updateInboundSales(inboundSales));
    }

    /**
     * 删除销售进线
     */
    @MethodDesc("删除销售进线")
    @PreAuthorize("@ss.hasPermission('inboundSales:inboundSales:remove')")
    @Log(title = "销售进线", businessType = BusinessType.DELETE)
	@DeleteMapping("/remove")
    public ApiResult remove(@RequestBody Long id) {
        return toAjax(inboundSalesService.deleteInboundSalesById(id));
    }
}
