package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

import java.math.BigDecimal;

@Data
public class OrderPayVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("支付方式编号")
    private Long id;

    @FieldDesc("订单id")
    private Long oderId;

    @FieldDesc("付款方式:1全款/2定价/3过程款/4尾款")
    private Long payType;

    @FieldDesc("付款截图")
    private String payImg;

    @FieldDesc("实收金额")
    private BigDecimal amount;

    @FieldDesc("支付方式  1:淘宝  2：微信  3：支付宝")
    private Long payChannel;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("收款编号")
    private String collectionNo;



}
