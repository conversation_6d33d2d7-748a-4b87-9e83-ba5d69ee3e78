package com.xgwc.order.service.impl;

import java.util.Date;
import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.entity.dto.IncomLineDto;
import com.xgwc.order.entity.vo.IncomLineVo;
import com.xgwc.order.service.IIncomCustomerService;
import com.xgwc.order.service.IIncomLineService;
import com.xgwc.user.feign.api.UserDeptFeign;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.IncomLineTarckMapper;
import com.xgwc.order.service.IIncomLineTarckService;
import com.xgwc.order.entity.IncomLineTarck;
import com.xgwc.order.entity.vo.IncomLineTarckVo;
import com.xgwc.order.entity.dto.IncomLineTarckDto;
import com.xgwc.order.entity.vo.IncomLineTarckQueryVo;


@Service
public class IncomLineTarckServiceImpl implements IIncomLineTarckService  {
    @Resource
    private IncomLineTarckMapper incomLineTarckMapper;
    @Resource
    private IIncomLineService incomLineService;
    @Resource
    private UserDeptFeign userDeptFeign;
    @Resource
    private IIncomCustomerService incomCustomerService;
    /**
     * 查询进线跟进
     * 
     * @param id 进线跟进主键
     * @return 进线跟进
     */
    @Override
    public IncomLineTarckDto selectIncomLineTarckById(Long id) {
        return incomLineTarckMapper.selectIncomLineTarckById(id);
    }

    /**
     * 查询进线跟进列表
     * 
     * @param incomLineTarck 进线跟进
     * @return 进线跟进
     */
    @Override
    public List<IncomLineTarckDto> selectIncomLineTarckList(IncomLineTarckQueryVo incomLineTarck) {
        return incomLineTarckMapper.selectIncomLineTarckList(incomLineTarck);
    }

    /**
     * 新增进线跟进
     * 
     * @param dto 进线跟进
     * @return 结果
     */
    @Override
    public int insertIncomLineTarck(IncomLineTarckVo dto) {
        if(dto.getOrderId() != null && dto.getOrderNo() == null) {
            throw new ApiException("关联了订单id，必须传入订单编号");
        }
        IncomLineDto incomLine = incomLineService.selectIncomLineById(dto.getInLineId());
        String trackName2 = "";
        if(StringUtils.isNotBlank(dto.getTrackName2())) trackName2 = "-" + dto.getTrackName2();
        incomLine.setTrackName(dto.getTrackName() + trackName2);
        incomLine.setTrackCode(dto.getTrackCode());
        IncomLineVo incomLineVo = BeanUtil.copyProperties(incomLine, IncomLineVo.class);
        incomLineVo.setTrackType("add");
        incomLineService.updateIncomLine(incomLineVo);

        IncomLineTarck incomLineTarck = BeanUtil.copyProperties(dto, IncomLineTarck.class);
        incomLineTarck.setTrackBy(SecurityUtils.getNickName());
        incomLineTarck.setTrackTime(new Date());
        incomLineTarck.setCreateTime(DateUtils.getNowDate());
        incomLineTarck.setCreateBy(SecurityUtils.getNickName());

        Long userId = SecurityUtils.getUserId();
        ApiResult result = userDeptFeign.getUserDeptByUserId(userId,"franchise");
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(result.getData()));
        JSONObject json = jsonObject.getJSONObject("data");
        if(json != null && json.getLong("deptId") == null) throw new ApiException("员工信息缺失，请完善员工信息");
        incomLineTarck.setDeptId(jsonObject.getLong("deptId"));
        incomLineTarck.setDeptName(jsonObject.getString("deptName"));

        incomCustomerService.insertIncomCustomer("tarck",incomLineVo);
        return incomLineTarckMapper.insertIncomLineTarck(incomLineTarck);
    }

    /**
     * 修改进线跟进
     * 
     * @param dto 进线跟进
     * @return 结果
     */
    @Override
    public int updateIncomLineTarck(IncomLineTarckVo dto) {
        IncomLineDto incomLine = incomLineService.selectIncomLineById(dto.getInLineId());String trackName2 = "";
        if(StringUtils.isNotBlank(dto.getTrackName2())) trackName2 = "-" + dto.getTrackName2();
        incomLine.setTrackName(dto.getTrackName() + trackName2);
        incomLine.setTrackCode(dto.getTrackCode());
        incomLineService.updateIncomLine(BeanUtil.copyProperties(incomLine, IncomLineVo.class));

        IncomLineTarck incomLineTarck = BeanUtil.copyProperties(dto, IncomLineTarck.class);
        incomLineTarck.setUpdateTime(DateUtils.getNowDate());
        incomLineTarck.setUpdateBy(SecurityUtils.getNickName());
        return incomLineTarckMapper.updateIncomLineTarck(incomLineTarck);
    }

    /**
     * 批量删除进线跟进
     * 
     * @param ids 需要删除的进线跟进主键
     * @return 结果
     */
    @Override
    public int deleteIncomLineTarckByIds(Long[] ids) {
        return incomLineTarckMapper.deleteIncomLineTarckByIds(ids);
    }

    /**
     * 删除进线跟进信息
     * 
     * @param id 进线跟进主键
     * @return 结果
     */
    @Override
    public int deleteIncomLineTarckById(Long id) {
        return incomLineTarckMapper.deleteIncomLineTarckById(id);
    }
}
