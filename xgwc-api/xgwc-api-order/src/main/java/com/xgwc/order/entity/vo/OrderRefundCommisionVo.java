package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class OrderRefundCommisionVo {

    /** 主键 */
    @FieldDesc("主键")
    private Long id;
    /**
     * 原ID
     */
    @FieldDesc("原ID")
    private Long preId;
    /**
     * 申请ID
     */
    @FieldDesc("申请ID")
    private Long applyId;

    /** 订单编号 */
    @FieldDesc("订单编号")
    private String subOrderNo;

    /** 设计师ID */
    @FieldDesc("设计师ID")
    private Long designerUserId;

    /** 设计师名称 */
    @FieldDesc("设计师名称")
    private String designerUserName;

    /** 设计师业务类型 */
    @FieldDesc("设计师业务类型")
    private String designerBusiness;

    /** 设计师手机号 */
    @FieldDesc("设计师手机号")
    private String designerPhone;

    /** 佣金 */
    @FieldDesc("佣金")
    private BigDecimal commisionAmount;

    /** 修改后佣金 */
    @FieldDesc("修改后佣金")
    private BigDecimal afterCommisionAmount;

}