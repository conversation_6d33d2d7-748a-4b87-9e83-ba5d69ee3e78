package com.xgwc.order.service;

import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.XgwcPlatformRefundDetails;
import com.xgwc.order.entity.dto.XgwcPlatformRefundContactDto;
import com.xgwc.order.entity.dto.XgwcPlatformRefundDto;
import com.xgwc.order.entity.vo.XgwcPlatformRefundQueryVo;

import java.util.List;

public interface IXgwcPlatformRefundService {

    void saveXgwcPlatformRefund(List<XgwcPlatformRefund> first, List<XgwcPlatformRefundDetails> second, Long brandOwnerId);

    /**
     * 未录入售后单列表
     *
     * @param refundQueryVo 平台售后单
     * @return 平台售后单集合
     */
    List<XgwcPlatformRefundDto> notUseList(XgwcPlatformRefundQueryVo refundQueryVo);

    XgwcPlatformRefundContactDto getContact(String refundNo);

}