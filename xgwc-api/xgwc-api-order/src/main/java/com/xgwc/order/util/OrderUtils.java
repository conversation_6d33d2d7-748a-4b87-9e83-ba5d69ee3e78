package com.xgwc.order.util;

import com.google.common.collect.ImmutableList;

public class OrderUtils {

    public static boolean isPlatformPayByChannel(Integer payChannel) {
        // :1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫
        return payChannel != null && ImmutableList.of(1, 5, 7).contains(payChannel);
    }

    public static Integer platformIdToPayChannel(Long platformId) {
        // 1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫
        return platformId == 39 ? 5 : 1;
    }
}
