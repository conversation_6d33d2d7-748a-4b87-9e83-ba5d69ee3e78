package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class InboundSales {

private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;

    /** 进线咨询人数 */
    private Integer inquiriesNumber;

    /** 晚间上线时长 */
    private Integer onlineDuration;

    /** 部门id */
    private Long deptId;

    /** 加盟商id */
    private Long franchiseId;

    /** 品牌商id */
    private Long brandId;

    /** 进行日期 */
    private Date proceedDate;

    /** 客服id */
    private Long userId;

    /** 是否删除：0正常，1删除 */
    private Integer isDel;

    /** 创建人id */
    private Long createById;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人id */
    private Long updateById;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;

}