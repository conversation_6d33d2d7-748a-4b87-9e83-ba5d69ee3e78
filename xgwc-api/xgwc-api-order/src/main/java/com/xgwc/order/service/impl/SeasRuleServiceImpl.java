package com.xgwc.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.SeasRuleMapper;
import com.xgwc.order.entity.dto.SeasRuleDto;
import com.xgwc.order.service.SeasRuleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SeasRuleServiceImpl implements SeasRuleService {

    @Resource
    private SeasRuleMapper seasRuleMapper;

    @Override
    public int saveSeasRule(SeasRuleDto seasRuleDto){
        SysUser sysUser = SecurityUtils.getSysUser();
        String nickName = sysUser.getUserName();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能操作:{}", JSONObject.toJSONString(sysUser));
            return 0;
        }
        SeasRuleDto seasRuleDto1 = seasRuleMapper.getSeasRule(brandId);
        seasRuleDto.setBrandId(brandId);
        int result;
        //如果系统中存在有效的规则，则修改
        if(seasRuleDto1 != null){
            seasRuleDto.setUpdateBy(nickName);
            seasRuleDto.setId(seasRuleDto1.getId());
            result = seasRuleMapper.updateSeasRule(seasRuleDto);
        }else{
            seasRuleDto.setCreateBy(nickName);
            result = seasRuleMapper.saveSeasRule(seasRuleDto);
        }
        return result;
    }

    @Override
    public SeasRuleDto getSeasRuleDto() {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能操作:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return seasRuleMapper.getSeasRule(brandId);
    }
}
