package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgwc.activiti.feign.api.ExecutionFeign;
import com.xgwc.activiti.feign.entity.FlowExecutionVo;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.OrderStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.AfterAgencyAuditMapper;
import com.xgwc.order.dao.OrderInvoiceApplyMapper;
import com.xgwc.order.dao.OrderRefundApplyMapper;
import com.xgwc.order.entity.AfterAgencyAudit;
import com.xgwc.order.entity.OrderInvoiceApply;
import com.xgwc.order.entity.dto.AfterAgencyAuditDto;
import com.xgwc.order.entity.dto.AfterAlreadyAuditDto;
import com.xgwc.order.entity.dto.FollowDto;
import com.xgwc.order.entity.dto.FranchiseStaffDto;
import com.xgwc.order.entity.dto.OrderCompanyDto;
import com.xgwc.order.entity.dto.OrderRefundApplyDto;
import com.xgwc.order.entity.vo.AfterAgencyAuditQueryVo;
import com.xgwc.order.entity.vo.AfterAgencyAuditVo;
import com.xgwc.order.entity.vo.AfterAlreadyAuditQueryVo;
import com.xgwc.order.entity.vo.AfterSalesAuditVo;
import com.xgwc.order.entity.vo.AfterSalesFlowLogVo;
import com.xgwc.order.entity.vo.OrderRefundAuditVo;
import com.xgwc.order.service.IAfterAgencyAuditService;
import com.xgwc.order.service.IAfterSalesFlowLogService;
import com.xgwc.order.service.IFollowService;
import com.xgwc.order.service.IOrderService;
import com.xgwc.settlement.feign.api.SettlementFeign;
import com.xgwc.user.feign.api.BrandOwnerFeign;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.api.StaffFeign;
import com.xgwc.user.feign.entity.BrandOwnerDto;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AfterAgencyAuditServiceImpl implements IAfterAgencyAuditService {
    @Resource
    private AfterAgencyAuditMapper afterAgencyAuditMapper;
    @Resource
    private OrderRefundApplyMapper orderRefundApplyMapper;
    @Resource
    private IFollowService followService;
    @Resource
    private ExecutionFeign executionFeign;
    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private BrandOwnerFeign brandOwnerFeign;
    @Resource
    private StaffFeign staffFeign;
    @Resource
    private IOrderService orderService;
    @Resource
    private SettlementFeign settlementFeign;
    @Resource
    private IAfterSalesFlowLogService afterSalesFlowLogService;
    @Resource
    private OrderInvoiceApplyMapper orderInvoiceApplyMapper;

    /**
     * 查询售后审核
     *
     * @param id 售后审核主键
     * @return 售后审核
     */
    @Override
    public AfterAgencyAuditDto selectAfterAgencyAuditById(Long id) {
        AfterAgencyAuditDto afterAgencyAuditDto = afterAgencyAuditMapper.selectAfterAgencyAuditById(id);
        if(afterAgencyAuditDto!= null){
            afterAgencyAuditDto.setFollowList(followService.selectFollowListByAgencyAuditId(afterAgencyAuditDto.getId()));
        }
        return afterAgencyAuditDto;
    }

    /**
     * 查询售后审核列表
     *
     * @param afterAgencyAudit 售后审核
     * @return 售后审核
     */
    @Override
    public List<AfterAgencyAuditDto> selectAfterAgencyAuditList(AfterAgencyAuditQueryVo afterAgencyAudit) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser.getBrandId() == null) return List.of();
        afterAgencyAudit.setBrandId(sysUser.getBrandId());
        afterAgencyAudit.setReceiverId(sysUser.getUserId());
        List<AfterAgencyAuditDto> list = afterAgencyAuditMapper.selectAfterAgencyAuditList(afterAgencyAudit);
        if(!list.isEmpty()){
            try {
                Map<Long, FollowDto> followDtoMap = followService.findFollowDtoByAgencyAuditIds(
                        list.stream().map(AfterAgencyAuditDto::getId).toList()
                ).stream().collect(Collectors.toMap(FollowDto::getAgencyAuditId, followDto -> followDto));
                for(AfterAgencyAuditDto item : list){
                    FollowDto followDto = followDtoMap.get(item.getId());
                    if (followDto != null) {
                        item.setConsultType(followDto.getConsultType());
                        item.setReturnTime(followDto.getReturnTime());
                    } else {
                        log.warn("未找到ID为 {} 的FollowDto", item.getId());
                    }
                }
            }catch (Exception e){
                log.error("查询根据进来失败", e);
            }
        }
        return list;
    }

    @Override
    public List<AfterAlreadyAuditDto> selectMyAfterAlreadyAuditList(AfterAlreadyAuditQueryVo queryVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser.getBrandId() == null) return List.of();
        queryVo.setBrandId(sysUser.getBrandId());
        queryVo.setCheckId(sysUser.getUserId());
        List<AfterAlreadyAuditDto> list = afterAgencyAuditMapper.selectMyAfterAlreadyAuditList(queryVo);
        return list;
    }

    /**
     * 新增售后审核
     * @param dto 售后审核
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertAfterAgencyAudit(AfterAgencyAuditVo dto, Long brandId, Long userId) {
        AfterAgencyAudit afterAgencyAudit = BeanUtil.copyProperties(dto, AfterAgencyAudit.class);
        afterAgencyAudit.setBrandId(brandId);
        afterAgencyAudit.setReceiverId(userId);
        afterAgencyAudit.setCreateTime(DateUtils.getNowDate());
        int result = afterAgencyAuditMapper.insertAfterAgencyAudit(afterAgencyAudit);
        if(result > 0){
            // 更新申请单领取状态
            orderRefundApplyMapper.updateOrderRefundApplyReceiveStatus(dto.getApplyId(), 1);
        }
        return afterAgencyAudit.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int auditAfterSalesOrder(AfterSalesAuditVo afterSalesAuditVo, Long userId, String userName, Long executionId) {
        int result;
        AfterAgencyAuditDto afterAgencyAuditDto = afterAgencyAuditMapper.selectAfterAgencyAuditById(afterSalesAuditVo.getAgencyAuditId());
        if(afterAgencyAuditDto == null){
            throw new ApiException("售后审核单不存在");
        }
        if(afterSalesAuditVo.getAuditStatus() == 0){ // 通过
            if (executionId == null) {
                // 启动工作流
                ApiResult<Long> apiResult = startFlow(afterSalesAuditVo);
                if(apiResult.getStatus() != 1){
                    throw new ApiException(apiResult.getMessage());
                }
                executionId = apiResult.getData();
            }
            // 更新申请单状态
            editOrderRefundApply(afterAgencyAuditDto.getApplyId(), "ING", 1, executionId);
            // 更新订单状态
            OrderCompanyDto orderCompanyDto = orderService.selectOrderCompanyByOrderNo(afterSalesAuditVo.getOrderNo());
            if(orderCompanyDto != null){
                // 更新订单状态
                orderService.updateOrderStatus(orderCompanyDto.getId(), OrderStatusEnums.AFTER_SALE_START);
            }
            settlementFeign.handleMainOrder(orderCompanyDto.getId());
            // 记录流转日志

        } else { // 拒绝
            // 更新申请单状态
            editOrderRefundApply(afterAgencyAuditDto.getApplyId(), "AFTER_SALES_REJECT", 2, null);
        }
        // 更新售后审核单状态
        result = afterAgencyAuditMapper.updateAfterAgencyAuditStatus(afterSalesAuditVo.getAgencyAuditId(), 1, userId,afterSalesAuditVo.getAuditRemark());
        Integer status = afterSalesAuditVo.getAuditStatus() == 0 ? 1 : 2;
        addAfterSalesFlowLog(afterSalesAuditVo.getId(),userId, userName,status,afterSalesAuditVo.getAuditRemark());
        return result;
    }

    private void addAfterSalesFlowLog(Long businessId, Long userId, String userName,Integer auditStatus,String auditRemark) {
        AfterSalesFlowLogVo flowLogVo = new AfterSalesFlowLogVo();
        flowLogVo.setBusinessId(businessId);
        flowLogVo.setNodeType(2);
        flowLogVo.setOperatorId(userId);
        flowLogVo.setOperatorName(userName);
        flowLogVo.setStatus(auditStatus);
        flowLogVo.setRemark(auditRemark);
        flowLogVo.setSortOrder(1);
        afterSalesFlowLogService.insertAfterSalesFlowLog(flowLogVo);
    }

    // 启动工作流
    private ApiResult<Long> startFlow(AfterSalesAuditVo vo) {
        OrderRefundApplyDto orderRefundApplyDto = orderRefundApplyMapper.getOrderRefundApplyDtoById(vo.getId());
        List<OrderInvoiceApply> orderInvoiceList = orderInvoiceApplyMapper.listPassByOrderNo(vo.getOrderNo());
        if(!orderInvoiceList.isEmpty()){
            // 计算发票总额
            BigDecimal invoiceTotal = orderInvoiceList.stream().map(OrderInvoiceApply::getInvoiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setInvoiceAmount(invoiceTotal);
            vo.setInvoiceId(orderInvoiceList.get(0).getId());
        }
        FlowExecutionVo flowExecutionVo = new FlowExecutionVo();
        flowExecutionVo.setTitle(vo.getOrderNo());
        flowExecutionVo.setFlowValue("AfterSales");
        flowExecutionVo.setBusinessKey(vo.getId());
        flowExecutionVo.setBrandId(vo.getBrandId());
        flowExecutionVo.setCreateBy(orderRefundApplyDto.getApplyUserId());
        flowExecutionVo.setCreateName(orderRefundApplyDto.getApplyUserName());
        ApiResult<BrandOwnerDto> result = brandOwnerFeign.getBrandOwnerById(vo.getBrandId());
        if(result.getStatus() == 1 && result.getData() != null){
            BrandOwnerDto data = result.getData();
            flowExecutionVo.setBrandName(data.getCompanyName());
        }
        flowExecutionVo.setFranchiseId(vo.getFranchiseId());
        ApiResult<FranchiseDto> franchiseResult = franchiseFeign.getFranchiseById(vo.getFranchiseId());
        if(franchiseResult.getStatus() == 1 && franchiseResult.getData() != null){
            FranchiseDto franchiseData = franchiseResult.getData();
            flowExecutionVo.setFranchiseName(franchiseData.getFranchiseName());
        }
        ApiResult staffResult = staffFeign.getFranchiseStaffInfoByUserIdAndFranchiseId(orderRefundApplyDto.getApplyUserId(), orderRefundApplyDto.getFranchiseId());
        if (staffResult.getStatus() == 1 && staffResult.getData() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            FranchiseStaffDto franchiseStaffDto = objectMapper.convertValue(staffResult.getData(), FranchiseStaffDto.class);
            if (franchiseStaffDto != null && franchiseStaffDto.getDeptId() != null) {
                flowExecutionVo.setDeptId(franchiseStaffDto.getDeptId());
                flowExecutionVo.setDeptName(franchiseStaffDto.getDeptName());
            }
        }
        vo.setIsAdvance(0);
        flowExecutionVo.setVariable(vo);
        return executionFeign.insertFlowExecution(flowExecutionVo);
    }

    // 更新申请单状态
    private void editOrderRefundApply(Long id, String applyStatus,Integer afterCheckStatus ,Long executionId) {
        OrderRefundAuditVo orderRefundAuditVo = new OrderRefundAuditVo();
        orderRefundAuditVo.setId(id);
        orderRefundAuditVo.setApplyStatus(applyStatus);
        orderRefundAuditVo.setAfterCheckStatus(afterCheckStatus);
        orderRefundAuditVo.setExecutionId(executionId);
        orderRefundAuditVo.setLastApproveTime(DateUtils.getNowDate());
        orderRefundApplyMapper.updateOrderRefundApplyCheckStatus(orderRefundAuditVo);
    }
}
