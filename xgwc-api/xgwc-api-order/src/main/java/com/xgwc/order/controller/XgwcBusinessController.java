package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.entity.dto.BusinessSettingDto;
import com.xgwc.order.entity.dto.XgwcBusinessDto;
import com.xgwc.order.entity.param.XgwcBusinessParam;
import com.xgwc.order.entity.vo.XgwcBusinessInfo;
import com.xgwc.order.entity.vo.XgwcBusinessTreeVo;
import com.xgwc.order.entity.vo.XgwcBusinessVo;
import com.xgwc.order.service.XgwcBusinessService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 服务管理-业务分类
 */
@RestController
@RequestMapping("/xgwcBusiness")
@Slf4j
public class XgwcBusinessController extends BaseController {

    @Resource
    private XgwcBusinessService xgwcBusinessService;

    /**
     * @param xgwcBusinessParam 查询条件
     * @return 业务列表
     * 查询业务列表
     */
    @MethodDesc("查询业务列表")
    @PreAuthorize("@ss.hasPermission('order:business:list')")
    @PostMapping("/getXgwcBusinessList")
    public ApiResult<XgwcBusinessInfo> getXgwcBusinessList(@RequestBody XgwcBusinessParam xgwcBusinessParam) {
        startPage();
        xgwcBusinessParam.setIsFlag(0);
        return getDataTable(xgwcBusinessService.getXgwcBusinessList(xgwcBusinessParam));
    }

    /**
     * 查询业务信息树
     *
     * @return 业务信息
     */
    @MethodDesc("查询业务信息树")
    @GetMapping("/getBusinessTree")
    public ApiResult getBusinessTree(@RequestParam(value = "brandOwnerId",required = false) Long brandOwnerId) {
        try {
            XgwcBusinessParam xgwcBusinessParam = new XgwcBusinessParam();
            xgwcBusinessParam.setBrandOwnerId(brandOwnerId);
            xgwcBusinessParam.setStatus(0);
            xgwcBusinessParam.setIsFlag(1);
            List<XgwcBusinessInfo> result = xgwcBusinessService.getXgwcBusinessList(xgwcBusinessParam);

            List<XgwcBusinessInfo> safeResult = Optional.ofNullable(result).orElse(Collections.emptyList());

            return ApiResult.ok(safeResult);
        } catch (Exception e) {
            log.error("获取业务信息树失败", e);
            return ApiResult.error( "获取业务信息树失败");
        }
    }

    @MethodDesc("查询业务树（一级目录为品牌商）")
    @GetMapping("/getBusinessTreeByBrandOwnerId")
    public ApiResult getBusinessTreeByBrandOwnerId() {
        try {
            String brandIds = SecurityUtils.getSysUser().getBrandIds();
            if(StringUtils.isNotEmpty(brandIds)){
                List<Long> brandOwnerId = Arrays.stream(brandIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                List<XgwcBusinessInfo> result = xgwcBusinessService.getBusinessTreeByBrandOwnerId(brandOwnerId);
                List<XgwcBusinessInfo> safeResult = Optional.ofNullable(result).orElse(Collections.emptyList());
                return ApiResult.ok(safeResult);
            }
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("获取业务树（一级目录为品牌商）失败", e);
            return ApiResult.error( "获取业务树失败");
        }
    }

    /**
     * 查询一级业务
     */
    @MethodDesc("查询一级业务")
    @GetMapping("/getBusinessOnlyFirst")
    protected ApiResult getBusinessOnlyFirst() {
        try {
            List<XgwcBusinessVo> result = xgwcBusinessService.getBusinessOnlyFirst();
            if (result != null) {
                List<XgwcBusinessVo> safeResult = Optional.of(result).orElse(Collections.emptyList());
                return ApiResult.ok(safeResult);
            }
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("获取一级业务失败", e);
            return ApiResult.error("获取一级业务失败");
        }
    }


    /**
     * @param xgwcBusinessDto 新增业务信息
     * @return 插入结果
     * 新增业务信息
     */
    @MethodDesc("新增业务信息")
    @PreAuthorize("@ss.hasPermission('order:business:insert')")
    @Submit(fileds = "userId")
    @PostMapping("/saveXgwcBusiness")
    public ApiResult saveXgwcBusiness(@RequestBody XgwcBusinessDto xgwcBusinessDto) {
        return xgwcBusinessService.saveXgwcBusiness(xgwcBusinessDto);
    }

    /**
     * @param businessId 业务id
     * @return 业务信息
     * 根据id查询业务信息
     */
    @MethodDesc("根据id查询业务信息")
    @PreAuthorize("@ss.hasPermission('order:business:query')")
    @GetMapping("/getXgwcBusinessById/{businessId}")
    public ApiResult getXgwcBusinessById(@PathVariable Long businessId) {
        return xgwcBusinessService.getXgwcBusinessById(businessId);
    }

    /**
     * @param xgwcBusinessDto 修改信息
     * @return 修改结果
     * 修改业务信息
     */
    @MethodDesc("修改业务信息")
    @Submit(fileds = "userId")
    @PreAuthorize("@ss.hasPermission('order:business:update')")
    @PostMapping("/updateXgwcBusiness")
    public ApiResult updateXgwcBusinessById(@RequestBody XgwcBusinessDto xgwcBusinessDto) {
        return xgwcBusinessService.updateXgwcBusinessById(xgwcBusinessDto);
    }

    /**
     * @param businessId 业务id
     * @return 业务信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('order:business:updateStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "businessId") Integer businessId,
                                 @RequestParam(value = "status") Integer status) {
        return xgwcBusinessService.updateStatusById(businessId,status);
    }

    /**
     * feign调用根据id查询业务信息
     * @param businessId 业务id
     * @return 业务信息
     */
    @MethodDesc("根据id查询业务信息")
    @GetMapping("/getXgwcBusinessByIdFeign/{businessId}")
    public ApiResult getXgwcBusinessByIdFeign(@PathVariable Long businessId) {
        return ApiResult.ok(xgwcBusinessService.getXgwcBusinessByIdFeign(businessId));
    }

    /**
     * 根据业务id查询上下级数据
     * @param businessId 业务id
     * @return 上下级数据
     */
    @MethodDesc("根据业务id查询上下级数据")
    @GetMapping("/getBusinessRelation/{businessId}")
    public ApiResult getBusinessRelation(@PathVariable Long businessId) {
        List<XgwcBusinessTreeVo> businessRelation = xgwcBusinessService.getBusinessRelation(businessId);
        return ApiResult.ok(businessRelation);
    }

    /**
     * 查询业务设置列表（一级业务）
     *
     * @param businessName 业务名称
     * @return 业务设置列表
     */
    @MethodDesc("查询业务设置列表（一级业务）")
    @GetMapping("/getBusinessSettingList")
    public ApiResult getBusinessSettingList(@RequestParam(value = "businessName", required = false) String businessName) {
        startPage();
        return getDataTable(xgwcBusinessService.getBusinessSettingList(businessName));
    }

    /**
     * 获取业务设置详情（一级业务）
     *
     * @param businessId 业务id
     * @return 业务设置详情
     */
    @MethodDesc("查询业务设置详情（一级业务）")
    @GetMapping("/getBusinessSettingById")
    public ApiResult getBusinessSettingById(@RequestParam(value = "businessId") Long businessId) {
        return ApiResult.ok(xgwcBusinessService.getBusinessSettingById(businessId));
    }

    /**
     * 更新业务设置
     *
     * @param businessSettingDto 业务设置信息
     * @return 更新结果
     */
    @MethodDesc("更新业务设置")
    @PutMapping("/updateBusinessSetting")
    public ApiResult updateBusinessSetting(@RequestBody BusinessSettingDto businessSettingDto) {
        return xgwcBusinessService.updateBusinessSetting(businessSettingDto);
    }
}
