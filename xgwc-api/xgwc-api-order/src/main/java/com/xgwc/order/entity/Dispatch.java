package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class Dispatch {

private static final long serialVersionUID=1L;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 已派设计师数量 */
    private Integer dispatchNumber;

    /** 派单状态：1 未领取 2 已领取 3 已派单 */
    private Integer dispatchStatus;

    /** 1 普通单 2 紧急单 */
    private String dispatchType;

    /**  */
    private Long id;

    /** 行更新时间 */
    private Date modifyTime;

    /** 需派设计师数 */
    private Integer needDispatchNumber;

    /** 订单Id */
    private Long orderId;

    /** 领取时间 */
    private Date receiveTime;

    /** 退回次数 */
    private Integer returnNumber;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 品牌商Id */
    private Long brandId;

}