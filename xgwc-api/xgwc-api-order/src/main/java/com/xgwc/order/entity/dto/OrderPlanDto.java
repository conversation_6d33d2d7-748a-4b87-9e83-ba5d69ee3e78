package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class OrderPlanDto {

    private static final long serialVersionUID=1L;

    private Long id;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long oderId;

    @FieldDesc("交稿状态：0未交稿 1交初稿 2交定稿 3制作中")
    @Excel(name = "交稿状态：0未交稿 1交初稿 2交定稿 3制作中")
    private Integer archiveType;

    @FieldDesc("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveTime;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;

    private OrderArchiveDto archive;


    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("oderId",getOderId())
            .append("archiveType",getArchiveType())
            .append("archiveTime",getArchiveTime())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("isDel",getIsDel())
        .toString();
    }
}
