package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class OrderPay {

    /** 支付方式编号 */
    private Long id;

    /** 订单id */
    private Long oderId;

    /** 付款方式:1全款/2定价/3过程款/4尾款 */
    private Integer payType;

    /** 付款截图 */
    private String payImg;

    /** 实收金额 */
    private BigDecimal amount;

    /** 现收金额 ： 发生在退款之后 */
    private BigDecimal nowAmount;

    /** 支付方式  1:淘宝  2：微信  3：支付宝 */
    private Integer payChannel;

    /** 备注 */
    private String remark;

    /** 收款编号 */
    private String collectionNo;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    // 可退款金额
    public BigDecimal getRefundableAmount() {
        return this.nowAmount != null ? this.nowAmount : this.amount;
    }

}