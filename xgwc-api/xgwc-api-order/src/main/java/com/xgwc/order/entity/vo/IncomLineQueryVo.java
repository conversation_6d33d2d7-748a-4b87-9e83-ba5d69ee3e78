package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

@Data
public class IncomLineQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("客户ID")
    private String taobaoId;

    @FieldDesc("客户电话")
    private String tel;

    @FieldDesc("销售名称")
    private String saleName;

    @FieldDesc("客户等级-字典")
    private String taobaoLv;

    @FieldDesc("有效性编号-字典")
    private String validDicCode;
    private String validDicCode2;

    @FieldDesc("跟进状态")
    private String trackCode;

    @FieldDesc("所属业务编号-字典")
    private String stateDicCode;

    @FieldDesc("添加微信0否 1是")
    private Long wechatTag;

    @FieldDesc("进线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inTimeStart;
    private Date inTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inTimeEnd;

    @FieldDesc("回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextTimeStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextTimeEnd;

    @FieldDesc("销售ID")
    private Long saleId;

    @FieldDesc("部门id")
    private Long deptId;
    private List<Long> deptIds;

    @FieldDesc("品牌商id-品牌商查询")
    private Long brandId;

    @FieldDesc("加盟商id")
    private Long franchiseId;
    private String franchiseName;
    @FieldDesc("领取人")
    private String receiver;

    @FieldDesc("领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTimeStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTimeEnd;

    private String isSeas ;
}
