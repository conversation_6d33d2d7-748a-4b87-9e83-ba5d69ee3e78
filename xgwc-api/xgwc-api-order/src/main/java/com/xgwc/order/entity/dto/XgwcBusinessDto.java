package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.dto
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  09:35
 */

/**
 * 服务管理-业务管理
 */
@Data
public class XgwcBusinessDto {

    /** 业务id */
    @FieldDesc("业务id")
    private Long businessId;

    /** 品牌商id */
    @FieldDesc("品牌商id")
    private Long brandOwnerId;

    /** 业务名称 */
    @NotNull("业务名称不能为空")
    @FieldDesc("业务名称")
    private String businessName;

    /** 父类id */
    @NotNull("父类id不能为空")
    @FieldDesc("父类id")
    private Long pid;

    /** 级别 */
    @FieldDesc("级别")
    private String level;

    /** 排序：越小越前 */
    @NotNull("排序不能为空")
    @FieldDesc("排序：越小越前")
    private Integer sort;

    /** 状态：0正常，1禁用 */
    @FieldDesc("状态：0正常，1禁用")
    private String status;

    /** 是否删除：0正常，1删除 */
    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
