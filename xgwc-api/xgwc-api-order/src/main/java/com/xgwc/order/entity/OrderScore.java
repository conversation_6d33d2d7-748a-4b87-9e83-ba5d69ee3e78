package com.xgwc.order.entity;

import lombok.Data;


@Data
public class OrderScore {

private static final long serialVersionUID=1L;

    /** 评分id */
    private Long id;

    /** 订单id */
    private Long oderId;

    /** 交稿id */
    private Long archiveId;

    /** 设计师ID */
    private Long designerId;

    /** 评分模块 */
    private String scoreModel;

    /** 评分分值 */
    private Long scoreNum;

    /** 评分项 */
    private String scoreItem;

    private Integer type;

    private Integer sort;

    private String createBy;

    private String updateBy;
}