package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class AlreadyDispatch {

private static final long serialVersionUID=1L;

    /**  */
    private Long id;

    /** 订单Id */
    private Long orderId;

    /** 待派单Id */
    private Long agencyDispatchId;

    /** 派单员 */
    private Long dispatchUserId;

    /** 派单时间 */
    private Date dispatchTime;

    /** 派单备注 */
    private String dispatchRemark;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;



}