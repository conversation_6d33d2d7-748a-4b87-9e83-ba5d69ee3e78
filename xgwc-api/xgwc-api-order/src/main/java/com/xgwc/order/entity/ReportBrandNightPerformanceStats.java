package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class ReportBrandNightPerformanceStats {

    /** 主键ID */
    private Long id;

    /** 品牌商ID */
    private Long brandId;

    /** 统计维度: 1加盟商月, 2加盟商部门月,  */
    private Long statDimension;

    /** 统计开始时间 */
    private Date statStartTime;

    /** 统计截止时间 */
    private Date statEndTime;

    /** 加盟商ID */
    private Long franchiseeId;

    /** 部门ID */
    private Long departmentId;

    /** 业绩单量 */
    private Long orderCount;

    /** 订单金额 */
    private BigDecimal orderAmount;

    /** 实收金额 */
    private BigDecimal actualAmount;

    /** 其他内容JSON */
    private String otherBizJson;

    /** 更新时间 */
    private Date updatedTime;



}