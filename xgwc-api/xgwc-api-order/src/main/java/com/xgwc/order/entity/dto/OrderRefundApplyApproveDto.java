package com.xgwc.order.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderRefundApplyApproveDto {

    /** 主键 */
    private Long id;
    @FieldDesc("流程审批任务Id")
    private Long taskId;
    @FieldDesc("审批流程实例Id")
    private Long executionId;

    @FieldDesc("退款类型 1.全额退款，2.部分退款，3.退垫付")
    private Integer refundType;
    @FieldDesc("实收金额")
    private BigDecimal preAmount;
    @FieldDesc("总退款金额")
    private BigDecimal lastAmount;
    /** 原佣金 */
    private BigDecimal preCommission;
    /** 现佣金 */
    private BigDecimal lastCommission;
    // 退款原因  平台字典 reason
    private String refundReasonCode;

    /** 下单日期 */
    private Date orderDate;

    @FieldDesc("订单编号")
    private String orderNo;
    /** 客户ID */
    private String taobaoId;

    @FieldDesc("加盟商id")
    private Long franchiseId;
    @FieldDesc("加盟商名称")
    private String franchiseName;
    /** 谈单人员名称 */
    private String saleManName;
    /** 申请人用户名 */
    private String applyUserName;
    /** 创建时间 */
    private Date createTime;
    /**
     * 最后审批时间
     */
    private Date lastApproveTime;
    /** 审批状态: 审批中 ING, 已通过 PASS, 驳回 REJECT, 终止 CANCEL, 撤回 WITHDRAW */
    private String applyStatus;

    private OrderRefundApplyApproveDto executionDatas;

}