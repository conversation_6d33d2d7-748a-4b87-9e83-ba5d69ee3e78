package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AfterAlreadyAuditDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键id")
    private Long id;

    @FieldDesc("申请id")
    private Long applyId;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("实付金额")
    private BigDecimal amount;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("退款类型 1.全额退款，2.部分退款，3.退垫付")
    private Integer refundType;

    @FieldDesc("总退款金额")
    private BigDecimal lastAmount;

    @FieldDesc("可退金额")
    private BigDecimal preAmount;

    @FieldDesc("退款原因")
    private String refundReason;

    @FieldDesc("申请人")
    private String applyUserName;

    @FieldDesc("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @FieldDesc("最后审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastApproveTime;

    @FieldDesc("审核状态")
    private String applyStatus;

    @FieldDesc("客户id")
    private String taobaoId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("客户名称")
    private String customerPhone;
}
