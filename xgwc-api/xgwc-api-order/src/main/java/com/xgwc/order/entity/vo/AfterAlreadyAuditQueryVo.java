package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class AfterAlreadyAuditQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("审核人")
    private Long checkId;

    @FieldDesc("其它：订单编号/客户id/客服")
    private String other;

    @FieldDesc("退款类型")
    private Integer refundType;

    @FieldDesc("退款原因code, 字典配置")
    private String refundReasonCode;

    @FieldDesc("开始时间")
    private String startTime;

    @FieldDesc("结束时间")
    private String endTime;

    @FieldDesc("申请状态")
    private String applyStatus;

}
