package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.List;

@Data
public class XgwcPlatformShopQueryVo {

    @FieldDesc("")
    private Long id;

    @FieldDesc("平台店铺id")
    private String shopId;

    @FieldDesc("店铺名称")
    private String shopName;

    @FieldDesc("平台id")
    private Long platformId;

    @FieldDesc("品牌商id")
    private Long brandId;

    private List<String> shopIdList;

}
