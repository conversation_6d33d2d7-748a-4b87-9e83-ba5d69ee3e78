package com.xgwc.order.entity.dto;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xgwc.order.entity.OrderRefundPayExt;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.util.StringUtil;

import java.math.BigDecimal;

@Setter
@Getter
public class OrderRefundPayDto {

    /** 支付方式  1:淘宝  2：微信  3：支付宝, 4:对公 */
    private Integer payChannel;
    /** 付款方式:1全款/2定价/3过程款/4尾款 */
    private Integer payType;
    /** 实收金额 */
    private BigDecimal amount;

    /** 付款截图 */
    private String payImg;
    /** 收款编号 */
    private String collectionNo;

    /** 退款金额 */
    private BigDecimal refundAmount;
    /** 退款方式  1:淘宝  2：微信  3：支付宝, 4:对公 */
    private Integer refundPayChannel;
    /** 退款配置 */
    @JsonIgnore
    private String refundConfig;
    /** 退款配置 */
    private OrderRefundPayExt refundPayExt;

    public void initJson() {
        if (StringUtil.isNotBlank(this.refundConfig)) {
            this.refundPayExt = JSON.parseObject(this.refundConfig, OrderRefundPayExt.class);
        }
    }

}