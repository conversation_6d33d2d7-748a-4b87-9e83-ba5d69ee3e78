package com.xgwc.order.service;

import com.xgwc.common.entity.ApiListResult;
import com.xgwc.order.entity.cmd.BrandNightPerformanceConfigCmd;
import com.xgwc.order.entity.cmd.BrandNightPerformanceConfigUpdateCmd;
import com.xgwc.order.entity.dto.BrandNightPerformanceConfigDto;
import com.xgwc.order.entity.vo.BrandNightPerformanceConfigQueryVo;

public interface IBrandNightPerformanceConfigService {
    /**
     * 查询品牌商晚间业绩配置
     *
     * @param id 品牌商晚间业绩配置主键
     * @return 品牌商晚间业绩配置
     */
    BrandNightPerformanceConfigDto get(Long id);

    /**
     * 查询品牌商晚间业绩配置列表
     *
     * @param queryVo 品牌商晚间业绩配置
     * @return 品牌商晚间业绩配置集合
     */
    ApiListResult<BrandNightPerformanceConfigDto> listPage(BrandNightPerformanceConfigQueryVo queryVo);

    /**
     * 新增品牌商晚间业绩配置
     *
     * @param cmd 品牌商晚间业绩配置
     * @return 结果
     */
    int add(BrandNightPerformanceConfigCmd cmd);

    /**
     * 修改品牌商晚间业绩配置
     *
     * @param cmd 品牌商晚间业绩配置
     * @return 结果
     */
    int update(BrandNightPerformanceConfigUpdateCmd cmd);

    /**
     * 批量删除品牌商晚间业绩配置
     *
     * @param ids 需要删除的品牌商晚间业绩配置主键集合
     * @return 结果
     */
    int remove(Long[] ids);

}
