package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.dto.AlreadyDispatchPageDto;
import com.xgwc.order.entity.dto.AlreadyRecordPageDto;
import com.xgwc.order.entity.dto.SendBackDto;
import com.xgwc.order.entity.vo.AlreadyDispatchVo;
import com.xgwc.order.entity.dto.AlreadyDispatchDto;
import com.xgwc.order.entity.vo.AlreadyDispatchQueryVo;
import com.xgwc.order.entity.vo.AlreadyRecordQueryVo;

public interface IAlreadyDispatchService  {
    /**
     * 查询我的已派单
     * 
     * @param id 我的已派单主键
     * @return 我的已派单
     */
    public AlreadyDispatchDto selectAlreadyDispatchById(Long id);

    /**
     * 查询我的已派单列表
     * 
     * @param alreadyDispatch 我的已派单
     * @return 我的已派单集合
     */
    public List<AlreadyDispatchPageDto> selectAlreadyDispatchList(AlreadyDispatchQueryVo alreadyDispatch);

    /**
     * 新增我的已派单
     * 
     * @param alreadyDispatch 我的已派单
     * @return 结果
     */
    public int insertAlreadyDispatch(AlreadyDispatchVo alreadyDispatch);

    /**
     * 修改我的已派单
     * 
     * @param alreadyDispatch 我的已派单
     * @return 结果
     */
    public int updateAlreadyDispatch(AlreadyDispatchVo alreadyDispatch);

    /**
     * 批量删除我的已派单
     * 
     * @param ids 需要删除的我的已派单主键集合
     * @return 结果
     */
    public int deleteAlreadyDispatchByIds(Long[] ids);

    /**
     * 删除我的已派单信息
     * 
     * @param id 我的已派单主键
     * @return 结果
     */
    public int deleteAlreadyDispatchById(Long id);

    /**
     * 查询退回记录
     * @return 结果
     * @param id 主键
     */
    List<SendBackDto> getSendBackInfo(Long id);

    /**
     * 查询已派单记录
     * @return 结果
     * @param params 查询条件
     */
    List<AlreadyRecordPageDto> alreadyRecordList(AlreadyRecordQueryVo params);
}
