package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class InboundSalesParamVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("进行日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date proceedDate;

    @FieldDesc("创建人id")
    private Long createById;

    @FieldDesc("修改人id")
    private Long updateById;

    @FieldDesc("进线人员信息")
    private List<InboundSalesBuilderVo> builderList;

}
