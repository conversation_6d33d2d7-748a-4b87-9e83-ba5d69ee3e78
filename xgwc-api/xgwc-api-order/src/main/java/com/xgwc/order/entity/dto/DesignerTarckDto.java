package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class DesignerTarckDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("跟进人")
    @Excel(name = "跟进人")
    private String createBy;

    @FieldDesc("跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "跟进时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("设计师id")
    @Excel(name = "设计师id")
    private Long designerId;

    @FieldDesc("跟进说明")
    @Excel(name = "跟进说明")
    private String details;

    @FieldDesc("编号")
    private Long id;

    @FieldDesc("下次回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下次回访时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nextTime;

    @FieldDesc("跟进状态编码-字典")
    @Excel(name = "跟进状态编码-字典")
    private String trackCode;

    @FieldDesc("跟进状态名称-字典")
    @Excel(name = "跟进状态名称-字典")
    private String trackName;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("designerId",getDesignerId())
            .append("details",getDetails())
            .append("id",getId())
            .append("nextTime",getNextTime())
            .append("trackCode",getTrackCode())
            .append("trackName",getTrackName())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
