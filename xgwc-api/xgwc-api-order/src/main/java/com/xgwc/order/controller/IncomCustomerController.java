package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.IncomCustomerQueryVo;
import com.xgwc.order.entity.vo.IncomCustomerVo;
import com.xgwc.order.service.IIncomCustomerService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/IncomLine/customer")
public class IncomCustomerController extends BaseController {
    @Autowired
    private IIncomCustomerService incomCustomerService;

    /**
     * 查询客户列表列表
     */
    @MethodDesc("查询客户列表列表")
//    @PreAuthorize("@ss.hasPermission('IncomLine:customer:list')")
    @GetMapping("/list")
    public ApiResult<IncomCustomerDto> list(IncomCustomerQueryVo incomCustomer) {
        startPage();
        List<IncomCustomerDto> list = incomCustomerService.selectIncomCustomerList(incomCustomer);
        return getDataTable(list);
    }

    @MethodDesc("获取客户基本信息")
//    @PreAuthorize("@ss.hasPermission('IncomLine:customer:basic')")
    @GetMapping(value = "/basic/{taobaoId}")
    public ApiResult<IncomCustomerBasicDto> basic(@PathVariable("taobaoId") String taobaoId) {
        return success(incomCustomerService.basic(taobaoId));
    }

    @MethodDesc("客户基本信息编辑")
    @PreAuthorize("@ss.hasPermission('IncomLine:customer:edit')")
    @PutMapping(value = "/basic")
    public ApiResult basicEdit(@RequestBody @Valid IncomCustomerVo vo) {
        return toAjax(incomCustomerService.updateIncomCustomer(vo));
    }

    @MethodDesc("获取客户购买历史/需求历史")
//    @PreAuthorize("@ss.hasPermission('IncomLine:customer:statis')")
    @GetMapping(value = "/statis/{taobaoId}")
    public ApiResult<List<IncomCustomerStatisDto>> statis(@PathVariable("taobaoId") String taobaoId) {
        return success(incomCustomerService.statis(taobaoId));
    }


    @MethodDesc("获取客户跟进历史")
//    @PreAuthorize("@ss.hasPermission('IncomLine:customer:tarck')")
    @GetMapping(value = "/tarck/{taobaoId}")
    public ApiResult<List<IncomLineTarckDto>> tarckHis(@PathVariable("taobaoId") String taobaoId) {
        return success(incomCustomerService.tarckHis(taobaoId));
    }

    @MethodDesc("获取客户聊天历史")
//    @PreAuthorize("@ss.hasPermission('IncomLine:customer:chat')")
    @GetMapping(value = "/chat/{taobaoId}")
    public ApiResult<List<IncomCustomerChatDto>> chatHis(@PathVariable("taobaoId") String taobaoId) {
        return success(incomCustomerService.chatHis(taobaoId));
    }

    @MethodDesc("导出")
    @PreAuthorize("@ss.hasPermission('IncomLine:customer:export')")
    public void export() {

    }
}
