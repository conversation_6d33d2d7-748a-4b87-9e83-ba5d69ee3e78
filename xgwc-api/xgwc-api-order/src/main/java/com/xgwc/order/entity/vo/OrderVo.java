package com.xgwc.order.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.order.entity.dto.OrderPayDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderVo {

    @FieldDesc("订单id")
    private Long id;

    @NotNull(message = "订单归类不能为空")
    @FieldDesc("订单归类: 1平台订单, 2线下")
    private Integer orderCategory;

    @FieldDesc("谈单人员")
    @NotNull(message = "谈单人员必填")
    private Long saleManId;

    @FieldDesc("谈单人员名称")
    @NotNull(message = "谈单人员名称必填")
    private String saleManName;

    @FieldDesc("录入部门编码")
    @NotNull(message = "录入部门编码必填")
    private Long deptId;

    @FieldDesc("录入部门名称")
    @NotNull(message = "录入部门名称必填")
    private String deptName;

    @FieldDesc("订单类型：0正常单，1转化单")
    @NotNull(message = "订单类型必填")
    private Integer transferState;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "下单日期必填")
    private String orderDate;

    @FieldDesc("订单来源ID（店铺id）")
    @NotNull(message = "订单来源ID必填")
    private Long storeId;

    @FieldDesc("订单来源名称（店铺名称）")
    @NotNull(message = "订单来源名称必填")
    private String storeName;

    @FieldDesc("支付方式  1:淘宝  2：微信  3：支付宝")
    @NotNull(message = "支付方式必填")
    private Integer payChannel;

    @FieldDesc("订单状态（0：未发货 1：完成  2：退款 3：部分退款）")
    private Integer shType;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("客户ID")
    @NotNull(message = "客户ID必填")
    private String taobaoId;

    @FieldDesc("订单金额")
    @NotNull(message = "订单金额必填")
    private BigDecimal orderAmount;

    @FieldDesc("实收金额")
    private BigDecimal amount;

    @FieldDesc("付款方式:1全款/2阶段付")
    @NotNull(message = "付款方式必填")
    private Integer payType;

    @FieldDesc("付款截图")
    @Excel(name = "付款截图")
    private String payImg;

    @FieldDesc("收款编号")
    @Excel(name = "收款编号")
    private String collectionNo;

    @FieldDesc("期望初稿日期：yyyy-MM-dd HH:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "期望初稿日期必填")
    private Date archiveExpectTime;

    @FieldDesc("派单类型：0自己派单，1品牌商派单，2无")
    @NotNull(message = "派单类型必填")
    private Integer allotType;

    @FieldDesc("是否紧急：0否 1是")
    private Integer allotUrgency;

    @FieldDesc("派单人id")
    private Long allotUserId;

    @FieldDesc("派单人")
    private String allotUserName;

    @FieldDesc("派单需求")
    private String allotRemark;

    @FieldDesc("派单设计师数量")
    private Long allotNum;

    @FieldDesc("协助文件")
    private String allotFile;

    @FieldDesc("母订单id")
    private Long pid;

    @FieldDesc("设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    private String designerPhone;

    @FieldDesc("设计师业务类型")
    private String designerBusiness;

    @FieldDesc("佣金金额")
    private BigDecimal money;

    @FieldDesc("约定初稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveAppointTime;

    @FieldDesc("提交定稿日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date archiveTime;

    @FieldDesc("交稿状态：0未交稿 1交初稿 2交定稿")
    private Integer archiveType;

    @FieldDesc("所属业务编号-字典")
    @NotNull( message = "所属业务编号不能为空")
    private String stateDicCode;

    @FieldDesc("所属业务名称-字典")
    @NotNull( message = "所属业务名称不能为空")
    private String stateDicName;

    @FieldDesc("是否转介绍：0否 1是")
    private Integer isTransfer;

    @FieldDesc("备注")
    private String remark;

    private List<OrderPayDto> orderPays;

    @FieldDesc("更换设计师")
    private List<OrderDesignerVo> orderDesigners;


}
