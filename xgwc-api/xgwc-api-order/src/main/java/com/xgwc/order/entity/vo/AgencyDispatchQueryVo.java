package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class AgencyDispatchQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("派单员")
    private Long dispatchUserId;

    @FieldDesc("加盟商")
    private Long franchiseeId;

    @FieldDesc("业务")
    private Long stateDicCode;

    @FieldDesc("其它")
    private String other;

    @FieldDesc("下单开始时间")
    private String startTime;

    @FieldDesc("下单结束时间")
    private String endTime;

}
