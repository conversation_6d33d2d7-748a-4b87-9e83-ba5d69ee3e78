package com.xgwc.order.service;

import java.util.List;
import com.xgwc.order.entity.vo.AfterSalesFlowLogVo;
import com.xgwc.order.entity.dto.AfterSalesFlowLogDto;

public interface IAfterSalesFlowLogService {
    /**
     * 查询售后审批记录表
     * @param businessId 业务ID
     * @return 售后审批记录表
     */
    List<AfterSalesFlowLogDto> selectAfterSalesFlowLogByBusinessId(Long businessId);

    /**
     * 新增售后审批记录表
     *
     * @param afterSalesFlowLog 售后审批记录表
     * @return 结果
     */
    int insertAfterSalesFlowLog(AfterSalesFlowLogVo afterSalesFlowLog);

}
