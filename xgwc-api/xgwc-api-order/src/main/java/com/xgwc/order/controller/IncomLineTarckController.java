package com.xgwc.order.controller;

import java.util.List;

import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.order.entity.vo.IncomLineTarckVo;
import com.xgwc.order.entity.dto.IncomLineTarckDto;
import com.xgwc.order.entity.vo.IncomLineTarckQueryVo;
import com.xgwc.order.service.IIncomLineTarckService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

@RestController
@RequestMapping("/incomLine/tarck")
public class IncomLineTarckController extends BaseController {
    @Autowired
    private IIncomLineTarckService incomLineTarckService;

    /**
     * 查询进线跟进列表
     */
    @MethodDesc("查询进线跟进列表")
//    @PreAuthorize("@ss.hasPermission('incomLine:tarck:list')")
    @GetMapping("/list")
    public ApiResult<IncomLineTarckDto> list(@Valid IncomLineTarckQueryVo incomLineTarck) {
        startPage();
        List<IncomLineTarckDto> list = incomLineTarckService.selectIncomLineTarckList(incomLineTarck);
        return getDataTable(list);
    }


    /**
     * 获取进线跟进详细信息
     */
    @MethodDesc("获取进线跟进详细信息")
//    @PreAuthorize("@ss.hasPermission('incomLine:tarck:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<IncomLineTarckDto> getInfo(@PathVariable("id") Long id) {
        return success(incomLineTarckService.selectIncomLineTarckById(id));
    }

    /**
     * 新增进线跟进
     */
    @MethodDesc("新增进线跟进")
    @PreAuthorize("@ss.hasPermission('incomLine:tarck:edit')")
    @Log(title = "进线跟进", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody @Valid IncomLineTarckVo incomLineTarck) {
        return toAjax(incomLineTarckService.insertIncomLineTarck(incomLineTarck));
    }

    /**
     * 修改进线跟进
     */
    @MethodDesc("修改进线跟进")
    @PreAuthorize("@ss.hasPermission('incomLine:tarck:edit')")
    @Log(title = "进线跟进", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody  @Valid IncomLineTarckVo incomLineTarck) {
        return toAjax(incomLineTarckService.updateIncomLineTarck(incomLineTarck));
    }

}
