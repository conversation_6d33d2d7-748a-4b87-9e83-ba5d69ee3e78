package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.AfterAgencyAuditDto;
import com.xgwc.order.entity.dto.AfterAlreadyAuditDto;
import com.xgwc.order.entity.vo.AfterAgencyAuditQueryVo;
import com.xgwc.order.entity.vo.AfterAlreadyAuditQueryVo;
import com.xgwc.order.service.IAfterAgencyAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 售后审核Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("/afterAgencyAudit")
public class AfterAgencyAuditController extends BaseController {
    @Autowired
    private IAfterAgencyAuditService afterAgencyAuditService;

    /**
     * 查询售后我的待审核列表
     */
    @MethodDesc("查询售后我的待审核列表")
    @PreAuthorize("@ss.hasPermission('afterAgencyAudit:afterAgencyAudit:list')")
    @GetMapping("/list")
    public ApiResult<AfterAgencyAuditDto> list(AfterAgencyAuditQueryVo afterAgencyAudit) {
        startPage();
        List<AfterAgencyAuditDto> list = afterAgencyAuditService.selectAfterAgencyAuditList(afterAgencyAudit);
        return getDataTable(list);
    }


    /**
     * 查询售后我的已审核列表
     */
    @MethodDesc("查询售后我的已审核列表")
    @PreAuthorize("@ss.hasPermission('afterAgencyAudit:afterAgencyAudit:list')")
    @GetMapping("/findMyAuditList")
    public ApiResult<AfterAlreadyAuditDto> findMyAuditList(AfterAlreadyAuditQueryVo queryVo) {
        startPage();
        List<AfterAlreadyAuditDto> list = afterAgencyAuditService.selectMyAfterAlreadyAuditList(queryVo);
        return getDataTable(list);
    }

    /**
     * 获取售后审核详细信息
     */
    @MethodDesc("获取售后审核详细信息")
    @PreAuthorize("@ss.hasPermission('afterAgencyAudit:afterAgencyAudit:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<AfterAgencyAuditDto> getInfo(@PathVariable("id") Long id) {
        return success(afterAgencyAuditService.selectAfterAgencyAuditById(id));
    }

}
