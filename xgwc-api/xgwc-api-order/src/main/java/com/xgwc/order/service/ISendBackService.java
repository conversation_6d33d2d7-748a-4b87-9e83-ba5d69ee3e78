package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.vo.SendBackVo;
import com.xgwc.order.entity.dto.SendBackDto;
import com.xgwc.order.entity.vo.SendBackQueryVo;

public interface ISendBackService  {
    /**
     * 查询退回记录
     * 
     * @param id 退回记录主键
     * @return 退回记录
     */
    public SendBackDto selectSendBackById(Long id);

    /**
     * 查询退回记录列表
     * 
     * @param sendBack 退回记录
     * @return 退回记录集合
     */
    public List<SendBackDto> selectSendBackList(SendBackQueryVo sendBack);

    /**
     * 新增退回记录
     * 
     * @param sendBack 退回记录
     * @return 结果
     */
    public int insertSendBack(SendBackVo sendBack);

    /**
     * 修改退回记录
     * 
     * @param sendBack 退回记录
     * @return 结果
     */
    public int updateSendBack(SendBackVo sendBack);

    /**
     * 批量删除退回记录
     * 
     * @param ids 需要删除的退回记录主键集合
     * @return 结果
     */
    public int deleteSendBackByIds(Long[] ids);

    /**
     * 删除退回记录信息
     * 
     * @param id 退回记录主键
     * @return 结果
     */
    public int deleteSendBackById(Long id);

    /**
     * 根据派单id查询退回记录列表
     *
     * @param dispatchId 派单id
     * @return 退回记录集合
     */
    public List<SendBackDto> selectSendBackListByDispatchId(Long dispatchId);
}
