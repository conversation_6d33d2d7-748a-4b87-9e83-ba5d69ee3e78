package com.xgwc.order.util;

import com.google.common.base.CaseFormat;
import com.xgwc.common.annotation.FieldDesc;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;

public class EntityToMySqlTableUtil {

    private static String packageName = "com.xgwc.order.entity";
    private static String WORK_PATH = "D:\\work\\new-xgwc\\xgwc-sass\\xgwc-parent\\xgwc-api";
    private static String serviceName = "xgwc-api-order";
    private static String output_file = "d:\\sql\\order.sql";


    public static void main(String[] args) throws ClassNotFoundException, IOException {
        File dir = new File(WORK_PATH+File.separator+serviceName+File.separator+"src\\main\\java\\"+getPackageName());
        File[] files = dir.listFiles();
        for (File file : files) {
            if(file.isFile()) {
                StringBuffer sb = new StringBuffer();
                String className = packageName+getClassName(file.getName());
                Class clazz = Class.forName(className);
                Class qclazz = null;
                try {
                    qclazz = Class.forName(packageName+".vo"+getClassName(file.getName())+"QueryVo");
                } catch (ClassNotFoundException e) {

                }

                String tableName = camelToUnderline(clazz.getSimpleName());
                Field[] fields = clazz.getDeclaredFields();
                if(fields.length == 0) {
                    continue;
                }
                sb.append("DROP TABLE IF EXISTS `").append(tableName).append("`;\n");
                sb.append("CREATE TABLE `").append(tableName).append("` ( \n");
                String idKey = "";
                try {
                    idKey = qclazz.getDeclaredField("id").getName();
                } catch (Exception e) {

                }
                for (Field field : fields) {
                    if(!java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                        String type = typeTransformation(field.getType().getSimpleName());
                        String name = field.getName();
                        sb.append("   `").append(camelToUnderline(name)).append("` ").append(type);
                        if(idKey.isEmpty()) {
                            idKey = camelToUnderline(field.getName());
                        }
                        String comment = "";
                        if(qclazz!= null) {
                            try {
                                Field nameField = qclazz.getDeclaredField(name);
                                FieldDesc desc = nameField.getAnnotation(FieldDesc.class);
                                if(desc != null) {
                                    comment = desc.value();
                                }
                            } catch (Exception dd) {

                            }
                        }
                        if(idKey.equals(camelToUnderline(name)) && "bigint(20)".equals(type)) {
                            sb.append(" NOT NULL AUTO_INCREMENT COMMENT '编号', ");
                        } else {
                            if(idKey.equals(camelToUnderline(name)))
                                sb.append(" NOT  NULL DEFAULT '' COMMENT '").append(comment).append("', ");
                            else
                                sb.append(" NULL DEFAULT NULL COMMENT '").append(comment).append("', ");
                        }
                        sb.append("\n");
                    }
                }
                sb.append("   PRIMARY KEY (`").append(idKey).append("`) USING BTREE  \n) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC ;\n");
//                System.out.println(sb.toString());
                outputFile(sb.toString());
            }

        }
    }

    private static void outputFile(String txt) throws IOException {
        File file = new File(output_file);
        if (!file.exists()) {
            file.createNewFile();
        }
        FileWriter write=new FileWriter(output_file,true);
        BufferedWriter bw = new BufferedWriter(write);
        bw.write(txt);
        bw.close();
        write.close();
    }

    private static String typeTransformation(String typeName) {
        typeName = typeName.toLowerCase().trim();
        switch (typeName) {
            case "int":
            case "long":
            case "integer":
                return "bigint(20)";
            case "float":
            case "double":
            case "bigdecimal":
                return "decimal(11,2)";
            case "boolean":
                return "varchar(1)";
            case "date":
                return "datetime";
            default:
                return "varchar(255)";
        }
    }

    private static String getClassName(String fileName) {
        fileName = fileName.substring(0,fileName.indexOf("."));
        return "."+fileName;
    }

    private static String getPackageName() {
        return packageName.replace(".","/");
    }
    private static String camelToUnderline(String camelCase) {
       return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, camelCase);
    }


}
