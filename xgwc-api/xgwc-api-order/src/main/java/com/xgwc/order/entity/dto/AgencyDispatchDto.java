package com.xgwc.order.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@Data
public class AgencyDispatchDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("派单Id")
    @Excel(name = "派单Id")
    private Long dispatchId;

    @FieldDesc("领取的设计师数量")
    @Excel(name = "领取的设计师数量")
    private Integer dispatchNumber;

    @FieldDesc("派单备注")
    @Excel(name = "派单备注")
    private String dispatchRemark;

    @FieldDesc("派单状态：1 待派单 2 派单中")
    @Excel(name = "派单状态：1 待派单 2 派单中")
    private Integer dispatchStatus;

    @FieldDesc("派单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "派单时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dispatchTime;

    @FieldDesc("派单员")
    @Excel(name = "派单员")
    private Long dispatchUserId;

    @FieldDesc("")
    @Excel(name = "")
    private Long id;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("需派设计师数")
    @Excel(name = "需派设计师数")
    private Integer needDispatchNumber;

    @FieldDesc("订单Id")
    @Excel(name = "订单Id")
    private Long orderId;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("已分配数量")
    @Excel(name = "已分配数量")
    private Integer assignedQuantity;



    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("dispatchId",getDispatchId())
            .append("dispatchNumber",getDispatchNumber())
            .append("dispatchRemark",getDispatchRemark())
            .append("dispatchStatus",getDispatchStatus())
            .append("dispatchTime",getDispatchTime())
            .append("dispatchUserId",getDispatchUserId())
            .append("id",getId())
            .append("modifyTime",getModifyTime())
            .append("needDispatchNumber",getNeedDispatchNumber())
            .append("orderId",getOrderId())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
