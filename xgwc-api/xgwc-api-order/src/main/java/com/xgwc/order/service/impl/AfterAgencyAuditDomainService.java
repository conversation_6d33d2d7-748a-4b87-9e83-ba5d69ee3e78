package com.xgwc.order.service.impl;

import com.xgwc.common.entity.dto.FlowExecutionDto;
import com.xgwc.common.enums.OrderStatusEnums;
import com.xgwc.common.util.DateUtils;
import com.xgwc.order.dao.OrderRefundApplyMapper;
import com.xgwc.order.entity.OrderRefundApply;
import com.xgwc.order.entity.dto.OrderCompanyDto;
import com.xgwc.order.entity.dto.OrderRefundApplyDto;
import com.xgwc.order.entity.dto.SubOrderDto;
import com.xgwc.order.service.IOrderService;
import com.xgwc.order.service.OrderRefundService;
import com.xgwc.settlement.feign.api.SettlementFeign;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AfterAgencyAuditDomainService {
    @Resource
    private IOrderService orderService;
    @Resource
    private SettlementFeign settlementFeign;
    @Resource
    private OrderRefundService orderRefundService;
    @Resource
    private OrderRefundApplyMapper orderRefundApplyMapper;


    public void executeAfterSales(FlowExecutionDto execution) {
        OrderRefundApply orderRefundApply = new OrderRefundApply();
        orderRefundApply.setId(execution.getBusinessKey());
        orderRefundApply.setApplyStatus(execution.getStatus());
        if (execution.getStatus().equals("WITHDRAW")) {
            orderRefundApply.setLastApproveTime(DateUtils.getTime());
        } else {
            Date endTime = execution.getEndTime();
            if (endTime != null) {
                orderRefundApply.setLastApproveTime(DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss"));
            } else {
                orderRefundApply.setLastApproveTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            }
        }

        orderRefundService.updateOrderRefundApply(orderRefundApply);
        // 更新订单状态
        OrderCompanyDto orderCompanyDto = orderService.selectOrderCompanyByOrderNo(execution.getTitle());
        if (orderCompanyDto != null) {
            orderService.updateOrderStatus(orderCompanyDto.getId(), OrderStatusEnums.AFTER_SALE_END);
        }
        if (execution.getStatus().equals("PASS")) {
            OrderRefundApplyDto orderRefundApplyDto = orderRefundApplyMapper.getOrderRefundApplyDtoById(execution.getBusinessKey());
            int refundStatus = 0;
            if (orderRefundApplyDto.getRefundType() == 1) {
                refundStatus = 2;
            } else if (orderRefundApplyDto.getRefundType() == 2) {
                if (orderRefundApplyDto.getLastAmount().compareTo(BigDecimal.ZERO) > 0) {
                    refundStatus = 1;
                } else {
                    refundStatus = 2;
                }
            }
            List<Long> ids = new ArrayList<>();
            Long mainOrderId = orderCompanyDto.getId();
            ids.add(mainOrderId);
            List<SubOrderDto> subOrderDtos = orderService.selectSubOrderListByPId(mainOrderId);
            if (subOrderDtos != null && !subOrderDtos.isEmpty()) {
                List<Long> subOrderIds = subOrderDtos.stream().map(SubOrderDto::getId).collect(Collectors.toList());
                ids.addAll(subOrderIds);
            }
            // 批量更新主 + 子订单的退款状态
            orderService.batchUpdateRefundStatus(ids, refundStatus);
        }
        settlementFeign.handleMainOrder(orderCompanyDto.getId());
    }

}