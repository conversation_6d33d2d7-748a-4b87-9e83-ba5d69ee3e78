package com.xgwc.order.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.util.Date;


@Data
public class XgwcPlatformGoods {

    private Long id;

    /** 旺店通平台货品id */
    private String goodsId;

    /** 商品名称 */
    private String goodsName;

    /** 平台id */
    private Long platformId;

    /** 平台店铺id */
    private String platformShopId;

    /** 品牌商id */
    private Long brandId;

    /** 业务分类 */
    private Long bizType;

    /** 0删除1在架2下架 */
    private Long status;

    /** 是否删除：0正常，1删除 */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;

    // 文档路径 https://open.wangdian.cn/Y/open/apidoc/doc?path=vip_api_goods_query_Y
    /*
     * {
     * 	"code": 0,
     * 	"message": "",
     * 	"total_count": 7188,
     * 	"goods_list": [
     *        {
     * 		"shop_id": "5",
     * 		"api_goods_id": "2592103086323400717",
     * 		"api_spec_id": "2592103086583447565",
     * 		"rec_id": "2606461714438291464",
     * 		"platform_id": 125,
     * 		"is_deleted": 0,
     * 		"status": 1,
     * 		"outer_id": "240801341",
     * 		"api_goods_name": "test货品",
     * 		"api_spec_name": "test货品1规格",
     * 		"spec_outer_id": "240801343",
     * 		"spec_code": "",
     * 		"stock_num": null,
     * 		"match_target_type": 0,
     * 		"match_code": "",
     * 		"merchant_name": null,
     * 		"merchant_code": null,
     * 		"merchant_no": null,
     * 		"match_target_id": "0",
     * 		"price": "0.0000",
     * 		"cid": "",
     * 		"pic_url": "https://img.alicdn.com/bao/uploaded/i1/1678900747/O1CN012MMk4M1HOAnPBeyda_!!1678900747.jpg",
     * 		"barcode": "",
     * 		"brand_id": null,
     * 		"class_id_path": null,
     * 		"modified": "2024-11-20 10:10:25"
     *        }
     * 	  ]
     * }
     */
    public static XgwcPlatformGoods initByJson(JSONObject jsonObject) {
        XgwcPlatformGoods goods = new XgwcPlatformGoods();
        goods.goodsId = jsonObject.getString("api_goods_id");
        goods.goodsName = jsonObject.getString("api_goods_name");
        goods.platformId = jsonObject.getLong("platform_id");
        goods.platformShopId = jsonObject.getString("shop_id");
        goods.status = jsonObject.getLong("status");
        goods.isDel = 0;
        goods.createTime = jsonObject.getDate("modified");
        goods.updateTime = new Date();
        return goods;
    }


}