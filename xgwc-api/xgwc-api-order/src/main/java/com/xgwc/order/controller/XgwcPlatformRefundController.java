package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcPlatformRefundContactDto;
import com.xgwc.order.entity.dto.XgwcPlatformRefundDto;
import com.xgwc.order.entity.vo.XgwcPlatformRefundQueryVo;
import com.xgwc.order.service.IXgwcPlatformRefundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/xgwcPlatformRefund")
public class XgwcPlatformRefundController extends BaseController {
    @Autowired
    private IXgwcPlatformRefundService xgwcPlatformRefundService;

    /**
     * 查询平台售后单列表
     */
    @MethodDesc("查询未录平台售后单列表")
    @PreAuthorize("@ss.hasPermission('order:platformRefund:notUseList')")
    @GetMapping("/notUseList")
    public ApiResult<XgwcPlatformRefundDto> notUseList(XgwcPlatformRefundQueryVo refundQueryVo) {
        return getDataTable(xgwcPlatformRefundService.notUseList(refundQueryVo));
    }

    /**
     * 查询未录平台售后单联系人
     */
    @MethodDesc("查询未录平台售后单联系人")
    @PreAuthorize("@ss.hasPermission('order:platformRefund:viewContact')")
    @GetMapping("/getContact/{refundNo}")
    public ApiResult<XgwcPlatformRefundContactDto> getContact(@PathVariable("refundNo") String refundNo) {
        return success(xgwcPlatformRefundService.getContact(refundNo));
    }

}