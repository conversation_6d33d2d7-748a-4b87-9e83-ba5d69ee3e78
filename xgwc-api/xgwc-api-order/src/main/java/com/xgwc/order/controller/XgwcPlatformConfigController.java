package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.XgwcPlatformConfig;
import com.xgwc.order.service.impl.XgwcPlatformConfigService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/xgwcPlatformConfig")
public class XgwcPlatformConfigController extends BaseController {

    @Autowired
    private XgwcPlatformConfigService xgwcPlatformConfigService;

    @MethodDesc("获取当前旺店通配置")
    @GetMapping("/getCurrentConfig")
    public ApiResult<XgwcPlatformConfig> getCurrentConfig() {
        return ApiResult.ok(xgwcPlatformConfigService.getCurrentConfig());
    }

    @MethodDesc("保存旺店通配置")
    @PutMapping("/save")
    public ApiResult<String> save(@RequestBody @Valid XgwcPlatformConfig config) {
        xgwcPlatformConfigService.save(config);
        return ApiResult.ok();
    }

}