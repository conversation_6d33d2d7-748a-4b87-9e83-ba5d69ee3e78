package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.ReportBrandNightPerformanceStatsDto;
import com.xgwc.order.entity.vo.ReportBrandNightPerformanceStatsQueryVo;
import com.xgwc.order.service.IReportBrandNightPerformanceStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/reportBrandNightPerformanceStats")
public class ReportBrandNightPerformanceStatsController extends BaseController {

    @Autowired
    private IReportBrandNightPerformanceStatsService reportBrandNightPerformanceStatsService;

    /**
     * 查询品牌商晚间业绩统计列表
     */
    @MethodDesc("查询品牌商晚间业绩统计列表")
    @PreAuthorize("@ss.hasPermission('reportBrandNightPerformanceStats:reportBrandNightPerformanceStats:list')")
    @GetMapping("/listPage")
    public ApiResult<ReportBrandNightPerformanceStatsDto> listPage(ReportBrandNightPerformanceStatsQueryVo queryVo) {
        startPage();
        List<ReportBrandNightPerformanceStatsDto> list = reportBrandNightPerformanceStatsService.listPage(queryVo);
        return getDataTable(list);
    }

}