package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.order.entity.OrderInvoiceApply;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderInvoiceDto {

    @FieldDesc("发票ID")
    private Long id;

    @FieldDesc("流程审批任务Id")
    private Long taskId;

    @FieldDesc("审批流程实例Id")
    private Long executionId;

    @FieldDesc("开票企业")
    private String invoicingCompany;

    @FieldDesc("抬头类型：1企业，2个人")
    private Integer titleType;

    @FieldDesc("发票类型：1.电子普票，2.纸质普票，3.电子专票，4.纸质专票")
    private Integer invoiceType;

    @FieldDesc("开票金额")
    private BigDecimal invoiceAmount;

    @FieldDesc("购方名称")
    private String buyer;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("客户ID")
    private String taobaoId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("谈单人")
    private String saleManName;

    @FieldDesc("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @FieldDesc("最后审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastApproveTime;

    @FieldDesc("审批状态: 审批中 ING, 已通过 PASS, 驳回 REJECT, 终止 CANCEL, 撤回 WITHDRAW")
    private String applyStatus;

}
