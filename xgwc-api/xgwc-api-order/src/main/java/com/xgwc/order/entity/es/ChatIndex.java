package com.xgwc.order.entity.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;

/**
 * 案例索引
 */
@Data
@IndexName("xgwc-chat-index")
public class ChatIndex {

    /**
     * 主键id
     */
    @IndexId
    private String id;

    /**
     * 业务主键
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long chatId;

    /**
     * 标题
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String title;

    /**
     * 关键词
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String chatContent;

    @IndexField(
            fieldType = FieldType.TEXT,
            analyzer = "hierarchy_analyzer",  // 自定义分词器
            searchAnalyzer = "hierarchy_analyzer"
    )
    private String typeCodes;

    @IndexField(fieldType = FieldType.LONG)
    private Long brandId;

    @IndexField(fieldType = FieldType.LONG)
    private Long franchiseId;

    @IndexField(fieldType = FieldType.LONG)
    private Long viewCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String create_time;

}
