package com.xgwc.order.dao;

import com.xgwc.order.entity.BrandCsCommissionRateConfig;
import com.xgwc.order.entity.vo.BrandCsCommissionRateConfigQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface BrandCsCommissionRateConfigMapper {

    /**
     * 查询客服提成完成率配置表
     *
     * @param id 客服提成完成率配置表主键
     * @return 客服提成完成率配置表
     */
    BrandCsCommissionRateConfig getByIdBrandId(@Param("id") Long id, @Param("brandId") Long brandId);

    /**
     * 查询客服提成完成率配置表列表
     *
     * @param queryVo 客服提成完成率配置表
     * @return 客服提成完成率配置表集合
     */
    List<BrandCsCommissionRateConfig> listByQuery(BrandCsCommissionRateConfigQueryVo queryVo);

    /**
     * 新增客服提成完成率配置表
     *
     * @param configList 客服提成完成率配置表
     * @return 结果
     */
    int insertList(@Param("configList") List<BrandCsCommissionRateConfig> configList);

    /**
     * 修改客服提成完成率配置表
     *
     * @param config 客服提成完成率配置表
     * @return 结果
     */
    int update(BrandCsCommissionRateConfig config);

    /**
     * 批量删除客服提成完成率配置表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int remove(@Param("ids") Long[] ids, @Param("brandId") Long brandId);

}
