package com.xgwc.order.service;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.order.entity.es.CaseIndex;
import com.xgwc.order.entity.es.CaseVo;
import com.xgwc.order.entity.es.ChatIndex;
import com.xgwc.order.entity.es.ChatVo;

public interface EsService {

    /**
     * 搜索案列库
     * @param caseVo 参数
     * @return 案列数据
     */
    JSONObject searchCaseData(CaseVo caseVo);

    /**
     * 搜索聊天记录
     * @param chatVo 参数
     * @return 案列数据
     */
    JSONObject searchChatData(ChatVo chatVo);

    /**
     * 添加案列数据
     * @param caseIndex 数据
     */
    boolean addCaseData(CaseIndex caseIndex);

    /**
     * 添加案列数据
     * @param chatIndex 数据
     */
    boolean addChatData(ChatIndex chatIndex);

    /**
     * 删除案列数据
     * @param caseIndex 数据
     * @return 是否成功
     */
    boolean deleteCaseData(CaseIndex caseIndex);

    /**
     * 删除聊天数据
     * @param chatIndex 数据
     * @return 是否成功
     */
    boolean deleteChatData(ChatIndex chatIndex);

    /**
     * 修改聊天记录
     * @param chatIndex 数据
     * @return 是否成功
     */
    boolean updateChatData(ChatIndex chatIndex);

    /**
     * 修改案例数据
     * @param caseIndex 数据
     * @return 是否成功
     */
    boolean updateCaseData(CaseIndex caseIndex);
}
