package com.xgwc.order.dao;

import java.util.List;
import com.xgwc.order.entity.InboundSales;
import com.xgwc.order.entity.vo.InboundSalesVo;
import com.xgwc.order.entity.dto.InboundSalesDto;
import com.xgwc.order.entity.vo.InboundSalesQueryVo;


public interface InboundSalesMapper {
    /**
     * 查询销售进线
     * @param id 销售进线主键
     * @return 销售进线
     */
    InboundSalesDto selectInboundSalesById(Long id);

    /**
     * 查询销售进线列表
     * @param inboundSales 销售进线
     * @return 销售进线集合
     */
    List<InboundSalesDto> selectInboundSalesList(InboundSalesQueryVo inboundSales);

    /**
     * 新增销售进线
     * @param inboundSales 销售进线
     * @return 结果
     */
    int insertInboundSales(InboundSales inboundSales);

    /**
     * 修改销售进线
     * @param inboundSales 销售进线
     * @return 结果
     */
    int updateInboundSales(InboundSales inboundSales);

    /**
     * 删除销售进线
     * @param id 销售进线主键
     * @return 结果
     */
    int deleteInboundSalesById(Long id);

    /**
     * 批量插入销售进线
     * @param inboundSalesList 销售进线集合
     * @return 结果
     */
    int batchInsert(List<InboundSales> inboundSalesList);
}
