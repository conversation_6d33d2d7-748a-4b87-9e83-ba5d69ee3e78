package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.order.entity.OrderExpensesApply;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单报销申请
 */
@Data
public class OrderExpensesDto {

    @FieldDesc("报销ID")
    private Long id;

    @FieldDesc("流程审批任务Id")
    private Long taskId;

    @FieldDesc("审批流程实例Id")
    private Long executionId;

    @FieldDesc("实收金额")
    private BigDecimal actualAmount;

    @FieldDesc("退款金额")
    private BigDecimal refundAmount;

    @FieldDesc("报销差额")
    private BigDecimal differenceAmount;

    @FieldDesc("申请人")
    private String applyUserName;

    @FieldDesc("客户id")
    private String customerNo;

    @FieldDesc("审批状态: 审批中 ING, 已通过 PASS, 驳回 REJECT, 终止 CANCEL, 撤回 WITHDRAW")
    private String applyStatus;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("谈单人")
    private String saleManName;

    @FieldDesc("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @FieldDesc("最后审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastApproveTime;

    @FieldDesc("所属品牌商 ")
    private String brandName;
}
