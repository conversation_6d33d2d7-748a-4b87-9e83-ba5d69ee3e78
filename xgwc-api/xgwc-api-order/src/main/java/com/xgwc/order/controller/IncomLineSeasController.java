package com.xgwc.order.controller;

import java.util.List;

import com.xgwc.order.entity.dto.IncomLineDto;
import com.xgwc.order.entity.vo.IncomLineQueryVo;
import com.xgwc.order.entity.vo.IncomLineTransferVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.order.service.IIncomLineSeasService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

@RestController
@RequestMapping("/incomLine/seas")
public class IncomLineSeasController extends BaseController {
    @Autowired
    private IIncomLineSeasService incomLineSeasService;

    /**
     * 查询客户公海列表
     */
    @MethodDesc("查询客户公海列表")
//    @PreAuthorize("@ss.hasPermission('incomLine:seas:list')")
    @GetMapping("/list")
    public ApiResult<IncomLineDto> list(IncomLineQueryVo incomLineSeas) {
        startPage();
        List<IncomLineDto> list = incomLineSeasService.selectIncomLineSeasList(incomLineSeas);
        return getDataTable(list);
    }

    @MethodDesc("转移客户公海")
    @PreAuthorize("@ss.hasPermission('incomLine:seas:transfer')")
    @Log(title = "进线管理", businessType = BusinessType.UPDATE)
    @PutMapping("/transfer")
    public ApiResult transfer(@RequestBody IncomLineTransferVo incomLine) {
        return toAjax(incomLineSeasService.transfer(incomLine));
    }

    @MethodDesc("领取客户公海")
    @PreAuthorize("@ss.hasPermission('incomLine:seas:collect')")
    @Log(title = "进线管理", businessType = BusinessType.UPDATE)
    @PutMapping("/collect/{id}")
    public ApiResult collect(@PathVariable Long id) {
        return toAjax(incomLineSeasService.collect(id));
    }

}
