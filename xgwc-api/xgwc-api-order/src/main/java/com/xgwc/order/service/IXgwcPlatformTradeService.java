package com.xgwc.order.service;

import com.xgwc.order.entity.XgwcPlatformTrade;
import com.xgwc.order.entity.XgwcPlatformTradeDetails;
import com.xgwc.order.entity.dto.XgwcPlatformTradeByBrandVO;
import com.xgwc.order.entity.vo.XgwcPlatformTradeQueryVo;

import java.util.List;

public interface IXgwcPlatformTradeService {


    void saveXgwcPlatformTrade(List<XgwcPlatformTrade> tradeList, List<XgwcPlatformTradeDetails> tradeDetailsList, Long brandOwnerId);

    /**
     * 品牌商查询平台交易单列表
     *
     * @param tradeQueryVo 平台交易单
     * @return 平台交易单集合
     */
    List<XgwcPlatformTradeByBrandVO> brandList(XgwcPlatformTradeQueryVo tradeQueryVo);

    /**
     * 服务商查询平台交易单列表
     *
     * @param tradeQueryVo 平台交易单
     * @return 平台交易单集合
     */
    List<XgwcPlatformTradeByBrandVO> serviceProviderList(XgwcPlatformTradeQueryVo tradeQueryVo);

}