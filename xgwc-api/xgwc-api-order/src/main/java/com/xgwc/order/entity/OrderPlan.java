package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class OrderPlan {

private static final long serialVersionUID=1L;

    /**  */
    private Long id;

    /** 订单id */
    private Long oderId;

    /** 交稿状态：0未交稿 1交初稿 2交定稿 3制作中 */
    private Integer archiveType;

    /** 操作时间 */
    private Date archiveTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 是否删除 */
    private Integer isDel;



}