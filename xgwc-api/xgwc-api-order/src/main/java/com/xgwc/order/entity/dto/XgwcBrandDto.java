package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.dto
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  09:43
 */

/**
 * 品牌管理-品牌列表
 */
@Data
public class XgwcBrandDto {

    /**
     * 品牌id
     */
    @FieldDesc("品牌id")
    private Long brandId;

    /**
     * 品牌商id
     */
    @FieldDesc("品牌商id")
    private Long brandOwnerId;

    /**
     * 品牌名称
     */
    @FieldDesc("品牌名称")
    private String brandName;

    /**
     * 排序
     */
    @FieldDesc("排序：越小越前")
    private Integer sort;

    /**
     * 状态
     */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    /**
     * 是否删除
     */
    @FieldDesc("是否删除：0否，1是")
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
