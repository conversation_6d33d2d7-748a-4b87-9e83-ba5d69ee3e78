package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class DispatchVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("已派设计师数量")
    private Integer dispatchNumber;

    @FieldDesc("1 普通单 2 紧急单")
    private Integer dispatchType;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("需派设计师数")
    private Integer needDispatchNumber;

    @FieldDesc("订单Id")
    private Long orderId;

    @FieldDesc("领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    @FieldDesc("退回次数")
    private Integer returnNumber;

    @FieldDesc("品牌商Id")
    private Long brandId;

}
