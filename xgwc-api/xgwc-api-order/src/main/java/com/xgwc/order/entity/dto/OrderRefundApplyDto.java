package com.xgwc.order.entity.dto;

import com.xgwc.order.entity.OrderRefundApply;
import com.xgwc.order.entity.OrderRefundCommision;
import com.xgwc.order.entity.OrderRefundPay;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单报销申请
 */
@Data
public class OrderRefundApplyDto extends OrderRefundApply {

    /**
     * 客户id
     */
    private String customerNo;

    /**
     * 谈单人
     */
    private String saleManName;

    /**
     * 下单日期
     */
    private String orderDate;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 客户id
     */
    private String taobaoId;

    /**
     * 实收金额
     */
    private BigDecimal amount;
    /**
     * 支付列表
     */
    private List<OrderRefundPay> payList;

    /**
     * 佣金列表
     */
    private List<OrderRefundCommision> commisionList;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 退款总金额
     */
    private BigDecimal totalRefundAmount;

    private List<AfterSalesFlowLogDto> flowLogList;
}
