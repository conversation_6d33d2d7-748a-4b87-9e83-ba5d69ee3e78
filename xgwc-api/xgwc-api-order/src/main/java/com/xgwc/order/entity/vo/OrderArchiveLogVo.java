package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class OrderArchiveLogVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("交稿id")
    private Long archiveId;

    @FieldDesc("审批人")
    private String review;

    @FieldDesc("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    @FieldDesc("审批状态")
    private Integer reviewStatus;

    @FieldDesc("审批结果")
    private String reviewMsg;



}
