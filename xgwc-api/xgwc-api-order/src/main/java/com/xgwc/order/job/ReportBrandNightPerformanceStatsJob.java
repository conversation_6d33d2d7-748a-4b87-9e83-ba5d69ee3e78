package com.xgwc.order.job;

import com.xgwc.common.util.DateUtils;
import com.xgwc.order.dao.BrandNightPerformanceConfigMapper;
import com.xgwc.order.entity.BrandNightPerformanceConfig;
import com.xgwc.order.entity.vo.BrandNightPerformanceConfigQueryVo;
import com.xgwc.order.service.impl.ReportBrandNightPerformanceStatsServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReportBrandNightPerformanceStatsJob {

    @Resource
    private BrandNightPerformanceConfigMapper brandNightPerformanceConfigMapper;
    @Resource
    private ReportBrandNightPerformanceStatsServiceImpl reportBrandNightPerformanceStatsServiceImpl;

    @Scheduled(cron = "0 15 0 * * *")
    public void job() {
        log.info("晚间业绩统计任务开始执行....");
        List<BrandNightPerformanceConfig> allConfigList = brandNightPerformanceConfigMapper.listByQuery(BrandNightPerformanceConfigQueryVo.builder().build());
        if (allConfigList.isEmpty()) {
            log.info("没有晚间业绩配置, 统计任务执行完毕....");
            return;
        }
        Date now = new Date();
        allConfigList.forEach(BrandNightPerformanceConfig::initRangeList);
        Map<Long, Map<Long, List<BrandNightPerformanceConfig>>> map = allConfigList.stream().collect(Collectors.groupingBy(BrandNightPerformanceConfig::getBrandId, Collectors.groupingBy(BrandNightPerformanceConfig::getFranchiseeId)));
        map.forEach((brandId, franchiseeConfigListMap) -> franchiseeConfigListMap.forEach((franchiseeId, configList) -> {
            for (int i = 0; i < 2; i++) {
                Date month = DateUtils.addMonth(now, -i);
                try {
                    reportBrandNightPerformanceStatsServiceImpl.initStatsData(brandId, franchiseeId, configList, month);
                } catch (Exception e) {
                    log.error("品牌商[{}]下加盟商[{}][{}]月的任务处理时出现异常:", brandId, franchiseeId, DateFormatUtils.format(month, "yyyy-MM"), e);
                }
            }
        }));
        log.info("晚间业绩统计任务执行完毕....");
    }

}