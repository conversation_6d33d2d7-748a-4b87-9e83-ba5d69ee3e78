package com.xgwc.order.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

@Data
public class DesignerStatisticsScoreQueryVo {

    private static final long serialVersionUID=1L;


    @FieldDesc("所属品牌商")
    private Long brandId;

    @FieldDesc("设计师id")
    @NotNull(message = "设计师id必填")
    private Long designerId;

    @FieldDesc("评分类型:0 加盟商 1被换评价")
    private Long scoreType;

    @FieldDesc("评分差：1好评，2中评，3差评")
    private Long scoreModel;


}
