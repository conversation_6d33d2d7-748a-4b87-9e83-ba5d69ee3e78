package com.xgwc.order.entity;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class OrderDesignerUpdate {

    @FieldDesc("id")
    private Long id;

    @FieldDesc("是否有责：1有 0无")
    private Integer isDuty;

    @FieldDesc("订单id")
    private Long oderId;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("设计师ID")
    private Long oldDesignerId;

    @FieldDesc("设计师名称")
    private String oldDesignerName;

    @FieldDesc("设计师电话")
    private String oldDesignerPhone;

    @FieldDesc("设计师业务类型")
    private String oldDesignerBusiness;

    @FieldDesc("设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    private String designerPhone;

    @FieldDesc("设计师业务类型")
    private String designerBusiness;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;
}
