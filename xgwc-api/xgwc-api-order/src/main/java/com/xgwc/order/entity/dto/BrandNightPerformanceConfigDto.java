package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.util.json.JsonUtils;
import com.xgwc.order.entity.NightConfigRange;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BrandNightPerformanceConfigDto {

    @FieldDesc("主键ID")
    @Excel(name = "主键ID")
    private Long id;

    @FieldDesc("品牌商ID")
    @Excel(name = "品牌商ID")
    private Long brandId;

    @FieldDesc("加盟商ID")
    @Excel(name = "加盟商ID")
    private Long franchiseeId;
    private String franchiseeName;

    @FieldDesc("部门ID")
    @Excel(name = "部门ID")
    private Long departmentId;
    private String departmentName;

    @JsonIgnore
    @FieldDesc("晚间时间段配置JSON")
    @Excel(name = "晚间时间段配置JSON")
    private String nightTimeJson;

    @FieldDesc("创建人id")
    @Excel(name = "创建人id")
    private Long createById;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人id")
    @Excel(name = "修改人id")
    private Long updateById;

    @FieldDesc("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;


    @Valid
    @NotNull
    @FieldDesc("区间配置")
    private List<NightConfigRange> rangeList;

    public BrandNightPerformanceConfigDto initRangeList() {
        this.rangeList = JsonUtils.toList(nightTimeJson, NightConfigRange.class);
        return this;
    }

}