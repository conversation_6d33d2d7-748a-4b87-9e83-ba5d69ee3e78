package com.xgwc.order.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class AfterSalesFlowLogDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("业务id")
    @Excel(name = "业务id")
    private Long businessId;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("节点类型（1:开始，2:售后审核）")
    @Excel(name = "节点类型（1:开始，2:售后审核）")
    private Long nodeType;

    @FieldDesc("操作人ID")
    @Excel(name = "操作人ID")
    private Long operatorId;

    @FieldDesc("操作人姓名")
    @Excel(name = "操作人姓名")
    private String operatorName;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("顺序")
    @Excel(name = "顺序")
    private Long sortOrder;

    @FieldDesc("节点状态（0发起审批，1通过，2拒绝）")
    @Excel(name = "节点状态（0发起审批，1通过，2拒绝）")
    private Long status;

    @FieldDesc("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("businessId",getBusinessId())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("id",getId())
            .append("nodeType",getNodeType())
            .append("operatorId",getOperatorId())
            .append("operatorName",getOperatorName())
            .append("remark",getRemark())
            .append("sortOrder",getSortOrder())
            .append("status",getStatus())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
