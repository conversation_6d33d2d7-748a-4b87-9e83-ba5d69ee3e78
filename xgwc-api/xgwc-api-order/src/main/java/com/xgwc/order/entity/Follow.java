package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class Follow {

private static final long serialVersionUID=1L;

    /** 售后待审核ID：xgwc_after_agency_audit主键 */
    private Long agencyAuditId;

    /** 协商状态:1待协商/2协商中/3协商成功/4协商失败 */
    private Integer consultType;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 主键ID */
    private Long id;

    /** 跟进说明 */
    private String remark;

    /** 回访时间 */
    private Date returnTime;

    /** 状态：0正常，1非正常 */
    private Integer status;

    /** 更新时间 */
    private Date updateTime;

    /** 跟进人ID */
    private Long followUserId;

}