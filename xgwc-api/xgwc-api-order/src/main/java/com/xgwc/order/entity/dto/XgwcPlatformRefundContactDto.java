package com.xgwc.order.entity.dto;

import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Setter
@Getter
public class XgwcPlatformRefundContactDto {

    @FieldDesc("平台订单编号")
    private String tradeNo;

    @FieldDesc("买家实付金额")
    @Excel(name = "买家实付金额")
    private BigDecimal payAmount;

    @FieldDesc("退款金额")
    private BigDecimal refundAmount;

    private List<GoodsContactData> goodsDetailsContactList;


    @Setter
    @Getter
    public static class GoodsContactData {

        @FieldDesc("退款商品ID")
        private String goodsId;

        @FieldDesc("退款商品标题")
        private String goodsName;

        @FieldDesc("退款金额")
        private BigDecimal refundAmount;

        @FieldDesc("业务分类id")
        private Long bizType;
        @FieldDesc("业务分类")
        private String bizTypeName;


        @FieldDesc("加盟商名称")
        private String franchiseName;


        @FieldDesc("部门负责人名称")
        private String deptPrincipalName;
        @FieldDesc("部门负责人电话")
        private String deptPrincipalPhone;
    }

}