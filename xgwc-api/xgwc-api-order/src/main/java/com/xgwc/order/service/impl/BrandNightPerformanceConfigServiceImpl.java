package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.BizUtils;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.PageUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.BrandNightPerformanceConfigMapper;
import com.xgwc.order.dao.CommonMapper;
import com.xgwc.order.entity.BrandNightPerformanceConfig;
import com.xgwc.order.entity.cmd.BrandNightPerformanceConfigCmd;
import com.xgwc.order.entity.cmd.BrandNightPerformanceConfigUpdateCmd;
import com.xgwc.order.entity.dto.BrandNightPerformanceConfigDto;
import com.xgwc.order.entity.dto.DeptDto;
import com.xgwc.order.entity.vo.BrandNightPerformanceConfigQueryVo;
import com.xgwc.order.service.IBrandNightPerformanceConfigService;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class BrandNightPerformanceConfigServiceImpl implements IBrandNightPerformanceConfigService {

    @Resource
    private CommonMapper commonMapper;
    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private BrandNightPerformanceConfigMapper brandNightPerformanceConfigMapper;


    /**
     * 新增品牌商晚间业绩配置
     *
     * @param cmd 品牌商晚间业绩配置
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(BrandNightPerformanceConfigCmd cmd) {
        BrandNightPerformanceConfig baseConfig = this.checkCmdAndInitConfig(cmd);
        List<BrandNightPerformanceConfig> dbList = brandNightPerformanceConfigMapper.listByQuery(BrandNightPerformanceConfigQueryVo.builder().brandId(baseConfig.getBrandId()).franchiseeId(cmd.getFranchiseeId()).departmentIds(cmd.getDepartmentIds()).build());
        if (!dbList.isEmpty()) {
            throw new ApiException(String.format("部门[%s]已配置, 不能重复", commonMapper.listFranchiseDeptByIds(dbList.stream().map(BrandNightPerformanceConfig::getDepartmentId).toList(), null, null).stream().map(DeptDto::getDeptName).collect(Collectors.joining(","))));
        }

        List<BrandNightPerformanceConfig> configList = Lists.newArrayList();
        cmd.getDepartmentIds().forEach(department -> {
            BrandNightPerformanceConfig config = BeanUtil.copyProperties(baseConfig, BrandNightPerformanceConfig.class);
            config.setDepartmentId(department);
            configList.add(config);
        });

        configList.forEach(config -> brandNightPerformanceConfigMapper.insert(config));
        return cmd.getDepartmentIds().size();
    }

    private BrandNightPerformanceConfig checkCmdAndInitConfig(BrandNightPerformanceConfigCmd cmd) {
        BizUtils.assertByFlag(SecurityUtils.getSysUser().isBrandUser(), "非品牌商用户不能操作");
        Set<String> franchiseIds = StringUtils.stringToSet(SecurityUtils.getSysUser().getFranchiseIds());
        BizUtils.assertByFlag(franchiseIds.contains(cmd.getFranchiseeId().toString()), "存在异常加盟商id");

        BrandNightPerformanceConfig config = BeanUtil.copyProperties(cmd, BrandNightPerformanceConfig.class);
        config.setBrandId(SecurityUtils.getSysUser().getBrandId());
        config.initRangeRateJson(cmd.getRangeList());
        config.setCreateById(SecurityUtils.getUserId());
        config.setCreateTime(new Date());
        config.setUpdateTime(config.getCreateTime());
        config.setUpdateById(config.getCreateById());
        return config;
    }

    /**
     * 查询品牌商晚间业绩配置列表
     *
     * @param queryVo 品牌商晚间业绩配置
     * @return 品牌商晚间业绩配置
     */
    @Override
    public ApiListResult<BrandNightPerformanceConfigDto> listPage(BrandNightPerformanceConfigQueryVo queryVo) {
        if (!SecurityUtils.getSysUser().isBrandUser()) {
            return ApiListResult.isOk();
        }
        queryVo.setBrandId(SecurityUtils.getSysUser().getBrandId());
        PageUtils.startPage();
        List<BrandNightPerformanceConfig> list = brandNightPerformanceConfigMapper.listByQuery(queryVo);
        return ApiListResult.isOk(new PageInfo<>(list).getTotal(), this.convertDto(list));
    }

    /**
     * 查询品牌商晚间业绩配置
     *
     * @param id 品牌商晚间业绩配置主键
     * @return 品牌商晚间业绩配置
     */
    @Override
    public BrandNightPerformanceConfigDto get(Long id) {
        if (!SecurityUtils.getSysUser().isBrandUser()) {
            return null;
        }
        BrandNightPerformanceConfig config = brandNightPerformanceConfigMapper.getByIdBrandId(id, SecurityUtils.getSysUser().getBrandId());
        if (config == null) {
            return null;
        }
        return this.convertDto(ImmutableList.of(config)).get(0).initRangeList();
    }

    private List<BrandNightPerformanceConfigDto> convertDto(List<BrandNightPerformanceConfig> configList) {
        List<BrandNightPerformanceConfigDto> dtoList = Lists.newArrayList();
        if (!configList.isEmpty()) {
            List<Long> franchiseIdList = configList.stream().map(BrandNightPerformanceConfig::getFranchiseeId).distinct().toList();
            Map<Long, String> franchiseNameMap = franchiseFeign.listByIds(franchiseIdList).stream().collect(Collectors.toMap(FranchiseDto::getId, FranchiseDto::getFranchiseName));
            List<Long> deptIdList = configList.stream().map(BrandNightPerformanceConfig::getDepartmentId).toList();
            Map<Long, String> deptNameMap = commonMapper.listFranchiseDeptByIds(deptIdList, null, null).stream().collect(Collectors.toMap(DeptDto::getDeptId, DeptDto::getDeptName));

            configList.forEach(config -> {
                BrandNightPerformanceConfigDto configDto = BeanUtil.copyProperties(config, BrandNightPerformanceConfigDto.class);
                configDto.setFranchiseeName(franchiseNameMap.get(config.getFranchiseeId()));
                configDto.setDepartmentName(deptNameMap.get(config.getDepartmentId()));
                dtoList.add(configDto);
            });
        }
        return dtoList;
    }


    /**
     * 修改品牌商晚间业绩配置
     *
     * @param cmd 品牌商晚间业绩配置
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BrandNightPerformanceConfigUpdateCmd cmd) {
        BizUtils.assertByFlag(cmd.getId() != null, "配置id不能为空");
        BizUtils.assertByFlag(SecurityUtils.getSysUser().isBrandUser(), "非品牌商用户不能操作");
        BrandNightPerformanceConfig config = brandNightPerformanceConfigMapper.getByIdBrandId(cmd.getId(), SecurityUtils.getSysUser().getBrandId());
        BizUtils.assertByFlag(config != null, "错误的配置id");

        config.initRangeRateJson(cmd.getRangeList());
        config.setUpdateTime(DateUtils.getNowDate());
        config.setUpdateById(SecurityUtils.getUserId());
        return brandNightPerformanceConfigMapper.update(config);
    }

    /**
     * 批量删除品牌商晚间业绩配置
     *
     * @param ids 需要删除的品牌商晚间业绩配置主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int remove(Long[] ids) {
        return brandNightPerformanceConfigMapper.remove(ids, SecurityUtils.getSysUser().getBrandId());
    }

}
