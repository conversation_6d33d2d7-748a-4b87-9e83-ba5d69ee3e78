package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;
import java.util.List;

@Data
public class OrderArchiveDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("定稿id")
    @Excel(name = "定稿id")
    private Long id;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long oderId;

    @FieldDesc("定稿名称")
    @Excel(name = "定稿名称")
    private String archiveName;

    @FieldDesc("定稿预览图")
    @Excel(name = "定稿预览图")
    private String archiveImg;

    @FieldDesc("上传定稿源文件")
    @Excel(name = "上传定稿源文件")
    private String archiveFiles;

    /** 业务id */
    @FieldDesc("业务id")
    private Long businessId;

    /** 业务名称 */
    @FieldDesc("业务名称")
    private String businessName;

    @FieldDesc("审批状态")
    @Excel(name = "审批状态")
    private Integer reviewStatus;

    @FieldDesc("链接")
    private String linkUrl;

    @FieldDesc("审批结果")
    @Excel(name = "审批结果")
    private String reviewMsg;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    private List<OrderArchiveLogDto> logs;

    private List<OrderScoreDto> scores;



    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("oderId",getOderId())
            .append("archiveName",getArchiveName())
            .append("archiveImg",getArchiveImg())
            .append("archiveFiles",getArchiveFiles())
            .append("reviewStatus",getReviewStatus())
            .append("reviewMsg",getReviewMsg())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
