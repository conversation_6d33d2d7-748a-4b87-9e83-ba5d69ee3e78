package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcChannelDto;
import com.xgwc.order.entity.param.XgwcChannelParam;
import com.xgwc.order.entity.vo.XgwcChannelVo;
import com.xgwc.order.service.XgwcChannelService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  18:14
 */

/**
 * 服务管理-渠道管理
 */
@RestController
@RequestMapping("/xgwcChannel")
@Slf4j
public class XgwcChannelController extends BaseController {

    @Resource
    private XgwcChannelService xgwcChannelService;

    /**
     * @param XgwcChannelParam 查询条件
     * @return 渠道列表
     * 查询渠道列表
     */
    @MethodDesc("查询渠道列表")
    @PreAuthorize("@ss.hasPermission('order:channel:list')")
    @PostMapping("/getXgwcChannelList")
    public ApiResult<XgwcChannelVo> getXgwcChannelList(@RequestBody XgwcChannelParam XgwcChannelParam) {
        startPage();
        return getDataTable(xgwcChannelService.getXgwcChannelList(XgwcChannelParam));
    }

    /**
     * 获取渠道下拉框
     *
     * @return 渠道下拉框
     */
    @MethodDesc("获取渠道下拉框")
    @GetMapping("/getChannelDropDown")
    public ApiResult getXgwcChannelById() {
        try {
            XgwcChannelParam xgwcChannelParam = new XgwcChannelParam();
            xgwcChannelParam.setStatus(0);
            List<XgwcChannelVo> result = xgwcChannelService.getXgwcChannelList(xgwcChannelParam);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取品牌下拉列表失败", e);
            return ApiResult.error("获取品牌下拉列表失败");
        }
    }

    /**
     * @param xgwcChannelDto 新增渠道信息
     * @return 插入结果
     * 新增渠道信息
     */
    @MethodDesc("新增渠道信息")
    @PreAuthorize("@ss.hasPermission('order:channel:insert')")
    @Submit(fileds = "userId")
    @PostMapping("/saveXgwcChannel")
    public ApiResult saveXgwcChannel(@RequestBody XgwcChannelDto xgwcChannelDto) {
       return xgwcChannelService.saveXgwcChannel(xgwcChannelDto);
    }

    /**
     * @param channelId 渠道id
     * @return 渠道信息
     * 根据渠道id查询渠道信息
     */
    @MethodDesc("根据渠道id查询渠道信息")
    @PreAuthorize("@ss.hasPermission('order:channel:update')")
    @GetMapping("/getXgwcChannelById/{channelId}")
    public ApiResult getXgwcChannelById(@PathVariable Integer channelId) {
        return xgwcChannelService.getXgwcChannelById(channelId);
    }

    /**
     * @param xgwcChannelDto 渠道信息
     * @return 修改结果
     * 修改渠道信息
     */
    @MethodDesc("修改渠道信息")
    @PreAuthorize("@ss.hasPermission('order:channel:update')")
    @Submit(fileds = "userId")
    @PostMapping("/updateXgwcChannel")
    public ApiResult updateXgwcChannelById(@RequestBody XgwcChannelDto xgwcChannelDto) {
      return xgwcChannelService.updateXgwcChannel(xgwcChannelDto);
    }

    /**
     * @param channelId 渠道id
     * @return 渠道信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('order:channel:updateStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "channelId") Integer channelId,
                                 @RequestParam(value = "status") Integer status) {
       return xgwcChannelService.updateStatusById(channelId,status);
    }

}
