package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class XgwcShopFranchiseBusinessQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("业务id")
    private Long businessId;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("店铺id")
    private Long shopFranchiseId;



}
