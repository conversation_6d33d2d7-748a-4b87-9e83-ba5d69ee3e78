package com.xgwc.order.service;


import com.xgwc.order.entity.XgwcPlatformGoods;
import com.xgwc.order.entity.XgwcPlatformGoodsSpec;
import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.XgwcPlatformRefundDetails;
import com.xgwc.order.entity.XgwcPlatformShop;
import com.xgwc.order.entity.XgwcPlatformTrade;
import com.xgwc.order.entity.XgwcPlatformTradeDetails;
import com.xgwc.order.entity.vo.Second;
import com.xgwc.order.util.WdtClient;

import java.io.IOException;
import java.util.Date;
import java.util.List;

public interface XgwcPlatformService {

    List<XgwcPlatformShop> pullShop(WdtClient wdtClient, Long brandOwnerId) throws IOException;

    Second<List<XgwcPlatformGoods>, List<XgwcPlatformGoodsSpec>> pullGoods(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException;

    Second<List<XgwcPlatformTrade>, List<XgwcPlatformTradeDetails>> pullTrade(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException;

    Second<XgwcPlatformTrade, List<XgwcPlatformTradeDetails>> pullTradeByTradeNo(String tradeNo, WdtClient wdtClient, Long brandOwnerId) throws IOException;

    Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> pullRefund(Date startTime, Date endTime, WdtClient wdtClient, Long brandOwnerId) throws IOException;

    Second<List<XgwcPlatformRefund>, List<XgwcPlatformRefundDetails>> pullRefundByTradeNo(String tradeNo, WdtClient wdtClient, Long brandOwnerId) throws IOException;

}