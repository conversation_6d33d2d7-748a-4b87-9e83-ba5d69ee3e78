package com.xgwc.order.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.order.entity.cmd.BrandCsCommissionRateConfigCmd;
import com.xgwc.order.entity.cmd.BrandCsCommissionRateConfigUpdateCmd;
import com.xgwc.order.entity.dto.BrandCsCommissionRateConfigDto;
import com.xgwc.order.entity.vo.BrandCsCommissionRateConfigQueryVo;
import com.xgwc.order.service.IBrandCsCommissionRateConfigService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/brandCsCommissionRateConfig")
public class BrandCsCommissionRateConfigController extends BaseController {

    @Resource
    private IBrandCsCommissionRateConfigService brandCsCommissionRateConfigService;

    /**
     * 查询客服提成完成率配置表列表
     */
    @MethodDesc("查询客服提成完成率配置表列表")
    @PreAuthorize("@ss.hasPermission('brandCsCommissionRateConfig:brandCsCommissionRateConfig:list')")
    @GetMapping("/listPage")
    public ApiResult<ApiListResult<BrandCsCommissionRateConfigDto>> listPage(@RequestBody @Valid BrandCsCommissionRateConfigQueryVo queryVo) {
        return ApiResult.ok(brandCsCommissionRateConfigService.listPage(queryVo));
    }

    /**
     * 获取客服提成完成率配置表详细信息
     */
    @MethodDesc("获取客服提成完成率配置表详细信息")
    @PreAuthorize("@ss.hasPermission('brandCsCommissionRateConfig:brandCsCommissionRateConfig:query')")
    @GetMapping(value = "/get")
    public ApiResult<BrandCsCommissionRateConfigDto> getInfo(@RequestParam("id") Long id) {
        return success(brandCsCommissionRateConfigService.get(id));
    }

    /**
     * 新增客服提成完成率配置表
     */
    @MethodDesc("新增客服提成完成率配置表")
    @PreAuthorize("@ss.hasPermission('brandCsCommissionRateConfig:brandCsCommissionRateConfig:add')")
    @Log(title = "客服提成完成率配置表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ApiResult add(@RequestBody @Valid BrandCsCommissionRateConfigCmd cmd) {
        return toAjax(brandCsCommissionRateConfigService.add(cmd));
    }

    /**
     * 修改客服提成完成率配置表
     */
    @MethodDesc("修改客服提成完成率配置表")
    @PreAuthorize("@ss.hasPermission('brandCsCommissionRateConfig:brandCsCommissionRateConfig:update')")
    @Log(title = "客服提成完成率配置表", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public ApiResult update(@RequestBody @Valid BrandCsCommissionRateConfigUpdateCmd cmd) {
        return toAjax(brandCsCommissionRateConfigService.update(cmd));
    }

    /**
     * 删除客服提成完成率配置表
     */
    @MethodDesc("删除客服提成完成率配置表")
    @PreAuthorize("@ss.hasPermission('brandCsCommissionRateConfig:brandCsCommissionRateConfig:remove')")
    @Log(title = "客服提成完成率配置表", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public ApiResult remove(@RequestBody Long[] ids) {
        return toAjax(brandCsCommissionRateConfigService.remove(ids));
    }

}