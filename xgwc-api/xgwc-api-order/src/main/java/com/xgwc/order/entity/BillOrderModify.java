package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BillOrderModify {

    private Long id;
    private Long orderId;
    private Long saleManId;
    private String saleManName;
    private Long deptId;
    private String deptName;
    private Integer transferState;
    private String orderDate;
    private Long storeId;
    private String storeName;
    private Long brandId;
    private Integer payChannel;
    private Integer shType;
    private String orderNo;
    private String taobaoId;
    private Double orderAmount;
    private Double amount;
    private BigDecimal nowAmount;
    private Long companyInfoId;
    private Integer payType;
    private String allotRemark;
    private Integer allotNum;
    private Long allotUserId;
    private String allotUserName;
    private Long pid;
    private Long designerId;
    private String designerName;
    private String designerBusiness;
    private BigDecimal money;
    private BigDecimal nowMoney;
    private BigDecimal commissionBacked;
    private BigDecimal commissionBackRemaining;
    private Integer backStatus;
    private Date createTime;
    private Long franchiseId;
    private Date modifyTime;
    private Date updateTime;
    //流程id
    private Long applyId;
}
