package com.xgwc.order.entity.vo;

import lombok.Data;

import java.util.Date;


@Data
public class OrderRefundAuditVo {

    /** 主键 */
    private Long id;

    /** 流程审核状态：ING-审核中，PASS-审核通过，REJECT-审核拒绝 CANCEL-终止  WITHDRAW-撤销 */
   private String applyStatus;

   /** 0:审核中，1：审核通过（走工作流）2:拒绝 */
   private Integer afterCheckStatus;

   /** 流程实例ID */
   private Long executionId;

   private Date lastApproveTime;
}
