package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.order.dao.AfterSalesFlowLogMapper;
import com.xgwc.order.service.IAfterSalesFlowLogService;
import com.xgwc.order.entity.AfterSalesFlowLog;
import com.xgwc.order.entity.vo.AfterSalesFlowLogVo;
import com.xgwc.order.entity.dto.AfterSalesFlowLogDto;

import java.util.List;


@Service
public class AfterSalesFlowLogServiceImpl implements IAfterSalesFlowLogService {
    @Resource
    private AfterSalesFlowLogMapper afterSalesFlowLogMapper;


    @Override
    public List<AfterSalesFlowLogDto> selectAfterSalesFlowLogByBusinessId(Long businessId) {
        return afterSalesFlowLogMapper.selectAfterSalesFlowLogByBusinessId(businessId);
    }


    @Override
    public int insertAfterSalesFlowLog(AfterSalesFlowLogVo dto) {
        AfterSalesFlowLog afterSalesFlowLog = BeanUtil.copyProperties(dto, AfterSalesFlowLog.class);
        afterSalesFlowLog.setCreateTime(DateUtils.getNowDate());
        afterSalesFlowLog.setCreateBy(dto.getOperatorName());
        return afterSalesFlowLogMapper.insertAfterSalesFlowLog(afterSalesFlowLog);
    }

}
