package com.xgwc.order.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class AfterSalesExecDataDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("业务id")
    @Excel(name = "业务id")
    private Long businessId;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("参数名")
    @Excel(name = "参数名")
    private String keyName;

    @FieldDesc("参数值")
    @Excel(name = "参数值")
    private String keyValue;

}
