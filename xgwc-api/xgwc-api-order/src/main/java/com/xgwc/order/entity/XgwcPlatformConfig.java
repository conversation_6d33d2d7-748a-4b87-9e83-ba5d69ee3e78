package com.xgwc.order.entity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;


@Data
public class XgwcPlatformConfig {

    // # 慧策开放平台-秘钥配置   https://open.wangdian.cn/Y/open/abut/apply_test

    /**
     * 品牌商id
     */
    private Long id;
    // 旺店通帐号
    @NotBlank
    private String sid;
    // 旺店通秘钥
    @NotBlank
    private String appKey;
    @NotBlank
    private String appSecret;
    // 奇门秘钥
    @NotBlank
    private String qimenAppKey;
    @NotBlank
    private String qimenAppSecret;
    @NotNull(message = "起始同步时间不能为空")
    public Date startTime;


    public static String getRedisKey(Long brandId, XgwcPlatformDataTypeEnum dataType) {
        return String.format("xgwc:order:pullWdtDataEndTime_%s_%s", brandId, dataType.name());
    }

}