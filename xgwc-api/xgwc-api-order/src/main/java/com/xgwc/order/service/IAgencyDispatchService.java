package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.dto.AgencyDispatchInfoDto;
import com.xgwc.order.entity.dto.AgencyDispatchPageDto;
import com.xgwc.order.entity.dto.AgencyRecordPageDto;
import com.xgwc.order.entity.vo.*;
import jakarta.validation.Valid;

public interface IAgencyDispatchService {
    /**
     * 查询派单表
     * 
     * @param id 派单表主键
     * @return 派单表
     */
    public AgencyDispatchInfoDto selectAgencyDispatchById(Long id);

    /**
     * 查询我的待派单列表
     * @param agencyDispatch 派单表
     * @return 待派单集合
     */
    public List<AgencyDispatchPageDto> selectMyAgencyDispatchList(AgencyDispatchQueryVo agencyDispatch);

    /**
     * 新增派单表
     * 
     * @param agencyDispatch 派单表
     * @return 结果
     */
    public int insertAgencyDispatch(AgencyDispatchVo agencyDispatch);

    /**
     * 修改派单表
     * 
     * @param agencyDispatch 派单表
     * @return 结果
     */
    public int updateAgencyDispatch(AgencyDispatchVo agencyDispatch);

    /**
     * 批量删除派单表
     * 
     * @param ids 需要删除的派单表主键集合
     * @return 结果
     */
    public int deleteAgencyDispatchByIds(Long[] ids);

    /**
     * 删除派单表信息
     * 
     * @param id 派单表主键
     * @return 结果
     */
    public int deleteAgencyDispatchById(Long id);

    /**
     * 派单分配
     * @param dto 派单分配参数
     * @return 结果
     */
    int distribution(@Valid DistributionVo dto);

    /**
     * 领取
     * @param receiveVo 领取参数
     * @return 结果
     */
    int receive(@Valid ReceiveVo receiveVo);

    /**
     * 退回
     * @param dto 退回参数
     * @return 结果
     */
    int returnAgencyDispatch(@Valid ReturnAgencyDispatchVo dto);

    /**
     * 更新备注
     * @param updateRemarkVo 更新备注参数
     * @return 结果
     */
    int updateRemark(@Valid UpdateRemarkVo updateRemarkVo);

    /**
     * 未派单记录
     * @param params
     * @return
     */
    List<AgencyRecordPageDto> agencyRecordList(AgencyRecordQueryVo params);

    /**
     * 统计紧急待派单数量
     * @return 紧急待派单数量
     */
    int countEmergency();
}
