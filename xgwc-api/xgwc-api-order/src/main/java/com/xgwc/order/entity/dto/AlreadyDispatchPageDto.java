package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class AlreadyDispatchPageDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("订单Id")
    @Excel(name = "订单Id")
    private Long orderId;

    @FieldDesc("待派单Id")
    @Excel(name = "待派单Id")
    private Long agencyDispatchId;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    @FieldDesc("期望初稿日期：yyyy-MM-dd HH:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "期望初稿日期：yyyy-MM-dd HH:00:00", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveExpectTime;

    @FieldDesc("约定初稿日期：yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "约定初稿日期：yyyy-MM-dd HH:00:00", width = 30, dateFormat = "yyyy-MM-dd")
    private Date archiveAppointTime;

    @FieldDesc("加盟商")
    @Excel(name = "加盟商")
    private String companyName;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String taobaoId;

    @FieldDesc("佣金金额")
    @Excel(name = "佣金金额")
    private Long money;

    @FieldDesc("设计师名称")
    @Excel(name = "设计师名称")
    private String designerName;

    @FieldDesc("订单金额")
    @Excel(name = "订单金额")
    private Long orderAmount;

    @FieldDesc("谈单人员名称")
    @Excel(name = "谈单人员名称")
    private String saleManName;

    @FieldDesc("要求")
    @Excel(name = "要求")
    private String allotRemark;

    @FieldDesc("收货情况")
    @Excel(name = "收货情况")
    private Integer shType;

    @FieldDesc("资料")
    @Excel(name = "资料")
    private String allotFile;

    @FieldDesc("母订单ID")
    private Long pid;
}
