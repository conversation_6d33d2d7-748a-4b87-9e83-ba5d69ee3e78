package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class OrderArchiveLogDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("交稿id")
    @Excel(name = "交稿id")
    private Long archiveId;

    @FieldDesc("审批人")
    @Excel(name = "审批人")
    private String review;

    @FieldDesc("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reviewTime;

    @FieldDesc("审批状态")
    @Excel(name = "审批状态")
    private Integer reviewStatus;

    @FieldDesc("审批结果")
    @Excel(name = "审批结果")
    private String reviewMsg;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("archiveId",getArchiveId())
            .append("review",getReview())
            .append("reviewTime",getReviewTime())
            .append("reviewStatus",getReviewStatus())
            .append("reviewMsg",getReviewMsg())
        .toString();
    }
}
