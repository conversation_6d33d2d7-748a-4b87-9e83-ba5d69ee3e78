package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单退款支付情况
 */
@Data
public class OrderRefundPayVo {

    /** 主键ID */
    @FieldDesc("主键ID")
    private Long id;

    /** 原业务ID */
    @FieldDesc("原业务ID")
    private Long preId;

    /** 申请记录ID：order_refund_apply主键 */
    @FieldDesc("申请记录ID：order_refund_apply主键")
    private Long applyId;

    /** 订单编号 */
    @FieldDesc("订单编号")
    private String orderNo;

    /** 付款方式:1全款/2定价/3过程款/4尾款 */
    @FieldDesc("付款方式:1全款/2定价/3过程款/4尾款")
    private Integer payType;

    /** 付款截图 */
    @FieldDesc("付款截图")
    private String payImg;

    /** 实收金额 */
    @FieldDesc("实收金额")
    private BigDecimal amount;

    /** 支付方式  1:淘宝  2：微信  3：支付宝, 4:对公 */
    @FieldDesc("支付方式  1:淘宝  2：微信  3：支付宝, 4:对公")
    private Integer payChannel;

    /** 收款编号 */
    @FieldDesc("收款编号")
    private String collectionNo;

    /** 退款金额 */
    @FieldDesc("退款金额")
    private BigDecimal refundAmount;

    /** 退款配置 */
    @FieldDesc("退款配置")
    private String refundConfig;

    /** 支付方式  1:淘宝  2：微信  3：支付宝, 4:对公 */
    @FieldDesc("支付方式  1:淘宝  2：微信  3：支付宝, 4:对公")
    private Integer refundPayChannel;

    /** 可退金额 */
    @FieldDesc("可退金额")
    private BigDecimal newAmount;




}
