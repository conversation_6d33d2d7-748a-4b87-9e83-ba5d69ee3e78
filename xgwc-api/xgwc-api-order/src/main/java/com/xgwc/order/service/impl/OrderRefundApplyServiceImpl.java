package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.ImmutableList;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.PageUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.OrderInvoiceApplyMapper;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.dao.OrderRefundApplyMapper;
import com.xgwc.order.entity.Order;
import com.xgwc.order.entity.OrderInvoiceApply;
import com.xgwc.order.entity.OrderRefundApply;
import com.xgwc.order.entity.OrderRefundCommision;
import com.xgwc.order.entity.OrderRefundPay;
import com.xgwc.order.entity.dto.OrderRefundApplyApproveDto;
import com.xgwc.order.entity.dto.OrderRefundApplyInfoDto;
import com.xgwc.order.entity.vo.OrderRefundApplyApproveQueryVo;
import com.xgwc.order.service.IOrderRefundApplyService;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.api.ServiceAuthorizeFeign;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class OrderRefundApplyServiceImpl implements IOrderRefundApplyService {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private ServiceAuthorizeFeign serviceAuthorizeFeign;
    @Resource
    private OrderRefundApplyMapper orderRefundApplyMapper;
    @Resource
    private OrderInvoiceApplyMapper orderInvoiceApplyMapper;

    @Override
    public List<OrderRefundApplyApproveDto> allApprove(OrderRefundApplyApproveQueryVo queryVo) {
        if (queryVo.getCrateTimeStart() != null) {
            queryVo.setCrateTimeStart(DateUtils.parseDate(DateUtils.getStartTime(queryVo.getCrateTimeStart())));
        }
        if (queryVo.getCreateTimeEnd() != null) {
            queryVo.setCreateTimeEnd(DateUtils.parseDate(DateUtils.getEndTime(queryVo.getCreateTimeEnd())));
        }
        List<OrderRefundApplyApproveDto> approveDtoList = this.listPage(queryVo);
        if (!approveDtoList.isEmpty()) {
            approveDtoList.forEach(approveDto -> approveDto.setExecutionDatas(BeanUtil.copyProperties(approveDto, OrderRefundApplyApproveDto.class)));
        }
        return approveDtoList;
    }


    @Override
    public OrderRefundApplyInfoDto getInfo(Long applyId) {
        OrderRefundApply orderRefundApply = orderRefundApplyMapper.getById(applyId);
        if (orderRefundApply == null) {
            return null;
        }
        boolean isAuth = false;
        SysUser sysUser = SecurityUtils.getSysUser();
        if (sysUser.isBrandUser()) {
            isAuth = orderRefundApply.getBrandId().equals(sysUser.getBrandId());
        } else if (sysUser.isFranchiseUser()) {
            isAuth = orderRefundApply.getFranchiseId().equals(sysUser.getFranchiseId());
        } else if (sysUser.isServiceUser()) {
            isAuth = serviceAuthorizeFeign.getServiceBrandIdList(sysUser.getServiceId()).contains(orderRefundApply.getBrandId());
        }
        if (!isAuth) {
            return null;
        }

        Order order = orderMapper.getByOrderNo(orderRefundApply.getOrderNo(), ImmutableList.of(orderRefundApply.getBrandId()));
        List<OrderRefundPay> refundPayList = orderRefundApplyMapper.getOrderRefundPayListByApplyId(orderRefundApply.getId());
        List<OrderRefundCommision> refundCommisionList = orderRefundApplyMapper.getOrderRefundCommisionListByApplyId(orderRefundApply.getId());
        List<OrderInvoiceApply> orderInvoiceList = orderInvoiceApplyMapper.listPassByOrderNo(orderRefundApply.getOrderNo());
        return OrderRefundApplyInfoDto.init(orderRefundApply, order, refundPayList, refundCommisionList, orderInvoiceList);
    }

    private List<OrderRefundApplyApproveDto> listPage(OrderRefundApplyApproveQueryVo queryVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if (sysUser.isBrandUser()) {
            queryVo.setBrandIdList(ImmutableList.of(sysUser.getBrandId()));
        } else if (sysUser.isFranchiseUser()) {
            queryVo.setFranchiseId(sysUser.getFranchiseId());
        } else if (sysUser.isServiceUser()) {
            queryVo.setBrandIdList(serviceAuthorizeFeign.getServiceBrandIdList(sysUser.getServiceId()));
            if (queryVo.getBrandIdList().isEmpty()) {
                return Lists.newArrayList();
            }
        } else {
            return Lists.newArrayList();
        }
        PageUtils.startPage();
        List<OrderRefundApplyApproveDto> voList = orderRefundApplyMapper.listByApprove(queryVo);
        if (!voList.isEmpty()) {
            List<Long> franchiseIdList = voList.stream().map(OrderRefundApplyApproveDto::getFranchiseId).distinct().toList();
            Map<Long, String> franchiseNameMap = franchiseFeign.listByIds(franchiseIdList).stream().collect(Collectors.toMap(FranchiseDto::getId, FranchiseDto::getFranchiseName));
            voList.forEach(vo -> vo.setFranchiseName(franchiseNameMap.get(vo.getFranchiseId())));
        }
        return voList;
    }

}