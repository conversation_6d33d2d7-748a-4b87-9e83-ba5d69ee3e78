package com.xgwc.order.entity;

import com.google.common.collect.ImmutableList;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单退款支付情况
 */
@Data
public class OrderRefundPay {

    /** 主键ID */
    private Long id;

    /**
     * 原业务ID
     */
    private Long preId;

    /** 申请记录ID：order_refund_apply主键 */
    private Long applyId;

    /** 订单编号 */
    private String orderNo;

    /** 付款方式:1全款/2定价/3过程款/4尾款 */
    private Integer payType;

    /** 付款截图 */
    private String payImg;

    /** 实收金额 */
    private BigDecimal amount;

    /** 支付方式  1:淘宝  2：微信  3：支付宝, 4:对公 */
    private Integer payChannel;

    /** 收款编号 */
    private String collectionNo;

    /** 退款金额 */
    private BigDecimal refundAmount;

    /** 退款配置 */
    private String refundConfig;

    /** 支付方式  1:淘宝  2：微信  3：支付宝, 4:对公 */
    private Integer refundPayChannel;

    /** 状态：0正常，1非正常 */
    private Integer status;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /** 可退金额 */
    private BigDecimal newAmount;


    // 以下是非表中字段 ------------------
    // 是否可退, 为空时表示可退, 不为空时为对应提示
    private String isRefundable;
    private BigDecimal nowAmount;
    /** 支付方式  1:淘宝  2：微信  3：支付宝, 4:对公 */
    private Integer lastRefundPayChannel;

    // 可退款金额
    public BigDecimal getRefundableAmount() {
        return this.nowAmount != null ? this.nowAmount : this.amount;
    }

}
