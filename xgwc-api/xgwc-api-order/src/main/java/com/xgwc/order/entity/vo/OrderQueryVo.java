package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("订单id")
    private Long id;

    @FieldDesc("谈单人员")
    private Long saleManId;

    @FieldDesc("谈单人员名称")
    private String saleManName;

    @FieldDesc("录入部门编码")
    private Long deptId;
    private List<Long> deptIds;

    @FieldDesc("录入部门名称")
    private String deptName;

    @FieldDesc("订单类型：0正常单，1转化单")
    private Long transferState;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDateStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDateEnd;

    @FieldDesc("订单来源ID（店铺id）")
    private Long storeId;

    @FieldDesc("订单来源名称（店铺名称）")
    private String storeName;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    private String franchiseName;

    @FieldDesc("支付方式  1:淘宝  2：微信  3：支付宝")
    private Long payChannel;

    @FieldDesc("订单状态（0：未发货 1：完成  2：退款 3：部分退款）")
    private Long shType;

    private Long[] shTypes;

    @FieldDesc("订单编号")
    private String orderNo;

    @FieldDesc("客户ID")
    private String taobaoId;

    @FieldDesc("订单金额")
    private BigDecimal orderAmount;

    @FieldDesc("实收金额")
    private BigDecimal amount;

    @FieldDesc("付款方式:1全款/2阶段付")
    private Long payType;

    @FieldDesc("派单类型")
    private Integer allotType;

    @FieldDesc("是否紧急：0否 1是")
    private Integer allotUrgency;

    @FieldDesc("派单需求")
    private String allotRemark;

    @FieldDesc("派单设计师数量")
    private Long allotNum;

    @FieldDesc("协助文件")
    private String allotFile;

    @FieldDesc("母订单id")
    private Long pid;

    @FieldDesc("设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    private String designerPhone;

    @FieldDesc("佣金金额")
    private Long money;

    @FieldDesc("交稿状态：0 未接单, 1 制作中, 2 已交初稿, 3 定稿待审核, 4 定稿被驳回, 5 已交定稿")
    private Integer archiveType;
    private List<Integer> archiveTypes;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;


    @FieldDesc("退款状态：0 未退款，1 部分退款，2 全额退款")
    private List<Long> refundStatuss;
    /**
     * 现实收金额， 退款之后
     */
    private BigDecimal nowAmount;

    /**
     * 现佣金，退款之后
     */
    private BigDecimal nowMoney;




}
