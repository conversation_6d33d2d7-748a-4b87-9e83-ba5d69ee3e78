package com.xgwc.order.service;

import java.util.List;
import com.xgwc.order.entity.AfterSalesExecData;
import com.xgwc.order.entity.vo.AfterSalesExecDataVo;
import com.xgwc.order.entity.dto.AfterSalesExecDataDto;
import com.xgwc.order.entity.vo.AfterSalesExecDataQueryVo;
import com.xgwc.order.entity.vo.OrderRefundApplyVo;

public interface IAfterSalesExecDataService {
    /**
     * 查询售后表单数据
     *
     * @param id 售后表单数据主键
     * @return 售后表单数据
     */
    AfterSalesExecDataDto selectAfterSalesExecDataById(Long id);

    /**
     * 新增售后表单数据
     * @param orderRefundApplyVo 售后表单数据
     * @return 结果
     */
    void insertAfterSalesExecData(OrderRefundApplyVo orderRefundApplyVo);


}
