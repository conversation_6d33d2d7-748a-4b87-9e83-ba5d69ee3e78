package com.xgwc.order.entity;

import lombok.Data;

import java.util.Date;


@Data
public class SendBack {

private static final long serialVersionUID=1L;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 派单Id */
    private Long dispatchId;

    /** 退回备注 */
    private String explanatory;

    /**  */
    private Long id;

    /** 行更新时间 */
    private Date modifyTime;

    /** 订单Id */
    private Long orderId;

    /** 退回时间 */
    private Date sendTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;



}