package com.xgwc.order.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.order.entity.dto.AlreadyDispatchDto;
import com.xgwc.order.entity.dto.AlreadyDispatchPageDto;
import com.xgwc.order.entity.dto.AlreadyRecordPageDto;
import com.xgwc.order.entity.dto.SendBackDto;
import com.xgwc.order.entity.vo.AlreadyDispatchQueryVo;
import com.xgwc.order.entity.vo.AlreadyDispatchVo;
import com.xgwc.order.entity.vo.AlreadyRecordQueryVo;
import com.xgwc.order.service.IAlreadyDispatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/alreadyDispatch")
public class AlreadyDispatchController extends BaseController {
    @Autowired
    private IAlreadyDispatchService alreadyDispatchService;

    /**
     * 查询我的已派单列表
     */
    @MethodDesc("查询我的已派单列表")
    @PreAuthorize("@ss.hasPermission('alreadyDispatch:alreadyDispatch:list')")
    @GetMapping("/list")
    public ApiResult<AlreadyDispatchPageDto> list(AlreadyDispatchQueryVo alreadyDispatch) {
        startPage();
        List<AlreadyDispatchPageDto> list = alreadyDispatchService.selectAlreadyDispatchList(alreadyDispatch);
        return getDataTable(list);
    }

    /**
     * 派单
     */
    @MethodDesc("派单")
    @PreAuthorize("@ss.hasPermission('alreadyDispatch:alreadyDispatch:add')")
    @Log(title = "我的已派单", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody AlreadyDispatchVo alreadyDispatch) {
        return toAjax(alreadyDispatchService.insertAlreadyDispatch(alreadyDispatch));
    }

    /**
     * 查询已派单详情
     */
    @MethodDesc("查询已派单详情")
    @PreAuthorize("@ss.hasPermission('agencyDispatch:agencyDispatch:edit')")
    @GetMapping(value = "/getInfo/{id}")
    public ApiResult<AlreadyDispatchDto> getAlreadyDispatch(@PathVariable("id") Long id) {
        return success(alreadyDispatchService.selectAlreadyDispatchById(id));
    }

    /**
     * 退回记录
     */
    @MethodDesc("退回记录")
    @PreAuthorize("@ss.hasPermission('alreadyDispatch:alreadyDispatch:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<SendBackDto> getSendBackInfo(@PathVariable("id") Long id) {
        return success(alreadyDispatchService.getSendBackInfo(id));
    }

    @MethodDesc("已派单记录")
    @PreAuthorize("@ss.hasPermission('alreadyDispatch:alreadyDispatch:list')")
    @GetMapping("/alreadyRecord")
    public ApiResult<AlreadyRecordPageDto> alreadyRecordList(AlreadyRecordQueryVo params) {
        startPage();
        List<AlreadyRecordPageDto> list = alreadyDispatchService.alreadyRecordList(params);
        return getDataTable(list);
    }
}
