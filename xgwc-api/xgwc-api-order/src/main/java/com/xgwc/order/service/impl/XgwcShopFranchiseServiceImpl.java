package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.XgwcShopFranchiseBusinessMapper;
import com.xgwc.order.dao.XgwcShopFranchiseMapper;
import com.xgwc.order.dao.XgwcShopMapper;
import com.xgwc.order.entity.XgwcShopFranchise;
import com.xgwc.order.entity.XgwcShopFranchiseBusiness;
import com.xgwc.order.feign.entity.XgwcShopFranchiseBusinessDto;
import com.xgwc.order.feign.entity.XgwcShopFranchiseDto;
import com.xgwc.order.entity.vo.XgwcShopBusinessVo;
import com.xgwc.order.entity.vo.XgwcShopFranchiseQueryVo;
import com.xgwc.order.feign.entity.ShopFranchiseVo;
import com.xgwc.order.feign.entity.XgwcShopFranchiseBusinessVo;
import com.xgwc.order.entity.vo.XgwcShopFranchiseVo;
import com.xgwc.order.service.IXgwcShopFranchiseService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class XgwcShopFranchiseServiceImpl implements IXgwcShopFranchiseService  {
    @Resource
    private XgwcShopFranchiseMapper xgwcShopFranchiseMapper;

    @Resource
    private XgwcShopFranchiseBusinessMapper xgwcShopFranchiseBusinessMapper;

    /**
     * 查询加盟商店铺
     * 
     * @param shopId 店铺主键
     * @return 加盟商店铺
     */
    @Override
    public XgwcShopFranchiseDto selectXgwcShopFranchiseByShopId(Long shopId) {
        XgwcShopFranchiseDto dto =  xgwcShopFranchiseMapper.selectXgwcShopFranchiseByShopId(shopId);
        List<XgwcShopFranchiseBusinessDto> business = xgwcShopFranchiseBusinessMapper.selectListByShopId(shopId);
        dto.setBusiness(business);
        return dto;
    }

    /**
     * 查询加盟商店铺列表
     * 
     * @param xgwcShopFranchise 加盟商店铺
     * @return 加盟商店铺
     */
    @Override
    public List<XgwcShopFranchiseDto> selectXgwcShopFranchiseList(XgwcShopFranchiseQueryVo xgwcShopFranchise) {
        SysUser user = SecurityUtils.getSysUser();
        //1：品牌商，2：加盟商，3：设计师，4:品牌商员工，5：加盟商员工
        switch (user.getUserType()) {
            case 1:
            case 4:
                xgwcShopFranchise.setBrandOwnerId(user.getBrandId());
                break;
            case 2:
            case 5:
                xgwcShopFranchise.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
                break;
        }
        List<XgwcShopFranchiseDto> list = xgwcShopFranchiseMapper.selectXgwcShopFranchiseList(xgwcShopFranchise);
        if( list != null && !list.isEmpty()) {
            Long[] ids = list.stream().map(XgwcShopFranchiseDto::getShopId).distinct().toArray(Long[]::new);
            List<XgwcShopFranchiseBusinessDto>  businessDtos = xgwcShopFranchiseBusinessMapper.selectListByShopIds(ids);
            list.forEach(e -> {
                List<XgwcShopFranchiseBusinessDto> bus = businessDtos.stream().filter(b -> b.getShopId().equals(e.getShopId())).collect(Collectors.toList());
                e.setBusiness(bus);
            });
        }
        return list;
    }

    @Override
    public List<XgwcShopFranchiseBusinessDto> shopFranchiseBusiness(Long userId) {
        SysUser user = SecurityUtils.getSysUser();
        Long franchiseId = null;
        Long brandId = null;
        switch (user.getUserType()) {
            case 2:
                franchiseId = SecurityUtils.getSysUser().getFranchiseId();
                break;
            case 5:
                if(userId == null) {
                    userId = SecurityUtils.getSysUser().getUserId();
                }
                break;
            case 1:
            case 4:
                brandId = SecurityUtils.getSysUser().getBrandId();
                break;
            default:
                return null;
        }
        return xgwcShopFranchiseBusinessMapper.shopFranchiseBusiness(userId,brandId,franchiseId);
    }

    @Override
    public int updateBusiness(List<XgwcShopFranchiseBusinessVo> business) {
        for (XgwcShopFranchiseBusinessVo vo : business) {
            XgwcShopFranchiseBusiness busines = BeanUtil.copyProperties(vo, XgwcShopFranchiseBusiness.class);
            xgwcShopFranchiseBusinessMapper.updateXgwcShopFranchiseBusiness(busines);
        }
        return 1;
    }
}
