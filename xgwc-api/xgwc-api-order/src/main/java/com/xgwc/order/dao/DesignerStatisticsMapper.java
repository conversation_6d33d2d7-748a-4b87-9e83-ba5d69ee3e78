package com.xgwc.order.dao;

import com.xgwc.order.entity.DesignerTarck;
import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.DesignerStatisticsQueryVo;
import com.xgwc.order.entity.vo.DesignerStatisticsScoreQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DesignerStatisticsMapper {

    List<DesignerStatisticsDto> selectDesignerStatistics(DesignerStatisticsQueryVo queryVo);

    DesignerStatisticsDto selectDesignerStatisticsById(Long designerId);

    List<DesignerStatisticsScoreDto> selectDesignerStatisticsScore(@Param("designerId") Long designerId,@Param("brandId") Long brandId);
    List<DesignerStatisticsScoreDto> selectDesignerStatisticsYearScore(@Param("designerId") Long designerId,@Param("brandId") Long brandId);

    List<DesignerTarckDto> selectDesignerTarck(Long designerId);

    int insertTarck(DesignerTarck designerTarck);
    int updateTarck(DesignerTarck designerTarck);

    DesignerStatisticsScoreAllDto selectDesignerStatisticsScoreAll(@Param("designerId") Long designerId,@Param("scoreType") Long scoreType);

    List<DesignerStatisticsOrderDto> selectOrder(DesignerStatisticsScoreQueryVo queryVo);
}
