package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单退款支付情况
 */
@Data
public class OrderRefundPayExtVo {

    /**
     * 是否开启千牛
     */
    @FieldDesc("是否开启千牛 0-否 1-是")
    private Integer openQianniu;

    /**
     * 费率
     */
    @FieldDesc("费率")
    private BigDecimal rate;

    /**
     * 收款码
     */
    @FieldDesc("收款码")
    private String receivePayImg;

    /**
     * 账号
     */
    @FieldDesc("账号")
    private String accountName;

    /**
     * 开户行
     */
    @FieldDesc("开户行")
    private String accountBank;

    /**
     * 开户号
     */
    @FieldDesc("开户号")
    private String corporateAccount;



}
