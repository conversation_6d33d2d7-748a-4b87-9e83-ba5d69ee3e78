package com.xgwc.order.entity.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SeasRuleDto {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 是否员工领取: 0是，1否
     */
    private Integer staffReceive;
    /**
     * 是否部门负责人领取: 0是，1否
     */
    private Integer deptReceive;

    /**
     * 每日限制是否开启：0是，1否
     */
    private Integer dayLimitOpen;

    /**
     * 每日限制
     */
    private Integer dayLimit;

    /**
     * 限制：分钟：0是，1否
     */
    private Integer intervalMinuter;

    /**
     * 限制分钟 是否开启
     */
    private Integer intervalEnabled;
    /**
     * 回收规则：回收规则：1.单次，2循环，3.不会收
     */
    @NotNull(message = "回收规则不能为空")
    private Integer recycleRule;

    /**
     * 有效行为：1任一，2.全部
     */
    @NotNull(message = "有效行为不能为空")
    private Integer anyOrAll;

    /**
     * 1.新增跟进,2.变更添加微信,3.补充联系电话,4.补充预算,5.更新回访时间,6.更新需求
     */
    @NotNull(message = "行为列表不能为空")
    private String behaviorType;

    /**
     * 超过多少小时回收
     */
    @NotNull(message = "回收时间设置未填写")
    private Integer recycleNoEff;

    /**
     * 超过多少天未成交回收
     */
    @NotNull(message = "回收时间设置未填写")
    private Integer recycleNoDeal;

    /**
     * 品牌商ID
     */
    private Long brandId;

    /**
     * 创建于
     */
    private String createBy;

    /**
     * 更新于
     */
    private String updateBy;
}
