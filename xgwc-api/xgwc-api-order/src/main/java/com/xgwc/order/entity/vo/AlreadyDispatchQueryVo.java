package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class AlreadyDispatchQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @FieldDesc("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @FieldDesc("其它")
    private String other;

    @FieldDesc("加盟商")
    private Long franchiseeId;

    @FieldDesc("派单员")
    private Long dispatchUserId;

    @FieldDesc("收货情况")
    private Integer shType;
}
