package com.xgwc.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.XgwcShopMapper;
import com.xgwc.order.entity.dto.XgwcShopDto;
import com.xgwc.order.entity.param.XgwcShopParam;
import com.xgwc.order.entity.vo.XgwcShopBusinessVo;
import com.xgwc.order.entity.vo.XgwcShopVo;
import com.xgwc.order.service.IXgwcShopFranchiseService;
import com.xgwc.order.service.XgwcShopService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:44
 */
@Service
@Slf4j
public class XgwcShopServiceImpl implements XgwcShopService {

    @Resource
    private XgwcShopMapper xgwcShopMapper;

    @Resource
    private IXgwcShopFranchiseService shopFranchiseService;


    /**
     * 根据查询参数获取店铺列表
     *
     * @param xgwcShopParam 查询参数
     * @return 包含店铺信息的列表
     */
    @Override
    public List<XgwcShopVo> getXgwcShopList(XgwcShopParam xgwcShopParam) {
        Long brandOwnerId = xgwcShopParam.getBrandOwnerId();
        brandOwnerId = (brandOwnerId == null) ? SecurityUtils.getSysUser().getBrandId() : brandOwnerId;
        xgwcShopParam.setBrandOwnerId(brandOwnerId);
        return xgwcShopMapper.getXgwcShopList(xgwcShopParam);
    }

    /**
     * 保存店铺信息
     *
     * @param xgwcShopDto 包含店铺信息的DTO对象
     * @return 操作结果的ApiResult对象
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult saveXgwcShop(XgwcShopDto xgwcShopDto) {
        if (xgwcShopDto == null) {
            log.error("保存店铺信息失败：店铺信息为空");
            return ApiResult.error("店铺信息不能为空");
        }

        // 保存店铺基本信息
        xgwcShopDto.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        xgwcShopDto.setCreateBy(SecurityUtils.getNickName());
        int affectedRows = xgwcShopMapper.saveXgwcShop(xgwcShopDto);
        if (affectedRows <= 0) {
            log.error("保存店铺基本信息失败，店铺名称: {}", xgwcShopDto.getShopName());
            throw new ApiException("保存店铺基本信息失败");
        }

        // 保存公司主体关联店铺信息
        int companyAffectedRows = xgwcShopMapper.seveXgwcCompanyShop(xgwcShopDto);
        if (companyAffectedRows <= 0){
            log.error("保存公司主体信息失败，公司主体名称: {}", xgwcShopDto.getShopName());
            throw new ApiException("保存店铺信息失败");
        }

        // 保存店铺业务信息
        List<XgwcShopBusinessVo> businessList = xgwcShopDto.getXgwcShopBusinessVos();
        if (!CollectionUtils.isEmpty(businessList)) {
            for (XgwcShopBusinessVo xgwcShopBusinessVo : businessList) {
                xgwcShopBusinessVo.setShopId(xgwcShopDto.getShopId());
                xgwcShopBusinessVo.setCreateBy(SecurityUtils.getNickName());
            }
            int businessAffectedRows = xgwcShopMapper.saveXgwcShopBusiness(businessList);
            if (businessAffectedRows <= 0) {
                log.error("保存店铺业务信息失败，店铺ID: {}", xgwcShopDto.getShopId());
                throw new ApiException("保存店铺业务信息失败");
            }
        }

        log.info("保存店铺信息成功，店铺名称: {}", xgwcShopDto.getShopName());
        return ApiResult.ok();
    }

    private static void setCreateBy(Object entity) {
        try {
            Method method = entity.getClass().getMethod("setCreateBy", String.class);
            method.invoke(entity, SecurityUtils.getNickName());
        } catch (Exception e) {
            throw new RuntimeException("实体类必须包含setCreateBy(String)方法", e);
        }
    }
    /**
     * 根据店铺ID查询店铺信息
     *
     * @param shopId 店铺ID，不能为null
     * @return 包含店铺信息的ApiResult对象
     */
    @Override
    public ApiResult getXgwcShopById(Long shopId) {
        if (shopId == null) {
            log.error("查询店铺信息失败：店铺ID为空");
            return ApiResult.error("店铺ID不能为空");
        }

        try {
            XgwcShopVo shopBaseInfo = xgwcShopMapper.getXgwcShopById(shopId);
            if (shopBaseInfo == null) {
                log.warn("查询店铺信息：店铺不存在，ID: {}", shopId);
                return ApiResult.error("店铺不存在");
            }
            XgwcShopDto xgwcShopDto = new XgwcShopDto();
            BeanUtils.copyProperties(shopBaseInfo, xgwcShopDto);
            List<XgwcShopBusinessVo> businessList = xgwcShopMapper.getXgwcShopBusiness(shopId);
            xgwcShopDto.setXgwcShopBusinessVos(businessList);

            log.info("查询店铺信息成功，店铺ID: {}", shopId);
            return ApiResult.ok(xgwcShopDto);
        } catch (Exception e) {
            log.error("查询店铺信息异常，店铺ID: {}", shopId, e);
            return ApiResult.error("查询店铺信息失败，请稍后再试");
        }
    }

    /**
     * 更新店铺信息
     *
     * @param xgwcShopDto 包含更新信息的店铺DTO对象
     * @return 操作结果的ApiResult对象
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateXgwcShopById(XgwcShopDto xgwcShopDto) {
        if (xgwcShopDto == null) {
            log.error("更新店铺信息失败：店铺信息为空");
            return ApiResult.error("店铺信息不能为空");
        }

        if (xgwcShopDto.getShopId() == null) {
            log.error("更新店铺信息失败：店铺ID为空");
            return ApiResult.error("店铺ID不能为空");
        }

        try {
            // 更新店铺基本信息
            xgwcShopDto.setUpdateBy(SecurityUtils.getNickName());
            int affectedRows = xgwcShopMapper.updateXgwcShopById(xgwcShopDto);
            if (affectedRows <= 0) {
                log.error("更新店铺基本信息失败，店铺ID: {}", xgwcShopDto.getShopId());
                throw new ApiException ("修改店铺信息失败");
            }

            // 修改公司主体关联店铺信息
            int companyAffectedRows = xgwcShopMapper.updateXgwcCompanyShop(xgwcShopDto);
            if (companyAffectedRows <= 0){
                log.error("更新公司主体信息失败，店铺名称: {}", xgwcShopDto.getShopName());
                throw new ApiException("更新店铺信息失败");
            }

            // 更新店铺业务信息
            List<XgwcShopBusinessVo> businessList = xgwcShopDto.getXgwcShopBusinessVos();
            if (!CollectionUtils.isEmpty(businessList)) {
                // 先删除原有业务信息
                xgwcShopMapper.deleteXgwcShopBusiness(xgwcShopDto.getShopId());

                // 保存新业务信息
                for (XgwcShopBusinessVo xgwcShopBusinessVo : businessList) {
                    xgwcShopBusinessVo.setShopId(xgwcShopDto.getShopId());
                    xgwcShopBusinessVo.setUpdateBy(SecurityUtils.getNickName());
                }
                int businessAffectedRows = xgwcShopMapper.saveXgwcShopBusiness(businessList);
                if (businessAffectedRows <= 0) {
                    log.error("更新店铺业务信息失败，店铺ID: {}", xgwcShopDto.getShopId());
                    throw new ApiException("保存店铺业务信息失败");
                }
            }

            log.info("更新店铺信息成功，店铺ID: {}, 店铺名称: {}",
                    xgwcShopDto.getShopId(), xgwcShopDto.getShopName());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新店铺信息异常，店铺ID: {}, 店铺名称: {}",
                    xgwcShopDto.getShopId(), xgwcShopDto.getShopName(), e);
            throw new ApiException("更新店铺信息失败，请稍后再试");
        }
    }

    /**
     * 更新店铺状态
     *
     * @param shopId 店铺ID，不能为null
     * @param status 要更新的状态值
     * @return 操作结果的ApiResult对象
     */
    @Override
    public ApiResult updateStatusById(Long shopId, Integer status) {
        if (shopId == null) {
            log.error("更新店铺状态失败：店铺ID为空");
            return ApiResult.error("店铺ID不能为空");
        }

        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新品牌状态失败");
        }

        int affectedRows = xgwcShopMapper.updateStatusById(shopId, status);
        if (affectedRows > 0) {
            log.info("店铺状态更新成功，ID: {}, 新状态: {}", shopId, status);
            return ApiResult.ok();
        }

        log.error("店铺状态更新失败，ID: {}", shopId);
        return ApiResult.error("状态更新失败");
    }

    @Override
    public List<XgwcShopVo> getShopByIds(List<Long> ids) {
        return xgwcShopMapper.findShopByIds(ids);
    }

    @Override
    public List<XgwcShopDto> getShopByFranchiseId(Long franchiseId) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        List<XgwcShopDto> shopDto = xgwcShopMapper.getShopByFranchiseId(franchiseId, brandId);
        if (!CollectionUtils.isEmpty(shopDto)) {
            shopDto.forEach(shop -> shop.setBusinessNames(
                    xgwcShopMapper.getXgwcShopBusiness(shop.getShopId())
                            .stream()
                            .filter(XgwcShopBusinessVo -> XgwcShopBusinessVo.getBusinessName() != null)
                            .map(XgwcShopBusinessVo::getBusinessName)
                            .collect(Collectors.joining("/"))
            ));
            return shopDto;
        }
        return null;
    }

    @Override
    public List<XgwcShopDto> getServiceProviderShopList(XgwcShopParam xgwcShopParam) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long serviceId = sysUser.getServiceId();
        if(serviceId == null){
            log.warn("当前登录账号非服务商账号，登录信息{}", JSONObject.toJSON(sysUser));
            return null;
        }
        List<Long> brandIds = xgwcShopMapper.getAuthorizeBrandIdsByServiceId(serviceId);
        if (!CollectionUtils.isEmpty(brandIds)){
            xgwcShopParam.setBrandIds(brandIds);
            return xgwcShopMapper.selectShopListBrandIds(xgwcShopParam);
        }
        return List.of();
    }

    @Override
    public ApiResult updateShopManager(XgwcShopDto xgwcShopDto) {
        Long shopId = xgwcShopDto.getShopId();
        Long managerId = xgwcShopDto.getManagerId();
        if ( shopId == null || managerId == null) {
            log.error("更新店铺管理员失败：店铺ID为空");
            return ApiResult.error("入参ID不能为空");
        }
        XgwcShopVo xgwcShopById = xgwcShopMapper.getXgwcShopById(shopId);
        if (xgwcShopById == null) {
            log.error("更新店铺管理员失败：店铺不存在，shopId={}", shopId);
            return ApiResult.error("修改负责人失败，请稍后重试");
        }
        int updated = xgwcShopMapper.updateXgwcShopManagerById(shopId, managerId);
        if (updated <= 0) {
            log.info("更新店铺管理员失败，shopId={}, managerId={}", shopId, managerId);
            return ApiResult.error("修改负责人失败，请稍后重试");
        }
        return ApiResult.ok();
    }
}
