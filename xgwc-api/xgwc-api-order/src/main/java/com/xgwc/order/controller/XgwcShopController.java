package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcShopDto;
import com.xgwc.order.entity.param.XgwcShopParam;
import com.xgwc.order.service.XgwcShopService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 服务管理-店铺管理
 */
@RestController
@RequestMapping("/xgwcShop")
public class XgwcShopController extends BaseController {

    @Resource
    private XgwcShopService xgwcShopService;

    /**
     * @param xgwcShopParam 查询条件
     * @return 店铺列表
     * 查询店铺列表
     */
    @MethodDesc("查询店铺列表")
    @PreAuthorize("@ss.hasPermission('order:shop:list')")
    @PostMapping("/getXgwcShopList")
    public ApiResult<XgwcShopDto> getXgwcShopList(@RequestBody XgwcShopParam xgwcShopParam) {
        startPage();
        return getDataTable(xgwcShopService.getXgwcShopList(xgwcShopParam));
    }

    /**
     * 查询财务服务商客户店铺列表
     *
     * @param xgwcShopParam 店铺查询条件
     * @return 店铺列表
     */
    @MethodDesc("查询财务服务商客户店铺列表")
    @PreAuthorize("@ss.hasPermission('serviceProvider:shop:list')")
    @PostMapping("/getServiceProviderShopList")
    public ApiResult<XgwcShopDto> getServiceProviderShopList(@RequestBody XgwcShopParam xgwcShopParam) {
        startPage();
        return getDataTable(xgwcShopService.getServiceProviderShopList(xgwcShopParam));
    }

    /**
     * @param xgwcShopDto 新增店铺信息
     * @return 插入结果
     * 新增店铺信息
     */
    @MethodDesc("新增店铺信息")
    @PreAuthorize("@ss.hasPermission('order:shop:insert')")
    @Submit(fileds = "userId")
    @PostMapping("/saveXgwcShop")
    public ApiResult saveXgwcShop(@RequestBody XgwcShopDto xgwcShopDto) {
        return xgwcShopService.saveXgwcShop(xgwcShopDto);
    }

    /**
     * @param shopId 店铺id
     * @return 店铺信息
     * 根据id查询店铺信息
     */
    @MethodDesc("根据id查询店铺信息")
    @PreAuthorize("@ss.hasPermission('order:shop:update')")
    @GetMapping("/getXgwcShopById/{shopId}")
    public ApiResult getXgwcShopById(@PathVariable Long shopId) {
        return xgwcShopService.getXgwcShopById(shopId);
    }

    /**
     * @param xgwcShopDto 修改信息
     * @return 修改结果
     * 修改店铺信息
     */
    @MethodDesc("修改店铺信息")
    @PreAuthorize("@ss.hasPermission('order:shop:update')")
    @Submit(fileds = "userId")
    @PostMapping("/updateXgwcShop")
    public ApiResult updateXgwcShopById(@RequestBody XgwcShopDto xgwcShopDto) {
        return xgwcShopService.updateXgwcShopById(xgwcShopDto);
    }

    /**
     * 修改店铺负责人
     *
     * @param xgwcShopDto 店铺
     * @return 修改结果
     */
    @MethodDesc("修改店铺负责人")
    @PreAuthorize("@ss.hasPermission('serviceProvider:shop:updateManager')")
    @PutMapping("/updateShopManager")
    public ApiResult updateShopManager(@RequestBody XgwcShopDto xgwcShopDto) {
        return xgwcShopService.updateShopManager(xgwcShopDto);
    }

    /**
     * @param shopId 品牌id
     * @return 品牌信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('order:shop:updateStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "shopId") Long shopId,
                                 @RequestParam(value = "status") Integer status) {
        return xgwcShopService.updateStatusById(shopId,status);
    }

    /**
     * 批量查询店铺
     * @param ids 店铺ids
     * @return 店铺信息
     */
    @MethodDesc("批量查询店铺")
    @RequestMapping("getShopByIds")
    public ApiResult getShopByIds(@RequestParam("ids") List<Long> ids){
        return ApiResult.ok(xgwcShopService.getShopByIds(ids));
    }

    /**
     * 获取店铺列表
     */
    @MethodDesc("获取店铺列表")
    @GetMapping("/getAll")
    public ApiResult getAll() {
        return ApiResult.ok(xgwcShopService.getXgwcShopList(new XgwcShopParam()));
    }

    /**
     * 根据加盟商id查询店铺详情
     */
    @MethodDesc("根据加盟商id查询店铺详情")
    @GetMapping("/getShopByFranchiseId")
    public ApiResult getShopByFranchiseId(@RequestParam("franchiseId") Long franchiseId) {
        return ApiResult.ok(xgwcShopService.getShopByFranchiseId(franchiseId));
    }
}
