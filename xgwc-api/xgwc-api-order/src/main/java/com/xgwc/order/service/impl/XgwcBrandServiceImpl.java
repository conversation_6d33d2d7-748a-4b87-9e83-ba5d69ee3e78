package com.xgwc.order.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.XgwcBrandMapper;
import com.xgwc.order.entity.dto.XgwcBrandDto;
import com.xgwc.order.entity.param.XgwcBrandParam;
import com.xgwc.order.service.XgwcBrandService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  09:44
 */
@Service
@Slf4j
public class XgwcBrandServiceImpl implements XgwcBrandService {
    @Resource
    private XgwcBrandMapper xgwcBrandMapper;

    /**
     * 根据条件查询品牌列表
     *
     * @param xgwcBrandParam 查询条件
     * @param brandIds       品牌id集合
     * @return 品牌列表
     */
    @Override
    public List<XgwcBrandDto> getXgwcBrandList(XgwcBrandParam xgwcBrandParam, List<String> brandIds) {
        xgwcBrandParam.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        return xgwcBrandMapper.getXgwcBrandList(xgwcBrandParam, brandIds);
    }

    /**
     * 新增品牌
     * @param xgwcBrandDto 品牌信息
     * @return 新增结果
     */
    @Override
    public ApiResult saveXgwcBrand(XgwcBrandDto xgwcBrandDto) {
        // 参数非空校验
        if (xgwcBrandDto == null) {
            log.error("新增品牌失败，参数为空");
            return ApiResult.error("参数为空");
        }

        try {
            xgwcBrandDto.setCreateBy(SecurityUtils.getNickName());
            xgwcBrandDto.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
            int saveResult = xgwcBrandMapper.saveXgwcBrand(xgwcBrandDto);
            if (saveResult > 0) {
                log.info("新增品牌成功，id为{}", xgwcBrandDto.getBrandId());
                return ApiResult.ok();
            } else {
                log.warn("新增品牌失败，受影响行数为0");
                return ApiResult.error("新增品牌失败");
            }
        } catch (Exception e) {
            log.error("新增品牌时发生异常", e);
            return ApiResult.error("新增品牌失败，请稍后重试");
        }
    }



    /**
     * 根据品牌id查询品牌信息
     * @param brandId 品牌id
     * @return 品牌信息
     */
    @Override
    public ApiResult getXgwcBrandById(Integer brandId) {
        if (brandId == null) {
            return ApiResult.error("品牌id不能为空");
        }

        XgwcBrandDto xgwcBrandDto = xgwcBrandMapper.getXgwcBrandById(brandId);
        if (xgwcBrandDto == null) {
            return ApiResult.error("未查询到品牌信息");
        }

        /*String status = xgwcBrandDto.getStatus();
        if (status != null) {
            try {
                int statusCode = Integer.parseInt(status);
                String statusText = XgwcShopEnum.getTextByCode(statusCode);
                xgwcBrandDto.setStatus(statusText);
            } catch (NumberFormatException e) {
                log.warn("品牌状态码无法解析: {}", status, e);
                xgwcBrandDto.setStatus("未知状态");
            }
        }*/
        return ApiResult.ok(xgwcBrandDto);
    }


    /**
     * 更新品牌信息
     * @param xgwcBrandDto 品牌信息
     * @return 更新结果
     */
    @Override
    public ApiResult updateXgwcBrandById(XgwcBrandDto xgwcBrandDto) {
        if (xgwcBrandDto == null) {
            return ApiResult.error("品牌信息不能为空");
        }

        Long brandId = xgwcBrandDto.getBrandId();
        if (brandId == null || brandId <= 0) {
            log.warn("品牌ID无效，更新失败: {}", brandId);
            return ApiResult.error("品牌ID无效");
        }

        try {
            xgwcBrandDto.setUpdateBy(SecurityUtils.getNickName());
            int updatedRows = xgwcBrandMapper.updateXgwcBrandById(xgwcBrandDto);
            if (updatedRows > 0) {
                log.info("更新品牌成功，id为{}", brandId);
                return ApiResult.ok();
            } else {
                log.error("更新品牌失败，id为{}", brandId);
                return ApiResult.error("更新品牌失败");
            }
        } catch (Exception e) {
            log.error("更新品牌时发生异常，id为{}", brandId, e);
            return ApiResult.error("更新品牌时发生异常");
        }
    }


    /**
     * 更新品牌状态
     * @param brandId 品牌id
     * @param status 状态
     * @return 更新结果
     */
    @Override
    public ApiResult updateStatusById(Integer brandId, Integer status) {
        // 参数校验：确保 brandId 合法且 status 在允许范围内
        if (brandId == null || brandId <= 0) {
            log.error("无效的品牌ID: {}", brandId);
            return ApiResult.error("更新品牌状态失败");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新品牌状态失败");
        }

        try {
            int i = xgwcBrandMapper.updateStatusById(brandId, status);
            if (i > 0) {
                log.info("更新品牌状态成功，brandId: {}, 新状态: {}", brandId, status);
                return ApiResult.ok();
            } else {
                log.error("更新品牌状态失败，brandId: {}，未找到记录或状态未更新", brandId);
                return ApiResult.error("更新品牌状态失败");
            }
        } catch (Exception e) {
            log.error("更新品牌状态时发生异常，brandId: {}, status: {}", brandId, status, e);
            return ApiResult.error("更新品牌状态失败");
        }
    }

}
