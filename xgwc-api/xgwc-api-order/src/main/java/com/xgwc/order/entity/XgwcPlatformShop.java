package com.xgwc.order.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;


@Data
public class XgwcPlatformShop {

    /**  */
    private Long id;

    /** 平台店铺id */
    private String shopId;

    /** 店铺名称 */
    private String shopName;

    /** 平台id */
    private Long platformId;

    /** 品牌商id */
    private Long brandId;


    public static XgwcPlatformShop init(JSONObject jsonObject, Long brandOwnerId) {
        XgwcPlatformShop shop = new XgwcPlatformShop();
        shop.shopId = jsonObject.getString("shop_id");
        shop.shopName = jsonObject.getString("shop_name");
        shop.platformId = jsonObject.getLong("platform_id");
        shop.brandId = brandOwnerId;
        return shop;
    }
}