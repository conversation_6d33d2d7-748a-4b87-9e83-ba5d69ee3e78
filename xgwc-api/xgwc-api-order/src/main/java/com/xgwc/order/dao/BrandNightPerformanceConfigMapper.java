package com.xgwc.order.dao;

import com.xgwc.order.entity.BrandNightPerformanceConfig;
import com.xgwc.order.entity.vo.BrandNightPerformanceConfigQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface BrandNightPerformanceConfigMapper {
    /**
     * 查询品牌商晚间业绩配置
     *
     * @param id 品牌商晚间业绩配置主键
     * @return 品牌商晚间业绩配置
     */
    BrandNightPerformanceConfig getByIdBrandId(@Param("id") Long id, @Param("brandId") Long brandId);

    /**
     * 查询品牌商晚间业绩配置列表
     *
     * @param configQueryVo 品牌商晚间业绩配置
     * @return 品牌商晚间业绩配置集合
     */
    List<BrandNightPerformanceConfig> listByQuery(BrandNightPerformanceConfigQueryVo configQueryVo);

    /**
     * 新增品牌商晚间业绩配置
     *
     * @param config 品牌商晚间业绩配置
     * @return 结果
     */
    int insert(BrandNightPerformanceConfig config);

    /**
     * 修改品牌商晚间业绩配置
     *
     * @param config 品牌商晚间业绩配置
     * @return 结果
     */
    int update(BrandNightPerformanceConfig config);

    /**
     * 批量删除品牌商晚间业绩配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int remove(@Param("ids") Long[] ids, @Param("brandId") Long brandId);
}
