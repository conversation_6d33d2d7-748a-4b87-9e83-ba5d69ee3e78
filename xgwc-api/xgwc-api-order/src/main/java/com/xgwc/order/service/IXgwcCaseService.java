package com.xgwc.order.service;

import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcCaseDto;
import com.xgwc.order.entity.es.CaseVo;
import com.xgwc.order.entity.vo.XgwcCaseQueryVo;
import com.xgwc.order.entity.vo.XgwcCaseUpStatusVo;
import com.xgwc.order.entity.vo.XgwcCaseVo;

import java.util.List;

public interface IXgwcCaseService  {

    /**
     * 查询案例表
     * @param xgwcCaseQueryVo
     * @return
     */
    public List<XgwcCaseDto> selectXgwcCaseList(XgwcCaseQueryVo xgwcCaseQueryVo);
    /**
     * 查询案例表
     * 
     * @param caseId 案例表主键
     * @return 案例表
     */
    public XgwcCaseDto selectXgwcCaseByCaseId(Long caseId);

    /**
     * 新增案例表
     * 
     * @param xgwcCase 案例表
     * @return 结果
     */
    public int insertXgwcCase(XgwcCaseVo xgwcCase);

    /**
     * 修改案例表
     * 
     * @param xgwcCase 案例表
     * @return 结果
     */
    public int updateXgwcCase(XgwcCaseVo xgwcCase);

    /**
     * 修改案例状态
     * @param vo
     * @return
     */
    public int upStatus(XgwcCaseUpStatusVo vo);

    /**
     * 批量删除案例
     * @param ids
     * @return
     */
    public int deleteByIds(Long[] ids);

    /**
     * 获取案例表信息
     *
     * @param caseId  案例表ID
     * @param isFlag  0-品牌商，1-加盟商
     * @param brandId 案例所属品牌商ID
     * @return 结果
     */
    ApiResult downloadCase(Long caseId, Integer isFlag, Long brandId);

    /**
     * 查询最大下载限制/已下载次数
     *
     * @param brandId 品牌商ID
     * @param isFlag  0-品牌商，1-加盟商
     */
    ApiResult getMaxDownloadLimit(Long brandId, Integer isFlag);

    ApiListResult search(CaseVo queryVo);
}
