package com.xgwc.order.service;

import com.xgwc.order.entity.dto.OrderRefundApplyApproveDto;
import com.xgwc.order.entity.dto.OrderRefundApplyInfoDto;
import com.xgwc.order.entity.vo.OrderRefundApplyApproveQueryVo;

import java.util.List;

public interface IOrderRefundApplyService {

    List<OrderRefundApplyApproveDto> allApprove(OrderRefundApplyApproveQueryVo queryVo);

    OrderRefundApplyInfoDto getInfo(Long applyId);

}