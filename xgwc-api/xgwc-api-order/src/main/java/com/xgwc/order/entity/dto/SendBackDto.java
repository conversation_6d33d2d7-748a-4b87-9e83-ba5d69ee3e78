package com.xgwc.order.entity.dto;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import java.util.Date;

@Data
public class SendBackDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("退回人")
    @Excel(name = "退回人")
    private String createBy;

    @FieldDesc("退回备注")
    @Excel(name = "退回备注")
    private String explanatory;

    @FieldDesc("订单Id")
    @Excel(name = "订单Id")
    private Long orderId;

    @FieldDesc("退回时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退回时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sendTime;

    @FieldDesc("派单工作台Id")
    @Excel(name = "派单工作台Id")
    private Long dispatchId;

    @FieldDesc("加盟商名称")
    @Excel(name = "加盟商名称")
    private String franchiseName;

    @FieldDesc("客户ID")
    @Excel(name = "客户ID")
    private String taobaoId;

    @FieldDesc("业务名称")
    @Excel(name = "业务名称")
    private String brandName;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;
}
