package com.xgwc.order.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class IncomLineVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("编号")
    private Long id;

    @FieldDesc("进线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inTime;

    @FieldDesc("客户ID")
    @NotNull( message = "客户ID不能为空")
    private String taobaoId;

    @FieldDesc("添加微信0否 1是")
    private Long wechatTag;

    @FieldDesc("联系电话")
    private String tel;

    @FieldDesc("来源店铺id-店铺查询")
    @NotNull( message = "来源店铺id不能为空")
    private Long shopId;

    @FieldDesc("来源店铺名称-店铺查询")
    @NotNull( message = "来源店铺名称不能为空")
    private String shopName;

    @FieldDesc("有效性编号-字典")
    @NotNull( message = "有效性编号不能为空")
    private String validDicCode;

    @FieldDesc("有效性名称-字典")
    @NotNull( message = "有效性名称不能为空")
    private String validDicName;

    @FieldDesc("有效性编号-字典2")
    private String validDicCode2;

    @FieldDesc("有效性名称-字典2")
    private String validDicName2;

    @FieldDesc("关联单号id-订单查询")
    private Long orderId;

    @FieldDesc("关联单号编号-订单查询")
    private String orderNo;

    @FieldDesc("所属业务编号-字典")
    @NotNull( message = "所属业务编号不能为空")
    private String stateDicCode;

    @FieldDesc("所属业务名称-字典")
    @NotNull( message = "所属业务名称不能为空")
    private String stateDicName;

    @FieldDesc("回访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull( message = "回访时间不能为空")
    private Date nextTime;

    @FieldDesc("需求")
    private String needInfo;

    @FieldDesc("销售ID")
    private Long saleId;

    @FieldDesc("销售名称")
    private String saleName;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("部门姓名")
    private String deptName;

    @FieldDesc("跟进状态")
    private String trackName;
    private String trackCode;
    private String trackType;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("预算")
    private BigDecimal planMoney;

}
