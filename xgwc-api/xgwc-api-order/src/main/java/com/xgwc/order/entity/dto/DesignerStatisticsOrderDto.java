package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DesignerStatisticsOrderDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long id;

    @FieldDesc("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @FieldDesc("下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("所属品牌商")
    private String brandName;

    @FieldDesc("业务类型名称")
    private String businessName;

    @FieldDesc("评价")
    private List<OrderScoreDto> scoreList;

}
