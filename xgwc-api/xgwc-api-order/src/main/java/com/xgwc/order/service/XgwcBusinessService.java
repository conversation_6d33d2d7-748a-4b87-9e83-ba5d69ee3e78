package com.xgwc.order.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.BusinessSettingDto;
import com.xgwc.order.entity.dto.XgwcBusinessDto;
import com.xgwc.order.entity.param.XgwcBusinessParam;
import com.xgwc.order.entity.vo.XgwcBusinessInfo;
import com.xgwc.order.entity.vo.XgwcBusinessTreeVo;
import com.xgwc.order.entity.vo.XgwcBusinessVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  09:31
 */
public interface XgwcBusinessService {

    /**
     * 获取业务列表
     * @param xgwcBusinessParam 业务参数
     * @return 业务列表
     */
    List<XgwcBusinessInfo> getXgwcBusinessList(XgwcBusinessParam xgwcBusinessParam);

    /**
     * 保存业务
     * @param xgwcBusinessDto 业务参数
     * @return 保存结果
     */
    ApiResult saveXgwcBusiness(XgwcBusinessDto xgwcBusinessDto);

    /**
     * 获取业务详情
     * @param businessId 业务ID
     * @return 业务详情
     */
    ApiResult getXgwcBusinessById(Long businessId);

    /**
     * 更新业务
     * @param xgwcBusinessDto 业务参数
     * @return 更新结果
     */
    ApiResult updateXgwcBusinessById(XgwcBusinessDto xgwcBusinessDto);

    /**
     * 更新业务状态
     * @param businessId 业务ID
     * @param status 业务状态
     * @return 更新结果
     */
    ApiResult updateStatusById(Integer businessId, Integer status);

    /**
     * 查询业务树（一级目录为品牌商）
     * @param brandOwnerId 品牌商ID
     * @return 业务树
     */
    List<XgwcBusinessInfo> getBusinessTreeByBrandOwnerId(List<Long> brandOwnerId);

    /**
     * 获取业务详情（Feign调用）
     * @param businessId 业务ID
     * @return 业务详情
     */
    XgwcBusinessVo getXgwcBusinessByIdFeign(Long businessId);

    /**
     * 根据业务id查询上下级数据
     * @param businessId 业务ID
     * @return 业务树
     */
    List<XgwcBusinessTreeVo> getBusinessRelation(Long businessId);

    /**
     * 获取一级业务列表
     *
     * @return 业务列表
     */
    List<XgwcBusinessVo> getBusinessOnlyFirst();

    /**
     * 查询业务设置列表（一级业务）
     *
     * @param businessName 业务名称
     * @return 业务设置列表
     */
    List<BusinessSettingDto> getBusinessSettingList(String businessName);

    /**
     * 获取业务设置详情（一级业务）
     *
     * @param businessId 业务ID
     * @return 业务设置详情
     */
    BusinessSettingDto getBusinessSettingById(Long businessId);

    /**
     * 更新业务设置
     *
     * @param businessSettingDto 业务设置参数
     * @return 更新结果
     */
    ApiResult updateBusinessSetting(BusinessSettingDto businessSettingDto);
}
