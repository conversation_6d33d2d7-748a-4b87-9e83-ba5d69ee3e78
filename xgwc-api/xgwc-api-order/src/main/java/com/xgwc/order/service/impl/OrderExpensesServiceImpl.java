package com.xgwc.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.xgwc.activiti.feign.api.ExecutionFeign;
import com.xgwc.common.entity.dto.FlowExecutionDto;
import com.xgwc.activiti.feign.entity.FlowExecutionVo;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.AutoIdUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.OrderExpensesApplyMapper;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.entity.OrderExpensesApply;
import com.xgwc.order.entity.dto.FranchiseStaffDto;
import com.xgwc.order.entity.dto.OrderDto;
import com.xgwc.order.entity.dto.OrderExpensesApplyDto;
import com.xgwc.order.entity.dto.OrderExpensesDto;
import com.xgwc.order.entity.vo.OrderExpensesQueryVo;
import com.xgwc.order.service.OrderExpensesService;
import com.xgwc.user.feign.api.BrandOwnerFeign;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.api.ServiceAuthorizeFeign;
import com.xgwc.user.feign.api.StaffFeign;
import com.xgwc.user.feign.entity.BrandOwnerDto;
import com.xgwc.user.feign.entity.FranchiseDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class OrderExpensesServiceImpl implements OrderExpensesService {

    @Resource
    private OrderExpensesApplyMapper orderExpensesApplyMapper;

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private FranchiseFeign franchiseFeign;
    @Resource
    private BrandOwnerFeign brandOwnerFeign;
    @Resource
    private StaffFeign staffFeign;
    @Resource
    private ExecutionFeign executionFeign;
    @Resource
    private ServiceAuthorizeFeign serviceAuthorizeFeign;


    @Override
    public ApiResult insertOrderExpensesApply(OrderExpensesApply orderExpensesApply) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null){
            return ApiResult.error("非加盟商不能发起报销");
        }
        if(orderExpensesApplyMapper.getOrderRefundApplyByOrderNo(orderExpensesApply.getOrderNo())!= null){
            return ApiResult.error("该订单正在报销中");
        }
        if(orderExpensesApply.getRefundAmount() == null || StringUtils.isEmpty(orderExpensesApply.getOrderNo())){
            log.warn("缺失参数:{}", JSONObject.toJSONString(orderExpensesApply));
            return ApiResult.error("缺少参数");
        }
        List<OrderExpensesDto> list = orderExpensesApplyMapper.findOrderExpensesApplyByOrderNo(orderExpensesApply.getOrderNo());
        OrderDto orderDto = orderMapper.selectOrderByOrderNo(orderExpensesApply.getOrderNo());
        if(orderDto != null){
            Integer transferState = orderDto.getTransferState();
            if(!(transferState != null && (transferState == 1 || transferState == 2))){
                return ApiResult.error("非转换单和协作单不能报销");
            }else if(orderDto.getPid() != null && orderDto.getPid() != 0){
                return ApiResult.error("非母订单不能报销");
            }
            BigDecimal historyRefundAmount = list.stream().map(OrderExpensesDto::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal maxAllowRefund = orderDto.getAmount().add(BigDecimal.TEN);
            BigDecimal totalRefundAfterApply = historyRefundAmount.add(orderExpensesApply.getRefundAmount());
            if (totalRefundAfterApply.compareTo(maxAllowRefund) > 0) {
                return ApiResult.error("报销金额不可超过实收 " + maxAllowRefund);
            }
            orderExpensesApply.setId(AutoIdUtil.getId());
            orderExpensesApply.setApplyUserId(sysUser.getUserId());
            orderExpensesApply.setApplyUserName(sysUser.getUserName());
            orderExpensesApply.setActualAmount(orderDto.getAmount());
            orderExpensesApply.setFranchiseId(franchiseId);
            orderExpensesApply.setCustomerNo(orderDto.getTaobaoId());
            orderExpensesApply.setBrandId(orderDto.getBrandId());
            // 启动工作流
            ApiResult<Long> apiResult = startFlow(orderExpensesApply);
            if(apiResult.getStatus() != 1){
                return ApiResult.error(apiResult.getMessage());
            }
            Long executionId = apiResult.getData();
            orderExpensesApply.setApplyStatus("ING");
            orderExpensesApply.setExecutionId(executionId);
            int result = orderExpensesApplyMapper.insertOrderExpensesApply(orderExpensesApply);
            return result == 1 ? ApiResult.ok() : ApiResult.error("保存失败");
        }else{
            return ApiResult.error("订单编号不存在");
        }
    }

    @Override
    public int updateOrderExpensesApply(OrderExpensesApply orderExpensesApply) {
        if(orderExpensesApply.getId() == null){
            log.warn("订单报销id不能为空:{}", JSONObject.toJSONString(orderExpensesApply));
            return 0;
        }
        return orderExpensesApplyMapper.updateOrderExpensesApply(orderExpensesApply);
    }

    @Override
    public OrderExpensesApplyDto selectOrderExpensesApplyById(Long id) {
        return orderExpensesApplyMapper.selectOrderExpensesApplyById(id);
    }

    @Override
    public void executeExpense(FlowExecutionDto execution) {
        OrderExpensesApply orderExpensesApply = new OrderExpensesApply();
        orderExpensesApply.setId(execution.getBusinessKey());
        orderExpensesApply.setApplyStatus(execution.getStatus());
        orderExpensesApply.setLastApproveTime(DateFormatUtils.format(execution.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
        updateOrderExpensesApply(orderExpensesApply);
    }

    @Override
    public List<OrderExpensesDto> selectAllOrderExpensesApply(OrderExpensesQueryVo queryVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Integer userType = sysUser.getUserType();
        if (userType == 1 || userType == 4) {
            queryVo.setBrandIdList(ImmutableList.of(sysUser.getBrandId()));
        } else if (userType == 2 || userType == 5) {
            queryVo.setFranchiseId(sysUser.getFranchiseId());
        } else if (userType == 6) {
            queryVo.setBrandIdList(serviceAuthorizeFeign.getServiceBrandIdList(sysUser.getServiceId()));
            if (queryVo.getBrandIdList().isEmpty()) {
                return Lists.newArrayList();
            }
        } else {
            return Lists.newArrayList();
        }
        List<OrderExpensesDto> list = orderExpensesApplyMapper.findAllOrderExpensesApply(queryVo);
        return list;
    }

    // 启动工作流
    private ApiResult<Long> startFlow(OrderExpensesApply vo){
        FlowExecutionVo flowExecutionVo = new FlowExecutionVo();
        flowExecutionVo.setTitle(vo.getOrderNo());
        flowExecutionVo.setFlowValue("expense");
        flowExecutionVo.setBusinessKey(vo.getId());
        flowExecutionVo.setBrandId(vo.getBrandId());
        flowExecutionVo.setCreateBy(vo.getApplyUserId());
        flowExecutionVo.setCreateName(vo.getApplyUserName());
        ApiResult<BrandOwnerDto> result = brandOwnerFeign.getBrandOwnerById(vo.getBrandId());
        if(result.getStatus() == 1 && result.getData() != null){
            BrandOwnerDto data = result.getData();
            flowExecutionVo.setBrandName(data.getCompanyName());
        }
        flowExecutionVo.setFranchiseId(vo.getFranchiseId());
        ApiResult<FranchiseDto> franchiseResult = franchiseFeign.getFranchiseById(vo.getFranchiseId());
        if(franchiseResult.getStatus() == 1 && franchiseResult.getData() != null){
            FranchiseDto franchiseData = franchiseResult.getData();
            flowExecutionVo.setFranchiseName(franchiseData.getFranchiseName());
        }
        ApiResult staffResult = staffFeign.getFranchiseStaffInfoByUserIdAndFranchiseId(SecurityUtils.getSysUser().getUserId(), vo.getFranchiseId());
        if (staffResult.getStatus() == 1 && staffResult.getData() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            FranchiseStaffDto franchiseStaffDto = objectMapper.convertValue(staffResult.getData(), FranchiseStaffDto.class);
            if (franchiseStaffDto != null && franchiseStaffDto.getDeptId() != null) {
                flowExecutionVo.setDeptId(franchiseStaffDto.getDeptId());
                flowExecutionVo.setDeptName(franchiseStaffDto.getDeptName());
            }
        }
        flowExecutionVo.setVariable(vo);
        return executionFeign.insertFlowExecution(flowExecutionVo);
    }
}
