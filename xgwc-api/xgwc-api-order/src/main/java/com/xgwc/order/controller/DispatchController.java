package com.xgwc.order.controller;

import java.util.List;

import com.xgwc.order.entity.dto.DispatchPageDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.order.entity.dto.DispatchDto;
import com.xgwc.order.entity.vo.DispatchQueryVo;
import com.xgwc.order.service.IDispatchService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

/**
 * 派单工作台Controller
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
@RestController
@RequestMapping("/dispatch")
public class DispatchController extends BaseController {
    @Autowired
    private IDispatchService dispatchService;

    /**
     * 查询派单工作台列表
     */
    @MethodDesc("查询派单工作台列表")
    @PreAuthorize("@ss.hasPermission('dispatch:dispatch:list')")
    @GetMapping("/list")
    public ApiResult<DispatchPageDto> list(DispatchQueryVo dispatch) {
        startPage();
        List<DispatchPageDto> list = dispatchService.selectDispatchList(dispatch);
        return getDataTable(list);
    }

    @MethodDesc("统计")
    @GetMapping("/statistics")
    public ApiResult statistics(){
        return success(dispatchService.statistics());
    }

    /**
     * 获取派单工作台详细信息
     */
    @MethodDesc("获取派单工作台详细信息")
    @PreAuthorize("@ss.hasPermission('dispatch:dispatch:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<DispatchDto> getInfo(@PathVariable("id") Long id) {
        return success(dispatchService.selectDispatchById(id));
    }

}
