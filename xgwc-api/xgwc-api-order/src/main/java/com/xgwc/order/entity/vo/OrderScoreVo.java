package com.xgwc.order.entity.vo;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class OrderScoreVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("评分id")
    private Long id;

    @FieldDesc("评分模块")
    private String scoreModel;

    @FieldDesc("评分分值")
    private Long scoreNum;
    @FieldDesc("排序")
    private Integer sort;

    @FieldDesc("评分项")
    private List<String> scoreItem;


    public String getScoreItem() {
        if(scoreItem==null || scoreItem.isEmpty()){
            return null;
        }
        return scoreItem.stream().collect(Collectors.joining(","));
    }
}
