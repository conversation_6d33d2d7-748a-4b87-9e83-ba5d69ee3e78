package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DesignerStatisticsQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("平均佣金")
    private BigDecimal avgMoney1;
    private BigDecimal avgMoney2;

    @FieldDesc("所属品牌商")
    private Long brandId;

    @FieldDesc("擅长业务（业务类型编码）")
    private Long[] businessId;

    @FieldDesc("设计师等级")
    private String designerLevel;

    @FieldDesc("设计师名称")
    private String designerName;

    @FieldDesc("设计师电话")
    private String designerPhone;

    @FieldDesc("最后接单时间")
    private String lastAcceptTime;

    @FieldDesc("最后接单时间")
    private Date orderLastTime;

    @FieldDesc("排序字段")
    private String field;

    @FieldDesc("升降序")
    private String direction;


}
