package com.xgwc.order.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.order.entity.dto.ChatRecordDto;
import com.xgwc.order.entity.vo.ChatRecordQueryVo;
import com.xgwc.order.entity.vo.ChatRecordStatusVo;
import com.xgwc.order.entity.vo.ChatRecordVo;
import com.xgwc.order.service.IChatRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 聊天记录Controller
 */
@RestController
@RequestMapping("/chatRecord")
public class ChatRecord<PERSON>ontroller extends BaseController {
    @Autowired
    private IChatRecordService chatRecordService;

    /**
     * 查询聊天记录列表
     */
    @MethodDesc("查询聊天记录列表")
    @PreAuthorize("@ss.hasPermission('chatRecord:chatRecord:list')")
    @GetMapping("/list")
    public ApiResult<ChatRecordDto> list(ChatRecordQueryVo chatRecord) {
        startPage();
        List<ChatRecordDto> list = chatRecordService.selectChatRecordList(chatRecord);
        return getDataTable(list);
    }

    /**
     * 获取聊天记录详细信息
     */
    @MethodDesc("获取聊天记录详细信息")
    @PreAuthorize("@ss.hasPermission('chatRecord:chatRecord:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<ChatRecordDto> getInfo(@PathVariable("id") Long id) {
        return success(chatRecordService.selectChatRecordById(id));
    }

    /**
     * 新增聊天记录
     */
    @MethodDesc("新增聊天记录")
    @PreAuthorize("@ss.hasPermission('chatRecord:chatRecord:add')")
    @Log(title = "聊天记录", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody ChatRecordVo chatRecord) {
        return toAjax(chatRecordService.insertChatRecord(chatRecord));
    }

    /**
     * 修改聊天记录
     */
    @MethodDesc("修改聊天记录")
    @PreAuthorize("@ss.hasPermission('chatRecord:chatRecord:edit')")
    @Log(title = "聊天记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody ChatRecordVo chatRecord) {
        return toAjax(chatRecordService.updateChatRecord(chatRecord));
    }

    /**
     * 删除聊天记录
     */
    @MethodDesc("删除聊天记录")
    @PreAuthorize("@ss.hasPermission('chatRecord:chatRecord:remove')")
    @Log(title = "聊天记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public ApiResult remove(@PathVariable Long id) {
        return toAjax(chatRecordService.deleteChatRecordById(id));
    }

    /**
     * 修改状态
     */
    @MethodDesc("修改状态")
    @PreAuthorize("@ss.hasPermission('chatRecord:chatRecord:edit')")
    @Log(title = "聊天记录", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public ApiResult updateStatus(@RequestBody ChatRecordStatusVo req) {
        return toAjax(chatRecordService.updateChatRecordStatus(req));
    }

    /**
     * 大搜索-获取聊天记录列表
     */
    @MethodDesc("大搜索-获取聊天记录列表")
    @PreAuthorize("@ss.hasPermission('chatRecord:chatRecord:list')")
    @PostMapping("/listBig")
    public ApiResult listBig(@RequestBody ChatRecordQueryVo chatRecord) {
        return ApiResult.ok(chatRecordService.selectChatRecordBigList(chatRecord));
    }

    /**
     * 大搜索-获取聊天记录详情
     */
    @MethodDesc("大搜索-获取聊天记录详情")
    @PreAuthorize("@ss.hasPermission('chatRecord:chatRecord:query')")
    @GetMapping("/getInfoBig/{id}")
    public ApiResult getInfoBig(@PathVariable("id") Long id) throws JsonProcessingException {
        return chatRecordService.getInfoBigById(id);
    }

}
