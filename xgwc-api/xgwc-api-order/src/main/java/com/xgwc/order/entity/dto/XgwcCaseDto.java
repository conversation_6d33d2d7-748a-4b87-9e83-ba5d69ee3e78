package com.xgwc.order.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import java.util.Date;

@Data
public class XgwcCaseDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("实收金额")
    @Excel(name = "实收金额")
    private BigDecimal amount;

    @FieldDesc("上传定稿源文件")
    @Excel(name = "上传定稿源文件")
    private String archiveFiles;

    @FieldDesc("定稿预览图")
    @Excel(name = "定稿预览图")
    private String archiveImg;

    @FieldDesc("品牌商id")
    @Excel(name = "品牌商id")
    private Long brandId;

    @FieldDesc("品牌商名称")
    private String brandName;

    @FieldDesc("业务id")
    @Excel(name = "业务id")
    private Long businessId;

    @FieldDesc("业务名称")
    @Excel(name = "业务名称")
    private String businessName;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long caseId;

    @FieldDesc("等级：1高 2中 3低")
    @Excel(name = "等级：1高 2中 3低")
    private Integer caseLevel;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("设计师ID")
    @Excel(name = "设计师ID")
    private Long designerId;

    @FieldDesc("设计师名称")
    @Excel(name = "设计师名称")
    private String designerName;

    @FieldDesc("设计师手机号")
    private String designerPhone;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("关键字")
    @Excel(name = "关键字")
    private String keyword;

    @FieldDesc("链接")
    @Excel(name = "链接")
    private String linkUrl;

    @FieldDesc("佣金金额")
    @Excel(name = "佣金金额")
    private BigDecimal money;

    @FieldDesc("订单id")
    @Excel(name = "订单id")
    private Long oderId;

    @FieldDesc("案例名称")
    private String title;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("设计师业务名称")
    @Excel(name = "设计师业务名称")
    private String designerBusinessName;

    @FieldDesc("下载次数")
    @Excel(name = "下载次数")
    private Integer downloadCount;


    private Integer status;
}
