package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.PageUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.dao.XgwcPlatformShopMapper;
import com.xgwc.order.entity.XgwcPlatformShop;
import com.xgwc.order.entity.vo.XgwcPlatformShopQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/xgwcPlatformShop")
public class XgwcPlatformShopController extends BaseController {

    @Autowired
    private XgwcPlatformShopMapper xgwcPlatformShopMapper;

    @MethodDesc("查询品牌商平台店铺列表")
    @GetMapping("/list")
    public ApiResult<XgwcPlatformShop> list(XgwcPlatformShopQueryVo shopQueryVo) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if (brandId == null) {
            return ApiResult.ok();
        }
        shopQueryVo.setBrandId(brandId);
        PageUtils.startPage();
        List<XgwcPlatformShop> list = xgwcPlatformShopMapper.selectXgwcPlatformShopList(shopQueryVo);
        return getDataTable(list);
    }


}
