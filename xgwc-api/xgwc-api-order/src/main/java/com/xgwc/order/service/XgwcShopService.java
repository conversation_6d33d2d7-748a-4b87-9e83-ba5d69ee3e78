package com.xgwc.order.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.XgwcShopDto;
import com.xgwc.order.entity.param.XgwcShopParam;
import com.xgwc.order.entity.vo.XgwcShopVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhu<PERSON>
 * @CreateTime: 2025-04-22  16:43
 */
public interface XgwcShopService {

    /**
     * 获取店铺列表
     * @param xgwcShopParam 店铺参数
     * @return 店铺列表
     */
    List<XgwcShopVo> getXgwcShopList(XgwcShopParam xgwcShopParam);

    /**
     * 保存店铺
     *
     * @param xgwcShopDto 店铺参数
     * @return 结果
     */
    ApiResult saveXgwcShop(XgwcShopDto xgwcShopDto);

    /**
     * 根据id获取店铺
     * @param shopId 店铺id
     * @return 店铺
     */
    ApiResult getXgwcShopById(Long shopId);

    /**
     * 更新店铺
     * @param xgwcShopDto 店铺参数
     * @return 结果
     */
    ApiResult updateXgwcShopById(XgwcShopDto xgwcShopDto);

    /**
     * 更新店铺状态
     * @param shopId 店铺id
     * @param status 状态
     * @return 结果
     */
    ApiResult updateStatusById(Long shopId, Integer status);

    /**
     * 批量查询店铺
     * @param ids ids
     * @return 结果
     */
    List<XgwcShopVo> getShopByIds(List<Long> ids);

    /**
     * 根据franchiseId查询店铺
     *
     * @param franchiseId 分店id
     * @return 店铺
     */
    List<XgwcShopDto> getShopByFranchiseId(Long franchiseId);

    /**
     * 获取财务服务商客户店铺列表
     *
     * @param xgwcShopParam 店铺参数
     * @return 店铺列表
     */
    List<XgwcShopDto> getServiceProviderShopList(XgwcShopParam xgwcShopParam);

    /**
     * 更新店铺负责人
     *
     * @param xgwcShopDto  店铺
     * @return 结果
     */
    ApiResult updateShopManager(XgwcShopDto xgwcShopDto);
}
