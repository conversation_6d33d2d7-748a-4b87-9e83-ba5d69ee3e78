package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class DispatchQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("类型：1:未领取总数 2:紧急单 3:困难单")
    private Integer type;

    @FieldDesc("已派设计师数量")
    private Integer dispatchNumber;

    @FieldDesc("派单状态：1 未领取 2 已领取 3 已派单")
    private Integer dispatchStatus;

    @FieldDesc("1 普通单 2 紧急单")
    private String dispatchType;

    @FieldDesc("")
    private Long id;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;

    @FieldDesc("需派设计师数")
    private Integer needDispatchNumber;

    @FieldDesc("订单Id")
    private Long orderId;

    @FieldDesc("领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  receiveTime;

    @FieldDesc("退回次数")
    private Integer returnNumber;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    private Long brandId;

    private String franchiseIds;

}
