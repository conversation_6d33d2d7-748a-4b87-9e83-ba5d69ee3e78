package com.xgwc.order.entity.es;

import lombok.Data;

@Data
public class ChatVo {

    /**
     * 检索聊天内容
     */
    private String content;

    /**
     * 业务编码
     */
    private String typeCodes;

    /**
     * 品牌ID
     */
    private Long branchId;

    /**
     * 品牌商ID
     */
    private Long franchiseId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页条数
     */
    private Integer pageSize;

    public Integer getPageNum() {
        return pageNum == null ? 1 : pageNum;
    }

    public Integer getPageSize() {
        return pageSize == null ? 10 : pageSize > 50 ? 50 : pageSize;
    }
}
