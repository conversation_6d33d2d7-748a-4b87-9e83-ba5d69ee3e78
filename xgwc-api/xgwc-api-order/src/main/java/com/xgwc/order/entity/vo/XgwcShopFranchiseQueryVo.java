package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.List;

@Data
public class XgwcShopFranchiseQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("所属品牌商")
    private Long brandOwnerId;

    @FieldDesc("业务Id")
    private Long businessId;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("加盟商id")
    private Long franchiseId;
    /** 渠道名称 */
    @FieldDesc("渠道id")
    private Long channelId;

    @FieldDesc("品牌店铺id")
    private Long shopId;

    @FieldDesc("状态：0开业 ，1停业")
    private Integer status;

    @FieldDesc("品牌店铺名称")
    private String shopName;
}
