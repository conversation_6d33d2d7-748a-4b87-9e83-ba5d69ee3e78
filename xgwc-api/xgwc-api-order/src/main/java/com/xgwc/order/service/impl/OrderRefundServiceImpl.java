package com.xgwc.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.order.dao.OrderMapper;
import com.xgwc.order.dao.OrderPayMapper;
import com.xgwc.order.dao.OrderRefundApplyMapper;
import com.xgwc.order.dao.XgwcPlatformRefundMapper;
import com.xgwc.order.entity.Order;
import com.xgwc.order.entity.OrderPay;
import com.xgwc.order.entity.OrderRefundApply;
import com.xgwc.order.entity.OrderRefundCommision;
import com.xgwc.order.entity.OrderRefundPay;
import com.xgwc.order.entity.XgwcPlatformRefund;
import com.xgwc.order.entity.dto.OrderDto;
import com.xgwc.order.entity.dto.OrderPayDto;
import com.xgwc.order.entity.dto.OrderRefundApplyDto;
import com.xgwc.order.entity.dto.SubOrderDto;
import com.xgwc.order.entity.vo.OrderRefundApplyVo;
import com.xgwc.order.service.IAfterSalesFlowLogService;
import com.xgwc.order.service.OrderRefundService;
import com.xgwc.order.util.OrderUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderRefundServiceImpl implements OrderRefundService {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderPayMapper orderPayMapper;
    @Resource
    private XgwcPlatformServiceImpl xgwcPlatformServiceImpl;
    @Resource
    private OrderRefundApplyMapper orderRefundApplyMapper;
    @Resource
    private XgwcPlatformRefundMapper xgwcPlatformRefundMapper;
    @Resource
    private IAfterSalesFlowLogService afterSalesFlowLogService;
    @Resource
    private OrderRefundApplyDomainService orderRefundApplyDomainService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiResult insertOrderRefundApply(OrderRefundApplyVo orderRefundApplyVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if (franchiseId == null) {
            return ApiResult.error("非加盟商不能发起退款申请");
        }
        orderRefundApplyVo.setFranchiseId(franchiseId);
        return orderRefundApplyDomainService.insertOrderRefundApply(orderRefundApplyVo, sysUser.getUserId(), sysUser.getUserName());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateOrderRefundApply(OrderRefundApply orderRefundApply) {
        int result = orderRefundApplyMapper.updateOrderRefundApply(orderRefundApply);
        if(result > 0){
            //如果是审核通过状态
            if(orderRefundApply.getApplyStatus().equals("PASS")){
                OrderRefundApplyDto orderRefundApplyDto = orderRefundApplyMapper.getOrderRefundApplyDtoById(orderRefundApply.getId());
                if(orderRefundApplyDto != null) {
                    Order order = new Order();
                    order.setId(orderRefundApplyDto.getPreId());
                    //step1: 更新订单实收金额为原来的金额-退款的金额=剩下的实收金额
                    order.setNowAmount(orderRefundApplyDto.getLastAmount());
                    orderMapper.updateOrder(order);
                    updateOrderPay(orderRefundApplyDto);
                    updateOrderCommision(orderRefundApplyDto);
                }
            }
        }
        return result;
    }

    /**
     * 更新佣金相关信息
     */
    private void updateOrderCommision(OrderRefundApplyDto orderRefundApplyDto){
        List<OrderRefundCommision> orderRefundCommisionList = orderRefundApplyMapper.getOrderRefundCommisionListByApplyId(orderRefundApplyDto.getId());
        if(orderRefundCommisionList != null && !orderRefundCommisionList.isEmpty()){
            List<Order> orders = orderRefundCommisionList.stream().map(x->{
                Order order = new Order();
                order.setId(x.getPreId());
                order.setNowMoney(x.getAfterCommisionAmount());
                return order;
            }).collect(Collectors.toList());
            //批量更新佣金
            orderMapper.batchUpdateOrders(orders);
        }
    }

    /**
     * 审核通过同步更新订单支付信息
     */
    private void updateOrderPay(OrderRefundApplyDto orderRefundApplyDto){
        List<OrderRefundPay> orderRefundPayList = orderRefundApplyMapper.getOrderRefundPayListByApplyId(orderRefundApplyDto.getId());
        //如果是退垫付
        if (orderRefundPayList != null && !orderRefundPayList.isEmpty()) {
            if (orderRefundApplyDto.getRefundType() == 3) {
                for (OrderRefundPay orderRefundPay : orderRefundPayList) {
                    Long preId = orderRefundPay.getPreId();
                    //preId为新增的退垫付到账记录
                    if(preId == null){
                        OrderPay orderPay = new OrderPay();
                        orderPay.setPayImg(orderRefundPay.getPayImg());
                        orderPay.setAmount(orderRefundPay.getAmount());
                        orderPay.setPayChannel(orderRefundPay.getPayChannel());
                        orderPay.setCollectionNo(orderRefundPay.getCollectionNo());
                        orderPay.setCreateTime(new Date());
                        orderPay.setOderId(orderRefundApplyDto.getPreId());
                        orderPay.setPayType(orderRefundPay.getPayType());
                        orderPayMapper.insertOrderPay(orderPay);
                    }else{
                        orderPayMapper.deleteOrderPayById(orderRefundPay.getPreId());
                    }
                }
            } else {
                //更新每个子流程
                for (OrderRefundPay orderRefundPay : orderRefundPayList) {
                    OrderPay orderPay = new OrderPay();
                    if (orderRefundPay.getPreId() != null) {
                        orderPay.setId(orderRefundPay.getPreId());
                        //原有金额-退款金额
                        orderPay.setNowAmount(orderRefundPay.getAmount().subtract(orderRefundPay.getRefundAmount()));
                        orderPayMapper.updateOrderPay(orderPay);
                    }
                }
            }
        }
    }

    @Override
    public OrderRefundApplyDto getOrderRefundApplyByApplyId(Long applyId) {
        OrderRefundApplyDto orderRefundApplyDto = orderRefundApplyMapper.getOrderRefundApplyDtoById(applyId);
        if(orderRefundApplyDto != null){
            List<OrderRefundPay> orderRefundPayList = orderRefundApplyMapper.getOrderRefundPayListByApplyId(applyId);
            if(!orderRefundPayList.isEmpty()){
                BigDecimal totalRefundAmount = new BigDecimal(0);
                for(OrderRefundPay orderRefundPay : orderRefundPayList){
                    totalRefundAmount = totalRefundAmount.add(orderRefundPay.getRefundAmount());
                }
                orderRefundApplyDto.setTotalRefundAmount(totalRefundAmount);
            }
            List<OrderRefundCommision> orderRefundCommisionList = orderRefundApplyMapper.getOrderRefundCommisionListByApplyId(applyId);
            if(!orderRefundCommisionList.isEmpty()){
                for (OrderRefundCommision item : orderRefundCommisionList) {
                    if(StringUtils.isNotEmpty(item.getDesignerPhone())){
                        item.setDesignerPhone(ParamDecryptUtil.decryptParam(item.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
                    }
                }
            }
            orderRefundApplyDto.setFlowLogList(afterSalesFlowLogService.selectAfterSalesFlowLogByBusinessId(applyId));
            orderRefundApplyDto.setPayList(orderRefundPayList);
            orderRefundApplyDto.setCommisionList(orderRefundCommisionList);
        }
        return orderRefundApplyDto;
    }

    @Override
    public OrderRefundApplyDto getOrderRefundInfoByOrderNo(String orderNo) {
        OrderDto orderDto = orderMapper.selectOrderByOrderNo(orderNo);
        if(orderDto != null){
            OrderRefundApplyDto orderRefundApplyDto = new OrderRefundApplyDto();
            orderRefundApplyDto.setOrderDate(orderDto.getOrderDate());
            orderRefundApplyDto.setOrderAmount(orderDto.getOrderAmount());
            orderRefundApplyDto.setAmount(orderDto.getNowAmount());
            orderRefundApplyDto.setOrderNo(orderNo);
            orderRefundApplyDto.setSaleManName(orderDto.getSaleManName());
            orderRefundApplyDto.setCustomerNo(orderDto.getTaobaoId());
            orderRefundApplyDto.setTaobaoId(orderDto.getTaobaoId());
            List<OrderPayDto> orderPays = orderPayMapper.selectOrderPayByOrderId(orderDto.getId());
            if (orderPays != null && !orderPays.isEmpty()) {
                List<OrderRefundPay> orderRefundPayList = orderPays.stream().map(x -> {
                    OrderRefundPay orderRefundPay = new OrderRefundPay();
                    orderRefundPay.setAmount(x.getNowAmount());
                    orderRefundPay.setNowAmount(x.getNowAmount());
                    orderRefundPay.setPayChannel(x.getPayChannel());
                    orderRefundPay.setPayType(x.getPayType());
                    orderRefundPay.setCollectionNo(x.getCollectionNo());
                    orderRefundPay.setPayImg(x.getPayImg());
                    orderRefundPay.setPreId(x.getId());
                    orderRefundPay.setNewAmount(x.getNowAmount());
                    return orderRefundPay;
                }).collect(Collectors.toList());
                this.initIsRefundable(orderRefundPayList, orderDto.getBrandId());
                orderRefundApplyDto.setPayList(orderRefundPayList);
            }
            List<SubOrderDto> subOrders = orderMapper.selectSubOrderList(orderDto.getId());
            if(orderDto.getDesignerId() != null) subOrders.add(0, BeanUtil.copyProperties(orderDto, SubOrderDto.class));
            if(subOrders != null && !subOrders.isEmpty()){
                List<OrderRefundCommision> orderRefundCommisionList = subOrders.stream().map(x->{
                    OrderRefundCommision orderRefundCommision = new OrderRefundCommision();
                    orderRefundCommision.setSubOrderNo(x.getOrderNo());
                    orderRefundCommision.setPreId(x.getId());
                    orderRefundCommision.setCommisionAmount(x.getMoney());
                    orderRefundCommision.setDesignerUserId(x.getDesignerId());
                    orderRefundCommision.setDesignerUserName(x.getDesignerName());
                    orderRefundCommision.setDesignerPhone(ParamDecryptUtil.decryptParam(x.getDesignerPhone(), ParamDecryptUtil.PHONE_KEY));
                    orderRefundCommision.setDesignerBusiness(x.getDesignerBusiness());
                    return orderRefundCommision;
                }).collect(Collectors.toList());
                orderRefundApplyDto.setCommisionList(orderRefundCommisionList);
            }
            return orderRefundApplyDto;
        }
        return null;
    }

    private void initIsRefundable(List<OrderRefundPay> orderPays, Long brandId) {
        // 是否有可退流水. 如果没有则异常提示
        if (orderPays.stream().noneMatch(x -> x.getRefundableAmount().compareTo(BigDecimal.ZERO) > 0)) {
            throw new ApiException("当前订单金额已全部退款，无法再进行退款");
        }

        List<OrderRefundPay> platformPayList = orderPays.stream().filter(x -> OrderUtils.isPlatformPayByChannel(x.getPayChannel())).toList();
        Map<String, BigDecimal> platformRefundableAmountMap = Maps.newHashMap();
        Map<String, List<XgwcPlatformRefund>> orderRefundListMap = Maps.newHashMap();
        if (!platformPayList.isEmpty()) {
            platformRefundableAmountMap = platformPayList.stream().collect(Collectors.toMap(OrderRefundPay::getCollectionNo, OrderRefundPay::getRefundableAmount));
            orderRefundListMap = xgwcPlatformRefundMapper.listByTradeNo(platformPayList.stream().map(OrderRefundPay::getCollectionNo).distinct().toList(), brandId).stream().collect(Collectors.groupingBy(XgwcPlatformRefund::getTradeNo));
        }
        for (OrderRefundPay orderPayDto : orderPays) {
            // 之前的退款方式 TODO
            orderPayDto.setLastRefundPayChannel(orderPayDto.getPayChannel());
            if (OrderUtils.isPlatformPayByChannel(orderPayDto.getPayChannel())) {
                List<XgwcPlatformRefund> refundList = orderRefundListMap.get(orderPayDto.getCollectionNo());
                if (refundList == null) {
                    refundList = xgwcPlatformServiceImpl.pullRefundByTradeNo(orderPayDto.getCollectionNo(), ImmutableList.of(brandId));
                    if (refundList == null) {
                        orderPayDto.setIsRefundable("缺少平台售后单");
                        continue;
                    }
                }
                if (refundList.stream().map(XgwcPlatformRefund::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(platformRefundableAmountMap.get(orderPayDto.getCollectionNo())) <= 0) {
                    orderPayDto.setIsRefundable("收款金额已全部退完");
                }
            } else {
                if (orderPayDto.getRefundableAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    orderPayDto.setIsRefundable("收款金额已全部退完");
                }
            }
        }
    }

}