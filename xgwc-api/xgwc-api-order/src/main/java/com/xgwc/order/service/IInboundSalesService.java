package com.xgwc.order.service;

import java.util.List;

import com.xgwc.order.entity.vo.InboundSalesParamVo;
import com.xgwc.order.entity.vo.InboundSalesVo;
import com.xgwc.order.entity.dto.InboundSalesDto;
import com.xgwc.order.entity.vo.InboundSalesQueryVo;
import jakarta.servlet.http.HttpServletResponse;

public interface IInboundSalesService {
    /**
     * 查询销售进线
     *
     * @param id 销售进线主键
     * @return 销售进线
     */
    InboundSalesDto selectInboundSalesById(Long id);

    /**
     * 查询销售进线列表
     *
     * @param inboundSales 销售进线
     * @return 销售进线集合
     */
    List<InboundSalesDto> selectInboundSalesList(InboundSalesQueryVo inboundSales);

    /**
     * 新增销售进线
     *
     * @param inboundSales 销售进线
     * @return 结果
     */
    int insertInboundSales(InboundSalesParamVo inboundSales);

    /**
     * 修改销售进线
     *
     * @param inboundSales 销售进线
     * @return 结果
     */
    int updateInboundSales(InboundSalesVo inboundSales);

    /**
     * 删除销售进线信息
     *
     * @param id 销售进线主键
     * @return 结果
     */
    int deleteInboundSalesById(Long id);

    /**
     * 导出销售进线数据
     * @param response 响应
     * @param inboundSales 查询条件
     */
    void export(HttpServletResponse response, InboundSalesQueryVo inboundSales);
}
