package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ReceiveVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("派单工作台Id")
    @NotNull(message = "派单工作台Id不能为空")
    private Long dispatchId;

    @FieldDesc("订单Id")
    @NotNull(message = "订单Id不能为空")
    private Long orderId;

    @FieldDesc("需派设计师数")
    @NotNull(message = "需派设计师数不能为空")
    private Integer needDispatchNumber;

    @FieldDesc("要派的设计师数量")
    @NotNull(message = "要派的设计师数量不能为空")
    private Integer dispatchNumber;

}
