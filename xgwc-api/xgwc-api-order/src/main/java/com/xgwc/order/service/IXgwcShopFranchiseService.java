package com.xgwc.order.service;

import com.xgwc.order.entity.vo.XgwcShopFranchiseVo;
import com.xgwc.order.feign.entity.*;
import com.xgwc.order.entity.vo.XgwcShopFranchiseQueryVo;

import java.util.List;

public interface IXgwcShopFranchiseService  {
    /**
     * 查询加盟商店铺
     * 
     * @param id 加盟商店铺主键
     * @return 加盟商店铺
     */
    public XgwcShopFranchiseDto selectXgwcShopFranchiseByShopId(Long id);

    /**
     * 查询加盟商店铺列表
     * 
     * @param xgwcShopFranchise 加盟商店铺
     * @return 加盟商店铺集合
     */
    public List<XgwcShopFranchiseDto> selectXgwcShopFranchiseList(XgwcShopFranchiseQueryVo xgwcShopFranchise);

    List<XgwcShopFranchiseBusinessDto> shopFranchiseBusiness(Long userId);

    public int updateBusiness(List<XgwcShopFranchiseBusinessVo> business);
}
