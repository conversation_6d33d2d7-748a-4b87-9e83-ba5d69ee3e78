package com.xgwc.order.entity;

import com.xgwc.common.util.json.JsonUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@Setter
@Getter
public class BrandNightPerformanceConfig {

    /** 主键ID */
    private Long id;

    /** 品牌商ID */
    private Long brandId;

    /** 加盟商ID */
    private Long franchiseeId;

    /** 部门ID */
    private Long departmentId;

    /** 晚间时间段配置JSON */
    private String nightTimeJson;

    /** 创建人id */
    private Long createById;

    /** 创建时间 */
    private Date createTime;

    /** 修改人id */
    private Long updateById;

    /** 更新时间 */
    private Date updateTime;


    public boolean isCrossedDay() {
        for (NightConfigRange nightConfigRange : rangeList) {
            if (nightConfigRange.isCrossedDay()) {
                return true;
            }
        }
        return false;
    }

    // 非表字段
    public List<NightConfigRange> rangeList;

    public void initRangeList() {
        this.rangeList = JsonUtils.toList(nightTimeJson, NightConfigRange.class);
    }

    public void initRangeRateJson(@Valid @NotNull @NotEmpty List<NightConfigRange> rangeList) {
        this.nightTimeJson = JsonUtils.toJsonString(rangeList);
    }

}