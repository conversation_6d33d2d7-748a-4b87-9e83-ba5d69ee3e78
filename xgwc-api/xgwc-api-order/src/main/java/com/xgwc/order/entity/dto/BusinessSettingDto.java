package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.dto
 * @Author: kouwenzhuo
 * @CreateTime: 2025-08-01  11:23
 */

@Data
public class BusinessSettingDto {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     *
     * 关联业务id
     */
    private Long relationBusinessId;

    /**
     * 业务设置名称
     */
    private String businessSettingName;

    /**
     * 相关业务信息
     */
    private List<BusinessSettingVo> businessSettingVo;

    /**
     * 是否支持品牌商派单：0支持，1不支持
     */
    private Integer status;

    @Data
    public static class BusinessSettingVo {
        /**
         *
         * 关联业务id
         */
        private Long relationBusinessId;

        /**
         * 业务设置名称
         */
        private String businessSettingName;
    }

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
