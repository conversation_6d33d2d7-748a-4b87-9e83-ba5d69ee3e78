package com.xgwc.order.entity;

import com.alibaba.fastjson2.JSONObject;
import com.xgwc.common.util.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class XgwcPlatformRefund {

    private Long id;
    /** 平台订单编号 */
    private String tradeNo;
    /** 平台退款编号 */
    private String refundNo;
    /** 退款状态 ：1取消退款,2已申请退款,3等待退货,4等待收货,5退款成功 */
    private String status;
    /** 买家申请退款金额 */
    private BigDecimal refundAmount;
    /** 实际退款 */
    private BigDecimal actualRefundAmount;

    /** 退款申请时间 */
    private Date refundTime;
    /** 退款原因 */
    private String reason;
    /** 品牌商id */
    private Long brandId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date updateTime;

    public boolean isReversal() {
        return "1".equals(this.status);
    }

    public boolean isAutoRefunded() {
        return "5".equals(this.status) && this.updateTime.getTime() >= DateUtils.addDays(this.refundTime, 2).getTime();
    }


    /*
     {
        "actual_refund_amount": "200.0",
        "goods_list": [
            {
                "goods_id": "674235965810",
                "goods_name": "代做工程预算造价广联达套定额安装水电市政土建建模装饰算量计价",
                "goods_no": "",
                "modified": "2025-07-25 17:58:54",
                "num": "2.0",
                "oid": "2844652189478212499",
                "platform_id": 1,
                "refund_no": "222966051480219924",
                "remark": "",
                "spec_id": "",
                "spec_name": "",
                "tid": "2844652189478212499",
                "total_amount": "200.0"
            }
        ],
        "is_aftersale": 0,
        "logistics_name": "",
        "logistics_no": "",
        "modified": "2025-07-25 17:58:54",
        "num": "2.0",
        "platform_id": 1,
        "reason": "其他",
        "refund_amount": "200.0",
        "refund_mask": 1,
        "refund_no": "222966051480219924",
        "refund_time": "2025-07-24 08:35:49",
        "remark": "",
        "shop_id": 2,
        "status": 5,
        "tid": "2844652189478212499",
        "type": "1"
    },
     */

    public static XgwcPlatformRefund initByJson(JSONObject jsonObject) {
        XgwcPlatformRefund refund = new XgwcPlatformRefund();
        refund.tradeNo = jsonObject.getString("tid");
        refund.refundNo = jsonObject.getString("refund_no");
        refund.status = jsonObject.getString("status"); // 平台状态：1取消退款,2已申请退款,3等待退货,4等待收货,5退款成功
        refund.refundAmount = jsonObject.getBigDecimal("refund_amount");
        refund.actualRefundAmount = jsonObject.getBigDecimal("actual_refund_amount");
        refund.refundTime = jsonObject.getDate("refund_time");
        refund.reason = jsonObject.getString("reason");
        refund.createTime = new Date();
        refund.updateTime = jsonObject.getDate("modified");
        return refund;
    }

}