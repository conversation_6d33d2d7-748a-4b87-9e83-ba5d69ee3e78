package com.xgwc.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单退款申请
 */
@Data
public class OrderRefundApply {

    /** 主键 */
    private Long id;

    /** 1.全额退款，2.部分退款，3.退垫付 */
    private Integer refundType;

    /** 退款原因code, 字典配置 */
    private String refundReasonCode;

    /** 退款原因 */
    private String refundReason;

    /**
     * 订单编号对应ID
     */
    private Long preId;

    /** 订单编号 */
    private String orderNo;

    /** 可退金额 */
    private BigDecimal preAmount;

    /** 总退款金额 */
    private BigDecimal lastAmount;

    /** 退款说明 */
    private String remark;

    /** 原佣金 */
    private BigDecimal preCommission;

    /** 现佣金 */
    private BigDecimal lastCommission;

    /** 申请人ID */
    private Long applyUserId;

    /** 申请人用户ID */
    private String applyUserName;

    /** 加盟商ID */
    private Long franchiseId;

    /** 品牌商ID */
    private Long brandId;

    /** 最后审核时间 */
    private String lastApproveTime;

    /** 审核状态：值由审批流定 */
    private String applyStatus;

    /** 平台发起退款时间(多个子单一起退时取最早的) */
    private Date platformRefundTime;

    /** 状态：0正常，1非正常 */
    private Integer status;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /** 0:审核中，1：审核通过（走工作流）2:拒绝 */
    private Integer afterCheckStatus;
}
