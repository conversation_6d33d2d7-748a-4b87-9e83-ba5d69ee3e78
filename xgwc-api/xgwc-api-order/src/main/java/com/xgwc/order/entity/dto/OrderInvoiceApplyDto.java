package com.xgwc.order.entity.dto;

import com.xgwc.order.entity.OrderInvoiceApply;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderInvoiceApplyDto extends OrderInvoiceApply {

    /**
     * 下单日期
     */
    private String orderDate;

    /**
     * 谈单人
     */
    private String saleManName;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 实收金额
     */
    private BigDecimal amount;
}
