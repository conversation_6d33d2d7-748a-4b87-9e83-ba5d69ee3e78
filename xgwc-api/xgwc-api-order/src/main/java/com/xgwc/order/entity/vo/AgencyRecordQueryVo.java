package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;


@Data
public class AgencyRecordQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("开始时间")
    private String startTime;

    @FieldDesc("结束时间")
    private String endTime;

    @FieldDesc("其它")
    private String other;

    @FieldDesc("派单员")
    private Long dispatchUserId;

    @FieldDesc("加盟商")
    private Long franchiseeId;

    @FieldDesc("业务")
    private Long storeId;

    @FieldDesc("收货情况")
    private Integer shType;

    @FieldDesc("派单状态：1 待派单 2 派单中")
    private Integer dispatchStatus;

    @FieldDesc("是否紧急派单：1 普通单 2 紧急单")
    private String dispatchType;

    private Long brandId;
}
