package com.xgwc.order.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Setter
@Getter
public class OrderRefundCommisionDto {

    /** 设计师名称 */
    private String designerUserName;

    /** 设计师业务类型 */
    private String designerBusiness;

    /** 设计师手机号 */
    private String designerPhone;

    /** 佣金 */
    private BigDecimal commisionAmount;

    /** 修改后佣金 */
    private BigDecimal afterCommisionAmount;


}