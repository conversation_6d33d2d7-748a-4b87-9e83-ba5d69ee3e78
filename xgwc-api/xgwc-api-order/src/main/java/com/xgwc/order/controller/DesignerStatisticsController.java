package com.xgwc.order.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.order.entity.dto.*;
import com.xgwc.order.entity.vo.DesignerStatisticsQueryVo;
import com.xgwc.order.entity.vo.DesignerStatisticsScoreQueryVo;
import com.xgwc.order.entity.vo.DesignerTarckVo;
import com.xgwc.order.service.IDesignerStatisticsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/designer/statistics")
public class DesignerStatisticsController extends BaseController {

    @Autowired
    private IDesignerStatisticsService designerStatisticsService;

    @MethodDesc("设计师列表")
    @PreAuthorize("@ss.hasPermission('designer:statistics:list')")
    @GetMapping("/list")
    public ApiResult<DesignerStatisticsDto> list(DesignerStatisticsQueryVo queryVo) {
        startPage();
        List<DesignerStatisticsDto> list = designerStatisticsService.list(queryVo);
        return getDataTable(list);
    }

    @MethodDesc("设计师明细")
//    @PreAuthorize("@ss.hasPermission('designer:statistics:query')")
    @GetMapping(value = "/{designerId}")
    public ApiResult<DesignerStatisticsDto> getInfo(@PathVariable("designerId") Long designerId) {
        return success(designerStatisticsService.selectById(designerId));
    }

    @MethodDesc("设计师-跟进列表")
//    @PreAuthorize("@ss.hasPermission('designer:tarck:list')")
    @GetMapping("/tarck/list")
    public ApiResult<DesignerTarckDto> list(@RequestParam("designerId") Long designerId) {
        startPage();
        List<DesignerTarckDto> list = designerStatisticsService.tarckList(designerId);
        return getDataTable(list);
    }

    @MethodDesc("设计师-新增跟进")
    @PreAuthorize("@ss.hasPermission('designer:tarck:add')")
    @PostMapping("/tarck/add")
    public ApiResult tarckAdd(@RequestBody @Valid DesignerTarckVo designerTarckVo) {
        designerStatisticsService.tarckAdd(designerTarckVo);
        return success();
    }

    @MethodDesc("设计师-未交稿")
    @PreAuthorize("@ss.hasPermission('designer:order:noarchive')")
    @GetMapping("/order/noarchive")
    public ApiResult<OrderDto> noarchive(@RequestParam("designerId") Long designerId) {
        startPage();
        List<OrderDto> list = designerStatisticsService.noArchive(designerId);
        return getDataTable(list);
    }

    @MethodDesc("设计师-退款单")
    @PreAuthorize("@ss.hasPermission('designer:order:refund')")
    @GetMapping("/order/refund")
    public ApiResult<OrderDto> refund(@RequestParam("designerId") Long designerId) {
        startPage();
        List<OrderDto> list = designerStatisticsService.refund(designerId);
        return getDataTable(list);
    }

    @MethodDesc("设计师-评价统计")
    @PreAuthorize("@ss.hasPermission('designer:score:all')")
    @GetMapping("/score/all")
    public ApiResult<DesignerStatisticsScoreAllDto> scoreAll(@Valid DesignerStatisticsScoreQueryVo queryVo) {
        DesignerStatisticsScoreAllDto dto = designerStatisticsService.scoreAll(queryVo);
        return success(dto);
    }

    @MethodDesc("设计师-评价列表")
    @PreAuthorize("@ss.hasPermission('designer:score:list')")
    @GetMapping("/score/list")
    public ApiResult<DesignerStatisticsOrderDto> scoreList(DesignerStatisticsScoreQueryVo queryVo) {
        startPage();
        List<DesignerStatisticsOrderDto> list = designerStatisticsService.scoreList(queryVo);
        return getDataTable(list);
    }
}
