package com.xgwc.order.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AfterAgencyAuditVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("申请记录ID：order_refund_apply主键")
    @NotNull(message = "申请记录ID不能为空")
    private Long applyId;

    @FieldDesc("加盟商id")
    @NotNull(message = "加盟商id不能为空")
    private Long franchiseId;

    @FieldDesc("订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

}
