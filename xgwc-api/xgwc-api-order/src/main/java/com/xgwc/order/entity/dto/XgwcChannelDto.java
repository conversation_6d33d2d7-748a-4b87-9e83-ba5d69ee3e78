package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.dto
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  09:19
 */

/**
 * 服务管理-渠道管理
 */
@Data
public class XgwcChannelDto {

    /** 渠道id */
    @FieldDesc("渠道id")
    private Long channelId;

    /** 品牌商id */
    @FieldDesc("品牌商id")
    private Long brandOwnerId;

    /** 渠道名称 */
    @NotNull("渠道名称不能为空")
    @FieldDesc("渠道名称")
    private String channelName;

    /** 排序：越小越前 */
    @FieldDesc("排序：越小越前")
    private Integer sort;

    /** 状态：0正常，1禁用 */
    @FieldDesc("状态：0正常，1禁用")
    private String status;

    /** 是否删除：0正常，1删除 */
    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    public XgwcChannelDto(Long channelId, @NotNull String channelName, Integer sort, String status, Integer isDel, String createBy, Date createTime, String updateBy, Date updateTime, Date modifyTime) {
        this.channelId = channelId;
        this.channelName = channelName;
        this.sort = sort;
        this.status = status;
        this.isDel = isDel;
        this.createBy = createBy;
        this.createTime = createTime;
        this.updateBy = updateBy;
        this.updateTime = updateTime;
        this.modifyTime = modifyTime;
    }

}
