package com.xgwc.order.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

@Data
public class IncomLineSeasDto extends IncomLineDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("领取人")
    @Excel(name = "领取人")
    private String receiver;

    @FieldDesc("领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "领取时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiveTime;

    /** 回收类型:0回收 1不回收 */
    private Integer recycleType;

    /** 回收时间 */
    private Date recycleTime;

    /** 回收次数 */
    private Integer recycleNum;
}
