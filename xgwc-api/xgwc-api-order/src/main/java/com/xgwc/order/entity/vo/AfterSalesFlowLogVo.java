package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class AfterSalesFlowLogVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("业务id")
    private Long businessId;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("节点类型（1:开始，2:售后审核）")
    private Integer nodeType;

    @FieldDesc("操作人ID")
    private Long operatorId;

    @FieldDesc("操作人姓名")
    private String operatorName;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("顺序")
    private Integer sortOrder;

    @FieldDesc("节点状态（0发起审批，1通过，2拒绝）")
    private Integer status;



}
