package com.xgwc.order.dao;

import com.xgwc.order.entity.ReportBrandNightPerformanceStats;
import com.xgwc.order.entity.dto.OrderSimpleDto;
import com.xgwc.order.entity.dto.ReportBrandNightPerformanceStatsDto;
import com.xgwc.order.entity.vo.ReportBrandNightPerformanceStatsQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


public interface ReportBrandNightPerformanceStatsMapper {

    List<OrderSimpleDto> queryOrderData(@Param(value = "brandId") Long brandId, @Param("franchiseeId") Long franchiseeId, @Param(value = "minTime") Date minTime, @Param(value = "maxTime") Date maxTime);

    int insertList(@Param(value = "statsList") List<ReportBrandNightPerformanceStats> statsList);

    int deleteByCond(@Param(value = "brandId") Long brandId, @Param("franchiseeId") Long franchiseeId, @Param(value = "minTime") Date minTime, @Param(value = "maxTime") Date maxTime);

    List<ReportBrandNightPerformanceStatsDto> listByQuery(ReportBrandNightPerformanceStatsQueryVo queryVo);

}