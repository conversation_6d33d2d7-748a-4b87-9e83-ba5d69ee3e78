package com.xgwc.order.service;

import com.xgwc.order.entity.dto.AfterAgencyAuditDto;
import com.xgwc.order.entity.dto.AfterAlreadyAuditDto;
import com.xgwc.order.entity.vo.AfterAgencyAuditQueryVo;
import com.xgwc.order.entity.vo.AfterAgencyAuditVo;
import com.xgwc.order.entity.vo.AfterAlreadyAuditQueryVo;
import com.xgwc.order.entity.vo.AfterSalesAuditVo;

import java.util.List;

public interface IAfterAgencyAuditService {
    /**
     * 查询售后审核
     *
     * @param id 售后审核主键
     * @return 售后审核
     */
    AfterAgencyAuditDto selectAfterAgencyAuditById(Long id);

    /**
     * 查询售后审核列表
     *
     * @param afterAgencyAudit 售后审核
     * @return 售后审核集合
     */
    List<AfterAgencyAuditDto> selectAfterAgencyAuditList(AfterAgencyAuditQueryVo afterAgencyAudit);

    /**
     * 查询我的已审核的售后订单
     * @param queryVo 查询条件
     * @return 已审核的售后订单集合
     */
    List<AfterAlreadyAuditDto> selectMyAfterAlreadyAuditList(AfterAlreadyAuditQueryVo queryVo);

    /**
     * 新增售后审核
     *
     * @param afterAgencyAudit 售后审核
     * @return 结果
     */
    Long insertAfterAgencyAudit(AfterAgencyAuditVo afterAgencyAudit, Long brandId, Long userId);

    /**
     * 审核售后订单
     * @param afterSalesAuditVo 售后审核
     * @return 结果
     */
    int auditAfterSalesOrder(AfterSalesAuditVo afterSalesAuditVo, Long userId, String userName, Long executionId);

}
