package com.xgwc.order.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

@Data
public class InboundSalesQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("进行日期开始时间")
    private String proceedDateStart;

    @FieldDesc("进行日期结束时间")
    private String proceedDateEnd;

    @FieldDesc("客服id")
    private Long userId;

    @FieldDesc("是否删除：0正常，1删除")
    private Long isDel;
}
