management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
spring:
  cloud:
    nacos:
      discovery: # 【配置中心】配置项
        namespace: 6cc1ba75-2b9c-4ceb-8398-fc381a8d747b
        group: DEFAULT_GROUP
  #连接池配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
    username: root
    password: ",uPkdLhlC1,t"
  data:
    redis:
      host: *************
      port: 6379
      database: 0
      password: TqbZ4tpJA153LN5Boax4
    lettuce:
      pool:
        max-idle: 20
        min-idle: 5
        max-active: 50
        adaptiveRefreshTriggersTimeout: 5
        enablePeriodicRefresh: 10
#mybatis配置
mybatis:
  type-aliases-package: com.xgwc.user.entity
  mapper-locations: classpath:mapper/*.xml
logging:
  level:
    com.xgwc.order.dao: debug
security:
  ignoreUrls:
    - /login/login
    - /user/register
    - /favcoin
    - /**
  jwt:
    key: vQvWiNN21UYmqQGXYY/jk3e6mOhNUez/hTcGIjon+9E=
    ttl: 86400000
feign:
  client:
    config:
      default:
        connectTimeout: 3000 # 连接超时时间（单位：毫秒）
        readTimeout: 3000 # 读取超时时间（单位：毫秒）