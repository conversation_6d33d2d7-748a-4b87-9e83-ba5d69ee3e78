{"settings": {"number_of_shards": 3, "number_of_replicas": 1, "analysis": {"analyzer": {"hierarchy_analyzer": {"type": "custom", "tokenizer": "comma_tokenizer", "filter": ["hierarchy_filter"]}}, "tokenizer": {"comma_tokenizer": {"type": "pattern", "pattern": ",", "group": -1}}, "filter": {"hierarchy_filter": {"type": "multiplexer", "filters": ["hierarchy_generator"], "preserve_original": false}, "hierarchy_generator": {"type": "pattern_capture", "patterns": ["(^[^_]+_[^_]+)", "(^[^_]+_[^_]+_[^_]+)", "(^[^_]+_[^_]+_[^_]+_[^_])", "(^[^_]+_[^_]+_[^_]+_[^_]+_[^_])"], "preserve_original": false}}}}, "mappings": {"properties": {"brandId": {"type": "long"}, "caseId": {"type": "long"}, "create_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}, "franchiseId": {"type": "long"}, "money": {"type": "double"}, "level": {"type": "integer"}, "title": {"type": "text", "analyzer": "ik_max_word", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "typeCodes": {"type": "text", "analyzer": "hierarchy_analyzer", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}