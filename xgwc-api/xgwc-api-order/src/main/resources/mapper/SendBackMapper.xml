<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.SendBackMapper">
    

    <sql id="selectSendBackVo">
        select create_by, create_time, dispatch_id, explanatory, id, modify_time, order_id, send_time, update_by, update_time from xgwc_send_back
    </sql>

    <select id="selectSendBackList" parameterType="com.xgwc.order.entity.vo.SendBackQueryVo" resultType="com.xgwc.order.entity.dto.SendBackDto">
        select t.id, t.create_by, t.explanatory, t.order_id, t.send_time,o.taobao_id as taobaoId
        ,f.franchise_name as franchiseName
        ,xb.business_name as brandName,o.order_no as orderNo
        from xgwc_send_back t
        left join xgwc_order o on t.order_id = o.id
        left join franchise f ON o.franchise_id = f.id
        left join xgwc_designer xd on o.designer_id = xd.designer_id
        left join xgwc_business xb  on xd.good_business = xb.business_id
        <where>
            <if test="startTime != null and endTime != null">
                AND t.send_time BETWEEN CONCAT(#{startTime}, ' 00:00:00')
                AND CONCAT(#{endTime}, ' 23:59:59')

            </if>
            <if test="franchiseeId != null "> and o.franchise_id = #{franchiseeId} </if>
            <if test="businessId != null "> and xb.business_id = #{businessId} </if>
            <if test="brandId != null "> and o.brand_id = #{brandId} </if>
            <if test="other != null  and other != ''">
                and (o.order_no like concat('%', #{other}, '%')
                         or o.taobao_id like concat('%', #{other}, '%')
                         or t.create_by like concat('%', #{other}, '%'))
            </if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectSendBackById" parameterType="Long" resultType="com.xgwc.order.entity.dto.SendBackDto">
        <include refid="selectSendBackVo"/>
        where id = #{id}
    </select>

    <select id="findSendBackListByDispatchId" resultType="com.xgwc.order.entity.dto.SendBackDto">
        select t.*
             ,xo.order_no as orderNo
             ,xo.taobao_id as taobaoId
        from xgwc_send_back t
        left join xgwc_order xo on xo.id = t.order_id
        where t.dispatch_id = #{dispatchId} and t.is_del = 0
    </select>
    <select id="findSendBackListByOrderId" resultType="com.xgwc.order.entity.dto.SendBackDto">
        select * from xgwc_send_back where order_id = #{orderId} and is_del = 0
    </select>

    <select id="findLatestRemarksByDispatchIds" resultType="com.xgwc.order.entity.dto.SendBackDto">
        select dispatch_id, explanatory from xgwc_send_back where dispatch_id in
        <foreach item="dispatchId" collection="dispatchIds" open="(" separator="," close=")">
            #{dispatchId}
        </foreach>
        order by send_time desc limit 1
    </select>

    <insert id="insertSendBack" parameterType="com.xgwc.order.entity.SendBack">
        insert into xgwc_send_back
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="dispatchId != null">dispatch_id,</if>
            <if test="explanatory != null">explanatory,</if>
            <if test="id != null">id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="orderId != null">order_id,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="dispatchId != null">#{dispatchId},</if>
            <if test="explanatory != null">#{explanatory},</if>
            <if test="id != null">#{id},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSendBack" parameterType="com.xgwc.order.entity.SendBack">
        update xgwc_send_back
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="dispatchId != null">dispatch_id = #{dispatchId},</if>
            <if test="explanatory != null">explanatory = #{explanatory},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteSendBackById" parameterType="Long">
        update xgwc_send_back set is_del = 1 where id = #{id}
    </update>

    <update id="deleteSendBackByIds" parameterType="String">
        update xgwc_send_back set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>