<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcPlatformRefundMapper">

    <sql id="selectXgwcPlatformRefundVo">
        select id, trade_no, refund_no, reason, status, refund_amount, actual_refund_amount, refund_time, brand_id, create_time, update_time from xgwc_platform_refund
    </sql>

    <select id="getByRefundNo" resultType="com.xgwc.order.entity.XgwcPlatformRefund">
        <include refid="selectXgwcPlatformRefundVo"/>
        where refund_no = #{refundNo} and brand_id = #{brandOwnerId}
    </select>

    <insert id="insertList" parameterType="com.xgwc.order.entity.XgwcPlatformRefund">
        insert into xgwc_platform_refund (trade_no,refund_no,reason,status,refund_amount,actual_refund_amount,refund_time,brand_id,create_time,update_time)
        values
        <foreach item="item" collection="xgwcPlatformRefundList" separator=",">
        (#{item.tradeNo},#{item.refundNo},#{item.reason},#{item.status},#{item.refundAmount},#{item.actualRefundAmount},#{item.refundTime},#{item.brandId},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <select id="selectNotUseList" parameterType="com.xgwc.order.entity.vo.XgwcPlatformRefundQueryVo" resultType="com.xgwc.order.entity.dto.XgwcPlatformRefundDto">
        select xpr.id, xpr.trade_no, xpr.refund_no, xpr.reason, xpr.status, xpr.refund_amount, xpt.pay_amount, xpr.actual_refund_amount, xpr.refund_time, xpt.pay_time, xpt.platform_id, xpt.platform_shop_id
        from xgwc_platform_refund xpr
        left join xgwc_platform_trade xpt on xpr.trade_no = xpt.trade_no and xpr.brand_id = xpt.brand_id
        where  xpr.brand_id = #{brandId}
            <if test="keyword != null  and keyword != ''">
                and xpr.trade_no like concat('%', #{keyword}, '%')
            </if>
            <if test="refundNo != null  and refundNo != ''"> and xpr.refund_no like concat('%', #{refundNo}, '%')</if>
            <if test="platformId != null  and platformId != ''"> and xpt.platform_id = #{platformId}</if>
            <if test="platformShopId != null  and platformShopId != ''"> and xpt.platform_shop_id = #{platformShopId}</if>
            <if test="status != null  and status != ''"> and xpr.status = #{status}</if>
            <if test="refundTimeStart != null and refundTimeEnd != null">
                and xpr.refund_time between #{refundTimeStart} and #{refundTimeEnd}
            </if>
            <if test="payTimeStart != null and payTimeEnd != null">
                and xpt.pay_time between #{payTimeStart} and #{payTimeEnd}
            </if>
            AND NOT EXISTS (SELECT 1 FROM order_refund_pay orp WHERE orp.refund_amount > 0 and xpt.trade_no = orp.order_no)
    </select>
    <select id="listByTradeNo" resultType="com.xgwc.order.entity.XgwcPlatformRefund">
        <include refid="selectXgwcPlatformRefundVo"/>
        where brand_id = #{brandOwnerId} and trade_no in
        <foreach item="tradeNo" collection="tradeNoList" open="(" separator="," close=")">
            #{tradeNo}
        </foreach>
    </select>

    <select id="listByRefundNo" resultType="com.xgwc.order.entity.XgwcPlatformRefund">
        <include refid="selectXgwcPlatformRefundVo"/>
        where brand_id = #{brandOwnerId} and refund_no in
        <foreach item="refundNo" collection="refundNoList" open="(" separator="," close=")">
            #{refundNo}
        </foreach>
    </select>

    <delete id="deleteByRefundNo">
        delete
        from xgwc_platform_refund
        where brand_id = #{brandOwnerId} and refund_no in
        <foreach item="refundNo" collection="refundNoList" open="(" separator="," close=")">
            #{refundNo}
        </foreach>
    </delete>

</mapper>