<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.ReportBrandNightPerformanceStatsMapper">

    <select id="queryOrderData" resultType="com.xgwc.order.entity.dto.OrderSimpleDto">
        select xo. from xgwc_order xo
        order by id asc
    </select>


    <insert id="insertList" parameterType="com.xgwc.order.entity.ReportBrandNightPerformanceStats">
        insert into report_brand_night_performance_stats
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="statDimension != null">stat_dimension,</if>
            <if test="statStartTime != null">stat_start_time,</if>
            <if test="statEndTime != null">stat_end_time,</if>
            <if test="franchiseeId != null">franchisee_id,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="orderCount != null">order_count,</if>
            <if test="orderAmount != null">order_amount,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="otherBizJson != null">other_biz_json,</if>
            <if test="updatedTime != null">updated_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="statDimension != null">#{statDimension},</if>
            <if test="statStartTime != null">#{statStartTime},</if>
            <if test="statEndTime != null">#{statEndTime},</if>
            <if test="franchiseeId != null">#{franchiseeId},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="orderCount != null">#{orderCount},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="otherBizJson != null">#{otherBizJson},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
         </trim>
    </insert>

    <delete id="deleteByCond">
        delete from report_brand_night_performance_stats where brand_id = #{brandId} and franchisee_id = #{franchiseeId} and stat_start_time >= #{minTime} and #{maxTime} >= stat_end_time
    </delete>

    <sql id="selectReportBrandNightPerformanceStatsVo">
        select id, brand_id, stat_dimension, stat_start_time, stat_end_time, franchisee_id, department_id, order_count, order_amount, actual_amount, other_biz_json, updated_time from report_brand_night_performance_stats
    </sql>

    <select id="listByQuery" parameterType="com.xgwc.order.entity.vo.ReportBrandNightPerformanceStatsQueryVo" resultType="com.xgwc.order.entity.dto.ReportBrandNightPerformanceStatsDto">
        <include refid="selectReportBrandNightPerformanceStatsVo"/>
        <where>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="statDimension != null "> and stat_dimension = #{statDimension}</if>
            <if test="statStartTimeStart != null and statStartTimeEnd != null">
                and stat_start_time between #{statStartTimeStart} and #{statStartTimeEnd}
            </if>
            <if test="statEndTimeStart != null and statEndTimeEnd != null">
                and stat_end_time between #{statEndTimeStart} and #{statEndTimeEnd}
            </if>
            <if test="franchiseeId != null "> and franchisee_id = #{franchiseeId}</if>
            <if test="departmentId != null "> and department_id = #{departmentId}</if>
            <if test="orderCount != null "> and order_count = #{orderCount}</if>
            <if test="orderAmount != null "> and order_amount = #{orderAmount}</if>
            <if test="actualAmount != null "> and actual_amount = #{actualAmount}</if>
            <if test="otherBizJson != null  and otherBizJson != ''"> and other_biz_json like concat('%', #{otherBizJson}, '%')</if>
            <if test="updatedTimeStart != null and updatedTimeEnd != null">
                and updated_time between #{updatedTimeStart} and #{updatedTimeEnd}
            </if>
        </where>
    </select>

</mapper>