<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcBrandMapper">

    <insert id="saveXgwcBrand">
        INSERT INTO `xgwc_sass`.`xgwc_brand` (`brand_id`,  `brand_owner_id`, `brand_name`, `sort`, `create_by`,`create_time`)
        VALUES (#{xgwcBrandDto.brandId},  #{xgwcBrandDto.brandOwnerId} ,#{xgwcBrandDto.brandName}, #{xgwcBrandDto.sort}, #{xgwcBrandDto.createBy}, now());
    </insert>

    <update id="updateXgwcBrandById">
        UPDATE `xgwc_sass`.`xgwc_brand`
        <set>
            <if test="xgwcBrandDto.brandName != null and xgwcBrandDto.brandName != ''">
                `brand_name` = #{xgwcBrandDto.brandName}
            </if>
            <if test="xgwcBrandDto.sort != null">
                , `sort` = #{xgwcBrandDto.sort}
            </if>
            <if test="xgwcBrandDto.updateBy != null and xgwcBrandDto.updateBy != ''">
                , `update_by` = #{xgwcBrandDto.updateBy}
            </if>
            ,`update_time` = NOW()
        </set>
        WHERE brand_id = #{xgwcBrandDto.brandId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`xgwc_brand`
        SET `status` = #{status}
        WHERE brand_id = #{brandId}
    </update>


    <select id="getXgwcBrandList" resultType="com.xgwc.order.entity.dto.XgwcBrandDto">
        select
        brand_id as brandId,
        brand_name as brandName,
        sort,
        status,
        create_time as createTime
        from xgwc_brand
        <where>
            brand_owner_id = #{xgwcBrandParam.brandOwnerId}

            <if test="xgwcBrandParam.brandName != null and xgwcBrandParam.brandName != ''">
                AND brand_name LIKE CONCAT('%', #{xgwcBrandParam.brandName}, '%')
            </if>
            <if test="xgwcBrandParam.status != null">
                AND status = #{xgwcBrandParam.status}
            </if>
            <if test="brandIds != null and brandIds.size() > 0">
                AND brand_id IN
                <foreach item="id" collection="brandIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by sort
    </select>

    <select id="getXgwcBrandById" resultType="com.xgwc.order.entity.dto.XgwcBrandDto">
        select brand_id    as brandId,
               brand_name  as brandName,
               sort,
               status,
               create_time as createTime
        from xgwc_brand
        where brand_id = #{brandId}
    </select>
</mapper>