<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.AgencyDispatchMapper">
    

    <sql id="selectDispatchRecordVo">
        select create_by, create_time, dispatch_id, dispatch_number, dispatch_remark, dispatch_status, dispatch_time, dispatch_user_id, id, modify_time, need_dispatch_number, order_id, update_by, update_time,assigned_quantity from xgwc_agency_dispatch
    </sql>

    <select id="findMyAgencyDispatchList" parameterType="com.xgwc.order.entity.vo.AgencyDispatchQueryVo" resultType="com.xgwc.order.entity.dto.AgencyDispatchPageDto">
        select
            t.id,
            t.dispatch_id,
            t.dispatch_number,
            t.dispatch_remark,
            t.dispatch_status,
            t.dispatch_user_id,
            t.need_dispatch_number,
            t.order_id,
            t.assigned_quantity,
            TIMESTAMPDIFF(MINUTE, o.create_time, now()) minuteTime,
            o.order_no AS orderNo,
            o.taobao_id AS taobaoId,
            o.sale_man_name AS saleManName,
            o.state_dic_code AS stateDicCode,
            o.state_dic_name AS stateDicName,
            o.order_amount AS orderAmount,
            o.store_name AS storeName,
            o.allot_remark AS allotRemark,
            d.dispatch_type AS dispatchType,
            o.archive_expect_time AS archiveExpectTime,
            f.franchise_name AS companyName,
            d.return_number as returnNumber
        from xgwc_agency_dispatch t
        LEFT JOIN xgwc_order o ON t.order_id = o.id
        LEFT JOIN franchise f ON o.franchise_id = f.id
        left join xgwc_dispatch d on t.dispatch_id = d.id
        where t.dispatch_number > t.assigned_quantity
        and t.dispatch_user_id = #{dispatchUserId}
        <if test="other != null  and other != ''">
            and (o.order_no like concat('%', #{other}, '%') or o.taobao_id like concat('%', #{other}, '%') or o.sale_man_name like concat('%', #{other}, '%'))
        </if>
        <if test="franchiseeId != null "> and o.franchise_id = #{franchiseeId} </if>
        <if test="stateDicCode != null "> and o.state_dic_code = #{stateDicCode} </if>
        <if test="startTime != null and endTime != null">
            AND o.order_date BETWEEN CONCAT(#{startTime}, ' 00:00:00')
            AND CONCAT(#{endTime}, ' 23:59:59')
        </if>
        order by t.create_time desc
    </select>
    
    <select id="selectAgencyDispatchById" parameterType="Long" resultType="com.xgwc.order.entity.dto.AgencyDispatchDto">
        <include refid="selectDispatchRecordVo"/>
        where id = #{id}
    </select>

    <select id="countTodayReceive" resultType="java.lang.Integer">
        select count(*) from xgwc_agency_dispatch where dispatch_user_id = #{userId} and DATE(create_time) = CURDATE()
    </select>

    <select id="countTodayDispatch" resultType="java.lang.Integer">
        select count(*) from xgwc_already_dispatch where dispatch_user_id = #{userId} and DATE(create_time) = CURDATE()
    </select>

    <select id="selectAgencyDispatchByOrderId" resultType="com.xgwc.order.entity.dto.AgencyDispatchDto">
        <include refid="selectDispatchRecordVo"/>
        where order_id = #{orderId}
    </select>

    <select id="findAgencyRecordList" resultType="com.xgwc.order.entity.dto.AgencyRecordPageDto">
        select
            t.id,
            t.dispatch_id,
            t.dispatch_status,
            t.order_id,
            d.dispatch_type AS dispatchType,
            o.order_no AS orderNo,
            o.order_date AS orderDate,
            o.archive_appoint_time AS archiveAppointTime,
            o.taobao_id AS taobaoId,
            o.sale_man_name AS saleManName,
            o.order_amount AS orderAmount,
            o.store_name AS storeName,
            o.allot_remark AS allotRemark,
            o.money as money,
            o.designer_name as designerName,
            o.allot_user_name as allotUserName,
            o.allot_file as allotFile,
            o.sh_type as shType,
            o.pid as pid,
            f.franchise_name AS companyName
        from xgwc_agency_dispatch t
        left join xgwc_dispatch d on t.dispatch_id = d.id
        LEFT JOIN xgwc_order o ON t.order_id = o.id
        LEFT JOIN franchise f ON o.franchise_id = f.id
        where t.dispatch_number > t.assigned_quantity
        <if test="brandId != null">and t.brand_id = #{brandId}</if>
        <if test="dispatchStatus != null"> and t.dispatch_status = #{dispatchStatus} </if>
        <if test="dispatchType != null"> and d.dispatch_type = #{dispatchType} </if>
        <if test="other != null  and other != ''">
            and (o.order_no like concat('%', #{other}, '%') or o.taobao_id like concat('%', #{other}, '%') or o.sale_man_name like concat('%', #{other}, '%'))
        </if>
        <if test="shType != null "> and o.sh_type = #{shType} </if>
        <if test="franchiseeId != null "> and o.franchise_id = #{franchiseeId} </if>
        <if test="storeId != null "> and o.store_id = #{storeId} </if>
        <if test="startTime != null and endTime != null "> AND o.order_date BETWEEN CONCAT(#{startTime}, ' 00:00:00') AND CONCAT(#{endTime}, ' 23:59:59')  </if>
        order by t.create_time desc
    </select>

    <select id="countEmergency" resultType="java.lang.Integer">
        select
            IFNULL(SUM(d.dispatch_type = 2), 0) AS dispatchTypeCount
        from xgwc_agency_dispatch t
                 left join xgwc_dispatch d on t.dispatch_id = d.id
                 LEFT JOIN xgwc_order o ON t.order_id = o.id
                 LEFT JOIN franchise_owner fo ON o.franchise_id = fo.franchise_id
        where t.dispatch_number > t.assigned_quantity
        and t.brand_id = #{brandId}
    </select>

    <insert id="insertAgencyDispatch" parameterType="com.xgwc.order.entity.AgencyDispatch">
        insert into xgwc_agency_dispatch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="dispatchId != null">dispatch_id,</if>
            <if test="dispatchNumber != null">dispatch_number,</if>
            <if test="dispatchRemark != null">dispatch_remark,</if>
            <if test="dispatchStatus != null">dispatch_status,</if>
            <if test="dispatchTime != null">dispatch_time,</if>
            <if test="dispatchUserId != null">dispatch_user_id,</if>
            <if test="id != null">id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="needDispatchNumber != null">need_dispatch_number,</if>
            <if test="orderId != null">order_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="brandId != null">brand_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="dispatchId != null">#{dispatchId},</if>
            <if test="dispatchNumber != null">#{dispatchNumber},</if>
            <if test="dispatchRemark != null">#{dispatchRemark},</if>
            <if test="dispatchStatus != null">#{dispatchStatus},</if>
            <if test="dispatchTime != null">#{dispatchTime},</if>
            <if test="dispatchUserId != null">#{dispatchUserId},</if>
            <if test="id != null">#{id},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="needDispatchNumber != null">#{needDispatchNumber},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="brandId != null">#{brandId},</if>
         </trim>
    </insert>

    <update id="updateAgencyDispatch" parameterType="com.xgwc.order.entity.AgencyDispatch">
        update xgwc_agency_dispatch
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="dispatchId != null">dispatch_id = #{dispatchId},</if>
            <if test="dispatchNumber != null">dispatch_number = #{dispatchNumber},</if>
            <if test="dispatchRemark != null">dispatch_remark = #{dispatchRemark},</if>
            <if test="dispatchStatus != null">dispatch_status = #{dispatchStatus},</if>
            <if test="dispatchTime != null">dispatch_time = #{dispatchTime},</if>
            <if test="dispatchUserId != null">dispatch_user_id = #{dispatchUserId},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="needDispatchNumber != null">need_dispatch_number = #{needDispatchNumber},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="assignedQuantity != null">assigned_quantity = #{assignedQuantity},</if>
        </trim>
        where id = #{id}
    </update>

<!--    <update id="deleteDispatchRecordById" parameterType="Long">-->
<!--        update xgwc_agency_dispatch set is_del = 1 where id = #{id}-->
<!--    </update>-->

    <delete id="deleteAgencyDispatchById" parameterType="Long">
        delete from xgwc_agency_dispatch where id = #{id}
    </delete>

    <update id="deleteAgencyDispatchByIds" parameterType="String">
        update xgwc_agency_dispatch set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateDesignateNumberById">
        update xgwc_agency_dispatch set assigned_quantity = #{assignedQuantity}, dispatch_remark = #{dispatchRemark} where id = #{id}
    </update>

    <update id="updateDispatchStatusById">
        update xgwc_agency_dispatch set dispatch_status = #{dispatchStatus} where id = #{id}
    </update>
</mapper>