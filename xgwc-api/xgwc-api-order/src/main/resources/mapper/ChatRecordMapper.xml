<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.ChatRecordMapper">
    

    <sql id="selectChatRecordVo">
        select brand_id, chat_content, classify, create_by, create_time, franchise_id, view_count, id, is_del, modify_time, status, title, update_by, update_time,source_id from xgwc_chat_record
    </sql>

    <select id="selectChatRecordList" parameterType="com.xgwc.order.entity.vo.ChatRecordQueryVo" resultType="com.xgwc.order.entity.dto.ChatRecordDto">
        select t.brand_id, t.chat_content, t.classify,t.create_time, t.franchise_id, t.id,
               t.status, t.title, t.source_id,bo.company_name as brandName
        from xgwc_chat_record t
        left join xgwc_brand_owner bo on bo.brand_id = t.brand_id
        <where>
            <if test="brandId != null "> and t.brand_id = #{brandId}</if>
            <if test="classify != null  and classify != ''"> and t.classify = #{classify}</if>
            <if test="startTime != null and endTime != null ">
                AND t.create_time BETWEEN CONCAT(#{startTime}, ' 00:00:00') AND CONCAT(#{endTime}, ' 23:59:59')
            </if>
            <if test="franchiseId != null "> and t.franchise_id = #{franchiseId}</if>
            <if test="isDel == null"> and t.is_del = 0</if>
            <if test="isDel != null "> and t.is_del = #{isDel}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="title != null  and title != ''"> and t.title like concat('%', #{title}, '%')</if>
        </where>
    </select>
    
    <select id="selectChatRecordById" parameterType="Long" resultType="com.xgwc.order.entity.dto.ChatRecordDto">
        select t.brand_id, t.chat_content, t.classify,t.create_time, t.franchise_id, t.id,
               t.status, t.title, t.source_id,bo.company_name as brandName, fo.franchise_name as franchiseName,
               t.view_count as viewCount, b.business_name as businessName
        from xgwc_chat_record t
        left join xgwc_brand_owner bo on bo.brand_id = t.brand_id
        left join franchise fo on fo.id = t.franchise_id
        left join xgwc_business b on b.business_id = t.classify
        where t.id = #{id}
    </select>
    <select id="selectFranchiseNamesByIds" resultType="com.xgwc.order.entity.dto.ChatRecordDto">
        select
            id as franchiseId,
            franchise_name as franchiseName
        from franchise where id in
        <foreach item='id' collection='franchiseId' open='(' separator=',' close=')'>
            #{id}
        </foreach>
    </select>
    <select id="selectBrandNamesByIds" resultType="com.xgwc.order.entity.dto.ChatRecordDto">
        select brand_id as brandId,
               company_name as brandName
        from xgwc_brand_owner where brand_id in
        <foreach item='id' collection='brandId' open='(' separator=',' close=')'>
           #{id}
        </foreach>
    </select>
    <select id="selectBusinessNamesByIds" resultType="com.xgwc.order.entity.dto.ChatRecordDto">
        SELECT
        `level` as classify,
        `business_name` AS businessName
        FROM xgwc_business
        WHERE `level` IN
        <foreach item="level" collection="level" open="(" separator="," close=")">
            #{level}
        </foreach>
    </select>

    <insert id="insertChatRecord" parameterType="com.xgwc.order.entity.ChatRecord" useGeneratedKeys="true" keyProperty="id">
        insert into xgwc_chat_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brandId != null">brand_id,</if>
            <if test="chatContent != null">chat_content,</if>
            <if test="classify != null">classify,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="id != null">id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="status != null">status,</if>
            <if test="title != null">title,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sourceId != null">source_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brandId != null">#{brandId},</if>
            <if test="chatContent != null">#{chatContent},</if>
            <if test="classify != null">#{classify},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="id != null">#{id},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="status != null">#{status},</if>
            <if test="title != null">#{title},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sourceId != null">#{sourceId},</if>
         </trim>
    </insert>

    <update id="updateChatRecord" parameterType="com.xgwc.order.entity.ChatRecord">
        update xgwc_chat_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="chatContent != null">chat_content = #{chatContent},</if>
            <if test="classify != null">classify = #{classify},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="title != null">title = #{title},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteChatRecordById" parameterType="Long">
        update xgwc_chat_record set is_del = 1 where id = #{id}
    </update>

    <update id="deleteChatRecordByIds" parameterType="String">
        update xgwc_chat_record set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateChatRecordViewCount">
        update xgwc_chat_record set view_count = #{viewCount}  where id = #{id}
    </update>
</mapper>