<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcCaseMapper">
    

    <sql id="selectXgwcCaseVo">
        SELECT
            xc.amount,
            xc.archive_files,
            xc.archive_img,
            xc.brand_id,
            xc.business_id,
            xc.business_name,
            xc.case_id,
            xc.case_level,
            xc.create_by,
            xc.create_time,
            xc.designer_id,
            xc.designer_name,
            xc.franchise_id,
            xc.keyword,
            xc.link_url,
            xc.money,
            xc.oder_id,
            xc.title,
            xc.update_by,
            xc.update_time,
            xc.status,
            xc.archive_id
        FROM
            xgwc_case xc
    </sql>

    <insert id="insertXgwcCase" parameterType="com.xgwc.order.entity.XgwcCase" useGeneratedKeys="true" keyProperty="caseId">
        insert into xgwc_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="amount != null">amount,</if>
            <if test="archiveId != null">archive_id,</if>
            <if test="archiveFiles != null">archive_files,</if>
            <if test="archiveImg != null">archive_img,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="businessId != null">business_id,</if>
            <if test="businessName != null">business_name,</if>
            <if test="caseId != null">case_id,</if>
            <if test="caseLevel != null">case_level,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="designerId != null">designer_id,</if>
            <if test="designerName != null">designer_name,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="keyword != null">keyword,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="money != null">money,</if>
            <if test="oderId != null">oder_id,</if>
            <if test="title != null">title,</if>
            <if test="downloadCount != null">download_count,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="amount != null">#{amount},</if>
            <if test="archiveId != null">#{archiveId},</if>
            <if test="archiveFiles != null">#{archiveFiles},</if>
            <if test="archiveImg != null">#{archiveImg},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="businessName != null">#{businessName},</if>
            <if test="caseId != null">#{caseId},</if>
            <if test="caseLevel != null">#{caseLevel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="designerId != null">#{designerId},</if>
            <if test="designerName != null">#{designerName},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="keyword != null">#{keyword},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="money != null">#{money},</if>
            <if test="oderId != null">#{oderId},</if>
            <if test="title != null">#{title},</if>
            <if test="downloadCount != null">#{downloadCount},</if>
         </trim>
    </insert>

    <update id="updateXgwcCase" parameterType="com.xgwc.order.entity.XgwcCase">
        update xgwc_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="amount != null">amount = #{amount},</if>
            <if test="archiveFiles != null">archive_files = #{archiveFiles},</if>
            <if test="archiveImg != null">archive_img = #{archiveImg},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="businessName != null">business_name = #{businessName},</if>
            <if test="caseLevel != null">case_level = #{caseLevel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="designerId != null">designer_id = #{designerId},</if>
            <if test="designerName != null">designer_name = #{designerName},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
            <if test="money != null">money = #{money},</if>
            <if test="oderId != null">oder_id = #{oderId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
        </trim>
        where case_id = #{caseId}
    </update>

    <update id="upStatus" parameterType="com.xgwc.order.entity.vo.XgwcCaseUpStatusVo">
        update xgwc_case
        set status = #{status}
        where case_id = #{caseId}
    </update>

    <select id="checkXgwcCaseByArchiveId" resultType="com.xgwc.order.entity.XgwcCase">
        <include refid="selectXgwcCaseVo"/>
        where archive_id = #{archiveId}
        limit 1
    </select>

    <select id="selectXgwcCaseByCaseId" resultType="com.xgwc.order.entity.dto.XgwcCaseDto">
        select xc.*,xbo.company_name as brandName, f.franchise_name as franchiseName,xb.business_name as designerBusinessName
        from xgwc_case xc
        left join xgwc_brand_owner xbo on xc.brand_id = xbo.brand_id
        left join franchise f on f.id = xc.franchise_id
        left join xgwc_designer xd on xd.designer_id = xc.designer_id
        left join xgwc_business xb on xb.business_id = xd.good_business
        where xc.case_id = #{caseId}
    </select>
    <select id="selectXgwcCaseList" parameterType="com.xgwc.order.entity.vo.XgwcCaseQueryVo" resultType="com.xgwc.order.entity.dto.XgwcCaseDto">
        SELECT
            xc.amount,
            xc.archive_files,
            xc.archive_img,
            xc.brand_id,
            xc.business_id,
            xc.business_name,
            xc.case_id,
            xc.case_level,
            xc.create_by,
            xc.create_time,
            xc.designer_id,
            xc.designer_name,
            xc.franchise_id,
            xc.keyword,
            xc.link_url,
            xc.money,
            xc.oder_id,
            xc.title,
            xc.update_by,
            xc.update_time,
            xc.archive_id,
            xc.status,
            xc.download_count,
            f.franchise_name,
            bo.company_name brand_name,
            xb.business_name as businessName
        FROM
        xgwc_case xc
        left join franchise f on xc.franchise_id = f.id
        left join xgwc_brand_owner bo on xc.brand_id = bo.brand_id
        left join xgwc_designer xd on xc.designer_id = xd.designer_id
        left join xgwc_business xb  on xd.good_business = xb.business_id
        <where>
            xc.is_del = 0
            <if test="value != null and value != ''">
                and (
                xc.case_id = #{value}
                or xc.title like concat('%', #{value}, '%')
                or xc.designer_name like concat('%', #{value}, '%')
                )
            </if>
            <if test="brandId != null ">
                and xc.brand_id = #{brandId}
            </if>
            <if test="businessId != null" >
                and xc.business_id = #{businessId}
            </if>
            <if test="franchiseId != null" >
                and xc.franchise_id = #{franchiseId}
            </if>
            <if test="caseLevel != null" >
                and xc.case_level = #{caseLevel}
            </if>
            <if test="status != null" >
                and xc.status = #{status}
            </if>
            <if test="caseIds != null and caseIds.size() > 0">
                and xc.case_id in
                <foreach item="caseId" collection="caseIds" open="(" separator="," close=")">
                    #{caseId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectAdmin" resultType="java.lang.Boolean">
        select count(1) > 0
        from sys_user_middle sum1
        where sum1.id = #{userId} and sum1.status = 0 and sum1.user_type = 1
    </select>
    <select id="selectFranchiseStaffDownloadCount" resultType="com.xgwc.order.entity.dto.StaffDto">
        SELECT
            id,
            download_count as downloadCount
        FROM xgwc_franchise_staff_dl_count
        where staff_id = #{staffId} and brand_id = #{brandId}
    </select>


    <update id="deleteByIds">
        update xgwc_case
        set is_del = 1
        where case_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateCaseDownloadCount">
        update xgwc_case
        set download_count = #{downloadCounts}
        where case_id = #{caseId}
    </update>
    <update id="updateBrandStaffDownloadCount">
        update xgwc_staff
        set download_count = #{staffVo.downloadCount}
        where id = #{staffVo.id}
    </update>
    <update id="updateFranchiseStaffDownloadCount">
      INSERT INTO xgwc_franchise_staff_dl_count (
          staff_id, brand_id, download_count, create_time, create_by, update_time, update_by
      )
      VALUES (
                 #{staffVo.id}, #{staffVo.brandId}, #{staffVo.downloadCount},
                 NOW(), #{staffVo.createBy}, NOW(), #{staffVo.updateBy}
             )
          ON DUPLICATE KEY UPDATE
                               download_count = VALUES(download_count),
                               update_time = NOW(),
                               update_by = VALUES(update_by)
    </update>

</mapper>