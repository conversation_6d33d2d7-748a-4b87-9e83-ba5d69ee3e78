<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.BillOrderModifyMapper">

    <insert id="batchInsert" parameterType="java.util.List" keyColumn="id" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO bill_order_modify (
        order_id, sale_man_id, sale_man_name, apply_id, dept_id,
        dept_name, transfer_state, order_date, store_id, store_name,
        brand_id, pay_channel, sh_type, order_no, taobao_id,
        order_amount, amount, now_amount, company_info_id, pay_type,
        allot_remark, allot_num, allot_user_id, allot_user_name, pid,
        designer_id, designer_name, designer_business, money, now_money,
        commission_backed, commission_back_remaining, back_status,
        create_time, franchise_id, update_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderId}, #{item.saleManId}, #{item.saleManName}, #{item.applyId}, #{item.deptId},
            #{item.deptName}, #{item.transferState}, #{item.orderDate}, #{item.storeId}, #{item.storeName},
            #{item.brandId}, #{item.payChannel}, #{item.shType}, #{item.orderNo}, #{item.taobaoId},
            #{item.orderAmount}, #{item.amount}, #{item.nowAmount}, #{item.companyInfoId}, #{item.payType},
            #{item.allotRemark}, #{item.allotNum}, #{item.allotUserId}, #{item.allotUserName}, #{item.pid},
            #{item.designerId}, #{item.designerName}, #{item.designerBusiness}, #{item.money}, #{item.nowMoney},
            #{item.commissionBacked}, #{item.commissionBackRemaining}, #{item.backStatus},
            #{item.createTime}, #{item.franchiseId}, now()
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        sale_man_id = VALUES(sale_man_id),
        sale_man_name = VALUES(sale_man_name),
        dept_id = VALUES(dept_id),
        dept_name = VALUES(dept_name),
        transfer_state = VALUES(transfer_state),
        order_date = VALUES(order_date),
        store_id = VALUES(store_id),
        store_name = VALUES(store_name),
        brand_id = VALUES(brand_id),
        pay_channel = VALUES(pay_channel),
        sh_type = VALUES(sh_type),
        order_no = VALUES(order_no),
        taobao_id = VALUES(taobao_id),
        order_amount = VALUES(order_amount),
        amount = VALUES(amount),
        now_amount = VALUES(now_amount),
        company_info_id = VALUES(company_info_id),
        pay_type = VALUES(pay_type),
        allot_remark = VALUES(allot_remark),
        allot_num = VALUES(allot_num),
        allot_user_id = VALUES(allot_user_id),
        allot_user_name = VALUES(allot_user_name),
        pid = VALUES(pid),
        designer_id = VALUES(designer_id),
        designer_name = VALUES(designer_name),
        designer_business = VALUES(designer_business),
        money = VALUES(money),
        now_money = VALUES(now_money),
        commission_backed = VALUES(commission_backed),
        commission_back_remaining = VALUES(commission_back_remaining),
        back_status = VALUES(back_status),
        create_time = VALUES(create_time),
        franchise_id = VALUES(franchise_id),
        update_time = now()
    </insert>

</mapper>