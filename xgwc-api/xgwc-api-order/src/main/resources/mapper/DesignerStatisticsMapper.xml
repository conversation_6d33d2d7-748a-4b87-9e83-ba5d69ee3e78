<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.DesignerStatisticsMapper">


    <select id="selectDesignerStatistics" parameterType="com.xgwc.order.entity.vo.DesignerStatisticsQueryVo" resultType="com.xgwc.order.entity.dto.DesignerStatisticsDto">
        select
            ds.designer_id,
            ds.brand_id,
            ds.designer_name,
            ds.designer_level,
            ds.designer_wechat,
            ds.designer_phone,
            ds.emergency_name,
            ds.emergency_phone,
            ds.details,
            ds.order_last_time,
            ds.business_id,
            ds.business_name,
            ds.his_order_business,
            ROUND(ds.avg_money,2) avg_money,
            ds.order_num,
            ds.no_archive,
            ds.order_year_num,
            ROUND(ds.good_num/ds.year_archive_num*100,2) good_num,
            ROUND(ds.refund_num/ds.year_archive_num*100,2) refund_num
        from xgwc_designer_statis ds
        left join (
        select
            brand_id,
            designer_id
        <if test="field != null and field == 'positiveRate'">
            ,sum(good_num)/sum(score_num) score_total
        </if>
        <if test="field != null and field != 'positiveRate'">
            ,sum(score_total) score_total
        </if>
        FROM
        xgwc_designer_statis_score
        <where>
            <if test="field != null and field == 'positiveRate'">
                and score_model = 'evaluation'
            </if>
            <if test="field != null and field != 'positiveRate'">
                and score_model = #{field}
            </if>
        </where>
        group by brand_id,designer_id
        ) dss3 on ds.brand_id = dss3.brand_id and ds.designer_id = dss3.designer_id
        <where>
            <if test="businessId != null and businessId.length > 0"> and ds.business_id in
                <foreach item="id" collection="businessId" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="brandId != null" >
                and ds.brand_id = #{brandId}
            </if>
            <if test="designerLevel != null and designerLevel != ''"> and ds.designer_level = #{designerLevel}</if>
            <if test="avgMoney1 != null">
                and ds.avg_money >= #{avgMoney1, jdbcType=DECIMAL}
            </if>
            <if test="avgMoney2 != null">
                and ds.avg_money &lt;= #{avgMoney2, jdbcType=DECIMAL}
            </if>
            <if test="lastAcceptTime != null and lastAcceptTime != ''">
                <if test="lastAcceptTime == 'never_order'" >
                    and ifnull(ds.order_last_time, 0) = 0
                </if>
                <if test="lastAcceptTime != 'never_order'" >
                    and ds.order_last_time > #{orderLastTime}
                </if>
            </if>
        </where>
        <if test="field != null and field == 'refundRate'">
            order by ds.refund_num
        </if>
        <if test="field != null and field != 'refundRate'">
            order by dss3.score_total
        </if>
        <if test="direction != null and direction == 'asc'"> asc </if>
        <if test="direction != null and direction == 'desc'"> desc </if>
    </select>
    <select id="selectDesignerStatisticsById" resultType="com.xgwc.order.entity.dto.DesignerStatisticsDto">
        select
            ds.designer_id,
            ds.brand_id,
            ds.designer_name,
            ds.designer_level,
            ds.designer_wechat,
            ds.designer_phone,
            ds.emergency_name,
            ds.emergency_phone,
            ds.details,
            ds.order_last_time,
            ds.business_id,
            ds.business_name,
            ds.his_order_business,
            ds.avg_money,
            ds.order_num,
            ds.no_archive,
            ds.order_year_num,
            ROUND(ds.good_num/ds.year_archive_num*100,2) good_num,
            ROUND(ds.refund_num/ds.year_archive_num*100,2) refund_num,
            d.description
        from xgwc_designer_statis ds
        left join xgwc_designer d on ds.designer_id = d.designer_id and ds.brand_id = d.brand_id
        where ds.designer_id =#{designerId}
    </select>
    <select id="selectDesignerStatisticsScore"
            resultType="com.xgwc.order.entity.dto.DesignerStatisticsScoreDto">
        select
            brand_id,
            designer_id,
            score_model,
            score_total,
            score_num,
            good_num,
            general_num,
            poor_num,
            avg_total
        from xgwc_designer_statis_score
        where designer_id =#{designerId}
        and brand_id = #{brandId}
    </select>
    <select id="selectDesignerStatisticsYearScore"
            resultType="com.xgwc.order.entity.dto.DesignerStatisticsScoreDto">
        select
            brand_id,
            designer_id,
            score_model,
            score_total,
            score_num,
            good_num,
            general_num,
            poor_num,
            avg_total
        from xgwc_designer_statis_score2
        where designer_id =#{designerId}
          and brand_id = #{brandId}
    </select>

    <select id="selectDesignerTarck" resultType="com.xgwc.order.entity.dto.DesignerTarckDto">
        select
            id,
            designer_id,
            track_code,
            track_name,
            next_time,
            details,
            create_by,
            create_time
        from xgwc_designer_tarck
        where designer_id =#{designerId}
    </select>
    <select id="selectDesignerStatisticsScoreAll"
            resultType="com.xgwc.order.entity.dto.DesignerStatisticsScoreAllDto">
        select
            sum(if(score_num > 3, 1,0)) good_num,
            sum(if(score_num = 3, 1,0)) general_num,
            sum(if(score_num &lt; 3, 1,0)) poor_num
        from xgwc_order_score
        where designer_id = #{designerId}
        <if test="scoreType != null">
            and type = #{scoreType}
        </if>
        and score_model = 'evaluation'

    </select>
    <select id="selectOrder"
            parameterType="com.xgwc.order.entity.vo.DesignerStatisticsScoreQueryVo"
            resultType="com.xgwc.order.entity.dto.DesignerStatisticsOrderDto">
        SELECT
            o.id,
            o.order_no,
            o.designer_id,
            o.order_date,
            o.brand_id ,
            bo.company_simple_name brand_name,
            o.state_dic_name business_name
        FROM
            xgwc_order o
        left join xgwc_brand_owner bo on o.brand_id = bo.brand_id
        where
             o.is_del = 0
            <if test="scoreModel != null or scoreType != null">
                and EXISTS (
                    SELECT 1 FROM
                    xgwc_order_score os
                    <where>
                        o.id = os.oder_id
                        <if test="scoreType != null">
                            and os.type = #{scoreType}
                        </if>
                        <if test="designerId != null"> and os.designer_id = #{designerId} </if>
                        <if test="scoreModel != null">
                            and os.score_model = 'evaluation'
                            <if test="scoreModel == 1">
                                and os.score_num > 3
                            </if>
                            <if test="scoreModel == 2">
                                and os.score_num = 3
                            </if>
                            <if test="scoreModel == 3">
                                and os.score_num &lt; 3
                            </if>
                        </if>
                    </where>
                )
            </if>
    </select>


    <insert id="insertTarck" parameterType="com.xgwc.order.entity.DesignerTarck">
        insert into xgwc_designer_tarck
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="designerId != null">designer_id,</if>
            <if test="trackCode != null">track_code,</if>
            <if test="trackName != null">track_name,</if>
            <if test="nextTime != null">next_time,</if>
            <if test="details != null">details,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="designerId != null">#{designerId},</if>
            <if test="trackCode != null">#{trackCode},</if>
            <if test="trackName != null">#{trackName},</if>
            <if test="nextTime != null">#{nextTime},</if>
            <if test="details != null">#{details},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
        <selectKey keyProperty="id" resultType="long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>


    <update id="updateTarck" parameterType="com.xgwc.order.entity.DesignerTarck">
        update xgwc_designer_statis set details = concat(#{details}, '(',DATE_FORMAT(#{createTime}, '%Y-%m-%d %H:%i:%s'), ')')
        where designer_id = #{designerId}
    </update>

</mapper>