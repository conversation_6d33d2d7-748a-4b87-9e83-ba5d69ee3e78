<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.BrandCsCommissionRateConfigMapper">
    

    <sql id="selectBrandCsCommissionRateConfigVo">
        select id, brand_id, type, franchisee_id, gear_name, staff_id, dept_id, business_id, effective_cycle_type, start_time, end_time, status, is_new_business, extra_month_count, extra_type, extra_commission_rate, is_contains_min, range_rate_json, create_by_id, create_time, update_by_id, update_time from brand_cs_commission_rate_config
    </sql>

    <select id="listByQuery" parameterType="com.xgwc.order.entity.vo.BrandCsCommissionRateConfigQueryVo" resultType="com.xgwc.order.entity.BrandCsCommissionRateConfig">
        <include refid="selectBrandCsCommissionRateConfigVo"/>
        <where>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="franchiseeId != null "> and franchisee_id = #{franchiseeId}</if>
            <if test="franchiseeIds != null and franchiseeIds.size() > 0">
                AND franchisee_id IN
                <foreach item="id" collection="franchiseeIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="gearName != null  and gearName != ''"> and gear_name like concat('%', #{gearName}, '%')</if>
            <if test="staffId != null "> and staff_id = #{staffId}</if>
            <if test="staffIds != null and staffIds.size() > 0">
                AND staff_id IN
                <foreach item="id" collection="staffIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptIds != null and deptIds.size() > 0">
                AND dept_id IN
                <foreach item="id" collection="deptIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="businessIds != null and businessIds.size() > 0">
                AND business_id IN
                <foreach item="id" collection="businessIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="effectiveCycleType != null "> and effective_cycle_type = #{effectiveCycleType}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="getByIdBrandId" parameterType="Long" resultType="com.xgwc.order.entity.BrandCsCommissionRateConfig">
        <include refid="selectBrandCsCommissionRateConfigVo"/>
        where id = #{id} and brand_id = #{brandId}
    </select>

    <insert id="insertList" parameterType="com.xgwc.order.entity.BrandCsCommissionRateConfig">
        insert into brand_cs_commission_rate_config ( brand_id, type, franchisee_id, gear_name, staff_id, dept_id, business_id, effective_cycle_type, start_time, end_time, status, is_new_business, extra_month_count, extra_type, extra_commission_rate, is_contains_min, range_rate_json, create_by_id, create_time, update_by_id, update_time)
        values
        <foreach item="item" collection="configList" separator=",">
            (#{item.brandId},#{item.type},#{item.franchiseeId},#{item.gearName},#{item.staffId},#{item.deptId},#{item.businessId},#{item.effectiveCycleType},#{item.startTime},#{item.endTime},#{item.status},#{item.isNewBusiness},#{item.extraMonthCount},#{item.extraType},#{item.extraCommissionRate},#{item.isContainsMin},#{item.rangeRateJson},#{item.createById},#{item.createTime},#{item.updateById},#{item.updateTime})
        </foreach>
    </insert>

    <update id="update" parameterType="com.xgwc.order.entity.BrandCsCommissionRateConfig">
        update brand_cs_commission_rate_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="franchiseeId != null">franchisee_id = #{franchiseeId},</if>
            <if test="gearName != null">gear_name = #{gearName},</if>
            <if test="staffId != null">staff_id = #{staffId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="effectiveCycleType != null">effective_cycle_type = #{effectiveCycleType},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isNewBusiness != null">is_new_business = #{isNewBusiness},</if>
            <if test="extraMonthCount != null">extra_month_count = #{extraMonthCount},</if>
            <if test="extraType != null">extra_type = #{extraType},</if>
            <if test="extraCommissionRate != null">extra_commission_rate = #{extraCommissionRate},</if>
            <if test="isContainsMin != null">is_contains_min = #{isContainsMin},</if>
            <if test="rangeRateJson != null">range_rate_json = #{rangeRateJson},</if>
            <if test="updateById != null">update_by_id = #{updateById},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="remove" parameterType="String">
        delete from brand_cs_commission_rate_config where brand_id = #{brandId}
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>