<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.AfterAgencyAuditMapper">
    

    <sql id="selectAfterAgencyAuditVo">
        select t.id
             ,t.apply_id as applyId
             ,t.brand_id as brandId
             ,t.franchise_id as franchiseId
             ,t.order_no as orderNo
             ,TIMESTAMPDIFF(MINUTE, ra.create_time, now()) minuteTime
             ,ra.refund_type as refundType
             ,ra.last_amount as lastAmount
             ,ra.pre_amount as preAmount
             ,ra.refund_reason as refundReason
             ,ra.remark as remark
             ,ra.customer_phone as customerPhone
             ,ra.platform_refund_time as platformRefundTime
             ,xo.amount as amount
             ,xo.taobao_id as taobaoId
             ,f.franchise_name as franchiseName
             ,xo.sale_man_name as saleManName
             ,xo.state_dic_name as businessName
        from xgwc_after_agency_audit t
        left join order_refund_apply ra on ra.id = t.apply_id
        left join xgwc_order xo on xo.order_no = t.order_no
        left join franchise f on f.id = t.franchise_id
    </sql>

    <select id="selectAfterAgencyAuditList" parameterType="com.xgwc.order.entity.vo.AfterAgencyAuditQueryVo"
            resultType="com.xgwc.order.entity.dto.AfterAgencyAuditDto">
        select t.id
            ,t.apply_id as applyId
            ,t.brand_id as brandId
            ,t.franchise_id as franchiseId
            ,t.order_no as orderNo
            ,TIMESTAMPDIFF(MINUTE, ra.create_time, now()) minuteTime
            ,ra.refund_type as refundType
            ,ra.last_amount as lastAmount
            ,ra.pre_amount as preAmount
            ,ra.refund_reason as refundReason
            ,ra.remark as remark
            ,ra.customer_phone as customerPhone
            ,ra.platform_refund_time as platformRefundTime
            ,xo.amount as amount
            ,xo.taobao_id as taobaoId
            ,f.franchise_name as franchiseName
            ,xo.sale_man_name as saleManName
            ,xo.state_dic_name as businessName
            -- 默认退款
            ,case
                when TIMESTAMPDIFF(MINUTE, now(), ra.platform_refund_time) &lt;= 480
                and TIMESTAMPDIFF(MINUTE, now(), ra.platform_refund_time) &gt; 60
                then 1
                else 0
            end as willAutoRefund
            -- 超时未领取
            ,case
                when TIMESTAMPDIFF(MINUTE, now(), ra.platform_refund_time) &lt;= 60
                then 1
                else 0
            end as timeoutUnreceived
        from xgwc_after_agency_audit t
        left join order_refund_apply ra on ra.id = t.apply_id
        left join xgwc_order xo on xo.order_no = t.order_no
        left join franchise f on f.id = t.franchise_id
        where t.check_status = 0
        <if test="brandId != null "> and t.brand_id = #{brandId}</if>
        <if test="franchiseId != null "> and t.franchise_id = #{franchiseId}</if>
        <if test="receiverId != null "> and t.receiver_id = #{receiverId}</if>
        <if test="other != null  and other != ''">
            and (t.order_no like concat('%', #{other}, '%')
                     or xo.taobao_id like concat('%', #{other}, '%')
                     or xo.sale_man_name like concat('%', #{other}, '%'))
        </if>
        order by t.create_time desc
    </select>
    
    <select id="selectAfterAgencyAuditById" parameterType="Long" resultType="com.xgwc.order.entity.dto.AfterAgencyAuditDto">
        <include refid="selectAfterAgencyAuditVo"/>
        where t.id = #{id}
    </select>

    <select id="selectMyAfterAlreadyAuditList" resultType="com.xgwc.order.entity.dto.AfterAlreadyAuditDto">
        select t.id
             ,t.apply_id as applyId
             ,t.order_no as orderNo
             ,t.franchise_id as franchiseId
             ,ra.refund_type as refundType
             ,ra.last_amount as lastAmount
             ,ra.pre_amount as preAmount
             ,ra.refund_reason as refundReason
             ,ra.apply_user_name as applyUserName
             ,ra.create_time as createTime
             ,ra.last_approve_time as lastApproveTime
             ,ra.apply_status as applyStatus
             ,ra.customer_phone as customerPhone
             ,xo.amount as amount
             ,xo.taobao_id as taobaoId
             ,f.franchise_name as franchiseName
        from xgwc_after_agency_audit t
        left join order_refund_apply ra on ra.id = t.apply_id
        left join xgwc_order xo on xo.order_no = t.order_no
        left join franchise f on f.id = t.franchise_id
        where t.check_id = #{checkId}
        <if test="brandId != null "> and t.brand_id = #{brandId}</if>
        <if test="franchiseId != null "> and t.franchise_id = #{franchiseId}</if>
        <if test="refundType != null "> and ra.refund_type = #{refundType}</if>
        <if test="refundReasonCode != null "> and ra.refund_reason_code = #{refundReasonCode}</if>
        <if test="applyStatus != null"> and ra.apply_status = #{applyStatus}</if>
        <if test="startTime != null and endTime != null ">
            and ra.create_time BETWEEN CONCAT(#{startTime}, ' 00:00:00') AND CONCAT(#{endTime}, ' 23:59:59')
        </if>
        <if test="other != null  and other != ''">
            and (t.order_no like concat('%', #{other}, '%')
            or xo.taobao_id like concat('%', #{other}, '%')
            or ra.apply_user_name like concat('%', #{other}, '%')
            or t.id like concat('%', #{other}, '%'))
        </if>
        order by t.create_time desc
    </select>

    <insert id="insertAfterAgencyAudit" parameterType="com.xgwc.order.entity.AfterAgencyAudit" useGeneratedKeys="true" keyProperty="id">
        insert into xgwc_after_agency_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyId != null">apply_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="id != null">id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="receiverId != null">receiver_id,</if>
            <if test="status != null">status,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyId != null">#{applyId},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="receiverId != null">#{receiverId},</if>
            <if test="status != null">#{status},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAfterAgencyAudit" parameterType="com.xgwc.order.entity.AfterAgencyAudit">
        update xgwc_after_agency_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="receiverId != null">receiver_id = #{receiverId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAfterAgencyAuditStatus">
        update xgwc_after_agency_audit set check_status = #{checkStatus}, check_id = #{userId},audit_remark = #{auditRemark} where id = #{agencyAuditId}
    </update>

</mapper>