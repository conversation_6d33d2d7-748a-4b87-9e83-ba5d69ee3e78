<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.FollowMapper">

    <select id="findFollowListByAgencyAuditId" resultType="com.xgwc.order.entity.dto.FollowDto">
        select f.*, s.stage_name as followUserName
        from xgwc_follow f
        left join xgwc_staff s on f.follow_user_id = s.bind_user_id
        where f.agency_audit_id = #{agencyAuditId}
        order by f.create_time desc
    </select>

    <select id="findFollowDtoByAgencyAuditIds" resultType="com.xgwc.order.entity.dto.FollowDto">
        select f.id,f.agency_audit_id,f.follow_user_id,f.consult_type,f.return_time
        from xgwc_follow f
        where f.agency_audit_id in
        <foreach item="agencyAuditId" collection="agencyAuditIds" open="(" separator="," close=")">
            #{agencyAuditId}
        </foreach>
        order by f.create_time desc limit 1
    </select>

    <insert id="insertFollow" parameterType="com.xgwc.order.entity.Follow">
        insert into xgwc_follow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="agencyAuditId != null">agency_audit_id,</if>
            <if test="followUserId != null">follow_user_id,</if>
            <if test="consultType != null">consult_type,</if>
            <if test="returnTime != null">return_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="agencyAuditId != null">#{agencyAuditId},</if>
            <if test="followUserId != null">#{followUserId},</if>
            <if test="consultType != null">#{consultType},</if>
            <if test="returnTime != null">#{returnTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

</mapper>