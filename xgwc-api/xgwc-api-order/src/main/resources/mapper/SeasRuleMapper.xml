<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.SeasRuleMapper">

    <insert id="saveSeasRule" keyColumn="id" useGeneratedKeys="true">
        insert into xgwc_seas_rule(staff_receive, dept_receive, day_limit_open, day_limit, interval_minuter_open,interval_minuter, recycle_rule, any_or_all, behavior_type, recycle_no_eff, recycle_no_deal, brand_id, status, create_by, create_time, update_by, update_time, modify_time)
        values(#{staffReceive}, #{deptReceive}, #{dayLimitOpen}, #{dayLimit}, #{intervalEnabled}, #{intervalMinuter},#{recycleRule}, #{anyOrAll}, #{behaviorType}, #{recycleNoEff}, #{recycleNoDeal}, #{brandId}, 0, #{createBy}, now(), #{createBy}, now(), now())
    </insert>

    <update id="updateSeasRule">
        update xgwc_seas_rule
            <set>
                update_by = #{updateBy}, update_time = now(), modify_time = now()
                <if test="staffReceive != null">
                    ,staff_receive = #{staffReceive}
                </if>
                <if test="deptReceive != null">
                    ,dept_receive = #{deptReceive}
                </if>
                <if test="dayLimitOpen != null">
                    ,day_limit_open = #{dayLimitOpen}
                </if>
                <if test="intervalEnabled != null">
                    ,interval_minuter_open = #{intervalEnabled}
                </if>
                <if test="dayLimit != null">
                    ,day_limit = #{dayLimit}
                </if>
                <if test="intervalMinuter != null">
                    ,interval_minuter = #{intervalMinuter}
                </if>
                <if test="recycleRule != null">
                    ,recycle_rule = #{recycleRule}
                </if>
                <if test="anyOrAll != null">
                    ,any_or_all = #{anyOrAll}
                </if>
                <if test="behaviorType != null">
                    ,behavior_type = #{behaviorType}
                </if>
                <if test="recycleNoEff != null">
                    ,recycle_no_eff = #{recycleNoEff}
                </if>
                <if test="recycleNoDeal != null">
                    ,recycle_no_deal = #{recycleNoDeal}
                </if>
            </set>
           <where>
               id = #{id}
           </where>
    </update>

    <select id="getSeasRule" resultType="com.xgwc.order.entity.dto.SeasRuleDto">
        select id, staff_receive staffReceive, dept_receive deptReceive, day_limit_open dayLimitOpen, day_limit dayLimit, interval_minuter_open intervalEnabled, interval_minuter intervalMinuter, recycle_rule recycleRule
        , any_or_all anyOrAll, behavior_type behaviorType, recycle_no_eff recycleNoEff, recycle_no_deal recycleNoDeal from xgwc_seas_rule where brand_id = #{brandId} and status = 0 limit 1
    </select>
</mapper>