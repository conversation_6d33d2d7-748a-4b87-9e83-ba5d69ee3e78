<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderRefundApplyMapper">

    <sql id="selectOrderRefundApplyVo">
        select id, refund_type, refund_reason_code, refund_reason, order_no, pre_amount, last_amount, remark, pre_commission, last_commission, apply_user_id,
               apply_user_name, franchise_id, brand_id, last_approve_time, apply_status, status, create_time, update_time, platform_refund_time,after_check_status from order_refund_apply
    </sql>

    <sql id="selectOrderRefundPayVo">
        select id, pre_id, apply_id, order_no, pay_type, pay_img, amount, pay_channel, collection_no, refund_amount, refund_config, refund_pay_channel, status,
               create_time, update_time from order_refund_pay
    </sql>

    <sql id="selectOrderRefundCommisionVo">
        select t.id, t.pre_id, t.apply_id, t.sub_order_no, t.designer_user_id, t.designer_user_name, t.commision_amount, t.after_commision_amount, t.status, t.create_time, t.update_time, t.create_by
             ,xd.phone as designerPhone,xb.business_name as designerBusiness
        from order_refund_commision t
        left join xgwc_designer xd on xd.designer_id = t.designer_user_id
        left join xgwc_business xb on xb.business_id = xd.good_business
    </sql>

    <insert id="insertOrderRefundApply">
        insert into order_refund_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="refundType != null">refund_type,</if>
            <if test="refundReasonCode != null">refund_reason_code,</if>
            <if test="refundReason != null">refund_reason,</if>
            <if test="preId != null">pre_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="preAmount != null">pre_amount,</if>
            <if test="lastAmount != null">last_amount,</if>
            <if test="remark != null">remark,</if>
            <if test="preCommision != null">pre_commission,</if>
            <if test="lastCommision != null">last_commission,</if>
            <if test="applyUserId != null">apply_user_id,</if>
            <if test="applyUserName != null">apply_user_name,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="executionId != null">execution_id,</if>
            <if test="customerPhone != null">customer_phone,</if>
            <if test="platformRefundTime != null">platform_refund_time,</if>
            last_approve_time, status, create_time, update_time
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="refundType != null">#{refundType},</if>
            <if test="refundReasonCode != null">#{refundReasonCode},</if>
            <if test="refundReason != null">#{refundReason},</if>
            <if test="preId != null">#{preId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="preAmount != null">#{preAmount},</if>
            <if test="lastAmount != null">#{lastAmount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="preCommision != null">#{preCommision},</if>
            <if test="lastCommision != null">#{lastCommision},</if>
            <if test="applyUserId != null">#{applyUserId},</if>
            <if test="applyUserName != null">#{applyUserName},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="executionId != null">#{executionId},</if>
            <if test="customerPhone != null">#{customerPhone},</if>
            <if test="platformRefundTime != null">#{platformRefundTime},</if>
            null, 0,NOW(),NOW()
        </trim>
    </insert>

    <insert id="batchInsertOrderRefundPayList">
        insert into order_refund_pay(pre_id, apply_id, order_no, pay_type, pay_img, amount, pay_channel, collection_no, refund_amount, refund_config, refund_pay_channel, status,
                                     create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.preId}, #{item.applyId}, #{item.orderNo}, #{item.payType}, #{item.payImg}, #{item.amount}, #{item.payChannel}, #{item.collectionNo}, #{item.refundAmount}
            , #{item.refundConfig}, #{item.refundPayChannel}, 0, now(), now())
        </foreach>
    </insert>

    <insert id="batchInsertOrderRefundCommisionList">
        insert into order_refund_commision(pre_id, apply_id, sub_order_no, designer_user_id, designer_user_name, commision_amount, after_commision_amount, status, create_time, update_time, create_by)
            values
        <foreach collection="list" item="item" separator=",">
            (#{item.preId}, #{item.applyId}, #{item.subOrderNo}, #{item.designerUserId}, #{item.designerUserName}, #{item.commisionAmount}, #{item.afterCommisionAmount}, 0, now(), now(), #{item.createBy})
        </foreach>
    </insert>

    <update id="updateOrderRefundApply">
        update order_refund_apply
        <set>
            update_time = now()
            <if test="lastApproveTime != null">
                ,last_approve_time = #{lastApproveTime}
            </if>
            <if test="status != null">
                ,status = #{status}
            </if>
            <if test="applyStatus != null">
                ,apply_status = #{applyStatus}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateOrderRefundApplyReceiveStatus">
        update order_refund_apply set is_receive = #{isReceive} where id = #{id}
    </update>

    <update id="updateOrderRefundApplyCheckStatus">
        UPDATE order_refund_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="afterCheckStatus != null">
                after_check_status = #{afterCheckStatus},
            </if>
            <if test="executionId != null">
                execution_id = #{executionId},
            </if>
            <if test="applyStatus != null">
                apply_status = #{applyStatus},
            </if>
            <if test="lastApproveTime != null">
                last_approve_time = #{lastApproveTime},
            </if>
        </trim>
        WHERE id = #{id}
    </update>


    <select id="getOrderRefundApplyDtoById" resultType="com.xgwc.order.entity.dto.OrderRefundApplyDto">
     SELECT
                    t.id,
                    t.refund_type,
                    t.refund_reason_code,
                    t.refund_reason,
                    t.order_no,
                    t.pre_amount,
                    t.last_amount,
                    t.remark,
                    t.pre_id,
                    t.pre_commission,
                    t.last_commission,
                    t.apply_user_id,
                    t.apply_user_name,
                    t.franchise_id,
                    t.brand_id,
                    t.last_approve_time,
                    t.apply_status,
                    t.create_time,
                    t.update_time,
                    t.platform_refund_time,
                    o.taobao_id customerNo,
                    o.sale_man_name,
                    o.order_amount,
                    o.amount,
                    o.order_date,
                    a.audit_remark as auditRemark
                FROM order_refund_apply t
                LEFT JOIN xgwc_order o ON t.order_no = o.order_no
                left join xgwc_after_agency_audit a on t.id = a.apply_id
                WHERE
                    t.id = #{id} and t.status = 0 and o.is_del = 0
    </select>

    <select id="getOrderRefundCommisionListByApplyId" resultType="com.xgwc.order.entity.OrderRefundCommision">
        <include refid="selectOrderRefundCommisionVo"/>
        where t.apply_id = #{applyId}
    </select>

    <select id="getOrderRefundPayListByApplyId" resultType="com.xgwc.order.entity.OrderRefundPay">
        <include refid="selectOrderRefundPayVo"/>
        where apply_id = #{applyId}
    </select>

    <select id="listByApprove" resultType="com.xgwc.order.entity.dto.OrderRefundApplyApproveDto">
        SELECT ora.id, xft.task_id_, ora.execution_id, ora.refund_type, ora.pre_amount, ora.last_amount, ora.pre_commission, ora.last_commission,
               ora.refund_reason_code, xo.order_date, ora.order_no, xo.taobao_id, xo.franchise_id, xo.sale_man_name, ora.apply_user_name, ora.create_time, ora.last_approve_time, ora.apply_status
        FROM order_refund_apply ora
            LEFT JOIN xgwc_order xo ON ora.order_no = xo.order_no
            LEFT JOIN xgwc_flow_execution xfe on ( xfe.flow_value = 'AfterSales' and ora.execution_id = xfe.id )
            LEFT JOIN xgwc_flow_task xft on ora.execution_id = xft.execution_id_
        WHERE
            ora.apply_status is not null
            <if test="brandId != null">and ora.brand_id = #{brandId}</if>
            <if test="brandIdList != null and brandIdList.size() > 0">
                and ora.brand_id in
                <foreach item="item" collection="brandIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                and (
                    ora.order_no like concat('%', #{keyword}, '%')
                    or xo.taobao_id like concat('%', #{keyword}, '%')
                    or ora.id like concat('%', #{keyword}, '%')
                    or xo.sale_man_name like concat('%', #{keyword}, '%')
                    or ora.apply_user_name like concat('%', #{keyword}, '%')
                )
            </if>
            <if test="applyStatus != null and applyStatus != ''">and ora.apply_status = #{applyStatus}</if>
            <if test="franchiseId != null">and xo.franchise_id = #{franchiseId}</if>
            <if test="refundType != null">and ora.refund_type = #{refundType}</if>
            <if test="refundReasonCode != null and refundReasonCode != ''">and ora.refund_reason_code = #{refundReasonCode}</if>
            <if test="crateTimeStart != null and createTimeEnd != null">
                and ora.create_time between #{crateTimeStart} and #{createTimeEnd}
            </if>
    </select>

    <select id="getById" resultType="com.xgwc.order.entity.OrderRefundApply">
        <include refid="selectOrderRefundApplyVo"/>
        where id = #{id}
    </select>

    <select id="listApproveIngByOrderNo" resultType="com.xgwc.order.entity.dto.OrderRefundApplyApproveIngDto">
        SELECT ora.id, ora.after_check_status, ora.apply_status, xaaa.id as agencyAuditId, xft.task_id_
        FROM order_refund_apply ora
            LEFT JOIN order_refund_pay orp on ora.id = orp.apply_id
            LEFT JOIN xgwc_after_agency_audit xaaa on (ora.id = xaaa.apply_id and xaaa.check_status = 0)
            LEFT JOIN xgwc_flow_task xft on ora.execution_id = xft.execution_id_
        WHERE orp.refund_amount > 0
            AND (ora.apply_status in ('ING', 'AFTER_SALES_ING') or ora.after_check_status = 0)
            AND ora.brand_id = #{brandOwnerId}
            AND orp.order_no in
            <foreach item="item" collection="orderNoList" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>

    <select id="getOrderRefundApplyByOrderNo" resultType="com.xgwc.order.entity.OrderRefundApply">
        <include refid="selectOrderRefundApplyVo"/>
        where order_no = #{orderNo} and brand_id = #{brandOwnerId}
        order by create_time desc
        limit 1
    </select>

    <select id="findByOrderNo" resultType="com.xgwc.order.entity.dto.OrderRefundApplyApproveDto">
        <include refid="selectOrderRefundApplyVo"/>
        where order_no = #{orderNo}
        and apply_status in ('ING', 'PASS','AFTER_SALES_ING')
    </select>

</mapper>