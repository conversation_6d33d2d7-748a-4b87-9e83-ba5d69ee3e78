<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.DispatchMapper">
    

    <sql id="selectDispatchVo">
        select create_by, create_time, dispatch_number, dispatch_status, dispatch_type, id, modify_time, need_dispatch_number, order_id, receive_time, return_number, update_by, update_time from xgwc_dispatch
    </sql>

    <select id="selectDispatchList" parameterType="com.xgwc.order.entity.vo.DispatchQueryVo" resultType="com.xgwc.order.entity.dto.DispatchPageDto">
        SELECT
            t.id,
            t.dispatch_number,
            t.dispatch_type,
            t.need_dispatch_number,
            t.order_id,
            t.return_number,
            CASE
                WHEN t.return_number > 2 THEN 1
                ELSE 0
            END AS is_difficult,
            TIMESTAMPDIFF(MINUTE, o.create_time, now()) minuteTime,
            o.order_no AS orderNo,
            o.taobao_id AS taobaoId,
            o.sale_man_name AS saleManName,
            o.state_dic_code AS stateDicCode,
            o.state_dic_name AS stateDicName,
            o.brand_id AS brandId,
            o.order_amount AS orderAmount,
            o.store_name AS storeName,
            o.allot_remark AS allotRemark,
            o.allot_file AS allotFile,
            o.archive_expect_time AS archiveExpectTime,
            f.franchise_name AS companyName
        FROM xgwc_dispatch t
        LEFT JOIN xgwc_order o ON t.order_id = o.id
        LEFT JOIN franchise f ON o.franchise_id = f.id
        where t.need_dispatch_number > t.dispatch_number
        <if test="brandId != null "> and t.brand_id = #{brandId}</if>
        <if test="franchiseIds != null and franchiseIds != ''"> and o.franchise_id in (#{franchiseIds})</if>
        <if test="type != null and type == 2"> and t.dispatch_type = 2</if>
        <if test="type != null and type == 3"> and t.return_number > 2</if>
        <if test="dispatchNumber != null "> and t.dispatch_number = #{dispatchNumber}</if>
        <if test="dispatchStatus != null "> and t.dispatch_status = #{dispatchStatus}</if>
        <if test="dispatchType != null  and dispatchType != ''"> and t.dispatch_type like concat('%', #{dispatchType}, '%')</if>
        <if test="id != null "> and t.id = #{id}</if>
        <if test="needDispatchNumber != null "> and t.need_dispatch_number = #{needDispatchNumber}</if>
        <if test="orderId != null "> and t.order_id = #{orderId}</if>
        <if test="receiveTime != null "> and t.receive_time between #{receiveTime}[0] and #{receiveTime}[1] </if>
        <if test="returnNumber != null "> and t.return_number = #{returnNumber}</if>
        <if test="createBy != null  and createBy != ''"> and t.create_by like concat('%', #{createBy}, '%')</if>
        <if test="createTime != null "> and t.create_time between #{createTime}[0] and #{createTime}[1] </if>
        order by t.create_time desc
    </select>
    
    <select id="selectDispatchById" parameterType="Long" resultType="com.xgwc.order.entity.dto.DispatchDto">
        <include refid="selectDispatchVo"/>
        where id = #{id}
    </select>

    <select id="countDispatch" resultType="java.lang.Integer">
        select count(*) from xgwc_dispatch where need_dispatch_number > dispatch_number and brand_id = #{brandId}
    </select>

    <select id="countUrgent" resultType="java.lang.Integer">
        select count(*) from xgwc_dispatch where dispatch_type = 2 and need_dispatch_number > dispatch_number and brand_id = #{brandId}
    </select>

    <select id="countDifficult" resultType="java.lang.Integer">
        select count(*) from xgwc_dispatch where return_number > 2 and need_dispatch_number > dispatch_number and brand_id = #{brandId}
    </select>

    <select id="selectDispatchByOrderId" resultType="com.xgwc.order.entity.dto.DispatchDto">
        <include refid="selectDispatchVo"/>
        where order_id = #{orderId}
    </select>

    <insert id="insertDispatch" parameterType="com.xgwc.order.entity.Dispatch">
        insert into xgwc_dispatch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="dispatchNumber != null">dispatch_number,</if>
            <if test="dispatchStatus != null">dispatch_status,</if>
            <if test="dispatchType != null">dispatch_type,</if>
            <if test="id != null">id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="needDispatchNumber != null">need_dispatch_number,</if>
            <if test="orderId != null">order_id,</if>
            <if test="receiveTime != null">receive_time,</if>
            <if test="returnNumber != null">return_number,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="brandId != null">brand_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="dispatchNumber != null">#{dispatchNumber},</if>
            <if test="dispatchStatus != null">#{dispatchStatus},</if>
            <if test="dispatchType != null">#{dispatchType},</if>
            <if test="id != null">#{id},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="needDispatchNumber != null">#{needDispatchNumber},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="receiveTime != null">#{receiveTime},</if>
            <if test="returnNumber != null">#{returnNumber},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="brandId != null">#{brandId},</if>
         </trim>
    </insert>

    <update id="updateDispatch" parameterType="com.xgwc.order.entity.Dispatch">
        update xgwc_dispatch
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="dispatchNumber != null">dispatch_number = #{dispatchNumber},</if>
            <if test="dispatchStatus != null">dispatch_status = #{dispatchStatus},</if>
            <if test="dispatchType != null">dispatch_type = #{dispatchType},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="needDispatchNumber != null">need_dispatch_number = #{needDispatchNumber},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="returnNumber != null">return_number = #{returnNumber},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteDispatchById" parameterType="Long">
        update xgwc_dispatch set is_del = 1 where id = #{id}
    </update>

    <update id="deleteDispatchByIds" parameterType="String">
        update xgwc_dispatch set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>