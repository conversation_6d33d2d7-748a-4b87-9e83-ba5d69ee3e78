<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.IncomLineMapper">
    

    <sql id="selectIncomLineVo">
        SELECT
            l.id,
            l.in_time,
            l.taobao_id,
            l.wechat_tag,
            l.tel,
            l.shop_id,
            l.shop_name,
            l.valid_dic_code,
            l.valid_dic_name,
            l.valid_dic_code2,
            l.valid_dic_name2,
            l.order_id,
            l.order_no,
            l.state_dic_code,
            l.state_dic_name,
            l.next_time,
            l.need_info,
            l.sale_id,
            l.sale_name,
            l.dept_id,
            l.dept_name,
            l.track_name,
            l.create_by,
            l.create_time,
            l.update_by,
            l.update_time,
            l.is_del,
            l.brand_id,
            l.franchise_id,
            l.plan_money,
            l.recycle_time,
            ifnull(bo.company_simple_name,bo.company_name) brand_name,
            ifnull(fo.company_simple_name,fo.company_name) franchise_name
        FROM
            incom_line l
        left join xgwc_brand_owner bo on l.brand_id = bo.brand_id
        left join franchise_owner fo on l.franchise_id = fo.franchise_id and l.brand_id = fo.brand_id
    </sql>

    <select id="selectIncomLineList" parameterType="com.xgwc.order.entity.vo.IncomLineQueryVo" resultType="com.xgwc.order.entity.dto.IncomLineDto">
        <include refid="selectIncomLineVo"/>
        <where>
            l.is_del = 0
            <if test="isSeas == null">
                and l.recycle_time > now()
            </if>
            <if test="isSeas != null">
                and l.recycle_time &lt; now()
            </if>
            <if test="taobaoId != null  and taobaoId != ''"> and l.taobao_id like concat('%', #{taobaoId}, '%')</if>
            <if test="saleName != null  and saleName != ''"> and l.sale_name like concat('%', #{saleName}, '%')</if>
            <if test="tel != null  and tel != ''"> and l.tel = #{tel}</if>
            <if test="validDicCode != null  and validDicCode != ''"> and l.valid_dic_code = #{validDicCode}</if>
            <if test="validDicCode2 != null  and validDicCode2 != ''"> and l.valid_dic_code2 = #{validDicCode2}</if>
            <if test="stateDicCode != null  and stateDicCode != ''"> and l.state_dic_code = #{stateDicCode} </if>
            <if test="trackCode != null  and trackCode != ''">
                and EXISTS (
                    select 1 from incom_line_tarck lt where lt.in_line_id = l.id and lt.track_code = #{trackCode}
                )
             </if>
            <if test="wechatTag != null "> and l.wechat_tag = #{wechatTag}</if>
            <if test="inTime != null and inTime != null">
                and l.in_time >= #{inTime}
            </if>
            <if test="inTimeStart != null and inTimeEnd != null">
                and l.in_time between #{inTimeStart} and DATE_ADD(#{inTimeEnd}, INTERVAL 1 DAY)
            </if>
            <if test="nextTimeStart != null and nextTimeEnd != null ">
                and l.next_time between #{nextTimeStart} and DATE_ADD(#{nextTimeEnd}, INTERVAL 1 DAY)
            </if>
            <if test="saleId != null "> and l.sale_id = #{saleId}</if>
            <if test="deptId != null "> and l.dept_id = #{deptId}</if>
            <if test="deptIds != null "> and l.dept_id in
                <foreach item="id" collection="deptIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
             </if>
            <if test="brandId != null "> and l.brand_id = #{brandId}</if>
            <if test="franchiseId != null "> and l.franchise_id = #{franchiseId}</if>
            <if test="franchiseName != null "> and ifnull(fo.company_simple_name,fo.company_name) like concat('%',  #{franchiseName}, '%')</if>
        </where>

    </select>

    <select id="selectIncomLineTotal" parameterType="com.xgwc.order.entity.vo.IncomLineQueryVo" resultType="com.xgwc.order.entity.dto.IncomLineTotalDto">
        select
            sum(if(l.track_code = 'ing',1 , 0)) followingup,
            sum(if(l.track_code = 'follow',1 , 0)) tobeup,
            sum(if(l.recycle_time &lt; now() + INTERVAL 3 DAY,1 , 0)) toberecycled
        from
         incom_line l
        <where>
            l.is_del = 0
            and l.recycle_time > now()
            <if test="brandId != null "> and l.brand_id = #{brandId}</if>
            <if test="franchiseId != null "> and l.franchise_id = #{franchiseId}</if>
        </where>
    </select>
    
    <select id="selectIncomLineById" parameterType="Long" resultType="com.xgwc.order.entity.dto.IncomLineDto">
        <include refid="selectIncomLineVo"/>
        where l.id = #{id}
    </select>

    <insert id="insertIncomLine" parameterType="com.xgwc.order.entity.IncomLine">
        insert into incom_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="inTime != null">in_time,</if>
            <if test="taobaoId != null">taobao_id,</if>
            <if test="wechatTag != null">wechat_tag,</if>
            <if test="tel != null">tel,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="validDicCode != null">valid_dic_code,</if>
            <if test="validDicName != null">valid_dic_name,</if>
            <if test="validDicCode2 != null">valid_dic_code2,</if>
            <if test="validDicName2 != null">valid_dic_name2,</if>
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="stateDicCode != null">state_dic_code,</if>
            <if test="stateDicName != null">state_dic_name,</if>
            <if test="nextTime != null">next_time,</if>
            <if test="needInfo != null">need_info,</if>
            <if test="saleId != null">sale_id,</if>
            <if test="saleName != null">sale_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="trackName != null">track_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="planMoney != null">plan_money,</if>
            <if test="recycleType != null">recycle_type,</if>
            <if test="recycleTime != null">recycle_time,</if>
            <if test="recycleNum != null">recycle_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="inTime != null">#{inTime},</if>
            <if test="taobaoId != null">#{taobaoId},</if>
            <if test="wechatTag != null">#{wechatTag},</if>
            <if test="tel != null">#{tel},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="validDicCode != null">#{validDicCode},</if>
            <if test="validDicName != null">#{validDicName},</if>
            <if test="validDicCode2 != null">#{validDicCode2},</if>
            <if test="validDicName2 != null">#{validDicName2},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="stateDicCode != null">#{stateDicCode},</if>
            <if test="stateDicName != null">#{stateDicName},</if>
            <if test="nextTime != null">#{nextTime},</if>
            <if test="needInfo != null">#{needInfo},</if>
            <if test="saleId != null">#{saleId},</if>
            <if test="saleName != null">#{saleName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="trackName != null">#{trackName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="planMoney != null">#{planMoney},</if>
            <if test="recycleType != null">#{recycleType},</if>
            <if test="recycleTime != null">#{recycleTime},</if>
            <if test="recycleNum != null">#{recycleNum},</if>
         </trim>
    </insert>

    <update id="updateIncomLine" parameterType="com.xgwc.order.entity.IncomLine">
        update incom_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="inTime != null">in_time = #{inTime},</if>
            <if test="taobaoId != null">taobao_id = #{taobaoId},</if>
            <if test="wechatTag != null">wechat_tag = #{wechatTag},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="validDicCode != null">valid_dic_code = #{validDicCode},</if>
            <if test="validDicName != null">valid_dic_name = #{validDicName},</if>
            <if test="validDicCode2 != null">valid_dic_code2 = #{validDicCode2},</if>
            <if test="validDicName2 != null">valid_dic_name2 = #{validDicName2},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="stateDicCode != null">state_dic_code = #{stateDicCode},</if>
            <if test="stateDicName != null">state_dic_name = #{stateDicName},</if>
            <if test="nextTime != null">next_time = #{nextTime},</if>
            <if test="needInfo != null">need_info = #{needInfo},</if>
            <if test="saleId != null">sale_id = #{saleId},</if>
            <if test="saleName != null">sale_name = #{saleName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="trackCode != null">track_code = #{trackCode},</if>
            <if test="trackName != null">track_name = #{trackName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="planMoney != null">plan_money = #{planMoney},</if>
            <if test="recycleType != null">recycle_type = #{recycleType},</if>
            <if test="recycleTime != null">recycle_time = #{recycleTime},</if>
            <if test="recycleNum != null">recycle_num = #{recycleNum},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteIncomLineById" parameterType="Long">
        update incom_line set is_del = 1 where id = #{id}
    </update>

    <update id="deleteIncomLineByIds" parameterType="String">
        update incom_line set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>