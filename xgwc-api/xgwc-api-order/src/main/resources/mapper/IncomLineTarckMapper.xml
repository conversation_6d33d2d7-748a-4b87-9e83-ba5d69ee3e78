<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.IncomLineTarckMapper">
    

    <sql id="selectIncomLineTarckVo">
        select id, in_line_id, track_code, track_name, next_time, order_id, order_no, track_by, track_time, brief_introduction, details, resource, dept_id, dept_name, create_by, create_time, update_by, update_time, is_del from incom_line_tarck
    </sql>

    <select id="selectIncomLineTarckList" parameterType="com.xgwc.order.entity.vo.IncomLineTarckQueryVo" resultType="com.xgwc.order.entity.dto.IncomLineTarckDto">
        <include refid="selectIncomLineTarckVo"/>
        <where>
            <if test="inLineId != null "> and in_line_id = #{inLineId}</if>
        </where>
    </select>

    <select id="selectIncomLineTarckListByCustomer" parameterType="com.xgwc.order.entity.vo.IncomCustomerQueryVo" resultType="com.xgwc.order.entity.dto.IncomLineTarckDto">
        select
            lt.id,
            lt.in_line_id,
            lt.track_code,
            lt.track_name,
            lt.track_code2,
            lt.track_name2,
            lt.next_time,
            lt.order_id,
            lt.order_no,
            lt.track_by,
            lt.track_time,
            lt.brief_introduction,
            lt.details,
            lt.resource,
            lt.dept_id,
            lt.dept_name,
            lt.create_by,
            lt.create_time,
            lt.update_by,
            lt.update_time,
            lt.is_del,
            l.state_dic_code,
            l.state_dic_name
        from incom_line_tarck lt
        left join incom_line l on lt.in_line_id = l.id
        where l.taobao_id = #{taobaoId}
        <if test="brandId != null">
            and l.brand_id = #{brandId}
        </if>
        <if test="franchiseId != null">
            and l.franchise_id = #{franchiseId}
        </if>
        order by lt.track_time desc
    </select>

    <select id="selectIncomLineTarckById" parameterType="Long" resultType="com.xgwc.order.entity.dto.IncomLineTarckDto">
        <include refid="selectIncomLineTarckVo"/>
        where id = #{id}
    </select>

    <insert id="insertIncomLineTarck" parameterType="com.xgwc.order.entity.IncomLineTarck">
        insert into incom_line_tarck
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="inLineId != null">in_line_id,</if>
            <if test="trackCode != null">track_code,</if>
            <if test="trackName != null">track_name,</if>
            <if test="trackCode2 != null">track_code2,</if>
            <if test="trackName2 != null">track_name2,</if>
            <if test="nextTime != null">next_time,</if>
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="trackBy != null">track_by,</if>
            <if test="trackTime != null">track_time,</if>
            <if test="briefIntroduction != null">brief_introduction,</if>
            <if test="details != null">details,</if>
            <if test="resource != null">resource,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="inLineId != null">#{inLineId},</if>
            <if test="trackCode != null">#{trackCode},</if>
            <if test="trackName != null">#{trackName},</if>
            <if test="trackCode2 != null">#{trackCode2},</if>
            <if test="trackName2 != null">#{trackName2},</if>
            <if test="nextTime != null">#{nextTime},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="trackBy != null">#{trackBy},</if>
            <if test="trackTime != null">#{trackTime},</if>
            <if test="briefIntroduction != null">#{briefIntroduction},</if>
            <if test="details != null">#{details},</if>
            <if test="resource != null">#{resource},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateIncomLineTarck" parameterType="com.xgwc.order.entity.IncomLineTarck">
        update incom_line_tarck
        <trim prefix="SET" suffixOverrides=",">
            <if test="inLineId != null">in_line_id = #{inLineId},</if>
            <if test="trackCode != null">track_code = #{trackCode},</if>
            <if test="trackName != null">track_name = #{trackName},</if>
            <if test="trackCode != null">track_code2 = #{trackCode2},</if>
            <if test="trackName != null">track_name2 = #{trackName2},</if>
            <if test="nextTime != null">next_time = #{nextTime},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="trackBy != null">track_by = #{trackBy},</if>
            <if test="trackTime != null">track_time = #{trackTime},</if>
            <if test="briefIntroduction != null">brief_introduction = #{briefIntroduction},</if>
            <if test="details != null">details = #{details},</if>
            <if test="resource != null">resource = #{resource},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteIncomLineTarckById" parameterType="Long">
        update incom_line_tarck set is_del = 1 where id = #{id}
    </update>

    <update id="deleteIncomLineTarckByIds" parameterType="String">
        update incom_line_tarck set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>