<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.IncomCustomerMapper">
    

    <sql id="selectIncomCustomerVo">
        SELECT
        c.taobao_id,
        any_value(c.taobao_lv) taobao_lv,
        any_value(c.tel) tel,
        any_value(c.last_time) last_time,
        any_value(c.track_name) track_name,
        sum(c.line_num) line_num,
        sum(c.tarck_num) tarck_num,
        any_value(c.wechat_tag) wechat_tag,
        any_value(c.create_by) create_by,
        any_value(c.create_time) create_time,
        sum( o.order_num ) order_num,
        sum( o.amount ) amount
        <if test="brandId != null">
            ,c.brand_id
            <if test="franchiseId == null">
                ,any_value(c.franchise_id) franchise_id
            </if>
        </if>
        <if test="franchiseId != null">
            ,c.franchise_id
            <if test="brandId == null">
                ,any_value(c.brand_id) brand_id
            </if>
        </if>
        FROM
        incom_customer c
        left join  (
            select
                taobao_id,
                brand_id,
                franchise_id,
                count(1) order_num,
                sum( order_amount ) amount
            from xgwc_order
            where
                is_del = 0
                and pid = 0
                <if test="brandId != null">
                    and brand_id = #{brandId}
                </if>
                <if test="franchiseId != null">
                    and franchise_id = #{franchiseId}
                </if>
            group by taobao_id,brand_id,franchise_id
        )  o ON c.taobao_id = o.taobao_id and c.brand_id = o.brand_id and c.franchise_id = o.franchise_id
        <where>
            <if test="taobaoId != null  and taobaoId != ''"> and c.taobao_id like concat('%', #{taobaoId}, '%')</if>
            <if test="taobaoLv != null  and taobaoLv != ''"> and c.taobao_lv = #{taobaoLv}</if>
            <if test="tel != null  and tel != ''"> and c.tel = #{tel}</if>
            <if test="addTime != null "> and c.last_time between #{addTime}[0] and #{addTime}[1] </if>
            <if test="wechatTag != null "> and c.wechat_tag = #{wechatTag}</if>
            <if test="brandId != null">
                and c.brand_id = #{brandId}
            </if>
            <if test="franchiseId != null">
                and c.franchise_id = #{franchiseId}
            </if>
        </where>
        group by c.taobao_id
        <if test="brandId != null">
            ,c.brand_id
        </if>
        <if test="franchiseId != null">
            ,c.franchise_id
        </if>
    </sql>

    <select id="selectIncomCustomerList" parameterType="com.xgwc.order.entity.vo.IncomCustomerQueryVo" resultType="com.xgwc.order.entity.dto.IncomCustomerDto">
        select
            c.*,
        ifnull(bo.company_simple_name,bo.company_name) brand_name,
        ifnull(fo.company_simple_name,fo.company_name) franchise_name
        from ( <include refid="selectIncomCustomerVo"/> ) c
        left join franchise_owner fo on c.franchise_id = fo.franchise_id and c.brand_id = fo.brand_id
        left join xgwc_brand_owner bo on c.brand_id = bo.brand_id
    </select>

    <select id="selectIncomCustomerStatisList" parameterType="com.xgwc.order.entity.vo.IncomCustomerQueryVo" resultType="com.xgwc.order.entity.dto.IncomCustomerStatisDto">
        select
            model,
            GROUP_CONCAT(concat(type_name,'(',type_num,')')) type_name
        from incom_customer_statis
        where taobao_id = #{taobaoId}
        <if test="brandId != null">
            and brand_id = #{brandId}
        </if>
        <if test="franchiseId != null">
            and franchise_id = #{franchiseId}
        </if>
        group by model
    </select>

    
    <select id="selectIncomCustomerOne" parameterType="com.xgwc.order.entity.vo.IncomCustomerQueryVo" resultType="com.xgwc.order.entity.dto.IncomCustomerBasicDto">
        SELECT
            c.taobao_id,
            c.taobao_lv,
            c.tel,
            c.last_time,
            c.track_name,
            c.line_num,
            c.tarck_num,
            c.wechat_tag,
            c.sex,
            o.order_num,
            o.amount,
            o.source,
            o.sale_name,
            c.brand_id,
            c.region,
            c.address,
            bo.company_simple_name brand_name,
            c.franchise_id,
            fo.company_simple_name franchise_name,
            TRUNCATE(o.order_num/c.line_num*100,2) succ_rate
        FROM
        (select
                taobao_id,
                any_value(brand_id) brand_id,
                any_value(franchise_id) franchise_id,
                any_value(taobao_lv) taobao_lv,
                any_value(tel) tel,
                any_value(last_time) last_time,
                any_value(track_name) track_name,
                any_value(region) region,
                any_value(address) address,
                sum(line_num) line_num,
                sum(tarck_num) tarck_num,
                any_value(wechat_tag) wechat_tag,
                any_value(sex) sex
            from incom_customer
            <where>
                <if test="brandId != null">
                    and brand_id = #{brandId}
                </if>
                <if test="franchiseId != null">
                    and franchise_id = #{franchiseId}
                </if>
            </where>
            GROUP BY taobao_id
        ) c
        LEFT JOIN (
            SELECT
                o.taobao_id,
                sum( o.order_amount ) amount,
                count( 1 ) order_num,
                GROUP_CONCAT(DISTINCT concat(o.dept_name,'-',o.sale_man_name))  sale_name,
                GROUP_CONCAT(DISTINCT xc.channel_name)  source
            FROM
                xgwc_order o
            left join xgwc_shop s on o.store_id = s.shop_id
            left join xgwc_channel xc on s.channel_id = xc.channel_id
            WHERE
                o.is_del = 0
                and o.pid = 0
                <if test="brandId != null">
                    and o.brand_id = #{brandId}
                </if>
                <if test="franchiseId != null">
                    and o.franchise_id = #{franchiseId}
                </if>
            GROUP BY o.taobao_id
        ) o ON c.taobao_id = o.taobao_id
        left join xgwc_brand_owner bo on c.brand_id = bo.brand_id
        left join franchise_owner fo on c.franchise_id = fo.id and c.brand_id = fo.brand_id
        where c.taobao_id = #{taobaoId}
        limit 1
    </select>

    <select id="selectIncomCustomerByTaobaoId" parameterType="String" resultType="com.xgwc.order.entity.dto.IncomCustomerDto">
        SELECT
            c.id,
            c.taobao_id,
            c.taobao_lv,
            c.tel,
            c.last_time,
            c.track_name,
            c.line_num,
            c.tarck_num,
            c.wechat_tag
        FROM
            incom_customer c
        where c.taobao_id = #{taobaoId}
        <if test="franchiseId != null">
            and c.franchise_id = #{franchiseId}
        </if>
        <if test="brandId != null" >
            and c.brand_id = #{brandId}
        </if>
    </select>

    <insert id="insertIncomCustomer" parameterType="com.xgwc.order.entity.IncomCustomer">
        insert into incom_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taobaoId != null">taobao_id,</if>
            <if test="taobaoLv != null">taobao_lv,</if>
            <if test="tel != null">tel,</if>
            <if test="lastTime != null">last_time,</if>
            <if test="trackName != null">track_name,</if>
            <if test="lineNum != null">line_num,</if>
            <if test="tarckNum != null">tarck_num,</if>
            <if test="wechatTag != null">wechat_tag,</if>
            <if test="sex != null">sex,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="region != null">region,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taobaoId != null">#{taobaoId},</if>
            <if test="taobaoLv != null">#{taobaoLv},</if>
            <if test="tel != null">#{tel},</if>
            <if test="lastTime != null">#{lastTime},</if>
            <if test="trackName != null">#{trackName},</if>
            <if test="lineNum != null">#{lineNum},</if>
            <if test="tarckNum != null">#{tarckNum},</if>
            <if test="wechatTag != null">#{wechatTag},</if>
            <if test="sex != null">#{sex},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="region != null">#{region},</if>
         </trim>
    </insert>

    <update id="updateIncomCustomer" parameterType="com.xgwc.order.entity.IncomCustomer">
        update incom_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="taobaoLv != null">taobao_lv = #{taobaoLv},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="lastTime != null">last_time = #{lastTime},</if>
            <if test="trackName != null">track_name = #{trackName},</if>
            <if test="lineNum != null">line_num = line_num + 1,</if>
            <if test="tarckNum != null">tarck_num = tarck_num + 1,</if>
            <if test="wechatTag != null">wechat_tag = #{wechatTag},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="region != null">region = #{region},</if>
        </trim>
        where taobao_id = #{taobaoId}
        <if test="franchiseId != null">
            and franchise_id = #{franchiseId}
        </if>
        <if test="brandId != null">
            and brand_id = #{brandId}
        </if>
    </update>

</mapper>