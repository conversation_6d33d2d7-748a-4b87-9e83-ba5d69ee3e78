<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderPlanMapper">
    

    <sql id="selectOrderPlanVo">
        select id, oder_id, archive_type, archive_time, create_by, create_time, update_by, update_time, is_del from xgwc_order_plan
    </sql>

    <select id="selectOrderPlanByOrderId" parameterType="Long" resultType="com.xgwc.order.entity.dto.OrderPlanDto">
        <include refid="selectOrderPlanVo"/>
        where oder_id = #{oderId}
        and is_del = 0
        order by archive_time asc
    </select>

    <insert id="insertOrderPlan" parameterType="com.xgwc.order.entity.OrderPlan">
        insert into xgwc_order_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oderId != null">oder_id,</if>
            <if test="archiveType != null">archive_type,</if>
            <if test="archiveTime != null">archive_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oderId != null">#{oderId},</if>
            <if test="archiveType != null">#{archiveType},</if>
            <if test="archiveTime != null">#{archiveTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateOrderPlan" parameterType="com.xgwc.order.entity.OrderPlan">
        update xgwc_order_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="oderId != null">oder_id = #{oderId},</if>
            <if test="archiveType != null">archive_type = #{archiveType},</if>
            <if test="archiveTime != null">archive_time = #{archiveTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="delectOrderPlanByOrderId">
        delete from xgwc_order_plan where oder_id = #{orderId}
    </delete>

</mapper>