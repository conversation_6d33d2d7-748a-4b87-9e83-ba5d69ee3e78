<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcPlatformRefundDetailsMapper">
    

    <sql id="selectXgwcPlatformRefundDetailsVo">
        select id, trade_no, oid, refund_no, goods_id, goods_spec_id, refund_num, refund_order_amount, brand_id, create_time, update_time from xgwc_platform_refund_details
    </sql>

    <select id="listByRefundNo" resultType="com.xgwc.order.entity.XgwcPlatformRefundDetails">
        <include refid="selectXgwcPlatformRefundDetailsVo"/>
        where refund_no = #{refundNo} and brand_id = #{brandOwnerId}
    </select>

    <insert id="insertList" parameterType="com.xgwc.order.entity.XgwcPlatformRefundDetails">
        insert into xgwc_platform_refund_details (trade_no,oid,refund_no,goods_id,goods_spec_id,refund_num,refund_order_amount,brand_id,create_time,update_time)
        values
        <foreach item="item" collection="xgwcPlatformRefundDetailsList" separator=",">
            (#{item.tradeNo},#{item.oid},#{item.refundNo},#{item.goodsId},#{item.goodsSpecId},#{item.refundNum},#{item.refundOrderAmount},#{item.brandId},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <delete id="deleteByRefundNo">
        delete
        from xgwc_platform_refund_details
        where brand_id = #{brandOwnerId} and refund_no in
        <foreach item="refundNo" collection="refundNoList" open="(" separator="," close=")">
            #{refundNo}
        </foreach>
    </delete>

</mapper>