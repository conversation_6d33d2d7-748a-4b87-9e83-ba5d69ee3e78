<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderInvoiceApplyMapper">

    <sql id="selectOrderInvoiceApplyVo">
        select id, order_no, invoicing_company, franchise_id, title_type, invoice_type, buyer, buyer_tax, register_address, register_mobile, bank,
               corporate_account, invoice_amount, actual_amount, customer_no, remark, special_invoice_fee, courier_fee, attachment, receive_user,
               receive_mobile, receive_pro_code, receive_city_code, receive_zone_code, receive_pro_name, receive_city_name, receive_zone_name,
               details_address, apply_user_id, apply_user_name, status, brand_id, apply_status, last_approve_time, create_time, update_time
        from order_invoice_apply
    </sql>
    <insert id="insertOrderInvoiceApply">
        INSERT INTO order_invoice_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="invoicingCompany != null">invoicing_company,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="titleType != null">title_type,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="buyer != null">buyer,</if>
            <if test="buyerTax != null">buyer_tax,</if>
            <if test="registerAddress != null">register_address,</if>
            <if test="registerMobile != null">register_mobile,</if>
            <if test="bank != null">bank,</if>
            <if test="corporateAccount != null">corporate_account,</if>
            <if test="invoiceAmount != null">invoice_amount,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="customerNo != null">customer_no,</if>
            <if test="remark != null">remark,</if>
            <if test="specialInvoiceFee != null">special_invoice_fee,</if>
            <if test="courierFee != null">courier_fee,</if>
            <if test="attachment != null">attachment,</if>
            <if test="receiveUser != null">receive_user,</if>
            <if test="receiveMobile != null">receive_mobile,</if>
            <if test="receiveProCode != null">receive_pro_code,</if>
            <if test="receiveCityCode != null">receive_city_code,</if>
            <if test="receiveZoneCode != null">receive_zone_code,</if>
            <if test="receiveProName != null">receive_pro_name,</if>
            <if test="receiveCityName != null">receive_city_name,</if>
            <if test="receiveZoneName != null">receive_zone_name,</if>
            <if test="detailsAddress != null">details_address,</if>
            <if test="applyUserId != null">apply_user_id,</if>
            <if test="applyUserName != null">apply_user_name,</if>
            status,
            <if test="brandId != null">brand_id,</if>
            <if test="applyStatus != null">apply_status,</if>
            last_approve_time,
            create_time,
            update_time,
            <if test="executionId != null">execution_id,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="invoicingCompany != null">#{invoicingCompany},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="titleType != null">#{titleType},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="buyer != null">#{buyer},</if>
            <if test="buyerTax != null">#{buyerTax},</if>
            <if test="registerAddress != null">#{registerAddress},</if>
            <if test="registerMobile != null">#{registerMobile},</if>
            <if test="bank != null">#{bank},</if>
            <if test="corporateAccount != null">#{corporateAccount},</if>
            <if test="invoiceAmount != null">#{invoiceAmount},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="customerNo != null">#{customerNo},</if>
            <if test="remark != null">#{remark},</if>
            <if test="specialInvoiceFee != null">#{specialInvoiceFee},</if>
            <if test="courierFee != null">#{courierFee},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="receiveUser != null">#{receiveUser},</if>
            <if test="receiveMobile != null">#{receiveMobile},</if>
            <if test="receiveProCode != null">#{receiveProCode},</if>
            <if test="receiveCityCode != null">#{receiveCityCode},</if>
            <if test="receiveZoneCode != null">#{receiveZoneCode},</if>
            <if test="receiveProName != null">#{receiveProName},</if>
            <if test="receiveCityName != null">#{receiveCityName},</if>
            <if test="receiveZoneName != null">#{receiveZoneName},</if>
            <if test="detailsAddress != null">#{detailsAddress},</if>
            <if test="applyUserId != null">#{applyUserId},</if>
            <if test="applyUserName != null">#{applyUserName},</if>
            0,
            <if test="brandId != null">#{brandId},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            null,
            now(),
            now(),
            <if test="executionId != null">#{executionId},</if>
        </trim>
    </insert>


    <update id="updateOrderInvoiceApply">
        update order_invoice_apply
        <set>
            update_time = now()
            <if test="lastApproveTime != null">
                ,last_approve_time = #{lastApproveTime}
            </if>
            <if test="status != null">
                ,status = #{status}
            </if>
            <if test="applyStatus != null">
                ,apply_status = #{applyStatus}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectOrderInvoiceApplyById" resultType="com.xgwc.order.entity.dto.OrderInvoiceApplyDto">
        SELECT
            t.id,
            t.order_no,
            t.invoicing_company,
            t.franchise_id,
            t.title_type,
            t.invoice_type,
            t.buyer,
            t.buyer_tax,
            t.register_address,
            t.register_mobile,
            t.bank,
            t.corporate_account,
            t.invoice_amount,
            t.actual_amount,
            t.customer_no,
            t.remark,
            t.special_invoice_fee,
            t.courier_fee,
            t.attachment,
            t.receive_user,
            t.receive_mobile,
            t.receive_pro_code,
            t.receive_city_code,
            t.receive_zone_code,
            t.receive_pro_name,
            t.receive_city_name,
            t.receive_zone_name,
            t.details_address,
            t.apply_user_id,
            t.apply_user_name,
            t.STATUS,
            t.brand_id,
            t.apply_status,
            t.last_approve_time,
            t.create_time,
            t.update_time,
            o.order_date,
            o.sale_man_name,
            o.order_amount,
            o.amount
        FROM
            order_invoice_apply t
                left join xgwc_order o on t.order_no = o.order_no
        where t.id = #{id} and t.status = 0
    </select>

    <select id="getPassByOrderNo" resultType="com.xgwc.order.entity.OrderInvoiceApply">
        <include refid="selectOrderInvoiceApplyVo"/>
        where order_no = #{orderNo} and apply_status = 'ING'
        limit 1
    </select>

    <select id="listPassByOrderNo" resultType="com.xgwc.order.entity.OrderInvoiceApply">
        <include refid="selectOrderInvoiceApplyVo"/>
        where order_no = #{orderNo} and apply_status = 'PASS'
    </select>

    <select id="selectAllOrderInvoiceApply" parameterType="com.xgwc.order.entity.vo.OrderInvoiceQueryVo" resultType="com.xgwc.order.entity.dto.OrderInvoiceDto">
        SELECT t.id,t.invoicing_company,t.title_type,t.invoice_type,t.invoice_amount,t.buyer,
               xo.order_date,t.order_no,xo.taobao_id,f.franchise_name,xo.sale_man_name,
               t.create_time,t.last_approve_time,
               xft.task_id_, t.execution_id, t.apply_status
        FROM order_invoice_apply t
        LEFT JOIN xgwc_order xo ON t.order_no = xo.order_no
        LEFT JOIN xgwc_flow_execution xfe on t.execution_id = xfe.id
        LEFT JOIN xgwc_flow_task xft on t.execution_id = xft.execution_id_
        LEFT JOIN franchise f on t.franchise_id = f.id
        WHERE xfe.flow_value = 'invoice'
        <if test="franchiseId != null">and t.franchise_id = #{franchiseId}</if>
        <if test="applyStatus != null and applyStatus != ''">and t.apply_status = #{applyStatus}</if>
        <if test="brandIdList != null and brandIdList.size() > 0">
            and t.brand_id in
            <foreach item="item" collection="brandIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword != ''">
            and (
            t.order_no like concat('%', #{keyword}, '%')
            or xo.taobao_id like concat('%', #{keyword}, '%')
            or t.id like concat('%', #{keyword}, '%')
            or xo.sale_man_name like concat('%', #{keyword}, '%')
            or t.apply_user_name like concat('%', #{keyword}, '%')
            or t.buyer like concat('%', #{keyword}, '%')
            )
        </if>
        <if test="startTime != null and endTime != null">
            and t.create_time BETWEEN CONCAT(#{startTime}, ' 00:00:00') AND CONCAT(#{endTime}, ' 23:59:59')
        </if>
        ORDER BY
            CASE t.apply_status
            WHEN 'ING' THEN 1
            WHEN 'PASS' THEN 2
            WHEN 'REJECT' THEN 3
            END,t.create_time
        desc
    </select>

    <select id="countApplyByOrderNo" resultType="java.lang.Double">
        SELECT SUM(invoice_amount)
        from order_invoice_apply
        where order_no = #{orderNo}
        and apply_status = 'PASS'
    </select>

    <select id="countApplyInvoiceByOrderNo" resultType="java.lang.Double">
        SELECT SUM(invoice_amount)
        from order_invoice_apply
        where order_no = #{orderNo}
          and apply_status in ('PASS', 'ING')
    </select>


</mapper>