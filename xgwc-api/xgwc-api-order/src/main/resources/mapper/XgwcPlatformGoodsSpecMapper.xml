<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcPlatformGoodsSpecMapper">
    

    <sql id="selectXgwcPlatformGoodsSpecVo">
        select id, goods_id, spec_id, spec_name, brand_id from xgwc_platform_goods_spec
    </sql>

    <insert id="insertList" parameterType="com.xgwc.order.entity.XgwcPlatformGoodsSpec">
        insert into xgwc_platform_goods_spec (goods_id,spec_id,spec_name,brand_id)
        values
        <foreach item="item" collection="xgwcPlatformGoodsSpecList" separator=",">
            (#{item.goodsId},#{item.specId},#{item.specName},#{item.brandId})
        </foreach>
    </insert>

    <delete id="deleteByGoodsId">
        delete
        from xgwc_platform_goods_spec
        where brand_id = #{brandOwnerId} and goods_id in
        <foreach item="goodsId" collection="goodsIdList" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
    </delete>
    <select id="listByGoodsSpecId" resultType="com.xgwc.order.entity.XgwcPlatformGoodsSpec">
        <include refid="selectXgwcPlatformGoodsSpecVo"/>
        where brand_id = #{brandOwnerId} and spec_id in
        <foreach item="goodsSpecId" collection="goodsSpecIdList" open="(" separator="," close=")">
            #{goodsSpecId}
        </foreach>
    </select>

</mapper>