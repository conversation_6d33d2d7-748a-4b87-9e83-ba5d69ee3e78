<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.BrandNightPerformanceConfigMapper">
    

    <sql id="selectBrandNightPerformanceConfigVo">
        select id, brand_id, franchisee_id, department_id, night_time_json, create_by_id, create_time, update_by_id, update_time from brand_night_performance_config
    </sql>

    <select id="listByQuery" parameterType="com.xgwc.order.entity.vo.BrandNightPerformanceConfigQueryVo" resultType="com.xgwc.order.entity.BrandNightPerformanceConfig">
        <include refid="selectBrandNightPerformanceConfigVo"/>
        <where>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="franchiseeId != null "> and franchisee_id = #{franchiseeId}</if>
            <if test="departmentId != null "> and department_id = #{departmentId}</if>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND department_id IN
                <foreach item="id" collection="departmentIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    
    <select id="getByIdBrandId" parameterType="Long" resultType="com.xgwc.order.entity.BrandNightPerformanceConfig">
        <include refid="selectBrandNightPerformanceConfigVo"/>
        where id = #{id} and brand_id = #{brandId}
    </select>

    <insert id="insert" parameterType="com.xgwc.order.entity.BrandNightPerformanceConfig">
        insert into brand_night_performance_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="franchiseeId != null">franchisee_id,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="nightTimeJson != null">night_time_json,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateById != null">update_by_id,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="franchiseeId != null">#{franchiseeId},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="nightTimeJson != null">#{nightTimeJson},</if>
            <if test="createById != null">#{createById},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateById != null">#{updateById},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="update" parameterType="com.xgwc.order.entity.BrandNightPerformanceConfig">
        update brand_night_performance_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="franchiseeId != null">franchisee_id = #{franchiseeId},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="nightTimeJson != null">night_time_json = #{nightTimeJson},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateById != null">update_by_id = #{updateById},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="remove">
        delete from brand_night_performance_config where  brand_id = #{brandId}
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>