<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.CommonMapper">

    <select id="listFranchiseDeptByIds" resultType="com.xgwc.order.entity.dto.DeptDto">
        select dept_id, dept_name, pid from franchise_dept
        where is_del = 0 and status = 0
        <if test="idList != null and idList.size() > 0">
            and dept_id in
            <foreach item="id" collection="idList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="franchiseeId != null">
            and franchise_id = #{franchiseeId}
        </if>
        <if test="name != null and name != ''">
            and dept_name like concat('%', #{name}, '%')
        </if>
    </select>

    <select id="listFranchiseStaffByIds" resultType="com.xgwc.order.entity.dto.StaffDto">
        select bind_user_id, name, stage_name, dept_id  from xgwc_franchise_staff
        where is_del = 0
        <if test="idList != null and idList.size() > 0">
            and bind_user_id in
            <foreach item="id" collection="idList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="franchiseeId != null">
            and franchise_id = #{franchiseeId}
        </if>
        <if test="name != null and name != ''">
            and stage_name like concat('%', #{name}, '%')
        </if>
    </select>
    <select id="listBrandStaffByIds" resultType="com.xgwc.order.entity.dto.StaffDto">
        select bind_user_id, name, stage_name from xgwc_staff where bind_user_id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>