<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcChannelMapper">

    <insert id="saveXgwcChannel">
        INSERT INTO `xgwc_sass`.`xgwc_channel` (`channel_id`,  `brand_owner_id`, `channel_name`, `sort`, `create_by`,
                                                `create_time`)
        VALUES (#{xgwcChannelDto.channelId}, #{xgwcChannelDto.brandOwnerId},#{xgwcChannelDto.channelName}, #{xgwcChannelDto.sort},
                #{xgwcChannelDto.createBy}, now());

    </insert>


    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`xgwc_channel`
        SET `status` = #{status}
        WHERE `channel_id` = #{channelId}
    </update>

    <update id="updateXgwcChannelById">
        UPDATE `xgwc_sass`.`xgwc_channel`
        <set>
            <if test="xgwcChannelDto.channelName != null and xgwcChannelDto.channelName != ''">
                `channel_name` = #{xgwcChannelDto.channelName}
            </if>
            <if test="xgwcChannelDto.sort != null">
                , `sort` = #{xgwcChannelDto.sort}
            </if>
            <if test="xgwcChannelDto.updateBy != null and xgwcChannelDto.updateBy != ''">
                , `update_by` = #{xgwcChannelDto.updateBy}
            </if>
            ,`update_time` = NOW()
        </set>
        WHERE channel_id = #{xgwcChannelDto.channelId}
    </update>

    <select id="getXgwcChannelList" resultType="com.xgwc.order.entity.vo.XgwcChannelVo">
        select
            channel_id as channelId,
            channel_name as channelName,
            sort,
            status,
            create_time as createTime
        from xgwc_channel
        <where>
            brand_owner_id = #{xgwcChannelParam.brandOwnerId}

            <if test="xgwcChannelParam.channelName != null and xgwcChannelParam.channelName != ''">
                AND channel_name like concat('%', #{xgwcChannelParam.channelName} , '%')
            </if>
            <if test="xgwcChannelParam.status != null">
                AND status = #{xgwcChannelParam.status}
            </if>
        </where>
        order by sort
    </select>

    <select id="getXgwcChannelById" resultType="com.xgwc.order.entity.vo.XgwcChannelVo">
        select
            channel_id as channelId,
            channel_name as channelName,
            sort
        from xgwc_channel
        where channel_id = #{channelId}
    </select>
</mapper>