<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcPlatformTradeDetailsMapper">
    

    <sql id="selectXgwcPlatformTradeDetailsVo">
        select id, trade_no, oid, goods_id, goods_spec_id, num, share_amount,refund_status, status, brand_id, create_time, update_time from xgwc_platform_trade_details
    </sql>

    <select id="selectList" parameterType="com.xgwc.order.entity.vo.XgwcPlatformTradeDetailsQueryVo" resultType="com.xgwc.order.entity.XgwcPlatformTradeDetails">
        <include refid="selectXgwcPlatformTradeDetailsVo"/>
        where brand_id = #{brandId}
            <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
            <if test="oidList != null  and oidList.size() > 0">
                and oid in
                <foreach item="oid" collection="oidList" open="(" separator="," close=")">
                    #{oid}
                </foreach>
            </if>
    </select>
    
    <insert id="insertList" parameterType="com.xgwc.order.entity.XgwcPlatformTradeDetails">
        insert into xgwc_platform_trade_details (trade_no,oid,goods_id,goods_spec_id,num,share_amount,refund_status, status,brand_id,create_time,update_time)
        values
        <foreach item="item" collection="xgwcPlatformTradeDetailsList" separator=",">
            (#{item.tradeNo},#{item.oid},#{item.goodsId},#{item.goodsSpecId},#{item.num},#{item.shareAmount},#{item.refundStatus},#{item.status},#{item.brandId},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <delete id="deleteByTradeNo">
        delete
        from xgwc_platform_trade_details
        where brand_id = #{brandOwnerId} and trade_no in
        <foreach item="tradeNo" collection="tradeNoList" open="(" separator="," close=")">
            #{tradeNo}
        </foreach>
    </delete>
</mapper>