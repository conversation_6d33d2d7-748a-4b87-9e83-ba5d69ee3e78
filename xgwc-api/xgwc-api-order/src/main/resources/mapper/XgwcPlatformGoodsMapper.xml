<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcPlatformGoodsMapper">


    <sql id="selectXgwcPlatformGoodsVo">
        select id,
               goods_id,
               goods_name,
               platform_id,
               platform_shop_id,
               brand_id,
               biz_type,
               status,
               is_del,
               create_by,
               create_time,
               update_by,
               update_time,
               modify_time
        from xgwc_platform_goods
    </sql>

    <select id="selectXgwcPlatformGoodsList" parameterType="com.xgwc.order.entity.vo.XgwcPlatformGoodsQueryVo"
            resultType="com.xgwc.order.entity.dto.XgwcPlatformGoodsDto">
        <include refid="selectXgwcPlatformGoodsVo"/>
        where brand_id = #{brandId} and is_del = 0
            <if test="id != null ">and id = #{id}</if>
            <if test="goodsId != null  and goodsId != ''">and goods_id = #{goodsId}</if>
            <if test="goodsName != null  and goodsName != ''">and goods_name like concat('%', #{goodsName}, '%')</if>
            <if test="platformId != null ">and platform_id = #{platformId}</if>
            <if test="platformShopId != null  and platformShopId != ''">and platform_shop_id = #{platformShopId}</if>
            <if test="keyword != null  and keyword != ''">
                and (
                    goods_name like concat('%', #{keyword}, '%')
                    or goods_id like concat('%', #{keyword}, '%')
                    or platform_shop_id like concat('%', #{keyword}, '%')
                    <if test="keywordPlatformShopIds != null and keywordPlatformShopIds.size() > 0">
                        or platform_shop_id in
                        <foreach item="keywordPlatformShopId" collection="keywordPlatformShopIds" open="(" separator=","
                                 close=")">
                            #{keywordPlatformShopId}
                        </foreach>
                    </if>
                )
            </if>
            <if test="hasShop == '1'.toString">and platform_shop_id in (select distinct x.platform_shop_id from xgwc_shop x where x.brand_owner_id = #{brandId} and x.platform_shop_id is not null)</if>
            <if test="hasShop == '0'.toString">and platform_shop_id not in (select distinct x.platform_shop_id from xgwc_shop x where x.brand_owner_id = #{brandId} and x.platform_shop_id is not null)</if>
            <if test="hasType == '1'.toString">and biz_type is not null</if>
            <if test="hasType == '0'.toString">and biz_type is null</if>
            <if test="bizType != null ">and biz_type = #{bizType}</if>
            <if test="status != null ">and status = #{status}</if>
    </select>

    <select id="getByGoodsById" resultType="com.xgwc.order.entity.dto.XgwcPlatformGoodsDto">
        <include refid="selectXgwcPlatformGoodsVo"/>
        where goods_id = #{goodsId} and brand_id = #{brandId}
    </select>

    <select id="listByGoodsIds" parameterType="Long" resultType="com.xgwc.order.entity.XgwcPlatformGoods">
        <include refid="selectXgwcPlatformGoodsVo"/>
        where brand_id = #{brandId} and goods_id in
        <foreach item="goodsId" collection="goodsIdList" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
    </select>

    <delete id="deleteByIds">
        delete
        from xgwc_platform_goods
        where id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertList" parameterType="com.xgwc.order.entity.XgwcPlatformGoods">
        insert into xgwc_platform_goods (goods_id,goods_name,platform_id,platform_shop_id,brand_id,biz_type,status,is_del,create_by,create_time,update_by,update_time,modify_time)
        values
        <foreach item="item" collection="xgwcPlatformGoodsList" separator=",">
            (#{item.goodsId},#{item.goodsName},#{item.platformId},#{item.platformShopId},#{item.brandId},#{item.bizType},#{item.status},#{item.isDel},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.modifyTime})
        </foreach>
    </insert>

    <update id="updateXgwcPlatformGoods" parameterType="com.xgwc.order.entity.XgwcPlatformGoods">
        update xgwc_platform_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="goodsName != null">goods_name = #{goodsName},</if>
            <if test="platformId != null">platform_id = #{platformId},</if>
            <if test="platformShopId != null">platform_shop_id = #{platformShopId},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>