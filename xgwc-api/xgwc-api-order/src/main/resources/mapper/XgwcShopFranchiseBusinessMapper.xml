<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcShopFranchiseBusinessMapper">
    

    <sql id="selectXgwcShopBusinessVo">
        select
            sb.id,
            sb.business_id,
            b.business_name,
            sb.franchise_dept_id dept_id,
            d.dept_name,
            sb.shop_id,
            s.shop_name shop_franchise_name,
            s.pay_type
        from xgwc_shop_business sb
         join xgwc_business b on sb.business_id = b.business_id
         join xgwc_shop s ON sb.shop_id = s.shop_id
         left join franchise_dept d on sb.franchise_dept_id = d.dept_id
    </sql>


    <select id="selectListByShopId" resultType="com.xgwc.order.feign.entity.XgwcShopFranchiseBusinessDto">
        <include refid="selectXgwcShopBusinessVo"/>
        where sb.shop_id = #{shopId}
    </select>

    <select id="selectListByShopIds" resultType="com.xgwc.order.feign.entity.XgwcShopFranchiseBusinessDto">
        <include refid="selectXgwcShopBusinessVo"/>
        where sb.shop_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="shopFranchiseBusiness" resultType="com.xgwc.order.feign.entity.XgwcShopFranchiseBusinessDto">
        <include refid="selectXgwcShopBusinessVo"/>
        <where>
            <if test="userId != null ">
                and EXISTS (
                select 1 from xgwc_franchise_staff fs where  sb.franchise_dept_id = fs.dept_id and fs.bind_user_id = #{userId}
                )
            </if>
            <if test="franchiseId != null "> and s.franchise_id = #{franchiseId}</if>
            <if test="brandId != null "> and s.brand_owner_id = #{brandId}</if>
        </where>
    </select>
    <update id="updateXgwcShopFranchiseBusiness" parameterType="com.xgwc.order.entity.XgwcShopFranchiseBusiness">
        update xgwc_shop_business
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="deptId != null">franchise_dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>