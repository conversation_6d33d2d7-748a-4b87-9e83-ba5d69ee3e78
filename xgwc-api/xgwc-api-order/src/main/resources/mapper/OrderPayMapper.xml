<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderPayMapper">
    

    <sql id="selectOrderPayVo">
        select id, oder_id, pay_type, pay_img, amount, ifnull(now_amount, amount) nowAmount, pay_channel, remark, collection_no, create_by, create_time, update_by, update_time from xgwc_order_pay
    </sql>

    <select id="selectOrderPayList" parameterType="com.xgwc.order.entity.vo.OrderPayQueryVo" resultType="com.xgwc.order.entity.dto.OrderPayDto">
        <include refid="selectOrderPayVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="oderId != null "> and oder_id = #{oderId}</if>
            <if test="payType != null "> and pay_type = #{payType}</if>
            <if test="payImg != null  and payImg != ''"> and pay_img like concat('%', #{payImg}, '%')</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="payChannel != null "> and pay_channel = #{payChannel}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="collectionNo != null  and collectionNo != ''"> and collection_no like concat('%', #{collectionNo}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by like concat('%', #{createBy}, '%')</if>
            <if test="createTime != null "> and create_time between #{createTime}[0] and #{createTime}[1] </if>
            <if test="updateBy != null  and updateBy != ''"> and update_by like concat('%', #{updateBy}, '%')</if>
            <if test="updateTime != null "> and update_time between #{updateTime}[0] and #{updateTime}[1] </if>
        </where>
        order by pay_type
    </select>
    
    <select id="selectOrderPayById" parameterType="Long" resultType="com.xgwc.order.entity.dto.OrderPayDto">
        <include refid="selectOrderPayVo"/>
        where id = #{id}
    </select>

    <select id="selectOrderPayByOrderId" parameterType="Long" resultType="com.xgwc.order.entity.dto.OrderPayDto">
        <include refid="selectOrderPayVo"/>
        where oder_id = #{orderId}
        order by pay_type
    </select>
    <select id="listByCollectionNo" resultType="com.xgwc.order.entity.OrderPay">
        select xop.id, xop.oder_id, xop.pay_type, xop.pay_img, xop.amount, ifnull(xop.now_amount, xop.amount) nowAmount, xop.pay_channel, xop.remark, xop.collection_no, xop.create_by, xop.create_time, xop.update_by, xop.update_time
        from xgwc_order xo left join xgwc_order_pay xop on xo.id = xop.oder_id
        where xo.is_del = 0 and xop.is_del = 0 and xo.brand_id in
        <foreach item="brandOwnerId" collection="brandOwnerIdList" open="(" separator="," close=")">
            #{brandOwnerId}
        </foreach>
        and xop.collection_no in
        <foreach item="collectionNo" collection="collectionNoList" open="(" separator="," close=")">
            #{collectionNo}
        </foreach>
    </select>

    <insert id="insertOrderPay" parameterType="com.xgwc.order.entity.OrderPay">
        insert into xgwc_order_pay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oderId != null">oder_id,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payImg != null">pay_img,</if>
            <if test="amount != null">amount,</if>
            <if test="payChannel != null">pay_channel,</if>
            <if test="remark != null">remark,</if>
            <if test="collectionNo != null">collection_no,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oderId != null">#{oderId},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payImg != null">#{payImg},</if>
            <if test="amount != null">#{amount},</if>
            <if test="payChannel != null">#{payChannel},</if>
            <if test="remark != null">#{remark},</if>
            <if test="collectionNo != null">#{collectionNo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertOrderPays" parameterType="com.xgwc.order.entity.OrderPay">
        insert into xgwc_order_pay
        (oder_id,pay_type,pay_img,amount,pay_channel,remark,collection_no,create_by,create_time)
        values
        <foreach item="item" collection="list" separator=",">
            (#{item.oderId},#{item.payType},#{item.payImg},#{item.amount},#{item.payChannel},#{item.remark},#{item.collectionNo},#{item.createBy},now())
        </foreach>
    </insert>

    <update id="updateOrderPay" parameterType="com.xgwc.order.entity.OrderPay">
        update xgwc_order_pay
        <trim prefix="SET" suffixOverrides=",">
            <if test="oderId != null">oder_id = #{oderId},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payImg != null">pay_img = #{payImg},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="nowAmount != null">now_amount = #{nowAmount},</if>
            <if test="payChannel != null">pay_channel = #{payChannel},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="collectionNo != null">collection_no = #{collectionNo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteOrderPayById" parameterType="Long">
        update xgwc_order_pay set is_del = 1 where id = #{id}
    </update>

    <update id="deleteOrderPayByIds" parameterType="String">
        update xgwc_order_pay set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <delete id="deleteFinanceOfflinePaymentsByOrderId">
        delete from finance_offline_payments
        where order_id = #{orderId}
    </delete>
    <delete id="deleteOrderPayByOrderIdType">
        delete from xgwc_order_pay where oder_id = #{orderId}
        <if test="payType != null and payType == 0">
            and pay_type = 1
        </if>
        <if test="payType != null and payType == 1">
            and pay_type > 1
        </if>
    </delete>
</mapper>