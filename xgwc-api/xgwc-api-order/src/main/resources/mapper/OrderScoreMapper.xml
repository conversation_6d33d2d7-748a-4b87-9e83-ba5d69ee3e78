<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderScoreMapper">
    

    <sql id="selectOrderScoreVo">
        select id, oder_id, archive_id, designer_id, score_model, score_num,create_by, score_item,type from xgwc_order_score
    </sql>

    <select id="selectOrderScoreByArchiveId" parameterType="Long" resultType="com.xgwc.order.entity.dto.OrderScoreDto">
        <include refid="selectOrderScoreVo"/>
        where archive_id = #{archiveId}
        order by score_model
    </select>

    <select id="selectOrderScoreByOrderIdDesignerId" parameterType="Long" resultType="com.xgwc.order.entity.dto.OrderScoreDto">
        <include refid="selectOrderScoreVo"/>
        where oder_id = #{oderId} and designer_id = #{designerId}
        order by score_model
    </select>
    <select id="selectOrderScoreByOrderIds"  resultType="com.xgwc.order.entity.dto.OrderScoreDto">
        <include refid="selectOrderScoreVo"/>
        where oder_id in
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        order by score_model
    </select>

    <insert id="insertOrderScore" parameterType="com.xgwc.order.entity.OrderScore">
        insert into xgwc_order_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oderId != null">oder_id,</if>
            <if test="archiveId != null">archive_id,</if>
            <if test="designerId != null">designer_id,</if>
            <if test="scoreModel != null">score_model,</if>
            <if test="scoreNum != null">score_num,</if>
            <if test="scoreItem != null">score_item,</if>
            <if test="type != null">type,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oderId != null">#{oderId},</if>
            <if test="archiveId != null">#{archiveId},</if>
            <if test="designerId != null">#{designerId},</if>
            <if test="scoreModel != null">#{scoreModel},</if>
            <if test="scoreNum != null">#{scoreNum},</if>
            <if test="scoreItem != null">#{scoreItem},</if>
            <if test="type != null">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateOrderScore" parameterType="com.xgwc.order.entity.OrderScore">
        update xgwc_order_score
        <trim prefix="SET" suffixOverrides=",">
            <if test="oderId != null">oder_id = #{oderId},</if>
            <if test="archiveId != null">archive_id = #{archiveId},</if>
            <if test="designerId != null">designer_id = #{designerId},</if>
            <if test="scoreModel != null">score_model = #{scoreModel},</if>
            <if test="scoreNum != null">score_num = #{scoreNum},</if>
            score_item = #{scoreItem},
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>