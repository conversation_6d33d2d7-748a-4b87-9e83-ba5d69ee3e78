<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcPlatformShopMapper">


    <sql id="selectXgwcPlatformShopVo">
        select id, shop_id, shop_name, platform_id, brand_id
        from xgwc_platform_shop
    </sql>

    <select id="selectXgwcPlatformShopList" parameterType="com.xgwc.order.entity.vo.XgwcPlatformShopQueryVo"
            resultType="com.xgwc.order.entity.XgwcPlatformShop">
        <include refid="selectXgwcPlatformShopVo"/>
        where brand_id = #{brandId}
            <if test="id != null ">and id = #{id}</if>
            <if test="shopId != null  and shopId != ''">and shop_id = #{shopId}</if>
            <if test="shopName != null  and shopName != ''">and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="platformId != null ">and platform_id = #{platformId}</if>
            <if test="shopIdList != null  and shopIdList.size() > 0">
                and shop_id in
                <foreach item="item" collection="shopIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>

    <insert id="insertXgwcPlatformShop" parameterType="com.xgwc.order.entity.XgwcPlatformShop">
        insert into xgwc_platform_shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="platformId != null">platform_id,</if>
            <if test="brandId != null">brand_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="platformId != null">#{platformId},</if>
            <if test="brandId != null">#{brandId},</if>
        </trim>
    </insert>

    <delete id="deleteByBrandOwnerId">
        delete
        from xgwc_platform_shop
        where brand_id = #{brandOwnerId}
    </delete>

</mapper>