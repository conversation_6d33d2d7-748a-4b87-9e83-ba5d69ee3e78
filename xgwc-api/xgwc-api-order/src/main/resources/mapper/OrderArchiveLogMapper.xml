<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderArchiveLogMapper">
    

    <sql id="selectOrderArchiveLogVo">
        select id, archive_id, review, review_time, review_status, review_msg from xgwc_order_archive_log
    </sql>

    <select id="selectOrderArchiveLogByArchiveId" parameterType="Long" resultType="com.xgwc.order.entity.dto.OrderArchiveLogDto">
        <include refid="selectOrderArchiveLogVo"/>
        where archive_id = #{archiveId}
        order by id desc
    </select>

    <insert id="insertOrderArchiveLog" parameterType="com.xgwc.order.entity.OrderArchiveLog">
        insert into xgwc_order_archive_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="archiveId != null">archive_id,</if>
            <if test="review != null">review,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="reviewMsg != null">review_msg,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="archiveId != null">#{archiveId},</if>
            <if test="review != null">#{review},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="reviewMsg != null">#{reviewMsg},</if>
         </trim>
    </insert>

    <update id="updateOrderArchiveLog" parameterType="com.xgwc.order.entity.OrderArchiveLog">
        update xgwc_order_archive_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="archiveId != null">archive_id = #{archiveId},</if>
            <if test="review != null">review = #{review},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="reviewMsg != null">review_msg = #{reviewMsg},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteOrderArchiveLogById" parameterType="Long">
        update xgwc_order_archive_log set is_del = 1 where id = #{id}
    </update>

    <update id="deleteOrderArchiveLogByIds" parameterType="String">
        update xgwc_order_archive_log set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>