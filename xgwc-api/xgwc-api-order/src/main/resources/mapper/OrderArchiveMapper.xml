<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderArchiveMapper">
    

    <sql id="selectOrderArchiveVo">
        select id, oder_id, archive_name, archive_img, archive_files,link_url, review_status, review_msg, create_by, create_time, update_by, update_time,business_id,business_name from xgwc_order_archive
    </sql>

    
    <select id="selectOrderArchiveByOrderId" parameterType="Long" resultType="com.xgwc.order.entity.dto.OrderArchiveDto">
        <include refid="selectOrderArchiveVo"/>
        where oder_id = #{oderId}
        limit 1
    </select>

    <insert id="insertOrderArchive" parameterType="com.xgwc.order.entity.OrderArchive">
        insert into xgwc_order_archive
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oderId != null">oder_id,</if>
            <if test="archiveName != null">archive_name,</if>
            <if test="archiveImg != null">archive_img,</if>
            <if test="archiveFiles != null">archive_files,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="reviewMsg != null">review_msg,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="businessId != null">business_id,</if>
            <if test="businessName != null">business_name,</if>
            <if test="linkUrl != null">link_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oderId != null">#{oderId},</if>
            <if test="archiveName != null">#{archiveName},</if>
            <if test="archiveImg != null">#{archiveImg},</if>
            <if test="archiveFiles != null">#{archiveFiles},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="reviewMsg != null">#{reviewMsg},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="businessName != null">#{businessName},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
         </trim>
    </insert>

    <update id="updateOrderArchive" parameterType="com.xgwc.order.entity.OrderArchive">
        update xgwc_order_archive
        <trim prefix="SET" suffixOverrides=",">
            review_status = #{reviewStatus},
            review_msg = #{reviewMsg},
            <if test="oderId != null">oder_id = #{oderId},</if>
            <if test="archiveName != null">archive_name = #{archiveName},</if>
            <if test="archiveImg != null">archive_img = #{archiveImg},</if>
            <if test="archiveFiles != null">archive_files = #{archiveFiles},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="businessName != null">business_name = #{businessName},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteOrderArchiveById" parameterType="Long">
        update xgwc_order_archive set is_del = 1 where id = #{id}
    </update>

    <update id="deleteOrderArchiveByIds" parameterType="String">
        update xgwc_order_archive set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>