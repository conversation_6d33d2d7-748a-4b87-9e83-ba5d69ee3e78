<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcPlatformConfigMapper">


    <sql id="selectXgwcPlatformConfigVo">
        select id, sid, app_key, app_secret, qimen_app_key, qimen_app_secret, start_time from xgwc_platform_config
    </sql>

    <insert id="insert">
        insert into xgwc_platform_config (id, sid, app_key, app_secret, qimen_app_key, qimen_app_secret, start_time) value (#{id}, #{sid}, #{appKey}, #{appSecret}, #{qimenAppKey}, #{qimenAppSecret}, #{startTime})
    </insert>

    <update id="update">
        update xgwc_platform_config
            set sid = #{sid},
                app_key = #{appKey},
                app_secret = #{appSecret},
                qimen_app_key = #{qimenAppKey},
                qimen_app_secret = #{qimenAppSecret},
                start_time = #{startTime}
        where id = #{id}
    </update>

    <select id="selectXgwcPlatformConfigList" resultType="com.xgwc.order.entity.XgwcPlatformConfig">
        <include refid="selectXgwcPlatformConfigVo"/>
        <where>
            <if test="idList != null and idList.size() > 0">
                and id in
                <foreach item="item" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>