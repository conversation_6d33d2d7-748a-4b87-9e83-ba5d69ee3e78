<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcPlatformTradeMapper">
    

    <sql id="selectXgwcPlatformTradeVo">
        select id, trade_no, real_amount, pay_amount, order_amount, remark, platform_id, platform_shop_id, trade_time, pay_time, refund_status, trade_status, brand_id, create_time, update_time from xgwc_platform_trade
    </sql>

    <select id="getByTradeNo" resultType="com.xgwc.order.entity.XgwcPlatformTrade">
        <include refid="selectXgwcPlatformTradeVo"/>
        where trade_no = #{tradeNo} and brand_id in
        <foreach item="brandOwnerId" collection="brandOwnerIdList" open="(" separator="," close=")">
            #{brandOwnerId}
        </foreach>
    </select>

    <select id="listByTradeNo" resultType="com.xgwc.order.entity.XgwcPlatformTrade">
        <include refid="selectXgwcPlatformTradeVo"/>
        where brand_id = #{brandOwnerId}
            and trade_no in
            <foreach item="tradeNo" collection="tradeNoList" open="(" separator="," close=")">
                #{tradeNo}
            </foreach>
    </select>

    <select id="selectListByQuery" resultType="com.xgwc.order.entity.dto.XgwcPlatformTradeByBrandVO">
        select xo.create_time, xo.order_no, xo.franchise_id, xs.shop_id, xs.shop_name, xo.sale_man_id, xo.sale_man_name, xpt.trade_no, xpt.pay_amount, xpt.platform_id, xpt.platform_shop_id, xps.shop_name as platformShopName, if (xo.create_time is not null, 1, 0) as archiveResult
        from xgwc_platform_trade xpt
            left join xgwc_order_pay xgp on (xgp.is_del = 0 and xpt.trade_no = xgp.collection_no)
            left join xgwc_order xo on (xo.is_del = 0 and xgp.oder_id = xo.id and xpt.brand_id = xo.brand_id)
            left join xgwc_shop xs on xo.store_id = xs.shop_id
            left join xgwc_platform_shop xps on xpt.platform_shop_id = xps.shop_id and xpt.brand_id = xps.brand_id
        <where>
            <if test="brandId != null">
                and xpt.brand_id = #{brandId}
            </if>
            <if test="brandIdList != null and brandIdList.size() > 0">
                and xpt.brand_id in
                <foreach item="item" collection="brandIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                and (xgp.collection_no like concat('%', #{keyword}, '%') or xpt.trade_no like concat('%', #{keyword}, '%'))
            </if>
            <if test="franchiseId != null "> and xo.franchise_id = #{franchiseId}</if>
            <if test="shopId != null "> and xs.shop_id = #{shopId}</if>
            <if test="platformId != null "> and xpt.platform_id = #{platformId}</if>
            <if test="platformShopId != null and platformShopId != ''"> and xpt.platform_shop_id = #{platformShopId}</if>
            <if test="archiveResult == '1'.toString">and xo.create_time is not null</if>
            <if test="archiveResult == '0'.toString">and xo.create_time is null</if>
            <if test="crateTimeStart != null and createTimeEnd != null">
                and xo.create_time between #{crateTimeStart} and #{createTimeEnd}
            </if>
        </where>
    </select>


    <insert id="insertList" parameterType="com.xgwc.order.entity.XgwcPlatformTrade">
        insert into xgwc_platform_trade (trade_no, pay_amount,real_amount,order_amount, remark, platform_id,platform_shop_id,refund_status,trade_status,trade_time,pay_time,brand_id,create_time,update_time)
        values
        <foreach item="item" collection="xgwcPlatformTradeList" separator=",">
            (#{item.tradeNo},#{item.payAmount},#{item.realAmount},#{item.orderAmount},#{item.remark},#{item.platformId},#{item.platformShopId},#{item.refundStatus},#{item.tradeStatus},#{item.tradeTime},#{item.payTime},#{item.brandId},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <delete id="deleteByTradeNo">
        delete
        from xgwc_platform_trade
        where brand_id = #{brandOwnerId} and trade_no in
        <foreach item="tradeNo" collection="tradeNoList" open="(" separator="," close=")">
            #{tradeNo}
        </foreach>
    </delete>

</mapper>