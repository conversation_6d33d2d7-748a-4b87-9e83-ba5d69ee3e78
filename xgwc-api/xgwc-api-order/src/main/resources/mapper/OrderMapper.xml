<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderMapper">
    

    <sql id="selectOrderVo">
        select
            o.id, o.sale_man_id, o.sale_man_name, o.dept_id, o.dept_name, o.transfer_state, o.order_date, o.store_id, o.store_name,
            o.brand_id, o.pay_channel, o.sh_type, o.order_no, o.order_category, o.taobao_id, o.order_amount, o.amount, o.pay_type, o.archive_expect_time,
            o.allot_type, o.allot_urgency, o.allot_remark, o.allot_num, o.allot_file, o.pid, o.designer_id, o.designer_name,
            o.designer_phone,o.designer_business, o.money, o.archive_appoint_time, o.archive_time, o.archive_type, o.remark,
            o.franchise_id,o.state_dic_code,o.state_dic_name, ifnull(o.now_amount, amount) nowAmount, ifnull(o.now_money, money) nowMoney,
            o.create_by, o.create_time, o.update_by, o.update_time,o.allot_user_id, o.allot_user_name, so.order_time stylist_time,o.is_after_sale,o.refund_status,o.invoice_status,
            ifnull( bo.company_simple_name, bo.company_name ) brand_name,r.refund_reason after_sale_reason, ifnull(r.platform_refund_time,r.last_approve_time) after_sale_date,
            ifnull( fo.company_simple_name, fo.company_name ) franchise_name,o.deal_time, o.is_lock,o.is_transfer,o.is_buyback
        from xgwc_order o
         LEFT JOIN stylist_orders so ON o.id = so.order_id
         LEFT JOIN xgwc_brand_owner bo ON o.brand_id = bo.brand_id
         LEFT JOIN franchise_owner fo ON o.franchise_id = fo.franchise_id AND o.brand_id = fo.brand_id
         LEFT JOIN (
            SELECT r1.order_no, r1.refund_reason, r1.platform_refund_time, r1.last_approve_time
            FROM order_refund_apply r1
                     JOIN (
                SELECT MAX(id) AS max_id
                FROM order_refund_apply
                GROUP BY order_no
            ) r2 ON r1.id = r2.max_id
        ) r ON o.order_no = r.order_no
    </sql>

    <select id="selectOnlyOrderById" resultType="com.xgwc.order.entity.dto.OrderDto">
        SELECT
            o.id,
            o.sale_man_id,
            o.sale_man_name,
            o.dept_id,
            o.dept_name,
            o.transfer_state,
            o.order_date,
            o.store_id,
            o.store_name,
            o.brand_id,
            o.pay_channel,
            o.sh_type,
            o.order_no,
            o.order_category,
            o.taobao_id,
            o.order_amount,
            o.amount,
            o.pay_type,
            o.archive_expect_time,
            o.allot_type,
            o.allot_urgency,
            o.allot_remark,
            o.allot_num,
            o.allot_file,
            o.pid,
            o.designer_id,
            o.designer_name,
            o.designer_phone,
            o.designer_business,
            o.money,
            o.archive_appoint_time,
            o.archive_time,
            o.archive_type,
            o.remark,
            o.franchise_id,
            o.state_dic_code,
            o.state_dic_name,
            ifnull( o.now_amount, amount ) nowAmount,
            ifnull( o.now_money, money ) nowMoney,
            o.create_by,
            o.create_time,
            o.update_by,
            o.update_time,
            o.allot_user_id,
            o.allot_user_name,
            o.is_after_sale,
            o.deal_time,
            o.settlement,
            o.settlement_time,
            o.is_archiving,
            o.is_transfer,
            o.is_buyback
        FROM
            xgwc_order o
        WHERE
            o.id = #{id}
    </select>

    <select id="selectOrderList" parameterType="com.xgwc.order.entity.vo.OrderQueryVo" resultType="com.xgwc.order.entity.dto.OrderDto">
        <include refid="selectOrderVo"/>
        <where>
            o.is_del = 0
            <if test="id != null "> and o.id = #{id}</if>
            <if test="saleManId != null "> and o.sale_man_id = #{saleManId}</if>
            <if test="saleManName != null  and saleManName != ''"> and o.sale_man_name = #{saleManName}</if>
            <if test="deptId != null "> and o.dept_id = #{deptId}</if>
            <if test="deptIds != null ">
                and o.dept_id in
                <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
             </if>
            <if test="deptName != null  and deptName != ''"> and o.dept_name = #{deptName}</if>
            <if test="transferState != null "> and o.transfer_state = #{transferState}</if>
            <if test="orderDateStart != null and orderDateEnd != null ">
                and o.order_date between #{orderDateStart} and DATE_ADD(#{orderDateEnd}, INTERVAL 1 DAY)
            </if>
            <if test="storeId != null "> and o.store_id = #{storeId}</if>
            <if test="storeName != null  and storeName != ''"> and o.store_name like concat('%', #{storeName}, '%')</if>
            <if test="franchiseName != null  and franchiseName != ''"> and ifnull( fo.company_simple_name, fo.company_name ) like concat('%', #{franchiseName}, '%')</if>
            <if test="brandId != null "> and o.brand_id = #{brandId}</if>
            <if test="franchiseId != null "> and o.franchise_id = #{franchiseId}</if>
            <if test="payChannel != null "> and o.pay_channel = #{payChannel}</if>
            <if test="shType != null "> and o.sh_type = #{shType}</if>
            <if test="shTypes != null "> and o.sh_type in
                <foreach item="shType" collection="shTypes" open="(" separator="," close=")">
                    #{shType}
                </foreach>
            </if>
            <if test="refundStatuss != null "> and o.refund_status in
                <foreach item="refundStatus" collection="refundStatuss" open="(" separator="," close=")">
                    #{refundStatus}
                </foreach>
            </if>
            <if test="orderNo != null  and orderNo != ''"> and o.order_no = #{orderNo}</if>
            <if test="taobaoId != null  and taobaoId != ''"> and o.taobao_id = #{taobaoId}</if>
            <if test="orderAmount != null "> and o.order_amount = #{orderAmount}</if>
            <if test="amount != null "> and o.amount = #{amount}</if>
            <if test="payType != null "> and o.pay_type = #{payType}</if>
            <if test="allotType != null  and allotType != ''"> and o.allot_type like concat('%', #{allotType}, '%')</if>
            <if test="allotUrgency != null  and allotUrgency != ''"> and o.allot_urgency like concat('%', #{allotUrgency}, '%')</if>
            <if test="allotRemark != null  and allotRemark != ''"> and o.allot_remark like concat('%', #{allotRemark}, '%')</if>
            <if test="allotNum != null "> and o.allot_num = #{allotNum}</if>
            <if test="allotFile != null  and allotFile != ''"> and o.allot_file like concat('%', #{allotFile}, '%')</if>
            <if test="pid != null "> and o.pid = #{pid}</if>
            <if test="nowAmount != null "> and o.now_amount = #{nowAmount}</if>
            <if test="nowMoney != null "> and o.now_money = #{nowMoney}</if>
            <if test="designerId != null "> and o.designer_id = #{designerId}</if>
            <if test="designerName != null  and designerName != ''"> and o.designer_name like concat('%', #{designerName}, '%')</if>
            <if test="designerPhone != null "> and o.designer_phone = #{designerPhone}</if>
            <if test="money != null "> and o.money = #{money}</if>
            <if test="archiveType != null "> and o.archive_type = #{archiveType}</if>
            <if test="archiveTypes != null "> and o.archive_type in
                <foreach item="archiveType" collection="archiveTypes" open="(" separator="," close=")">
                    #{archiveType}
                </foreach>
             </if>
            <if test="createBy != null  and createBy != ''"> and o.create_by like concat('%', #{createBy}, '%')</if>
            <if test="createTimeStart != null and createTimeEnd != null ">
                and o.create_time between #{createTimeStart} and DATE_ADD(#{createTimeEnd}, INTERVAL 1 DAY)
            </if>
            <if test="createTime != null and createTime != null ">
                and o.create_time >= #{createTime}
            </if>
        </where>
        order by o.order_date desc, o.create_time desc
    </select>

    <select id="selectSubOrderList" parameterType="Long" resultType="com.xgwc.order.entity.dto.SubOrderDto">
        select id, order_no,pid,designer_id, designer_name, designer_phone, money, ifnull(now_money, money) nowMoney, archive_appoint_time, archive_type, remark,allot_user_id, allot_user_name,designer_business from xgwc_order
        where pid = #{pid}
    </select>

    <select id="selectSubOrderListByPno" parameterType="String" resultType="com.xgwc.order.entity.dto.SubOrderDto">
        select
            so.id, so.order_no,so.designer_id, so.designer_name, so.designer_phone,so.designer_business, so.money, so.archive_appoint_time, so.archive_type, so.remark, so.allot_user_id, so.allot_user_name
        from xgwc_order so
        left join xgwc_order o on so.pid = o.id
        where o.order_no = #{pno}
    </select>


    <select id="selectOrderById" parameterType="Long" resultType="com.xgwc.order.entity.dto.OrderDto">
        <include refid="selectOrderVo"/>
        where o.id = #{id}
    </select>
    <select id="selectOrderByTaobaoId" parameterType="String" resultType="com.xgwc.order.entity.dto.OrderDto">
        <include refid="selectOrderVo"/>
        where o.taobao_id = #{taobaoId}
    </select>


    <select id="selectOrderTraceById" parameterType="Long" resultType="com.xgwc.order.entity.OrderTrace">
        select
            t.*,
            concat(ifnull(ifnull( bo.company_simple_name, bo.company_name ),''),ifnull(ifnull( fo.company_simple_name, fo.company_name ), '')) company_name
        from xgwc_order_trace t
         LEFT JOIN xgwc_brand_owner bo ON t.brand_id = bo.brand_id
         LEFT JOIN franchise_owner fo ON t.franchise_id = fo.franchise_id
        where t.order_id = #{id}
    </select>


    <select id="selectOrderByOrderNo" parameterType="String" resultType="com.xgwc.order.entity.dto.OrderDto">
        <include refid="selectOrderVo"/>
        where o.order_no = #{orderNo}
    </select>

    <select id="selectOrderByOrderNos" parameterType="String" resultType="com.xgwc.order.entity.dto.OrderDto">
        <include refid="selectOrderVo"/>
        where o.order_no in
        <foreach item="orderNo" collection="array" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>


    <select id="getDesignersBusiness" parameterType="Long" resultType="Long">
        select DISTINCT good_business from xgwc_designer
        where designer_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>

    <select id="findHistoryOrderList" resultType="com.xgwc.order.entity.dto.OrderDto">
        <include refid="selectOrderVo"/>
        <where>
            <if test="brandId != null "> and o.brand_id = #{brandId}</if>
            <if test="designerName != null  and designerName != ''"> and o.designer_name like concat('%', #{designerName}, '%')</if>
            <if test="other != null  and other != ''">
                and (o.order_no like concat('%', #{other}, '%') or o.taobao_id like concat('%', #{other}, '%'))
            </if>
        </where>
        order by o.create_time desc
    </select>
    <select id="selectStylistOrdersById" resultType="java.lang.Long">
        SELECT
            so.order_id as id
        FROM
            stylist_orders so
        where so.order_id = #{id}
    </select>

    <select id="findSubOrderByOrderId" resultType="com.xgwc.order.entity.dto.SubOrderDto">
        select so.id,so.pid, so.order_no,so.designer_id, so.designer_name, so.designer_phone,so.designer_business, so.money, so.archive_appoint_time, so.archive_type, so.remark, so.allot_user_id, so.allot_user_name
        from xgwc_order so
        where so.id = #{id} or so.pid = #{id}
    </select>

    <select id="findBusinessLevelByOrderId" resultType="java.lang.String">
        select b.`level`
        from xgwc_order o
        left join xgwc_business b on o.state_dic_code = b.business_id
        where o.id = #{id}
    </select>


    <insert id="insertOrder" parameterType="com.xgwc.order.entity.Order" useGeneratedKeys="true" keyProperty="id">
        insert into xgwc_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderCategory != null">order_category,</if>
            <if test="saleManId != null">sale_man_id,</if>
            <if test="saleManName != null">sale_man_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="transferState != null">transfer_state,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="storeId != null">store_id,</if>
            <if test="storeName != null">store_name,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="payChannel != null">pay_channel,</if>
            <if test="shType != null">sh_type,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="taobaoId != null">taobao_id,</if>
            <if test="orderAmount != null">order_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="payType != null">pay_type,</if>
            <if test="archiveExpectTime != null">archive_expect_time,</if>
            <if test="allotType != null">allot_type,</if>
            <if test="allotUrgency != null">allot_urgency,</if>
            <if test="allotRemark != null">allot_remark,</if>
            <if test="allotNum != null">allot_num,</if>
            <if test="allotUserId != null">allot_user_id,</if>
            <if test="allotUserName != null">allot_user_name,</if>
            <if test="allotFile != null">allot_file,</if>
            <if test="pid != null">pid,</if>
            <if test="designerId != null">designer_id,</if>
            <if test="designerName != null">designer_name,</if>
            <if test="designerPhone != null">designer_phone,</if>
            <if test="designerBusiness != null">designer_business,</if>
            <if test="money != null">money,</if>
            <if test="archiveAppointTime != null">archive_appoint_time,</if>
            <if test="archiveType != null">archive_type,</if>
            <if test="isArchiving != null">is_archiving,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="stateDicCode != null">state_dic_code,</if>
            <if test="stateDicName != null">state_dic_name,</if>
            <if test="isTransfer != null">is_transfer,</if>
            <if test="isBuyback != null">is_buyback,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderCategory != null">#{orderCategory},</if>
            <if test="saleManId != null">#{saleManId},</if>
            <if test="saleManName != null">#{saleManName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="transferState != null">#{transferState},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="storeName != null">#{storeName},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="payChannel != null">#{payChannel},</if>
            <if test="shType != null">#{shType},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="taobaoId != null">#{taobaoId},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="payType != null">#{payType},</if>
            <if test="archiveExpectTime != null">#{archiveExpectTime},</if>
            <if test="allotType != null">#{allotType},</if>
            <if test="allotUrgency != null">#{allotUrgency},</if>
            <if test="allotRemark != null">#{allotRemark},</if>
            <if test="allotNum != null">#{allotNum},</if>
            <if test="allotUserId != null">#{allotUserId},</if>
            <if test="allotUserName != null">#{allotUserName},</if>
            <if test="allotFile != null">#{allotFile},</if>
            <if test="pid != null">#{pid},</if>
            <if test="designerId != null">#{designerId},</if>
            <if test="designerName != null">#{designerName},</if>
            <if test="designerPhone != null">#{designerPhone},</if>
            <if test="designerBusiness != null">#{designerBusiness},</if>
            <if test="money != null">#{money},</if>
            <if test="archiveAppointTime != null">#{archiveAppointTime},</if>
            <if test="archiveType != null">#{archiveType},</if>
            <if test="isArchiving != null">#{isArchiving},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="stateDicCode != null">#{stateDicCode},</if>
            <if test="stateDicName != null">#{stateDicName},</if>
            <if test="isTransfer != null">#{isTransfer},</if>
            <if test="isBuyback != null">#{isBuyback},</if>
         </trim>
        <selectKey keyProperty="id" resultType="long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <insert id="insertOrders" parameterType="com.xgwc.order.entity.Order">
        INSERT INTO xgwc_order (
            sale_man_id,
            sale_man_name,
            dept_id,
            dept_name,
            transfer_state,
            order_date,
            store_id,
            store_name,
            brand_id,
            franchise_id,
            pay_channel,
            sh_type,
            order_no,
            order_category,
            taobao_id,
            order_amount,
            amount,
            pay_type,
            archive_expect_time,
            allot_type,
            allot_urgency,
            allot_remark,
            allot_num,
            allot_user_id,
            allot_user_name,
            allot_file,
            pid,
            designer_id,
            designer_name,
            designer_phone,
            designer_business,
            money,
            archive_appoint_time,
            remark,
            create_by,
            create_time,
            state_dic_code,
            state_dic_name
        ) VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.saleManId},
            #{item.saleManName},
            #{item.deptId},
            #{item.deptName},
            #{item.transferState},
            #{item.orderDate},
            #{item.storeId},
            #{item.storeName},
            #{item.brandId},
            #{item.franchiseId},
            #{item.payChannel},
            #{item.shType},
            #{item.orderNo},
            #{item.orderCategory},
            #{item.taobaoId},
            #{item.orderAmount},
            #{item.amount},
            #{item.payType},
            #{item.archiveExpectTime},
            #{item.allotType},
            #{item.allotUrgency},
            #{item.allotRemark},
            #{item.allotNum},
            #{item.allotUserId},
            #{item.allotUserName},
            #{item.allotFile},
            #{item.pid},
            #{item.designerId},
            #{item.designerName},
            #{item.designerPhone},
            #{item.designerBusiness},
            #{item.money},
            #{item.archiveAppointTime},
            #{item.remark},
            #{item.createBy},
            #{item.createTime},
            #{item.stateDicCode},
            #{item.stateDicName}
            )
        </foreach>
    </insert>

    <update id="updateOrder" parameterType="com.xgwc.order.entity.Order">
        update xgwc_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="saleManId != null">sale_man_id = #{saleManId},</if>
            <if test="saleManName != null">sale_man_name = #{saleManName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="transferState != null">transfer_state = #{transferState},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="payChannel != null">pay_channel = #{payChannel},</if>
            <if test="shType != null">sh_type = #{shType},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="orderCategory != null">order_category = #{orderCategory},</if>
            <if test="taobaoId != null">taobao_id = #{taobaoId},</if>
            <if test="orderAmount != null">order_amount = #{orderAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="archiveExpectTime != null">archive_expect_time = #{archiveExpectTime},</if>
            <if test="allotType != null">allot_type = #{allotType},</if>
            <if test="allotUrgency != null">allot_urgency = #{allotUrgency},</if>
            <if test="allotRemark != null">allot_remark = #{allotRemark},</if>
            <if test="allotNum != null">allot_num = #{allotNum},</if>
            <if test="allotUserId != null">allot_user_id = #{allotUserId},</if>
            <if test="allotUserName != null">allot_user_name = #{allotUserName},</if>
            <if test="allotFile != null">allot_file = #{allotFile},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="designerId != null">designer_id = #{designerId},</if>
            <if test="designerName != null">designer_name = #{designerName},</if>
            <if test="designerPhone != null">designer_phone = #{designerPhone},</if>
            <if test="designerBusiness != null">designer_business = #{designerBusiness},</if>
            <if test="money != null">money = #{money},</if>
            <if test="archiveAppointTime != null">archive_appoint_time = #{archiveAppointTime},</if>
            <if test="archiveTime != null">archive_time = #{archiveTime},</if>
            <if test="archiveType != null">archive_type = #{archiveType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="stateDicCode != null">state_dic_code = #{stateDicCode},</if>
            <if test="stateDicName != null">state_dic_name = #{stateDicName},</if>
            <if test="nowAmount != null ">now_amount = #{nowAmount},</if>
            <if test="nowMoney != null ">now_money = #{nowMoney},</if>
            <if test="isTransfer != null ">is_transfer = #{isTransfer},</if>
        </trim>
        where id = #{id}
    </update>

    <insert id="designerUpdate" parameterType="com.xgwc.order.entity.OrderDesignerUpdate" useGeneratedKeys="true" keyProperty="id">
        insert into xgwc_order_designer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDuty != null">is_duty,</if>
            <if test="oderId != null">oder_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="oldDesignerId != null">old_designer_id,</if>
            <if test="oldDesignerName != null">old_designer_name,</if>
            <if test="oldDesignerPhone != null">old_designer_phone,</if>
            <if test="oldDesignerBusiness != null">old_designer_business,</if>
            <if test="designerId != null">designer_id,</if>
            <if test="designerName != null">designer_name,</if>
            <if test="designerPhone != null">designer_phone,</if>
            <if test="designerBusiness != null">designer_business,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDuty != null">#{isDuty},</if>
            <if test="oderId != null">#{oderId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="oldDesignerId != null">#{oldDesignerId},</if>
            <if test="oldDesignerName != null">#{oldDesignerName},</if>
            <if test="oldDesignerPhone != null">#{oldDesignerPhone},</if>
            <if test="oldDesignerBusiness != null">#{oldDesignerBusiness},</if>
            <if test="designerId != null">#{designerId},</if>
            <if test="designerName != null">#{designerName},</if>
            <if test="designerPhone != null">#{designerPhone},</if>
            <if test="designerBusiness != null">#{designerBusiness},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <insert id="insertOrderTrace" parameterType="com.xgwc.order.entity.OrderTrace">
        insert into xgwc_order_trace
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="saleManName != null">sale_man_name,</if>
            <if test="transferState != null">transfer_state,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="payChannel != null">pay_channel,</if>
            <if test="shType != null">sh_type,</if>
            <if test="taobaoId != null">taobao_id,</if>
            <if test="orderAmount != null">order_amount,</if>
            <if test="archiveExpectTime != null">archive_expect_time,</if>
            <if test="payType != null">pay_type,</if>
            <if test="allotUserName != null">allot_user_name,</if>
            <if test="designerName != null">designer_name,</if>
            <if test="money != null">money,</if>
            <if test="archiveAppointTime != null">archive_appoint_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="type != null">type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="saleManName != null">#{saleManName},</if>
            <if test="transferState != null">#{transferState},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="payChannel != null">#{payChannel},</if>
            <if test="shType != null">#{shType},</if>
            <if test="taobaoId != null">#{taobaoId},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="archiveExpectTime != null">#{archiveExpectTime},</if>
            <if test="payType != null">#{payType},</if>
            <if test="allotUserName != null">#{allotUserName},</if>
            <if test="designerName != null">#{designerName},</if>
            <if test="money != null">#{money},</if>
            <if test="archiveAppointTime != null">#{archiveAppointTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="type != null">#{type},</if>
        </trim>
    </insert>

    <update id="deleteOrderById" parameterType="Long">
        update xgwc_order set is_del = 1 where id = #{id}
    </update>

    <delete id="deleteStylistOrdersByOrderId">
        delete from stylist_orders where order_id = #{orderId}
    </delete>

    <update id="deleteOrderByIds" parameterType="String">
        update xgwc_order set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateStylistOrders">
        UPDATE `xgwc_sass`.`stylist_orders`
        <trim prefix="SET" suffixOverrides=",">
            <if test="order.orderNo != null">`order_no` = #{order.orderNo},</if>
            <if test="order.taobaoId != null">`customer_id` = #{order.taobaoId},</if>
            <if test="order.brandId != null">`brand_owner_id` = #{order.brandId},</if>
            <if test="order.money != null">`commission` = #{order.money},</if>
            <if test="order.archiveAppointTime != null">`scheduled_date` = #{order.archiveAppointTime},</if>
            <if test="order.shType != null">`order_status` = #{order.shType},</if>
            <if test="order.updateBy != null">`update_by` = #{order.updateBy},</if>
            `update_time` = now()
        </trim>
        WHERE `order_id` = #{order.id}
    </update>

    <select id="selectOrderCompanyByOrderNo" parameterType="String" resultType="com.xgwc.order.entity.dto.OrderCompanyDto">
        select
            o.id, o.sale_man_id, o.sale_man_name, o.dept_id, o.dept_name, o.transfer_state, o.order_date, o.store_id, o.store_name,
            o.brand_id, o.pay_channel, o.sh_type, o.order_no, o.taobao_id, o.order_amount, o.amount, o.pay_type, o.archive_expect_time,
            o.allot_type, o.allot_urgency, o.allot_remark, o.allot_num, o.allot_file, o.pid, o.designer_id, o.designer_name,
            o.designer_phone,o.designer_business, o.money, o.archive_appoint_time, o.archive_time, o.archive_type, o.remark,
            o.franchise_id,o.state_dic_code,o.state_dic_name,
            o.create_by, o.create_time, o.update_by, o.update_time,o.allot_user_id, o.allot_user_name,
            i.company_name
        from xgwc_order o
                 LEFT JOIN xgwc_company_shop s ON o.store_id = s.shop_id
                 LEFT JOIN xgwc_company_info i ON i.id = s.company_id
        where order_no = #{orderNo} limit 1
    </select>

    <select id="getByOrderNo" resultType="com.xgwc.order.entity.Order">
        select order_no, franchise_id, taobao_id, order_date, sale_man_name, order_amount, amount from xgwc_order
        where order_no = #{orderNo} and brand_id in
        <foreach item="brandOwnerId" collection="brandOwnerIdList" open="(" separator="," close=")">
            #{brandOwnerId}
        </foreach>
    </select>

    <update id="batchUpdateOrders" parameterType="java.util.List">
        UPDATE xgwc_order
        SET
        money = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN #{item.nowMoney}
        </foreach>
        END,
        update_time = now()
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>


    <update id="updateOrderStatus">
        update xgwc_order
            <set>
                deal_time = #{dealTime}, update_by = #{updateBy}, update_time = now(),
                <if test="archiveType != null">
                    archive_type = #{archiveType},
                    archive_time = now(),
                </if>
                <if test="isAfterSale != null">
                    is_after_sale = #{isAfterSale},
                </if>
                <if test="isArchiving != null">
                    is_archiving = #{isArchiving},
                </if>
                <if test="settlement != null">
                    settlement = #{settlement},
                </if>
                <if test="settlementTime != null">
                    settlement_time=#{settlementTime},
                </if>
            </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="updateOrderAfterSalesStatus">
        UPDATE xgwc_order
        SET is_after_sale = #{isAfterSale}, update_time = now()
        WHERE id = #{id}
    </update>

    <update id="batchUpdateRefundStatus">
        UPDATE xgwc_order
        SET refund_status = #{refundStatus}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateInvoiceStatus">
        UPDATE xgwc_order
        SET invoice_status = #{invoiceStatus}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="franchiseRules" resultType="com.xgwc.order.entity.vo.RoleDataVo">
        select
            data_scope,
            dept_id,
            time_limit,
            data_masking
        from franchise_role_data
        where  menu_id = #{menuId}
            and role_id in
        <foreach item="role" collection="roles" open="(" separator="," close=")">
            #{role}
        </foreach>
    </select>

    <select id="brandRules" resultType="com.xgwc.order.entity.vo.RoleDataVo">
        select
        data_scope,
        dept_id,
        time_limit,
        data_masking
        from xgwc_role_data
        where  menu_id = #{menuId}
        and role_id in
        <foreach item="role" collection="roles" open="(" separator="," close=")">
            #{role}
        </foreach>
    </select>

    <select id="getOrderDtoListByIds" resultType="com.xgwc.order.entity.BillOrderModify">
        select * from xgwc_order where id in
        <foreach item="id" collection="orderIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>