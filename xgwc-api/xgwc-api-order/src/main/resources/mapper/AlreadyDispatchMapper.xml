<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.AlreadyDispatchMapper">
    

    <sql id="selectAlreadyDispatchVo">
        select id, order_id, agency_dispatch_id, dispatch_user_id, dispatch_time, dispatch_remark, create_by, create_time, update_by, update_time, modify_time from xgwc_already_dispatch
    </sql>

    <select id="selectAlreadyDispatchList" parameterType="com.xgwc.order.entity.vo.AlreadyDispatchQueryVo" resultType="com.xgwc.order.entity.dto.AlreadyDispatchPageDto">
        select
            t.id,
            t.order_id as orderId,
            t.agency_dispatch_id as agencyDispatchId,
            o.order_date as orderDate,
            o.archive_expect_time as archiveExpectTime,
            o.archive_appoint_time as archiveAppointTime,
            o.order_no AS orderNo,
            o.taobao_id AS taobaoId,
            o.money as money,
            o.designer_name AS designerName,
            o.order_amount AS orderAmount,
            o.sale_man_name AS saleManName,
            o.remark AS remark,
            o.sh_type AS shType,
            o.allot_file as allotFile,
            o.allot_remark as allotRemark,
            o.pid as pid,
            f.franchise_name AS companyName
        from xgwc_already_dispatch t
        LEFT JOIN xgwc_order o ON t.order_id = o.id
        LEFT JOIN franchise f ON o.franchise_id = f.id
        <where>
            <if test="dispatchUserId != null "> and t.dispatch_user_id = #{dispatchUserId} </if>
            <if test="other != null  and other != ''">
                and (o.order_no like concat('%', #{other}, '%') or o.taobao_id like concat('%', #{other}, '%') or o.sale_man_name like concat('%', #{other}, '%'))
            </if>
            <if test="franchiseeId != null "> and o.franchise_id = #{franchiseeId} </if>
            <if test="shType != null "> and o.sh_type = #{shType} </if>
            <if test="startTime != null and endTime != null">
                and o.order_date between #{startTime} and #{endTime}
            </if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectAlreadyDispatchById" parameterType="Long" resultType="com.xgwc.order.entity.dto.AlreadyDispatchDto">
        select
            t.id,
            t.order_id as oderId,
            t.agency_dispatch_id as agencyDispatchId,
            o.order_no AS orderNo,
            o.order_date AS orderDate,
            o.archive_expect_time as archiveExpectTime,
            o.archive_appoint_time AS archiveAppointTime,
            o.taobao_id AS taobaoId,
            o.sale_man_name AS saleManName,
            o.order_amount AS orderAmount,
            o.state_dic_code as stateDicCode,
            o.state_dic_name AS stateDicName,
            o.allot_remark AS allotRemark,
            o.allot_urgency AS dispatchType,
            o.money as money,
            o.designer_name as designerName,
            o.allot_user_name as allotUserName,
            o.allot_file as allotFile,
            o.allot_user_id as allotUserId,
            o.designer_id as designerId,
            o.designer_phone as designerPhone,
            o.designer_business as designerBusiness,
            o.remark as remark,
            o.amount as amount,
            o.pid as pid,
            f.franchise_name AS franchiseName
        from xgwc_already_dispatch t
        LEFT JOIN xgwc_order o ON t.order_id = o.id
        LEFT JOIN franchise f ON o.franchise_id = f.id
        where t.id = #{id}
    </select>

    <select id="findAlreadyRecordList" resultType="com.xgwc.order.entity.dto.AlreadyRecordPageDto">
        select
            t.id,
            t.order_id as orderId,
            t.agency_dispatch_id as agencyDispatchId,
            o.order_no AS orderNo,
            o.order_date AS orderDate,
            o.archive_appoint_time AS archiveAppointTime,
            o.taobao_id AS taobaoId,
            o.sale_man_name AS saleManName,
            o.order_amount AS orderAmount,
            o.store_name AS storeName,
            o.allot_remark AS allotRemark,
            o.allot_urgency AS dispatchType,
            o.money as money,
            o.designer_name as designerName,
            o.allot_user_name as allotUserName,
            o.allot_file as allotFile,
            o.sh_type as shType,
            o.pid as pid,
            fo.company_name AS companyName
        from xgwc_already_dispatch t
        LEFT JOIN xgwc_order o ON t.order_id = o.id
        LEFT JOIN franchise_owner fo ON o.franchise_id = fo.franchise_id
        <where>
            <if test="brandId != null "> and o.brand_id = #{brandId} </if>
            <if test="other != null  and other != ''">
                and (o.order_no like concat('%', #{other}, '%') or o.taobao_id like concat('%', #{other}, '%') or o.sale_man_name like concat('%', #{other}, '%'))
            </if>
            <if test="shType != null "> and o.sh_type = #{shType} </if>
            <if test="franchiseeId != null "> and o.franchise_id = #{franchiseeId} </if>
            <if test="storeId != null "> and o.store_id = #{storeId} </if>
            <if test="orderDate != null "> and o.order_date between #{orderDate}[0] and #{orderDate}[1] </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="findSubOrderByDispatchId" resultType="com.xgwc.order.entity.dto.SubOrderDto">
        select t.order_id as id,
               t.dispatch_user_id as allotUserId,
               xs.name as allotUserName,
               xo.order_no as orderNo,
               xo.pid as pid,
               xo.designer_id as designerId,
               xo.designer_name as designerName,
               xo.designer_phone as designerPhone,
               xo.money as money,
               xo.archive_appoint_time as archiveAppointTime,
               xo.archive_time as archiveTime,
               xo.archive_type as archiveType,
               xo.remark as remark
        from xgwc_already_dispatch t
        left join xgwc_order xo on xo.id = t.order_id
        left join xgwc_staff xs on xs.bind_user_id = t.dispatch_user_id
        where t.agency_dispatch_id = #{agencyDispatchId}
        and t.dispatch_user_id = #{dispatchUserId}
    </select>

    <insert id="insertAlreadyDispatch" parameterType="com.xgwc.order.entity.AlreadyDispatch">
        insert into xgwc_already_dispatch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="agencyDispatchId != null">agency_dispatch_id,</if>
            <if test="dispatchUserId != null">dispatch_user_id,</if>
            <if test="dispatchTime != null">dispatch_time,</if>
            <if test="dispatchRemark != null">dispatch_remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="agencyDispatchId != null">#{agencyDispatchId},</if>
            <if test="dispatchUserId != null">#{dispatchUserId},</if>
            <if test="dispatchTime != null">#{dispatchTime},</if>
            <if test="dispatchRemark != null">#{dispatchRemark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
         </trim>
    </insert>

    <update id="updateAlreadyDispatch" parameterType="com.xgwc.order.entity.AlreadyDispatch">
        update xgwc_already_dispatch
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="agencyDispatchId != null">agency_dispatch_id = #{agencyDispatchId},</if>
            <if test="dispatchUserId != null">dispatch_user_id = #{dispatchUserId},</if>
            <if test="dispatchTime != null">dispatch_time = #{dispatchTime},</if>
            <if test="dispatchRemark != null">dispatch_remark = #{dispatchRemark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAlreadyDispatchById" parameterType="Long">
        update xgwc_already_dispatch set is_del = 1 where id = #{id}
    </update>

    <update id="deleteAlreadyDispatchByIds" parameterType="String">
        update xgwc_already_dispatch set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>