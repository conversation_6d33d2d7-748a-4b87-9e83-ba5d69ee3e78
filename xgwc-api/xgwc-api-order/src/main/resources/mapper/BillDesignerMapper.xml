<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.BillDesignerMapper">
    <select id="getBillDesignerByOrderId" resultType="com.xgwc.order.entity.BillDesigner">
        SELECT
            o.*
        FROM
            bill_order o
            LEFT JOIN bill_designer d ON d.bill_id = o.bill_id
        where o.order_id = #{orderId} limit 1
    </select>
</mapper>