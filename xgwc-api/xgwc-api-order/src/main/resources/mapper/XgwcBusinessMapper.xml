<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcBusinessMapper">

    <insert id="saveXgwcBusiness">
        INSERT INTO `xgwc_sass`.`xgwc_business` (`business_id`, `brand_owner_id`,`business_name`, `pid`, `level`, `sort`, `create_by`, `create_time`)
        VALUES (#{xgwcBusinessDto.businessId}, #{xgwcBusinessDto.brandOwnerId},#{xgwcBusinessDto.businessName}, #{xgwcBusinessDto.pid},
                #{xgwcBusinessDto.level}, #{xgwcBusinessDto.sort},
                #{xgwcBusinessDto.createBy}, now());
    </insert>

    <update id="updateStatusById">
        update xgwc_business
        set status = #{status}
        where business_id = #{businessId}
    </update>

    <update id="updateXgwcBusinessById">
        update xgwc_business
        <set>
            <if test="xgwcBusinessDto.businessName != null and xgwcBusinessDto.businessName != ''">
                business_name = #{xgwcBusinessDto.businessName},
            </if>
            <if test="xgwcBusinessDto.brandOwnerId != null">
                brand_owner_id = #{xgwcBusinessDto.brandOwnerId},
            </if>
            <if test="xgwcBusinessDto.pid != null">
                pid = #{xgwcBusinessDto.pid},
            </if>
            <if test="xgwcBusinessDto.level != null">
                `level` = #{xgwcBusinessDto.level},
            </if>
            <if test="xgwcBusinessDto.sort != null">
                sort = #{xgwcBusinessDto.sort},
            </if>
            <if test="xgwcBusinessDto.updateBy != null">
                update_by = #{xgwcBusinessDto.updateBy},
            </if>
            `update_time` = NOW(),
            <if test="xgwcBusinessDto.modifyTime != null">
                modify_time = #{xgwcBusinessDto.modifyTime},
            </if>
        </set>
        where business_id = #{xgwcBusinessDto.businessId}
    </update>
    <update id="updateBusinessSetting">
        INSERT INTO xgwc_business_setting (
        business_id,
        relation_business_id,
        business_name,
        status,
        create_by,
        create_time
        )
        VALUES
        <foreach collection="businessSettingDto.businessSettingVo" item="item" separator=",">
            (
            #{businessSettingDto.businessId},
            #{item.relationBusinessId},
            #{item.businessSettingName},
            #{businessSettingDto.status},
            #{businessSettingDto.createBy},
            now()
            )
        </foreach>
    </update>
    <delete id="delBusinessSetting">
        DELETE FROM xgwc_business_setting
        WHERE
        business_id = #{businessId}
    </delete>

    <select id="getXgwcBusinessList" resultType="com.xgwc.order.entity.vo.XgwcBusinessVo">
        select
        business_id as businessId,
        business_name as businessName,
        pid,
        level,
        sort,
        status,
        create_time as createTime
        from xgwc_business
        <where>
            brand_owner_id = #{xgwcBusinessParam.brandOwnerId}

            <if test="xgwcBusinessParam.businessName != null and xgwcBusinessParam.businessName != ''">
                AND business_name like concat('%', #{xgwcBusinessParam.businessName} , '%')
            </if>
            <if test="xgwcBusinessParam.status != null">
                AND status = #{xgwcBusinessParam.status}
            </if>
        </where>
        order by sort
    </select>
    <select id="getXgwcBusinessById" resultType="com.xgwc.order.entity.vo.XgwcBusinessVo">
        select
        business_id as businessId,
        business_name as businessName,
        pid,
        level,
        sort,
        brand_owner_id as brandOwnerId
        from xgwc_business
        <where>
            <if test="businessId != null">
                AND business_id = #{businessId}
            </if>
            <if test="pid != null">
                AND business_id = #{pid}
            </if>
        </where>
    </select>
    <select id="selectXgwcBusinessById" resultType="com.xgwc.order.entity.vo.XgwcBusinessVo">
        select
        level
        from xgwc_business
        <where>
            <if test="pid != null">
                AND pid = #{pid}
            </if>
        </where>
    </select>
    <select id="getBusinessTreeByBrandOwnerId" resultType="com.xgwc.order.entity.vo.XgwcBusinessVo">
        SELECT
        b.business_id AS businessId,
        b.business_name AS businessName,
        bo.brand_id AS brandOwnerId,
        bo.company_name AS brandOwnerName,
        b.pid,
        b.level
        FROM xgwc_brand_owner bo
        LEFT JOIN xgwc_business b ON bo.brand_id = b.brand_owner_id
        WHERE b.status = 0 and  bo.brand_id IN
        <foreach item="item" collection="brandOwnerId" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="getParentByPid" resultType="com.xgwc.order.entity.vo.XgwcBusinessVo">
        SELECT business_id, business_name, pid, level
        FROM xgwc_business
        WHERE business_id = #{pid}
    </select>

    <select id="getChildrenByPid" resultType="com.xgwc.order.entity.vo.XgwcBusinessTreeVo">
        SELECT business_id, business_name, pid AS parent_business_id
        FROM xgwc_business
        WHERE pid = #{pid}
    </select>

    <select id="getDescendantsByLevel" resultType="com.xgwc.order.entity.vo.XgwcBusinessTreeVo">
        SELECT business_id, business_name, pid AS parent_business_id
        FROM xgwc_business
        WHERE level LIKE #{levelPattern}
    </select>
    <select id="getBusinessLevelById" resultType="java.lang.String">
        SELECT GROUP_CONCAT(level SEPARATOR ',') AS levels
        FROM xgwc_business
        WHERE business_id IN
        <foreach item="id" collection="businessIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="listByIds" resultType="com.xgwc.order.entity.vo.XgwcBusinessTreeVo">
        SELECT business_id, business_name
        FROM xgwc_business
        WHERE brand_owner_id = #{brandId} and business_id IN
        <foreach item="id" collection="businessIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getBusinessOnlyFirst" resultType="com.xgwc.order.entity.vo.XgwcBusinessVo">
        select
            business_id as businessId,
            business_name as businessName
        from xgwc_business
        where pid = #{parentId} and brand_owner_id = #{brandId} and status = 0
    </select>
    <select id="getBusinessSettingList" resultType="com.xgwc.order.entity.dto.BusinessSettingDto">
        select
        b.business_id as businessId,
        b.business_name as businessName,
        GROUP_CONCAT(bs.business_name SEPARATOR ', ') AS businessSettingName,
        ifnull(bs.status, 1) as `status`
        from xgwc_business b
        left join xgwc_business_setting bs on b.business_id = bs.business_id
        <where>
            b.brand_owner_id = #{brandId} and b.pid = 0
            <if test="businessName != null">
                and b.business_name like concat('%',#{businessName},'%')
            </if>
        </where>
        group by b.business_id, b.business_name, bs.status
        order by b.sort
    </select>
    <select id="getBusinessSettingById" resultType="com.xgwc.order.entity.dto.BusinessSettingDto">
        select
            b.business_id as id,
            b.business_name as businessName,
            GROUP_CONCAT(bs.relation_business_id SEPARATOR ', ') AS relationBusinessId,
            ifnull(bs.status, 1) as `status`
        from xgwc_business b
                 left join xgwc_business_setting bs on b.business_id = bs.business_id
        where b.business_id = #{businessId}
        group by b.business_id, b.business_name, bs.status
    </select>
</mapper>