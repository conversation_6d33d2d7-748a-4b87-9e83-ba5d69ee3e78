<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.InboundSalesMapper">
    

    <sql id="selectInboundSalesVo">
        select id, inquiries_number, online_duration, dept_id, franchise_id, brand_id, proceed_date, user_id, is_del, create_by_id, create_by, create_time, update_by_id, update_by, update_time, modify_time from report_inbound_sales
    </sql>

    <select id="selectInboundSalesList" parameterType="com.xgwc.order.entity.vo.InboundSalesQueryVo" resultType="com.xgwc.order.entity.dto.InboundSalesDto">
        select t.id,
               t.inquiries_number,
               t.online_duration,
               t.dept_id,
               t.franchise_id,
               t.brand_id,
               t.proceed_date,
               t.user_id,
               t.create_time,
               f.franchise_name,
               s.name as staffName,
               d.dept_name
        from report_inbound_sales t
        left join franchise f on t.franchise_id = f.id
        left join xgwc_franchise_staff s on s.id = t.user_id
        left join franchise_dept d on d.dept_id = t.dept_id
        <where>
            <if test="id != null "> and t.id = #{id}</if>
            <if test="franchiseId != null "> and t.franchise_id = #{franchiseId}</if>
            <if test="brandId != null "> and t.brand_id = #{brandId}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="proceedDateStart != null and proceedDateEnd != null">
                and t.proceed_date BETWEEN CONCAT(#{proceedDateStart}, ' 00:00:00') AND CONCAT(#{proceedDateEnd}, ' 23:59:59')
            </if>
            <if test="userId != null "> and t.user_id = #{userId}</if>
            <if test="isDel == null"> and t.is_del = 0</if>
            <if test="isDel != null "> and t.is_del = #{isDel}</if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectInboundSalesById" parameterType="Long" resultType="com.xgwc.order.entity.dto.InboundSalesDto">
        <include refid="selectInboundSalesVo"/>
        where id = #{id}
    </select>

    <insert id="insertInboundSales" parameterType="com.xgwc.order.entity.InboundSales">
        insert into report_inbound_sales
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="inquiriesNumber != null">inquiries_number,</if>
            <if test="onlineDuration != null">online_duration,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="proceedDate != null">proceed_date,</if>
            <if test="userId != null">user_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateById != null">update_by_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="inquiriesNumber != null">#{inquiriesNumber},</if>
            <if test="onlineDuration != null">#{onlineDuration},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="proceedDate != null">#{proceedDate},</if>
            <if test="userId != null">#{userId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createById != null">#{createById},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateById != null">#{updateById},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
         </trim>
    </insert>

    <insert id="batchInsert">
        insert into report_inbound_sales
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="inquiriesNumber != null">inquiries_number,</if>
            <if test="onlineDuration != null">online_duration,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="proceedDate != null">proceed_date,</if>
            <if test="userId != null">user_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateById != null">update_by_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
         </trim>
        <foreach collection="inboundSalesList" item="item" separator=",">
            values
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.inquiriesNumber != null">#{item.inquiriesNumber},</if>
                <if test="item.onlineDuration != null">#{item.onlineDuration},</if>
                <if test="item.deptId != null">#{item.deptId},</if>
                <if test="item.franchiseId != null">#{item.franchiseId},</if>
                <if test="item.brandId != null">#{item.brandId},</if>
                <if test="item.proceedDate != null">#{item.proceedDate},</if>
                <if test="item.userId != null">#{item.userId},</if>
                <if test="item.isDel != null">#{item.isDel},</if>
                <if test="item.createById != null">#{item.createById},</if>
                <if test="item.createBy != null">#{item.createBy},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.updateById != null">#{item.updateById},</if>
                <if test="item.updateBy != null">#{item.updateBy},</if>
                <if test="item.updateTime != null">#{item.updateTime},</if>
                <if test="item.modifyTime != null">#{item.modifyTime},</if>
            </trim>
        </foreach>
    </insert>

    <update id="updateInboundSales" parameterType="com.xgwc.order.entity.InboundSales">
        update report_inbound_sales
        <trim prefix="SET" suffixOverrides=",">
            <if test="inquiriesNumber != null">inquiries_number = #{inquiriesNumber},</if>
            <if test="onlineDuration != null">online_duration = #{onlineDuration},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="proceedDate != null">proceed_date = #{proceedDate},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateById != null">update_by_id = #{updateById},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteInboundSalesById" parameterType="Long">
        update report_inbound_sales set is_del = 1 where id = #{id}
    </update>

</mapper>