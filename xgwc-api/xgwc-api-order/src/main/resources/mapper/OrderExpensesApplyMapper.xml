<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.OrderExpensesApplyMapper">

    <sql id="selectOrderExpensesApplyVo">
        select id, refund_amount, actual_amount, order_no, customer_no, apply_user_id, apply_user_name, last_approve_time, attachment, remark, franchise_id, brand_id, status, apply_status, create_time, update_time from order_expenses_apply
    </sql>

    <insert id="insertOrderExpensesApply">
        insert into order_expenses_apply(id, refund_amount, actual_amount, order_no, customer_no, apply_user_id, apply_user_name, last_approve_time, attachment, remark, franchise_id, brand_id, status, apply_status, create_time, update_time,execution_id)
        values(#{id}, #{refundAmount}, #{actualAmount}, #{orderNo}, #{customerNo}, #{applyUserId}, #{applyUserName}, #{lastApproveTime}, #{attachment}, #{remark}, #{franchiseId}, #{brandId}, 0, #{applyStatus}, now(), now(),#{executionId})
    </insert>

    <update id="updateOrderExpensesApply">
        update order_expenses_apply
            <set>
                update_time = now()
                <if test="lastApproveTime != null">
                    ,last_approve_time = #{lastApproveTime}
                </if>
                <if test="status != null">
                    ,status = #{status}
                </if>
                <if test="applyStatus != null">
                    ,apply_status = #{applyStatus}
                </if>
            </set>
          where id = #{id}
    </update>

    <select id="selectOrderExpensesApplyById" resultType="com.xgwc.order.entity.dto.OrderExpensesApplyDto">
        SELECT
            oea.id,
            oea.refund_amount refundAmount,
            oea.actual_amount actualAmount,
            oea.order_no orderNo,
            oea.customer_no customerNo,
            oea.apply_user_id applyUserId,
            oea.apply_user_name applyUserName,
            oea.last_approve_time lastApproveTime,
            oea.attachment,
            oea.remark,
            oea.franchise_id franchiseId,
            oea.brand_id brandId,
            oea.STATUS,
            oea.apply_status,
            oea.create_time,
            oea.update_time,
            oea.execution_id,
            f.franchise_name franchiseName,
            ifnull(xbo.company_simple_name, xbo.company_name) brandName
        FROM
            order_expenses_apply oea
                left join franchise f on oea.franchise_id = f.id
                left join xgwc_brand_owner xbo on xbo.brand_id = oea.brand_id
        where oea.id = #{id} and oea.`status` = 0
    </select>

    <select id="findAllOrderExpensesApply" resultType="com.xgwc.order.entity.dto.OrderExpensesDto">
        SELECT t.id,t.actual_amount,t.refund_amount,(t.actual_amount - t.refund_amount) differenceAmount,
               t.order_no,t.customer_no,t.apply_user_name,
               t.execution_id, t.apply_status,t.create_time,t.last_approve_time,
               xo.order_date,f.franchise_name,xo.sale_man_name, xft.task_id_,
               ifnull(xbo.company_simple_name,xbo.company_name) brand_name
        FROM order_expenses_apply t
        LEFT JOIN xgwc_order xo ON t.order_no = xo.order_no
        LEFT JOIN xgwc_flow_execution xfe on t.execution_id = xfe.id
        LEFT JOIN xgwc_flow_task xft on t.execution_id = xft.execution_id_
        LEFT JOIN franchise f on t.franchise_id = f.id
        LEFT JOIN xgwc_brand_owner xbo on t.brand_id = xbo.brand_id
        WHERE xfe.flow_value = 'expense'
        <if test="franchiseId != null">and t.franchise_id = #{franchiseId}</if>
        <if test="applyStatus != null and applyStatus != ''">and t.apply_status = #{applyStatus}</if>
        <if test="brandIdList != null and brandIdList.size() > 0">
            and t.brand_id in
            <foreach item="item" collection="brandIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword != ''">
            and (
            t.order_no like concat('%', #{keyword}, '%')
            or xo.taobao_id like concat('%', #{keyword}, '%')
            or t.id like concat('%', #{keyword}, '%')
            or xo.sale_man_name like concat('%', #{keyword}, '%')
            or t.apply_user_name like concat('%', #{keyword}, '%')
            )
        </if>
        <if test="startTime != null and endTime != null">
            and t.create_time BETWEEN CONCAT(#{startTime}, ' 00:00:00') AND CONCAT(#{endTime}, ' 23:59:59')
        </if>
        order by t.create_time desc
    </select>

    <select id="getOrderRefundApplyByOrderNo" resultType="com.xgwc.order.entity.dto.OrderExpensesApplyDto">
        <include refid="selectOrderExpensesApplyVo"/>
        where order_no = #{orderNo} and apply_status = 'ING'
        order by create_time desc
        limit 1
    </select>

    <select id="findOrderExpensesApplyByOrderNo" resultType="com.xgwc.order.entity.dto.OrderExpensesDto">
        <include refid="selectOrderExpensesApplyVo"/>
        where order_no = #{orderNo} and apply_status in ('ING','PASS')
    </select>

</mapper>