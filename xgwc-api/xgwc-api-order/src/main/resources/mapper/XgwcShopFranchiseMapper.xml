<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcShopFranchiseMapper">
    

    <sql id="selectXgwcShopFranchiseVo">
        select
            s.shop_id,
            s.shop_name,
            s.brand_owner_id,
            ifnull( xbo.company_simple_name, xbo.company_name ) brand_owner_name,
            s.brand_id,
            b.brand_name,
            s.channel_id,
            c.channel_name,
            s.status,
            bs.business_name,
            s.create_time review_time
        from
            xgwc_shop s
        left join xgwc_brand b on b.brand_id = s.brand_id
        left join xgwc_channel c on c.channel_id = s.channel_id
        left join xgwc_brand_owner xbo on s.brand_owner_id = xbo.brand_id
        left join (
            select
                sb.shop_id,
                group_concat( b.business_name,if(fd.dept_name is null, '', concat('(',fd.dept_name ,')'))) business_name
            from
                xgwc_shop_business sb
            left join franchise_dept fd on sb.franchise_dept_id = fd.dept_id
            left join xgwc_business b on sb.business_id = b.business_id
            group by sb.shop_id
        ) bs on s.shop_id = bs.shop_id
        where s.is_del = 0 and b.is_del = 0
    </sql>

    <select id="selectXgwcShopFranchiseList" parameterType="com.xgwc.order.entity.vo.XgwcShopFranchiseQueryVo" resultType="com.xgwc.order.feign.entity.XgwcShopFranchiseDto">
        <include refid="selectXgwcShopFranchiseVo"/>
        <if test="shopName != null and shopName != ''">
            and s.shop_name LIKE CONCAT('%', #{shopName}, '%')
        </if>
        <if test="brandOwnerId != null">
            and s.brand_owner_id = #{brandOwnerId}
        </if>
        <if test="franchiseId != null">
            and s.franchise_id = #{franchiseId}
        </if>
        <if test="shopId != null">
            and s.shop_id = #{shopId}
        </if>
        <if test="status != null">
            and s.status = #{status}
        </if>
        <if test="deptId != null">
            and EXISTS (
                select 1 from xgwc_shop_business sb where sb.shop_id = s.shop_id
                sb.franchise_dept_id = #{deptId}
            )
        </if>
    </select>

    <select id="selectXgwcShopFranchiseByShopId" parameterType="Long" resultType="com.xgwc.order.feign.entity.XgwcShopFranchiseDto">
        select
            s.shop_id,
            s.shop_name,
            s.brand_owner_id,
            ifnull( xbo.company_simple_name, xbo.company_name ) brand_owner_name,
            s.brand_id,
            b.brand_name,
            s.channel_id,
            c.channel_name,
            s.status,
            s.create_time review_time,
            s.pay_type,
            s.platform_shop_id,
            s.shop_deposit,
            ifnull( ci.company_simple_name, ci.company_name ) company_info_name
        from
            xgwc_shop s
        left join xgwc_brand b on b.brand_id = s.brand_id
        left join xgwc_channel c on c.channel_id = s.channel_id
        left join xgwc_brand_owner xbo on s.brand_owner_id = xbo.brand_id
        left join xgwc_company_info ci on s.company_info_id = ci.id
        where s.shop_id = #{shopId}
    </select>

</mapper>