<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.IncomLineSeasMapper">
    

    <sql id="selectIncomLineSeasVo">
        SELECT
            l.id,
            l.receiver,
            l.receive_time,
            l.in_time,
            l.taobao_id,
            l.wechat_tag,
            l.tel,
            l.shop_id,
            l.shop_name,
            l.valid_dic_code,
            l.valid_dic_name,
            l.order_id,
            l.order_no,
            l.state_dic_code,
            l.state_dic_name,
            l.next_time,
            l.need_info,
            l.sale_id,
            l.sale_name,
            l.dept_id,
            l.dept_name,
            l.track_name,
            l.brand_id,
            l.franchise_id,
            l.plan_money,
            l.recycle_time,
            l.recycle_num,
            ifnull(bo.company_simple_name,bo.company_name) brand_name,
            ifnull(fo.company_simple_name,fo.company_name) franchise_name
        FROM
            incom_line l
        left join xgwc_brand_owner bo on l.brand_id = bo.brand_id
        left join franchise_owner fo on l.franchise_id = fo.franchise_id and l.brand_id = fo.brand_id
    </sql>

    <select id="selectIncomLineSeasById" parameterType="Long" resultType="com.xgwc.order.entity.dto.IncomLineSeasDto">
        <include refid="selectIncomLineSeasVo"/>
        where l.id = #{id}
    </select>

    <select id="selectIncomLineSeasByIds" parameterType="Long" resultType="com.xgwc.order.entity.dto.IncomLineSeasDto">
        <include refid="selectIncomLineSeasVo"/>
        where l.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="collect" parameterType="com.xgwc.order.entity.IncomLine">
        update incom_line set
            sale_id = #{saleId},
            sale_name = #{saleName},
            dept_id = #{deptId},
            dept_name = #{deptName},
            receiver = #{receiver},
            receive_time = #{receiveTime},
            recycle_time = #{recycleTime},
            recycle_num = recycle_num + 1
        where id = #{id}
    </update>

</mapper>