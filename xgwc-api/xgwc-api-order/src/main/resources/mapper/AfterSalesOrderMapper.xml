<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.AfterSalesOrderMapper">

    <sql id="selectOrderRefundApplyVo">
        select id, refund_type, refund_reason_code, refund_reason, order_no, pre_amount, last_amount, remark, pre_commission, last_commission, apply_user_id,
               apply_user_name, franchise_id, brand_id, last_approve_time, apply_status, status, create_time, update_time from order_refund_apply
    </sql>

    <select id="list" resultType="com.xgwc.order.entity.dto.AfterSalesOrderDto">
        select
        t.id,
        TIMESTAMPDIFF(MINUTE, t.create_time, now()) as minuteTime,
        t.order_no as orderNo,
        t.refund_type as refundType,
        t.last_amount as lastAmount,
        t.pre_amount as preAmount,
        t.refund_reason as refundReason,
        t.remark as remark,
        t.franchise_id as franchiseId,
        t.customer_phone as customerPhone,
        t.platform_refund_time as platformRefundTime,
        xo.amount as amount,
        xo.taobao_id as taobaoId,
        f.franchise_name as franchiseName,
        xo.sale_man_name as saleManName,
        xo.state_dic_name as businessName,
        -- 默认退款
        case
            when TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &lt;= 480
                and TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &gt; 60
            then 1
            else 0
        end as willAutoRefund,
        -- 超时未领取
        case
            when TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &lt;= 60
            then 1
            else 0
        end as timeoutUnreceived
        from order_refund_apply t
        left join xgwc_order xo on xo.order_no = t.order_no
        left join franchise f on f.id = t.franchise_id
        where t.refund_type in (1,2)
        and t.after_check_status = 0
        and t.is_receive = 0
        and t.brand_id = #{brandId}
        <if test="type != null and type == 2">
            and TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &gt; 60
            and TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &lt;= 480
        </if>
        <if test="type != null and type == 3">
            and (
                now() &gt;= t.platform_refund_time
                or TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &gt;= 60
            )
        </if>
        order by t.create_time desc
    </select>

    <select id="count" resultType="java.lang.Integer">
        select COUNT(1)
        from order_refund_apply
        where refund_type in (1,2)
        and after_check_status = 0
        and is_receive = 0
        and brand_id = #{brandId}
    </select>

    <select id="countWillRefund" resultType="java.lang.Integer">
        select count(1)
        from order_refund_apply t
        where t.refund_type in (1,2)
        and t.after_check_status = 0
        and t.is_receive = 0
        and t.brand_id = #{brandId}
        and TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &gt; 60
        and TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &lt;= 480
    </select>

    <select id="countTimeoutUnreceived" resultType="java.lang.Integer">
        select count(1)
        from order_refund_apply t
        where t.refund_type in (1,2)
        and t.after_check_status = 0
        and t.is_receive = 0
        and t.brand_id = #{brandId}
        and (
            now() &gt;= t.platform_refund_time
            or TIMESTAMPDIFF(MINUTE, now(), t.platform_refund_time) &lt;= 60
        )
    </select>

    <select id="countReceived" resultType="java.lang.Integer">
        select count(1)
        from xgwc_after_agency_audit
        where receiver_id = #{userId}
        and DATE(create_time) = CURDATE()
    </select>

</mapper>