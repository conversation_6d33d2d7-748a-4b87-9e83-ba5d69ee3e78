<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.AfterSalesExecDataMapper">
    

    <sql id="selectAfterSalesExecDataVo">
        select business_id, id,key_name, key_value from xgwc_after_sales_exec_data
    </sql>

    <select id="selectAfterSalesExecDataById" parameterType="Long" resultType="com.xgwc.order.entity.dto.AfterSalesExecDataDto">
        <include refid="selectAfterSalesExecDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertAfterSalesExecData" parameterType="com.xgwc.order.entity.AfterSalesExecData">
        insert into xgwc_after_sales_exec_data
        (business_id, key_name, key_value,create_by)
        values
        <foreach item="item" collection="list" separator="," >
            (#{item.businessId},#{item.keyName},#{item.keyValue},#{item.createBy})
        </foreach>
    </insert>

</mapper>