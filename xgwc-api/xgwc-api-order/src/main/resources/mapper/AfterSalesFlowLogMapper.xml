<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.AfterSalesFlowLogMapper">
    
    <sql id="selectAfterSalesFlowLogVo">
        select business_id, create_by, create_time, id, node_type, operator_id, operator_name, remark, sort_order, status, update_time from xgwc_after_sales_flow_log
    </sql>

    <select id="selectAfterSalesFlowLogByBusinessId" parameterType="Long" resultType="com.xgwc.order.entity.dto.AfterSalesFlowLogDto">
        <include refid="selectAfterSalesFlowLogVo"/>
        where business_id = #{businessId}
        order by sort_order asc
    </select>

    <insert id="insertAfterSalesFlowLog" parameterType="com.xgwc.order.entity.AfterSalesFlowLog">
        insert into xgwc_after_sales_flow_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessId != null">business_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="id != null">id,</if>
            <if test="nodeType != null">node_type,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="remark != null">remark,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessId != null">#{businessId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="id != null">#{id},</if>
            <if test="nodeType != null">#{nodeType},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

</mapper>