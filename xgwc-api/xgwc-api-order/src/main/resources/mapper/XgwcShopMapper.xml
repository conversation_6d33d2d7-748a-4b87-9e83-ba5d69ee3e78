<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.order.dao.XgwcShopMapper">
    <insert id="saveXgwcShop" useGeneratedKeys="true" keyProperty="shopId">
        INSERT INTO `xgwc_sass`.`xgwc_shop` (`shop_name`, `brand_id`, `brand_owner_id`,`franchise_id`, `franchise_owner_id`,`channel_id`, `dept_id`,`pay_type` ,
                                             `platform_shop_id`,`shop_deposit`, `company_info_id`, `create_by`, `create_time`)
        VALUES (#{xgwcShopDto.shopName}, #{xgwcShopDto.brandId}, #{xgwcShopDto.brandOwnerId}, #{xgwcShopDto.franchiseId}, #{xgwcShopDto.franchiseOwnerId},
                #{xgwcShopDto.channelId},#{xgwcShopDto.deptId}, #{xgwcShopDto.payType}, #{xgwcShopDto.platformShopId}, #{xgwcShopDto.shopDeposit},
                #{xgwcShopDto.companyInfoId},#{xgwcShopDto.createBy},now());
    </insert>

    <insert id="saveXgwcShopBusiness">
        INSERT INTO `xgwc_sass`.`xgwc_shop_business` (`business_id`, `dept_id`, `shop_id`,`is_brand_order`,
        `create_by`, `create_time`, `update_by`, `update_time`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.businessId}, #{item.deptId}, #{item.shopId},
            #{item.isBrandOrder},#{item.createBy}, now(), #{item.updateBy}, now())
        </foreach>
    </insert>

    <insert id="seveXgwcCompanyShop">
        INSERT INTO `xgwc_sass`.`xgwc_company_shop` (`company_id`, `shop_id`, `create_by`, `create_time`)
        VALUES (#{xgwcShopDto.companyInfoId}, #{xgwcShopDto.shopId}, #{xgwcShopDto.createBy}, now());
    </insert>

    <update id="updateXgwcShopById">
        UPDATE `xgwc_sass`.`xgwc_shop`
        <set>
            <if test="xgwcShopDto.shopName != null and xgwcShopDto.shopName != ''">
                `shop_name` = #{xgwcShopDto.shopName},
            </if>
            <if test="xgwcShopDto.franchiseId != null">
                `franchise_id` = #{xgwcShopDto.franchiseId},
            </if>
            <if test="xgwcShopDto.franchiseOwnerId != null">
                `franchise_owner_id` = #{xgwcShopDto.franchiseOwnerId},
            </if>
            <if test="xgwcShopDto.brandId != null">
                `brand_id` = #{xgwcShopDto.brandId},
            </if>
            <if test="xgwcShopDto.channelId != null">
                `channel_id` = #{xgwcShopDto.channelId},
            </if>
            <if test="xgwcShopDto.deptId != null">
                `dept_id` = #{xgwcShopDto.deptId},
            </if>
            <if test="xgwcShopDto.payType != null">
                `pay_type` = #{xgwcShopDto.payType},
            </if>
            <if test="xgwcShopDto.platformShopId != null">
                `platform_shop_id` = #{xgwcShopDto.platformShopId},
            </if>
            <if test="xgwcShopDto.shopDeposit != null">
                `shop_deposit` = #{xgwcShopDto.shopDeposit},
            </if>
            <if test="xgwcShopDto.companyInfoId != null">
                `company_info_id` = #{xgwcShopDto.companyInfoId},
            </if>
            <if test="xgwcShopDto.updateBy != null and xgwcShopDto.updateBy != ''">
                `update_by` = #{xgwcShopDto.updateBy},
            </if>
            `update_time` = NOW()
        </set>
        WHERE `shop_id` = #{xgwcShopDto.shopId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`xgwc_shop`
        SET `status` = #{status}
        WHERE `shop_id` = #{shopId}
    </update>
    <update id="updateXgwcCompanyShop">
        UPDATE `xgwc_sass`.`xgwc_company_shop`
        SET `company_id` = #{companyInfoId}, `update_by` = #{updateBy}, `update_time` = NOW()
        WHERE `shop_id` = #{shopId}
    </update>
    <update id="updateXgwcShopManagerById">
        UPDATE `xgwc_sass`.`xgwc_shop`
        SET `manager_id` = #{managerId}
        WHERE `shop_id` = #{shopId}
    </update>

    <delete id="deleteXgwcShopBusiness">
        DELETE
        FROM xgwc_shop_business
        WHERE shop_id = #{shopId}
    </delete>

    <select id="listPlatformShopIdByLikeName" resultType="java.lang.String">
        select distinct platform_shop_id
        from xgwc_shop
        where brand_owner_id = #{brandId}
          and platform_shop_id is not null
          and shop_name like concat('%', #{shopName}, '%')
    </select>
    <select id="listByPlatformShopIds" resultType="com.xgwc.order.entity.vo.XgwcShopVo">
        select shop_id as shopId, shop_name as shopName, platform_shop_id as platformShopId
        from xgwc_shop
        where brand_owner_id = #{brandId} and platform_shop_id in
        <foreach item="platformShopId" collection="platformShopIdList" open="(" separator="," close=")">
            #{platformShopId}
        </foreach>
    </select>

    <select id="getXgwcShopList" resultType="com.xgwc.order.entity.vo.XgwcShopVo">
        SELECT
        shop.*,
        GROUP_CONCAT(
        CONCAT(bus.business_name, '(', dept.dept_name, ')')
        SEPARATOR ', '
        ) AS businessNames,
        ifnull(fo.company_simple_name, fo.company_name) companyName,
        b.brand_name as brandName,
        c.channel_name as channelName,
        ifnull(xbo.company_simple_name, xbo.company_name) brandOwnerName
        FROM xgwc_shop shop
        LEFT JOIN xgwc_shop_business s ON s.shop_id = shop.shop_id
        LEFT JOIN xgwc_brand b ON b.brand_id = shop.brand_id
        LEFT JOIN xgwc_channel c ON c.channel_id = shop.channel_id
        LEFT JOIN xgwc_business bus ON s.business_id = bus.business_id
        LEFT JOIN xgwc_brand_dept dept ON s.dept_id = dept.dept_id
        LEFT JOIN xgwc_brand_owner xbo ON shop.brand_owner_id = xbo.brand_id
        LEFT JOIN franchise_owner fo ON fo.franchise_id = shop.franchise_id and fo.brand_id = shop.brand_owner_id
        <where>
            shop.brand_owner_id = #{xgwcShopParam.brandOwnerId}

            <if test="xgwcShopParam.shopName != null and xgwcShopParam.shopName != ''">
                AND shop.shop_name = #{xgwcShopParam.shopName}
            </if>
            <if test="xgwcShopParam.brandId != null">
                AND shop.brand_id = #{xgwcShopParam.brandId}
            </if>
            <if test="xgwcShopParam.channelId != null">
                AND shop.channel_id = #{xgwcShopParam.channelId}
            </if>
            <if test="xgwcShopParam.deptId != null">
                AND dept.dept_id = #{xgwcShopParam.deptId}
            </if>
            <if test="xgwcShopParam.status != null">
                AND shop.status = #{xgwcShopParam.status}
            </if>
            <if test="xgwcShopParam.franchiseId != null">
                AND shop.franchise_id = #{xgwcShopParam.franchiseId}
            </if>
        </where>
        GROUP BY
            shop.shop_id,
            brandName,
            channelName,
            brandOwnerName,
            companyName
        ORDER BY
            shop.shop_id DESC
    </select>

    <select id="getByPlatformShopId" resultType="com.xgwc.order.entity.XgwcShop">
        select shop_id, shop_name, dept_id, franchise_id
        from xgwc_shop
        where brand_owner_id in
            <foreach item="id" collection="brandOwnerIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
          and platform_shop_id = #{platformShopId}
    </select>

    <select id="getXgwcShopById" resultType="com.xgwc.order.entity.vo.XgwcShopVo">
        select xs.shop_id     as shopId,
               xs.shop_name   as shopName,
               xs.brand_owner_id brand_owner_id,
               ifnull(bo.company_simple_name,bo.company_name) brand_owner_name,
               xs.brand_id    as brandId,
               xb.brand_name  as brandName,
               xs.franchise_id as franchiseId,
               xs.channel_id  as channelId,
               xc.channel_name as channelName,
               xs.dept_id     as deptId,
               xd.dept_name   as deptName,
               xs.pay_type    as payType,
               xs.platform_shop_id as platformShopId,
               xs.shop_deposit  as shopDeposit,
               xs.company_info_id as companyInfoId
        from xgwc_shop xs
         left join xgwc_brand xb on xs.brand_id = xb.brand_id
         left join xgwc_channel xc on xs.channel_id = xc.channel_id
         left join xgwc_brand_dept xd on xs.dept_id = xd.dept_id
         left join xgwc_brand_owner bo on xs.brand_owner_id = bo.brand_id
        where shop_id = #{shopId}
    </select>

    <select id="getXgwcShopBusiness" resultType="com.xgwc.order.entity.vo.XgwcShopBusinessVo">
        select shop_channel_id as shopChannelId,
               xb.business_id     as businessId,
               xb.business_name  as businessName,
               xb.pid pbusiness_id,
               xd.dept_id         as deptId,
               xd.dept_name       as deptName,
               shop_id         as shopId
        from xgwc_shop_business xs
         left join xgwc_business xb on xs.business_id = xb.business_id
         left join xgwc_brand_dept xd on xs.dept_id = xd.dept_id
        where shop_id = #{shopId}
    </select>

    <select id="findShopByIds" resultType="com.xgwc.order.entity.vo.XgwcShopVo">
        select  shop_id     as shopId,
            shop_name   as shopName,
            brand_id    as brandId,
            channel_id  as channelId,
            dept_id     as deptId,
            status,
            create_time as createTime
        from xgwc_shop
        where shop_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getShopByFranchiseId" resultType="com.xgwc.order.entity.dto.XgwcShopDto">
        select xs.shop_id     as shopId,
               xs.shop_name   as shopName,
               xs.brand_id    as brandId,
               xb.brand_name  as brandName,
               xs.channel_id  as channelId,
               xc.channel_name as channelName,
               xs.status      as status
        from xgwc_shop xs
                 left join xgwc_brand xb on xs.brand_id = xb.brand_id
                 left join xgwc_channel xc on xs.channel_id = xc.channel_id
        where xs.franchise_id = #{franchiseId} and xs.brand_owner_id = #{brandId}
    </select>
    <select id="listPrincipalInfoByBusinessIdList" resultType="com.xgwc.order.entity.vo.BusinessPrincipalInfoVO">
        select xsb.business_id, xfs.name, xfs.login_phone
        from xgwc_shop_business xsb
            left join franchise_dept fd on xsb.franchise_dept_id = fd.dept_id
            left join xgwc_franchise_staff xfs on fd.dept_id = xfs.franchise_id and xsb.franchise_dept_id = xfs.dept_id
        where fd.franchise_id = #{franchiseId} and xsb.business_id in
        <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
            #{businessId}
        </foreach>
    </select>
    <select id="getAuthorizeBrandIdsByServiceId" resultType="java.lang.Long">
        SELECT
            brand_id
        FROM
            `xgwc_service_authorize` t
        WHERE
            t.service_id = #{serviceId}
          AND t.`status` = 0
        GROUP BY t.brand_id
    </select>
    <select id="selectShopListBrandIds" resultType="com.xgwc.order.entity.dto.XgwcShopDto">
        SELECT
        shop.*,
        GROUP_CONCAT(bus.business_name SEPARATOR ', ') AS businessNames,
        ifnull(fo.company_simple_name, fo.company_name) companyName,
        b.brand_name as brandName,
        c.channel_name as channelName,
        xss.stage_name as managerName,
        ifnull(xbo.company_simple_name, xbo.company_name) brandOwnerName
        FROM xgwc_shop shop
        LEFT JOIN xgwc_shop_business s ON s.shop_id = shop.shop_id
        LEFT JOIN xgwc_brand b ON b.brand_id = shop.brand_id
        LEFT JOIN xgwc_channel c ON c.channel_id = shop.channel_id
        LEFT JOIN xgwc_business bus ON s.business_id = bus.business_id
        LEFT JOIN xgwc_brand_owner xbo ON shop.brand_owner_id = xbo.brand_id
        LEFT JOIN xgwc_service_staff xss ON shop.manager_id = xss.bind_user_id
        LEFT JOIN franchise_owner fo ON fo.franchise_id = shop.franchise_id and fo.brand_id = shop.brand_owner_id
        <where>
            shop.brand_owner_id in
            <foreach item="id" collection="xgwcShopParam.brandIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="xgwcShopParam.shopName != null and xgwcShopParam.shopName != ''">
                AND shop.shop_name = #{xgwcShopParam.shopName}
            </if>
            <if test="xgwcShopParam.managerId != null">
                AND shop.manager_id = #{xgwcShopParam.managerId}
            </if>
            <if test="xgwcShopParam.status != null">
                AND shop.status = #{xgwcShopParam.status}
            </if>
            <if test="xgwcShopParam.brandOwnerId != null">
                AND shop.brand_owner_id = #{xgwcShopParam.brandOwnerId}
            </if>
        </where>
        GROUP BY
        shop.shop_id,
        brandName,
        channelName,
        brandOwnerName,
        managerName,
        companyName
        ORDER BY
        shop.shop_id DESC
    </select>

</mapper>