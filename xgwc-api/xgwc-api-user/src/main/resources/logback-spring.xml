<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="server_name" source="spring.cloud.client.hostname"/>
    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="server_ip" source="spring.cloud.client.ip-address"/>
    <springProperty scope="context" name="profile" source="spring.profiles.active" defaultValue="default"/>
    <property name="MAX_HISTORY" value="15"/>
    <!-- 定义日志文件总大小 -->
    <property name="TOTAL_SIZE_CAP" value="10GB"/>
    <property name="LOG_PATH" value="${user.home}/logs"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </layout>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 基于时间的滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${appName}/output/output.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志文件保留天数 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <!-- 日志归档文件总大小 -->
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <!-- 日志输出格式 -->
        <encoder>
            <pattern>[%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36}: %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 请求日志 -->
    <appender name="REQUEST" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${appName}/access/access.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <charset>utf-8</charset>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.xgwc.common.spring.web.accesslog.AccessLogInterceptor" level="INFO" additivity="false">
        <appender-ref ref="REQUEST"/>
    </logger>

    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>