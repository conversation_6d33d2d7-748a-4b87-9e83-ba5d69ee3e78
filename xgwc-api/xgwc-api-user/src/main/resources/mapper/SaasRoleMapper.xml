<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SaasRoleMapper">

    <delete id="deleteRoleMenu">
        delete from saas_role_menu where role_id = #{roleId}
    </delete>
    <delete id="deleteRoleData">
        delete from saas_role_data where role_id = #{roleId}
    </delete>
    <delete id="deleteRoleScope">
        delete from saas_role_scope where role_id = #{roleId}
    </delete>

    <select id="selectSaasRoleList" parameterType="com.xgwc.user.entity.vo.SaasRoleQueryVo"
            resultType="com.xgwc.user.entity.dto.SaasRoleDto">
        SELECT
        r.role_id AS roleId,
        r.role_name AS roleName,
        r.sort,
        r.is_default AS isDefault,
        r.role_scope AS roleScope,
        r.effective_scope AS effectiveScope,
        GROUP_CONCAT(
        COALESCE(
        ifnull(bo.company_simple_name, bo.company_name),
        ifnull( fo.company_simple_name, fo.company_name ),
        so1.company_name,
        so2.company_name) SEPARATOR '、'
        ) as companyName,
        r.status,
        r.create_by AS createBy,
        r.create_time AS createTime
        FROM /* r.role_scope 角色权限范围:0-品普 1-品管 2-加普 3-加管 4-服普 5-服管 6-销普 7-销管 8-saas普 9-saas管*/
        saas_role r
        LEFT JOIN saas_role_scope rs ON r.role_id = rs.role_id
        LEFT JOIN xgwc_brand_owner bo ON rs.effective_scope = bo.brand_id and r.role_scope IN (0,1)
        LEFT JOIN franchise_owner fo ON rs.effective_scope = fo.franchise_id and r.role_scope IN (2,3)
        LEFT JOIN xgwc_service_owner so1 ON rs.effective_scope = so1.id and so1.service_type = 1 and r.role_scope IN (4,5)
        LEFT JOIN xgwc_service_owner so2 ON rs.effective_scope = so2.id and so2.service_type = 2 and r.role_scope IN (6,7)
        <where>
            <if test="roleName != null  and roleName != ''">and r.role_name like concat('%', #{roleName}, '%')</if>
            <if test="effectiveScope != null ">and rs.effective_scope = #{effectiveScope}</if>
            <if test="status != null ">and r.status = #{status}</if>
            and r.is_del = 0
        </where>
        GROUP BY roleId,roleName,sort,isDefault,roleScope,status,createBy,createTime
        ORDER BY createTime DESC
    </select>

    <select id="selectSaasRoleByRoleId" parameterType="Long" resultType="com.xgwc.user.entity.dto.SaasRoleDto">
        SELECT
            r.role_id AS roleId,
            r.role_name AS roleName,
            r.sort AS sort,
            r.is_default AS isDefault,
            r.role_scope AS roleScope,
            r.business_system AS businessSystem
        FROM
            saas_role r
        WHERE
            r.role_id = #{roleId}
    </select>
    <select id="getRoleMenusById" resultType="com.xgwc.user.entity.vo.SysRoleMenuVo">
        SELECT
            frd.`id` AS id,
            frd.`role_id` AS roleId,
            frd.`menu_id` AS menuId,
            sm.pid,
            sm.`name` AS menuName
        FROM `xgwc_sass`.`saas_role_menu` frd
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frd.menu_id
        WHERE `role_id` = #{roleId}
    </select>
    <select id="selectLastLevelMenu" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM sys_menu
        WHERE pid = #{menuId} and is_del = 0 and FIND_IN_SET(#{isFlag},model_type)
    </select>
    <select id="getRoleDataById" resultType="com.xgwc.user.entity.vo.SysRoleDataVo">
        SELECT
            xrd.`id` AS id,
            xrd.`role_id` AS roleId,
            xrd.`menu_id` AS menuId,
            sm.`name` AS menuName,
            sm.pid,
            xrd.`data_scope` AS dataScope,
            xrd.`dept_id` AS deptId,
            xrd.`time_limit` AS timeLimit,
            xrd.`data_masking` AS dataMasking
        FROM `xgwc_sass`.`saas_role_data` xrd
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = xrd.menu_id
        WHERE `role_id` = #{roleId}
    </select>
    <select id="selectLastLevelMenuDate" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM sys_menu
        WHERE pid = #{menuId} and is_del = 0 and FIND_IN_SET(#{isFlag},model_type) and type IN ( 'catalog', 'menu' )
    </select>
    <select id="selectRoleScopeById" resultType="com.xgwc.user.entity.dto.SaasRoleDto$SaasRoleScopeDto">
        SELECT
            rs.`effective_scope` AS effectiveScope,
            COALESCE(
                    ifnull(bo.company_simple_name, bo.company_name),
                    ifnull( fo.company_simple_name, fo.company_name ),
                    so1.company_name,
                    so2.company_name) AS companyName
        FROM saas_role_scope  rs
                 LEFT JOIN saas_role r ON rs.role_id = r.role_id
                 LEFT JOIN xgwc_brand_owner bo ON rs.effective_scope = bo.brand_id and r.role_scope IN (0,1)
                 LEFT JOIN franchise_owner fo ON rs.effective_scope = fo.franchise_id and r.role_scope IN (2,3)
                 LEFT JOIN xgwc_service_owner so1 ON rs.effective_scope = so1.id and so1.service_type = 1 and r.role_scope IN (4,5)
                 LEFT JOIN xgwc_service_owner so2 ON rs.effective_scope = so2.id and so2.service_type = 2 and r.role_scope IN (6,7)
        WHERE rs.`role_id` = #{roleId}
    </select>

    <insert id="insertSaasRole" parameterType="com.xgwc.user.entity.vo.SaasRoleVo">
        insert into saas_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleName != null">role_name,</if>
            <if test="sort != null">sort,</if>
            <if test="isFlag != null">is_flag,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="roleScope != null">role_scope,</if>
            <if test="effectiveScope != null">effective_scope,</if>
            <if test="businessSystem != null">business_system,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleName != null">#{roleName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="isFlag != null">#{isFlag},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="roleScope != null">#{roleScope},</if>
            <if test="effectiveScope != null">#{effectiveScope},</if>
            <if test="businessSystem != null">#{businessSystem},</if>
            <if test="createBy != null">#{createBy},</if>
            now()
        </trim>
    </insert>

    <insert id="saveRoleMenu">
        INSERT INTO `xgwc_sass`.`saas_role_menu` (`role_id`, `menu_id`, `create_by`, `create_time`)
        VALUES
        <foreach collection="sysRoleMenuVoList" item="roleMenu" separator=",">
            (#{roleMenu.roleId}, #{roleMenu.menuId}, #{roleMenu.createBy}, now())
        </foreach>
    </insert>

    <insert id="saveRoleDataScope">
        INSERT INTO `xgwc_sass`.`saas_role_data` (`role_id`, `menu_id`, `data_scope`, `dept_id`, `time_limit`, `data_masking`, `create_by`, `create_time`)
        VALUES
            (#{sysRoleDataVo.roleId}, #{sysRoleDataVo.menuId}, #{sysRoleDataVo.dataScope}, #{sysRoleDataVo.deptId},
             #{sysRoleDataVo.timeLimit}, #{sysRoleDataVo.dataMasking},
             #{sysRoleDataVo.createBy}, now())
    </insert>

    <insert id="saveRoleDataScopeDeptId">
        INSERT INTO `xgwc_sass`.`saas_role_data` (`role_id`, `menu_id`, `data_scope`, `dept_id`, `time_limit`, `data_masking`, `create_by`, `create_time`)
        VALUES
        <foreach collection="dataMenu.deptIds" item="item" separator=",">
            (#{dataMenu.roleId}, #{dataMenu.menuId}, #{dataMenu.dataScope}, #{item}, #{dataMenu.timeLimit}, #{dataMenu.dataMasking}, #{dataMenu.createBy}, now())
        </foreach>
    </insert>
    <insert id="insertSaasRoleScope">
        INSERT INTO `xgwc_sass`.`saas_role_scope` (`role_id`, `effective_scope`, `create_by`, `create_time`)
        VALUES
        <foreach collection="saasRole.effectiveScope" item="item" separator=",">
            (#{saasRole.roleId}, #{item}, #{saasRole.createBy}, now())
        </foreach>
    </insert>

    <update id="updateSaasRole" parameterType="com.xgwc.user.entity.vo.SaasRoleVo">
        update saas_role
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleName != null">role_name = #{roleName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="roleScope != null">role_scope = #{roleScope},</if>
            <if test="effectiveScope != null">effective_scope = #{effectiveScope},</if>
            <if test="businessSystem != null">business_system = #{businessSystem},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where role_id = #{roleId}
    </update>

    <update id="deleteSaasRoleByRoleId" parameterType="Long">
        update saas_role
        set is_del = 1
        where role_id = #{roleId}
    </update>
</mapper>