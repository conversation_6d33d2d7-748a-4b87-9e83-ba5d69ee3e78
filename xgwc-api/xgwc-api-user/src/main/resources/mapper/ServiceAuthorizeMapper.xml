<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.ServiceAuthorizeMapper">

    <sql id="selectServiceAuthorizeVo">
        select id, service_id, brand_id, status, is_del, create_by, create_time, update_by, update_time, modify_time from xgwc_service_authorize
    </sql>

    <select id="selectServiceAuthorizeById" parameterType="Long" resultType="com.xgwc.user.entity.dto.ServiceAuthorizeDto">
        <include refid="selectServiceAuthorizeVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceAuthorizeByBrandId" resultType="com.xgwc.user.entity.dto.ServiceAuthorizeDto">
        <include refid="selectServiceAuthorizeVo"/>
        where brand_id = #{brandId}
        and is_del = 0 and status = 0
    </select>

    <select id="getServiceBrandIdList" resultType="java.lang.Long">
        select brand_id from xgwc_service_authorize where service_id = #{serviceId}
    </select>
    <select id="getServiceBrandList" resultType="com.xgwc.user.feign.entity.BrandOwnerDto">
        select xbo.brand_id, xbo.company_name from xgwc_service_authorize xsa left join xgwc_brand_owner xbo on xsa.brand_id = xbo.brand_id where xsa.service_id = #{serviceId}
    </select>

    <insert id="insertServiceAuthorize" parameterType="com.xgwc.user.entity.ServiceAuthorize">
        insert into xgwc_service_authorize
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="status != null">status,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="status != null">#{status},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
         </trim>
    </insert>

    <update id="updateServiceAuthorize" parameterType="com.xgwc.user.entity.ServiceAuthorize">
        update xgwc_service_authorize
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>