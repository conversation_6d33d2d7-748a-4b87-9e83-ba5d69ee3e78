<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseMapper">

    <select id="getFranchiseDtoByUserId" resultType="com.xgwc.user.entity.dto.FranchiseDto">
        SELECT
            t.id,
            t.franchise_name franchiseName,
            fs.stage_name,
            t.manager_name managerName,
            t.manager_phone managerPhone,
            t.manager_user_id managerUserId,
            fs.dept_id
        FROM
            franchise t
        left join xgwc_franchise_staff fs on  t.manager_user_id = fs.bind_user_id
        WHERE
            t.manager_user_id = #{userId}
          AND t.`status` = 0
          AND t.is_del = 0
    </select>

    <select id="findFranchiseById" resultType="com.xgwc.user.entity.dto.FranchiseDto">
        SELECT
            t.id,
            t.franchise_name franchiseName,
            t.manager_name managerName,
            t.manager_phone managerPhone,
            t.manager_user_id managerUserId
        FROM
            franchise t
        WHERE
            t.id = #{id}
          AND t.`status` = 0
          AND t.is_del = 0
    </select>
    <select id="listByIds" resultType="com.xgwc.user.feign.entity.FranchiseDto">
        SELECT
            t.id,
            t.franchise_name franchiseName,
            t.manager_name managerName,
            t.manager_phone managerPhone,
            t.manager_user_id managerUserId
        FROM
            franchise t
        WHERE
            t.id in
            <foreach item="item" collection="ids" separator="," open="(" close=")">
                #{item}
            </foreach>
          AND t.`status` = 0
          AND t.is_del = 0
    </select>

    <insert id="insertFranchise" parameterType="com.xgwc.user.entity.Franchise">
        insert into franchise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id!= null">id,</if>
            <if test="franchiseName!= null">franchise_name,</if>
            <if test="managerName!= null">manager_name,</if>
            <if test="managerPhone!= null">manager_phone,</if>
            <if test="managerUserId!= null">manager_user_id,</if>
            <if test="createBy != null">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id!= null">#{id},</if>
            <if test="franchiseName!= null">#{franchiseName},</if>
            <if test="managerName!= null">#{managerName},</if>
            <if test="managerPhone!= null">#{managerPhone},</if>
            <if test="managerUserId!= null">#{managerUserId},</if>
            <if test="createBy != null">#{createBy},</if>
        </trim>
    </insert>

</mapper>