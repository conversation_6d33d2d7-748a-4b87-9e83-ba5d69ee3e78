<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.BrandOwnerMapper">
    

    <sql id="selectBrandOwnerVo">
        select brand_id, company_name, company_simple_name, contact, manager_phone, password, status, is_del, reason, create_by, create_time, update_by, update_time, modify_time,user_id from xgwc_brand_owner
    </sql>

    <select id="selectBrandOwnerList" parameterType="com.xgwc.user.entity.vo.BrandOwnerQueryVo" resultType="com.xgwc.user.entity.dto.BrandOwnerDto">
        <include refid="selectBrandOwnerVo"/>
        <where>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="companyName != null  and companyName != ''"> and company_name = #{companyName}</if>
            <if test="companySimpleName != null  and companySimpleName != ''"> and company_simple_name = #{companySimpleName}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="managerPhone != null  and managerPhone != ''"> and manager_phone = #{managerPhone}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="reason != null  and reason != ''"> and reason like concat('%', #{reason}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectBrandOwnerByBrandId" parameterType="Long" resultType="com.xgwc.user.entity.dto.BrandOwnerDto">
        <include refid="selectBrandOwnerVo"/>
        where brand_id = #{brandId}
    </select>

    <insert id="insertBrandOwner" parameterType="com.xgwc.user.entity.BrandOwner" useGeneratedKeys="true" keyProperty="brandId">
        insert into xgwc_brand_owner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="companySimpleName != null">company_simple_name,</if>
            <if test="contact != null">contact,</if>
            <if test="managerPhone != null">manager_phone,</if>
            <if test="password != null">password,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="status != null">status,</if>
            <if test="isDel != null">is_del,</if>
            <if test="reason != null">reason,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="companySimpleName != null">#{companySimpleName},</if>
            <if test="contact != null">#{contact},</if>
            <if test="managerPhone != null">#{managerPhone},</if>
            <if test="password != null">#{password},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="status != null">#{status},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>

    <update id="updateBrandOwner" parameterType="com.xgwc.user.entity.BrandOwner">
        update xgwc_brand_owner
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companySimpleName != null">company_simple_name = #{companySimpleName},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="managerPhone != null">manager_phone = #{managerPhone},</if>
            <if test="password != null">password = #{password},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where brand_id = #{brandId}
    </update>

    <update id="deleteBrandOwnerByBrandId" parameterType="Long">
        update xgwc_brand_owner set is_del = 1 where brand_id = #{brandId}
    </update>

    <update id="deleteBrandOwnerByBrandIds" parameterType="String">

        update xgwc_brand_owner set is_del = 1 where brand_id in
        <foreach item="brandId" collection="array" open="(" separator="," close=")">
            #{brandId}
        </foreach>
    </update>

    <select id="selectBrandOwnerByUserId" resultType="com.xgwc.user.entity.dto.BrandOwnerDto">
        <include refid="selectBrandOwnerVo"/>
        where user_id = #{userId} and status = 0 and is_del = 0
    </select>

    <select id="selectBrandOwnerByBrandIds" resultType="com.xgwc.user.entity.dto.BrandOwnerDto">
        <include refid="selectBrandOwnerVo"/>
        where brand_id in
        <foreach item="brandId" collection="brandIds" open="(" separator="," close=")">
            #{brandId}
        </foreach>
    </select>

    <select id="selectAllBrandOwner" resultType="com.xgwc.user.entity.dto.BrandOwnerSimpleDto">
        select brand_id, company_name
        from xgwc_brand_owner
        where status = 0 and is_del = 0
    </select>

    <select id="selectBrandOwnerByMobile" resultType="com.xgwc.user.entity.vo.BrandOwnerVo">
        <include refid="selectBrandOwnerVo"/>
        where manager_phone = #{mobile} and status = 0 and is_del = 0
        limit 1
    </select>

    <select id="selectBrandOwnerByManagerUserId" resultType="com.xgwc.user.entity.dto.BrandOwnerDto">
        <include refid="selectBrandOwnerVo"/>
        where user_id = #{managerUserId}
    </select>
</mapper>