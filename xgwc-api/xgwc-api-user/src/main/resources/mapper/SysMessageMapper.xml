<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysMessageMapper">
    
    <insert id="insertMessage" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into sys_message(type_id, type_key, type_name, message, user_id, is_read, create_time, update_time)
            values(#{typeId}, #{typeId}, #{typeName}, #{message}, #{userId}, 0, now(), now())
    </insert>

    <insert id="batchInsertMessages" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into sys_message(type_id, type_key, type_name, message, user_id, is_read, create_time, update_time)
        values
        <foreach item="item" collection="list" separator="," >
            (#{item.typeId},#{item.typeKey},#{item.typeName},#{item.message}, #{item.userId}, 0, now(),now())
        </foreach>
    </insert>

    <update id="updateReadStatus">
        update sys_message set is_read = 1 where id = #{id}
    </update>

    <select id="selectById" resultType="com.xgwc.user.entity.SysMessage">
        SELECT
            id,
            type_key,
            type_name,
            type_id typeId,
            message,
            user_id userId,
            is_read isRead,
            create_time createTime
        FROM
            sys_message t
        WHERE
            t.id = #{id}
    </select>

    <select id="getSysMessageList" resultType="com.xgwc.user.entity.SysMessage">
        SELECT
            id,
            type_key,
            type_name,
            message,
            type_id,
            user_id userId,
            is_read isRead,
            create_time createTime
        FROM
            sys_message t
        WHERE
            t.user_id = #{userId}
            <if test="typeKey != null">
                and type_key = #{typeKey}
            </if>
    </select>

    <select id="getMessageTypeKeyGroup" resultType="com.xgwc.user.entity.MessageTypeKey">
        SELECT
            type_key typeKey,
            any_value(type_name) typeName
        FROM
            sys_message t
           where t.user_id = #{userId}
           group by type_key
    </select>
</mapper>