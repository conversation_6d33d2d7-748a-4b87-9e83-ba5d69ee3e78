<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.BrandAgreementRecordMapper">
    
   <insert id="insertAgreementRecord" useGeneratedKeys="true" keyColumn="id">
       insert into brand_agreement_record(user_id, agreement_id, is_agree, create_time, update_time)
       VALUES(#{userId}, #{agreementId}, 1, now(), now())
       ON DUPLICATE KEY UPDATE
        `is_agree` = 1
   </insert>

    <select id="selectAgreementIdByUserId" resultType="java.lang.Integer">
        select agreement_id from brand_agreement_record where user_id = #{userId} and is_agree = 1
    </select>
</mapper>