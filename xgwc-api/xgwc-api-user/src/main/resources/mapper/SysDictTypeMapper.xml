<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysDictTypeMapper">
    

    <sql id="selectSysDictTypeVo">
        select create_by, create_time, dict_data, dict_id, dict_label, dict_sort, dict_value, is_del, modify_time, remark, status, update_by, update_time from sys_dict_type
    </sql>

    <select id="selectSysDictTypeList" parameterType="com.xgwc.user.entity.vo.SysDictTypeQueryVo" resultType="com.xgwc.user.entity.dto.SysDictTypeDto">
        <include refid="selectSysDictTypeVo"/>
        <where>
           is_del = 0
            <if test="dictData != null  and dictData != ''"> and dict_data = #{dictData}</if>
            <if test="dictId != null "> and dict_id = #{dictId}</if>
            <if test="dictLabel != null  and dictLabel != ''"> and dict_label like concat('%', #{dictLabel}, '%')</if>
            <if test="dictValue != null  and dictValue != ''"> and dict_value = #{dictValue}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by dict_sort
    </select>

    <select id="selectSysDictTypeList2" parameterType="com.xgwc.user.entity.vo.SysDictTypeQuery2Vo" resultType="com.xgwc.user.entity.dto.SysDictTypeDto">
        SELECT
            dt.create_by,
            dt.create_time,
            dt.dict_data,
            dt.dict_id,
            dt.dict_label,
            dt.dict_sort,
            dt.dict_value,
            dt.is_del,
            dt.modify_time,
            dt.remark,
            dt.status,
            dt.update_by,
            dt.update_time,
            if(ifnull(dd.num,0)  > 0 ,1,0) is_data
        FROM
            sys_dict_type dt
        left join (
            select dict_type, count(dd.dict_code) num from sys_dict_data dd
            group by dd.dict_type
        ) dd on dt.dict_value = dd.dict_type
        <where>
            dt.is_del = 0
            and dt.dict_data = 2
            <if test="dictLabel != null  and dictLabel != ''"> and dt.dict_label like concat('%', #{dictLabel}, '%')</if>
            <if test="status != null "> and dt.status = #{status}</if>
            <if test="isData == 0 "> and ifnull(dd.num,0) = 0</if>
            <if test="isData == 1 "> and ifnull(dd.num,0) > 0</if>

        </where>
    </select>

    
    <select id="selectSysDictTypeByDictId" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysDictTypeDto">
        <include refid="selectSysDictTypeVo"/>
        where dict_id = #{dictId}
    </select>

    <select id="selectSysDictTypeByDictValue" resultType="com.xgwc.user.entity.dto.SysDictTypeDto">
        <include refid="selectSysDictTypeVo"/>
        where dict_value = #{dictValue}
    </select>

    <insert id="insertSysDictType" parameterType="com.xgwc.user.entity.SysDictType">
        insert into sys_dict_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="dictData != null">dict_data,</if>
            <if test="dictId != null">dict_id,</if>
            <if test="dictLabel != null">dict_label,</if>
            <if test="dictSort != null">dict_sort,</if>
            <if test="dictValue != null">dict_value,</if>
            <if test="isDel != null">is_del,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="dictData != null">#{dictData},</if>
            <if test="dictId != null">#{dictId},</if>
            <if test="dictLabel != null">#{dictLabel},</if>
            <if test="dictSort != null">#{dictSort},</if>
            <if test="dictValue != null">#{dictValue},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysDictType" parameterType="com.xgwc.user.entity.SysDictType">
        update sys_dict_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="dictData != null">dict_data = #{dictData},</if>
            <if test="dictLabel != null">dict_label = #{dictLabel},</if>
            <if test="dictSort != null">dict_sort = #{dictSort},</if>
            <if test="dictValue != null">dict_value = #{dictValue},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where dict_id = #{dictId}
    </update>

    <update id="updateStatus" >
        update sys_dict_type
        set status = #{status}, modify_time = now()
        where dict_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteSysDictTypeByDictId" parameterType="Long">
        update sys_dict_type set is_del = 1 where dict_id = #{dictId}
    </update>

    <update id="deleteSysDictTypeByDictIds" parameterType="String">
        update sys_dict_type set is_del = 1 where dict_id in
        <foreach item="dictId" collection="array" open="(" separator="," close=")">
            #{dictId}
        </foreach>
    </update>
</mapper>