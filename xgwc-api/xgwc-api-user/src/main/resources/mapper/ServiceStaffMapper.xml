<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.ServiceStaffMapper">
    

    <sql id="selectServiceStaffVo">
        select id, name, stage_name, dept_id, post_id, role_ids, job_nature, status, is_principal, superior, bind_status, bind_user_id, login_phone, service_owner_id, is_del, create_by, create_time, update_by, update_time, modify_time,resignation_time from xgwc_service_staff
    </sql>

    <select id="selectServiceStaffById" parameterType="Long" resultType="com.xgwc.user.entity.ServiceStaffDto">
        <include refid="selectServiceStaffVo"/>
        where id = #{id} and is_del = 0
    </select>

</mapper>