<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysOperLogMapper">
    

    <sql id="selectSysOperLogVo">
        select oper_id, title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time from sys_oper_log
    </sql>

    <select id="selectSysOperLogList" parameterType="com.xgwc.user.entity.vo.SysOperLogQueryVo" resultType="com.xgwc.user.entity.dto.SysOperLogDto">
        <include refid="selectSysOperLogVo"/>
        <where>
            <if test="operId != null "> and oper_id = #{operId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="method != null  and method != ''"> and method like concat('%', #{method}, '%')</if>
            <if test="requestMethod != null  and requestMethod != ''"> and request_method like concat('%', #{requestMethod}, '%')</if>
            <if test="operatorType != null "> and operator_type = #{operatorType}</if>
            <if test="operName != null  and operName != ''"> and oper_name like concat('%', #{operName}, '%')</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="operUrl != null  and operUrl != ''"> and oper_url like concat('%', #{operUrl}, '%')</if>
            <if test="operIp != null  and operIp != ''"> and oper_ip like concat('%', #{operIp}, '%')</if>
            <if test="operLocation != null  and operLocation != ''"> and oper_location like concat('%', #{operLocation}, '%')</if>
            <if test="operParam != null  and operParam != ''"> and oper_param like concat('%', #{operParam}, '%')</if>
            <if test="jsonResult != null  and jsonResult != ''"> and json_result like concat('%', #{jsonResult}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="errorMsg != null  and errorMsg != ''"> and error_msg like concat('%', #{errorMsg}, '%')</if>
            <if test="operTime != null "> and oper_time between #{operTime}[0] and #{operTime}[1] </if>
        </where>
    </select>
    
    <select id="selectSysOperLogByOperId" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysOperLogDto">
        <include refid="selectSysOperLogVo"/>
        where oper_id = #{operId}
    </select>

    <insert id="insertSysOperLog" parameterType="com.xgwc.user.feign.entity.SysOperLog">
        insert into sys_oper_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operId != null">oper_id,</if>
            <if test="title != null">title,</if>
            <if test="businessType != null">business_type,</if>
            <if test="method != null">method,</if>
            <if test="requestMethod != null">request_method,</if>
            <if test="operatorType != null">operator_type,</if>
            <if test="operName != null">oper_name,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="operUrl != null">oper_url,</if>
            <if test="operIp != null">oper_ip,</if>
            <if test="operLocation != null">oper_location,</if>
            <if test="operParam != null">oper_param,</if>
            <if test="jsonResult != null">json_result,</if>
            <if test="status != null">status,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="operTime != null">oper_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operId != null">#{operId},</if>
            <if test="title != null">#{title},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="method != null">#{method},</if>
            <if test="requestMethod != null">#{requestMethod},</if>
            <if test="operatorType != null">#{operatorType},</if>
            <if test="operName != null">#{operName},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="operUrl != null">#{operUrl},</if>
            <if test="operIp != null">#{operIp},</if>
            <if test="operLocation != null">#{operLocation},</if>
            <if test="operParam != null">#{operParam},</if>
            <if test="jsonResult != null">#{jsonResult},</if>
            <if test="status != null">#{status},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="operTime != null">#{operTime},</if>
         </trim>
    </insert>

    <update id="updateSysOperLog" parameterType="com.xgwc.user.feign.entity.SysOperLog">
        update sys_oper_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="method != null">method = #{method},</if>
            <if test="requestMethod != null">request_method = #{requestMethod},</if>
            <if test="operatorType != null">operator_type = #{operatorType},</if>
            <if test="operName != null">oper_name = #{operName},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="operUrl != null">oper_url = #{operUrl},</if>
            <if test="operIp != null">oper_ip = #{operIp},</if>
            <if test="operLocation != null">oper_location = #{operLocation},</if>
            <if test="operParam != null">oper_param = #{operParam},</if>
            <if test="jsonResult != null">json_result = #{jsonResult},</if>
            <if test="status != null">status = #{status},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
        </trim>
        where oper_id = #{operId}
    </update>

    <delete id="deleteSysOperLogByOperId" parameterType="Long">
        delete from sys_oper_log where oper_id = #{operId}
    </delete>

    <delete id="deleteSysOperLogByOperIds" parameterType="String">
        delete from sys_oper_log where oper_id in 
        <foreach item="operId" collection="array" open="(" separator="," close=")">
            #{operId}
        </foreach>
    </delete>
</mapper>