<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.PrivacyNumberMapper">

    <insert id="insertPrivacyNumber" useGeneratedKeys="true" keyColumn="id">
        insert into privacy_number_bind(middle_number, bind_number_a, bind_number_b, max_binding_time, expire_time, bind_status,create_time,update_time)
        values(#{middleNumber}, #{bindNumberA}, #{bindNumberB}, #{maxBindingTime}, #{expireTime}, 0, now(), now())
    </insert>

    <select id="getBindMiddleNumber" resultType="java.lang.String">
        SELECT
            t.middle_number
        FROM
            privacy_number_bind t
        WHERE
            ( t.bind_number_a = #{bindNumberA} OR t.bind_number_b = #{bindNumberA} OR t.bind_number_a = #{bindNumberB} OR t.bind_number_b = #{bindNumberB} )
          AND t.expire_time >= now()
        group by t.middle_number
    </select>

    <select id="getAllMiddleNumberList" resultType="java.lang.String">
        select t.privacy_number from privacy_number t where t.count &lt; 200 and status = 0
    </select>

    <select id="selectPrivacyNumber" resultType="com.xgwc.user.entity.dto.PrivacyNumberDto">
        SELECT
            t.bind_number_a bindNumberA,
            t.bind_number_b bindNumberB,
            t.middle_number middleNumber
        FROM
            privacy_number_bind t
        WHERE
            t.bind_number_a = #{photoA}
          AND t.bind_number_b = #{phoneB}
          AND t.expire_time >= now()
    </select>

    <update id="updateBindStatus">
        UPDATE privacy_number_bind
        SET bind_status = 1
        WHERE
            bind_number_a = #{bindNumberA}
          AND bind_number_b = #{bindNumberB}
          AND bind_status = 0
    </update>

    <insert id="insertCallBackRecord">
            INSERT INTO privacy_number_record (
                app_id,
                bind_id,
                call_id,
                display_number,
                caller_number,
                callee_number,
                start_call_time,
                caller_answered_time,
                callee_answered_time,
                end_time,
                caller_state,
                callee_state,
                caller_duration,
                callee_duration,
                duration,
                recUrl,
                customer_data,
                create_time,
                update_time
            )
            VALUES (
                       #{appId,jdbcType=VARCHAR},
                       #{bindId,jdbcType=VARCHAR},
                       #{callId,jdbcType=VARCHAR},
                       #{displayNumber},
                       #{callerNumber,jdbcType=VARCHAR},
                       #{calleeNumber,jdbcType=VARCHAR},
                       #{startCallTime,jdbcType=VARCHAR},
                       #{callerAnsweredTime},
                       #{calleeAnsweredTime},
                       #{endTime},
                       #{callerState},
                       #{calleeState},
                       #{callerDuration},
                       #{calleeState},
                       #{duration},
                       #{recUrl},
                       #{customerData},
                       now(),
                       now()
                   )
    </insert>

    <update id="updateMiddleNumberCount">
        update privacy_number set count = #{count} where middle_number = #{middleNumber} and status = 0
    </update>

    <update id="updateMiddleNumberMinusOne">
        update privacy_number set count = count - 1 where middle_number = #{middleNumber} and status = 0
    </update>
</mapper>