<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.ReportTargetFranchiseStaffMapper">
    

    <sql id="selectReportTargetFranchiseStaffVo">
        select id, brand_id, franchise_id, dept_id, target_id, target_dept_id, user_id, target_amount, amount_order, amount_real, target_new_customer, target_old_customer, target_transfer_amount, target_comission_rate,
               target_conversion_rate, new_customer_amount, old_customer_amount, transfer_amount, conversion_rate, comission_rate, status, create_by, create_by_id, create_time, update_by,
               update_by_id, update_time from report_target_franchise_staff
    </sql>

    <insert id="batchInsertReportTargetFranchiseDept">
        INSERT INTO report_target_franchise_staff (
        brand_id,
        franchise_id,
        dept_id,
        target_date,
        target_id,
        target_dept_id,
        user_id,
        target_amount,
        amount_order,
        amount_real,
        target_new_customer,
        target_old_customer,
        target_transfer_amount,
        target_comission_rate,
        target_conversion_rate,
        new_customer_amount,
        old_customer_amount,
        transfer_amount,
        conversion_rate,
        comission_rate,
        status,
        create_by,
        create_by_id,
        create_time,
        update_by,
        update_by_id,
        update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.brandId},
            #{item.franchiseId},
            #{item.deptId},
            #{item.targetDate},
            #{item.targetId},
            #{item.targetDeptId},
            #{item.userId},
            #{item.targetAmount},
            #{item.amountOrder},
            #{item.amountReal},
            #{item.targetNewCustomer},
            #{item.targetOldCustomer},
            #{item.targetTransferAmount},
            #{item.targetComissionRate},
            #{item.targetConversionRate},
            #{item.newCustomerAmount},
            #{item.oldCustomerAmount},
            #{item.transferAmount},
            #{item.conversionRate},
            #{item.comissionRate},
            0,
            #{item.createBy},
            #{item.createById},
            now(),
            #{item.updateBy},
            #{item.updateById},
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <update id="updateReportTargetFranchiseStaff" parameterType="com.xgwc.user.entity.ReportTargetFranchiseStaff">
        update report_target_franchise_staff
        <trim prefix="SET" suffixOverrides=",">
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="targetAmount != null">target_amount = #{targetAmount},</if>
            <if test="amountOrder != null">amount_order = #{amountOrder},</if>
            <if test="amountReal != null">amount_real = #{amountReal},</if>
            <if test="targetNewCustomer != null">target_new_customer = #{targetNewCustomer},</if>
            <if test="targetOldCustomer != null">target_old_customer = #{targetOldCustomer},</if>
            <if test="targetTransferAmount != null">target_transfer_amount = #{targetTransferAmount},</if>
            <if test="targetComissionRate != null">target_comission_rate = #{targetComissionRate},</if>
            <if test="targetConversionRate != null">target_conversion_rate = #{targetConversionRate},</if>
            <if test="newCustomerAmount != null">new_customer_amount = #{newCustomerAmount},</if>
            <if test="oldCustomerAmount != null">old_customer_amount = #{oldCustomerAmount},</if>
            <if test="transferAmount != null">transfer_amount = #{transferAmount},</if>
            <if test="conversionRate != null">conversion_rate = #{conversionRate},</if>
            <if test="comissionRate != null">comission_rate = #{comissionRate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateById != null">update_by_id = #{updateById},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id} and brand_id = #{brandId}
    </update>

   <select id="getReportTargetFranchiseStaffByTargetId" resultType="com.xgwc.user.entity.dto.ReportTargetFranchiseStaffDto">
       SELECT
           t.id,
           t.brand_id,
           t.franchise_id,
           t.dept_id,
           t.target_id,
           t.user_id,
           t.target_amount,
           t.amount_order,
           t.amount_real,
           t.target_new_customer,
           t.target_old_customer,
           t.target_transfer_amount,
           t.target_comission_rate,
           t.target_conversion_rate,
           t.new_customer_amount,
           t.old_customer_amount,
           t.transfer_amount,
           t.conversion_rate,
           t.comission_rate,
           t.STATUS,
           t.create_by,
           t.create_by_id,
           t.create_time,
           fs.stage_name userName,
           fd.dept_name,
           s.station_name
       FROM
           report_target_franchise_staff t
               left join xgwc_franchise_staff fs on fs.bind_user_id = t.user_id
               left join franchise_dept fd on fs.dept_id = fd.dept_id
               left join franchise_station s on fs.post_id = s.station_id
       where t.target_id = #{targetId} and t.dept_id = #{deptId}
   </select>

    <update id="deleteByTargetId">
        update report_target_franchise_staff set status = 1 where target_id = #{targetId}
    </update>
</mapper>