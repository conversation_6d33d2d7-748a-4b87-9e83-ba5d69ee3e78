<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseRoleMapper">
    <insert id="saveFranchiseRole" useGeneratedKeys="true" keyProperty="roleId">
        INSERT INTO `xgwc_sass`.`franchise_role`(`role_name`,`is_flag`,`franchise_id`,`sort`,`create_by`,`create_time`)
        VALUES (#{franchiseRoleDto.roleName},#{franchiseRoleDto.isFlag},#{franchiseRoleDto.franchiseId},
                #{franchiseRoleDto.sort},#{franchiseRoleDto.createBy},now())
    </insert>

    <insert id="saveRoleMenu">
        INSERT INTO `xgwc_sass`.`franchise_role_menu` (`role_id`, `menu_id`, `create_by`, `create_time`)
        VALUES
        <foreach collection="sysRoleMenuVoList" item="roleMenu" separator=",">
            (#{roleMenu.roleId}, #{roleMenu.menuId}, #{roleMenu.createBy}, now())
        </foreach>
    </insert>
    <insert id="saveFranchiseRoleDataScope">
        INSERT INTO `xgwc_sass`.`franchise_role_data` (`role_id`, `menu_id`, `data_scope`, `dept_id`, `time_limit`, `data_masking`, `create_by`, `create_time`)
        VALUES
        (#{sysRoleDataVo.roleId}, #{sysRoleDataVo.menuId}, #{sysRoleDataVo.dataScope}, #{sysRoleDataVo.deptId},
         #{sysRoleDataVo.timeLimit}, #{sysRoleDataVo.dataMasking},
         #{sysRoleDataVo.createBy}, now())
    </insert>
    <insert id="savefranchiseBrandRoleDataScopeDeptId">
        INSERT INTO `xgwc_sass`.`franchise_role_data` (`role_id`, `menu_id`, `data_scope`, `dept_id`, `time_limit`, `data_masking`, `create_by`, `create_time`)
        VALUES
        <foreach collection="dataMenu.deptIds" item="item" separator=",">
            (#{dataMenu.roleId}, #{dataMenu.menuId}, #{dataMenu.dataScope}, #{item}, #{dataMenu.timeLimit}, #{dataMenu.dataMasking}, #{dataMenu.createBy}, now())
        </foreach>
    </insert>
    <insert id="saveDownloadLimit">
        INSERT INTO `xgwc_sass`.`franchise_role_data_dowmload` (`role_id`,`brand_id` , `download_limit`, `create_by`, `create_time`)
        VALUES
        <foreach collection="dataMenu.downloadLimitFranchise" item="item" separator=",">
            (#{dataMenu.roleId}, #{item.brandId}, #{item.downloadLimit}, #{item.createBy}, now())
        </foreach>
    </insert>

    <update id="updateFranchiseRole">
        UPDATE `xgwc_sass`.`franchise_role`
        <set>
            <if test="franchiseRoleDto.roleName != null and franchiseRoleDto.roleName != ''">
                `role_name` = #{franchiseRoleDto.roleName},
            </if>
            <if test="franchiseRoleDto.isFlag != null and franchiseRoleDto.isFlag != ''">
                `is_flag` = #{franchiseRoleDto.isFlag},
            </if>
            <if test="franchiseRoleDto.franchiseId != null">
                `franchise_id` = #{franchiseRoleDto.franchiseId},
            </if>
            <if test="franchiseRoleDto.sort != null">
                `sort` = #{franchiseRoleDto.sort},
            </if>
            <if test="franchiseRoleDto.updateBy != null">
                `update_by` = #{franchiseRoleDto.updateBy},
            </if>
            `update_time` = now(),
        </set>
        WHERE `role_id` = #{franchiseRoleDto.roleId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`franchise_role`
        SET `status` = #{status}
        WHERE `role_id` = #{roleId}
    </update>
    <delete id="deleteRoleMenu">
        DELETE FROM `xgwc_sass`.`franchise_role_menu`
        WHERE `role_id` = #{roleId}
    </delete>
    <delete id="deleteRoleData">
        DELETE FROM `xgwc_sass`.`franchise_role_data`
        WHERE `role_id` = #{roleId}
    </delete>
    <delete id="deleteRoleDownloadLimit">
        DELETE FROM `xgwc_sass`.`franchise_role_data_dowmload`
        WHERE `role_id` = #{roleId}
    </delete>

    <select id="getFranchiseRoleList" resultType="com.xgwc.user.entity.vo.FranchiseRoleVo">
        SELECT
        `role_id` AS roleId,
        `role_name` AS roleName,
        `franchise_id` AS franchiseId,
        `is_flag` AS isFlag,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`franchise_role`
        <where>
            `franchise_id` = #{franchiseRoleParam.franchiseId}

            <if test="franchiseRoleParam.roleName != null and franchiseRoleParam.roleName != ''">
                AND `role_name` = #{franchiseRoleParam.roleName}
            </if>
            <if test="franchiseRoleParam.status != null">
                AND `status` = #{franchiseRoleParam.status}
            </if>
        </where>
        ORDER BY `sort`
    </select>
    <select id="getFranchiseRoleById" resultType="com.xgwc.user.entity.vo.FranchiseRoleVo">
        SELECT `role_id`     AS roleId,
               `role_name`   AS roleName,
               `is_flag`     AS isFlag,
               `franchise_id` AS franchiseId,
               `sort`,
               `status`,
               `is_del`      AS isDel,
               `create_by`   AS createBy,
               `create_time` AS createTime,
               `update_by`   AS updateBy,
               `update_time` AS updateTime,
               `modify_time` AS modifyTime
        FROM `xgwc_sass`.`franchise_role`
        WHERE `role_id` = #{roleId}
    </select>
    <select id="getFranchiseRoleDataById" resultType="com.xgwc.user.entity.vo.SysRoleDataVo">
        SELECT
            xrd.`id` AS id,
            xrd.`role_id` AS roleId,
            xrd.`menu_id` AS menuId,
            sm.`name` AS menuName,
            sm.pid,
            xrd.`data_scope` AS dataScope,
            xrd.`dept_id` AS deptId,
            xrd.`time_limit` AS timeLimit,
            xrd.`data_masking` AS dataMasking
        FROM `xgwc_sass`.`franchise_role_data` xrd
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = xrd.menu_id
        WHERE `role_id` = #{roleId}
    </select>
    <select id="getFranchiseRoleMenusById" resultType="com.xgwc.user.entity.vo.SysRoleMenuVo">
        SELECT
            frd.`id` AS id,
            frd.`role_id` AS roleId,
            frd.`menu_id` AS menuId,
            sm.pid,
            sm.`name` AS menuName
        FROM `xgwc_sass`.`franchise_role_menu` frd
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frd.menu_id
        WHERE `role_id` = #{roleId}
    </select>

    <select id="selectRoleByUserId" resultType="com.xgwc.user.entity.vo.FranchiseRoleVo">
        SELECT
            fr.*
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`franchise_role` AS fr on fr.role_id = xur.role_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 1 and fr.status = 0
    </select>

    <select id="selectMenuByUserId" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        SELECT
            sm.*
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`franchise_role` AS fr on fr.role_id = xur.role_id
                 left join `xgwc_sass`.`franchise_role_menu` AS frm on fr.role_id = frm.role_id
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frm.menu_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 1 and sm.is_del = 0 and fr.status = 0 and sm.type IN ( 'catalog', 'menu' )
        order by sm.sort
    </select>
    <select id="getFranchiseRoleDownloadById" resultType="com.xgwc.user.entity.vo.SysRoleDateDownloadLimitVo">
        SELECT
            xbo.brand_id as brandId,
            xbo.company_name as brandName,
            xfo.download_limit as downloadLimit,
            xfo.franchise_id as franchiseId,
            frdd.download_limit as downloadLimitRole,
            frdd.role_id as roleId,
            frdd.id
        FROM `xgwc_sass`.`franchise_owner` xfo
                 left join `xgwc_sass`.`xgwc_brand_owner` xbo on xbo.brand_id = xfo.brand_id
                 left join `xgwc_sass`.`franchise_role_data_dowmload` frdd on xbo.brand_id = frdd.brand_id
                 left join `xgwc_sass`.`franchise_role` frr on frdd.role_id = frr.role_id
        <where>
                    frdd.role_id = #{roleId}
                and xfo.franchise_id = #{franchiseId}
                and frr.status = 0
        </where>
    </select>
    <select id="selectBrandSetDownload" resultType="com.xgwc.user.entity.vo.SysRoleDateDownloadLimitVo">
        SELECT
            xbo.brand_id as brandId,
            xbo.company_name as brandName,
            xfo.download_limit as downloadLimit,
            xfo.franchise_id as franchiseId
        FROM `xgwc_sass`.`franchise_owner` xfo
                 left join `xgwc_sass`.`xgwc_brand_owner` xbo on xbo.brand_id = xfo.brand_id
        WHERE xfo.franchise_id = #{franchiseId}
    </select>
    <select id="selectLastLevelMenu" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM sys_menu
        WHERE pid = #{menuId} and is_del = 0 and FIND_IN_SET('franchiseeAdmin',model_type)
    </select>
    <select id="selectLastLevelMenuDate" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM sys_menu
        WHERE pid = #{menuId} and is_del = 0 and FIND_IN_SET('franchiseeAdmin',model_type) and type IN ( 'catalog', 'menu' )
    </select>
    <select id="selectRoleServiceByUserId" resultType="com.xgwc.user.entity.vo.FranchiseRoleVo">
        SELECT
            fr.*
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`service_role` AS fr on fr.role_id = xur.role_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 2 and fr.status = 0
    </select>
    <select id="selectMenuServiceByUserId" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        SELECT
            sm.*
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`service_role` AS fr on fr.role_id = xur.role_id
                 left join `xgwc_sass`.`service_role_menu` AS frm on fr.role_id = frm.role_id
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frm.menu_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 2 and sm.is_del = 0 and fr.status = 0 and sm.type IN ( 'catalog', 'menu' )
        order by sm.sort
    </select>
    <select id="selectRoleMarketByUserId" resultType="com.xgwc.user.entity.vo.FranchiseRoleVo">
        SELECT
            fr.*
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`market_role` AS fr on fr.role_id = xur.role_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 3 and fr.status = 0
    </select>
    <select id="selectMenuMarketByUserId" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        SELECT
            sm.*
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`market_role` AS fr on fr.role_id = xur.role_id
                 left join `xgwc_sass`.`market_role_menu` AS frm on fr.role_id = frm.role_id
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frm.menu_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 3 and sm.is_del = 0 and fr.status = 0 and sm.type IN ( 'catalog', 'menu' )
        order by sm.sort
    </select>

</mapper>