<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseDeptMapper">
    <insert id="saveFranchiseDept">
        INSERT INTO `xgwc_sass`.`franchise_dept`
        (`dept_id`, `dept_name`, `franchise_id`, `pid`, `level`, `sort`, `create_by`, `create_time`)
        VALUES (#{franchiseDeptDto.deptId}, #{franchiseDeptDto.deptName}, #{franchiseDeptDto.franchiseId},
                #{franchiseDeptDto.pid},
                #{franchiseDeptDto.level}, #{franchiseDeptDto.sort},
                #{franchiseDeptDto.createBy}, now())
    </insert>
    <update id="updateFranchiseDeptById">
        UPDATE `xgwc_sass`.`franchise_dept`
        <set>
            <if test="franchiseDeptDto.deptName != null and franchiseDeptDto.deptName !=''">
                `dept_name` = #{franchiseDeptDto.deptName},
            </if>
            <if test="franchiseDeptDto.franchiseId != null and franchiseDeptDto.franchiseId !=''">
                `franchise_id` = #{franchiseDeptDto.franchiseId},
            </if>
            <if test="franchiseDeptDto.pid != null and franchiseDeptDto.pid !=''">
                `pid` = #{franchiseDeptDto.pid},
            </if>
            <if test="franchiseDeptDto.level != null and franchiseDeptDto.level !=''">
                `level` = #{franchiseDeptDto.level},
            </if>
            <if test="franchiseDeptDto.sort != null and franchiseDeptDto.sort !=''">
                `sort` = #{franchiseDeptDto.sort},
            </if>
            <if test="franchiseDeptDto.updateBy != null and franchiseDeptDto.updateBy !=''">
                `update_by` = #{franchiseDeptDto.updateBy},
            </if>
            `update_time` = now()
        </set>
        wHERE `dept_id` = #{franchiseDeptDto.deptId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`franchise_dept`
        SET `status` = #{status}
        WHERE `dept_id` = #{deptId}
    </update>
    <update id="updateDeptStaffSchedule">
        UPDATE xgwc_franchise_staff
        <set>
            `is_assistant` = 0,
            <if test="staff.isSchedule != null">
                `is_schedule` = #{staff.isSchedule},
            </if>
            <if test="staff.updateBy != null and staff.updateBy != ''">
                `update_by` = #{staff.updateBy},
            </if>
            `update_time` = NOW(),
        </set>
        WHERE `id` = #{staff.id}
    </update>


    <update id="updateDeptStaffManage">
        UPDATE xgwc_franchise_staff
        <trim prefix="SET" suffixOverrides=",">
            `is_principal` = 0,
            <if test="principal.isSchedule != null">
                `is_schedule` = #{principal.isSchedule},
            </if>
            <if test="principal.updateBy != null and principal.updateBy != ''">
                `update_by` = #{principal.updateBy},
            </if>
            `update_time` = NOW(),
        </trim>
        WHERE `id` = #{principal.id}
    </update>
    <update id="updateDeptStaffAssistant">
        UPDATE xgwc_franchise_staff
        set is_assistant = 1, is_principal = 1, is_schedule = 1
        where `dept_id` = #{deptId}
    </update>

    <select id="getFranchiseDeptList" resultType="com.xgwc.user.entity.vo.FranchiseDeptVo">
        SELECT
        `dept_id` AS deptId,
        `dept_name` AS deptName,
        `franchise_id` AS franchiseId,
        `pid`,
        `level`,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`franchise_dept`
        <where>
            AND `franchise_id` = #{franchiseDeptParam.franchiseId}

            <if test="franchiseDeptParam.deptName != null and franchiseDeptParam.deptName !=''">
                AND `dept_name` Like concat('%', #{franchiseDeptParam.deptName}, '%')
            </if>
            <if test="franchiseDeptParam.status != null">
                AND `status` = #{franchiseDeptParam.status}
            </if>
        </where>
        ORDER BY `sort`
    </select>

    <select id="getFranchiseDeptById" resultType="com.xgwc.user.entity.dto.FranchiseDeptDto">
        SELECT `dept_id`      AS deptId,
               `dept_name`    AS deptName,
               `franchise_id` AS franchiseId,
               `pid`,
               `level`,
               `sort`,
               `status`,
               `is_del`       AS isDel,
               `create_by`    AS createBy,
               `create_time`  AS createTime,
               `update_by`    AS updateBy,
               `update_time`  AS updateTime,
               `modify_time`     ASmodifyTime
        FROM `xgwc_sass`.`franchise_dept`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
            </if>
        </where>
    </select>
    <select id="selectXgwcDeptStaffManage" resultType="com.xgwc.user.entity.vo.StaffVo">
        SELECT
        `id`,
        `name`,
        `dept_id` AS deptId,
        `is_principal` AS isPrincipal
        FROM
        `xgwc_sass`.`xgwc_franchise_staff`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
                AND `is_principal` = 0
            </if>
        </where>
    </select>
    <select id="selectXgwcDeptStaffSchedule" resultType="com.xgwc.user.entity.vo.StaffVo">
        SELECT
        `id`,
        `name`,
        `dept_id` AS deptId,
        `is_assistant` AS isAssistant,
        `is_schedule` AS isSchedule
        FROM
        `xgwc_sass`.`xgwc_franchise_staff`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
                AND `is_assistant` = 0
            </if>
        </where>
    </select>

    <select id="getFranchiseDeptByfranchiseId" resultType="com.xgwc.user.entity.dto.FranchiseDeptDto">
        SELECT `dept_id`      AS deptId,
               `dept_name`    AS deptName,
               `franchise_id` AS franchiseId,
               `pid`,
               `level`,
               `sort`,
               `status`,
               `is_del`       AS isDel,
               `create_by`    AS createBy,
               `create_time`  AS createTime,
               `update_by`    AS updateBy,
               `update_time`  AS updateTime,
               `modify_time`     ASmodifyTime
        FROM `xgwc_sass`.`franchise_dept`
        WHERE `franchise_id` = #{franchiseId}
         and status = 0 and is_del = 0
    </select>

    <select id="selectDeptStaffNum" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
        `xgwc_sass`.`xgwc_franchise_staff`
        WHERE
        <if test="deptId != null">
            `dept_id` = #{deptId}
        </if>
    </select>
    <select id="getUserDeptByUserId" resultType="com.xgwc.user.entity.dto.FranchiseDeptDto">
        select
            d.dept_id,
            d.dept_name,
            d.franchise_id,
            d.pid
        from xgwc_franchise_staff s
        left join franchise_dept d on s.dept_id = d.dept_id
        where s.bind_user_id = #{userId}
        limit 1
    </select>

    <select id="getDeptByPid" resultType="com.xgwc.user.entity.vo.FranchiseDeptVo">
        SELECT
            dept_id AS deptId,
            dept_name AS deptName,
            franchise_id AS franchiseId,
            pid,
            level
        FROM franchise_dept
        WHERE pid = #{pid}
        AND franchise_id = #{franchiseId}
    </select>

    <select id="getDeptByFranchiseId" resultType="com.xgwc.user.entity.vo.FranchiseDeptVo">
        SELECT
            dept_id AS deptId,
            dept_name AS deptName,
            franchise_id AS franchiseId,
            pid,
            level
        FROM franchise_dept
        WHERE franchise_id = #{franchiseId}
        AND status = 0 AND is_del = 0
        AND pid = 0
    </select>
</mapper>