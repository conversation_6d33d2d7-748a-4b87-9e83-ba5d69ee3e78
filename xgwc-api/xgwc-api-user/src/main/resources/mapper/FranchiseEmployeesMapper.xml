<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseEmployeesMapper">


    <sql id="selectEmployeesVo">
        select employee_id,staff_id, name, sex, ethnicity, birthdate,education, political_status, marital_status, id_number, email, phone, address, emer_name, emer_phone, entry_date, contract_end_date, social_status, probation_status, probation_end_date, annual_leave_days, resignation_date, create_by, create_time, update_by, update_time, modify_time from franchise_employees
    </sql>

    <delete id="deleteFranchiseEmpAttachments">
        DELETE FROM franchise_emp_attachments WHERE employee_id = #{employeeId}
    </delete>

    <select id="selectEmployeesList" parameterType="com.xgwc.user.entity.vo.FranchiseEmployeesQueryVo" resultType="com.xgwc.user.entity.dto.FranchiseEmployeesDto">
        SELECT
        xe.`employee_id` as employeeId,
        xe.`staff_id` as staffId,
        xe.`name`,
        xs.`stage_name` as stageName,
        xe.`sex`,
        xs.`job_nature` as jobNature,
        xs.`status`,
        xbs.`station_name` as stationName,
        xbd.`dept_name` as deptName,
        xe.`social_status` as socialStatus,
        xe.`phone`,
        xe.`entry_date` as entryDate,
        xe.`contract_end_date` as contractEndDate,
        xe.`resignation_date` as resignationDate,
        xe.`education`,
        xe.`birthdate`
        FROM `franchise_employees` xe
        LEFT JOIN franchise_emp_accounts ea on xe.employee_id = ea.employee_id
        LEFT JOIN xgwc_franchise_staff xs on xe.staff_id = xs.id
        LEFT JOIN franchise_dept xbd on xs.dept_id = xbd.dept_id
        LEFT JOIN franchise_station xbs on xs.post_id = xbs.station_id
        <where>
            xe.`franchise_id` = #{employees.franchiseId}

            <if test="employees.name != null and employees.name != ''">
                and (
                xe.name = #{employees.name}
                or xs.stage_name = #{employees.name}
                or xe.phone = #{employees.name}
                or xe.id_number = #{employees.name}
                or ea.account_number = #{employees.name}
                or ea.alipay_account = #{employees.name}
                )
            </if>
            <if test="employees.entryDate != null ">
                and xe.entry_date between #{employees.entryDate[0]} and #{employees.entryDate[1]}
            </if>
            <if test="employees.contractEndDate != null ">
                and xe.contract_end_date between #{employees.contractEndDate[0]} and #{employees.contractEndDate[1]}
            </if>
            <if test="employees.birthdate != null ">
                and xe.birthdate between #{employees.birthdate[0]} and #{employees.birthdate[1]}
            </if>
            <if test="employees.resignationDate != null ">
                and xe.resignation_date between #{employees.resignationDate[0]} and #{employees.resignationDate[1]}
            </if>
            <if test="employees.deptId != null">
                and xbd.`dept_id` = #{employees.deptId}
            </if>
            <if test="employees.stationId != null">
                and xbs.`station_id` = #{employees.stationId}
            </if>
            <if test="employees.jobNature != null">
                and xs.`job_nature` = #{employees.jobNature}
            </if>
            <if test="employees.status != null">
                and xs.`status` = #{employees.status}
            </if>
            <if test="employees.socialStatus != null">
                and xe.`social_status` = #{employees.socialStatus}
            </if>
            <if test="employees.education != null">
                and xe.`education` = #{employees.education}
            </if>
        </where>
        ORDER BY xe.`employee_id` DESC
    </select>

    <select id="selectEmployeesByEmployeeId" parameterType="Long" resultType="com.xgwc.user.entity.dto.FranchiseEmployeesDto">
        select
            xe.employee_id as employeeId,
            xe.staff_id as staffId,
            xe.`name`,
            xe.sex,
            xe.ethnicity,
            xe.birthdate,
            xe.education,
            xe.political_status as politicalStatus,
            xe.marital_status as maritalStatus,
            xe.id_number as idNumber,
            xe.email,
            xe.phone,
            xe.address,
            xe.emer_name as emerName,
            xe.emer_phone as emerPhone,
            xe.entry_date as entryDate,
            xe.contract_end_date as contractEndDate,
            xe.social_status as socialStatus,
            xe.buy_social_date as buySocialDate,
            xe.probation_status as probationStatus,
            xe.probation_end_date as probationEndDate,
            xe.annual_leave_days as annualLeaveDays,
            xe.resignation_date as resignationDate,
            eac.account_name as accountName,
            eac.bank_name as bankName,
            eac.account_number as accountNumber,
            eac.alipay_name as alipayName,
            eac.alipay_account as alipayAccount
        from franchise_employees xe
                 LEFT JOIN franchise_emp_accounts eac on xe.employee_id = eac.employee_id
        where xe.staff_id = #{employeeId}
    </select>
    <select id="selectFranchiseEmpAttachments" resultType="com.xgwc.user.entity.FranchiseEmpAttachments">
            select
                eat.attachment_id as attachmentId,
                eat.employee_id as employeeId,
                eat.attachment_type as attachmentType,
                eat.file_name as fileName,
                eat.file_path as filePath,
                eat.upload_date as uploadDate
            from franchise_employees xe
                     LEFT JOIN franchise_emp_attachments eat on xe.employee_id = eat.employee_id
            where xe.employee_id=#{employeeId}
    </select>

    <!-- 身份证号校验 -->
    <select id="existsByIdNumber" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM franchise_employees
        WHERE id_number = #{idNumber}
    </select>

    <!-- 手机号校验 -->
    <select id="existsByPhone" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM franchise_employees
        WHERE phone = #{phone}
    </select>

    <!-- 银行卡号校验 -->
    <select id="existsByAccountNumber" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM franchise_emp_accounts
        WHERE account_number = #{accountNumber}
    </select>

    <!-- 支付宝账号校验 -->
    <select id="existsByAlipayAccount" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM franchise_emp_accounts
        WHERE alipay_account = #{alipayAccount}
    </select>

    <insert id="insertEmployees" parameterType="com.xgwc.user.entity.FranchiseEmployees" useGeneratedKeys="true" keyProperty="employeeId">
        insert into franchise_employees
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="franchiseEmployees.employeeId != null">employee_id,</if>
            <if test="franchiseEmployees.staffId != null">staff_id,</if>
            <if test="franchiseEmployees.accountId != null">account_id,</if>
            <if test="franchiseEmployees.attachmentId != null">attachment_id,</if>
            <if test="franchiseEmployees.franchiseId != null">franchise_id,</if>
            <if test="franchiseEmployees.name != null">`name`,</if>
            <if test="franchiseEmployees.sex != null">sex,</if>
            <if test="franchiseEmployees.ethnicity != null">ethnicity,</if>
            <if test="franchiseEmployees.birthdate != null">birthdate,</if>
            <if test="franchiseEmployees.education != null">education,</if>
            <if test="franchiseEmployees.politicalStatus != null">political_status,</if>
            <if test="franchiseEmployees.maritalStatus != null">marital_status,</if>
            <if test="franchiseEmployees.idNumber != null">id_number,</if>
            <if test="franchiseEmployees.email != null">email,</if>
            <if test="franchiseEmployees.phone != null">phone,</if>
            <if test="franchiseEmployees.address != null">address,</if>
            <if test="franchiseEmployees.emerName != null">emer_name,</if>
            <if test="franchiseEmployees.emerPhone != null">emer_phone,</if>
            <if test="franchiseEmployees.entryDate != null">entry_date,</if>
            <if test="franchiseEmployees.contractEndDate != null">contract_end_date,</if>
            <if test="franchiseEmployees.socialStatus != null">social_status,</if>
            <if test="franchiseEmployees.buySocialDate != null">buy_social_date,</if>
            <if test="franchiseEmployees.probationStatus != null">probation_status,</if>
            <if test="franchiseEmployees.probationEndDate != null">probation_end_date,</if>
            <if test="franchiseEmployees.annualLeaveDays != null">annual_leave_days,</if>
            <if test="franchiseEmployees.resignationDate != null">resignation_date,</if>
            <if test="franchiseEmployees.createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="franchiseEmployees.employeeId != null">#{franchiseEmployees.employeeId},</if>
            <if test="franchiseEmployees.staffId != null">#{franchiseEmployees.staffId},</if>
            <if test="franchiseEmployees.accountId != null">#{franchiseEmployees.accountId},</if>
            <if test="franchiseEmployees.attachmentId != null">#{franchiseEmployees.attachmentId},</if>
            <if test="franchiseEmployees.franchiseId != null">#{franchiseEmployees.franchiseId},</if>
            <if test="franchiseEmployees.name != null">#{franchiseEmployees.name},</if>
            <if test="franchiseEmployees.sex != null">#{franchiseEmployees.sex},</if>
            <if test="franchiseEmployees.ethnicity != null">#{franchiseEmployees.ethnicity},</if>
            <if test="franchiseEmployees.birthdate != null">#{franchiseEmployees.birthdate},</if>
            <if test="franchiseEmployees.education != null">#{franchiseEmployees.education},</if>
            <if test="franchiseEmployees.politicalStatus != null">#{franchiseEmployees.politicalStatus},</if>
            <if test="franchiseEmployees.maritalStatus != null">#{franchiseEmployees.maritalStatus},</if>
            <if test="franchiseEmployees.idNumber != null">#{franchiseEmployees.idNumber},</if>
            <if test="franchiseEmployees.email != null">#{franchiseEmployees.email},</if>
            <if test="franchiseEmployees.phone != null">#{franchiseEmployees.phone},</if>
            <if test="franchiseEmployees.address != null">#{franchiseEmployees.address},</if>
            <if test="franchiseEmployees.emerName != null">#{franchiseEmployees.emerName},</if>
            <if test="franchiseEmployees.emerPhone != null">#{franchiseEmployees.emerPhone},</if>
            <if test="franchiseEmployees.entryDate != null">#{franchiseEmployees.entryDate},</if>
            <if test="franchiseEmployees.contractEndDate != null">#{franchiseEmployees.contractEndDate},</if>
            <if test="franchiseEmployees.socialStatus != null">#{franchiseEmployees.socialStatus},</if>
            <if test="franchiseEmployees.buySocialDate != null">#{franchiseEmployees.buySocialDate},</if>
            <if test="franchiseEmployees.probationStatus != null">#{franchiseEmployees.probationStatus},</if>
            <if test="franchiseEmployees.probationEndDate != null">#{franchiseEmployees.probationEndDate},</if>
            <if test="franchiseEmployees.annualLeaveDays != null">#{franchiseEmployees.annualLeaveDays},</if>
            <if test="franchiseEmployees.resignationDate != null">#{franchiseEmployees.resignationDate},</if>
            <if test="franchiseEmployees.createBy != null">#{franchiseEmployees.createBy},</if>
            now()
         </trim>
    </insert>

    <insert id="insertFranchiseEmpAccounts" parameterType="com.xgwc.user.entity.FranchiseEmpAccounts" useGeneratedKeys="true" keyProperty="accountId">
        insert into franchise_emp_accounts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="franchiseEmpAccounts.accountName != null">account_name,</if>
            <if test="franchiseEmpAccounts.employeeId != null">employee_id,</if>
            <if test="franchiseEmpAccounts.bankName != null">bank_name,</if>
            <if test="franchiseEmpAccounts.accountNumber != null">account_number,</if>
            <if test="franchiseEmpAccounts.alipayName != null">alipay_name,</if>
            <if test="franchiseEmpAccounts.alipayAccount != null">alipay_account,</if>
            <if test="franchiseEmpAccounts.createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="franchiseEmpAccounts.accountName != null">#{franchiseEmpAccounts.accountName},</if>
            <if test="franchiseEmpAccounts.employeeId != null">#{franchiseEmpAccounts.employeeId},</if>
            <if test="franchiseEmpAccounts.bankName != null">#{franchiseEmpAccounts.bankName},</if>
            <if test="franchiseEmpAccounts.accountNumber != null">#{franchiseEmpAccounts.accountNumber},</if>
            <if test="franchiseEmpAccounts.alipayName != null">#{franchiseEmpAccounts.alipayName},</if>
            <if test="franchiseEmpAccounts.alipayAccount != null">#{franchiseEmpAccounts.alipayAccount},</if>
            <if test="franchiseEmpAccounts.createBy != null">#{franchiseEmpAccounts.createBy},</if>
            now()
        </trim>
    </insert>

    <insert id="insertFranchiseEmpAttachments" parameterType="com.xgwc.user.entity.FranchiseEmpAttachments" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into franchise_emp_attachments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="franchiseEmpAttachments.employeeId != null">employee_id,</if>
            <if test="franchiseEmpAttachments.attachmentType != null">attachment_type,</if>
            <if test="franchiseEmpAttachments.fileName != null">file_name,</if>
            <if test="franchiseEmpAttachments.filePath != null">file_path,</if>
            <if test="franchiseEmpAttachments.fileCount != null">file_count,</if>
            upload_date,
            <if test="franchiseEmpAttachments.createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="franchiseEmpAttachments.employeeId != null">#{franchiseEmpAttachments.employeeId},</if>
            <if test="franchiseEmpAttachments.attachmentType != null">#{franchiseEmpAttachments.attachmentType},</if>
            <if test="franchiseEmpAttachments.fileName != null">#{franchiseEmpAttachments.fileName},</if>
            <if test="franchiseEmpAttachments.filePath != null">#{franchiseEmpAttachments.filePath},</if>
            <if test="franchiseEmpAttachments.fileCount != null">#{franchiseEmpAttachments.fileCount},</if>
            now(),
            <if test="franchiseEmpAttachments.createBy != null">#{franchiseEmpAttachments.createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateEmployees" parameterType="com.xgwc.user.entity.FranchiseEmployees">
        update franchise_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="franchiseEmployees.name != null">name = #{franchiseEmployees.name},</if>
            <if test="franchiseEmployees.staffId != null">staff_id = #{franchiseEmployees.staffId},</if>
            <if test="franchiseEmployees.accountId != null">account_id = #{franchiseEmployees.accountId},</if>
            <if test="franchiseEmployees.attachmentId != null">attachment_id = #{franchiseEmployees.attachmentId},</if>
            <if test="franchiseEmployees.sex != null">sex = #{franchiseEmployees.sex},</if>
            <if test="franchiseEmployees.ethnicity != null">ethnicity = #{franchiseEmployees.ethnicity},</if>
            <if test="franchiseEmployees.birthdate != null">birthdate = #{franchiseEmployees.birthdate},</if>
            <if test="franchiseEmployees.education != null">education = #{franchiseEmployees.education},</if>
            <if test="franchiseEmployees.politicalStatus != null">political_status = #{franchiseEmployees.politicalStatus},</if>
            <if test="franchiseEmployees.maritalStatus != null">marital_status = #{franchiseEmployees.maritalStatus},</if>
            <if test="franchiseEmployees.idNumber != null">id_number = #{franchiseEmployees.idNumber},</if>
            <if test="franchiseEmployees.email != null">email = #{franchiseEmployees.email},</if>
            <if test="franchiseEmployees.phone != null">phone = #{franchiseEmployees.phone},</if>
            <if test="franchiseEmployees.address != null">address = #{franchiseEmployees.address},</if>
            <if test="franchiseEmployees.emerName != null">emer_name = #{franchiseEmployees.emerName},</if>
            <if test="franchiseEmployees.emerPhone != null">emer_phone = #{franchiseEmployees.emerPhone},</if>
            <if test="franchiseEmployees.entryDate != null">entry_date = #{franchiseEmployees.entryDate},</if>
            <if test="franchiseEmployees.contractEndDate != null">contract_end_date = #{franchiseEmployees.contractEndDate},</if>
            <if test="franchiseEmployees.socialStatus != null">social_status = #{franchiseEmployees.socialStatus},</if>
            <if test="franchiseEmployees.buySocialDate != null">buy_social_date = #{franchiseEmployees.buySocialDate},</if>
            <if test="franchiseEmployees.probationStatus != null">probation_status = #{franchiseEmployees.probationStatus},</if>
            <if test="franchiseEmployees.probationEndDate != null">probation_end_date = #{franchiseEmployees.probationEndDate},</if>
            <if test="franchiseEmployees.annualLeaveDays != null">annual_leave_days = #{franchiseEmployees.annualLeaveDays},</if>
            <if test="franchiseEmployees.resignationDate != null">resignation_date = #{franchiseEmployees.resignationDate},</if>
            <if test="franchiseEmployees.updateBy != null">update_by = #{franchiseEmployees.updateBy},</if>
            <if test="franchiseEmployees.updateTime != null">update_time = now(),</if>
        </trim>
        where employee_id = #{franchiseEmployees.employeeId}
    </update>
    <update id="updateFranchiseEmpAccounts" parameterType="com.xgwc.user.entity.FranchiseEmpAccounts">
        update franchise_emp_accounts
        <trim prefix="SET" suffixOverrides=",">
            <if test="franchiseEmpAccounts.accountName != null">account_name = #{franchiseEmpAccounts.accountName},</if>
            <if test="franchiseEmpAccounts.bankName != null">bank_name = #{franchiseEmpAccounts.bankName},</if>
            <if test="franchiseEmpAccounts.accountNumber != null">account_number = #{franchiseEmpAccounts.accountNumber},</if>
            <if test="franchiseEmpAccounts.alipayName != null">alipay_name = #{franchiseEmpAccounts.alipayName},</if>
            <if test="franchiseEmpAccounts.alipayAccount != null">alipay_account = #{franchiseEmpAccounts.alipayAccount},</if>
            <if test="franchiseEmpAccounts.updateBy != null">update_by = #{franchiseEmpAccounts.updateBy},</if>
            <if test="franchiseEmpAccounts.updateTime != null">update_time = now(),</if>
        </trim>
            where employee_id = #{franchiseEmpAccounts.employeeId}
    </update>
    <update id="updateFranchiseEmpAttachments" parameterType="com.xgwc.user.entity.FranchiseEmpAttachments">
        update franchise_emp_attachments
        <trim prefix="SET" suffixOverrides=",">
            <if test="franchiseEmpAttachments.attachmentType != null">attachment_type = #{franchiseEmpAttachments.attachmentType},</if>
            <if test="franchiseEmpAttachments.fileName != null">file_name = #{franchiseEmpAttachments.fileName},</if>
            <if test="franchiseEmpAttachments.filePath != null">file_path = #{franchiseEmpAttachments.filePath},</if>
            <if test="franchiseEmpAttachments.uploadDate != null">upload_date = #{franchiseEmpAttachments.uploadDate},</if>
            <if test="franchiseEmpAttachments.updateBy != null">update_by = #{franchiseEmpAttachments.updateBy},</if>
            <if test="franchiseEmpAttachments.updateTime != null">update_time = now(),</if>
        </trim>
            where employee_id = #{franchiseEmpAttachments.employeeId} and attachment_type = #{franchiseEmpAttachments.attachmentType}
    </update>
</mapper>