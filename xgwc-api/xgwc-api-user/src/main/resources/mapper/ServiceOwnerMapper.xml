<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.ServiceOwnerMapper">
    

    <sql id="selectServiceOwnerVo">
        select company_name, contact, create_by, create_time, id, is_del, manager_phone, modify_time, password, service_type, status, update_by, update_time, user_id from xgwc_service_owner
    </sql>

    <select id="selectServiceOwnerList" parameterType="com.xgwc.user.entity.vo.ServiceOwnerQueryVo" resultType="com.xgwc.user.entity.dto.ServiceOwnerDto">
        <include refid="selectServiceOwnerVo"/>
        <where>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="contact != null  and contact != ''"> and contact like concat('%', #{contact}, '%')</if>
            <if test="id != null "> and id = #{id}</if>
            <if test="isDel == null"> and is_del = 0</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="managerPhone != null  and managerPhone != ''"> and manager_phone like concat('%', #{managerPhone}, '%')</if>
            <if test="serviceType != null "> and service_type = #{serviceType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectServiceOwnerById" parameterType="Long" resultType="com.xgwc.user.entity.dto.ServiceOwnerDto">
        <include refid="selectServiceOwnerVo"/>
        where id = #{id}
    </select>

    <select id="selectServiceOwnerByUserId" resultType="com.xgwc.user.entity.dto.ServiceOwnerDto">
        <include refid="selectServiceOwnerVo"/>
        where user_id = #{userId}
    </select>

    <select id="findServiceOwnerListForSelect" resultType="com.xgwc.user.entity.dto.ServiceOwnerSimpleDto">
        select id,company_name, manager_phone
        from xgwc_service_owner
        where status = 0 and is_del = 0
        and service_type = {{serviceType}}
    </select>

    <insert id="insertServiceOwner" parameterType="com.xgwc.user.entity.ServiceOwner" useGeneratedKeys="true" keyProperty="id">
        insert into xgwc_service_owner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="contact != null">contact,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="id != null">id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="managerPhone != null">manager_phone,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="password != null">password,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="status != null">status,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="contact != null">#{contact},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="id != null">#{id},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="managerPhone != null">#{managerPhone},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="password != null">#{password},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="status != null">#{status},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>

    <update id="updateServiceOwner" parameterType="com.xgwc.user.entity.ServiceOwner">
        update xgwc_service_owner
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="managerPhone != null">manager_phone = #{managerPhone},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="password != null">password = #{password},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteServiceOwnerById" parameterType="Long">
        update xgwc_service_owner set is_del = 1 where id = #{id}
    </update>

    <select id="selectServiceOwnerStaffByUserId" resultType="com.xgwc.user.entity.dto.ServiceOwnerDto">
        SELECT
            o.company_name,
            o.contact,
            o.create_by,
            o.create_time,
            o.id,
            o.is_del,
            o.manager_phone,
            o.modify_time,
            o.PASSWORD,
            o.service_type,
            o.STATUS,
            o.update_by,
            o.update_time,
            o.user_id
        FROM
            xgwc_service_owner o
                left join xgwc_service_staff s on s.bind_user_id = o.user_id
        where o.user_id = #{userId}
          and o.is_del = 0
          and s.is_del = 0
    </select>

</mapper>