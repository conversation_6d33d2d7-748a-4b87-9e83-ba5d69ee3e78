<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.StaffLogMapper">
    

    <sql id="selectStaffLogVo">
        select id, staff_id, field_name,remark, create_by, create_time, update_by, update_time, modify_time from franchise_staff_log
    </sql>

    <select id="selectStaffLogList" parameterType="com.xgwc.user.entity.vo.StaffLogQueryVo" resultType="com.xgwc.user.entity.dto.StaffLogDto">
        <include refid="selectStaffLogVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="staffId != null "> and staff_id = #{staffId}</if>
            <if test="fieldName != null  and fieldName != ''"> and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by like concat('%', #{createBy}, '%')</if>
            <if test="createTime != null "> and create_time between #{createTime}[0] and #{createTime}[1] </if>
            <if test="updateBy != null  and updateBy != ''"> and update_by like concat('%', #{updateBy}, '%')</if>
            <if test="updateTime != null "> and update_time between #{updateTime}[0] and #{updateTime}[1] </if>
            <if test="modifyTime != null "> and modify_time between #{modifyTime}[0] and #{modifyTime}[1] </if>
        </where>
    </select>
    
    <select id="selectStaffLogById" parameterType="Long" resultType="com.xgwc.user.entity.dto.StaffLogDto">
        <include refid="selectStaffLogVo"/>
        where id = #{id}
    </select>
    <select id="selectLogsByStaffIdAndBusinessType" resultType="com.xgwc.user.entity.dto.StaffLogDto">
        <include refid="selectStaffLogVo"/>
        where staff_id = #{staffId} and business_type = #{businessType}
        order by create_time desc
    </select>

    <insert id="insertStaffLog" parameterType="com.xgwc.user.entity.StaffLog">
        insert into franchise_staff_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="staffId != null">staff_id,</if>
            <if test="fieldName != null">field_name,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null">business_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="staffId != null">#{staffId},</if>
            <if test="fieldName != null">#{fieldName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
         </trim>
    </insert>

    <update id="updateStaffLog" parameterType="com.xgwc.user.entity.StaffLog">
        update franchise_staff_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="staffId != null">staff_id = #{staffId},</if>
            <if test="fieldName != null">field_name = #{fieldName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteStaffLogById" parameterType="Long">
        update franchise_staff_log set is_del = 1 where id = #{id}
    </update>

    <update id="deleteStaffLogByIds" parameterType="String">
        update franchise_staff_log set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>