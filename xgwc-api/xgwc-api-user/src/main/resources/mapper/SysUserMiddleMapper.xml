<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysUserMiddleMapper">
    
    <insert id="insertSysUserMiddle">
        insert into sys_user_middle(id, main_user_id, user_type, source_id, status, create_time, update_time) values (#{id}, #{mainUserId}, #{userType}, #{sourceId}, 0, now(), now())
    </insert>

    <update id="updateSysUserMiddleSourceId">
        update sys_user_middle set source_id = #{sourceId} where id = #{id}
    </update>

    <update id="updateSysUserMiddleStatus">
        update sys_user_middle set status = #{status} where id = #{id}
    </update>

    <select id="selectSysUserMiddleListByUserId" resultType="com.xgwc.user.entity.dto.SysUserMiddleDto">
        select id userId, main_user_id mainUserId, user_type userType, source_id sourceId from sys_user_middle where main_user_id = #{mainUserId} and status = 0
    </select>

    <select id="selectSysUserMiddleByCondition" resultType="com.xgwc.user.entity.dto.SysUserMiddleDto">
        select id userId, main_user_id mainUserId, user_type userType, source_id sourceId from sys_user_middle where main_user_id = #{mainUserId} and status = 0 and user_type = #{userType}
        <if test="sourceId != null">
            and source_id = #{sourceId}
        </if>
    </select>

    <select id="selectSysUserMiddleById" resultType="com.xgwc.user.entity.dto.SysUserMiddleDto">
        SELECT
            t.id userId,
            t.main_user_id mainUserId,
            t.user_type userType,
            t.source_id sourceId,
            s.phone,
            s.brand_id brandId,
            s.user_name,
            DATEDIFF(NOW(), s.create_time) AS entryDay,
            s.stage_name
        FROM
            sys_user_middle t
                left join sys_user s on s.user_id = t.main_user_id
        WHERE
            id = #{id} and t.status = 0
    </select>

    <select id="selectSysUserMiddleByUserIdAndUserType" resultType="com.xgwc.user.entity.dto.SysUserMiddleDto">
        SELECT
            t.id userId,
            t.main_user_id mainUserId,
            t.user_type userType,
            t.source_id sourceId
        FROM sys_user_middle t
        where t.main_user_id = #{userId}
        and t.user_type = #{userType}
        and t.status = 0
    </select>

    <select id="selectSysUserMiddleByUserIdAndUserTypeAndSourceId" resultType="com.xgwc.user.entity.dto.SysUserMiddleDto">
        SELECT
            t.id userId,
            t.main_user_id mainUserId,
            t.user_type userType,
            t.source_id sourceId
        FROM sys_user_middle t
        where t.main_user_id = #{userId}
        and t.user_type = #{userType}
        and t.source_id = #{sourceId}
        and t.status = 0
    </select>
    <select id="findSysUserMiddleByUserIdAndUserTypeAndSourceId"
            resultType="com.xgwc.user.entity.dto.SysUserMiddleDto">
        SELECT
            t.id userId,
            t.main_user_id mainUserId,
            t.user_type userType,
            t.source_id sourceId
        FROM sys_user_middle t
        where t.main_user_id = #{userId}
        and t.user_type = #{userType}
        <if test="sourceId != null">and t.source_id = #{sourceId}</if>
        and t.status = 0
    </select>

</mapper>