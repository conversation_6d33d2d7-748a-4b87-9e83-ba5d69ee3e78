<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.UserMenuMapper">

    <select id="getMenuPermissionByUserId" resultType="java.lang.String">
        select DISTINCT auth_code
        from
            sys_menu m
        left join sys_role_menu rm on rm.menu_id = m.id
        left join sys_user_role ur on rm.role_id = ur.role_id
       <where>
           <if test="userId != null">
               and ur.user_id = #{userId}
           </if>
       </where>
    </select>

    <select id="getMenuPermissionByRoleId" resultType="java.lang.String">
        select DISTINCT auth_code
        from
            sys_menu m
        left join sys_role_menu rm on rm.menu_id = m.id
        left join sys_role r on rm.role_id = r.id
        where r.code = #{roleId}
    </select>

    <select id="getMenuByUserId" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        select
            m.*
        from sys_menu m
     left join sys_role_menu rm on rm.menu_id = m.id
     left join sys_user_role ur on rm.role_id = ur.role_id
    <where>
        m.type IN ( 'catalog', 'menu' )
        AND m.STATUS = 0
        and m.is_del = 0
        <if test="userId != null">
            and ur.user_id = #{userId}
        </if>
        <if test="isAdmin != null and isAdmin != ''">
            and FIND_IN_SET('sassAdmin',m.model_type)
        </if>
    </where>
        order by m.sort
    </select>


    <delete id="deleteByRoleId">
        delete from sys_role_menu
        where rm.role_id = #{roleId}
    </delete>
    
    <insert id="insertRoles" >
        insert into  sys_role_menu (role_id,menu_id) values
        <foreach item="menuId" collection="array" open="(" separator="," close=")">
            (#{roleId}, #{menuId})
        </foreach>
    </insert>
</mapper>