<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysTenantPackageMapper">
    

    <sql id="selectSysTenantPackageVo">
        select id, name, status, remark, menu_ids, creator, create_time, updater, update_time from sys_tenant_package
    </sql>

    <select id="selectSysTenantPackageList" parameterType="com.xgwc.user.entity.vo.SysTenantPackageQueryVo" resultType="com.xgwc.user.entity.dto.SysTenantPackageDto">
        <include refid="selectSysTenantPackageVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="creator != null  and creator != ''"> and creator like concat('%', #{creator}, '%')</if>
            <if test="createTime != null "> and create_time between #{createTime}[0] and #{createTime}[1] </if>
            <if test="updater != null  and updater != ''"> and updater like concat('%', #{updater}, '%')</if>
            <if test="updateTime != null "> and update_time between #{updateTime}[0] and #{updateTime}[1] </if>
        </where>
    </select>
    
    <select id="selectSysTenantPackageByTenantId" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysTenantPackageDto">
        SELECT
            stp.menu_ids
        FROM
            sys_tenant_package stp
                LEFT JOIN sys_tenant st on st.package_id = stp.id
        WHERE
            st.id = #{id}
    </select>

    <select id="selectSysTenantPackageById" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysTenantPackageDto">
        <include refid="selectSysTenantPackageVo"/>
        where id = #{id}
    </select>


    <insert id="insertSysTenantPackage" parameterType="com.xgwc.user.entity.SysTenantPackage">
        insert into sys_tenant_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="menuIds != null">menu_ids,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="menuIds != null">#{menuIds},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysTenantPackage" parameterType="com.xgwc.user.entity.SysTenantPackage">
        update sys_tenant_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="menuIds != null">menu_ids = #{menuIds},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysTenantPackageById" parameterType="Long">
        delete from sys_tenant_package where id = #{id}
    </delete>

    <delete id="deleteSysTenantPackageByIds" parameterType="String">
        delete from sys_tenant_package where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>