<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.XgwcEmployeesMapper">


    <sql id="selectEmployeesVo">
        select employee_id,staff_id, name, sex, ethnicity, birthdate,education, political_status, marital_status, id_number, email, phone, address, emer_name, emer_phone, entry_date, contract_end_date, social_status, probation_status, probation_end_date, annual_leave_days, resignation_date, create_by, create_time, update_by, update_time, modify_time from xgwc_employees
    </sql>

    <delete id="deleteXgwcEmpAttachments">
        DELETE FROM xgwc_emp_attachments WHERE employee_id = #{employeeId}
    </delete>

    <select id="selectEmployeesList" parameterType="com.xgwc.user.entity.vo.XgwcEmployeesQueryVo" resultType="com.xgwc.user.entity.dto.XgwcEmployeesDto">
        SELECT
        xe.`employee_id` as employeeId,
        xe.`staff_id` as staffId,
        xe.`name`,
        xs.`stage_name` as stageName,
        xe.`sex`,
        xs.`job_nature` as jobNature,
        xs.`status`,
        xbs.`station_name` as stationName,
        xbd.`dept_name` as deptName,
        sc.`name` as companyName,
        xe.`social_status` as socialStatus,
        xe.`phone`,
        xe.`entry_date` as entryDate,
        xe.`contract_end_date` as contractEndDate,
        xe.`resignation_date` as resignationDate,
        xe.`education`,
        xe.`birthdate`
        FROM `xgwc_employees` xe
        LEFT JOIN xgwc_emp_accounts eac on xe.employee_id = eac.employee_id
        LEFT JOIN xgwc_staff xs on xe.staff_id = xs.id
        LEFT JOIN xgwc_brand_dept xbd on xs.dept_id = xbd.dept_id
        LEFT JOIN xgwc_brand_station xbs on xs.post_id = xbs.station_id
        LEFT JOIN sys_company sc on xs.company_id = sc.id
        <where>
            xe.`brand_owner_id` = #{employees.brandOwnerId}

            <if test="employees.name != null and employees.name != ''">
                and (
                xe.name = #{employees.name}
                or xs.stage_name = #{employees.name}
                or xe.phone = #{employees.name}
                or xe.id_number = #{employees.name}
                or eac.account_number = #{employees.name}
                or eac.alipay_account = #{employees.name}
                )
            </if>
            <if test="employees.entryDate != null ">
                and xe.entry_date between #{employees.entryDate[0]} and #{employees.entryDate[1]}
            </if>
            <if test="employees.contractEndDate != null ">
                and xe.contract_end_date between #{employees.contractEndDate[0]} and #{employees.contractEndDate[1]}
            </if>
            <if test="employees.birthdate != null ">
                and xe.birthdate between #{employees.birthdate[0]} and #{employees.birthdate[1]}
            </if>
            <if test="employees.resignationDate != null ">
                and xe.resignation_date between #{employees.resignationDate[0]} and #{employees.resignationDate[1]}
            </if>
            <if test="employees.companyId != null">
                and sc.`id` = #{employees.companyId}
            </if>
            <if test="employees.deptId != null">
                and xbd.`dept_id` = #{employees.deptId}
            </if>
            <if test="employees.stationId != null">
                and xbs.`station_id` = #{employees.stationId}
            </if>
            <if test="employees.jobNature != null">
                and xs.`job_nature` = #{employees.jobNature}
            </if>
            <if test="employees.status != null">
                and xs.`status` = #{employees.status}
            </if>
            <if test="employees.socialStatus != null">
                and xe.`social_status` = #{employees.socialStatus}
            </if>
            <if test="employees.education != null">
                and xe.`education` = #{employees.education}
            </if>
        </where>
        ORDER BY xe.`employee_id` DESC
    </select>

    <select id="selectEmployeesByEmployeeId" parameterType="Long" resultType="com.xgwc.user.entity.dto.XgwcEmployeesDto">
        select
            xe.employee_id as employeeId,
            xe.staff_id as staffId,
            xe.`name`,
            xe.sex,
            xe.ethnicity,
            xe.birthdate,
            xe.education,
            xe.political_status as politicalStatus,
            xe.marital_status as maritalStatus,
            xe.id_number as idNumber,
            xe.email,
            xe.phone,
            xe.address,
            xe.emer_name as emerName,
            xe.emer_phone as emerPhone,
            xe.entry_date as entryDate,
            xe.contract_end_date as contractEndDate,
            xe.social_status as socialStatus,
            xe.buy_social_date as buySocialDate,
            xe.probation_status as probationStatus,
            xe.probation_end_date as probationEndDate,
            xe.annual_leave_days as annualLeaveDays,
            xe.resignation_date as resignationDate,
            eac.account_name as accountName,
            eac.bank_name as bankName,
            eac.account_number as accountNumber,
            eac.alipay_name as alipayName,
            eac.alipay_account as alipayAccount
        from xgwc_employees xe
                 LEFT JOIN xgwc_emp_accounts eac on xe.employee_id = eac.employee_id
        where xe.staff_id = #{employeeId}
    </select>
    <select id="selectXgwcEmpAttachments" resultType="com.xgwc.user.entity.XgwcEmpAttachments">
            select
                eat.attachment_id as attachmentId,
                eat.employee_id as employeeId,
                eat.attachment_type as attachmentType,
                eat.file_name as fileName,
                eat.file_path as filePath,
                eat.upload_date as uploadDate
            from xgwc_employees xe
                     LEFT JOIN xgwc_emp_attachments eat on xe.employee_id = eat.employee_id
            where xe.employee_id=#{employeeId}
    </select>

    <!-- 身份证号校验 -->
    <select id="existsByIdNumber" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM xgwc_employees
        WHERE id_number = #{idNumber}
    </select>

    <!-- 手机号校验 -->
    <select id="existsByPhone" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM xgwc_employees
        WHERE phone = #{phone}
    </select>

    <!-- 银行卡号校验 -->
    <select id="existsByAccountNumber" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM xgwc_emp_accounts
        WHERE account_number = #{accountNumber}
    </select>

    <!-- 支付宝账号校验 -->
    <select id="existsByAlipayAccount" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM xgwc_emp_accounts
        WHERE alipay_account = #{alipayAccount}
    </select>

    <insert id="insertEmployees" parameterType="com.xgwc.user.entity.XgwcEmployees" useGeneratedKeys="true" keyProperty="employeeId">
        insert into xgwc_employees
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xgwcEmployees.employeeId != null">employee_id,</if>
            <if test="xgwcEmployees.staffId != null">staff_id,</if>
            <if test="xgwcEmployees.accountId != null">account_id,</if>
            <if test="xgwcEmployees.brandOwnerId != null">brand_owner_id,</if>
            <if test="xgwcEmployees.attachmentId != null">attachment_id,</if>
            <if test="xgwcEmployees.name != null">`name`,</if>
            <if test="xgwcEmployees.sex != null">sex,</if>
            <if test="xgwcEmployees.ethnicity != null">ethnicity,</if>
            <if test="xgwcEmployees.birthdate != null">birthdate,</if>
            <if test="xgwcEmployees.education != null">education,</if>
            <if test="xgwcEmployees.politicalStatus != null">political_status,</if>
            <if test="xgwcEmployees.maritalStatus != null">marital_status,</if>
            <if test="xgwcEmployees.idNumber != null">id_number,</if>
            <if test="xgwcEmployees.email != null">email,</if>
            <if test="xgwcEmployees.phone != null">phone,</if>
            <if test="xgwcEmployees.address != null">address,</if>
            <if test="xgwcEmployees.emerName != null">emer_name,</if>
            <if test="xgwcEmployees.emerPhone != null">emer_phone,</if>
            <if test="xgwcEmployees.entryDate != null">entry_date,</if>
            <if test="xgwcEmployees.contractEndDate != null">contract_end_date,</if>
            <if test="xgwcEmployees.socialStatus != null">social_status,</if>
            <if test="xgwcEmployees.buySocialDate != null">buy_social_date,</if>
            <if test="xgwcEmployees.probationStatus != null">probation_status,</if>
            <if test="xgwcEmployees.probationEndDate != null">probation_end_date,</if>
            <if test="xgwcEmployees.annualLeaveDays != null">annual_leave_days,</if>
            <if test="xgwcEmployees.resignationDate != null">resignation_date,</if>
            <if test="xgwcEmployees.createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xgwcEmployees.employeeId != null">#{xgwcEmployees.employeeId},</if>
            <if test="xgwcEmployees.staffId != null">#{xgwcEmployees.staffId},</if>
            <if test="xgwcEmployees.accountId != null">#{xgwcEmployees.accountId},</if>
            <if test="xgwcEmployees.brandOwnerId != null">#{xgwcEmployees.brandOwnerId},</if>
            <if test="xgwcEmployees.attachmentId != null">#{xgwcEmployees.attachmentId},</if>
            <if test="xgwcEmployees.name != null">#{xgwcEmployees.name},</if>
            <if test="xgwcEmployees.sex != null">#{xgwcEmployees.sex},</if>
            <if test="xgwcEmployees.ethnicity != null">#{xgwcEmployees.ethnicity},</if>
            <if test="xgwcEmployees.birthdate != null">#{xgwcEmployees.birthdate},</if>
            <if test="xgwcEmployees.education != null">#{xgwcEmployees.education},</if>
            <if test="xgwcEmployees.politicalStatus != null">#{xgwcEmployees.politicalStatus},</if>
            <if test="xgwcEmployees.maritalStatus != null">#{xgwcEmployees.maritalStatus},</if>
            <if test="xgwcEmployees.idNumber != null">#{xgwcEmployees.idNumber},</if>
            <if test="xgwcEmployees.email != null">#{xgwcEmployees.email},</if>
            <if test="xgwcEmployees.phone != null">#{xgwcEmployees.phone},</if>
            <if test="xgwcEmployees.address != null">#{xgwcEmployees.address},</if>
            <if test="xgwcEmployees.emerName != null">#{xgwcEmployees.emerName},</if>
            <if test="xgwcEmployees.emerPhone != null">#{xgwcEmployees.emerPhone},</if>
            <if test="xgwcEmployees.entryDate != null">#{xgwcEmployees.entryDate},</if>
            <if test="xgwcEmployees.contractEndDate != null">#{xgwcEmployees.contractEndDate},</if>
            <if test="xgwcEmployees.socialStatus != null">#{xgwcEmployees.socialStatus},</if>
            <if test="xgwcEmployees.buySocialDate != null">#{xgwcEmployees.buySocialDate},</if>
            <if test="xgwcEmployees.probationStatus != null">#{xgwcEmployees.probationStatus},</if>
            <if test="xgwcEmployees.probationEndDate != null">#{xgwcEmployees.probationEndDate},</if>
            <if test="xgwcEmployees.annualLeaveDays != null">#{xgwcEmployees.annualLeaveDays},</if>
            <if test="xgwcEmployees.resignationDate != null">#{xgwcEmployees.resignationDate},</if>
            <if test="xgwcEmployees.createBy != null">#{xgwcEmployees.createBy},</if>
            now()
         </trim>
    </insert>

    <insert id="insertXgwcEmpAccounts" parameterType="com.xgwc.user.entity.XgwcEmpAccounts" useGeneratedKeys="true" keyProperty="accountId">
        insert into xgwc_emp_accounts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xgwcEmpAccounts.accountName != null">account_name,</if>
            <if test="xgwcEmpAccounts.employeeId != null">employee_id,</if>
            <if test="xgwcEmpAccounts.bankName != null">bank_name,</if>
            <if test="xgwcEmpAccounts.accountNumber != null">account_number,</if>
            <if test="xgwcEmpAccounts.alipayName != null">alipay_name,</if>
            <if test="xgwcEmpAccounts.alipayAccount != null">alipay_account,</if>
            <if test="xgwcEmpAccounts.createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xgwcEmpAccounts.accountName != null">#{xgwcEmpAccounts.accountName},</if>
            <if test="xgwcEmpAccounts.employeeId != null">#{xgwcEmpAccounts.employeeId},</if>
            <if test="xgwcEmpAccounts.bankName != null">#{xgwcEmpAccounts.bankName},</if>
            <if test="xgwcEmpAccounts.accountNumber != null">#{xgwcEmpAccounts.accountNumber},</if>
            <if test="xgwcEmpAccounts.alipayName != null">#{xgwcEmpAccounts.alipayName},</if>
            <if test="xgwcEmpAccounts.alipayAccount != null">#{xgwcEmpAccounts.alipayAccount},</if>
            <if test="xgwcEmpAccounts.createBy != null">#{xgwcEmpAccounts.createBy},</if>
            now()
        </trim>
    </insert>

    <insert id="insertXgwcEmpAttachments" parameterType="com.xgwc.user.entity.XgwcEmpAttachments" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into xgwc_emp_attachments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xgwcEmpAttachments.employeeId != null">employee_id,</if>
            <if test="xgwcEmpAttachments.attachmentType != null">attachment_type,</if>
            <if test="xgwcEmpAttachments.fileName != null">file_name,</if>
            <if test="xgwcEmpAttachments.filePath != null">file_path,</if>
            <if test="xgwcEmpAttachments.fileCount != null">file_count,</if>
            upload_date,
            <if test="xgwcEmpAttachments.createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xgwcEmpAttachments.employeeId != null">#{xgwcEmpAttachments.employeeId},</if>
            <if test="xgwcEmpAttachments.attachmentType != null">#{xgwcEmpAttachments.attachmentType},</if>
            <if test="xgwcEmpAttachments.fileName != null">#{xgwcEmpAttachments.fileName},</if>
            <if test="xgwcEmpAttachments.filePath != null">#{xgwcEmpAttachments.filePath},</if>
            <if test="xgwcEmpAttachments.fileCount != null">#{xgwcEmpAttachments.fileCount},</if>
            now(),
            <if test="xgwcEmpAttachments.createBy != null">#{xgwcEmpAttachments.createBy},</if>
            now()
        </trim>
    </insert>

    <update id="updateEmployees" parameterType="com.xgwc.user.entity.XgwcEmployees">
        update xgwc_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="xgwcEmployees.name != null">name = #{xgwcEmployees.name},</if>
            <if test="xgwcEmployees.staffId != null">staff_id = #{xgwcEmployees.staffId},</if>
            <if test="xgwcEmployees.accountId != null">account_id = #{xgwcEmployees.accountId},</if>
            <if test="xgwcEmployees.attachmentId != null">attachment_id = #{xgwcEmployees.attachmentId},</if>
            <if test="xgwcEmployees.sex != null">sex = #{xgwcEmployees.sex},</if>
            <if test="xgwcEmployees.ethnicity != null">ethnicity = #{xgwcEmployees.ethnicity},</if>
            <if test="xgwcEmployees.birthdate != null">birthdate = #{xgwcEmployees.birthdate},</if>
            <if test="xgwcEmployees.education != null">education = #{xgwcEmployees.education},</if>
            <if test="xgwcEmployees.politicalStatus != null">political_status = #{xgwcEmployees.politicalStatus},</if>
            <if test="xgwcEmployees.maritalStatus != null">marital_status = #{xgwcEmployees.maritalStatus},</if>
            <if test="xgwcEmployees.idNumber != null">id_number = #{xgwcEmployees.idNumber},</if>
            <if test="xgwcEmployees.email != null">email = #{xgwcEmployees.email},</if>
            <if test="xgwcEmployees.phone != null">phone = #{xgwcEmployees.phone},</if>
            <if test="xgwcEmployees.address != null">address = #{xgwcEmployees.address},</if>
            <if test="xgwcEmployees.emerName != null">emer_name = #{xgwcEmployees.emerName},</if>
            <if test="xgwcEmployees.emerPhone != null">emer_phone = #{xgwcEmployees.emerPhone},</if>
            <if test="xgwcEmployees.entryDate != null">entry_date = #{xgwcEmployees.entryDate},</if>
            <if test="xgwcEmployees.contractEndDate != null">contract_end_date = #{xgwcEmployees.contractEndDate},</if>
            <if test="xgwcEmployees.socialStatus != null">social_status = #{xgwcEmployees.socialStatus},</if>
            <if test="xgwcEmployees.buySocialDate != null">buy_social_date = #{xgwcEmployees.buySocialDate},</if>
            <if test="xgwcEmployees.probationStatus != null">probation_status = #{xgwcEmployees.probationStatus},</if>
            <if test="xgwcEmployees.probationEndDate != null">probation_end_date = #{xgwcEmployees.probationEndDate},</if>
            <if test="xgwcEmployees.annualLeaveDays != null">annual_leave_days = #{xgwcEmployees.annualLeaveDays},</if>
            <if test="xgwcEmployees.resignationDate != null">resignation_date = #{xgwcEmployees.resignationDate},</if>
            <if test="xgwcEmployees.updateBy != null">update_by = #{xgwcEmployees.updateBy},</if>
            <if test="xgwcEmployees.updateTime != null">update_time = now(),</if>
        </trim>
        where employee_id = #{xgwcEmployees.employeeId}
    </update>
    <update id="updateXgwcEmpAccounts" parameterType="com.xgwc.user.entity.XgwcEmpAccounts">
        update xgwc_emp_accounts
        <trim prefix="SET" suffixOverrides=",">
            <if test="xgwcEmpAccounts.accountName != null">account_name = #{xgwcEmpAccounts.accountName},</if>
            <if test="xgwcEmpAccounts.bankName != null">bank_name = #{xgwcEmpAccounts.bankName},</if>
            <if test="xgwcEmpAccounts.accountNumber != null">account_number = #{xgwcEmpAccounts.accountNumber},</if>
            <if test="xgwcEmpAccounts.alipayName != null">alipay_name = #{xgwcEmpAccounts.alipayName},</if>
            <if test="xgwcEmpAccounts.alipayAccount != null">alipay_account = #{xgwcEmpAccounts.alipayAccount},</if>
            <if test="xgwcEmpAccounts.updateBy != null">update_by = #{xgwcEmpAccounts.updateBy},</if>
            <if test="xgwcEmpAccounts.updateTime != null">update_time = now(),</if>
        </trim>
            where employee_id = #{xgwcEmpAccounts.employeeId}
    </update>
    <update id="updateXgwcEmpAttachments" parameterType="com.xgwc.user.entity.XgwcEmpAttachments">
        update xgwc_emp_attachments
        <trim prefix="SET" suffixOverrides=",">
            <if test="xgwcEmpAttachments.attachmentType != null">attachment_type = #{xgwcEmpAttachments.attachmentType},</if>
            <if test="xgwcEmpAttachments.fileName != null">file_name = #{xgwcEmpAttachments.fileName},</if>
            <if test="xgwcEmpAttachments.filePath != null">file_path = #{xgwcEmpAttachments.filePath},</if>
            <if test="xgwcEmpAttachments.uploadDate != null">upload_date = #{xgwcEmpAttachments.uploadDate},</if>
            <if test="xgwcEmpAttachments.updateBy != null">update_by = #{xgwcEmpAttachments.updateBy},</if>
            <if test="xgwcEmpAttachments.updateTime != null">update_time = now(),</if>
        </trim>
            where employee_id = #{xgwcEmpAttachments.employeeId} and attachment_type = #{xgwcEmpAttachments.attachmentType}
    </update>
</mapper>