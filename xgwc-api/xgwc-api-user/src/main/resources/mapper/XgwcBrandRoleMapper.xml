<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.XgwcBrandRoleMapper">
    <insert id="saveXgwcBrandRole" useGeneratedKeys="true" keyProperty="roleId">
        INSERT INTO `xgwc_sass`.`xgwc_brand_role`
        (`role_id`, `role_name`, `brand_owner_id`, `is_flag`,`sort`, `create_by`, `create_time`)
        VALUES (#{xgwcBrandRoleDto.roleId}, #{xgwcBrandRoleDto.roleName}, #{xgwcBrandRoleDto.brandOwnerId}, #{xgwcBrandRoleDto.isFlag}, #{xgwcBrandRoleDto.sort},
                #{xgwcBrandRoleDto.createBy}, now())
    </insert>

    <insert id="saveXgwcBrandRoleMenu">
        INSERT INTO `xgwc_sass`.`xgwc_role_menu`
        (`role_id`, `menu_id`, `create_by`, `create_time`)
        VALUES
        <foreach collection="xgwcBrandRoleDto.menuIds" item="menuId" separator=",">
            (#{xgwcBrandRoleDto.roleId}, #{menuId.menuId}, #{xgwcBrandRoleDto.createBy}, now())
        </foreach>
    </insert>

    <insert id="saveXgwcBrandRoleDataScope">
        INSERT INTO `xgwc_sass`.`xgwc_role_data`
        (`role_id`, `menu_id`, `data_scope`, `dept_id`, `business_ids`, `time_limit`, `data_masking`,`download_limit` , `create_by`, `create_time`)
        VALUES
        (#{dataMenu.roleId}, #{dataMenu.menuId}, #{dataMenu.dataScope}, #{dataMenu.deptId}, #{dataMenu.businessIds}, #{dataMenu.timeLimit},
         #{dataMenu.dataMasking},#{dataMenu.downloadLimit} , #{dataMenu.createBy}, now())
    </insert>

    <insert id="saveXgwcBrandRoleDataScopeDeptId">
        INSERT INTO `xgwc_sass`.`xgwc_role_data`
        (`role_id`, `menu_id`, `data_scope`, `dept_id`, `time_limit`, `data_masking`, `download_limit`, `create_by`, `create_time`)
        VALUES
        <foreach collection="dataMenu.deptIds" item="item" separator=",">
            (#{dataMenu.roleId}, #{dataMenu.menuId}, #{dataMenu.dataScope}, #{item}, #{dataMenu.timeLimit},
             #{dataMenu.dataMasking}, #{dataMenu.downloadLimit}, #{dataMenu.createBy}, now())
        </foreach>
    </insert>

    <insert id="insertBrandRoleMenu">
        insert into xgwc_role_menu (role_id, menu_id, create_time)
        values
        <foreach item="item" collection="array" separator="," >
            (#{item.roleId} , #{item.menuId}, now())
        </foreach>
    </insert>

    <insert id="insertBrandRoleUser">
        insert into xgwc_user_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">role_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="isFlag != null">is_flag,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">#{roleId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="isFlag != null">#{isFlag},</if>
            now()
        </trim>
    </insert>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`xgwc_brand_role`
        SET `status` = #{status}
        WHERE `role_id` = #{roleId}
    </update>
    <update id="updateXgwcBrandRole">
        UPDATE `xgwc_sass`.`xgwc_brand_role`
        <set>
            <if test="xgwcBrandRoleDto.roleName != null and xgwcBrandRoleDto.roleName != ''">
                `role_name` = #{xgwcBrandRoleDto.roleName},
            </if>
            <if test="xgwcBrandRoleDto.isFlag != null and xgwcBrandRoleDto.isFlag != ''">
                `is_flag` = #{xgwcBrandRoleDto.isFlag},
            </if>
            <if test="xgwcBrandRoleDto.sort != null and xgwcBrandRoleDto.sort != ''">
                `sort`= #{xgwcBrandRoleDto.sort},
            </if>
            <if test="xgwcBrandRoleDto.updateBy != null and xgwcBrandRoleDto.updateBy != ''">
                `update_by` = #{xgwcBrandRoleDto.updateBy},
            </if>
            `update_time` = now()
        </set>
        WHERE `role_id` = #{xgwcBrandRoleDto.roleId}
    </update>

    <delete id="deleteXgwcBrandRoleMenu">
        DELETE FROM `xgwc_sass`.`xgwc_role_menu`
        WHERE `role_id` = #{roleId}
    </delete>

    <delete id="deleteXgwcBrandRoleDataScope">
        DELETE FROM `xgwc_sass`.`xgwc_role_data`
        WHERE `role_id` = #{roleId}
    </delete>

    <delete id="deleteBrandRoleUser">
        delete from xgwc_user_role where user_id = #{userId}
    </delete>

    <select id="getXgwcBrandRoleList" resultType="com.xgwc.user.entity.vo.XgwcBrandRoleVo">
        SELECT
        `role_id` AS roleId,
        `role_name` AS roleName,
        `is_flag` AS isFlag,
        `sort` AS sort,
        `status` AS status,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`xgwc_brand_role`
        <where>

                AND `brand_owner_id` = #{xgwcBrandRoleParam.brandOwnerId}

            <if test="xgwcBrandRoleParam.roleName != null and xgwcBrandRoleParam.roleName != ''">
                AND `role_name` = #{xgwcBrandRoleParam.roleName}
            </if>
            <if test="xgwcBrandRoleParam.status != null">
                AND `status` = #{xgwcBrandRoleParam.status}
            </if>
        </where>
        order by sort
    </select>

    <select id="getXgwcBrandRoleById" resultType="com.xgwc.user.entity.vo.XgwcBrandRoleVo">
        SELECT `role_id`     AS roleId,
               `role_name`   AS roleName,
               `is_flag`     AS isFlag,
               `sort`        AS sort
        FROM `xgwc_sass`.`xgwc_brand_role`
        WHERE `role_id` = #{roleId}
    </select>
    <select id="getXgwcBrandRoleMenuById" resultType="com.xgwc.user.entity.vo.SysRoleMenuVo">
        SELECT
            xgwc_role_menu.`id` AS  id,
            xgwc_role_menu.`role_id` AS roleId,
            xgwc_role_menu.`menu_id` AS menuId,
            sm.pid,
            sm.`name` AS menuName
        FROM `xgwc_sass`.`xgwc_role_menu` AS xgwc_role_menu
        left join `xgwc_sass`.`sys_menu` AS sm on sm.id = xgwc_role_menu.menu_id
        WHERE `role_id` = #{roleId} and sm.is_del = 0
    </select>
    <select id="getXgwcBrandRoleDataById" resultType="com.xgwc.user.entity.vo.SysRoleDataVo">
        SELECT
            xrd.`id` AS id,
            xrd.`role_id` AS roleId,
            xrd.`menu_id` AS menuId,
            sm.`name` AS menuName,
            sm.`pid`,
            xrd.`data_scope` AS dataScope,
            xrd.`business_ids` AS businessIds,
            xrd.`dept_id` AS deptId,
            xrd.`time_limit` AS timeLimit,
            xrd.`data_masking` AS dataMasking,
            xrd.`download_limit` AS downloadLimit
        FROM `xgwc_sass`.`xgwc_role_data` xrd
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = xrd.menu_id
        WHERE `role_id` = #{roleId} and sm.is_del = 0
    </select>

    <select id="findBrandRoleKeyByUserId" resultType="java.lang.String">
        select DISTINCT r.is_flag
        from
            xgwc_brand_role r
        left join xgwc_user_role ur on r.role_id = ur.role_id
        where ur.user_id = #{userId} and r.status = 0
    </select>

    <select id="findMenuByUserId" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        select  m.*
        from sys_menu m
        -- left join xgwc_role_menu rm on rm.menu_id = m.id
        -- left join xgwc_user_role ur on rm.role_id = ur.role_id
        <where>
            m.type IN ( 'catalog', 'menu' )
            AND m.STATUS = 0
            and m.is_del = 0
            and FIND_IN_SET(#{modelType}, m.model_type)
            <if test="userId != null">
                and ur.user_id = #{userId}
            </if>
        </where>
        order by m.sort
    </select>

    <select id="selectRoleByUserId" resultType="com.xgwc.user.entity.vo.XgwcBrandRoleVo">
        SELECT
            xbr.*
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`xgwc_brand_role` AS xbr on xbr.role_id = xur.role_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 0 and xbr.status = 0
    </select>

    <select id="selectMenuByUserId" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        SELECT
            sm.*
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`xgwc_brand_role` AS xbr on xbr.role_id = xur.role_id
                 left join `xgwc_sass`.`xgwc_role_menu` AS xrm on xbr.role_id = xrm.role_id
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = xrm.menu_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 0 and sm.is_del = 0 and xbr.status = 0 and sm.type IN ( 'catalog', 'menu' )
        order by sm.sort
    </select>
    <select id="getXgwcBrandRoleDataByIds" resultType="java.lang.Integer">
        SELECT
        MAX(download_limit) AS maxDownloadLimit
        FROM `xgwc_sass`.`xgwc_role_data`
        WHERE `role_id` IN
        <foreach item="item" collection="roleIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="selectRoleByMenu" resultType="com.xgwc.user.entity.vo.SysRoleMenuVo">
        SELECT
            xrm.`id` AS id,
            xrm.`role_id` AS roleId,
            xrm.`menu_id` AS menuId
        FROM `xgwc_sass`.`xgwc_brand_role` AS xbr
                 left join `xgwc_sass`.`xgwc_role_menu` AS xrm on xbr.role_id = xrm.role_id
        WHERE xrm.menu_id = #{menuId} and xbr.brand_owner_id = #{brandId} and xbr.status = 0
    </select>
    <select id="selectLastLevelMenu" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM sys_menu
        WHERE pid = #{menuId} and is_del = 0 and FIND_IN_SET('brandAdmin',model_type)
    </select>
    <select id="selectLastLevelMenuData" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM sys_menu
        WHERE pid = #{menuId} and is_del = 0 and FIND_IN_SET('brandAdmin',model_type) and type IN ( 'catalog', 'menu' )
    </select>

    <select id="findMenuByModelType" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        select  m.*
        from sys_menu m
        where m.STATUS = 0
        and m.is_del = 0
        and FIND_IN_SET(#{modelType}, m.model_type)
    </select>

</mapper>

