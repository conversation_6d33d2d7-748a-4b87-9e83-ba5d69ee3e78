<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysMenuMapper">
    

    <sql id="selectSysMenuVo">
        select
            active_icon,
            active_path,
            affix_tab,
            auth_code,
            component,
            create_by,
            create_time,
            hide_in_breadcrumb,
            hide_in_menu,
            hide_in_tab,
            hidechildren_in_menu,
            icon,
            id,
            is_del,
            keep_alive,
            modify_time,
            name,
            path,
            pid,
            remark,
            status,
            title,
            type,
            update_by,
            update_time,
            sort,
            model_type
        from sys_menu
    </sql>

    <select id="selectSysMenuList" parameterType="com.xgwc.user.entity.vo.SysMenuQueryVo" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        <include refid="selectSysMenuVo"/>
        <where>
           is_del = 0
            <if test="authCode != null  and authCode != ''"> and auth_code like concat('%', #{authCode}, '%')</if>
            <if test="component != null  and component != ''"> and component like concat('%', #{component}, '%')</if>
            <if test="id != null "> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="path != null  and path != ''"> and path like concat('%', #{path}, '%')</if>
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="modelType != null "> and model_type = #{modelType}</if>
        </where>
        order by sort
    </select>
    
    <select id="selectSysMenuById" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        <include refid="selectSysMenuVo"/>
        where id = #{id}
    </select>
    <select id="getMenuTreeData" resultType="com.xgwc.user.entity.vo.SysMenuVo">
        select  id,
                name,
                pid,
                type,
                model_type as modelType
        from sys_menu
        <where>
            is_del = 0 and type IN ( 'catalog', 'menu' )
            <if test="isFlag != null">
             and FIND_IN_SET(#{isFlag}, model_type)
            </if>
        </where>
        order by sort
    </select>

    <select id="selectSysMenuByModelType" resultType="com.xgwc.user.entity.dto.SysMenuDto">
        <include refid="selectSysMenuVo"/>
        where is_del = 0 and FIND_IN_SET(#{modelType}, model_type)
    </select>
    <select id="selectMenuTreeByModelType" resultType="java.lang.String">
        select DISTINCT auth_code
        from sys_menu
        where is_del = 0 and FIND_IN_SET(#{modelType}, model_type)
    </select>
    <select id="selectSysMenuBrandByUserId" resultType="java.lang.String">
        SELECT DISTINCT sm.auth_code
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`xgwc_brand_role` AS xbr on xbr.role_id = xur.role_id
                 left join `xgwc_sass`.`xgwc_role_menu` AS xrm on xbr.role_id = xrm.role_id
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = xrm.menu_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 0 and sm.is_del = 0 and xbr.status = 0;
    </select>
    <select id="selectSysMenuFranchiseByUserId" resultType="java.lang.String">
        SELECT DISTINCT sm.auth_code
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`franchise_role` AS fr on fr.role_id = xur.role_id
                 left join `xgwc_sass`.`franchise_role_menu` AS frm on fr.role_id = frm.role_id
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frm.menu_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 1 and sm.is_del = 0 and fr.status = 0;
    </select>
    <select id="getMenuTree" resultType="com.xgwc.user.entity.vo.SysMenuVo">
        select  id,
        name,
        pid,
        type,
        model_type as modelType
        from sys_menu
        <where>
            is_del = 0
            <if test="isFlag != null">
                and FIND_IN_SET(#{isFlag}, model_type)
            </if>
        </where>
        order by sort
    </select>
    <select id="selectSysMenuServiceByUserId" resultType="java.lang.String">
        SELECT DISTINCT sm.auth_code
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`service_role` AS fr on fr.role_id = xur.role_id
                 left join `xgwc_sass`.`service_role_menu` AS frm on fr.role_id = frm.role_id
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frm.menu_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 2 and sm.is_del = 0 and fr.status = 0;
    </select>
    <select id="selectSysMenuMarketByUserId" resultType="java.lang.String">
        SELECT DISTINCT sm.auth_code
        FROM `xgwc_sass`.`xgwc_user_role` AS xur
                 left join `xgwc_sass`.`market_role` AS fr on fr.role_id = xur.role_id
                 left join `xgwc_sass`.`market_role_menu` AS frm on fr.role_id = frm.role_id
                 left join `xgwc_sass`.`sys_menu` AS sm on sm.id = frm.menu_id
        WHERE xur.user_id = #{userId} and xur.is_flag = 3 and sm.is_del = 0 and fr.status = 0;
    </select>

    <insert id="insertSysMenu" parameterType="com.xgwc.user.entity.SysMenu">
        insert into sys_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activeIcon != null">active_icon,</if>
            <if test="activePath != null">active_path,</if>
            <if test="affixTab != null">affix_tab,</if>
            <if test="authCode != null">auth_code,</if>
            <if test="component != null">component,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="hideInBreadcrumb != null">hide_in_breadcrumb,</if>
            <if test="hideInMenu != null">hide_in_menu,</if>
            <if test="hideInTab != null">hide_in_tab,</if>
            <if test="hidechildrenInMenu != null">hidechildren_in_menu,</if>
            <if test="icon != null">icon,</if>
            <if test="id != null">id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="keepAlive != null">keep_alive,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="name != null">name,</if>
            <if test="path != null">path,</if>
            <if test="pid != null">pid,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="title != null">title,</if>
            <if test="type != null">type,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="modelType != null">model_type,</if>
            <if test="sort != null">sort,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activeIcon != null">#{activeIcon},</if>
            <if test="activePath != null">#{activePath},</if>
            <if test="affixTab != null">#{affixTab},</if>
            <if test="authCode != null">#{authCode},</if>
            <if test="component != null">#{component},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="hideInBreadcrumb != null">#{hideInBreadcrumb},</if>
            <if test="hideInMenu != null">#{hideInMenu},</if>
            <if test="hideInTab != null">#{hideInTab},</if>
            <if test="hidechildrenInMenu != null">#{hidechildrenInMenu},</if>
            <if test="icon != null">#{icon},</if>
            <if test="id != null">#{id},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="keepAlive != null">#{keepAlive},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="name != null">#{name},</if>
            <if test="path != null">#{path},</if>
            <if test="pid != null">#{pid},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="title != null">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modelType != null">#{modelType},</if>
            <if test="sort != null">#{sort},</if>
         </trim>
    </insert>

    <update id="updateSysMenu" parameterType="com.xgwc.user.entity.SysMenu">
        update sys_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="activeIcon != null">active_icon = #{activeIcon},</if>
            <if test="activePath != null">active_path = #{activePath},</if>
            <if test="affixTab != null">affix_tab = #{affixTab},</if>
            <if test="authCode != null">auth_code = #{authCode},</if>
            <if test="component != null">component = #{component},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="hideInBreadcrumb != null">hide_in_breadcrumb = #{hideInBreadcrumb},</if>
            <if test="hideInMenu != null">hide_in_menu = #{hideInMenu},</if>
            <if test="hideInTab != null">hide_in_tab = #{hideInTab},</if>
            <if test="hidechildrenInMenu != null">hidechildren_in_menu = #{hidechildrenInMenu},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="keepAlive != null">keep_alive = #{keepAlive},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="name != null">name = #{name},</if>
            <if test="path != null">path = #{path},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="title != null">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modelType != null">model_type = #{modelType},</if>
            <if test="sort != null">sort = #{sort},</if>

        </trim>
        where id = #{id}
    </update>

    <update id="deleteSysMenuById" parameterType="Long">
        update sys_menu set is_del = 1 where id = #{id}
    </update>

    <update id="deleteSysMenuByIds" parameterType="String">
        update sys_menu set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>