<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseStationMapper">
    <insert id="saveFranchiseStation">
        INSERT INTO `xgwc_sass`.`franchise_station`
        (`station_name`, `franchise_id`, `sort`, `create_by`, `create_time`)
        VALUES (#{franchiseStationDto.stationName}, #{franchiseStationDto.franchiseId}, #{franchiseStationDto.sort},
                #{franchiseStationDto.createBy}, now())
    </insert>

    <update id="updateFranchiseStation">
        UPDATE `xgwc_sass`.`franchise_station`
        <set>
            <if test="franchiseStationDto.stationName != null">
                `station_name` = #{franchiseStationDto.stationName},
            </if>
            <if test="franchiseStationDto.franchiseId != null">
                `franchise_id` = #{franchiseStationDto.franchiseId},
            </if>
            <if test="franchiseStationDto.sort != null">
                `sort` = #{franchiseStationDto.sort},
            </if>
            <if test="franchiseStationDto.updateBy != null">
                `update_by` = #{franchiseStationDto.updateBy},
            </if>
            `update_time` = now()
        </set>
       where `station_id` = #{franchiseStationDto.stationId}
    </update>

    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`franchise_station`
        SET `status` = #{status}
        WHERE `station_id` = #{stationId}
    </update>

    <select id="getFranchiseStationList" resultType="com.xgwc.user.entity.vo.FranchiseStationVo">
        SELECT
        `station_id` AS stationId,
        `station_name` AS stationName,
        `franchise_id` AS franchiseId,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`franchise_station`
        <where>
            `franchise_id` = #{franchiseStationParam.franchiseId}

            <if test="franchiseStationParam.stationName != null and franchiseStationParam.stationName !=''">
                AND `station_name` = #{franchiseStationParam.stationName}
            </if>
            <if test="franchiseStationParam.status != null">
                AND `status` = #{franchiseStationParam.status}
            </if>
            <if test="franchiseStationParam.stationId != null">
                AND `station_id` = #{franchiseStationParam.stationId}
            </if>
        </where>
        order by `sort`
    </select>

    <select id="getFranchiseStationById" resultType="com.xgwc.user.entity.vo.FranchiseStationVo">
        SELECT
        `station_id` AS stationId,
        `station_name` AS stationName,
        `franchise_id` AS franchiseId,
        `sort`,
        `status`,
        `is_del` AS isDel,
        `create_by` AS createBy,
        `create_time` AS createTime,
        `update_by` AS updateBy,
        `update_time` AS updateTime,
        `modify_time` AS modifyTime
        FROM `xgwc_sass`.`franchise_station`
        WHERE `station_id` = #{stationId}
    </select>
</mapper>