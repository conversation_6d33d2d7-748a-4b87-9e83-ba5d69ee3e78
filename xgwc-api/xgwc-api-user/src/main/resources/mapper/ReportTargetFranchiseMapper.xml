<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.ReportTargetFranchiseMapper">
    

    <sql id="selectReportTargetFranchiseVo">
        select target_id, brand_id, franchise_id, target_name, target_date, target_amount, amount_order, amount_real, target_new_customer, target_old_customer, target_transfer_amount, target_comission_rate,
               target_conversion_rate, new_customer_amount, old_customer_amount, transfer_amount, conversion_rate, comission_rate, check_status, status, create_time, update_time, create_by, create_by_id,
               update_by, update_by_id, dept_count, submit_dept_count from report_target_franchise
    </sql>

    <select id="selectReportTargetFranchiseById" parameterType="Long" resultType="com.xgwc.user.entity.ReportTargetFranchise">
        <include refid="selectReportTargetFranchiseVo"/>
        where target_id = #{targetId}
    </select>

    <insert id="insertReportTargetFranchise" parameterType="com.xgwc.user.entity.ReportTargetFranchise" keyColumn="target_id" useGeneratedKeys="true" keyProperty="targetId">
        insert into report_target_franchise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brandId != null">brand_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="targetName != null">target_name,</if>
            <if test="targetDate != null">target_date,</if>
            <if test="targetAmount != null">target_amount,</if>
            <if test="amountOrder != null">amount_order,</if>
            <if test="amountReal != null">amount_real,</if>
            <if test="targetNewCustomer != null">target_new_customer,</if>
            <if test="targetOldCustomer != null">target_old_customer,</if>
            <if test="targetTransferAmount != null">target_transfer_amount,</if>
            <if test="targetComissionRate != null">target_comission_rate,</if>
            <if test="targetConversionRate != null">target_conversion_rate,</if>
            <if test="newCustomerAmount != null">new_customer_amount,</if>
            <if test="oldCustomerAmount != null">old_customer_amount,</if>
            <if test="transferAmount != null">transfer_amount,</if>
            <if test="conversionRate != null">conversion_rate,</if>
            <if test="comissionRate != null">comission_rate,</if>
            <if test="deptCount != null">dept_count,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateById != null">update_by_id,</if>
            create_time,
            status,
            check_status
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brandId != null">#{brandId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="targetName != null">#{targetName},</if>
            <if test="targetDate != null">#{targetDate},</if>
            <if test="targetAmount != null">#{targetAmount},</if>
            <if test="amountOrder != null">#{amountOrder},</if>
            <if test="amountReal != null">#{amountReal},</if>
            <if test="targetNewCustomer != null">#{targetNewCustomer},</if>
            <if test="targetOldCustomer != null">#{targetOldCustomer},</if>
            <if test="targetTransferAmount != null">#{targetTransferAmount},</if>
            <if test="targetComissionRate != null">#{targetComissionRate},</if>
            <if test="targetConversionRate != null">#{targetConversionRate},</if>
            <if test="newCustomerAmount != null">#{newCustomerAmount},</if>
            <if test="oldCustomerAmount != null">#{oldCustomerAmount},</if>
            <if test="transferAmount != null">#{transferAmount},</if>
            <if test="conversionRate != null">#{conversionRate},</if>
            <if test="comissionRate != null">#{comissionRate},</if>
            <if test="deptCount != null">#{deptCount},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createById != null">#{createById},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateById != null">#{updateById},</if>
            now(),
            0,
            0
         </trim>
    </insert>

    <update id="updateReportTargetFranchise" parameterType="com.xgwc.user.entity.ReportTargetFranchise">
        update report_target_franchise
        <trim prefix="SET" suffixOverrides=",">
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="targetName != null">target_name = #{targetName},</if>
            <if test="targetDate != null">target_date = #{targetDate},</if>
            <if test="targetAmount != null">target_amount = #{targetAmount},</if>
            <if test="amountOrder != null">amount_order = #{amountOrder},</if>
            <if test="amountReal != null">amount_real = #{amountReal},</if>
            <if test="targetNewCustomer != null">target_new_customer = #{targetNewCustomer},</if>
            <if test="targetOldCustomer != null">target_old_customer = #{targetOldCustomer},</if>
            <if test="targetTransferAmount != null">target_transfer_amount = #{targetTransferAmount},</if>
            <if test="targetComissionRate != null">target_comission_rate = #{targetComissionRate},</if>
            <if test="targetConversionRate != null">target_conversion_rate = #{targetConversionRate},</if>
            <if test="newCustomerAmount != null">new_customer_amount = #{newCustomerAmount},</if>
            <if test="oldCustomerAmount != null">old_customer_amount = #{oldCustomerAmount},</if>
            <if test="transferAmount != null">transfer_amount = #{transferAmount},</if>
            <if test="conversionRate != null">conversion_rate = #{conversionRate},</if>
            <if test="comissionRate != null">comission_rate = #{comissionRate},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="deptCount != null">dept_count = #{deptCount},</if>
            <if test="submitDeptCount != null">submit_dept_count = #{submitDeptCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateById != null">update_by_id = #{updateById},</if>
        </trim>
        where target_id = #{targetId}
    </update>

    <select id="selectTargetFranchiseList" resultType="com.xgwc.user.entity.dto.ReportTargetFranchiseDto">
        SELECT
        t.*,
        ifnull( fo.company_simple_name, fo.company_name ) franchiseName
        FROM
        report_target_franchise t
        LEFT JOIN franchise_owner fo ON fo.franchise_id = t.franchise_id and fo.brand_id = t.brand_id
        WHERE
        t.brand_id = #{brandId}
        <if test="franchiseId != null">
            and t.franchise_id = #{franchiseId}
        </if>
        <if test="targetName != null">
            and t.target_name like concat ('%', #{targetName}, '%')
        </if>
        <if test="targetDate != null">
            and  t.target_date = #{targetDate}
        </if>
        <if test="checkStatus != null">
            and t.check_status = #{checkStatus}
        </if>
        AND t.`status` = 0
    </select>

   <update id="freshAmount">
       UPDATE report_target_franchise t
       SET t.target_amount = ( SELECT sum( target_amount ) FROM report_target_franchise_dept WHERE target_id = #{targetId} AND STATUS = 0 ),
           t.target_new_customer = ( SELECT sum( target_new_customer ) FROM report_target_franchise_dept WHERE target_id = #{targetId} AND STATUS = 0 ),
           t.target_old_customer = ( SELECT sum( target_old_customer ) FROM report_target_franchise_dept WHERE target_id = #{targetId} AND STATUS = 0 ),
           t.target_transfer_amount = ( SELECT sum( target_transfer_amount ) FROM report_target_franchise_dept WHERE target_id = #{targetId} AND STATUS = 0 )
       WHERE
           t.target_id = #{targetId}
         AND STATUS = 0
   </update>

</mapper>