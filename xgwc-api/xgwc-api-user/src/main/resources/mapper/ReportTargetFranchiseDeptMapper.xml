<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.ReportTargetFranchiseDeptMapper">
    

    <sql id="selectReportTargetFranchiseDeptVo">
        select target_dept_id, brand_id, franchise_id, target_date, dept_id, target_id, target_amount, amount_order, amount_real, target_new_customer, target_old_customer, target_transfer_amount, target_comission_rate, target_conversion_rate, new_customer_amount, old_customer_amount, transfer_amount, conversion_rate, comission_rate, status, create_by, create_by_id, update_by, update_by_id, create_time, update_time from report_target_franchise_dept
    </sql>

    <insert id="batchInsertReportTargetFranchiseDept" parameterType="java.util.List">
        INSERT INTO report_target_franchise_dept (
        brand_id,
        franchise_id,
        target_date,
        dept_id,
        target_id,
        target_amount,
        amount_order,
        amount_real,
        target_new_customer,
        target_old_customer,
        target_transfer_amount,
        target_comission_rate,
        target_conversion_rate,
        new_customer_amount,
        old_customer_amount,
        transfer_amount,
        conversion_rate,
        comission_rate,
        status,
        create_by,
        create_by_id,
        update_by,
        update_by_id,
        create_time,
        update_time,
        check_status
        ) VALUES
        <foreach collection="reportTargetFranchiseDepts" item="item" separator=",">
            (
            #{item.brandId},
            #{item.franchiseId},
            #{item.targetDate},
            #{item.deptId},
            #{item.targetId},
            #{item.targetAmount},
            #{item.amountOrder},
            #{item.amountReal},
            #{item.targetNewCustomer},
            #{item.targetOldCustomer},
            #{item.targetTransferAmount},
            #{item.targetComissionRate},
            #{item.targetConversionRate},
            #{item.newCustomerAmount},
            #{item.oldCustomerAmount},
            #{item.transferAmount},
            #{item.conversionRate},
            #{item.comissionRate},
            0,
            #{item.createBy},
            #{item.createById},
            #{item.updateBy},
            #{item.updateById},
            now(),
            #{item.updateTime},
            0
            )
        </foreach>
    </insert>


    <update id="updateReportTargetFranchiseDept" parameterType="com.xgwc.user.entity.ReportTargetFranchiseDept">
        update report_target_franchise_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="targetDate != null">target_date = #{targetDate},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="targetAmount != null">target_amount = #{targetAmount},</if>
            <if test="amountOrder != null">amount_order = #{amountOrder},</if>
            <if test="amountReal != null">amount_real = #{amountReal},</if>
            <if test="targetNewCustomer != null">target_new_customer = #{targetNewCustomer},</if>
            <if test="targetOldCustomer != null">target_old_customer = #{targetOldCustomer},</if>
            <if test="targetTransferAmount != null">target_transfer_amount = #{targetTransferAmount},</if>
            <if test="targetComissionRate != null">target_comission_rate = #{targetComissionRate},</if>
            <if test="targetConversionRate != null">target_conversion_rate = #{targetConversionRate},</if>
            <if test="newCustomerAmount != null">new_customer_amount = #{newCustomerAmount},</if>
            <if test="oldCustomerAmount != null">old_customer_amount = #{oldCustomerAmount},</if>
            <if test="transferAmount != null">transfer_amount = #{transferAmount},</if>
            <if test="conversionRate != null">conversion_rate = #{conversionRate},</if>
            <if test="comissionRate != null">comission_rate = #{comissionRate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateById != null">update_by_id = #{updateById},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
        </trim>
        where target_dept_id = #{targetDeptId} and brand_id = #{brandId}
    </update>

    <select id="getFranchiseInfoByFranchiseIds" resultType="com.xgwc.user.entity.dto.ReportTargetFranchiseDeptDto">
        SELECT
            t.dept_id,
            t.dept_name,
            t.franchise_id,
            ifnull( fo.company_simple_name, fo.company_name ) franchiseName
        FROM
            franchise_dept t
                LEFT JOIN franchise_owner fo ON ( fo.franchise_id = t.franchise_id AND fo.brand_id = #{brandId} )
        WHERE
            t.franchise_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
           and t.status = 0 and t.is_del = 0
    </select>

    <update id="deleteByTargetId">
        update report_target_franchise_dept set status = 1 where target_id = #{targetId}
    </update>

    <select id="getPreMonthReportTargetFranchiseDepts" resultType="com.xgwc.user.entity.ReportTargetFranchiseDept">
        select * from report_target_franchise_dept t where t.target_date = #{preMonth}
        and t.franchise_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBrandTargetDeptList" resultType="com.xgwc.user.entity.dto.ReportTargetBrandDeptDto">
        SELECT
        t.*,
        ifnull( fo.company_simple_name, fo.company_name ) franchiseName,
        fd.dept_name
        FROM
        report_target_franchise_dept t
        LEFT JOIN franchise_owner fo ON fo.franchise_id = t.franchise_id and fo.brand_id = t.brand_id
        left join report_target_franchise tf on tf.target_id = t.target_id
        left join franchise_dept fd on fd.dept_id = t.dept_id
        WHERE
        t.brand_id = #{brandId}
        <if test="franchiseId != null">
            and t.franchise_id = #{franchiseId}
        </if>
        <if test="targetName != null">
            and tf.target_name like concat ('%', #{targetName}, '%')
        </if>
        <if test="targetDate != null">
            and tf.target_date = #{targetDate}
        </if>
        <if test="checkStatus != null">
            and t.check_status = #{checkStatus}
        </if>
        AND t.`status` = 0
    </select>

    <select id="selectFranchiseTargetDeptList" resultType="com.xgwc.user.entity.dto.ReportTargetBrandDeptDto">
        SELECT
        t.*,
        ifnull( bo.company_simple_name, bo.company_name ) brandName
        FROM
        report_target_franchise_dept t
        LEFT JOIN xgwc_brand_owner bo ON bo.brand_id = t.brand_id
        left join report_target_franchise tf on tf.target_id = t.target_id
        WHERE
        t.franchise_id = #{franchiseId}
        <if test="brandId != null">
            and t.brand_id = #{brandId}
        </if>
        <if test="targetName != null">
            and tf.target_name like concat ('%', #{targetName}, '%')
        </if>
        <if test="targetDate != null">
            and tf.target_date = #{targetDate}
        </if>
        <if test="checkStatus != null">
            and t.check_status = #{checkStatus}
        </if>
        AND t.`status` = 0
    </select>

    <select id="getReportTargetFranchiseDeptsByTaskId" resultType="com.xgwc.user.entity.dto.ReportTargetFranchiseDeptDto">
        SELECT
            t.*,
            d.dept_name,
            ifnull( fo.company_simple_name, fo.company_name ) franchiseName
        FROM
            report_target_franchise_dept t
                left join franchise_dept d on d.dept_id = t.dept_id
                LEFT JOIN franchise_owner fo ON ( fo.franchise_id = t.franchise_id AND fo.brand_id = t.brand_id )
        WHERE
            t.target_id = #{targetId}
    </select>

    <select id="countNotSubmitDept" resultType="java.lang.Integer">
        select count(1) from report_target_franchise_dept t where t.check_status = 0 and t.status = 0 and t.dept_id = #{deptId}
    </select>

    <select id="selectFranchiseTargetDeptListByFranchiseId"
            resultType="com.xgwc.user.entity.dto.ReportTargetBrandDeptDto">
        SELECT
        t.*,
        ifnull( bo.company_simple_name, bo.company_name ) brandName
        FROM
        report_target_franchise_dept t
        LEFT JOIN xgwc_brand_owner bo ON bo.brand_id = t.brand_id
        left join report_target_franchise tf on tf.target_id = t.target_id
        WHERE
        t.franchise_id = #{franchiseId}
        <if test="brandId != null">
            and t.brand_id = #{brandId}
        </if>
        <if test="deptId != null">
            and t.dept_id = #{deptId}
        </if>
        <if test="targetName != null">
            and tf.target_name like concat ('%', #{targetn}, '%')
        </if>
        <if test="targetDate != null">
            and tf.target_date = #{targetDate}
        </if>
        <if test="checkStatus != null">
            and t.check_status = #{checkStatus}
        </if>
        AND t.`status` = 0
    </select>

    <select id="countAndCheckStatusDto" resultType="com.xgwc.user.entity.dto.CountAndCheckStatusDto">
        select count(1) count, sum(t.check_status) checkStatusCount from report_target_franchise_dept t where t.target_id = #{targetId} and t.`status` = 0
    </select>

    <update id="freshAmount">
        UPDATE report_target_franchise_dept t
        SET t.target_amount = ( SELECT sum( target_amount ) FROM report_target_franchise_staff WHERE target_dept_id = #{deptTargeId} AND STATUS = 0 ),
            t.target_new_customer = ( SELECT sum( target_new_customer ) FROM report_target_franchise_staff WHERE target_dept_id = #{deptTargeId} AND STATUS = 0 ),
            t.target_old_customer = ( SELECT sum( target_old_customer ) FROM report_target_franchise_staff WHERE target_dept_id = #{deptTargeId} AND STATUS = 0 ),
            t.target_transfer_amount = ( SELECT sum( target_transfer_amount ) FROM report_target_franchise_staff WHERE target_dept_id = #{deptTargeId} AND STATUS = 0 )
        WHERE
            t.target_dept_id = #{deptTargeId}
          AND STATUS = 0
    </update>
</mapper>