<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.XgwcBrandDeptMapper">
    <insert id="saveXgwcBrandDept">
        INSERT INTO `xgwc_sass`.`xgwc_brand_dept` (`dept_id`, `dept_name`, `company_id`, `brand_owner_id`, `pid`, `level`, `sort`,
                                                   `create_by`,`create_time`)
        VALUES (#{xgwcBrandDeptDto.deptId}, #{xgwcBrandDeptDto.deptName}, #{xgwcBrandDeptDto.companyId},
                #{xgwcBrandDeptDto.brandOwnerId},
                #{xgwcBrandDeptDto.pid},
                #{xgwcBrandDeptDto.level},
                #{xgwcBrandDeptDto.sort},
                #{xgwcBrandDeptDto.createBy}, NOW())
    </insert>


    <update id="updateStatusById">
        UPDATE `xgwc_sass`.`xgwc_brand_dept`
        SET `status` = #{status}
        WHERE `dept_id` = #{deptId}
    </update>

    <update id="updateXgwcBrandDeptById">
        UPDATE `xgwc_sass`.`xgwc_brand_dept`
        <set>
            <if test="xgwcBrandDeptDto.deptName != null and xgwcBrandDeptDto.deptName != ''">
                `dept_name` = #{xgwcBrandDeptDto.deptName},
            </if>
            <if test="xgwcBrandDeptDto.companyId != null">
                `company_id` = #{xgwcBrandDeptDto.companyId},
            </if>
            <if test="xgwcBrandDeptDto.pid != null">
                `pid` = #{xgwcBrandDeptDto.pid},
            </if>
            <if test="xgwcBrandDeptDto.level != null">
                `level` = #{xgwcBrandDeptDto.level},
            </if>
            <if test="xgwcBrandDeptDto.sort != null">
                `sort` = #{xgwcBrandDeptDto.sort},
            </if>
            <!-- 自动填充更新时间 -->
            `update_time` = NOW(),
            <if test="xgwcBrandDeptDto.updateBy != null and xgwcBrandDeptDto.updateBy != ''">
                `update_by` = #{xgwcBrandDeptDto.updateBy},
            </if>
        </set>
        WHERE `dept_id` = #{xgwcBrandDeptDto.deptId}
    </update>

    <update id="updateDeptStaffSchedule">
        UPDATE xgwc_staff
        <set>
            `is_assistant` = 0,
            <if test="staff.isSchedule != null">
                `is_schedule` = #{staff.isSchedule},
            </if>
            <if test="staff.updateBy != null and staff.updateBy != ''">
                `update_by` = #{staff.updateBy},
            </if>
            `update_time` = NOW(),
        </set>
        WHERE `id` = #{staff.id}
    </update>


    <update id="updateDeptStaffManage">
        UPDATE xgwc_staff
        <trim prefix="SET" suffixOverrides=",">
            `is_principal` = 0,
            <if test="principal.isSchedule != null">
                `is_schedule` = #{principal.isSchedule},
            </if>
            <if test="principal.updateBy != null and principal.updateBy != ''">
                `update_by` = #{sprincipal.updateBy},
            </if>
            `update_time` = NOW(),
        </trim>
        WHERE `id` = #{principal.id}
    </update>
    <update id="updateDeptStaffAssistant">
        UPDATE xgwc_staff
        set is_assistant = 1, is_principal = 1, is_schedule = 1
        where `dept_id` = #{deptId}
    </update>
    <select id="getXgwcBrandDeptList" resultType="com.xgwc.user.entity.vo.XgwcBrandDeptVo">
        SELECT
        xbd.`dept_id` AS deptId,
        xbd.`dept_name` AS deptName,
        sc.id AS companyId,
        sc.name AS companyName,
        xbd.`pid`,
        xbd.`level`,
        xbd.`sort`,
        xbd.`status`,
        xbd.`create_time` AS createTime
        FROM
        `xgwc_sass`.`xgwc_brand_dept` xbd
        left join sys_company sc on xbd.company_id = sc.id
        <where>
            xbd.`brand_owner_id` = #{xgwcBrandDeptParam.brandOwnerId}

            <if test="xgwcBrandDeptParam.deptName != null and xgwcBrandDeptParam.deptName != ''">
                AND xbd.`dept_name` LIKE CONCAT('%',#{xgwcBrandDeptParam.deptName},'%')
            </if>
            <if test="xgwcBrandDeptParam.status != null">
                AND xbd.`status` = #{xgwcBrandDeptParam.status}
            </if>
            <if test="xgwcBrandDeptParam.companyId != null">
                AND xbd.`company_id` = #{xgwcBrandDeptParam.companyId}
            </if>
        </where>
        order by xbd.sort
    </select>
    <select id="getXgwcBrandDeptById" resultType="com.xgwc.user.entity.dto.XgwcBrandDeptDto">
        SELECT
        xbd.`dept_id` AS deptId,
        xbd.`dept_name` AS deptName,
        sc.id AS companyId,
        sc.name AS companyName,
        xbd.`pid`,
        xbd.`level`,
        xbd.`sort`
        FROM
        `xgwc_sass`.`xgwc_brand_dept` xbd
        left join sys_company sc on xbd.company_id = sc.id
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
            </if>
        </where>
    </select>

    <select id="selectXgwcDeptStaffManage" resultType="com.xgwc.user.entity.vo.StaffVo">
        SELECT
        `id`,
        `name`,
        `dept_id` AS deptId,
        `is_schedule` AS isSchedule
        FROM
        `xgwc_sass`.`xgwc_staff`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
                AND `is_principal` = 0
            </if>
        </where>
    </select>

    <select id="selectXgwcDeptStaffSchedule" resultType="com.xgwc.user.entity.vo.StaffVo">
        SELECT
        `id`,
        `name`,
        `dept_id` AS deptId,
        `is_assistant` AS isAssistant
        FROM
        `xgwc_sass`.`xgwc_staff`
        <where>
            <if test="deptId != null">
                AND `dept_id` = #{deptId}
                AND `is_assistant` = 0
            </if>
        </where>
    </select>

    <select id="getBrandDeptByBrandId" resultType="com.xgwc.user.entity.dto.XgwcBrandDeptDto">
        SELECT
            xbd.`dept_id` AS deptId,
            xbd.`dept_name` AS deptName,
            xbd.`pid`,
            xbd.`level`,
            xbd.`sort`
        FROM
            `xgwc_sass`.`xgwc_brand_dept` xbd
        where xbd.brand_owner_id = #{brandId} and xbd.`status` = 0 and xbd.is_del = 0
        order by sort
    </select>

    <select id="selectDeptStaffNum" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            `xgwc_sass`.`xgwc_staff`
        WHERE
            <if test="deptId != null">
                `dept_id` = #{deptId}
            </if>
    </select>
    <select id="getXgwcBrandDeptByUserId" resultType="com.xgwc.user.entity.dto.XgwcBrandDeptDto">
        select
            d.dept_id,
            d.dept_name,
            d.brand_owner_id,
            d.company_id,
            d.company_name,
            d.pid
        from xgwc_staff s
        left join xgwc_brand_dept d on s.dept_id = d.dept_id
        where s.bind_user_id = #{userId}
        limit 1
    </select>
</mapper>