<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseNoticeMapper">
    
    <insert id="insertNoticeClassify" useGeneratedKeys="true" keyColumn="id">
        INSERT INTO franchise_notice_classify ( classify_name, franchise_id, sort, STATUS, create_by, create_time, update_by, update_time )
        VALUES (#{classifyName,jdbcType=VARCHAR}, #{franchiseId,jdbcType=BIGINT}, #{sort,jdbcType=INTEGER}, 0, #{createBy,jdbcType=VARCHAR},
               now(), #{updateBy,jdbcType=VARCHAR}, now())
    </insert>

    <update id="updateNoticeClassify">
        update franchise_notice_classify
            <set>
                update_by = #{updateBy,jdbcType=VARCHAR}, update_time = now()
                <if test="classifyName != null">
                    ,classify_name = #{classifyName,jdbcType=VARCHAR}
                </if>
                <if test="sort != null">
                    ,sort = #{sort}
                </if>
            </set>
            <where>
                franchise_id = #{franchiseId,jdbcType=BIGINT} and id = #{id,jdbcType=INTEGER}
            </where>
    </update>

    <update id="updateStatus">
        update franchise_notice_classify
        <set>
            update_by = #{updateBy,jdbcType=VARCHAR}, update_time = now(), STATUS = #{status,jdbcType=INTEGER}
        </set>
        <where>
            franchise_id = #{franchiseId,jdbcType=BIGINT} and id = #{id,jdbcType=INTEGER}
        </where>
    </update>

    <select id="getNoticeClassifyList" resultType="com.xgwc.user.entity.dto.notice.NoticeClassfyDto">
        select id, classify_name classifyName, sort, STATUS from franchise_notice_classify where franchise_id = #{franchiseId,jdbcType=BIGINT}
        <if test="status != null">
            and status = #{status}
        </if>
        order by sort
    </select>

    <select id="getNoticeClassifyListByIds" resultType="com.xgwc.user.entity.dto.notice.NoticeClassfyDto">
        select id, classify_name classifyName, sort, STATUS from franchise_notice_classify where id in
        ( <foreach collection="list" item="list" separator=",">
            #{list}
        </foreach>)
    </select>

    <insert id="insertFranchiseNotice" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO franchise_notice (
            notice_type,
            classify_id,
            title,
            content,
            attachment_count,
            attachment,
            send_count,
            send_group,
            schedule_time,
            STATUS,
            send_status,
            publish_time,
            create_by,
            create_time,
            update_by,
            update_time,
            franchise_id)
        VALUES(#{noticeType,jdbcType=INTEGER}, #{classifyId,jdbcType=INTEGER}, #{title,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{attachmentCount}, #{attachment},#{sendCount,jdbcType=INTEGER},
              #{sendGroup,jdbcType=VARCHAR}, #{scheduleTime,jdbcType=VARCHAR}, 0, 0, #{publishTime,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, now(), #{franchiseId,jdbcType=BIGINT})
    </insert>

    <update id="updateFranchiseNotice">
        update franchise_notice
            <set>
                update_by = #{updateBy}, update_time = now()
                <if test="noticeType != null">
                    ,notice_type = #{noticeType}
                </if>
                <if test="classifyId != null">
                    ,classify_id = #{classifyId}
                </if>
                <if test="title != null">
                    ,title = #{title}
                </if>
                <if test="content != null">
                    ,content = #{content}
                </if>
                <if test="attachment != null">
                    ,attachment = #{attachment}
                </if>
                <if test="attachmentCount != null">
                    ,attachment_count = #{attachmentCount}
                </if>
                <if test="sendGroup != null">
                    ,send_group = #{sendGroup}
                </if>
                <if test="sendCount != null">
                    ,send_count = #{sendCount}
                </if>
                <if test="scheduleTime != null">
                    ,schedule_time = #{scheduleTime}
                </if>
            </set>
        <where>
            id = #{id} and franchise_id = #{franchiseId}
        </where>
    </update>

    <update id="updateSendedFranchiseNotice">
        update franchise_notice
        <set>
            update_by = #{updateBy}, update_time = now()
            <if test="noticeType != null">
                ,notice_type = #{noticeType}
            </if>
            <if test="classifyId != null">
                ,classify_id = #{classifyId}
            </if>
            <if test="title != null">
                ,title = #{title}
            </if>
            <if test="content != null">
                ,content = #{content}
            </if>
            <if test="attachment != null">
                ,attachment = #{attachment}
            </if>
        </set>
        <where>
            id = #{id} and franchise_id = #{franchiseId}
        </where>
    </update>

    <update id="deleteFranchiseNotice">
        update franchise_notice set status = 1, update_by = #{updateBy}, update_time = now() where id = #{id} and franchise_id = #{franchiseId}
    </update>

    <select id="getFranchiseNoticeById" resultType="com.xgwc.user.entity.dto.FranchiseNoticeDto">
        select notice_type noticeType,
               classify_id classifyId,
               title,
               content,
               attachment_count attachmentCount,
               attachment,
               send_count sendCount,
               send_group sendGroup,
               schedule_time scheduleTime,
               STATUS,
               send_status sendStatus,
               publish_time publishTime,
               create_time createTime
               from franchise_notice where id = #{id} and franchise_id = #{franchiseId} and status = 0
    </select>

    <select id="getFranchiseNoticeList" resultType="com.xgwc.user.entity.dto.FranchiseNoticeDto">
        select
               id,
               notice_type noticeType,
               classify_id classifyId,
               title,
               content,
               attachment_count attachmentCount,
               attachment,
               send_count sendCount,
               send_group sendGroup,
               schedule_time scheduleTime,
               STATUS,
               send_status sendStatus,
               publish_time publishTime,
               create_time createTime,
               franchise_id franchiseId
        from franchise_notice where franchise_id = #{franchiseId}
        <if test="title != null">
            and title like concat("%", #{title,jdbcType=VARCHAR}, "%")
        </if>
        <if test="noticeType != null">
            and notice_type = #{noticeType}
        </if>
        <if test="classifyId != null">
            and classify_id = #{classifyId}
        </if>
        <if test="publishTimeStart != null">
            and publish_time &gt;= #{publishTimeStart}
        </if>
        <if test="publishTimeEnd != null">
            and publish_time &lt;= #{publishTimeEnd}
        </if>
        <if test="publishStatus != null and publishStatus == 0">
            and send_status in (1,2,3)
        </if>
        <if test="publishStatus != null and publishStatus == 1">
            and send_status = 0
        </if>
        order by create_time desc
    </select>

    <insert id="batchInsertNoticeDetails" useGeneratedKeys="true" keyColumn="id">
        INSERT INTO franchise_notice_detail (user_id, notice_id, is_read, status, create_time, update_time)
        VALUES
        <foreach collection="userIds" item="userId" separator=",">
            (#{userId}, #{noticeId}, 0, 0, NOW(), NOW())
        </foreach>
    </insert>

    <update id="updateReadStatus">
        update franchise_notice_detail set is_read = 1 where user_id = #{userId} and notice_id = #{noticeId}
    </update>

    <update id="updateReadCount">
        update franchise_notice  set read_count = (select count(1) from franchise_notice_detail t where t.notice_id = #{noticeId} and t.is_read = 1) where id = #{noticeId}
    </update>

</mapper>