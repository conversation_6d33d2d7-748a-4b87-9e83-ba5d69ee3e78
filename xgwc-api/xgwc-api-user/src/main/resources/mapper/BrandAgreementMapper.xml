<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.BrandAgreementMapper">
    
    <insert id="insertAgreement" useGeneratedKeys="true" keyColumn="id">
        insert into brand_agreement(title, content, brand_id, status, create_time, update_time, create_by, update_by)
        VALUES(#{title}, #{content}, #{brandId}, 0, now(), now(), #{createBy}, #{updateBy})
    </insert>

    <select id="getAgreementByBrandId"  resultType="com.xgwc.user.entity.dto.AgreementDto">
        select id, title, content, brand_id brandId from brand_agreement where status = 0 and brand_id = #{brandId} limit 1
    </select>

    <select id="getAgreementByBrandIds" resultType="com.xgwc.user.entity.dto.AgreementDto">
        select id, title, content, brand_id brandId from brand_agreement where status = 0 and brand_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by create_time desc
    </select>

    <update id="updateAgreement">
        update brand_agreement set title = #{title}, content = #{content}, update_by = #{updateBy} where brand_id = #{brandId} and status = 0
    </update>

    <select id="getAgreementById" resultType="com.xgwc.user.entity.dto.AgreementDto">
        select id, title, content, brand_id brandId from brand_agreement where status = 0 and id = #{agreementId}
    </select>

    <select id="eixstAgreementById" resultType="java.lang.Integer">
        select 1 brandId from brand_agreement where status = 0 and id = #{agreementId}
    </select>
</mapper>