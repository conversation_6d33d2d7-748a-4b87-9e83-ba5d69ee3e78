<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysRoleMapper">
    

    <sql id="selectSysRoleVo">
        select id, name, code, sort, status, type, remark, is_del, tenant_id, create_by, create_time, update_by, update_time, modify_time from sys_role
    </sql>

    <select id="selectSysRoleList" parameterType="com.xgwc.user.entity.vo.SysRoleQueryVo" resultType="com.xgwc.user.entity.dto.SysRoleDto">
        <include refid="selectSysRoleVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code like concat('%', #{code}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time between #{createTime}[0] and #{createTime}[1] </if>
            <if test="updateBy != null "> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time between #{updateTime}[0] and #{updateTime}[1] </if>
            <if test="modifyTime != null "> and modify_time between #{modifyTime}[0] and #{modifyTime}[1] </if>
        </where>
    </select>
    
    <select id="selectSysRoleById" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysRoleDto">
        <include refid="selectSysRoleVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysRole" parameterType="com.xgwc.user.entity.SysRole">
        insert into sys_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
         </trim>
        <selectKey keyProperty="id" resultType="long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <update id="updateSysRole" parameterType="com.xgwc.user.entity.SysRole">
        update sys_role
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysRoleById" parameterType="Long">
        delete from sys_role where id = #{id}
    </delete>

    <delete id="deleteSysRoleByIds" parameterType="String">
        delete from sys_role where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

<!--    <insert id="insertSysRoleMenu" >-->
<!--        insert into sys_role_menu (role_id, menu_id, create_time)-->
<!--        values-->
<!--        <foreach item="item" collection="array" separator="," >-->
<!--            (#{item.roleId, item.menuId}, now())-->
<!--        </foreach>-->
<!--    </insert>-->

    <insert id="insertSysRoleUser" parameterType="java.lang.Long">
        insert into sys_user_role (role_id, user_id, create_time)
        values (#{roleId}, #{userId}, now())
    </insert>
    <insert id="insertSysRoleMenu" parameterType="com.xgwc.user.entity.SysRoleMenu">
        insert into sys_role_menu (role_id, menu_id, create_time)
        values
        <foreach item="item" collection="array" separator="," >
            (#{item.roleId} , #{item.menuId}, now())
        </foreach>
    </insert>
</mapper>