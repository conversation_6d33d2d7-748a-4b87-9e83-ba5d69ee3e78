<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SmsSendMapper">

    <insert id="insertSmsSendRecord" useGeneratedKeys="true" keyColumn="id">
        INSERT INTO sys_sms_send (phone, type, STATUS, message, create_time, update_time )
        VALUES
            (#{photoNum}, #{smsType}, #{status}, #{message}, now(), now())
    </insert>

    <select id="findSmsSendByPhoneNum" resultType="com.xgwc.user.entity.dto.SysSmsSend">
        SELECT * from sys_sms_send where phone = #{phoneNum} and message = #{message} and type = 1
    </select>
</mapper>