<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.StaffMapper">
    

    <sql id="selectStaffVo">
        select id, name, stage_name, dept_id, post_id, role_ids, job_nature, status, is_principal, superior, archive_status, bind_status, bind_user_id, download_count
               ,is_del,create_by, create_time, update_by, update_time, modify_time,brand_id,company_id,resignation_time from xgwc_staff
    </sql>

    <select id="selectStaffList" parameterType="com.xgwc.user.entity.vo.StaffQueryVo" resultType="com.xgwc.user.entity.dto.StaffDto">
        select t.id, t.name, t.stage_name, t.dept_id, t.post_id, t.role_ids, t.job_nature, t.status, t.is_principal, t.superior, t.archive_status
               ,t.bind_status, t.bind_user_id, t.is_del, t.create_by, t.create_time,t.login_phone,t.resignation_time
                ,d.dept_name, p.station_name,c.name as company_name
        from xgwc_staff t
        left join xgwc_brand_dept d on t.dept_id = d.dept_id
        left join xgwc_brand_station p on t.post_id = p.station_id
        left join sys_company c on t.company_id = c.id
        <where>
            <if test="id != null "> and t.id = #{id}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="postId != null "> and t.post_id = #{postId}</if>
            <if test="jobNature != null "> and t.job_nature = #{jobNature}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="isPrincipal != null "> and t.is_principal = #{isPrincipal}</if>
            <if test="superior != null "> and t.superior = #{superior}</if>
            <if test="archiveStatus != null "> and t.archive_status = #{archiveStatus}</if>
            <if test="bindStatus != null "> and t.bind_status = #{bindStatus}</if>
            <if test="bindUserId != null "> and t.bind_user_id = #{bindUserId}</if>
            <if test="isDel == null"> and t.is_del = 0</if>
            <if test="brandId != null "> and t.brand_id = #{brandId}</if>
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="name != null ">and t.name like concat('%', #{name}, '%')</if>
            <if test="stageName != null ">and t.stage_name like concat('%', #{stageName}, '%')</if>
            <if test="loginPhone != null ">and t.login_phone like concat('%', #{loginPhone}, '%')</if>
            <if test="businessId != null ">
                and t.dept_id in (
                        select sb.dept_id from xgwc_shop s
                        left join xgwc_shop_business sb on s.shop_id = sb.shop_id
                        where s.brand_owner_id = #{brandId}
                        and sb.business_id = #{businessId}
                        GROUP by sb.dept_id
                )
            </if>
<!--            <if test="other != null  and other != ''">-->
<!--                and (-->
<!--                t.name like concat('%', #{other}, '%')-->
<!--                or t.stage_name like concat('%', #{other}, '%')-->
<!--                or t.login_phone like concat('%', #{other}, '%')-->
<!--                )-->
<!--            </if>-->
            <if test="startTime != null and endTime != null">
                and t.create_time BETWEEN CONCAT(#{startTime}, ' 00:00:00') AND CONCAT(#{endTime}, ' 23:59:59')
            </if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectStaffById" parameterType="Long" resultType="com.xgwc.user.entity.dto.StaffDto">
        <include refid="selectStaffVo"/>
        where id = #{id}
    </select>

    <select id="getStaffByUserId" resultType="com.xgwc.user.entity.dto.StaffDto">
        <include refid="selectStaffVo"/>
        where bind_user_id = #{userId} and `status` = 0 and is_del = 0
    </select>

    <select id="selectStaffByNameAndBrandId" resultType="com.xgwc.user.entity.dto.StaffDto">
        <include refid="selectStaffVo"/>
        where name = #{name}
        and brand_id = #{brandId}
        and is_del = 0
    </select>
    <select id="selectStaffByUserIdAndBrandId" resultType="com.xgwc.user.entity.dto.StaffDto">
        <include refid="selectStaffVo"/>
        where bind_user_id = #{userId}
        and  brand_id = #{brandId}
        and status = 0
        and bind_status = 0
    </select>
    <select id="selectStaffListDropDown" resultType="com.xgwc.user.entity.dto.StaffDto">
        select t.id, t.name, t.stage_name, sc.name as companyName
        from xgwc_staff t
                 left join xgwc_brand_dept d on t.dept_id = d.dept_id
                 left join sys_company sc on d.company_id = sc.id
        <where>
                t.brand_id = #{brandId}
            and t.archive_status = 0
            and t.`status` = 0
            and t.is_del = 0
        </where>
    </select>

    <insert id="insertStaff" parameterType="com.xgwc.user.entity.Staff">
        insert into xgwc_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="stageName != null">stage_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="postId != null">post_id,</if>
            <if test="roleIds != null">role_ids,</if>
            <if test="jobNature != null">job_nature,</if>
            <if test="status != null">status,</if>
            <if test="isPrincipal != null">is_principal,</if>
            <if test="superior != null">superior,</if>
            <if test="archiveStatus != null">archive_status,</if>
            <if test="bindStatus != null">bind_status,</if>
            <if test="bindUserId != null">bind_user_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="loginPhone != null">login_phone,</if>
            <if test="companyId != null">company_id,</if>
            <if test="resignationTime != null">resignation_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="stageName != null">#{stageName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="postId != null">#{postId},</if>
            <if test="roleIds != null">#{roleIds},</if>
            <if test="jobNature != null">#{jobNature},</if>
            <if test="status != null">#{status},</if>
            <if test="isPrincipal != null">#{isPrincipal},</if>
            <if test="superior != null">#{superior},</if>
            <if test="archiveStatus != null">#{archiveStatus},</if>
            <if test="bindStatus != null">#{bindStatus},</if>
            <if test="bindUserId != null">#{bindUserId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="loginPhone != null">#{loginPhone},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="resignationTime != null">#{resignationTime},</if>
         </trim>
    </insert>

    <update id="updateStaff" parameterType="com.xgwc.user.entity.Staff">
        update xgwc_staff
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="stageName != null">stage_name = #{stageName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="postId != null">post_id = #{postId},</if>
            <if test="roleIds != null">role_ids = #{roleIds},</if>
            <if test="jobNature != null">job_nature = #{jobNature},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isPrincipal != null">is_principal = #{isPrincipal},</if>
            <if test="superior != null">superior = #{superior},</if>
            <if test="archiveStatus != null">archive_status = #{archiveStatus},</if>
            <if test="bindStatus != null">bind_status = #{bindStatus},</if>
            <if test="bindUserId != null">bind_user_id = #{bindUserId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="loginPhone != null">login_phone = #{loginPhone},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="resignationTime != null">resignation_time = #{resignationTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteStaffById" parameterType="Long">
        update xgwc_staff set is_del = 1 where id = #{id}
    </update>

    <update id="updateBindStatus">
        update xgwc_staff set bind_status = #{bindStatus}, bind_user_id = #{bindUserId}, login_phone = #{phone},stage_name = #{stageName} where id = #{id}
    </update>

    <update id="updateStaffStatus">
        update xgwc_staff set status = #{status} where id = #{id}
    </update>

    <delete id="deleteStaffByIds" parameterType="String">
        update xgwc_staff set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getStaffUserIdListByDeptIds" resultType="java.lang.Long">
        SELECT
            xs.bind_user_id
        FROM
            xgwc_staff xs
                LEFT JOIN xgwc_brand_dept d ON xs.dept_id = d.dept_id
        where xs.`status` = 0 and xs.is_del = 0 and d.`status` = 0 and d.is_del = 0 and xs.brand_id = #{brandId} and xs.dept_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getStaffUserIdListByBrand" resultType="java.lang.Long">
        SELECT
            xs.bind_user_id
        FROM
            xgwc_staff xs
                LEFT JOIN xgwc_brand_dept d ON xs.dept_id = d.dept_id
        where xs.`status` = 0 and xs.is_del = 0 and d.`status` = 0 and d.is_del = 0 and xs.brand_id = #{brandId}
    </select>

    <select id="countStaffUserIdListByBrand" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            xgwc_staff xs
                LEFT JOIN xgwc_brand_dept d ON xs.dept_id = d.dept_id
        where xs.`status` = 0 and xs.is_del = 0 and d.`status` = 0 and d.is_del = 0 and xs.brand_id = #{brandId}
    </select>

    <select id="getStaffUserInfoListByBrandId" resultType="com.xgwc.user.entity.dto.SimpleDeptUserInfoDto">
        SELECT
            xs.id,
            xs.bind_user_id userId,
            xs.dept_id deptId,
            xs.`name` userName
        FROM
            xgwc_staff xs
                LEFT JOIN xgwc_brand_dept d ON xs.dept_id = d.dept_id
        <where>
            xs.`status` = 0 and xs.is_del = 0 and d.`status` = 0 and d.is_del = 0 and xs.brand_id = #{brandId}
            <if test="deptId != null">
                and xs.dept_id = #{deptId}
            </if>
        </where>
    </select>

    <select id="findStaffByBindUserId" resultType="com.xgwc.user.entity.dto.StaffDto">
        <include refid="selectStaffVo"/>
        where bind_user_id = #{userId} and `status` = 0 and is_del = 0
    </select>

    <select id="selectStaffByBrandId" resultType="com.xgwc.user.entity.dto.StaffDto">
        <include refid="selectStaffVo"/>
        where brand_id = #{brandId} and `status` = 0 and is_del = 0 and bind_status = 1
    </select>
    <select id="findFranchiseStaffByBindUserId" resultType="com.xgwc.user.entity.dto.StaffDto">
        select id,
               role_ids as roleIds,
               download_count as downloadCount
        from xgwc_franchise_staff
        where bind_user_id = #{userId} and `status` = 0 and is_del = 0
    </select>

    <select id="findStaffByUserIdAndBrandId" resultType="com.xgwc.user.entity.dto.StaffDto">
        select t.id, t.name, t.stage_name, t.dept_id, t.post_id,
               t.role_ids, t.job_nature, t.status, t.is_principal,
               t.superior, t.archive_status, t.bind_status,
               t.bind_user_id,bd.dept_name
        from xgwc_staff t
        left join xgwc_brand_dept bd on bd.dept_id = t.dept_id
        where t.bind_user_id = #{userId}
        and t.brand_id = #{brandId}
        and t.status = 0
        and t.bind_status = 1
    </select>
</mapper>