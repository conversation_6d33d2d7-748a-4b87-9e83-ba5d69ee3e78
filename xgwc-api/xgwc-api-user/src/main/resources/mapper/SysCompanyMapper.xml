<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysCompanyMapper">
    

    <sql id="selectSysCompanyVo">
        select id, pid, name, order_num, status, create_by, create_time, update_by, update_time, modify_time from sys_company
    </sql>

    <select id="selectSysCompanyList" parameterType="com.xgwc.user.entity.vo.SysCompanyQueryVo" resultType="com.xgwc.user.entity.vo.SysCompanyVo">
        <include refid="selectSysCompanyVo"/>
        <where>
            and brand_owner_id = #{sysCompany.brandOwnerId}
            <if test="sysCompany.name != null  and sysCompany.name != ''"> and `name` like concat('%', #{sysCompany.name}, '%')</if>
            <if test="sysCompany.status != null "> and status = #{sysCompany.status}</if>
        </where>
        order by order_num
    </select>
    
    <select id="selectSysCompanyById" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysCompanyDto">
        <include refid="selectSysCompanyVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="pid != null"> and id = #{pid}</if>
        </where>
    </select>

    <select id="selectCompanyDropDown" resultType="com.xgwc.user.entity.dto.SysCompanyDto">
        <include refid="selectSysCompanyVo"/>
        order by order_num
    </select>

    <insert id="insertSysCompany" parameterType="com.xgwc.user.entity.SysCompany" useGeneratedKeys="true" keyProperty="id">
        insert into sys_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`id`,</if>
            <if test="pid != null">`pid`,</if>
            <if test="name != null">`name`,</if>
            <if test="orderNum != null">`order_num`,</if>
            <if test="createBy != null">`create_by`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="brandOwnerId != null">`brand_owner_id`,</if>
            <if test="companyType != null">`company_type`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pid != null">#{pid},</if>
            <if test="name != null">#{name},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">now(),</if>
            <if test="brandOwnerId != null">#{brandOwnerId},</if>
            <if test="companyType != null">#{companyType},</if>
         </trim>
    </insert>

    <update id="updateSysCompany" parameterType="com.xgwc.user.entity.SysCompany">
        update sys_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = now(),</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateStatus" >
        update sys_company
        set status = #{status}
        where id =  #{id}
    </update>
</mapper>