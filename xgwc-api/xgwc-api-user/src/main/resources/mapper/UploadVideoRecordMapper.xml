<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.UploadVideoRecordMapper">

   <insert id="insertUploadVideoRecord" useGeneratedKeys="true" keyProperty="id">
       insert into upload_video_record(oringin_name, transcode_id, transcode_result, create_by, transcode_status, video_id, status, create_time, update_time) values
           (#{oringinName}, #{transcodeId}, #{transcodeResult,jdbcType=VARCHAR}, #{createBy}, #{transcodeStatus}, #{videoId}, 0, now(), now())
   </insert>

    <select id="getUploadVideoRecordById" resultType="com.xgwc.user.entity.VideoInfo">
        select id, oringin_name oringinName, transcode_result transcodeResult from upload_video_record where id = #{id} and transcode_status = 2
    </select>

    <select id="getUploadVideoRecordList" resultType="com.xgwc.user.entity.VideoInfo">
        select id, oringin_name oringinName, transcode_result transcodeResult from upload_video_record where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
         and transcode_status = 2
    </select>
</mapper>