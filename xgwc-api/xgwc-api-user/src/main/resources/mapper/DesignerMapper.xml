<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.DesignerMapper">
    

    <sql id="selectDesignerVo">
        select designer_id, idcard_front, idcard_back, idcard_is_longterm, idcard_start, idcard_end, name, idcard_no, phone,
               email, emergency_name, emergency_phone, good_business, description, bank_user_name, bank_name, bank_no, zfb_name,
               zfb_account, manager_name, manager_phone, receive_order_status, check_status, check_time, status, is_del, create_by,
               create_time, update_by, update_time, modify_time, reason,brand_id,manager_user_id,wechat_name,wechat_account_url,alipay_account_url
        from xgwc_designer
    </sql>

    <select id="selectDesignerList" parameterType="com.xgwc.user.entity.dto.DesignerQueryDto" resultType="com.xgwc.user.entity.vo.DesignerVO">
        select t.designer_id, t.idcard_front, t.idcard_back, t.idcard_is_longterm, t.idcard_start, t.idcard_end, t.name, t.idcard_no, t.phone, t.email, t.emergency_name,
            t.emergency_phone, t.good_business, t.description, t.bank_user_name, t.bank_name, t.bank_no, t.zfb_name, t.zfb_account, t.manager_name, t.manager_phone,
            t.receive_order_status, t.check_status, t.check_time, t.status, t.is_del, t.create_by, t.create_time, t.update_by,t.reason,b.business_name as businessName,
            t.brand_id,xbo.company_name as brandName,t.wechat_name,t.wechat_account_url,t.alipay_account_url
        from xgwc_designer t
        left join xgwc_business b on b.business_id = t.good_business
        left join xgwc_brand_owner xbo on xbo.brand_id = t.brand_id
        where t.check_status != 3
        <if test="brandName != null and brandName != ''"> and xbo.company_name = #{brandName}</if>
        <if test="designerId != null "> and t.designer_id = #{designerId}</if>
        <if test="name != null  and name != ''"> and t.name like concat('%', #{name}, '%')</if>
        <if test="phone != null  and phone != ''"> and t.phone like concat('%', #{phone}, '%')</if>
        <if test="emergencyName != null  and emergencyName != ''"> and t.emergency_name like concat('%', #{emergencyName}, '%')</if>
        <if test="emergencyPhone != null  and emergencyPhone != ''"> and t.emergency_phone like concat('%', #{emergencyPhone}, '%')</if>
        <if test="goodBusiness != null "> and t.good_business = #{goodBusiness}</if>
        <if test="description != null  and description != ''"> and t.description like concat('%', #{description}, '%')</if>
        <if test="managerName != null  and managerName != ''"> and t.manager_name like concat('%', #{managerName}, '%')</if>
        <if test="managerPhone != null  and managerPhone != ''"> and t.manager_phone like concat('%', #{managerPhone}, '%')</if>
        <if test="receiveOrderStatus != null "> and t.receive_order_status = #{receiveOrderStatus}</if>
        <if test="checkStatus != null "> and t.check_status = #{checkStatus}</if>
        <if test="status != null "> and t.status = #{status}</if>
        <if test="isDel != null "> and t.is_del = #{isDel}</if>
        <if test="createTime != null "> and t.create_time between #{createTime}[0] and #{createTime}[1] </if>
        <if test="brandId != null "> and t.brand_id = #{brandId}</if>
        <if test="managerUserId != null "> and t.manager_user_id = #{managerUserId}</if>
        order by t.create_time desc
    </select>
    
    <select id="selectDesignerByDesignerId" parameterType="Long" resultType="com.xgwc.user.entity.vo.DesignerVO">
        select t.designer_id, t.idcard_front, t.idcard_back, t.idcard_is_longterm, t.idcard_start, t.idcard_end, t.name, t.idcard_no, t.phone, t.email, t.emergency_name,
               t.emergency_phone, t.good_business, t.description, t.bank_user_name, t.bank_name, t.bank_no, t.zfb_name, t.zfb_account, t.manager_name, t.manager_phone,
               t.receive_order_status, t.check_status, t.check_time, t.status, t.is_del, t.create_by, t.create_time,t.reason,t.brand_id,t.manager_user_id,
               t.designer_level,t.wechat_id,t.company_id,t.wechat_name,t.wechat_account_url,t.alipay_account_url,
               b.business_name as businessName
        from xgwc_designer t
        left join xgwc_business b on b.business_id = t.good_business
        where t.designer_id = #{designerId}
    </select>

    <select id="findDesignerByManagerUserIdAndBrandIdList" resultType="com.xgwc.user.entity.vo.ApplyInfoVO">
        select t.designer_id as id,t.name as companyName,b.company_name as brandName
        from xgwc_designer t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.check_status != 3
        and t.manager_user_id = #{managerUserId}
        and t.brand_id != #{brandId}
    </select>

    <select id="findDesignerByManagerPhone" resultType="com.xgwc.user.entity.vo.DesignerVO">
        <include refid="selectDesignerVo"/>
        where manager_phone = #{managerPhone}
        and brand_id = #{brandId}
    </select>

    <select id="findDesignerByManagerUserIdAndBrandId" resultType="com.xgwc.user.entity.vo.DesignerVO">
        select t.*,b.company_name as brandName
        from xgwc_designer t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.check_status != 3
        and t.manager_user_id = #{managerUserId}
        and t.brand_id = #{brandId}
    </select>

    <select id="findDesignerByManagerUserId" resultType="com.xgwc.user.entity.vo.DesignerVO">
        select t.*
        from xgwc_designer t
        where t.check_status != 3
        and t.manager_user_id = #{managerUserId}
    </select>

    <insert id="insertDesigner" parameterType="com.xgwc.user.entity.Designer" useGeneratedKeys="true" keyProperty="designerId">
        insert into xgwc_designer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="designerId != null">designer_id,</if>
            <if test="idcardFront != null">idcard_front,</if>
            <if test="idcardBack != null">idcard_back,</if>
            <if test="idcardIsLongterm != null">idcard_is_longterm,</if>
            <if test="idcardStart != null">idcard_start,</if>
            <if test="idcardEnd != null">idcard_end,</if>
            <if test="name != null">name,</if>
            <if test="idcardNo != null">idcard_no,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="emergencyName != null">emergency_name,</if>
            <if test="emergencyPhone != null">emergency_phone,</if>
            <if test="goodBusiness != null">good_business,</if>
            <if test="description != null">description,</if>
            <if test="bankUserName != null">bank_user_name,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="bankNo != null">bank_no,</if>
            <if test="zfbName != null">zfb_name,</if>
            <if test="zfbAccount != null">zfb_account,</if>
            <if test="managerName != null">manager_name,</if>
            <if test="managerPhone != null">manager_phone,</if>
            <if test="receiveOrderStatus != null">receive_order_status,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="status != null">status,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="reason != null">reason,</if>
            <if test="managerUserId != null">manager_user_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="businessCode != null">business_code,</if>
            <if test="wechatName != null">wechat_name,</if>
            <if test="wechatAccountUrl != null">wechat_account_url,</if>
            <if test="alipayAccountUrl != null">alipay_account_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="designerId != null">#{designerId},</if>
            <if test="idcardFront != null">#{idcardFront},</if>
            <if test="idcardBack != null">#{idcardBack},</if>
            <if test="idcardIsLongterm != null">#{idcardIsLongterm},</if>
            <if test="idcardStart != null">#{idcardStart},</if>
            <if test="idcardEnd != null">#{idcardEnd},</if>
            <if test="name != null">#{name},</if>
            <if test="idcardNo != null">#{idcardNo},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="emergencyName != null">#{emergencyName},</if>
            <if test="emergencyPhone != null">#{emergencyPhone},</if>
            <if test="goodBusiness != null">#{goodBusiness},</if>
            <if test="description != null">#{description},</if>
            <if test="bankUserName != null">#{bankUserName},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="bankNo != null">#{bankNo},</if>
            <if test="zfbName != null">#{zfbName},</if>
            <if test="zfbAccount != null">#{zfbAccount},</if>
            <if test="managerName != null">#{managerName},</if>
            <if test="managerPhone != null">#{managerPhone},</if>
            <if test="receiveOrderStatus != null">#{receiveOrderStatus},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="status != null">#{status},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="reason != null">#{reason},</if>
            <if test="managerUserId != null">#{managerUserId},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="businessCode != null">#{businessCode},</if>
            <if test="wechatName != null">#{wechatName},</if>
            <if test="wechatAccountUrl != null">#{wechatAccountUrl},</if>
            <if test="alipayAccountUrl != null">#{alipayAccountUrl},</if>
         </trim>
    </insert>

    <update id="updateDesigner" parameterType="com.xgwc.user.entity.Designer">
        update xgwc_designer
        <trim prefix="SET" suffixOverrides=",">
            <if test="idcardFront != null">idcard_front = #{idcardFront},</if>
            <if test="idcardBack != null">idcard_back = #{idcardBack},</if>
            <if test="idcardIsLongterm != null">idcard_is_longterm = #{idcardIsLongterm},</if>
            <if test="idcardStart != null">idcard_start = #{idcardStart},</if>
            <if test="idcardEnd != null">idcard_end = #{idcardEnd},</if>
            <if test="name != null">name = #{name},</if>
            <if test="idcardNo != null">idcard_no = #{idcardNo},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="emergencyName != null">emergency_name = #{emergencyName},</if>
            <if test="emergencyPhone != null">emergency_phone = #{emergencyPhone},</if>
            <if test="goodBusiness != null">good_business = #{goodBusiness},</if>
            <if test="description != null">description = #{description},</if>
            <if test="bankUserName != null">bank_user_name = #{bankUserName},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="bankNo != null">bank_no = #{bankNo},</if>
            <if test="zfbName != null">zfb_name = #{zfbName},</if>
            <if test="zfbAccount != null">zfb_account = #{zfbAccount},</if>
            <if test="managerName != null">manager_name = #{managerName},</if>
            <if test="managerPhone != null">manager_phone = #{managerPhone},</if>
            <if test="receiveOrderStatus != null">receive_order_status = #{receiveOrderStatus},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="designerLevel != null">designer_level = #{designerLevel},</if>
            <if test="wechatId != null">wechat_id = #{wechatId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="wechatName != null">wechat_name = #{wechatName},</if>
            <if test="wechatAccountUrl != null">wechat_account_url = #{wechatAccountUrl},</if>
            <if test="alipayAccountUrl != null">alipay_account_url = #{alipayAccountUrl},</if>
        </trim>
        where designer_id = #{designerId}
    </update>

    <update id="deleteDesignerByDesignerId" parameterType="Long">
        update xgwc_designer set is_del = 1 where designer_id = #{designerId}
    </update>

    <update id="deleteDesignerByDesignerIds" parameterType="String">
        update xgwc_designer set is_del = 1 where designer_id in
        <foreach item="designerId" collection="array" open="(" separator="," close=")">
            #{designerId}
        </foreach>
    </update>

    <select id="getSimpleDesignerBrandDtoList" resultType="com.xgwc.user.entity.dto.SimpleDesignerBrandDto">
        select t.manager_user_id userId, b.brand_id brandId,  ifnull(b.company_simple_name, b.company_name) brandName
        from xgwc_designer t
                 left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.manager_user_id = #{userId} and t.status = 0 and t.is_del = 0 and b.is_del = 0
    </select>

    <select id="getStaffUserIdListByDeptIds" resultType="java.lang.Long">
        SELECT
            t.manager_user_id
        FROM
            xgwc_designer t
        WHERE
            t.brand_id = #{brandId}
          AND t.good_business IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
          AND t.`status` = 0
          AND t.is_del = 0
    </select>

    <select id="getStaffUserIdListByBrand" resultType="java.lang.Long">
        SELECT
            t.manager_user_id
        FROM
            xgwc_designer t
        WHERE
            t.brand_id = #{brandId}
          AND t.`status` = 0
          AND t.is_del = 0
    </select>
    
    <select id="countStaffUserIdListByBrand" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            xgwc_designer t
        WHERE
            t.brand_id = #{brandId}
          AND t.`status` = 0
          AND t.is_del = 0
    </select>

    <select id="getSimpleUserInfoDtoListByBusinessId" resultType="com.xgwc.user.entity.dto.SimpleUserInfoDto">
        SELECT
            t.manager_user_id userId,
            t.`name` userName
        FROM
            xgwc_designer t
        WHERE
            t.brand_id = #{brandId}
          AND t.good_business = #{businessId}
          AND t.`status` = 0
          AND t.is_del = 0
          order by manager_user_id asc
    </select>

    <select id="getSimpleUserInfoDtoList" resultType="com.xgwc.user.entity.dto.SimpleClassifyUserInfoDto">
        SELECT
            t.manager_user_id userId,
            t.`name` userName,
            b.business_id businessId,
            b.business_name businessName
        FROM
            xgwc_designer t
                left join xgwc_business b on t.good_business = b.business_id
        WHERE
            t.brand_id = #{brandId}
          AND t.`status` = 0
          AND t.is_del = 0
        order by b.sort asc, t.manager_user_id asc
    </select>

    <select id="findClassifyByFranchiseId" resultType="java.lang.String">
        select business_ids from xgwc_shop_franchise where franchise_id = #{franchiseId}
    </select>

    <select id="findFranchiseeDesignerList" resultType="com.xgwc.user.entity.vo.DesignerVO">
        select t.designer_id,t.name, t.idcard_no, t.phone, t.email, t.emergency_name,t.emergency_phone, t.good_business, t.description,
            t.manager_name, t.manager_phone, t.receive_order_status, t.check_status, t.check_time, t.status, t.create_time,b.business_name as businessName,
            t.brand_id,xbo.company_name as brandName,t.wechat_name,t.wechat_account_url,t.alipay_account_url
        from xgwc_designer t
        left join xgwc_business b on b.business_id = t.good_business
        left join xgwc_brand_owner xbo on xbo.brand_id = t.brand_id
        WHERE b.level IN (
            SELECT DISTINCT SUBSTRING_INDEX(xb.level, '_', 2)
            FROM xgwc_shop s
            left join xgwc_shop_business sb on s.shop_id  = sb.shop_id
            LEFT JOIN xgwc_business xb ON xb.business_id = sb.business_id
            where s.franchise_owner_id in (
                select id from franchise_owner where franchise_id = #{franchiseId}
            )
        )
        and t.check_status = 1 and t.is_del = 0
        and t.receive_order_status is not null
        <if test="name != null  and name != ''"> and t.name = #{name} </if>
        <if test="phone != null  and phone != ''"> and t.phone = #{phone} </if>
        <if test="managerName != null  and managerName != ''"> and t.manager_name = #{managerName}</if>
        <if test="managerPhone != null  and managerPhone != ''"> and t.manager_phone = #{managerPhone}</if>
        <if test="receiveOrderStatus != null "> and t.receive_order_status = #{receiveOrderStatus}</if>
        <if test="checkStatus != null "> and t.check_status = #{checkStatus}</if>
        <if test="status != null "> and t.status = #{status}</if>
        <if test="brandId != null "> and t.brand_id = #{brandId}</if>
        order by t.create_time desc
    </select>

    <select id="findDesignerDropdownByBusinessId" resultType="com.xgwc.user.entity.vo.DesignerSimpleVO">
        select t.designer_id,t.name, t.phone,t.good_business,t.check_status,t.status,
               b.business_name as businessName,
               t.brand_id,xbo.company_name as brandName
        from xgwc_designer t
        left join xgwc_business b on b.business_id = t.good_business
        left join xgwc_brand_owner xbo on xbo.brand_id = t.brand_id
        WHERE b.level IN (
            SELECT DISTINCT SUBSTRING_INDEX(xb.level, '_', 2)
            FROM xgwc_shop s
            left join xgwc_shop_business sb on s.shop_id  = sb.shop_id
             LEFT JOIN xgwc_business xb ON xb.business_id = sb.business_id
            where s.franchise_owner_id in (
                select id from franchise_owner where franchise_id = #{franchiseId}
            )
          )
          and t.check_status = 1 and t.is_del = 0
          and b.business_id = #{businessId}
    </select>

    <select id="findBrandDesignerDropdown" resultType="com.xgwc.user.entity.vo.DesignerSimpleVO">
        select t.designer_id,t.name, t.phone,t.good_business,t.check_status,t.status,
               b.business_name as businessName,
               t.brand_id,xbo.company_name as brandName
        from xgwc_designer t
        left join xgwc_business b on b.business_id = t.good_business
        left join xgwc_brand_owner xbo on xbo.brand_id = t.brand_id
        WHERE t.brand_id = #{brandId}
        and t.check_status = 1 and t.is_del = 0 and t.status = 0
    </select>

    <select id="findBrandDesignerByBusinessId" resultType="com.xgwc.user.entity.vo.DesignerSimpleVO">
        select t.designer_id,t.name, t.phone,t.good_business,t.check_status,t.status,
               b.business_name as businessName,
               t.brand_id,xbo.company_name as brandName
        from xgwc_designer t
        left join xgwc_business b on b.business_id = t.good_business
        left join xgwc_brand_owner xbo on xbo.brand_id = t.brand_id
        WHERE b.business_id = #{businessId}
        and t.brand_id = #{brandId}
        and t.check_status = 1 and t.is_del = 0

    </select>

    <select id="countApplyDesignerByBrandId" resultType="java.lang.Integer">
        SELECT count(1)
        FROM xgwc_designer t
        WHERE  t.brand_id = #{brandId}
        AND t.check_status = #{checkStatus}
        AND t.is_del = 0
    </select>

    <select id="countPassApplyDesignerByBrandId" resultType="java.lang.Integer">
        SELECT count(1)
        FROM xgwc_designer t
        WHERE  t.brand_id = #{brandId}
        AND t.check_status = 1
        AND t.receive_order_status = #{receiveOrderStatus}
        AND t.is_del = 0
    </select>

    <select id="countFranchiseeDesignerByFranchiseId" resultType="java.lang.Integer">
        select count(1)
        from xgwc_designer t
        left join xgwc_business b on b.business_id = t.good_business
        WHERE b.level IN (
            SELECT DISTINCT SUBSTRING_INDEX(xb.level, '_', 2)
            FROM xgwc_shop s
            left join xgwc_shop_business sb on s.shop_id  = sb.shop_id
            LEFT JOIN xgwc_business xb ON xb.business_id = sb.business_id
            where s.franchise_owner_id in (
                select id from franchise_owner where franchise_id = #{franchiseId}
            )
        )
        and t.check_status = 1 and t.is_del = 0
        and t.receive_order_status = #{receiveOrderStatus}
    </select>

    <select id="getDesignerByUserId" resultType="com.xgwc.user.entity.Designer">
        SELECT
            t.designer_id,
            t.idcard_front,
            t.idcard_back,
            t.idcard_is_longterm,
            t.idcard_start,
            t.idcard_end,
            t.NAME,
            t.idcard_no,
            t.phone,
            t.email,
            t.emergency_name,
            t.emergency_phone,
            t.good_business,
            t.description,
            t.bank_user_name,
            t.bank_name,
            t.bank_no,
            t.zfb_name,
            t.zfb_account,
            t.manager_name,
            t.manager_phone,
            t.receive_order_status,
            t.check_status,
            t.check_time,
            t.STATUS,
            t.is_del,
            t.create_by,
            t.create_time,
            t.reason,
            t.brand_id,
            t.manager_user_id,
            t.designer_level,
            t.wechat_id,
            t.company_id,
            t.wechat_name,
            t.wechat_account_url,
            t.alipay_account_url
        FROM
            xgwc_designer t
        WHERE
            t.manager_user_id = #{userId}
    </select>

</mapper>