<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.DeptReportMapper">

    <sql id="SalesEfficiencySql">
        select
            brand_id,
            franchise_id,
            user_id,
            dept_id,
            sum(if(pid = 0 , order_amount , 0)) order_amount,
            sum(if(pid = 0  and transfer_state = 0, order_amount , 0)) order_amount_zc,
            sum(if(pid = 0 , 1, 0)) order_num,
            sum(if(pid = 0 and transfer_state = 0, 1, 0)) order_num_zc,
            sum(if(pid = 0 and transfer_state = 0 and refund_status = 0, 1, 0)) order_num_yx,
            sum(if(pid = 0 , inquiries_number, 0)) line_num,
            sum(if(pid = 0 and is_buyback = 0, order_amount , 0)) xk_amount,
            sum(if(pid = 0 and is_buyback = 0 and transfer_state = 0, order_amount , 0)) xk_amount_zc,
            sum(if(pid = 0 and is_buyback = 0, 1 , 0)) xk_num,
            sum(if(pid = 0 and is_buyback = 0 and transfer_state = 0, 1 , 0)) xk_num_zc,
            sum(if(pid = 0 and is_buyback = 0 and transfer_state = 0 and refund_status = 0, 1 , 0)) xk_num_yx,
            sum(if(pid = 0 and is_buyback = 1, order_amount , 0)) hg_amount,
            sum(if(pid = 0 and is_buyback = 1 and transfer_state = 0, order_amount , 0)) hg_amount_zc,
            sum(if(pid = 0 and is_buyback = 1, 1 , 0)) hg_num,
            sum(if(pid = 0 and is_buyback = 1 and transfer_state = 0, 1 , 0)) hg_num_zc,
            sum(if(pid = 0 and is_transfer = 1, order_amount , 0)) zjs_amount,
            sum(if(pid = 0 and is_transfer = 1 and transfer_state = 0, order_amount , 0)) zjs_amount_zc,
            sum(if(pid = 0 and is_transfer = 1, 1 , 0)) zjs_num,
            sum(if(pid = 0 and is_transfer = 1 and transfer_state = 0, 1 , 0)) zjs_num_zc,
            sum(if(pid = 0 and refund_status = 0 and transfer_state = 0, order_amount, 0)) yx_amount,
            sum(money) money,
            sum(if(transfer_state = 0, money, 0)) money_zc,
            sum(if(refund_status = 0 and transfer_state = 0, money, 0)) money_yx,
            sum(if(pid = 0 and refund_status != 0 , amount - now_amount, 0)) tk_amount,
            sum(if(pid = 0 and refund_status != 0 and transfer_state = 0 , amount - now_amount, 0)) tk_amount_zc,
            sum(if(pid = 0 and refund_status != 0 , 1, 0)) tk_num,
            sum(if(pid = 0 and refund_status != 0 and transfer_state = 0 , 1, 0)) tk_num_zc
        from (
                 select
                     ifnull(o.sale_man_id, rs.user_id) user_id,
                     ifnull(o.franchise_id, rs.franchise_id) franchise_id,
                     ifnull(o.brand_id, rs.brand_id) brand_id,
                     ifnull(o.dept_id, rs.dept_id) dept_id,
                     fs.stage_name user_name,
                     o.transfer_state,
                     o.order_date,
                     o.order_amount,
                     o.amount,
                     o.now_amount,
                     ifnull(o.pid, 0) pid,
                     o.money,
                     o.create_time,
                     o.is_del,
                     o.refund_status,
                     o.is_transfer,
                     o.is_buyback,
                     rs.inquiries_number
                 from xgwc_franchise_staff fs
              left join xgwc_order o on fs.id = o.sale_man_id
              left join report_inbound_sales rs on fs.id = rs.user_id
             ) a
        where ifnull(user_id,0) > 0
        <if test="franchiseId != null">
            and a.franchise_id = #{franchiseId}
        </if>
        <if test="deptId != null">
            and a.dept_id = #{deptId}
        </if>
        <if test="userId != null">
            and a.user_id = #{userId}
        </if>
        <if test="orderDateStart != null">
            and a.order_date >= #{orderDateStart}
        </if>
        <if test="orderDateEnd != null">
            and a.order_date &lt;= #{orderDateEnd}
        </if>
        <if test="createTime != null">
            and a.create_time >=  #{createTime}
        </if>
        group by brand_id,franchise_id,user_id,dept_id
    </sql>
    <select id="SalesEfficiency" parameterType="com.xgwc.user.entity.dto.SalesEfficiencyQueryDto" resultType="com.xgwc.user.entity.vo.SalesEfficiencyVo">
        select
            t.*,
            ROUND(t.xk_num / t.line_num * 100, 2) AS xk_rate,
            ROUND(t.xk_num_yx / t.line_num * 100, 2) AS xk_rate_yx,
            ROUND(t.xk_num_zc / t.line_num * 100, 2) AS xk_rate_zc,
            ROUND(t.xk_amount / t.xk_num, 2) AS xk_price,
            ROUND(t.xk_amount_zc / t.xk_num_zc, 2) AS xk_price_zc,
            ROUND(t.xk_amount / t.line_num, 2) AS xk_value,
            ROUND(t.xk_amount_zc / t.line_num, 2) AS xk_value_zc,
            ROUND(t.money / t.yx_amount * 100, 2) AS yj_rate,
            ROUND(t.money_yx / t.yx_amount * 100, 2) AS yj_rate_yx,
            ROUND(t.money_zc / t.yx_amount * 100, 2) AS yj_rate_zc,
            ROUND(t.order_num_zc / t.order_num * 100, 2) AS zc_rate,
            ROUND(t.order_num_yx / t.order_num * 100, 2) AS yx_rate,
            ROUND(t.tk_num / t.order_num * 100, 2) AS tk_rate,
            ROUND(t.tk_num_zc / t.order_num_zc * 100, 2) AS tk_rate_zc,
            fs.stage_name user_name,
            d.dept_name,
            ifnull( fo.company_simple_name, fo.company_name ) franchise_name
        from(
            <include refid="SalesEfficiencySql"/>
        ) t
        left join xgwc_franchise_staff fs on t.user_id = fs.id
        left join franchise_dept d on t.dept_id = d.dept_id
        left join franchise_owner fo on t.franchise_id = fo.franchise_id
    </select>

    <select id="SalesEfficiencyTotal" parameterType="com.xgwc.user.entity.dto.SalesEfficiencyQueryDto" resultType="com.xgwc.user.entity.vo.SalesEfficiencyVo">
        select
            "汇总" franchise_id,
        sum(order_amount) order_amount,
        sum(order_amount_zc) order_amount_zc,
        sum(order_num) order_num,
        sum(order_num_zc) order_num_zc,

        sum(yx_amount) yx_amount,


        sum(xk_amount) xk_amount,
        sum(xk_amount_zc) xk_amount_zc,
        sum(xk_num) xk_num,
        sum(xk_num_zc) xk_num_zc,

        sum(hg_num) hg_num,
        sum(hg_num_zc) hg_num_zc,
        sum(hg_amount) hg_amount,
        sum(hg_amount_zc) hg_amount_zc,

        sum(zjs_num) zjs_num,
        sum(zjs_num_zc) zjs_num_zc,
        sum(zjs_amount) zjs_amount,
        sum(zjs_amount_zc) zjs_amount_zc,
        sum(money) money,
        sum(money_yx) money_yx,
        sum(money_zc) money_zc,

        sum(tk_num) tk_num,
        sum(tk_num_zc) tk_num_zc,
        sum(tk_amount) tk_amount,
        sum(tk_amount_zc) tk_amount_zc,
        sum(line_num) line_num,
        ROUND(sum(t.xk_num )/    sum(t.line_num) * 100, 2) AS xk_rate,
        ROUND(sum(t.xk_num_yx) / sum(t.line_num) * 100, 2) AS xk_rate_yx,
        ROUND(sum(t.xk_num_zc) / sum(t.line_num) * 100, 2) AS xk_rate_zc,
        ROUND(sum(t.xk_amount) / sum(t.xk_num), 2) AS xk_price,
        ROUND(sum(t.xk_amount_zc) / sum(t.xk_num_zc), 2) AS xk_price_zc,
        ROUND(sum(t.xk_amount) / sum(t.line_num), 2) AS xk_value,
        ROUND(sum(t.xk_amount_zc) / sum(t.line_num), 2) AS xk_value_zc,
        ROUND(sum(t.money) /    sum(t.yx_amount) * 100, 2) AS yj_rate,
        ROUND(sum(t.money_yx) / sum(t.yx_amount) * 100, 2) AS yj_rate_yx,
        ROUND(sum(t.money_zc) / sum(t.yx_amount) * 100, 2) AS yj_rate_zc,
        ROUND(sum(t.order_num_zc) / sum(t.order_num) * 100, 2) AS zc_rate,
        ROUND(sum(t.order_num_yx) / sum(t.order_num) * 100, 2) AS yx_rate,
        ROUND(sum(t.tk_num) / sum(t.order_num) * 100, 2) AS tk_rate,
        ROUND(sum(t.tk_num_zc) / sum(t.order_num_zc) * 100, 2) AS tk_rate_zc
        from(
        <include refid="SalesEfficiencySql"/>
        ) t
    </select>

</mapper>