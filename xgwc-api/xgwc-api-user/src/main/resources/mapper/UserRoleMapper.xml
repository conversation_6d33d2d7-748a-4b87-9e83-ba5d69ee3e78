<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.UserRoleMapper">

    <select id="listRoleKeyByUserId" resultType="java.lang.String">
        select DISTINCT code
        from
            sys_role r
            left join sys_user_role ur on r.id = ur.role_id
        where ur.user_id = #{userId}
    </select>

</mapper>