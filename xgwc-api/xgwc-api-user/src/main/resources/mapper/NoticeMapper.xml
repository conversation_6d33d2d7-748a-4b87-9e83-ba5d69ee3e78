<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.NoticeMapper">
    
    <insert id="insertNotice" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO notice (
            notice_type,
            classify_id,
            title,
            content,
            attachment_count,
            attachment,
            send_count,
            send_group,
            schedule_time,
            STATUS,
            send_status,
            publish_time,
            create_by,
            create_time,
            update_by,
            update_time,
            source, source_id)
        VALUES(#{noticeType,jdbcType=INTEGER}, #{classifyId,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{attachmentCount}, #{attachment},#{sendCount,jdbcType=INTEGER},
              #{sendGroup,jdbcType=VARCHAR}, #{scheduleTime,jdbcType=VARCHAR}, 0, 0, #{publishTime,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, now()
                  , #{source,jdbcType=INTEGER}, #{sourceId,jdbcType=BIGINT})
    </insert>

    <update id="updateNotice">
        update notice
            <set>
                update_by = #{updateBy}, update_time = now()
                <if test="noticeType != null">
                    ,notice_type = #{noticeType}
                </if>
                <if test="classifyId != null">
                    ,classify_id = #{classifyId}
                </if>
                <if test="title != null">
                    ,title = #{title}
                </if>
                <if test="content != null">
                    ,content = #{content}
                </if>
                <if test="attachment != null">
                    ,attachment = #{attachment}
                </if>
                <if test="attachmentCount != null">
                    ,attachment_count = #{attachmentCount}
                </if>
                <if test="sendGroup != null">
                    ,send_group = #{sendGroup}
                </if>
                <if test="sendCount != null">
                    ,send_count = #{sendCount}
                </if>
                <if test="scheduleTime != null">
                    ,schedule_time = #{scheduleTime}
                </if>
            </set>
        <where>
            id = #{id} and `source` = #{source} and source_id = #{sourceId}
        </where>
    </update>

    <update id="updateSendedNotice">
        update notice
        <set>
            update_by = #{updateBy}, update_time = now()
            <if test="noticeType != null">
                ,notice_type = #{noticeType}
            </if>
            <if test="classifyId != null">
                ,classify_id = #{classifyId}
            </if>
            <if test="title != null">
                ,title = #{title}
            </if>
            <if test="content != null">
                ,content = #{content}
            </if>
            <if test="attachment != null">
                ,attachment = #{attachment}
            </if>
        </set>
        <where>
            id = #{id} and `source` = #{source} and source_id = #{sourceId}
        </where>
    </update>

    <update id="updateSendStatusNotice">
        update notice set send_status = #{sendStatus} where id = #{noticeId}
    </update>

    <update id="deleteNotice">
        update notice set status = 1, update_by = #{updateBy}, update_time = now() where id = #{id} and `source` = #{source} and source_id = #{sourceId}
    </update>



    <select id="getSelfNoticeById" resultType="com.xgwc.user.entity.dto.notice.SelfNoticeDto">
        SELECT
            n.id,
            n.notice_type noticeType,
            n.classify_id classifyId,
            c.classify_name classifyName,
            n.title,
            n.content,
            n.attachment,
            n.STATUS,
            n.publish_time publishTime,
            n.create_time createTime,
            n.source,
            n.source_id sourceId,
            t.is_read isRead
        FROM
            notice n
            left join franchise_notice_classify c on c.id = n.classify_id
            left join ${tableName} t on t.notice_id = n.id
        WHERE
            n.id = #{noticeId}
            and t.user_id = #{userId}
          AND n.STATUS = 0
    </select>

    <select id="getNoticeByCondition" resultType="com.xgwc.user.entity.dto.notice.NoticeDto">
        SELECT
            n.id,
            n.notice_type noticeType,
            n.classify_id classifyId,
            c.classify_name classifyName,
            n.title,
            n.content,
            n.attachment_count attachmentCount,
            n.attachment,
            n.send_count sendCount,
            n.send_group sendGroup,
            n.schedule_time scheduleTime,
            n.STATUS,
            n.send_status sendStatus,
            n.publish_time publishTime,
            n.create_time createTime,
            n.source,
            n.source_id sourceId
        FROM
            notice n
                left join franchise_notice_classify c on c.id = n.classify_id
        WHERE
            n.id = #{id}
          AND n.`source` = #{source}
          AND n.source_id = #{sourceId}
          AND n.STATUS = 0
    </select>

    <select id="getNoticeById" resultType="com.xgwc.user.entity.dto.notice.NoticeDto">
        SELECT
            n.id,
            n.notice_type noticeType,
            n.classify_id classifyId,
            n.title,
            n.content,
            n.attachment_count attachmentCount,
            n.attachment,
            n.send_count sendCount,
            n.send_group sendGroup,
            n.schedule_time scheduleTime,
            n.STATUS,
            n.send_status sendStatus,
            n.publish_time publishTime,
            n.create_time createTime,
            n.source,
            n.source_id sourceId
        FROM
            notice n
        WHERE
            n.id = #{noticeId}
          AND n.STATUS = 0
    </select>

    <select id="getNoticeList" resultType="com.xgwc.user.entity.dto.notice.NoticeDto">
        select
        n.id,
        n.notice_type noticeType,
        n.classify_id classifyId,
        n.title,
        n.send_count sendCount,
        ifnull(n.read_count, 0) readCount,
        n.schedule_time scheduleTime,
        n.STATUS,
        n.send_status sendStatus,
        n.publish_time publishTime,
        n.create_time createTime,
        n.source,
        n.source_id sourceId,
        n.create_by createBy
        from notice n
        where n.`source` = #{source} and n.source_id = #{sourceId} and n.status = 0
        <if test="title != null">
            and n.title like concat("%", #{title,jdbcType=VARCHAR}, "%")
        </if>
        <if test="noticeType != null">
            and n.notice_type = #{noticeType}
        </if>
        <if test="classifyId != null">
            and n.classify_id = #{classifyId}
        </if>
        <if test="publishTimeStart != null">
            and n.publish_time &gt;= #{publishTimeStart}
        </if>
        <if test="publishTimeEnd != null">
            and n.publish_time &lt;= #{publishTimeEnd}
        </if>
        <if test="publishStatus != null and publishStatus == 0">
            and n.publish_time &lt;= now()
        </if>
        <if test="publishStatus != null and publishStatus == 1">
            and n.publish_time &gt;= now()
        </if>
        order by n.create_time desc
    </select>

    <insert id="batchInsertNoticeDetails">
        INSERT INTO ${tableName} (user_id, notice_id, is_read, status, create_time, update_time)
        VALUES
        <foreach collection="userIds" item="userId" separator=",">
            (#{userId}, #{noticeId}, 0, 0, NOW(), NOW())
        </foreach>
    </insert>

    <update id="updateReadStatus">
        update ${tableName} set is_read = 1 where user_id = #{userId} and notice_id = #{noticeId}
    </update>

    <update id="updateReadCount">
        update notice  set read_count = (read_count + 1) where id = #{noticeId}
    </update>

    <select id="getReadStatus" resultType="java.lang.Integer">
        select is_read from ${tableName} where user_id = #{userId} and notice_id = #{noticeId}
    </select>

    <select id="getBrandNoticeClassifyCountDtoByUserId"
            resultType="com.xgwc.user.entity.dto.notice.BrandNoticeClassifyCountDto">
        SELECT
            classify_id classifyId,
            count( 1 ) count
        FROM
            notice n
            LEFT JOIN ${tableName} d ON n.id = d.notice_id
        where n.`status` = 0 and n.source = 1
          <if test="sourceId != null">
              and n.source_id = #{sourceId}
          </if>
          and d.user_id = #{userId}
        GROUP BY
            n.classify_id
    </select>

    <select id="getFranchiseNoticeClassifyCountDtoByUserId"
            resultType="com.xgwc.user.entity.dto.notice.BrandNoticeClassifyCountDto">
        SELECT
            t.classifyId,
            t.count,
            c.classify_name classifyName
        FROM
            (
                SELECT
                    classify_id classifyId,
                    count( 1 ) count
                FROM
                    notice n
                    LEFT JOIN ${tableName} d ON n.id = d.notice_id
                WHERE
                    n.`status` = 0
                  AND n.source = 2
                  AND n.source_id = #{sourceId}
                  AND d.user_id = #{userId}
                GROUP BY
                    n.classify_id
            ) t
                LEFT JOIN franchise_notice_classify c ON c.id = t.classifyId
    </select>

    <select id="getBrandSelfNoticeList" resultType="com.xgwc.user.entity.dto.notice.SelfNoticeDto">
        SELECT
            n.id, n.notice_type noticeType, n.classify_id classifyId, n.create_by createBy, n.publish_time publishTime, ifnull(d.is_read, 0) isRead, n.title,t.brand_id brandId,
            t.company_name brandName, n.source
        FROM
            notice n
            LEFT JOIN ${tableName} d ON d.notice_id = n.id
            LEFT JOIN xgwc_brand_owner t ON t.brand_id = n.source_id
        WHERE
            n.`status` = 0
            AND d.user_id = #{userId}
            AND n.source = 1
        <if test="brandId != null">
            and n.source_id = #{brandId}
        </if>
        <if test="classifyId != null">
            and classify_id = #{classifyId}
        </if>

    </select>

    <select id="getFranchiseNoticeList" resultType="com.xgwc.user.entity.dto.notice.SelfNoticeDto">
        SELECT
        n.id, n.notice_type noticeType, n.classify_id classifyId, c.classify_name classifyName, n.create_by createBy, n.publish_time publishTime, ifnull(d.is_read, 0) isRead, n.title, n.content, n.attachment,0 brandId,
        '本部门' brandName
        FROM
        notice n
        LEFT JOIN ${tableName} d ON d.notice_id = n.id
        left join franchise_notice_classify c on c.id = n.classify_id
        WHERE
        n.`status` = 0
        AND d.user_id = #{userId}
        AND n.source = 2
        <if test="sourceId != null">
            and n.source_id = #{sourceId}
        </if>
        <if test="classifyId != null">
            and n.classify_id = #{classifyId}
        </if>
    </select>

    <select id="getAllFranchiseNoticeList" resultType="com.xgwc.user.entity.dto.notice.SelfNoticeDto">
        select * from (
        (SELECT
        n.id, n.notice_type noticeType, n.classify_id classifyId, c.classify_name classifyName, n.create_by createBy, n.publish_time publishTime, ifnull(d.is_read, 0) isRead, n.title,
        0 brandId,
        '本部门' brandName, n.source
        FROM
        notice n
        LEFT JOIN ${tableName} d ON d.notice_id = n.id
        left join franchise_notice_classify c on c.id = n.classify_id
        WHERE
        n.`status` = 0
        AND d.user_id = #{userId}
        AND n.source = 2
        and n.source_id = #{sourceId})
        union all
        (
        SELECT
        n.id, n.notice_type noticeType, n.classify_id classifyId, "" classifyName, n.create_by createBy, n.publish_time publishTime, ifnull(d.is_read, 0) isRead, n.title,t.brand_id brandId,
        t.company_name brandName, n.source
        FROM
        notice n
        LEFT JOIN ${tableName} d ON d.notice_id = n.id
        LEFT JOIN xgwc_brand_owner t ON t.brand_id = n.source_id
        WHERE
        n.`status` = 0
        AND d.user_id = #{userId}
        AND n.source = 1
        )) t order by t.publishTime desc

    </select>
    
    <select id="getSimpleBrandDtoByUserId" resultType="com.xgwc.user.entity.dto.notice.SimpleBrandDto">
        SELECT
            t.brand_id brandId,
            any_value(t.company_name) brandName
        FROM
            notice n
                LEFT JOIN ${tableName} d ON n.id = d.notice_id
                LEFT JOIN xgwc_brand_owner t ON t.brand_id = n.source_id
        WHERE
            n.source = 1
            AND d.user_id = #{userId}
            AND n.`status` = 0
        GROUP BY
            t.brand_id
    </select>

    <select id="existFranchiseNotice" resultType="java.lang.Integer">
        SELECT
          count(1)
        FROM
        notice n
        LEFT JOIN ${tableName} d ON d.notice_id = n.id
        WHERE
        n.`status` = 0
        AND d.user_id = #{userId}
        AND n.source = 2
        and n.source_id = #{sourceId}
    </select>
</mapper>