<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FaceVerityRecordMapper">
    
    <select id="getFaceVerityRecordByCertNo" resultType="com.xgwc.user.entity.FaceVerityRecord">
        SELECT
            t.id,
            t.params,
            t.uuid,
            t.user_id,
            t.return_url,
            t.init_result,
            t.cert_name,
            t.cert_no,
            t.certify_url,
            t.verity_pass,
            t.verity_result,
            t.create_time,
            t.certify_id
        FROM
            face_verity_record t
        where t.cert_no = #{certNo} order by t.create_time desc limit 1
    </select>

    <select id="getFaceVerityRecordByCertUserId" resultType="com.xgwc.user.entity.FaceVerityRecord">
        SELECT
            t.id,
            t.params,
            t.uuid,
            t.user_id,
            t.return_url,
            t.init_result,
            t.cert_name,
            t.cert_no,
            t.certify_url,
            t.verity_pass,
            t.verity_result,
            t.create_time,
            t.certify_id
        FROM
            face_verity_record t
        where t.user_id = #{userId} order by t.create_time desc limit 1
    </select>

    <select id="getFaceVerityRecordByCertifyId" resultType="com.xgwc.user.entity.FaceVerityRecord">
        SELECT
            t.id,
            t.params,
            t.uuid,
            t.user_id,
            t.return_url,
            t.init_result,
            t.cert_name,
            t.cert_no,
            t.certify_url,
            t.verity_pass,
            t.verity_result,
            t.create_time,
            t.certify_id
        FROM
            face_verity_record t
        where t.certify_id = #{certifyId} order by t.create_time desc limit 1
    </select>

    <insert id="insertFaceVerityRecord" keyProperty="id" useGeneratedKeys="true" keyColumn="id">
        insert into face_verity_record(params, uuid, user_id, return_url,init_result,cert_name,cert_no,certify_id,certify_url,verity_pass,verity_result,create_time,update_time)
        values(#{params}, #{uuid}, #{userId}, #{returnUrl}, #{initResult}, #{certName}, #{certNo}, #{certifyId}, #{certifyUrl}, #{verityPass}, #{verityResult}, now(), now())
    </insert>

    <update id="updateFaceVerityRecord">
        update face_verity_record
            <set>
                <if test="params !=null">params = #{params},</if>
                <if test="uuid != null"> uuid = #{uuid},</if>
                <if test="userId != null">user_id = #{userId},</if>
                <if test="returnUrl != null">return_url = #{returnUrl},</if>
                <if test="initResult != null">init_result = #{initResult},</if>
                <if test="certName != null">cert_name = #{certName},</if>
                <if test="certNo != null">cert_no = #{certNo},</if>
                <if test="certifyId != null">certify_id = #{certifyId},</if>
                <if test="certifyUrl != null">certify_url = #{certifyUrl},</if>
                <if test="verityPass != null">verity_pass = #{verityPass},</if>
                <if test="verityResult != null">verity_result = #{verityResult},</if>
                update_time = now()
            </set>
           <where>
               id = #{id}
           </where>
    </update>
</mapper>