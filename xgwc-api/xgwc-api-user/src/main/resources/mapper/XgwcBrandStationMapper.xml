<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.XgwcBrandStationMapper">
    <insert id="saveXgwcBrandStation">
        insert into xgwc_brand_station
        (`station_name`, `company_id`, `brand_owner_id`, `sort`, `create_by`, `create_time`)
        values (#{xgwcBrandStationDto.stationName}, #{xgwcBrandStationDto.companyId},
                #{xgwcBrandStationDto.brandOwnerId},
                #{xgwcBrandStationDto.sort},
                #{xgwcBrandStationDto.createBy}, now())
    </insert>

    <update id="updateStatusById">
        update xgwc_brand_station
        set status = #{status}
        where station_id = #{stationId}
    </update>
    <update id="updateXgwcBrandStation">
        update xgwc_brand_station
        <set>
            <if test="xgwcBrandStationDto.stationName != null and xgwcBrandStationDto.stationName != ''">
                `station_name` = #{xgwcBrandStationDto.stationName},
            </if>
            <if test="xgwcBrandStationDto.companyId != null">
                `company_id` = #{xgwcBrandStationDto.companyId},
            </if>
            <if test="xgwcBrandStationDto.sort != null">
                `sort` = #{xgwcBrandStationDto.sort},
            </if>
            <if test="xgwcBrandStationDto.updateBy != null and xgwcBrandStationDto.updateBy != ''">
                `update_by` = #{xgwcBrandStationDto.updateBy},
            </if>
            `update_time` = now()
        </set>
        where station_id = #{xgwcBrandStationDto.stationId}
    </update>

    <select id="getXgwcBrandStationList" resultType="com.xgwc.user.entity.vo.XgwcBrandStationVo">
        SELECT
        xbs.station_id AS stationId,
        xbs.station_name AS stationName,
        xbs.company_id AS companyId,
        xbd.name AS companyName,
        xbs.sort,
        xbs.status,
        xbs.is_del AS isDel,
        xbs.create_by AS createBy,
        xbs.create_time AS createTime,
        xbs.update_by AS updateBy,
        xbs.update_time AS updateTime,
        xbs.modify_time AS modifyTime
        FROM
        xgwc_brand_station xbs
        LEFT JOIN
        sys_company xbd ON xbs.company_id = xbd.id
        <where>
            xbs.`brand_owner_id` = #{xgwcBrandStationParam.brandOwnerId}

            <if test="xgwcBrandStationParam.stationName != null and xgwcBrandStationParam.stationName != ''">
                AND xbs.`station_name` = #{xgwcBrandStationParam.stationName}
            </if>
            <if test="xgwcBrandStationParam.status != null">
                AND xbs.`status` = #{xgwcBrandStationParam.status}
            </if>
            <if test="xgwcBrandStationParam.stationId != null">
                AND xbs.`station_id` = #{xgwcBrandStationParam.stationId}
            </if>
            <if test="xgwcBrandStationParam.companyId != null">
                AND xbs.`company_id` = #{xgwcBrandStationParam.companyId}
            </if>
        </where>
        order by xbs.sort
    </select>
    <select id="getXgwcBrandStationById" resultType="com.xgwc.user.entity.vo.XgwcBrandStationVo">
        SELECT `station_id`   AS stationId,
               `station_name` AS stationName,
               `company_id`   AS companyId,
               `sort`,
               `status`,
               `is_del`       AS isDel,
               `create_by`    AS createBy,
               `create_time`  AS createTime,
               `update_by`    AS updateBy,
               `update_time`  AS updateTime,
               `modify_time`  AS modifyTime
        FROM `xgwc_sass`.`xgwc_brand_station`
        WHERE `station_id` = #{stationId}
    </select>
</mapper>