<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseStaffMapper">
    

    <sql id="selectFranchiseStaffVo">
        select t.id, t.name, t.stage_name, t.dept_id, t.post_id, t.role_ids, t.job_nature, t.status, t.is_principal, t.superior, t.archive_status, t.bind_status, t.bind_user_id, t.login_phone, t.franchise_id,
               t.download_count,
               t.operation_time,
               t.last_operation_time,
               t.last_operation,
               t.is_del, t.create_by, t.create_time,fd.dept_name,t.resignation_time,t.staff_type
        from xgwc_franchise_staff t
        left join franchise_dept fd on fd.dept_id = t.dept_id
    </sql>

    <select id="selectFranchiseStaffList" parameterType="com.xgwc.user.entity.vo.FranchiseStaffQueryVo" resultType="com.xgwc.user.entity.dto.FranchiseStaffPageDto">
        select t.id, t.name, t.stage_name, t.job_nature, t.status, t.archive_status, t.bind_status, t.login_phone,
               t.create_time,t.dept_id, d.dept_name, p.station_name,t.bind_user_id AS userId,t.staff_type
        from xgwc_franchise_staff t
        left join franchise_dept d on t.dept_id = d.dept_id
        left join franchise_station p on t.post_id = p.station_id
        <where>
            <if test="id != null "> and t.id = #{id}</if>
            <if test="deptId != null and deptIds.size() > 0">
                and t.dept_id in
                <foreach item="id" collection="deptIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="postId != null "> and t.post_id = #{postId}</if>
            <if test="roleIds != null  and roleIds != ''"> and t.role_ids like concat('%', #{roleIds}, '%')</if>
            <if test="jobNature != null "> and t.job_nature = #{jobNature}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="isPrincipal != null "> and t.is_principal = #{isPrincipal}</if>
            <if test="superior != null "> and t.superior = #{superior}</if>
            <if test="archiveStatus != null "> and t.archive_status = #{archiveStatus}</if>
            <if test="bindStatus != null "> and t.bind_status = #{bindStatus}</if>
            <if test="bindUserId != null "> and t.bind_user_id = #{bindUserId}</if>
            <if test="franchiseId != null "> and t.franchise_id = #{franchiseId}</if>
            <if test="isDel == null"> and t.is_del = 0</if>
            <if test="name != null ">and t.name like concat('%', #{name}, '%')</if>
            <if test="stageName != null ">and t.stage_name like concat('%', #{stageName}, '%')</if>
            <if test="loginPhone != null ">and t.login_phone like concat('%', #{loginPhone}, '%')</if>
            <if test="other != null  and other != ''">
                and (
                    t.name like concat('%', #{other}, '%')
                    or t.stage_name like concat('%', #{other}, '%')
                    or t.login_phone like concat('%', #{other}, '%')
                )
            </if>
            <if test="startTime != null and endTime != null">
                and t.create_time BETWEEN CONCAT(#{startTime}, ' 00:00:00') AND CONCAT(#{endTime}, ' 23:59:59')
            </if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectFranchiseStaffById" parameterType="Long" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        <include refid="selectFranchiseStaffVo"/>
        where t.id = #{id}
    </select>

    <select id="selectFranchiseStaffByUserId" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        <include refid="selectFranchiseStaffVo"/>
        where t.bind_user_id = #{userId}  and t.`status` = 0 and t.is_del = 0
    </select>

    <select id="findFranchiseStaffByNameAndFranchiseId" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        <include refid="selectFranchiseStaffVo"/>
        where t.name = #{name}
        and t.is_del = 0
        and t.franchise_id = #{franchiseId}
    </select>

    <insert id="insertFranchiseStaff" parameterType="com.xgwc.user.entity.FranchiseStaff" useGeneratedKeys="true" keyProperty="id">
        insert into xgwc_franchise_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="stageName != null">stage_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="postId != null">post_id,</if>
            <if test="roleIds != null">role_ids,</if>
            <if test="jobNature != null">job_nature,</if>
            <if test="status != null">status,</if>
            <if test="isPrincipal != null">is_principal,</if>
            <if test="superior != null">superior,</if>
            <if test="archiveStatus != null">archive_status,</if>
            <if test="bindStatus != null">bind_status,</if>
            <if test="bindUserId != null">bind_user_id,</if>
            <if test="loginPhone != null">login_phone,</if>
            <if test="franchiseId != null">franchise_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="resignationTime != null">resignation_time,</if>
            <if test="staffType != null">staff_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="stageName != null">#{stageName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="postId != null">#{postId},</if>
            <if test="roleIds != null">#{roleIds},</if>
            <if test="jobNature != null">#{jobNature},</if>
            <if test="status != null">#{status},</if>
            <if test="isPrincipal != null">#{isPrincipal},</if>
            <if test="superior != null">#{superior},</if>
            <if test="archiveStatus != null">#{archiveStatus},</if>
            <if test="bindStatus != null">#{bindStatus},</if>
            <if test="bindUserId != null">#{bindUserId},</if>
            <if test="loginPhone != null">#{loginPhone},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="resignationTime != null">#{resignationTime},</if>
            <if test="staffType != null">#{staffType},</if>
         </trim>
    </insert>

    <update id="updateFranchiseStaff" parameterType="com.xgwc.user.entity.FranchiseStaff">
        update xgwc_franchise_staff
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="stageName != null">stage_name = #{stageName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="postId != null">post_id = #{postId},</if>
            <if test="roleIds != null">role_ids = #{roleIds},</if>
            <if test="jobNature != null">job_nature = #{jobNature},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isPrincipal != null">is_principal = #{isPrincipal},</if>
            <if test="superior != null">superior = #{superior},</if>
            <if test="archiveStatus != null">archive_status = #{archiveStatus},</if>
            <if test="bindStatus != null">bind_status = #{bindStatus},</if>
            <if test="bindUserId != null">bind_user_id = #{bindUserId},</if>
            <if test="loginPhone != null">login_phone = #{loginPhone},</if>
            <if test="franchiseId != null">franchise_id = #{franchiseId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="resignationTime != null">resignation_time = #{resignationTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteFranchiseStaffById" parameterType="Long">
        update xgwc_franchise_staff set is_del = 1 where id = #{id}
    </update>

    <update id="deleteFranchiseStaffByIds" parameterType="String">
        update xgwc_franchise_staff set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBindStatus">
        update xgwc_franchise_staff set bind_status = #{bindStatus}, bind_user_id = #{bindUserId},login_phone = #{phone},stage_name = #{stageName} where id = #{id}
    </update>

    <update id="updateFranchiseeStaffStatus">
        update xgwc_franchise_staff set status = #{status} where id = #{id}
    </update>
    <update id="updateStaffDownloadLimit">
        INSERT INTO xgwc_franchise_staff_dl_limit (
            id,
            staff_id,
            brand_id,
            download_limit,
            create_time,
            update_time,
            update_by
        ) VALUES (
                     #{id},
                     #{staffId},
                     #{brandId},
                     #{downloadLimit},
                     #{operationTime},
                     NOW(),
                     #{lastOperation}
                 ) ON DUPLICATE KEY UPDATE
            download_limit = VALUES(download_limit),
            create_time = VALUES(create_time),
            update_time = NOW(),
            update_by = VALUES(update_by);
    </update>

    <update id="refreshFranchiseStaffDownloadCount">
        UPDATE xgwc_franchise_staff_dl_count SET download_count = 0;
    </update>

    <update id="refreshBrandStaffDownloadCount">
        UPDATE xgwc_staff SET download_count = 0 WHERE download_count != 0;
    </update>

    <select id="getStaffUserIdListByDeptId" resultType="java.lang.Long">
        SELECT
            t.bind_user_id
        FROM
            xgwc_franchise_staff t
            LEFT JOIN franchise_dept d ON t.dept_id = d.dept_id
        WHERE
            t.`status` = 0
          AND t.is_del = 0
          AND d.`status` = 0
          AND d.is_del = 0
          and t.dept_id = #{deptId}
          and d.franchise_id = #{franchiseId}
    </select>

    <select id="getStaffUserIdListByDeptIds" resultType="java.lang.Long">
        SELECT
            t.bind_user_id
        FROM
            xgwc_franchise_staff t
                LEFT JOIN franchise_dept d ON t.dept_id = d.dept_id
        WHERE
            t.`status` = 0
          AND t.is_del = 0
          AND d.`status` = 0
          AND d.is_del = 0
          and t.dept_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        and d.franchise_id = #{franchiseId}
    </select>

    <select id="getStaffUserIdListByDeptIdsAndBrandId" resultType="java.lang.Long">
        SELECT
        t.bind_user_id
        FROM
        xgwc_franchise_staff t
        LEFT JOIN franchise_dept d ON t.dept_id = d.dept_id
        left join franchise_owner o on o.franchise_id = t.franchise_id
        WHERE
        t.`status` = 0
        AND t.is_del = 0
        AND d.`status` = 0
        AND d.is_del = 0
        and t.dept_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        and o.brand_id = #{brandId}
    </select>

    <select id="getStaffUserIdListByFranchise" resultType="java.lang.Long">
        SELECT
        t.bind_user_id
        FROM
        xgwc_franchise_staff t
        LEFT JOIN franchise_dept d ON t.dept_id = d.dept_id
        WHERE
        t.`status` = 0
        AND t.is_del = 0
        AND d.`status` = 0
        AND d.is_del = 0
        and d.franchise_id = #{franchiseId}
    </select>

    <select id="countStaffUserIdByFranchise" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            xgwc_franchise_staff t
                LEFT JOIN franchise_dept d ON t.dept_id = d.dept_id
        WHERE
            t.`status` = 0
          AND t.is_del = 0
          AND d.`status` = 0
          AND d.is_del = 0
          and d.franchise_id = #{franchiseId}
    </select>

    <select id="countStaffUserIdByBrandId" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            xgwc_franchise_staff t
                LEFT JOIN franchise_owner fo ON fo.franchise_id = t.franchise_id
        WHERE
            t.`status` = 0
          AND t.is_del = 0
          AND fo.`status` = 0
          AND fo.is_del = 0
          AND fo.brand_id = #{brandId}
    </select>

    <select id="getStaffUserIdListByBrandId" resultType="java.lang.Long">
        SELECT
            t.bind_user_id
        FROM
            xgwc_franchise_staff t
                LEFT JOIN franchise_owner o on o.franchise_id = t.franchise_id
        WHERE
            t.`status` = 0
          AND t.is_del = 0
          AND o.`status` = 0
          AND o.is_del = 0
          and o.brand_id = #{brandId}
    </select>

    <select id="getStaffUserInfoListByFranchise" resultType="com.xgwc.user.entity.dto.SimpleDeptUserInfoDto">
        SELECT
            t.bind_user_id userId,
            t.name userName,
            t.dept_id deptId
        FROM
            xgwc_franchise_staff t
                LEFT JOIN franchise_dept d ON t.dept_id = d.dept_id
        WHERE
            t.`status` = 0
          AND t.is_del = 0
          AND d.`status` = 0
          AND d.is_del = 0
          and d.franchise_id = #{franchiseId}
    </select>
    <select id="selectFranchiseStaffListDropDown" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        select t.id, t.name, t.stage_name
        from xgwc_franchise_staff t
        where
                t.franchise_id = #{franchiseId}
                and t.archive_status = 0
                and t.is_del = 0
                and t.status = 0
    </select>

    <select id="findFranchiseStaffByUserIdAndFranchiseId"
            resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        <include refid="selectFranchiseStaffVo"/>
        where t.bind_user_id = #{userId}
        and t.franchise_id = #{franchiseId}
        and t.is_del = 0
    </select>

    <select id="findFranchiseStaffByBindUserId" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        <include refid="selectFranchiseStaffVo"/>
        where t.bind_user_id = #{bindUserId}
        and t.is_del = 0
        -- and t.status = 0
    </select>

    <select id="selectStaffDownloadLimitList" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        select xfs.id,
        xfs.name,
        xfs.role_ids as roleIds,
        xfs.franchise_id as franchiseId,
        xfs.stage_name as stageName,
        xfs.login_phone as loginPhone,
        fo.company_name as franchiseShortName,
        xfsd.download_limit as downloadLimit,
        xfsd.create_time as operationTime,
        xfsd.update_time as lastOperationTime,
        xfsd.update_by as lastOperation
        from xgwc_franchise_staff xfs
        left join franchise_owner fo on xfs.franchise_id = fo.franchise_id
        left join xgwc_franchise_staff_dl_limit xfsd on xfs.id = xfsd.staff_id
        <where>
            fo.brand_id = #{franchiseStaff.brandId}

            <if test="franchiseStaff.limitName != null and franchiseStaff.limitName != ''">
                AND (
                xfs.name = #{franchiseStaff.limitName}
                OR xfs.stage_name = #{franchiseStaff.limitName}
                OR xfs.login_phone = #{franchiseStaff.limitName}
                OR xfsd.update_by = #{franchiseStaff.limitName}
                )
            </if>
            <if test="franchiseStaff.isModify != null">
                and xfsd.is_modified = #{franchiseStaff.isModify}
            </if>
            <if test="franchiseStaff.franchiseId != null">
                and xfs.franchise_id = #{franchiseStaff.franchiseId}
            </if>
        </where>
    </select>
    <select id="selectMaxRoleDateDownloadLimit" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        SELECT
        fr.role_id AS roleId,
        frd.download_limit AS maxRoleDateDownloadLimit
        FROM `xgwc_sass`.`franchise_role_data_dowmload` frd
        left join `xgwc_sass`.`franchise_role` fr ON fr.role_id = frd.role_id
        WHERE fr.status = 0 and frd.role_id IN
        <foreach item="roleId" collection="roleIds" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        ORDER BY download_limit DESC
        LIMIT 1
    </select>

    <select id="findFranchiseStaffListDropDownByFranchiseId"
            resultType="com.xgwc.user.entity.dto.FranchiseStaffSimpleDto">
        select fs.id,
               fs.name,
               fs.stage_name,
               fs.dept_id,
               fd.dept_name,
               fs.post_id,
               s.station_name post_name,
               fs.role_ids,
               fs.job_nature,
               fs.status,
               fs.bind_user_id,
               fs.is_principal
        from xgwc_franchise_staff fs
        left join franchise_dept fd on fs.dept_id = fd.dept_id
        left join franchise_station s on fs.post_id = s.station_id
        <where>
             fs.is_del = 0
            and fs.bind_status = 1
            and fs.status = 0
            <if test="franchiseId != null">
                and fs.franchise_id = #{franchiseId}
            </if>
            <if test="deptId != null">
                and fs.dept_id = #{deptId}
            </if>
        </where>
    </select>
    <select id="selectAdmin" resultType="java.lang.Boolean">
        select count(1) > 0
        from sys_user_middle sum1
        where sum1.id = #{userId} and sum1.status = 0 and sum1.user_type = 2
    </select>
    <select id="selectFranchiseStaffDownload" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        SELECT
        fsd.id,
        fsd.staff_id,
        fsd.brand_id,
        bo.company_name as brandName,
        fsd.download_limit,
        fsd.create_time,
        fsd.update_time,
        fsd.update_by
        FROM xgwc_franchise_staff_dl_limit fsd
            left join xgwc_brand_owner bo on bo.brand_id = fsd.brand_id
        <where>
            staff_id = #{id}
            <if test="brandId != null">
                and brand_id = #{brandId}
            </if>
        </where>
    </select>

    <select id="selectDownloadCount" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        SELECT
        fsd.id,
        fsd.staff_id,
        fsd.brand_id,
        fsd.download_count
        FROM xgwc_franchise_staff_dl_count fsd
        left join xgwc_brand_owner bo on bo.brand_id = fsd.brand_id
        <where>
            staff_id = #{staffId}
            <if test="brandId != null">
                and brand_id = #{brandId}
            </if>
        </where>
    </select>

    <select id="getFirstLevelDirectorStaffs" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        SELECT
            t.*
        FROM
            franchise_dept t
                LEFT JOIN xgwc_franchise_staff s ON s.dept_id = t.dept_id
        WHERE
            t.franchise_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
          AND t.`status` = 0
          AND t.is_del = 0
          AND t.pid = 0
          AND s.is_principal = 0
    </select>

    <select id="getDeptDirectorStaffs" resultType="com.xgwc.user.entity.dto.FranchiseStaffDto">
        SELECT
        t.*
        FROM
        franchise_dept t
        LEFT JOIN xgwc_franchise_staff s ON s.dept_id = t.dept_id
        WHERE
        t.dept_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND t.`status` = 0
        AND t.is_del = 0
        AND t.pid = 0
        AND s.is_principal = 0
    </select>

    <select id="selectStaffByDeptIdsAndFranchiseId" resultType="com.xgwc.user.entity.dto.FranchiseStaffSimpleDto">
        select t.id, t.name, t.stage_name
        from xgwc_franchise_staff t
        where t.`status` in (0, 2)
        and t.is_del = 0
        and t.dept_id in
        <foreach item="id" collection="deptIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        and d.franchise_id = #{franchiseId}
    </select>

</mapper>