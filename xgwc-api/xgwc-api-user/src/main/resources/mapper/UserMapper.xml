<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.UserMapper">

    <select id="getUserInfoByUserName" resultType="com.xgwc.common.entity.SysUser">
        select user_id mainUserId, user_name userName, password, status, user_type, brand_id
        from sys_user where user_name = #{userName} and status = 0
    </select>

    <select id="getUserInfoByUserId" resultType="com.xgwc.common.entity.SysUser">
        select user_id mainUserId, user_name userName, password, status, user_type, brand_id,phone,stage_name
        from sys_user where user_id = #{userId} and status = 0
    </select>

    <select id="findUserByPhone" resultType="com.xgwc.common.entity.SysUser">
        select user_id mainUserId, user_name userName,status,password,user_type
        from sys_user where phone = #{phone} and status = 0
    </select>

    <insert id="save" parameterType="com.xgwc.common.entity.SysUser">
        insert into sys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mainUserId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="password != null">password,</if>
            <if test="phone != null">phone,</if>
            <if test="status != null">status,</if>
            <if test="isDel != null">is_del,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="remark != null">remark,</if>
            <if test="wechatOpenid != null">wechat_openid,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="passwordModifyTime != null">password_modify_time,</if>
            <if test="userType != null">user_type,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="stageName != null">stage_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mainUserId != null">#{mainUserId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="password != null">#{password},</if>
            <if test="phone != null">#{phone},</if>
            <if test="status != null">#{status},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="wechatOpenid != null">#{wechatOpenid},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="passwordModifyTime != null">#{passwordModifyTime},</if>
            <if test="userType != null">#{userType},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="stageName != null">#{stageName},</if>
        </trim>
    </insert>


    <sql id="selectSysUserVo">
        select user_id , user_name, phone, status, is_del, login_ip, login_date, remark, wechat_openid, create_by
             , create_time, update_by, update_time, modify_time, password_modify_time,tenant_id,user_type,brand_id,stage_name from sys_user
    </sql>

    <select id="selectSysUserList" parameterType="com.xgwc.user.entity.vo.SysUserQueryVo" resultType="com.xgwc.user.entity.dto.SysUserDto">
        <include refid="selectSysUserVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDel == null "> and is_del = 0</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="loginIp != null  and loginIp != ''"> and login_ip like concat('%', #{loginIp}, '%')</if>
            <if test="loginDate != null "> and login_date between #{loginDate}[0] and #{loginDate}[1] </if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="wechatOpenid != null  and wechatOpenid != ''"> and wechat_openid like concat('%', #{wechatOpenid}, '%')</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time between #{createTime}[0] and #{createTime}[1] </if>
            <if test="updateBy != null "> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time between #{updateTime}[0] and #{updateTime}[1] </if>
            <if test="modifyTime != null "> and modify_time between #{modifyTime}[0] and #{modifyTime}[1] </if>
            <if test="passwordModifyTime != null "> and password_modify_time between #{passwordModifyTime}[0] and #{passwordModifyTime}[1] </if>
        </where>
    </select>

    <select id="selectSysUserByUserId" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysUserDto">
        <include refid="selectSysUserVo"/>
        where user_id = #{userId}
    </select>

    <select id="findUserByStageName" resultType="com.xgwc.common.entity.SysUser">
        <include refid="selectSysUserVo"/>
        where stage_name = #{stageName}
    </select>

    <update id="updateSysUser" parameterType="com.xgwc.common.entity.SysUser">
        update sys_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="password != null">password = #{password},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDel != null"> is_del = #{isDel},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="wechatOpenid != null">wechat_openid = #{wechatOpenid},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="passwordModifyTime != null">password_modify_time = #{passwordModifyTime},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="userType != null">user_type = #{userType},</if>
        </trim>
        where user_id = #{mainUserId}
    </update>

    <update id="deleteSysUserByUserId" parameterType="Long">
        update sys_user set is_del = 1 where user_id = #{userId}
    </update>

    <update id="deleteSysUserByUserIds" parameterType="String">
        update sys_user set is_del = 1 where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>
</mapper>