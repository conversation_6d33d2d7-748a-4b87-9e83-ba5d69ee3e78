<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysDictDataMapper">
    

    <sql id="selectSysDictDataVo">
        SELECT
            create_by,
            create_time,
            css_class,
            dict_code,
            dict_label,
            dict_sort,
            dict_type,
            dict_value,
            is_default,
            is_del,
            list_class,
            modify_time,
            parent_dict_code,
            remark,
            status,
            update_by,
            update_time,
            brand_id
        FROM
            sys_dict_data
    </sql>

    <select id="selectSysDictDataList" parameterType="com.xgwc.user.entity.vo.SysDictDataQueryVo" resultType="com.xgwc.user.entity.dto.SysDictDataDto">
        <include refid="selectSysDictDataVo"/>
        <where>
           is_del = 0
            <if test="dictCode != null "> and dict_code = #{dictCode}</if>
            <if test="parentDictCode != null and parentDictCode != '' "> and parent_dict_code = #{parentDictCode}</if>
            <if test="dictType != null  and dictType != ''"> and dict_type = #{dictType}</if>
            <if test="dictValue != null  and dictValue != ''"> and dict_value like concat('%', #{dictValue}, '%')</if>
            <if test="status != null  and status != ''"> and status like concat('%', #{status}, '%')</if>
            <if test="brandId != null  and brandId != ''"> and brand_id = #{brandId}</if>
        </where>
        order by dict_sort
    </select>
    <select id="selectSysDictDataByType" resultType="com.xgwc.user.entity.dto.SysDictDataDto">
        <include refid="selectSysDictDataVo"/>
        where
        is_del = 0
        and dict_type = #{dictType}
        <if test="brandId != null" >
            and brand_id = #{brandId}
        </if>
        order by dict_sort
    </select>


    <select id="selectSysDictDataByDictCode" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysDictDataDto">
        <include refid="selectSysDictDataVo"/>
        where dict_code = #{dictCode}
    </select>

    <insert id="insertSysDictData" parameterType="com.xgwc.user.entity.SysDictData">
        insert into sys_dict_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="cssClass != null">css_class,</if>
            <if test="dictCode != null">dict_code,</if>
            <if test="dictLabel != null">dict_label,</if>
            <if test="dictSort != null">dict_sort,</if>
            <if test="dictType != null">dict_type,</if>
            <if test="dictValue != null">dict_value,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="isDel != null">is_del,</if>
            <if test="listClass != null">list_class,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="parentDictCode != null">parent_dict_code,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="brandId != null">brand_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="cssClass != null">#{cssClass},</if>
            <if test="dictCode != null">#{dictCode},</if>
            <if test="dictLabel != null">#{dictLabel},</if>
            <if test="dictSort != null">#{dictSort},</if>
            <if test="dictType != null">#{dictType},</if>
            <if test="dictValue != null">#{dictValue},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="listClass != null">#{listClass},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="parentDictCode != null">#{parentDictCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="brandId != null">#{brandId},</if>
         </trim>
    </insert>

    <update id="updateSysDictData" parameterType="com.xgwc.user.entity.SysDictData">
        update sys_dict_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="cssClass != null">css_class = #{cssClass},</if>
            <if test="dictLabel != null">dict_label = #{dictLabel},</if>
            <if test="dictSort != null">dict_sort = #{dictSort},</if>
            <if test="dictType != null">dict_type = #{dictType},</if>
            <if test="dictValue != null">dict_value = #{dictValue},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="listClass != null">list_class = #{listClass},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="parentDictCode != null">parent_dict_code = #{parentDictCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
        </trim>
        where dict_code = #{dictCode}
    </update>
    <update id="updateSysDictDataByDictType" parameterType="java.lang.String">
        update sys_dict_data set dict_type = #{dictType}
        where dict_type = #{oldDictType}
    </update>



    <update id="updateStatus" >
        update sys_dict_data
        set status = #{status}, modify_time = now()
        where dict_code in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteSysDictDataByDictCode" parameterType="Long">
        update sys_dict_data set is_del = 1 where dict_code = #{dictCode}
    </update>

    <update id="deleteSysDictDataByDictCodes" parameterType="String">
        update sys_dict_data set is_del = 1 where dict_code in
        <foreach item="dictCode" collection="array" open="(" separator="," close=")">
            #{dictCode}
        </foreach>
    </update>
</mapper>