<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.ApplyRecordMapper">
    

    <sql id="selectApplyRecordVo">
        select brand_id, business_id, business_type, check_status, create_by, create_time, id, modify_time, update_by, update_time, user_id from xgwc_apply_record
    </sql>

    <select id="selectApplyRecordList" parameterType="com.xgwc.user.entity.vo.ApplyRecordQueryVo" resultType="com.xgwc.user.entity.dto.ApplyRecordDto">
        <include refid="selectApplyRecordVo"/>
        <where>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="checkStatus != null "> and check_status = #{checkStatus}</if>
            <if test="createBy != null  and createBy != ''"> and create_by like concat('%', #{createBy}, '%')</if>
            <if test="createTime != null "> and create_time between #{createTime}[0] and #{createTime}[1] </if>
            <if test="id != null "> and id = #{id}</if>
            <if test="modifyTime != null "> and modify_time between #{modifyTime}[0] and #{modifyTime}[1] </if>
            <if test="updateBy != null  and updateBy != ''"> and update_by like concat('%', #{updateBy}, '%')</if>
            <if test="updateTime != null "> and update_time between #{updateTime}[0] and #{updateTime}[1] </if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>
    
    <select id="selectApplyRecordById" parameterType="Long" resultType="com.xgwc.user.entity.dto.ApplyRecordDto">
        <include refid="selectApplyRecordVo"/>
        where id = #{id}
    </select>

    <select id="findApplyRecordByUserId" resultType="com.xgwc.user.entity.dto.ApplyRecordDto">
        <include refid="selectApplyRecordVo"/>
        where check_status != 3
        and user_id = #{userId}
        and business_id = #{businessId}
        and brand_id = #{brandId}
    </select>

    <select id="findApplyRecordListByUserIdAndBusinessType"
            resultType="com.xgwc.user.entity.dto.ApplyRecordDto">
        <include refid="selectApplyRecordVo"/>
        where check_status != 3
        and user_id = #{userId}
        and business_type = #{businessType}

    </select>

    <insert id="insertApplyRecord" parameterType="com.xgwc.user.entity.ApplyRecord">
        insert into xgwc_apply_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brandId != null">brand_id,</if>
            <if test="businessId != null">business_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="id != null">id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brandId != null">#{brandId},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="id != null">#{id},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>

    <update id="updateApplyRecord" parameterType="com.xgwc.user.entity.ApplyRecord">
        update xgwc_apply_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteApplyRecordById" parameterType="Long">
        update xgwc_apply_record set is_del = 1 where id = #{id}
    </update>

    <update id="deleteApplyRecordByIds" parameterType="String">
        update xgwc_apply_record set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateApplyRecordStatusById">
        update xgwc_apply_record set check_status = #{status} where id = #{id}
    </update>
</mapper>