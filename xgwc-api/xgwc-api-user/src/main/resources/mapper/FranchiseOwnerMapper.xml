<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseOwnerMapper">

    <sql id="selectFranchiseOwnerVo">
        select id, business_license, company_name, company_simple_name, license_code, license_is_longterm, license_start, license_end, operate_province,
               operate_city, operate_region, operate_address, scale, idcard_front, idcard_back, idcard_name, idcard_no, idcard_is_longterm, idcard_start,
               idcard_end, idcard_phone, idcard_email, bank_user_name, bank_name, bank_no, manager_name, manager_phone, check_status, check_time, status,
               download_limit, is_configured,
               is_del, reason, create_by, create_time, update_by, update_time, modify_time,shop_ids,brand_id,manager_user_id,franchise_id
        from franchise_owner
    </sql>

    <select id="selectFranchiseOwnerList" parameterType="com.xgwc.user.entity.dto.FranchiseOwnerQueryDto" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        SELECT
            t.id,
            t.business_license,
            t.company_name,
            t.company_simple_name,
            t.license_code,
            t.license_is_longterm,
            t.license_start,
            t.license_end,
            t.operate_province,
            t.operate_city,
            t.operate_region,
            t.operate_address,
            t.scale,
            t.idcard_front,
            t.idcard_back,
            t.idcard_name,
            t.idcard_no,
            t.idcard_is_longterm,
            t.idcard_start,
            t.idcard_end,
            t.idcard_phone,
            t.idcard_email,
            t.bank_user_name,
            t.bank_name,
            t.bank_no,
            t.manager_name,
            t.manager_phone,
            t.check_status,
            t.check_time,
            t.status,
            t.download_limit,
            t.is_configured,
            t.is_del,
            t.reason,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.modify_time,
            t.shop_ids,
            t.brand_id,
            t.manager_user_id,
            t.franchise_id,
            ( SELECT GROUP_CONCAT(s.shop_name SEPARATOR '/') FROM xgwc_shop s WHERE s.franchise_owner_id = t.id) AS shopName
        FROM franchise_owner t
        WHERE t.check_status != 3
        <if test="id != null "> AND t.id = #{id}</if>
        <if test="companyName != null  and companyName != ''"> AND t.company_name LIKE CONCAT('%', #{companyName}, '%')</if>
        <if test="companySimpleName != null  and companySimpleName != ''"> AND t.company_simple_name LIKE CONCAT('%', #{companySimpleName}, '%')</if>
        <if test="managerName != null  and managerName != ''"> AND t.manager_name LIKE CONCAT('%', #{managerName}, '%')</if>
        <if test="managerPhone != null  and managerPhone != ''"> AND t.manager_phone LIKE CONCAT('%', #{managerPhone}, '%')</if>
        <if test="checkStatus != null "> AND t.check_status = #{checkStatus}</if>
        <if test="checkTime != null "> AND t.check_time BETWEEN #{checkTime}[0] AND #{checkTime}[1] </if>
        <if test="status != null "> AND t.status = #{status}</if>
        <if test="isDel != null "> AND t.is_del = #{isDel}</if>
        <if test="createTime != null "> AND t.create_time BETWEEN #{createTime}[0] AND #{createTime}[1] </if>
        <if test="brandId != null "> AND t.brand_id = #{brandId}</if>
        ORDER BY t.create_time DESC
    </select>
    
    <select id="selectFranchiseOwnerById" parameterType="Long" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        SELECT
            t.id,
            t.business_license,
            t.company_name,
            t.company_simple_name,
            t.license_code,
            t.license_is_longterm,
            t.license_start,
            t.license_end,
            t.operate_province,
            t.operate_city,
            t.operate_region,
            t.operate_address,
            t.scale,
            t.idcard_front,
            t.idcard_back,
            t.idcard_name,
            t.idcard_no,
            t.idcard_is_longterm,
            t.idcard_start,
            t.idcard_end,
            t.idcard_phone,
            t.idcard_email,
            t.bank_user_name,
            t.bank_name,
            t.bank_no,
            t.manager_name,
            t.manager_phone,
            t.check_status,
            t.check_time,
            t.status,
            t.download_limit,
            t.is_configured,
            t.is_del,
            t.reason,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.modify_time,
            t.shop_ids,
            t.brand_id,
            t.manager_user_id,
            t.franchise_id,
            f.franchise_name as franchiseName,
            b.company_name as brandName
        FROM franchise_owner t
        left join franchise f on f.id = t.franchise_id
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.id = #{id}
    </select>

    <select id="findFranchiseOwnerByManagerUserIdAndBrandIdList" resultType="com.xgwc.user.entity.vo.ApplyInfoVO">
        select t.id,t.company_name,b.company_name as brandName
        from franchise_owner t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.check_status != 3
        and t.manager_user_id = #{managerUserId}
        and t.brand_id != #{brandId}
    </select>

    <select id="findFranchiseOwnerByManagerPhone" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        <include refid="selectFranchiseOwnerVo"/>
        where manager_phone = #{managerPhone}
        and brand_id = #{brandId}
    </select>

    <select id="findFranchiseOwnerByManagerUserIdAndBrandId" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        select t.*,b.company_name as brandName
        from franchise_owner t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.check_status != 3
        and t.manager_user_id = #{managerUserId}
        and t.brand_id = #{brandId}
    </select>

    <select id="findMyApplyList" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        select t.*,t.company_name,b.company_name as brandName
        from franchise_owner t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.manager_user_id = #{managerUserId}
    </select>

    <insert id="insertFranchiseOwner" parameterType="com.xgwc.user.entity.FranchiseOwner" useGeneratedKeys="true" keyProperty="id">
        insert into franchise_owner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="companyName != null">company_name,</if>
            <if test="companySimpleName != null">company_simple_name,</if>
            <if test="licenseCode != null">license_code,</if>
            <if test="licenseIsLongterm != null">license_is_longterm,</if>
            <if test="licenseStart != null">license_start,</if>
            <if test="licenseEnd != null">license_end,</if>
            <if test="operateProvince != null">operate_province,</if>
            <if test="operateCity != null">operate_city,</if>
            <if test="operateRegion != null">operate_region,</if>
            <if test="operateAddress != null">operate_address,</if>
            <if test="scale != null">scale,</if>
            <if test="idcardFront != null">idcard_front,</if>
            <if test="idcardBack != null">idcard_back,</if>
            <if test="idcardName != null">idcard_name,</if>
            <if test="idcardNo != null">idcard_no,</if>
            <if test="idcardIsLongterm != null">idcard_is_longterm,</if>
            <if test="idcardStart != null">idcard_start,</if>
            <if test="idcardEnd != null">idcard_end,</if>
            <if test="idcardPhone != null">idcard_phone,</if>
            <if test="idcardEmail != null">idcard_email,</if>
            <if test="bankUserName != null">bank_user_name,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="bankNo != null">bank_no,</if>
            <if test="managerName != null">manager_name,</if>
            <if test="managerPhone != null">manager_phone,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="status != null">status,</if>
            <if test="isDel != null">is_del,</if>
            <if test="reason != null">reason,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="managerUserId != null">manager_user_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="franchiseId != null">franchise_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companySimpleName != null">#{companySimpleName},</if>
            <if test="licenseCode != null">#{licenseCode},</if>
            <if test="licenseIsLongterm != null">#{licenseIsLongterm},</if>
            <if test="licenseStart != null">#{licenseStart},</if>
            <if test="licenseEnd != null">#{licenseEnd},</if>
            <if test="operateProvince != null">#{operateProvince},</if>
            <if test="operateCity != null">#{operateCity},</if>
            <if test="operateRegion != null">#{operateRegion},</if>
            <if test="operateAddress != null">#{operateAddress},</if>
            <if test="scale != null">#{scale},</if>
            <if test="idcardFront != null">#{idcardFront},</if>
            <if test="idcardBack != null">#{idcardBack},</if>
            <if test="idcardName != null">#{idcardName},</if>
            <if test="idcardNo != null">#{idcardNo},</if>
            <if test="idcardIsLongterm != null">#{idcardIsLongterm},</if>
            <if test="idcardStart != null">#{idcardStart},</if>
            <if test="idcardEnd != null">#{idcardEnd},</if>
            <if test="idcardPhone != null">#{idcardPhone},</if>
            <if test="idcardEmail != null">#{idcardEmail},</if>
            <if test="bankUserName != null">#{bankUserName},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="bankNo != null">#{bankNo},</if>
            <if test="managerName != null">#{managerName},</if>
            <if test="managerPhone != null">#{managerPhone},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="status != null">#{status},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="managerUserId != null">#{managerUserId},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="franchiseId != null">#{franchiseId},</if>
         </trim>
        <selectKey keyProperty="id" resultType="long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <update id="updateFranchiseOwner" parameterType="com.xgwc.user.entity.FranchiseOwner">
        update franchise_owner
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessLicense != null">business_license = #{businessLicense},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companySimpleName != null">company_simple_name = #{companySimpleName},</if>
            <if test="licenseCode != null">license_code = #{licenseCode},</if>
            <if test="licenseIsLongterm != null">license_is_longterm = #{licenseIsLongterm},</if>
            <if test="licenseStart != null">license_start = #{licenseStart},</if>
            <if test="licenseEnd != null">license_end = #{licenseEnd},</if>
            <if test="operateProvince != null">operate_province = #{operateProvince},</if>
            <if test="operateCity != null">operate_city = #{operateCity},</if>
            <if test="operateRegion != null">operate_region = #{operateRegion},</if>
            <if test="operateAddress != null">operate_address = #{operateAddress},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="idcardFront != null">idcard_front = #{idcardFront},</if>
            <if test="idcardBack != null">idcard_back = #{idcardBack},</if>
            <if test="idcardName != null">idcard_name = #{idcardName},</if>
            <if test="idcardNo != null">idcard_no = #{idcardNo},</if>
            <if test="idcardIsLongterm != null">idcard_is_longterm = #{idcardIsLongterm},</if>
            <if test="idcardStart != null">idcard_start = #{idcardStart},</if>
            <if test="idcardEnd != null">idcard_end = #{idcardEnd},</if>
            <if test="idcardPhone != null">idcard_phone = #{idcardPhone},</if>
            <if test="idcardEmail != null">idcard_email = #{idcardEmail},</if>
            <if test="bankUserName != null">bank_user_name = #{bankUserName},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="bankNo != null">bank_no = #{bankNo},</if>
            <if test="managerName != null">manager_name = #{managerName},</if>
            <if test="managerPhone != null">manager_phone = #{managerPhone},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="downloadLimit != null">download_limit = #{downloadLimit},</if>
            <if test="isConfigured != null">is_configured = #{isConfigured},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="shopIds != null">shop_ids = #{shopIds},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFranchiseOwnerById" parameterType="Long">
        delete from franchise_owner where id = #{id}
    </delete>

    <delete id="deleteFranchiseOwnerByIds" parameterType="String">
        delete from franchise_owner where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getFranchiseOwnerVOByUserId" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        <include refid="selectFranchiseOwnerVo"/>
        where manager_user_id = #{userId} and is_del = 0
    </select>

    <select id="findFranchiseOwnerListByBrandId" resultType="com.xgwc.user.entity.vo.FranchiseOwnerSimpleVO">
        SELECT
            f.id AS id,
            ifnull( t.company_simple_name, t.company_name ) AS companyName,
            t.id AS franchiseOwnerId
        FROM
            franchise_owner t
                LEFT JOIN franchise f ON f.id = t.franchise_id
        WHERE
            t.check_status = 1
          AND t.is_del = 0
          AND t.brand_id = #{brandId}
    </select>
    <select id="selectFranchiseDownloadLimit" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        SELECT
            id,
            company_simple_name,
            download_limit,
            is_configured
        FROM franchise_owner
        <where>
            <if test="franchiseOwnerQueryDto.brandId != null">
                and brand_id = #{franchiseOwnerQueryDto.brandId}
            </if>
            <if test="franchiseOwnerQueryDto.companySimpleName != null and franchiseOwnerQueryDto.companySimpleName != ''">
                and company_simple_name = #{franchiseOwnerQueryDto.companySimpleName}
            </if>
            <if test="franchiseOwnerQueryDto.isConfigured != null">
                and is_configured = #{franchiseOwnerQueryDto.isConfigured}
            </if>
        </where>
    </select>

    <select id="selectMyFranchiseOwnerApplyList" parameterType="com.xgwc.user.entity.dto.MyFranchiseOwnerQueryDto"
            resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        SELECT t.*
               ,b.company_name AS brandName
               ,( SELECT GROUP_CONCAT(s.shop_name SEPARATOR '/')
                FROM xgwc_shop s
                WHERE s.franchise_owner_id = t.id
                ) AS shopName
        FROM franchise_owner t
        LEFT JOIN xgwc_brand_owner b ON b.brand_id = t.brand_id
        where t.franchise_id = #{franchiseId}
        <if test="status != null "> and t.status = #{status}</if>
        <if test="brandId != null "> and t.brand_id = #{brandId}</if>
        order by t.create_time desc
    </select>

    <select id="findFranchiseOwnerByFranchiseIdAndBrandId"
            resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        select t.*,b.company_name as brandName
        from franchise_owner t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.franchise_id = #{franchiseId}
        and t.brand_id = #{brandId}
    </select>

    <select id="findFranchiseOwnerByFranchiseId" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        select t.*,b.company_name as brandName
        from franchise_owner t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.franchise_id = #{franchiseId}
    </select>
    <select id="selectFranchiseInfoByFranchiseId" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        select t.download_limit as downloadLimit,b.company_name as brandName,b.brand_id as brandId
        from franchise_owner t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.franchise_id = #{franchiseId}
        ORDER BY
            t.create_time
    </select>
    <select id="selectBrandNameAndFranchiseName" resultType="com.xgwc.user.entity.vo.FranchiseOwnerVO">
        select t.franchise_id as franchiseId,
               CONCAT(
                       b.company_name,
                       '-',
                       ifnull(t.company_simple_name, t.company_name)
               ) AS companyName
        from franchise_owner t
        left join xgwc_brand_owner b on b.brand_id = t.brand_id
        where t.brand_id = #{brandId} and check_status = 1
        and t.is_del = 0
        ORDER BY
            t.create_time
    </select>

    <select id="findCooperateFranchiseByServiceId" resultType="com.xgwc.user.entity.vo.FranchiseOwnerSimpleVO">
        select
            s.franchise_id as id,
            concat(f.franchise_name, '（', group_concat(s.shop_name separator '，'), '）') as companyName
        from xgwc_shop s
        left join franchise f on f.id = s.franchise_id
        where s.franchise_id in (
            select c.franchise_id
            from xgwc_market_cooperative c
            where c.service_owner_id = #{serviceId} and c.status = 0
        )
        group by s.franchise_id
    </select>
</mapper>