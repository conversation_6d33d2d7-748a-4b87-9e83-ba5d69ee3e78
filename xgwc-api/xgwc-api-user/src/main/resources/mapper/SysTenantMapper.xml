<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.SysTenantMapper">
    

    <sql id="selectSysTenantVo">
        select id, name, contact_user_id, contact_name, contact_mobile, status, website, package_id, expire_time, account_count, creator, create_time, updater, update_time from sys_tenant
    </sql>

    <select id="selectSysTenantList" parameterType="com.xgwc.user.entity.vo.SysTenantQueryVo" resultType="com.xgwc.user.entity.dto.SysTenantDto">
        <include refid="selectSysTenantVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="contactUserId != null "> and contact_user_id = #{contactUserId}</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactMobile != null  and contactMobile != ''"> and contact_mobile like concat('%', #{contactMobile}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="website != null  and website != ''"> and website like concat('%', #{website}, '%')</if>
            <if test="packageId != null "> and package_id = #{packageId}</if>
            <if test="expireTime != null "> and expire_time between #{expireTime}[0] and #{expireTime}[1] </if>
            <if test="accountCount != null "> and account_count = #{accountCount}</if>
            <if test="creator != null  and creator != ''"> and creator like concat('%', #{creator}, '%')</if>
            <if test="createTime != null "> and create_time between #{createTime}[0] and #{createTime}[1] </if>
            <if test="updater != null  and updater != ''"> and updater like concat('%', #{updater}, '%')</if>
            <if test="updateTime != null "> and update_time between #{updateTime}[0] and #{updateTime}[1] </if>
        </where>
    </select>
    
    <select id="selectSysTenantById" parameterType="Long" resultType="com.xgwc.user.entity.dto.SysTenantDto">
        <include refid="selectSysTenantVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysTenant" parameterType="com.xgwc.user.entity.SysTenant">
        insert into sys_tenant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="contactUserId != null">contact_user_id,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactMobile != null">contact_mobile,</if>
            <if test="status != null">status,</if>
            <if test="website != null">website,</if>
            <if test="packageId != null">package_id,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="accountCount != null">account_count,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="contactUserId != null">#{contactUserId},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactMobile != null">#{contactMobile},</if>
            <if test="status != null">#{status},</if>
            <if test="website != null">#{website},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="accountCount != null">#{accountCount},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
        <selectKey keyProperty="id" resultType="long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <update id="updateSysTenant" parameterType="com.xgwc.user.entity.SysTenant">
        update sys_tenant
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="contactUserId != null">contact_user_id = #{contactUserId},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactMobile != null">contact_mobile = #{contactMobile},</if>
            <if test="status != null">status = #{status},</if>
            <if test="website != null">website = #{website},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="accountCount != null">account_count = #{accountCount},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysTenantById" parameterType="Long">
        delete from sys_tenant where id = #{id}
    </delete>

    <delete id="deleteSysTenantByIds" parameterType="String">
        delete from sys_tenant where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>