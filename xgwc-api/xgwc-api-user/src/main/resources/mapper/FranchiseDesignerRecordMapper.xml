<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.FranchiseDesignerRecordMapper">
    

    <sql id="selectFranchiseDesignerRecordVo">
        select brand_id, business_id, business_type, check_status, check_time, create_by, create_time, id, modify_time, reason, update_by, update_time from xgwc_franchise_designer_record
    </sql>

    <select id="selectFranchiseDesignerRecordList" parameterType="com.xgwc.user.entity.vo.FranchiseDesignerRecordQueryVo" resultType="com.xgwc.user.entity.dto.FranchiseDesignerRecordDto">
        <include refid="selectFranchiseDesignerRecordVo"/>
        <where>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="checkStatus != null "> and check_status = #{checkStatus}</if>
            <if test="checkTime != null "> and check_time between #{checkTime}[0] and #{checkTime}[1] </if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time between #{createTime}[0] and #{createTime}[1] </if>
            <if test="id != null "> and id = #{id}</if>
            <if test="modifyTime != null "> and modify_time between #{modifyTime}[0] and #{modifyTime}[1] </if>
            <if test="reason != null  and reason != ''"> and reason like concat('%', #{reason}, '%')</if>
            <if test="updateBy != null "> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time between #{updateTime}[0] and #{updateTime}[1] </if>
        </where>
    </select>
    
    <select id="selectFranchiseDesignerRecordById" parameterType="Long" resultType="com.xgwc.user.entity.dto.FranchiseDesignerRecordDto">
        <include refid="selectFranchiseDesignerRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectFranchiseDesignerRecordListByBusinessId"
            resultType="com.xgwc.user.entity.dto.FranchiseDesignerRecordDto">
        <include refid="selectFranchiseDesignerRecordVo"/>
        where business_id = #{businessId} and business_type = #{businessType}
    </select>

    <insert id="insertFranchiseDesignerRecord" parameterType="com.xgwc.user.entity.FranchiseDesignerRecord">
        insert into xgwc_franchise_designer_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brandId != null">brand_id,</if>
            <if test="businessId != null">business_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="id != null">id,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="reason != null">reason,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brandId != null">#{brandId},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="id != null">#{id},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="reason != null">#{reason},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFranchiseDesignerRecord" parameterType="com.xgwc.user.entity.FranchiseDesignerRecord">
        update xgwc_franchise_designer_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteFranchiseDesignerRecordById" parameterType="Long">
        update xgwc_franchise_designer_record set is_del = 1 where id = #{id}
    </update>

    <update id="deleteFranchiseDesignerRecordByIds" parameterType="String">
        update xgwc_franchise_designer_record set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>