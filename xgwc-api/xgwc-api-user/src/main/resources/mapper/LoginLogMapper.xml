<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.LoginLogMapper">

    <insert id="insertLoginLog" useGeneratedKeys="true" keyColumn="id">
        insert into sys_login_log(user_id, login_param, login_type, ip, browser, create_time, update_time)
        values (#{userId}, #{loginParam}, #{loginType}, #{ip}, #{browser}, now(), now())
    </insert>
</mapper>