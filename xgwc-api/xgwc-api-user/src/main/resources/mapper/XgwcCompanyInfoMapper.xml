<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.user.dao.XgwcCompanyInfoMapper">

    <insert id="saveXgwcCompanyInfo" parameterType="com.xgwc.user.entity.XgwcCompanyInfo" useGeneratedKeys="true" keyProperty="id">
        insert into xgwc_company_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xgwcCompanyInfoVo.franchiseId != null">franchise_id,</if>
            <if test="xgwcCompanyInfoVo.brandId != null">brand_id,</if>
            <if test="xgwcCompanyInfoVo.businessLicense != null">business_license,</if>
            <if test="xgwcCompanyInfoVo.companyName != null">company_name,</if>
            <if test="xgwcCompanyInfoVo.companySimpleName != null">company_simple_name,</if>
            <if test="xgwcCompanyInfoVo.licenseCode != null">license_code,</if>
            <if test="xgwcCompanyInfoVo.licenseIsLongterm != null">license_is_longterm,</if>
            <if test="xgwcCompanyInfoVo.licenseStart != null">license_start,</if>
            <if test="xgwcCompanyInfoVo.licenseEnd != null">license_end,</if>
            <if test="xgwcCompanyInfoVo.operateProvince != null">operate_province,</if>
            <if test="xgwcCompanyInfoVo.operateCity != null">operate_city,</if>
            <if test="xgwcCompanyInfoVo.operateRegion != null">operate_region,</if>
            <if test="xgwcCompanyInfoVo.operateAddress != null">operate_address,</if>
            <if test="xgwcCompanyInfoVo.scale != null">`scale`,</if>
            <if test="xgwcCompanyInfoVo.idcardFront != null">`idcard_front`,</if>
            <if test="xgwcCompanyInfoVo.idcardBack != null">`idcard_back`,</if>
            <if test="xgwcCompanyInfoVo.idcardName != null">idcard_name,</if>
            <if test="xgwcCompanyInfoVo.idcardNo != null">idcard_no,</if>
            <if test="xgwcCompanyInfoVo.idcardIsLongterm != null">idcard_is_longterm,</if>
            <if test="xgwcCompanyInfoVo.idcardStart != null">idcard_start,</if>
            <if test="xgwcCompanyInfoVo.idcardEnd != null">idcard_end,</if>
            <if test="xgwcCompanyInfoVo.idcardPhone != null">idcard_phone,</if>
            <if test="xgwcCompanyInfoVo.idcardEmail != null">idcard_email,</if>
            <if test="xgwcCompanyInfoVo.bankUserName != null">bank_user_name,</if>
            <if test="xgwcCompanyInfoVo.bankName != null">bank_name,</if>
            <if test="xgwcCompanyInfoVo.bankNo != null">bank_no,</if>
            <if test="xgwcCompanyInfoVo.createBy != null">create_by,</if>
            <if test="xgwcCompanyInfoVo.isFlag != null">is_flag,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xgwcCompanyInfoVo.franchiseId != null">#{xgwcCompanyInfoVo.franchiseId},</if>
            <if test="xgwcCompanyInfoVo.brandId != null">#{xgwcCompanyInfoVo.brandId},</if>
            <if test="xgwcCompanyInfoVo.businessLicense != null">#{xgwcCompanyInfoVo.businessLicense},</if>
            <if test="xgwcCompanyInfoVo.companyName != null">#{xgwcCompanyInfoVo.companyName},</if>
            <if test="xgwcCompanyInfoVo.companySimpleName != null">#{xgwcCompanyInfoVo.companySimpleName},</if>
            <if test="xgwcCompanyInfoVo.licenseCode != null">#{xgwcCompanyInfoVo.licenseCode},</if>
            <if test="xgwcCompanyInfoVo.licenseIsLongterm != null">#{xgwcCompanyInfoVo.licenseIsLongterm},</if>
            <if test="xgwcCompanyInfoVo.licenseStart != null">#{xgwcCompanyInfoVo.licenseStart},</if>
            <if test="xgwcCompanyInfoVo.licenseEnd != null">#{xgwcCompanyInfoVo.licenseEnd},</if>
            <if test="xgwcCompanyInfoVo.operateProvince != null">#{xgwcCompanyInfoVo.operateProvince},</if>
            <if test="xgwcCompanyInfoVo.operateCity != null">#{xgwcCompanyInfoVo.operateCity},</if>
            <if test="xgwcCompanyInfoVo.operateRegion != null">#{xgwcCompanyInfoVo.operateRegion},</if>
            <if test="xgwcCompanyInfoVo.operateAddress != null">#{xgwcCompanyInfoVo.operateAddress},</if>
            <if test="xgwcCompanyInfoVo.scale != null">#{xgwcCompanyInfoVo.scale},</if>
            <if test="xgwcCompanyInfoVo.idcardFront != null">#{xgwcCompanyInfoVo.idcardFront},</if>
            <if test="xgwcCompanyInfoVo.idcardBack != null">#{xgwcCompanyInfoVo.idcardBack},</if>
            <if test="xgwcCompanyInfoVo.idcardName != null">#{xgwcCompanyInfoVo.idcardName},</if>
            <if test="xgwcCompanyInfoVo.idcardNo != null">#{xgwcCompanyInfoVo.idcardNo},</if>
            <if test="xgwcCompanyInfoVo.idcardIsLongterm != null">#{xgwcCompanyInfoVo.idcardIsLongterm},</if>
            <if test="xgwcCompanyInfoVo.idcardStart != null">#{xgwcCompanyInfoVo.idcardStart},</if>
            <if test="xgwcCompanyInfoVo.idcardEnd != null">#{xgwcCompanyInfoVo.idcardEnd},</if>
            <if test="xgwcCompanyInfoVo.idcardPhone != null">#{xgwcCompanyInfoVo.idcardPhone},</if>
            <if test="xgwcCompanyInfoVo.idcardEmail != null">#{xgwcCompanyInfoVo.idcardEmail},</if>
            <if test="xgwcCompanyInfoVo.bankUserName != null">#{xgwcCompanyInfoVo.bankUserName},</if>
            <if test="xgwcCompanyInfoVo.bankName != null">#{xgwcCompanyInfoVo.bankName},</if>
            <if test="xgwcCompanyInfoVo.bankNo != null">#{xgwcCompanyInfoVo.bankNo},</if>
            <if test="xgwcCompanyInfoVo.createBy != null">#{xgwcCompanyInfoVo.createBy},</if>
            <if test="xgwcCompanyInfoVo.isFlag != null">#{xgwcCompanyInfoVo.isFlag},</if>
            now()
        </trim>
    </insert>

    <update id="updateXgwcCompanyById">
        update xgwc_company_info
        <set>
            <if test="xgwcCompanyInfoVo.franchiseId != null">franchise_id = #{xgwcCompanyInfoVo.franchiseId},</if>
            <if test="xgwcCompanyInfoVo.brandId != null">brand_id = #{xgwcCompanyInfoVo.brandId},</if>
            <if test="xgwcCompanyInfoVo.businessLicense != null">business_license = #{xgwcCompanyInfoVo.businessLicense},</if>
            <if test="xgwcCompanyInfoVo.companyName != null">company_name = #{xgwcCompanyInfoVo.companyName},</if>
            <if test="xgwcCompanyInfoVo.companySimpleName != null">company_simple_name = #{xgwcCompanyInfoVo.companySimpleName},</if>
            <if test="xgwcCompanyInfoVo.licenseCode != null">license_code = #{xgwcCompanyInfoVo.licenseCode},</if>
            <if test="xgwcCompanyInfoVo.licenseIsLongterm != null">license_is_longterm = #{xgwcCompanyInfoVo.licenseIsLongterm},</if>
            <if test="xgwcCompanyInfoVo.licenseStart != null">license_start = #{xgwcCompanyInfoVo.licenseStart},</if>
            <if test="xgwcCompanyInfoVo.licenseEnd != null">license_end = #{xgwcCompanyInfoVo.licenseEnd},</if>
            <if test="xgwcCompanyInfoVo.operateProvince != null">operate_province = #{xgwcCompanyInfoVo.operateProvince},</if>
            <if test="xgwcCompanyInfoVo.operateCity != null">operate_city = #{xgwcCompanyInfoVo.operateCity},</if>
            <if test="xgwcCompanyInfoVo.operateRegion != null">operate_region = #{xgwcCompanyInfoVo.operateRegion},</if>
            <if test="xgwcCompanyInfoVo.operateAddress != null">operate_address = #{xgwcCompanyInfoVo.operateAddress},</if>
            <if test="xgwcCompanyInfoVo.scale != null">scale = #{xgwcCompanyInfoVo.scale},</if>
            <if test="xgwcCompanyInfoVo.idcardFront != null">idcard_front = #{xgwcCompanyInfoVo.idcardFront},</if>
            <if test="xgwcCompanyInfoVo.idcardBack != null">idcard_back = #{xgwcCompanyInfoVo.idcardBack},</if>
            <if test="xgwcCompanyInfoVo.idcardName != null">idcard_name = #{xgwcCompanyInfoVo.idcardName},</if>
            <if test="xgwcCompanyInfoVo.idcardNo != null">idcard_no = #{xgwcCompanyInfoVo.idcardNo},</if>
            <if test="xgwcCompanyInfoVo.idcardIsLongterm != null">idcard_is_longterm = #{xgwcCompanyInfoVo.idcardIsLongterm},</if>
            <if test="xgwcCompanyInfoVo.idcardStart != null">idcard_start = #{xgwcCompanyInfoVo.idcardStart},</if>
            <if test="xgwcCompanyInfoVo.idcardEnd != null">idcard_end = #{xgwcCompanyInfoVo.idcardEnd},</if>
            <if test="xgwcCompanyInfoVo.idcardPhone != null">idcard_phone = #{xgwcCompanyInfoVo.idcardPhone},</if>
            <if test="xgwcCompanyInfoVo.idcardEmail != null">idcard_email = #{xgwcCompanyInfoVo.idcardEmail},</if>
            <if test="xgwcCompanyInfoVo.bankUserName != null">bank_user_name = #{xgwcCompanyInfoVo.bankUserName},</if>
            <if test="xgwcCompanyInfoVo.bankName != null">bank_name = #{xgwcCompanyInfoVo.bankName},</if>
            <if test="xgwcCompanyInfoVo.bankNo != null">bank_no = #{xgwcCompanyInfoVo.bankNo},</if>
            <if test="xgwcCompanyInfoVo.updateBy != null">update_by = #{xgwcCompanyInfoVo.updateBy},</if>
            update_time = now()
        </set>
        where id = #{xgwcCompanyInfoVo.id}
    </update>
    <update id="updateStatusById">
        update xgwc_company_info
        set status = #{status}
        where id = #{companyId}
    </update>


    <select id="getCompanyInfoList" resultType="com.xgwc.user.entity.dto.XgwcCompanyInfoDto">
        SELECT
        xci.id,
        xci.company_name AS companyName,
        xci.company_simple_name AS companySimpleName,
        xci.idcard_name AS idcardName,
        GROUP_CONCAT(xs.shop_name SEPARATOR '/') AS shopName,
        ifnull(fo.company_simple_name, fo.company_name) AS franchiseName,
        xci.create_time AS createTime,
        xci.status,
        xci.is_flag AS isFlag
        FROM xgwc_company_info xci
        LEFT JOIN xgwc_company_shop xcs ON xci.id = xcs.company_id
        LEFT JOIN xgwc_shop xs ON xcs.shop_id = xs.shop_id
        LEFT JOIN franchise_owner fo ON xci.franchise_id = fo.franchise_id and xci.brand_id = fo.brand_id
        <where>
            xci.brand_id = #{xgwcCompanyInfoVo.brandId}

            <if test="xgwcCompanyInfoVo.companyName != null and xgwcCompanyInfoVo.companyName != ''">
                AND (xci.company_name = #{xgwcCompanyInfoVo.companyName}
                    OR xci.company_simple_name = #{xgwcCompanyInfoVo.companyName})
            </if>
            <if test="xgwcCompanyInfoVo.franchiseId != null">
                AND xci.franchise_id = #{xgwcCompanyInfoVo.franchiseId}
            </if>
            <if test="xgwcCompanyInfoVo.status != null">
                AND xci.status = #{xgwcCompanyInfoVo.status}
            </if>
        </where>
        GROUP BY
        id,
        companyName,
        companySimpleName,
        idcardName,
        franchiseName,
        createTime,
        status,
        isFlag
        ORDER BY
        xci.create_time DESC
    </select>
    <select id="getXgwcCompanyById" resultType="com.xgwc.user.entity.dto.XgwcCompanyInfoDto">
        SELECT
        xci.id,
        xci.franchise_id AS franchiseId,
        fo.company_name AS franchiseName,
        xci.company_name AS companyName,
        xci.company_simple_name AS companySimpleName,
        xci.business_license AS businessLicense,
        xci.license_code AS licenseCode,
        xci.license_is_longterm AS licenseIsLongterm,
        xci.license_start AS licenseStart,
        xci.license_end AS licenseEnd,
        xci.operate_province AS operateProvince,
        xci.operate_city AS operateCity,
        xci.operate_region AS operateRegion,
        xci.operate_address AS operateAddress,
        xci.scale AS scale,
        xci.idcard_front AS idcardFront,
        xci.idcard_back AS idcardBack,
        xci.idcard_name AS idcardName,
        xci.idcard_no AS idcardNo,
        xci.idcard_is_longterm AS idcardIsLongterm,
        xci.idcard_start AS idcardStart,
        xci.idcard_end AS idcardEnd,
        xci.idcard_phone AS idcardPhone,
        xci.idcard_email AS idcardEmail,
        xci.bank_user_name AS bankUserName,
        xci.bank_name AS bankName,
        xci.bank_no AS bankNo
        FROM xgwc_company_info xci
                 LEFT JOIN franchise_owner fo ON xci.franchise_id = fo.franchise_id and xci.brand_id = fo.brand_id
        WHERE xci.id = #{companyId} AND xci.brand_id = #{brandId}
    </select>
    <select id="selectDictData" resultType="com.xgwc.user.entity.SysDictData">
        SELECT
            dd.dict_code AS dictCode,
            dd.dict_label AS dictLabel
        FROM sys_dict_data dd
                 LEFT JOIN franchise_owner fo ON dd.brand_id = fo.brand_id
        WHERE dict_type = #{paymentCodeBody} and fo.franchise_id = #{franchiseId}
    </select>

</mapper>