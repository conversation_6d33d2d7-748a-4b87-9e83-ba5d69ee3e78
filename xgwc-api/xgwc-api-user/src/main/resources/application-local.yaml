management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
spring:
  cloud:
    nacos:
      discovery: # 【配置中心】配置项
        namespace: 6cc1ba75-2b9c-4ceb-8398-fc381a8d747b # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
  security:
    user:
      name: admin
      password: U4eQZbFLqdw9eGMC1aFq0mP4iJtIaFo8
      roles: ACTUATOR_ADMIN
  #连接池配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://*************:3306/xgwc_sass?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true # MySQL Connector/J 8.X 连接的示例
    username: root
    password: ",uPkdLhlC1,t"
  data:
    redis:
      host: *************
      port: 6379
      database: 0
      password: TqbZ4tpJA153LN5Boax4
    lettuce:
      pool:
        max-idle: 20
        min-idle: 5
        max-active: 50
        adaptiveRefreshTriggersTimeout: 5
        enablePeriodicRefresh: 10
#mybatis配置
mybatis:
  type-aliases-package: com.xgwc.user.entity
  mapper-locations: classpath:mapper/*.xml
logging:
  level:
    com.xgwc.order.dao: debug
security:
  ignoreUrls:
    - /login/login
    - /user/register
    - /user/get_use_detail
    - /favcoin
    - /upload/uploadfile
    - /upload/uploadpic
    - /login/send_code
    - /**
  jwt:
    key: vQvWiNN21UYmqQGXYY/jk3e6mOhNUez/hTcGIjon+9E=
    ttl: 86400000
aliyun:
  oss:
    # 地域节点
    endpoint: oss-cn-shenzhen.aliyuncs.com
    # AccessKey
    accessKeyId: LTAI5tKjULGeFUY7ZB9gdFup
    # AccessKey 秘钥
    accessKeySecret: ******************************
    # bucket名称
    bucketName: xgwcoa-dev
    # 访问域名
    host: https://xgwcoa-dev.oss-cn-shenzhen.aliyuncs.com
  sms:
    accessKeyId: LTAI5tEfBXHiZotDhHSqhXQu
    accessKeySecret: ******************************
    endpoint: dysmsapi.aliyuncs.com
    signName: 阿里云短信测试
    registerTemplateCode: SMS_154950909
    loginTemplateCode: SMS_154950909
    loginCodeLimit: 2
    registerCodeLimit: 2
    open: false
  ocr:
    endpoint: ocr-api.cn-hangzhou.aliyuncs.com
    accessKeyId: LTAI5tDPBy1uzhAd6FcKMTCM
    accessKeySecret: ******************************
    open: true
  vod:
    accessKeyId: LTAI5tEDwNYZJo1Y82HAnigd
    accessKeySecret: ******************************
    templateId: 247d1c4c4ce8e9e4ceff52a32684cdf5
com:
  xgwc:
    socket:
      url: wss://*************:8051/api/websocket/