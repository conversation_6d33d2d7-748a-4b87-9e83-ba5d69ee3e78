package com.xgwc.user.entity.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.json.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class SysUserInfo implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 所属部门
	 */
	private Long deptId;

	/**
	 * 所属部门名称
	 */
	private String deptName;

	/**
	 * 所属岗位
	 */
	private JSONArray postIds;

	/**
	 * 登陆用户名
	 */
	private String username;

	/**
	 * 手机号码
	 */
	private String phone;

	/**
	 * 备注信息
	 */
	private String note;

	/**
	 * 最后登陆IP
	 */
	private String loginIp;

	/**
	 * 最后登录时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
	private Date loginTime;

	/**
	 * 启用状态，0:禁用 1:启用
	 */
	private String status;

}
