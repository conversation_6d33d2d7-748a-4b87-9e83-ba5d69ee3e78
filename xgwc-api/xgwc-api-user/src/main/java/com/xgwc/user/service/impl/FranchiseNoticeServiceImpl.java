package com.xgwc.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.FranchiseNoticeMapper;
import com.xgwc.user.entity.dto.notice.NoticeClassfyDto;
import com.xgwc.user.entity.vo.FranchiseNoticeClassifyVo;
import com.xgwc.user.service.FranchiseNoticeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.List;

@Slf4j
@Service
public class FranchiseNoticeServiceImpl implements FranchiseNoticeService {

    @Resource
    private FranchiseNoticeMapper franchiseNoticeMapper;
    
    @Override
    public int insertNoticeClassify(FranchiseNoticeClassifyVo noticeClassifyVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null){
            log.error("添加通知分类失败,员工不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
            return -1;
        }
        noticeClassifyVo.setFranchiseId(franchiseId);
        noticeClassifyVo.setCreateBy(SecurityUtils.getNickName());
        return franchiseNoticeMapper.insertNoticeClassify(noticeClassifyVo);
    }

    @Override
    public int updateNoticeClassify(FranchiseNoticeClassifyVo noticeClassifyVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null){
            log.error("修改通知分类失败,员工不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
            return -1;
        }
        noticeClassifyVo.setStatus(null);
        noticeClassifyVo.setFranchiseId(franchiseId);
        noticeClassifyVo.setUpdateBy(SecurityUtils.getNickName());
        return franchiseNoticeMapper.updateNoticeClassify(noticeClassifyVo);
    }

    @Override
    public int updateNoticeClassifyStatus(FranchiseNoticeClassifyVo noticeClassifyVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null){
            log.error("修改通知分类状态失败,员工不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
            return -1;
        }
        noticeClassifyVo.setFranchiseId(franchiseId);
        noticeClassifyVo.setUpdateBy(SecurityUtils.getNickName());
        return franchiseNoticeMapper.updateStatus(noticeClassifyVo);
    }

    @Override
    public List<NoticeClassfyDto> getNoticeClassifyList(FranchiseNoticeClassifyVo noticeClassifyVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null){
            log.error("添加通知分类失败,员工不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        noticeClassifyVo.setFranchiseId(franchiseId);
        return franchiseNoticeMapper.getNoticeClassifyList(noticeClassifyVo);
    }
}
