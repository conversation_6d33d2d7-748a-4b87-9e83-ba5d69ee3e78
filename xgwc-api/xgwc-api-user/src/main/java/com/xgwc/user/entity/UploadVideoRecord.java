package com.xgwc.user.entity;

import lombok.Data;

@Data
public class UploadVideoRecord {

    private Integer id;

    /**
     * 原始文件名
     */
    private String oringinName;

    /**
     * 转码任务ID
     */
    private String transcodeId;

    /**
     * 转码结果
     */
    private String transcodeResult;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 状态：0正常，1 有效
     */
    private String status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 转码状态：0表示提交任务，1表示转码中，2表示转码完成
     */
    private Integer transcodeStatus;

}
