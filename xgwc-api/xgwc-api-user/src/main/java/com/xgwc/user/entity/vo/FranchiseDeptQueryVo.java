package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 加盟商部门管理查询参数
 */
@Data
public class FranchiseDeptQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("部门名称")
    private String deptName;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    @FieldDesc("层级")
    private Long level;

    @FieldDesc("父类id")
    private Long pid;

    @FieldDesc("排序：越小越前")
    private Long sort;

    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;



}
