package com.xgwc.user.config;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.teaopenapi.models.Config;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "aliyun.sms")
@RefreshScope
public class SmsClientConfig {

    private String accessKeyId;

    private String accessKeySecret;

    private String endpoint;

    /**
     * 标签名称
     */
    private String signName;

    /**
     * 登录模板
     */
    private String loginTemplateCode;

    /**
     * 注册模板
     */
    private String registerTemplateCode;

    /**
     * 每天发送条数
     */
    private int loginCodeLimit;

    /**
     * 每天发送条数
     */
    private int registerCodeLimit;

    /**
     * 是否开始
     */
    private boolean open;



    @Bean("smsClient")
    public Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = endpoint;
        return new Client(config);
    }
}
