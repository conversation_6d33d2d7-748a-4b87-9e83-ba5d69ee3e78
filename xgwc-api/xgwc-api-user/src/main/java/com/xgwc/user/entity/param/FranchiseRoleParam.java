package com.xgwc.user.entity.param;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-28  11:56
 */

/**
 * 加盟商角色管理参数
 */
@Data
public class FranchiseRoleParam {

    /**
     * 角色名称
     */
    @FieldDesc("角色名称")
    private String roleName;

    /**
     * 状态：0正常，1禁用
     */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    /**
     * 加盟商id
     */
    @FieldDesc("加盟商id")
    private Long franchiseId;
}
