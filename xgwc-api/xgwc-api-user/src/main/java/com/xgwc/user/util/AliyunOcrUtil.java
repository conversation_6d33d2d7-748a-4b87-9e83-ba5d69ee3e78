package com.xgwc.user.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeAllTextRequest;
import com.aliyun.ocr_api20210707.models.RecognizeAllTextResponse;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.xgwc.common.util.StringUtil;
import com.xgwc.user.config.AliyunOcrConfig;
import com.xgwc.user.entity.dto.aliyun.BusinessLicense;
import com.xgwc.user.entity.dto.aliyun.IdCard;
import com.xgwc.user.entity.dto.aliyun.Invoice;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AliyunOcrUtil {

    @Resource
    private Client ocrClient;

    @Resource
    private AliyunOcrConfig aliyunOcrConfig;

    /**
     * 识别身份证文字
     * @param imageUrl 图片的 URL 地址
     * @return 识别结果
     */
    public IdCard recognizeIdCardText(String imageUrl) {
        String jsonStr = recognizeText(imageUrl, "IdCard");
        String dataJson = getDataJson(jsonStr);
        if(StringUtil.isNotEmpty(dataJson)){
            return JSON.parseObject(dataJson, new TypeReference<>() {});
        }
        return null;
    }

    /**
     * 解析最里层的data属性
     */
    private String getDataJson(String jsonStr){
        if(StringUtil.isNotEmpty(jsonStr)){
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONArray jsonArray = jsonObject.getJSONArray("subImages");
            if(jsonArray != null && !jsonArray.isEmpty()){
                JSONObject kvInfoJson = jsonArray.getJSONObject(0).getJSONObject("kvInfo");
                if(kvInfoJson != null){
                    JSONObject dataJson = kvInfoJson.getJSONObject("data");
                    if(dataJson != null){
                        return dataJson.toJSONString();
                    }
                }

            }
        }
        return null;
    }

    /**
     * 识别营业执照文字
     * @param imageUrl 图片的 URL 地址
     * @return 识别结果
     */
    public BusinessLicense recognizeBusinessLicenseText(String imageUrl) {
        String jsonStr = recognizeText(imageUrl, "BusinessLicense");
        String dataJson = getDataJson(jsonStr);
        if(StringUtil.isNotEmpty(dataJson)){
            return JSON.parseObject(dataJson, new TypeReference<>() {});
        }
        return null;
    }

    /**
     * 识别发票
     * @param imageUrl 图片地址
     * @return识别结果
     */
    public Invoice recognizeInvoiceText(String imageUrl) {
        String jsonStr = recognizeText(imageUrl, "Invoice");
        String dataJson = getDataJson(jsonStr);
        if(StringUtil.isNotEmpty(dataJson)){
            return JSON.parseObject(dataJson, new TypeReference<>() {});
        }
        return null;
    }

    public String recognizeText(String imageUrl, String type) {
        if(!aliyunOcrConfig.isOpen()){
            return null;
        }
        RecognizeAllTextRequest request = new RecognizeAllTextRequest()
                .setUrl(imageUrl)
                .setType(type); // 通用文字识别类型
        try {
            RecognizeAllTextResponse response = ocrClient.recognizeAllTextWithOptions(request, new RuntimeOptions());
            if(response != null && response.getStatusCode() == 200) {
                return Common.toJSONString(response.body.data);
            }
        } catch (Exception e) {
            log.error("阿里云OCR识别失败, error:", e);
        }
        return null;
    }
}
