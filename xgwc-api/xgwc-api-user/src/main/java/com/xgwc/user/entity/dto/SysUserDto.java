package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class SysUserDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("用户ID")
    @Excel(name = "用户ID")
    private Long userId;

    @FieldDesc("用户名")
    @Excel(name = "用户名")
    private String userName;

    @FieldDesc("手机号")
    private String phone;

    @FieldDesc("帐号状态（0正常 1停用）")
    @Excel(name = "帐号状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("删除标志（0代表存在 1代表删除）")
    @Excel(name = "删除标志（0代表存在 1代表删除）")
    private Integer delFlag;

    @FieldDesc("登录IP")
    @Excel(name = "登录IP")
    private String loginIp;

    @FieldDesc("登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登录时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loginDate;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("微信id")
    @Excel(name = "微信id")
    private String wechatOpenid;

    @FieldDesc("创建人ID")
    @Excel(name = "创建人ID")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人ID")
    @Excel(name = "修改人ID")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("密码修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "密码修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date passwordModifyTime;

    /**
     * 用户类型（0：普通用户，1：品牌商，2：加盟商，3：设计师, 4:品牌商员工，5：加盟商员工）
     */
    private Integer userType;

    /**
     * 品牌商ID
     */
    private Long brandId;

    /**
     * 加盟商ID
     */
    private Long franchiseId;

    /**
     * 花名
     */
    private String stageName;



    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId",getUserId())
            .append("userName",getUserName())
            .append("status",getStatus())
            .append("delFlag",getDelFlag())
            .append("loginIp",getLoginIp())
            .append("loginDate",getLoginDate())
            .append("remark",getRemark())
            .append("wechatOpenid",getWechatOpenid())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
            .append("passwordModifyTime",getPasswordModifyTime())
        .toString();
        }
}
