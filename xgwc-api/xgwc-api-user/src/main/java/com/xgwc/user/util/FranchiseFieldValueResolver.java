package com.xgwc.user.util;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.xgwc.common.util.SpringUtils;
import com.xgwc.user.dao.FranchiseDeptMapper;
import com.xgwc.user.dao.FranchiseRoleMapper;
import com.xgwc.user.dao.FranchiseStaffMapper;
import com.xgwc.user.dao.FranchiseStationMapper;
import com.xgwc.user.entity.dto.FranchiseDeptDto;
import com.xgwc.user.entity.dto.FranchiseStaffDto;
import com.xgwc.user.entity.vo.FranchiseRoleVo;
import com.xgwc.user.entity.vo.FranchiseStationVo;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class FranchiseFieldValueResolver {

    private static final Map<String, Function<String, String>> fieldResolvers = new HashMap<>();

    static {
        fieldResolvers.put("部门", id -> {
            if (StringUtils.isBlank(id)) return "无";
            FranchiseDeptDto franchiseDeptDto = SpringUtils.getBean(FranchiseDeptMapper.class).getFranchiseDeptById(Long.valueOf(id));
            return franchiseDeptDto != null ? franchiseDeptDto.getDeptName() : id;
        });

        fieldResolvers.put("岗位", id -> {
            if (StringUtils.isBlank(id)) return "无";
            FranchiseStationVo franchiseStationVo = SpringUtils.getBean(FranchiseStationMapper.class).getFranchiseStationById(Integer.parseInt(id));
            return franchiseStationVo != null ? franchiseStationVo.getStationName() : id;
        });

        fieldResolvers.put("角色权限", ids -> {
            if (StringUtils.isBlank(ids)) return "无";
            FranchiseRoleMapper roleMapper = SpringUtils.getBean(FranchiseRoleMapper.class);
            return Arrays.stream(ids.split(","))
                    .map(id -> roleMapper.getFranchiseRoleById(Long.parseLong(id)))
                    .filter(Objects::nonNull)
                    .map(FranchiseRoleVo::getRoleName)
                    .collect(Collectors.joining(", "));
        });

        fieldResolvers.put("工作性质", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "全职" : id.equals("1") ? "外包" : id.equals("2") ? "兼职" : id;
        });

        fieldResolvers.put("状态", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "在职" : id.equals("1") ? "离职" : id;
        });

        fieldResolvers.put("部门负责人", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "是" : id.equals("1") ? "否" : id;
        });

        fieldResolvers.put("直属上级", id -> {
            if (StringUtils.isBlank(id)) return "无";
            FranchiseStaffDto franchiseStaffDto = SpringUtils.getBean(FranchiseStaffMapper.class).selectFranchiseStaffById(Long.parseLong(id));
            return franchiseStaffDto != null ? franchiseStaffDto.getStageName() : id;
        });
    }

    public static String resolve(String fieldName, String value) {
        Function<String, String> resolver = fieldResolvers.get(fieldName);
        return resolver != null ? resolver.apply(value) : value;
    }
}
