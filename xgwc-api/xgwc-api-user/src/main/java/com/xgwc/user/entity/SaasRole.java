package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class SaasRole {

private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long roleId;

    /** 角色名称 */
    private String roleName;

    /** 排序 */
    private Integer sort;

    /** 是否系统默认角色（0-否，1-是） */
    private Integer isDefault;

    /** 角色权限范围:0-品普 1-品管 2-加普 3-加管 4-服普 5-服管 6-销普 7-销管 8-saas普 9-saas管 */
    private Integer roleScope;

    /** 生效范围 品牌商/加盟商/财务服务商/销售服务商（id） */
    private Long effectiveScope;

    /** 业务系统 0-猴霸 1-人资 */
    private Integer businessSystem;

    /** 角色状态（0-正常/1-禁用） */
    private Integer status;

    /** 备注 */
    private String remark;

    /** 删除 0-正常 1-删除 */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;



}