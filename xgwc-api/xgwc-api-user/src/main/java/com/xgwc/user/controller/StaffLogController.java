package com.xgwc.user.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.vo.StaffLogVo;
import com.xgwc.user.entity.dto.StaffLogDto;
import com.xgwc.user.entity.vo.StaffLogQueryVo;
import com.xgwc.user.service.IStaffLogService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/staffLog/staffLog")
public class StaffLogController extends BaseController {
    @Autowired
    private IStaffLogService staffLogService;

    /**
     * 查询加盟商员工日志列表
     */
    @MethodDesc("查询加盟商员工日志列表")
    @PreAuthorize("@ss.hasPermission('staffLog:staffLog:list')")
    @GetMapping("/list")
    public ApiResult<StaffLogDto> list(StaffLogQueryVo staffLog) {
        startPage();
        List<StaffLogDto> list = staffLogService.selectStaffLogList(staffLog);
        return getDataTable(list);
    }


    /**
     * 修改加盟商员工日志
     */
    @MethodDesc("修改加盟商员工日志")
    @PreAuthorize("@ss.hasPermission('staffLog:staffLog:edit')")
    @Log(title = "加盟商员工日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody StaffLogVo staffLog) {
        return toAjax(staffLogService.updateStaffLog(staffLog));
    }

    /**
     * 删除加盟商员工日志
     */
    @MethodDesc("删除加盟商员工日志")
    @PreAuthorize("@ss.hasPermission('staffLog:staffLog:remove')")
    @Log(title = "加盟商员工日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(staffLogService.deleteStaffLogByIds(ids));
    }
}
