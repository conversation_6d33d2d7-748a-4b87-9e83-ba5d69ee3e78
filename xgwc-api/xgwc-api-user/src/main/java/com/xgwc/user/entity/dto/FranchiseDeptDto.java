package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.Staff;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.dto
 * @Author: k<PERSON><PERSON><PERSON>huo
 * @CreateTime: 2025-04-28  11:24
 */

/**
 * 加盟商部门表
 */
@Data
public class FranchiseDeptDto {
    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 加盟商id
     */
    private Long franchiseId;

    /**
     * 父类id（用于表示部门层级关系）
     */
    private Long pid;

    /**
     * 层级（表示部门在层级结构中的位置）
     */
    private Integer level;

    /**
     * 排序：越小越前（用于部门列表的排序）
     */
    private Integer sort;

    /**
     * 状态：0正常，1禁用（表示部门的启用状态）
     */
    private Integer status;

    /**
     * 是否删除：0正常，1删除（表示部门是否被逻辑删除）
     */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @FieldDesc("部门负责人")
    private Staff isPrincipal;

    @FieldDesc("助理")
    private List<Staff> isAssistant;
}
