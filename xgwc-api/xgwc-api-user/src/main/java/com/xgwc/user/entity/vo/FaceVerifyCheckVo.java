package com.xgwc.user.entity.vo;

import lombok.Data;

@Data
public class FaceVerifyCheckVo {

    /**
     * 认证名称
     */
    private String certName;

    /**
     * 身份证号码
     */
    private String certNo;

    /**
     * MetaInfo环境参数。实际环境需要通过JS文件，调用函数getMetaInfo()获取
     */
    private String metaInfo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 回调token uuid 32位
     */
    private String callBackToken;

    /**
     * 返回地址
     */
    private String returnUrl;

    /**
     * 唯一标识
     */
    private String certifyId;

    /**
     * 是否通过
     */
    private String passed;

    /**
     * callbackToken
     */
    private String callbackToken;

}
