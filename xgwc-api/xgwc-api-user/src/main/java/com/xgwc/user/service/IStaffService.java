package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.BrandDeptStaff;
import com.xgwc.user.entity.dto.SimpleDeptUserInfoDto;
import com.xgwc.user.entity.dto.StaffDto;
import com.xgwc.user.entity.vo.StaffQueryVo;
import com.xgwc.user.entity.vo.StaffVo;

import java.util.List;
import java.util.Map;

public interface IStaffService  {
    /**
     * 查询员工管理
     * 
     * @param id 员工管理主键
     * @return 员工管理
     */
    public StaffDto selectStaffById(Long id);

    /**
     * 查询员工管理列表
     * 
     * @param staff 员工管理
     * @return 员工管理集合
     */
    public List<StaffDto> selectStaffList(StaffQueryVo staff);

    /**
     * 新增员工管理
     * 
     * @param staff 员工管理
     * @return 结果
     */
    public int insertStaff(StaffVo staff);

    /**
     * 修改员工管理
     * 
     * @param staff 员工管理
     * @return 结果
     */
    public int updateStaff(StaffVo staff);

    /**
     * 批量删除员工管理
     * 
     * @param ids 需要删除的员工管理主键集合
     * @return 结果
     */
    public int deleteStaffByIds(Long[] ids);

    /**
     * 删除员工管理信息
     * 
     * @param id 员工管理主键
     * @return 结果
     */
    public int deleteStaffById(Long id);

    /**
     * 根据姓名和品牌商id查询员工管理
     * @param name 姓名
     * @param brandId 品牌商id
     * @return 员工信息
     */
    public StaffDto selectStaffByNameAndBrandId(String name,Long brandId);

    /**
     * 查询员工管理下拉框
     *
     * @return 员工信息
     */
    List<Map<String, Object>> selectStaffListDropDown();

    /**
     * 绑定员工
     * @param id 员工id
     * @param bindUserId 绑定用户id
     * @param bindStatus 绑定状态
     * @param phone 手机号
     * @return 结果
     */
    int updateBindStatus(Long id,Long bindUserId,Integer bindStatus,String phone,String stageName);

    /**
     * 获取品牌商部门员工数
     * @return 列表
     */
    List<BrandDeptStaff> getDeptStaffTree();

    /**
     * 获取品牌商员工下拉框
     * @return 列表
     */
    List<SimpleDeptUserInfoDto> getBrandStaffDropDown(Long deptId);

    /**
     * 修改员工状态
     * @param id 员工id
     * @param status 状态
     * @return 结果
     */
    int updateStaffStatus(Long id,Integer status);

    /**
     * 根据绑定用户id查询员工信息
     * @param userId 绑定用户id
     * @return 员工信息
     */
    StaffDto selectStaffByBindUserId(Long userId);

    /**
     * 获取员工下载数
     *
     * @param userId  用户id
     * @param isFlag  0-品牌商，1-加盟商
     * @param brandId 品牌商id
     * @return 结果
     */
    StaffDto getStaffDownloadCountByUserId(Long userId, Integer isFlag, Long brandId);

    /**
     * 根据派单管理-派单工作台菜单 查询有该菜单权限的员工信息
     *
     * @return 员工信息
     */
    ApiResult getStaffInfoByMenu();

    /**
     * 根据用户id和品牌商id查询员工信息
     * @param userId 用户id
     * @param brandId 品牌商id
     * @return 员工信息
     */
    StaffDto selectStaffByUserIdAndBrandId(Long userId, Long brandId);
}
