package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.EmployeesDateFlag;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.FranchiseEmployeesMapper;
import com.xgwc.user.dao.FranchiseStaffMapper;
import com.xgwc.user.entity.FranchiseEmpAccounts;
import com.xgwc.user.entity.FranchiseEmpAttachments;
import com.xgwc.user.entity.FranchiseEmployees;
import com.xgwc.user.entity.FranchiseStaff;
import com.xgwc.user.entity.dto.FranchiseEmployeesCountDto;
import com.xgwc.user.entity.dto.FranchiseEmployeesDto;
import com.xgwc.user.entity.vo.FranchiseEmployeesQueryVo;
import com.xgwc.user.entity.vo.FranchiseEmployeesVo;
import com.xgwc.user.service.FranchiseEmployeesService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;


@Service
@Slf4j
public class FranchiseEmployeesServiceImpl extends BaseController implements FranchiseEmployeesService {
    @Resource
    private FranchiseEmployeesMapper franchiseEmployeesMapper;
    @Resource
    private FranchiseStaffMapper franchiseStaffMapper;

    // 状态：0在职，1离职
    public static final Integer STATUS_ACTIVE = 0;
    public static final Integer STATUS_RESIGNING = 2;

    /**
     * 查询员工档案
     *
     * @param employeeId 员工档案主键
     * @return 员工档案
     */
    @Override
    public FranchiseEmployeesDto selectEmployeesByEmployeeId(Long employeeId) {
        FranchiseEmployeesDto franchiseEmployeesDto = franchiseEmployeesMapper.selectEmployeesByEmployeeId(employeeId);
        if (franchiseEmployeesDto != null) {
            List<FranchiseEmpAttachments> franchiseEmpAttachments = franchiseEmployeesMapper.selectFranchiseEmpAttachments(franchiseEmployeesDto.getEmployeeId());
            // 解密 true-加密 false-解密
            extracted(franchiseEmployeesDto);
            franchiseEmployeesDto.setXgwcEmpAttachments(franchiseEmpAttachments);
        }
        return franchiseEmployeesDto;
    }

    private void extracted(FranchiseEmployeesDto franchiseEmployeesDto) {
        // 解密 true-加密 false-解密
        franchiseEmployeesDto.setPhone(ParamDecryptUtil.encryptField(franchiseEmployeesDto.getPhone(), "手机号", false));
        franchiseEmployeesDto.setEmail(ParamDecryptUtil.encryptField(franchiseEmployeesDto.getEmail(), "邮箱", false));
        franchiseEmployeesDto.setIdNumber(ParamDecryptUtil.encryptField(franchiseEmployeesDto.getIdNumber(), "身份证号", false));
        franchiseEmployeesDto.setAddress(ParamDecryptUtil.encryptField(franchiseEmployeesDto.getAddress(), "员工地址", false));
        franchiseEmployeesDto.setEmerPhone(ParamDecryptUtil.encryptField(franchiseEmployeesDto.getEmerPhone(), "紧急联系人电话", false));
        franchiseEmployeesDto.setAlipayAccount(ParamDecryptUtil.encryptField(franchiseEmployeesDto.getAlipayAccount(), "支付宝账号", false));
        franchiseEmployeesDto.setAccountNumber(ParamDecryptUtil.encryptField(franchiseEmployeesDto.getAccountNumber(), "银行卡号", false));
    }

    /**
     * 查询员工档案列表
     *
     * @param employees 员工档案
     * @return 员工档案
     */
    @Override
    public FranchiseEmployeesCountDto selectEmployeesList(FranchiseEmployeesQueryVo employees) {
/*        Date[] entryDate = employees.getEntryDate();
        Integer dateFlag = employees.getDateFlag();
        if (dateFlag != null) {
            EmployeesDateFlag textByCode = EmployeesDateFlag.getTextByCode(dateFlag);
            if (textByCode != null) {
                extracted(employees, entryDate, textByCode);
            }
        }*/
        String name = employees.getName();
        boolean letterOrChinese = isLetterOrChinese(name);
        if (!letterOrChinese) {
            // 对敏感入参加密 加密之后匹配数据库中加密字段
            employees.setName(ParamDecryptUtil.encryptField(name, "/手机号/银行卡号/支付宝账号/身份证号", true));
        }
        //startPage();
        PageHelper.startPage(employees.getPageNum(), employees.getPageSize());
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        employees.setFranchiseId(franchiseId);
        List<FranchiseEmployeesDto> franchiseEmployeesDtos = franchiseEmployeesMapper.selectEmployeesList(employees);
        for (FranchiseEmployeesDto franchiseEmployeesDto : franchiseEmployeesDtos) {
            // 解密 true-加密 false-解密
            franchiseEmployeesDto.setPhone(ParamDecryptUtil.encryptField(franchiseEmployeesDto.getPhone(), "手机号", false));
        }
        ApiResult dataTable = getDataTable(franchiseEmployeesDtos);
        FranchiseEmployeesQueryVo franchiseEmployeesQueryVo = new FranchiseEmployeesQueryVo();
        franchiseEmployeesQueryVo.setFranchiseId(franchiseId);
        List<FranchiseEmployeesDto> franchiseEmployeesDtos1 = franchiseEmployeesMapper.selectEmployeesList(franchiseEmployeesQueryVo);
        FranchiseEmployeesCountDto franchiseEmployeesCountDto = getCurrentEmployeesCount(franchiseEmployeesDtos1);
        franchiseEmployeesCountDto.setEmployeesDtos(dataTable);
        return franchiseEmployeesCountDto;
    }

    /**
     * 列表页签统计
     */
    private FranchiseEmployeesCountDto getCurrentEmployeesCount(List<FranchiseEmployeesDto> employees) {
        FranchiseEmployeesCountDto result = new FranchiseEmployeesCountDto();

        for (FranchiseEmployeesDto e : employees) {
            updateStatusCount(result, e.getStatus());
            updateEntryCount(result, e.getEntryDate());
            updateExitCount(result, e.getResignationDate());
            updateContractCount(result, e.getContractEndDate());
        }

        return result;
    }

    private void updateStatusCount(FranchiseEmployeesCountDto result, Integer status) {
        if (STATUS_ACTIVE.equals(status)) {
            result.incrementCurrentCount();
        } else if (STATUS_RESIGNING.equals(status)) {
            result.incrementResignationCount();
        }
    }

    private void updateEntryCount(FranchiseEmployeesCountDto result, Date entryDate) {
        if (DateUtils.isDateInCurrentMonth(entryDate)) {
            result.incrementMonthlyEntryCount();
        }
    }

    private void updateExitCount(FranchiseEmployeesCountDto result, Date resignationDate) {
        if (DateUtils.isDateInCurrentMonth(resignationDate)) {
            result.incrementMonthlyExitCount();
        }
    }

    private void updateContractCount(FranchiseEmployeesCountDto result, Date contractEndDate) {
        if (DateUtils.isWithinNextThreeMonths(contractEndDate)) {
            result.incrementContractMonthsCount();
        }
    }

    private static boolean isLetterOrChinese(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }
        // 正则规则：仅由字母或中文组成
        return str.matches("^[a-zA-Z\\p{Script=Han}]+$");
    }

    private static void extracted(FranchiseEmployeesQueryVo employees, Date[] entryDate, EmployeesDateFlag textByCode) {
        switch (textByCode) {
            case CONTRACT_END_DATE -> employees.setContractEndDate(entryDate);
            case RESIGNATION_DATE -> employees.setResignationDate(entryDate);
            case BIRTHDATE -> employees.setBirthdate(entryDate);
            default -> employees.setEntryDate(entryDate);
        }
    }

    /**
     * 新增员工档案
     *
     * @param vo 员工档案
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult insertEmployees(FranchiseEmployeesVo vo) {
        // 参数校验前置
        if (vo == null) {
            log.error("员工信息录入失败：输入参数不能为空");
            throw new IllegalArgumentException("员工信息不能为空");
        }

        validateEmployee(vo);  // 实体校验

        // 参数唯一性校验
        try {
            String validated = validateUniqueFieldsAsync(vo);
            if (validated != null) {
                return ApiResult.error(validated);
            }
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
            // ================= 员工信息处理 =================
            FranchiseEmployees franchiseEmployees = processEmployeeInfo(vo);
            franchiseEmployees.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
            franchiseEmployees.setCreateBy(SecurityUtils.getNickName());
            int employeesResult = franchiseEmployeesMapper.insertEmployees(franchiseEmployees);
            FranchiseStaff staff = new FranchiseStaff();
            staff.setId(franchiseEmployees.getStaffId());
            staff.setArchiveStatus(1);
            franchiseStaffMapper.updateFranchiseStaff(staff);
            if (employeesResult <= 0) {
                log.error("员工信息新增失败");
                throw new ApiException("员工信息新增失败");
            }

            // ================= 账户信息处理 =================
            FranchiseEmpAccounts franchiseEmpAccounts = processAccountInfo(vo);
            franchiseEmpAccounts.setCreateBy(SecurityUtils.getNickName());
            franchiseEmpAccounts.setEmployeeId(franchiseEmployees.getEmployeeId());
            int accountsResult = franchiseEmployeesMapper.insertFranchiseEmpAccounts(franchiseEmpAccounts);
            if (accountsResult <= 0) {
                log.error("账户信息新增失败");
                throw new ApiException("账户信息新增失败");
            }

            // ================= 附件信息处理 =================
            List<FranchiseEmpAttachments> franchiseEmpAttachmentsList = vo.getXgwcEmpAttachments();
            if (!CollectionUtils.isEmpty(franchiseEmpAttachmentsList)) {
                for (FranchiseEmpAttachments empAttachments : franchiseEmpAttachmentsList) {
                    empAttachments.setEmployeeId(franchiseEmployees.getEmployeeId());
                    empAttachments.setCreateBy(SecurityUtils.getNickName());
                    int attachmentsResult = franchiseEmployeesMapper.insertFranchiseEmpAttachments(empAttachments);
                    if (attachmentsResult <= 0) {
                        log.error("附件信息新增失败");
                        throw new ApiException("附件信息新增失败");
                    }
                }
            }

            log.info("员工信息录入成功 ID:[{}] 姓名:[{}]",
                    franchiseEmployees.getStaffId(),
                    vo.getName());
            return ApiResult.ok();
        } catch (Exception e) {  // 全局异常捕获
            log.error("系统异常 员工:[{}] 错误信息:{}",
                    vo.getEmployeeId(), e.getMessage(), e);
            throw new ApiException("系统处理异常");
        }
    }

    private String validateUniqueFieldsAsync(FranchiseEmployeesVo vo) throws ExecutionException, InterruptedException {

        FranchiseEmployees franchiseEmployees = processEmployeeInfo(vo);
        FranchiseEmpAccounts franchiseEmpAccounts = processAccountInfo(vo);

        CompletableFuture<String> idNumberFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniquenesAsync(franchiseEmployees.getIdNumber(), "身份证号", franchiseEmployeesMapper::existsByIdNumber)
        );

        CompletableFuture<String> phoneFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniquenesAsync(franchiseEmployees.getPhone(), "手机号", franchiseEmployeesMapper::existsByPhone)
        );

        CompletableFuture<String> accountNumberFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniquenesAsync(franchiseEmpAccounts.getAccountNumber(), "银行卡号", franchiseEmployeesMapper::existsByAccountNumber)
        );

        CompletableFuture<String> alipayAccountFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniquenesAsync(franchiseEmpAccounts.getAlipayAccount(), "支付宝账号", franchiseEmployeesMapper::existsByAlipayAccount)
        );

        // 等待所有异步任务完成，并收集结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(idNumberFuture, phoneFuture, accountNumberFuture, alipayAccountFuture);
        allFutures.get(); // 阻塞直到所有任务完成

        // 收集所有校验结果
        List<String> errorMessages = new ArrayList<>();

        String idNumberResult = idNumberFuture.get();
        if (idNumberResult != null) {
            errorMessages.add(idNumberResult);
        }

        String phoneResult = phoneFuture.get();
        if (phoneResult != null) {
            errorMessages.add(phoneResult);
        }

        String accountNumberResult = accountNumberFuture.get();
        if (accountNumberResult != null) {
            errorMessages.add(accountNumberResult);
        }

        String alipayAccountResult = alipayAccountFuture.get();
        if (alipayAccountResult != null) {
            errorMessages.add(alipayAccountResult);
        }

        // 拼接所有错误信息
        if (!errorMessages.isEmpty()) {
            return String.join("，", errorMessages);
        }
        return null;
    }

    private String validateFieldUniquenesAsync(
            String fieldValue,
            String fieldName,
            Function<String, Boolean> existsChecker) {
        if (fieldValue != null && !fieldValue.trim().isEmpty() && existsChecker.apply(fieldValue)) {
            log.error("========================={}已存在", fieldName);
            return (fieldName + "已存在");
        }
        return null;
    }

    // 员工信息处理（抽取方法）
    private FranchiseEmployees processEmployeeInfo(FranchiseEmployeesVo dto) {
        FranchiseEmployees entity = BeanUtil.copyProperties(dto, FranchiseEmployees.class);

        // 字段加密 true-加密 false-解密
        entity.setPhone(ParamDecryptUtil.encryptField(dto.getPhone(), "手机号", true));
        entity.setEmail(ParamDecryptUtil.encryptField(dto.getEmail(), "邮箱", true));
        entity.setIdNumber(ParamDecryptUtil.encryptField(dto.getIdNumber(), "身份证号", true));
        entity.setAddress(ParamDecryptUtil.encryptField(dto.getAddress(), "员工住址", true));
        entity.setEmerPhone(ParamDecryptUtil.encryptField(dto.getEmerPhone(), "紧急联系人手机号", true));
        return entity;
    }

    // 账户信息处理（抽取方法）
    private FranchiseEmpAccounts processAccountInfo(FranchiseEmployeesVo dto) {
        FranchiseEmpAccounts account = BeanUtil.copyProperties(dto, FranchiseEmpAccounts.class);

        // 字段加密 true-加密 false-解密
        account.setAccountNumber(ParamDecryptUtil.encryptField(dto.getAccountNumber(), "银行账号", true));
        account.setAlipayAccount(ParamDecryptUtil.encryptField(dto.getAlipayAccount(), "支付宝账号", true));
        return account;
    }

    // 员工实体校验
    private void validateEmployee(FranchiseEmployeesVo employee) {
        if (StringUtils.isBlank(employee.getIdNumber())) {
            throw new ApiException("身份证号不能为空");
        }
        if (StringUtils.isBlank(employee.getPhone())) {
            throw new ApiException("手机号不能为空");
        }
        if (StringUtils.isBlank(employee.getAccountNumber())) {
            throw new ApiException("银行账号不能为空");
        }
        if (StringUtils.isBlank(employee.getAlipayAccount())) {
            throw new ApiException("支付宝账号不能为空");
        }
    }


    /**
     * 修改员工档案
     *
     * @param dto 员工档案
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateEmployees(FranchiseEmployeesVo dto) {

        // 参数校验前置
        if (dto == null) {
            log.error("员工信息录入失败：输入参数不能为空");
            throw new IllegalArgumentException("员工信息不能为空");
        }

        FranchiseEmployeesDto employees = selectEmployeesByEmployeeId(dto.getStaffId());
        if(employees ==  null){
            return ApiResult.error("员工信息不存在");
        }

        if (Objects.equals(employees.getPhone(), dto.getPhone())) {
            dto.setPhone(null);
        }
        if (Objects.equals(employees.getIdNumber(), dto.getIdNumber())) {
            dto.setIdNumber(null);
        }
        if (Objects.equals(employees.getAccountNumber(), dto.getAccountNumber())) {
            dto.setAccountNumber(null);
        }
        if (Objects.equals(employees.getAlipayAccount(), dto.getAlipayAccount())) {
            dto.setAlipayAccount(null);
        }

        // 参数唯一性校验
        try {
            String validated = validateUniqueFieldsAsync(dto);
            if (validated != null) {
                return ApiResult.error(validated);
            }
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
            // ================= 员工信息处理 =================
            FranchiseEmployees franchiseEmployees = processEmployeeInfo(dto);
            franchiseEmployees.setUpdateBy(SecurityUtils.getNickName());
            int employeesResult = franchiseEmployeesMapper.updateEmployees(franchiseEmployees);
            if (employeesResult <= 0) {
                log.error("账户信息修改失败");
                throw new ApiException("账户信息修改失败");
            }

            // ================= 账户信息处理 =================
            FranchiseEmpAccounts franchiseEmpAccounts = processAccountInfo(dto);
            franchiseEmpAccounts.setUpdateBy(SecurityUtils.getNickName());
            franchiseEmpAccounts.setEmployeeId(franchiseEmployees.getEmployeeId());
            int accountsResult = franchiseEmployeesMapper.updateFranchiseEmpAccounts(franchiseEmpAccounts);
            if (accountsResult <= 0) {
                log.error("账户信息修改失败");
                throw new ApiException("账户信息修改失败");
            }

            // ================= 附件信息处理 =================
            List<FranchiseEmpAttachments> franchiseEmpAttachmentsList = dto.getXgwcEmpAttachments();
            franchiseEmployeesMapper.deleteFranchiseEmpAttachments(dto.getEmployeeId());
            if(!CollectionUtils.isEmpty(franchiseEmpAttachmentsList)){
                for (FranchiseEmpAttachments empAttachments : franchiseEmpAttachmentsList) {
                    empAttachments.setEmployeeId(franchiseEmployees.getEmployeeId());
                    empAttachments.setUpdateBy(SecurityUtils.getNickName());
                    int updAttachmentsResult = franchiseEmployeesMapper.insertFranchiseEmpAttachments(empAttachments);
                    if (updAttachmentsResult <= 0) {
                        log.error("附件信息修改失败");
                        throw new ApiException("附件信息修改失败");
                    }
                }
            }

            log.info("员工信息修改成功 ID:[{}] 姓名:[{}]",
                    franchiseEmployees.getStaffId(),
                    dto.getName());
            return ApiResult.ok();
        } catch (Exception e) {  // 全局异常捕获
            log.error("系统异常 员工:[{}] 错误信息:{}",
                    dto.getStaffId(), e.getMessage(), e);
            throw new ApiException("系统处理异常");
        }
    }

    @Override
    public int resignationsEmployees(FranchiseEmployeesVo employees) {
        return 0;
    }
}
