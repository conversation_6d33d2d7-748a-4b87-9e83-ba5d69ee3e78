package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysTenantQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("租户编号")
    private Long id;

    @FieldDesc("租户名")
    private String name;

    @FieldDesc("联系人的用户编号")
    private Long contactUserId;

    @FieldDesc("联系人")
    private String contactName;

    @FieldDesc("联系手机")
    private String contactMobile;

    @FieldDesc("租户状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("绑定域名")
    private String website;

    @FieldDesc("租户套餐编号")
    private Long packageId;

    @FieldDesc("过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  expireTime;

    @FieldDesc("账号数量")
    private Long accountCount;

    @FieldDesc("创建者")
    private String creator;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("更新者")
    private String updater;

    @FieldDesc("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("是否删除")
    private Integer isDel;



}
