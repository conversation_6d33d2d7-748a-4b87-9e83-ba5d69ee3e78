package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.DesignerVO;
import com.xgwc.user.service.IDesignerService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("designer")
@Validated
public class DesignerController extends BaseController
{
    @Autowired
    private IDesignerService designerService;

    /**
     * 查询设计师管理列表
     */
    @MethodDesc("设计师列表")
    @GetMapping("/list")
    public ApiResult list(DesignerQueryDto designer, @RequestHeader(value = "brandId", required = false) Long brandId) {
        startPage();
        designer.setBrandId(brandId);
        List<DesignerVO> list = designerService.selectDesignerList(designer);
        return getDataTable(list);
    }

    /**
     * 查询设计师申请记录
     */
    @MethodDesc("设计师申请记录")
    @GetMapping("/applyLog")
    public ApiResult applyLog(DesignerQueryDto designer) {
        startPage();
        return getDataTable(designerService.findApplyLogList(designer));
    }

    /**
     * 获取设计师管理详细信息
     */
    @MethodDesc("获取设计师管理详细信息")
    @GetMapping(value = "/{designerId}")
    public ApiResult getInfo(@PathVariable("designerId") Long designerId) {
        return success(designerService.selectDesignerByDesignerId(designerId));
    }

    /**
     * 新增设计师管理
     */
    @MethodDesc("设计师注册")
    @Log(title = "设计师管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult<LoginResult> add(@RequestBody @Valid DesignerDto designer) {
        return ApiResult.ok(designerService.insertDesigner(designer));
    }

    /**
     * 修改设计师管理
     */
    @MethodDesc("修改设计师管理")
    @Log(title = "设计师管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody @Valid DesignerVO designer) {
        return toAjax(designerService.updateDesigner(designer));
    }

    /**
     * 删除设计师管理
     */
    @MethodDesc("删除设计师管理")
    @PreAuthorize("@ss.hasPermission('user:Designer:remove')")
    @Log(title = "设计师管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{designerIds}")
    public ApiResult remove(@PathVariable Long[] designerIds) {
        return toAjax(designerService.deleteDesignerByDesignerIds(designerIds));
    }

    /**
     * 审核
     */
    @MethodDesc("审核")
    @PutMapping("/audit")
    @PreAuthorize("@ss.hasPermission('user:Designer:audit')")
    @Log(title = "设计师管理", businessType = BusinessType.UPDATE)
    public ApiResult<Boolean> audit(@RequestBody @Valid DesignerAuditDto updateReqVO) {
        designerService.audit(updateReqVO);
        return success(true);
    }

    /**
     * 接单
     */
    @MethodDesc("接单")
    @PutMapping("/receive")
    @Log(title = "设计师管理", businessType = BusinessType.UPDATE)
    public ApiResult<Boolean> receive(@RequestBody @Valid DesignerReceiveDto updateReqVO) {
        designerService.receive(updateReqVO);
        return success(true);
    }

    /**
     * 重新申请
     */
    @MethodDesc("设计师注册")
    @Log(title = "设计师管理", businessType = BusinessType.INSERT)
    @PostMapping("/reapply")
    public ApiResult reapply(@RequestBody @Valid DesignerDto designer) {
        return toAjax(designerService.reapply(designer));
    }

    /**
     * 根据业务ID获取业务下的设计师列表
     */
    @RequestMapping("get_designerlist_bybusinessid")
    public ApiResult getDesignerByBusinessId(Integer businessId) {
        if(businessId == null){
            return ApiResult.error("业务ID不能为空");
        }
        return ApiResult.ok(designerService.getSimpleUserInfoListByBusinessId(businessId));
    }

    /**
     * 根据managerUserId获取加入的品牌商下拉列表
     */
    @GetMapping("/joinBrandDropDown")
    public ApiResult getInJoinBrandDropDown() {
        return designerService.getInJoinBrandDropDown();
    }

    /**
     * 获取本品牌下的分类设计师树
     */
    @RequestMapping("getClassifyDesignerTree")
    public ApiResult getClassifyDesignerTree (){
        List<ClassifyDesignerTree> classifyDesignerTrees = designerService.getClassifyDesignerTree();
        return getDataTable(classifyDesignerTrees);
    }

    /**
     * 加盟商设计师列表
     */
    @MethodDesc("加盟商设计师列表")
    @PreAuthorize("@ss.hasPermission('user:Designer:franchiseeDesignerList')")
    @GetMapping("/franchiseeDesignerList")
    public ApiResult getFranchiseeDesignerList(DesignerQueryDto designerQueryDto) {
        startPage();
        List<DesignerVO> list = designerService.selectFranchiseeDesignerList(designerQueryDto);
        return getDataTable(list);
    }

    /**
     * 修改设计师特长
     */
    @MethodDesc("修改设计师特长")
    @PutMapping("/updateSpecialty")
    @PreAuthorize("@ss.hasPermission('user:Designer:editSpecialty')")
    @Log(title = "设计师管理", businessType = BusinessType.UPDATE)
    public ApiResult updateSpecialty(@RequestBody @Valid DesignerSpecialtyUpdateDto updateReqVO) {
        designerService.updateSpecialty(updateReqVO);
        return success(true);
    }

    /**
     * 设置等级
     */
    @MethodDesc("设置等级")
    @PutMapping("/setGrade")
    @PreAuthorize("@ss.hasPermission('user:Designer:editGrade')")
    @Log(title = "设计师管理", businessType = BusinessType.UPDATE)
    public ApiResult setGrade(@RequestBody @Valid DesignerGradeUpdateDto updateReqVO) {
        designerService.setGrade(updateReqVO);
        return success(true);
    }


    @MethodDesc("修改设计师状态")
    @PutMapping("/updateStatus")
    @PreAuthorize("@ss.hasPermission('user:Designer:editStatus')")
    @Log(title = "设计师管理", businessType = BusinessType.UPDATE)
    public ApiResult<Boolean> updateStatus(@RequestBody @Valid DesignerStatusUpdateDto updateReqVO) {
        designerService.updateStatus(updateReqVO);
        return success(true);
    }

    /**
     * 根据业务id获取设计师下拉列表
     */
    @MethodDesc("根据业务id获取设计师下拉列表")
    @GetMapping("/getDesignerDropdownByBusinessId/{businessId}")
    public ApiResult getDesignerDropdownByBusinessId(@PathVariable("businessId") Long businessId) {
        if(businessId == null){
            return ApiResult.error("业务ID不能为空");
        }
        return ApiResult.ok(designerService.getDesignerDropdownByBusinessId(businessId));
    }

    /**
     * 品牌商设计师下拉列表
     */
    @MethodDesc("品牌商设计师下拉列表")
    @GetMapping("/getBrandDesignerDropdown")
    public ApiResult getBrandDesignerDropdown() {
        return ApiResult.ok(designerService.getBrandDesignerDropdown());
    }

    /**
     * 根据品牌商id获取品牌商下所有设计师（姓名+擅长业务+手机号）
     */
    @GetMapping("/getBrandDesignerForBrand")
    public ApiResult getBrandDesignerForBrand(@RequestParam(value = "brandId", required = false) Long brandId) {
        return ApiResult.ok(designerService.getBrandDesignerForBrand(brandId));
    }

    /**
     * 根据业务id获取品牌商下所有设计师
     */
    @MethodDesc("根据业务id获取品牌商下所有设计师")
    @GetMapping("/getBrandDesignerByBusinessId/{businessId}")
    public ApiResult getBrandDesignerByBusinessId(@PathVariable("businessId") Long businessId) {
        if(businessId == null){
            return ApiResult.error("业务ID不能为空");
        }
        return ApiResult.ok(designerService.getBrandDesignerByBusinessId(businessId));
    }

    @MethodDesc("品牌商设计师列表统计")
    @GetMapping("/statistics")
    public ApiResult statistics(){
        return success(designerService.statistics());
    }

    @MethodDesc("加盟商设计师列表统计")
    @GetMapping("/franchiseeStatistics")
    public ApiResult franchiseeStatistics(){
        return success(designerService.franchiseeStatistics());
    }
}
