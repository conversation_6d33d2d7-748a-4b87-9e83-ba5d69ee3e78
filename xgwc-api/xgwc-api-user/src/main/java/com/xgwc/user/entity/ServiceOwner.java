package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class ServiceOwner {

private static final long serialVersionUID=1L;

    /** 公司名称 */
    private String companyName;

    /** 联系人 */
    private String contact;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 服务商id */
    private Long id;

    /** 是否删除：0正常，1删除 */
    private Long isDel;

    /** 管理员手机号 */
    private String managerPhone;

    /** 行更新时间 */
    private Date modifyTime;

    /** 密码 */
    private String password;

    /** 服务商类型：1：财务服务商 */
    private Integer serviceType;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 管理员用户ID */
    private Long userId;



}