package com.xgwc.user.dao;

import com.xgwc.user.entity.dto.AgreementDto;
import com.xgwc.user.entity.vo.AgreementVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BrandAgreementRecordMapper {

    /**
     * 插入协议
     * @param userId 用户id
     * @param agreementId 协议id
     * @return 是否成功
     */
    int insertAgreementRecord(@Param(value = "agreementId") Integer agreementId, @Param(value = "userId") Long userId);

    /**
     * 根据用户id获取已读的协议
     * @param userId 用户id
     * @return 协议id列表
     */
    List<Integer> selectAgreementIdByUserId(Long userId);
}
