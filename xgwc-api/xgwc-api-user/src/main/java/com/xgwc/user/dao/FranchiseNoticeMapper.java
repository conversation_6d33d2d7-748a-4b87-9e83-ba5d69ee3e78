package com.xgwc.user.dao;

import com.xgwc.user.entity.dto.FranchiseNoticeDto;
import com.xgwc.user.entity.dto.notice.NoticeClassfyDto;
import com.xgwc.user.entity.vo.FranchiseNoticeClassifyVo;
import com.xgwc.user.entity.vo.FranchiseNoticeVo;
import com.xgwc.user.entity.vo.SelectFranchiseNoticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FranchiseNoticeMapper {

    /**
     * 插入通知分类
     * @param noticeClassifyVo 参数
     * @return 是否成功
     */
    int insertNoticeClassify(FranchiseNoticeClassifyVo noticeClassifyVo);

    /**
     * 修改分类(where条件中加上加盟商ID，防止胡乱修改)
     * @param noticeClassifyVo 参数
     * @return 是否成功
     */
    int updateNoticeClassify(FranchiseNoticeClassifyVo noticeClassifyVo);

    /**
     * 修改状态
     * @param noticeClassifyVo 参数
     * @return 是否成功
     */
    int updateStatus(FranchiseNoticeClassifyVo noticeClassifyVo);

    /**
     * 获取通知分类列表
     * @param noticeClassifyVo 参数
     * @return 通知分类列表
     */
    List<NoticeClassfyDto> getNoticeClassifyList(FranchiseNoticeClassifyVo noticeClassifyVo);


    /**
     * 查找分类名称
     * @param ids id列表
     * @return 通知分类列表
     */
    List<NoticeClassfyDto> getNoticeClassifyListByIds(List<Integer> ids);
    /**
     * 插入通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int insertFranchiseNotice(FranchiseNoticeVo noticeVo);

    /**
     * 更新通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int updateFranchiseNotice(FranchiseNoticeVo noticeVo);

    /**
     * 更新已发送通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int updateSendedFranchiseNotice(FranchiseNoticeVo noticeVo);

    /**
     * 删除通用
     * @param noticeVo 参数
     * @return 是否成功
     */
    int deleteFranchiseNotice(FranchiseNoticeVo noticeVo);

    /**
     * 根据通知id获取通知详情
     * @param id 通知id
     * @param franchiseId 加盟商ID
     * @return 通知详情
     */
    FranchiseNoticeDto getFranchiseNoticeById(@Param(value = "id") Integer id, @Param(value = "franchiseId") Long franchiseId);
    /**
     * 获取列表
     * @param selectFranchiseNoticeVo 参数
     * @return 列表
     */
    List<FranchiseNoticeDto> getFranchiseNoticeList(SelectFranchiseNoticeVo selectFranchiseNoticeVo);

    /**
     * 批量插入通知详情
     * @param userIds 用户id
     * @param noticeId 通知id
     * @return 是否成功
     */
    int batchInsertNoticeDetails(@Param(value = "userIds") List<Long> userIds, @Param(value = "noticeId") Integer noticeId);

    /**
     * 更新读取状态
     * @param noticeId 通知id
     * @return 是否成功
     */
    int updateReadStatus(@Param(value = "noticeId") Integer noticeId, @Param(value = "userId") Long userId);

    /**
     * 更新已读数量
     * @param noticeId 通知id
     * @return 是否成功
     */
    int updateReadCount(Integer noticeId);
}
