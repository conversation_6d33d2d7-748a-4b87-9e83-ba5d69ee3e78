package com.xgwc.user.controller;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.StringUtil;
import com.xgwc.user.service.VideoService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("video")
public class VideoController {

    @Resource
    private VideoService videoService;

    /**
     * 根据视频ID获取视频信息
     */
    @GetMapping("getVideoById")
    public ApiResult getVideoById(Integer videoId) {
        if(videoId == null) {
            return ApiResult.error("参数为空");
        }
        return ApiResult.ok(videoService.getUploadVideoRecord(videoId));
    }

    /**
     * 根据视频ID获取视频信息
     */
    @GetMapping("getVideoByIds")
    public ApiResult getVideoByIds(String videoIds) {
        if(StringUtil.isEmpty(videoIds)) {
            return ApiResult.error("参数为空");
        }
        return ApiResult.ok(videoService.getUploadVideoRecordList(videoIds));
    }
}
