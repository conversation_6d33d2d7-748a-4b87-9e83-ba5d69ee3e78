package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 加盟商管理角色
 */
@Data
public class FranchiseRoleQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("数据范围（1：全部数据 2：本部门数据 3：本人数据）")
    private Long dataScope;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;

    @FieldDesc("角色id")
    private Long roleId;

    @FieldDesc("角色名称")
    private String roleName;

    @FieldDesc("排序：越小越前")
    private Long sort;

    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;



}
