package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.SysDictData;
import com.xgwc.user.entity.vo.SysDictDataVo;
import com.xgwc.user.entity.dto.SysDictDataDto;
import com.xgwc.user.entity.vo.SysDictDataQueryVo;

public interface ISysDictDataService  {
    /**
     * 查询字典数据
     * 
     * @param dictCode 字典数据主键
     * @return 字典数据
     */
    public SysDictDataDto selectSysDictDataByDictCode(Long dictCode);

    /**
     * 查询字典数据列表
     * 
     * @param sysDictData 字典数据
     * @return 字典数据集合
     */
    public List<SysDictDataDto> selectSysDictDataList(SysDictDataQueryVo sysDictData);

    public List<SysDictDataDto> selectSysDictDataByType(String dictType,Long brandId);

    /**
     * 新增字典数据
     * 
     * @param sysDictData 字典数据
     * @return 结果
     */
    public int insertSysDictData(SysDictDataVo sysDictData);

    /**
     * 修改字典数据
     * 
     * @param sysDictData 字典数据
     * @return 结果
     */
    public int updateSysDictData(SysDictDataVo sysDictData);

    public int updateStatus(Long[] ids,Integer status);

    /**
     * 批量删除字典数据
     * 
     * @param dictCodes 需要删除的字典数据主键集合
     * @return 结果
     */
    public int deleteSysDictDataByDictCodes(Long[] dictCodes);

    /**
     * 删除字典数据信息
     * 
     * @param dictCode 字典数据主键
     * @return 结果
     */
    public int deleteSysDictDataByDictCode(Long dictCode);
}
