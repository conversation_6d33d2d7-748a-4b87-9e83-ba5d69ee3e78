package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysRoleQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("角色ID")
    private Long id;

    @FieldDesc("角色名称")
    private String name;

    @FieldDesc("角色权限字符串")
    private String code;

    @FieldDesc("显示顺序")
    private Long sort;

    @FieldDesc("角色状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("角色类型")
    private Integer type;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("是否删除")
    private Integer isDel;


    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;



}
