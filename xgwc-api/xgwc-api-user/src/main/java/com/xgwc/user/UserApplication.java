package com.xgwc.user;

import com.xgwc.order.feign.api.OrderFeign;
import com.xgwc.order.feign.api.ShopFranchiseFeign;
import com.xgwc.serviceProvider.feign.api.ServiceRoleFeign;
import com.xgwc.serviceProvider.feign.api.ServiceStaffFeign;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.xgwc.*"})
@ComponentScan("com.xgwc")
@MapperScan("com.xgwc.user.dao")
@EnableFeignClients(clients = {OrderFeign.class, ShopFranchiseFeign.class, ServiceStaffFeign.class, ServiceRoleFeign.class})
@EnableScheduling
@EnableRetry
@EnableAsync
@Slf4j
public class UserApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserApplication.class, args);
        log.info("项目启动成功!");
    }
}