package com.xgwc.user.entity.dto;
import lombok.Data;

import java.util.Date;

/**
 * 用户表
 */
@Data
public class SysUserRegisterDTO {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 启用状态，0:禁用 1:启用
     */
    private Integer status;

    /**
     * 删除标志（0 代表存在 2 代表删除）
     */
    private Integer delFlag;

    /**
     * 最后登陆IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 微信id
     */
    private String wechatOpenid;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 行修改时间
     */
    private Date modifyTime;

    /**
     * 密码修改时间
     */
    private Date passwordModifyTime;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 用户类型（0：普通用户，1：品牌商，2：加盟商，3：设计师，4:品牌商员工，5：加盟商员工）
     */
    private Integer userType;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 品牌商ID
     */
    private Long brandId;

}