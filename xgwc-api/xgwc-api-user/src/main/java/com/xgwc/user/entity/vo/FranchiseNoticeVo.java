package com.xgwc.user.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class FranchiseNoticeVo {
    /**
     * 通知分类ID
     */
    private Integer id;

    /**
     * 通知类型：1站内信，2短信，3企微
     */
    @NotNull(message = "通知渠道不能为空")
    private Integer noticeType;

    /**
     * 分类id
     */
    @NotNull(message = "通知分类不能为空")
    private Integer classifyId;

    /**
     * 标题
     */
    @NotNull(message = "标题不能为空")
    private String title;

    /**
     * 内容
     */
    @NotNull(message = "通知内容不能为空")
    private String content;

    /**
     * 发送数量
     */
    private Integer sendCount;

    /**
     * 发送人 json 格式 [{"deptId": 1, "count":30}, {"deptId": 2, "count":4, "userIds":"1,2,3,4"}]
     */
    @NotNull(message = "发送人不能为空")
    private String sendGroup;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 附件数量
     */
    private Integer attachmentCount;

    /**
     * 状态：0正常，1非正常
     */
    private Integer status;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 加盟商id
     */
    private Long franchiseId;

    /**
     * 定时时间
     */
    private String scheduleTime;

    /**
     * 发送状态：0 未发送，1发送中，2.已发送，3.发送失败
     */
    private Integer sendStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 发送人用户id
     */
    private List<Long> userIds;

}
