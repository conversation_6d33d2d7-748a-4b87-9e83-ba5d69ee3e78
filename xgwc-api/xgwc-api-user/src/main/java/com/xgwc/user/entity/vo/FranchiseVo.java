package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class FranchiseVo {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @FieldDesc("id")
    private Long id;

    /**
     * 管理员名称
     */
    @FieldDesc("管理员名称")
    private String managerName;

    /**
     * 加盟商名称
     */
    @FieldDesc("加盟商名称")
    private String franchiseName;

    /**
     * 管理员手机号
     */
    @FieldDesc("管理员手机号")
    private String managerPhone;

    /**
     * 管理员用户id
     */
    @FieldDesc("管理员用户id")
    private Long managerUserId;

    /**
     * 状态：0正常，其余非正常
     */
    @FieldDesc("状态：0正常，其余非正常")
    private Integer status;

    /**
     * 创建人
     */
    @FieldDesc("创建人")
    private String createBy;

}
