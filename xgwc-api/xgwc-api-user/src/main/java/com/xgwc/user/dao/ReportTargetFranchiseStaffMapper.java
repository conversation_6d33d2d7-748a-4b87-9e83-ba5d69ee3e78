package com.xgwc.user.dao;

import com.xgwc.user.entity.ReportTargetFranchiseStaff;
import com.xgwc.user.entity.dto.ReportTargetFranchiseStaffDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReportTargetFranchiseStaffMapper {


    /**
     * 批量写入
     */
    int batchInsertReportTargetFranchiseDept(List<ReportTargetFranchiseStaff> reportTargetFranchiseStaffs);

    /**
     * 修改个人目标
     * @param reportTargetFranchiseStaff 参数
     * @return 是否成功
     */
    int updateReportTargetFranchiseStaff(ReportTargetFranchiseStaff reportTargetFranchiseStaff);


    /**
     * 根据部门id和目标id获取员工目标情况
     * @param targetId 目标id
     * @param deptId 员工id
     * @return 员工信息
     */
    List<ReportTargetFranchiseStaffDto> getReportTargetFranchiseStaffByTargetId(@Param(value = "targetId") Long targetId, @Param(value = "deptId") Long deptId);

    /**
     * 根据目标id移除目标
     * @param targetId 目标id
     * @return 是否成功
     */
    int deleteByTargetId(Long targetId);

}
