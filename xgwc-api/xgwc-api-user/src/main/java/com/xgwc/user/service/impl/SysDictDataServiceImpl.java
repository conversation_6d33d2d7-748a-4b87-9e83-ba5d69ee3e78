package com.xgwc.user.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.SysDictTypeMapper;
import com.xgwc.user.entity.SysDictType;
import com.xgwc.user.entity.dto.SysDictTypeDto;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.SysDictDataMapper;
import com.xgwc.user.service.ISysDictDataService;
import com.xgwc.user.entity.SysDictData;
import com.xgwc.user.entity.vo.SysDictDataVo;
import com.xgwc.user.entity.dto.SysDictDataDto;
import com.xgwc.user.entity.vo.SysDictDataQueryVo;


@Service
public class SysDictDataServiceImpl implements ISysDictDataService  {
    @Resource
    private SysDictDataMapper sysDictDataMapper;

    @Resource
    private SysDictTypeMapper sysDictTypeMapper;

    /**
     * 查询字典数据
     * 
     * @param dictCode 字典数据主键
     * @return 字典数据
     */
    @Override
    public SysDictDataDto selectSysDictDataByDictCode(Long dictCode) {
        return sysDictDataMapper.selectSysDictDataByDictCode(dictCode);
    }

    /**
     * 查询字典数据列表
     * 
     * @param sysDictData 字典数据
     * @return 字典数据
     */
    @Override
    public List<SysDictDataDto> selectSysDictDataList(SysDictDataQueryVo sysDictData) {
        SysDictTypeDto dictTypeDto = sysDictTypeMapper.selectSysDictTypeByDictValue(sysDictData.getDictType());
        if(dictTypeDto.getDictData().equals("2")){
            if(SecurityUtils.getSysUser().getBrandId() == null) {
                throw new ApiException("非品牌商不允许使用");
            }
            sysDictData.setBrandId(SecurityUtils.getSysUser().getBrandId());
        }
        return sysDictDataMapper.selectSysDictDataList(sysDictData);
    }

    @Override
    public List<SysDictDataDto> selectSysDictDataByType(String dictType,Long brandId) {
        return sysDictDataMapper.selectSysDictDataByType(dictType,brandId);
    }

    /**
     * 新增字典数据
     * 
     * @param dto 字典数据
     * @return 结果
     */
    @Override
    public int insertSysDictData(SysDictDataVo dto) {
        SysDictTypeDto dictTypeDto = sysDictTypeMapper.selectSysDictTypeByDictValue(dto.getDictType());
        Long brandId = null;
        if(dictTypeDto.getDictData().equals("2")){
            if(SecurityUtils.getSysUser().getBrandId() == null) {
                throw new ApiException("非品牌商不允许新增");
            }
            brandId = SecurityUtils.getSysUser().getBrandId();
        }
        List<SysDictDataDto> list = sysDictDataMapper.selectSysDictDataByType(dto.getDictType(),brandId);
        if(list != null) {
            long index = list.stream().filter(l -> l.getDictLabel().equals(dto.getDictLabel()) ).count();
            if(index > 0) throw new ApiException("字典标签已存在");
            index = list.stream().filter(l -> l.getDictValue().equals(dto.getDictValue()) ).count();
            if(index > 0) throw new ApiException("字典键值已存在");
        }
        SysDictData sysDictData = BeanUtil.copyProperties(dto, SysDictData.class);
        sysDictData.setCreateTime(DateUtils.getNowDate());
        sysDictData.setCreateBy(SecurityUtils.getNickName());
        if(dictTypeDto.getDictData().equals("2")){
            sysDictData.setBrandId(SecurityUtils.getSysUser().getBrandId());
        }
        return sysDictDataMapper.insertSysDictData(sysDictData);
    }

    /**
     * 修改字典数据
     * 
     * @param dto 字典数据
     * @return 结果
     */
    @Override
    public int updateSysDictData(SysDictDataVo dto) {
        SysDictTypeDto dictTypeDto = sysDictTypeMapper.selectSysDictTypeByDictValue(dto.getDictType());
        Long brandId = null;
        if(dictTypeDto.getDictData().equals("2")){
            if(SecurityUtils.getSysUser().getBrandId() == null) {
                throw new ApiException("非品牌商不允许修改");
            }
            brandId = SecurityUtils.getSysUser().getBrandId();
        }
        List<SysDictDataDto> list = sysDictDataMapper.selectSysDictDataByType(dto.getDictType(), brandId);
        if(list != null) {
            long index = list.stream().filter(l -> l.getDictLabel().equals(dto.getDictLabel()) && l.getDictCode() != dto.getDictCode() ).count();
            if(index > 0) throw new ApiException("字典标签已存在");
            index = list.stream().filter(l -> l.getDictValue().equals(dto.getDictValue()) && l.getDictCode() != dto.getDictCode() ).count();
            if(index > 0) throw new ApiException("字典键值已存在");
        }
        SysDictData sysDictData = BeanUtil.copyProperties(dto, SysDictData.class);
        sysDictData.setUpdateTime(DateUtils.getNowDate());
        sysDictData.setUpdateBy(SecurityUtils.getNickName());
        return sysDictDataMapper.updateSysDictData(sysDictData);
    }

    @Override
    public int updateStatus(Long[] ids, Integer status) {
        return sysDictDataMapper.updateStatus(ids, status);
    }

    /**
     * 批量删除字典数据
     * 
     * @param dictCodes 需要删除的字典数据主键
     * @return 结果
     */
    @Override
    public int deleteSysDictDataByDictCodes(Long[] dictCodes) {
        return sysDictDataMapper.deleteSysDictDataByDictCodes(dictCodes);
    }

    /**
     * 删除字典数据信息
     * 
     * @param dictCode 字典数据主键
     * @return 结果
     */
    @Override
    public int deleteSysDictDataByDictCode(Long dictCode) {
        return sysDictDataMapper.deleteSysDictDataByDictCode(dictCode);
    }
}
