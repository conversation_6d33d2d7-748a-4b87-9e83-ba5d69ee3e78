package com.xgwc.user.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.XgwcCompanyInfoDto;
import com.xgwc.user.entity.param.XgwcBrandRoleParam;
import com.xgwc.user.entity.vo.XgwcCompanyInfoVo;
import com.xgwc.user.service.XgwcCompanyInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-07-04  09:46
 */


/**
 * 公司主体
 */
@RestController
@RequestMapping("/companyInfo")
@Slf4j
public class XgwcCompanyInfoController extends BaseController {

    @Resource
    private XgwcCompanyInfoService xgwcCompanyInfoService;


    /**
     * 获取公司主体列表
     *
     * @return 列表
     */
    @MethodDesc("获取公司主体列表")
    @PreAuthorize("@ss.hasPermission('companyInfo:companyInfo:list')")
    @GetMapping("/list")
    public ApiResult getCompanyInfoList(XgwcCompanyInfoVo xgwcCompanyInfoVo) {
        startPage();
        Integer isFlag = xgwcCompanyInfoVo.getIsFlag();
        xgwcCompanyInfoVo.setIsFlag(isFlag == null ? 1 : isFlag);
        return getDataTable(xgwcCompanyInfoService.getCompanyInfoList(xgwcCompanyInfoVo));
    }

    @MethodDesc("获取公司主体下拉框")
    @GetMapping("/getCompanyInfoDropDown")
    public ApiResult getCompanyInfoSelect(@RequestParam(value = "brandId", required = false) Long brandId,
                                          @RequestParam(value = "franchiseId", required = false) Long franchiseId,
                                          @RequestParam(value = "isFlag", required = false) Integer isFlag) {
        try {
            XgwcCompanyInfoVo xgwcCompanyInfoVo = new XgwcCompanyInfoVo();
            xgwcCompanyInfoVo.setStatus(1);
            xgwcCompanyInfoVo.setBrandId(brandId);
            xgwcCompanyInfoVo.setFranchiseId(franchiseId);
            xgwcCompanyInfoVo.setIsFlag(isFlag == null ? 1 : isFlag);
            List<XgwcCompanyInfoDto> result = xgwcCompanyInfoService.getCompanyInfoList(xgwcCompanyInfoVo);

            List<XgwcCompanyInfoDto> safeResult = Optional.ofNullable(result).orElse(Collections.emptyList());

            return ApiResult.ok(safeResult);
        } catch (Exception e) {
            log.error("获取公司主体失败", e);
            return ApiResult.error( "获取公司主体失败");
        }
    }

    /**
     * 新增公司主体信息
     *
     * @param xgwcCompanyInfoVo 新增公司主体信息
     * @return 插入结果
     */
    @MethodDesc("新增公司主体信息")
    @PreAuthorize("@ss.hasPermission('companyInfo:companyInfo:add')")
    @Submit(fileds = "userId")
    @PostMapping("/saveCompanyInfo")
    public ApiResult saveCompanyInfo(@RequestBody XgwcCompanyInfoVo xgwcCompanyInfoVo) {
        return xgwcCompanyInfoService.saveXgwcCompanyInfo(xgwcCompanyInfoVo);
    }

    /**
     * 根据id查询公司主体信息
     *
     * @param companyId 公司主体id
     * @return 公司主体信息
     */
    @MethodDesc("根据id查询公司主体信息")
    @PreAuthorize("@ss.hasPermission('companyInfo:companyInfo:query')")
    @GetMapping("/getCompanyInfoById/{companyId}")
    public ApiResult getCompanyInfoById(@PathVariable Long companyId) {
        return xgwcCompanyInfoService.getXgwcCompanyById(companyId);
    }

    /**
     * 修改公司主体信息
     *
     * @param xgwcCompanyInfoVo 修改信息
     * @return 修改结果
     */
    @MethodDesc("修改公司主体信息")
    @PreAuthorize("@ss.hasPermission('companyInfo:companyInfo:edit')")
    @Submit(fileds = "userId")
    @PostMapping("/updateCompanyInfo")
    public ApiResult updateCompanyInfo(@RequestBody XgwcCompanyInfoVo xgwcCompanyInfoVo) {
        return xgwcCompanyInfoService.updateXgwcCompanyById(xgwcCompanyInfoVo);
    }

    /**
     * 根据id修改状态
     * 
     * @param companyId 公司主体id
     * @return 公司主体信息
     */
    @MethodDesc("修改公司主体状态")
    @PreAuthorize("@ss.hasPermission('companyInfo:companyInfo:updateStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "companyId") Long companyId,
                                      @RequestParam(value = "status") Integer status) {
        return xgwcCompanyInfoService.updateStatusById(companyId,status);
    }
}
