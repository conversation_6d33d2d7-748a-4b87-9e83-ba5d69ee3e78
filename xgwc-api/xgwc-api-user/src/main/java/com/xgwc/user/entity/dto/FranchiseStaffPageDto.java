package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class FranchiseStaffPageDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("用户id")
    private Long userId;

    @FieldDesc("姓名")
    @Excel(name = "姓名")
    private String name;

    @FieldDesc("花名")
    @Excel(name = "花名")
    private String stageName;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    @Excel(name = "工作性质：0全职，1外包，2兼职")
    private Integer jobNature;

    @FieldDesc("状态：0在职，1离职")
    @Excel(name = "状态：0在职，1离职")
    private Integer status;

    @FieldDesc("档案状态：0未录，1已录")
    @Excel(name = "档案状态：0未录，1已录")
    private Integer archiveStatus;

    @FieldDesc("绑定状态：0未绑，1已绑")
    @Excel(name = "绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("登录手机号")
    @Excel(name = "登录手机号")
    private String loginPhone;


    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("部门id")
    private String deptId;

    @FieldDesc("部门名称")
    @Excel(name = "部门名称")
    private String deptName;

    @FieldDesc("岗位名称")
    @Excel(name = "岗位名称")
    private String stationName;

    @FieldDesc("员工类型：0内部员工，1合作员工")
    private Integer staffType;

}
