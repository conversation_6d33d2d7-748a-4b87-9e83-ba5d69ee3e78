package com.xgwc.user.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.*;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoRequest;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoResponse;
import com.xgwc.user.config.AliyunOssConfig;
import com.xgwc.user.entity.dto.upload.UploadFileDto;
import com.xgwc.user.service.UploadService;
import com.xgwc.user.util.UploadUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.imaging.Imaging;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class UploadServiceImpl implements UploadService {

    @Resource
    private AliyunOssConfig aliyunOssConfig;

    /**
     * 上传文件
     */
    @Override
    public UploadFileDto uploadFile(MultipartFile file) {
        UploadFileDto uploadFileDto = new UploadFileDto();
        String fileName = UploadUtil.buildFileName(file);
        try {
            InputStream inputStream = file.getInputStream();
            // 分块大小设置为 5MB（可以根据需要调整）
            long partSize = 5 * 1024 * 1024;
            // 调用工具类上传文件
            boolean isSuccess = multiPartuploadFile(inputStream, fileName, partSize);
            if(isSuccess){
                uploadFileDto.setUrl(fileName);
                uploadFileDto.setHost(aliyunOssConfig.getHost());
                return uploadFileDto;
            }
        }catch (Exception e){
            log.error("上传文件失败, error:", e);
        }
        return null;
    }

    /**
     * 上传图片
     */
    @Override
    public UploadFileDto uploadPic(MultipartFile file){
        UploadFileDto uploadFileDto = new UploadFileDto();
        try {
            BufferedImage sourceImg;
            if (file.getOriginalFilename() != null && file.getOriginalFilename().endsWith("gif") || file.getOriginalFilename().endsWith("GIF")) {
                sourceImg = Imaging.getBufferedImage(file.getInputStream());
            } else {
                sourceImg = ImageIO.read(file.getInputStream());
            }
            uploadFileDto.setWidth(sourceImg.getWidth());
            uploadFileDto.setHeight(sourceImg.getHeight());
            String fileName = UploadUtil.buildFileName(file);
            boolean isSuccess = uploadFile(file, fileName);
            if(isSuccess){
                uploadFileDto.setHost(aliyunOssConfig.getHost());
                uploadFileDto.setUrl(fileName);
                return uploadFileDto;
            }
        }catch (Exception e){
            log.error("上传图片失败:", e);
        }
        return null;
    }

    // 获取视频上传地址和凭证
    public static CreateUploadVideoResponse createUploadVideo(DefaultAcsClient client, String title,
                                                              String fileName) throws Exception {
        CreateUploadVideoRequest request = new CreateUploadVideoRequest();
        request.setTitle(title);
        request.setFileName(fileName);
        request.setCateId(0L); // 分类ID，0表示默认分类
        request.setTags("tag1,tag2"); // 视频标签
        request.setDescription("视频描述");
        request.setCoverURL("http://cover.example.com/image.jpg"); // 封面URL
        return client.getAcsResponse(request);
    }

    /**
     * 适用于小文件上传
     */
    public boolean uploadFile(MultipartFile file ,String filePath)  {
        // 生成 OSSClient
        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessKeyId(), aliyunOssConfig.getAccessKeySecret());
        try {
            ossClient.putObject(aliyunOssConfig.getBucketName(), filePath, file.getInputStream());
            return true;
        } catch (IOException e) {
            log.error("上传阿里云失败:", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return false;
    }

    public boolean multiPartuploadFile(InputStream inputStream, String filePath, long partSize)  {
        // 生成 OSSClient
        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessKeyId(), aliyunOssConfig.getAccessKeySecret());
        try {
            // 初始化分块上传
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(aliyunOssConfig.getBucketName(), filePath);
            InitiateMultipartUploadResult initResult = ossClient.initiateMultipartUpload(initRequest);
            String uploadId = initResult.getUploadId();
            // 分块上传
            List<PartETag> partETags = new ArrayList<>();
            long partNumber = 1;
            byte[] buffer = new byte[(int) partSize];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                // 创建分块上传请求
                UploadPartRequest uploadRequest = new UploadPartRequest();
                uploadRequest.setBucketName(aliyunOssConfig.getBucketName());
                uploadRequest.setKey(filePath);
                uploadRequest.setUploadId(uploadId);
                uploadRequest.setPartNumber((int) partNumber);
                uploadRequest.setInputStream(new java.io.ByteArrayInputStream(buffer, 0, bytesRead));
                uploadRequest.setPartSize(bytesRead);

                // 上传分块
                PartETag partETag = ossClient.uploadPart(uploadRequest).getPartETag();
                partETags.add(partETag);
                partNumber++;
            }
            // 完成分块上传
            ossClient.completeMultipartUpload(new com.aliyun.oss.model.CompleteMultipartUploadRequest(aliyunOssConfig.getBucketName(), filePath, uploadId, partETags));
            return true;
        } catch (Exception e) {
            log.error("分片上传阿里云失败:", e);
        } finally {
            // 关闭OSSClient
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return false;
    }
}
