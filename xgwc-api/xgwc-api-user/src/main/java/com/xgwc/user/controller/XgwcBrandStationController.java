package com.xgwc.user.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.XgwcBrandStationDto;
import com.xgwc.user.entity.param.XgwcBrandStationParam;
import com.xgwc.user.entity.vo.XgwcBrandStationVo;
import com.xgwc.user.service.XgwcBrandStationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 组织管理-岗位管理
 */
@RestController
@RequestMapping("/xgwcBrandStation")
@Slf4j
public class XgwcBrandStationController extends BaseController {

    @Resource
    private XgwcBrandStationService xgwcBrandStationService;

    /**
     * @param xgwcBrandStationParam 查询条件
     * @return 岗位管理列表
     * 查询岗位管理列表
     */
    @MethodDesc("查询岗位管理列表")
    @PreAuthorize("@ss.hasPermission('user:station:list')")
    @PostMapping("/getXgwcBrandStationList")
    public ApiResult<XgwcBrandStationVo> getXgwcBrandStationList(@RequestBody XgwcBrandStationParam xgwcBrandStationParam) {
        startPage();
        return getDataTable(xgwcBrandStationService.getXgwcBrandStationList(xgwcBrandStationParam));
    }

    /**
     * 获取岗位管理下拉列表
     *
     * @return 岗位管理下拉列表
     */
    @MethodDesc("获取岗位管理下拉列表")
    @GetMapping("/getBrandStationDropDown")
    public ApiResult getXgwcBrandStationDropDown(@RequestParam(required = false) Integer companyId) {
        try {
            XgwcBrandStationParam xgwcBrandStationParam = new XgwcBrandStationParam();
            xgwcBrandStationParam.setCompanyId(companyId);
            xgwcBrandStationParam.setStatus(0);
            List<XgwcBrandStationVo> result = xgwcBrandStationService.getXgwcBrandStationList(xgwcBrandStationParam);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取岗位管理下拉列表失败", e);
            return ApiResult.error("获取岗位管理下拉列表失败");
        }
    }

    /**
     * @param xgwcBrandStationDto 新增岗位管理信息
     * @return 插入结果
     * 新增岗位管理信息
     */
    @MethodDesc("新增岗位管理信息")
    @PreAuthorize("@ss.hasPermission('user:station:insert')")
    @Submit(fileds = "userId")
    @PostMapping("/saveXgwcBrandStation")
    public ApiResult saveXgwcBrandStation(@RequestBody XgwcBrandStationDto xgwcBrandStationDto) {
        return xgwcBrandStationService.saveXgwcBrandStation(xgwcBrandStationDto);
    }

    /**
     * @param stationId 岗位管理id
     * @return 岗位管理信息
     * 根据id查询岗位管理信息
     */
    @MethodDesc("根据id查询岗位管理信息")
    @PreAuthorize("@ss.hasPermission('user:station:update')")
    @GetMapping("/getXgwcBrandStationById/{stationId}")
    public ApiResult getXgwcBrandStationById(@PathVariable Integer stationId) {
        return xgwcBrandStationService.getXgwcBrandStationById(stationId);
    }

    /**
     * @param xgwcBrandStationDto 修改信息
     * @return 修改结果
     * 修改岗位管理信息
     */
    @MethodDesc("修改岗位管理信息")
    @PreAuthorize("@ss.hasPermission('user:station:update')")
    @Submit(fileds = "userId")
    @PostMapping("/updateXgwcBrandStation")
    public ApiResult updateXgwcBrandStation(@RequestBody XgwcBrandStationDto xgwcBrandStationDto) {
        return xgwcBrandStationService.updateXgwcBrandStation(xgwcBrandStationDto);
    }

    /**
     * @param stationId 岗位id
     * @return 岗位信息
     * 根据id修改状态
     */
    @MethodDesc("修改岗位状态")
    @PreAuthorize("@ss.hasPermission('user:station:updateStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "stationId") Integer stationId,
                                 @RequestParam(value = "status") Integer status) {
        return xgwcBrandStationService.updateStatusById(stationId,status);
    }

}
