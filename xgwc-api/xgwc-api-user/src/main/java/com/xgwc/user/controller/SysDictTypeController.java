package com.xgwc.user.controller;

import java.util.List;

import com.xgwc.user.entity.vo.SysDictTypeQuery2Vo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.vo.SysDictTypeVo;
import com.xgwc.user.entity.dto.SysDictTypeDto;
import com.xgwc.user.entity.vo.SysDictTypeQueryVo;
import com.xgwc.user.service.ISysDictTypeService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/user/dictType")
public class SysDictTypeController extends BaseController {
    @Autowired
    private ISysDictTypeService sysDictTypeService;

    /**
     * 查询字典管理列表
     */
    @MethodDesc("查询字典管理列表")
    @PreAuthorize("@ss.hasPermission('user:dictType:list')")
    @GetMapping("/list")
    public ApiResult<SysDictTypeDto> list(SysDictTypeQueryVo sysDictType) {
        startPage();
        List<SysDictTypeDto> list = sysDictTypeService.selectSysDictTypeList(sysDictType);
        return getDataTable(list);
    }


    @MethodDesc("品牌商字典管理列表")
    @PreAuthorize("@ss.hasPermission('user:dictType:list')")
    @GetMapping("/list2")
    public ApiResult<SysDictTypeDto> list2(SysDictTypeQuery2Vo sysDictType) {
        startPage();
        List<SysDictTypeDto> list = sysDictTypeService.selectSysDictTypeList2(sysDictType);
        return getDataTable(list);
    }

    /**
     * 获取字典管理详细信息
     */
    @MethodDesc("获取字典管理详细信息")
    @GetMapping(value = "/{dictId}")
    public ApiResult<SysDictTypeDto> getInfo(@PathVariable("dictId") Long dictId) {
        return success(sysDictTypeService.selectSysDictTypeByDictId(dictId));
    }

    /**
     * 新增字典管理
     */
    @MethodDesc("新增字典管理")
    @PreAuthorize("@ss.hasPermission('user:dictType:edit')")
    @Log(title = "字典管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody SysDictTypeVo sysDictType) {
        return toAjax(sysDictTypeService.insertSysDictType(sysDictType));
    }

    /**
     * 修改字典管理
     */
    @MethodDesc("修改字典管理")
    @PreAuthorize("@ss.hasPermission('user:dictType:edit')")
    @Log(title = "字典管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody SysDictTypeVo sysDictType) {
        return toAjax(sysDictTypeService.updateSysDictType(sysDictType));
    }

    @MethodDesc("禁用/启用")
    @PreAuthorize("@ss.hasPermission('user:dictType:status')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{ids}")
    public ApiResult status(@PathVariable Long[] ids, @RequestBody Integer status) {
        return toAjax(sysDictTypeService.updateStatus(ids,status));
    }

    /**
     * 删除字典管理
     */
    @MethodDesc("删除字典管理")
    @PreAuthorize("@ss.hasPermission('user:dictType:remove')")
    @Log(title = "字典管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dictIds}")
    public ApiResult remove(@PathVariable Long[] dictIds) {
        return toAjax(sysDictTypeService.deleteSysDictTypeByDictIds(dictIds));
    }
}
