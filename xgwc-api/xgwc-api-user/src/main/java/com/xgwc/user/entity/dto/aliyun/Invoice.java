package com.xgwc.user.entity.dto.aliyun;

import lombok.Data;

@Data
public class Invoice {

    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 开票日期
     */
    private String invoiceDate;

    /**
     * 机器编码
     */
    private String machineCode;

    /**
     * 校验码
     */
    private String checkCode;

    /**
     * 受票方名称
     */
    private String purchaserName;

    /**
     * 密码区
     */
    private String passwordArea;

    /**
     * 不含税金额
     */
    private String invoiceAmountPreTax;

    /**
     * 发票税额
     */
    private String invoiceTax;

    /**
     * 大写金额
     */
    private String totalAmountInWords;

    /**
     * 发票金额
     */
    private String totalAmount;

    /**
     * 销售方名称
     */
    private String sellerName;

    /**
     * 销售方税号
     */
    private String sellerTaxNumber;

    /**
     * 销售方地址、电话
     */
    private String sellerContactInfo;

    /**
     * 销售方开户行、账号
     */
    private String sellerBankAccountInfo;

    /**
     * 开票人
     */
    private String drawer;

    /**
     * 标题
     */
    private String title;

    /**
     * 发票类型（电子普通发票、电子专用发票、专用发票、普通发票、通用发票）
     */
    private String invoiceType;

    /**
     * 联次
     */
    private String formType;

    /**
     * 机打发票代码
     */
    private String printedInvoiceCode;

    /**
     * 机打发票号码
     */
    private String printedInvoiceNumber;

    /**
     * 受票方开户行、账号
     */
    private String purchaserBankAccountInfo;

    /**
     * 受票方地址、电话
     */
    private String purchaserContactInfo;

    /**
     * 受票方税号
     */
    private String purchaserTaxNumber;

    /**
     * 收款人
     */
    private String recipient;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 复核人
     */
    private String reviewer;

    /**
     * 特殊标识信息
     */
    private String specialTag;

    /**
     * 发票详单
     */
    private String invoiceDetails;
}
