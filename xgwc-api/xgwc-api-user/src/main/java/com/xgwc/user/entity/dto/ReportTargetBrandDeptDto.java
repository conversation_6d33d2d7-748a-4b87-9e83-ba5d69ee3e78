package com.xgwc.user.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReportTargetBrandDeptDto {

    private Long targetDeptId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 加盟商名称
     */
    private String brandName;

    /**
     * 加盟商名称
     */
    private String franchiseName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 模板名称
     */
    private String targetName;

    /**
     * 目标日期
     */
    private String targetDate;

    /** 目标额度 */
    private BigDecimal targetAmount;

    /** 订单金额 */
    private BigDecimal amountOrder;

    /** 实收金额 */
    private BigDecimal amountReal;

    /** 新客目标 */
    private BigDecimal targetNewCustomer;

    /** 老客目标 */
    private BigDecimal targetOldCustomer;

    /** 转介绍目标 */
    private BigDecimal targetTransferAmount;

    /** 佣金比例目标 */
    private BigDecimal targetComissionRate;

    /** 新客转化率目标 */
    private BigDecimal targetConversionRate;

    /** 实际新客金额 */
    private BigDecimal newCustomerAmount;

    /** 实际老客金额 */
    private BigDecimal oldCustomerAmount;

    /** 转介绍金额 */
    private BigDecimal transferAmount;

    /** 实际转换率 */
    private BigDecimal conversionRate;

    /** 实际佣金比例 */
    private BigDecimal comissionRate;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改时间
     */
    private String updateTime;
}
