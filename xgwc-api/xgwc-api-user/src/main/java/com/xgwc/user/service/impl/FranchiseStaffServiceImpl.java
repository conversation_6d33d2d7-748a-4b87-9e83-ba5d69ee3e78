package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.*;
import com.xgwc.user.entity.FranchiseStaff;
import com.xgwc.user.entity.StaffLog;
import com.xgwc.user.entity.dto.FranchiseStaffDto;
import com.xgwc.user.entity.dto.FranchiseStaffPageDto;
import com.xgwc.user.entity.dto.FranchiseStaffSimpleDto;
import com.xgwc.user.entity.vo.FranchiseDeptVo;
import com.xgwc.user.entity.vo.FranchiseOwnerVO;
import com.xgwc.user.entity.vo.FranchiseStaffQueryVo;
import com.xgwc.user.entity.vo.FranchiseStaffVo;
import com.xgwc.user.feign.entity.FeignFranchiseStaffDto;
import com.xgwc.user.service.IFranchiseStaffService;
import com.xgwc.user.util.ChangeLogUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.sql.SQLException;
import java.sql.SQLTransientException;
import java.util.*;

import static com.xgwc.common.util.ParamDecryptUtil.PHONE_KEY;


@Service
@Slf4j
public class FranchiseStaffServiceImpl implements IFranchiseStaffService  {
    @Resource
    private FranchiseStaffMapper franchiseStaffMapper;
    @Resource
    private StaffLogMapper staffLogMapper;
    @Resource
    private XgwcBrandRoleMapper xgwcBrandRoleMapper;
    @Resource
    private FranchiseOwnerMapper franchiseOwnerService;
    @Resource
    private FranchiseDeptMapper franchiseDeptMapper;


    /**
     * 查询加盟商员工
     * 
     * @param id 加盟商员工主键
     * @return 加盟商员工
     */
    @Override
    public FranchiseStaffDto selectFranchiseStaffById(Long id) {
        return franchiseStaffMapper.selectFranchiseStaffById(id);
    }

    /**
     * 查询加盟商员工列表
     * 
     * @param franchiseStaff 加盟商员工
     * @return 加盟商员工
     */
    @Override
    public List<FranchiseStaffPageDto> selectFranchiseStaffList(FranchiseStaffQueryVo franchiseStaff) {
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        if(franchiseId == null) return List.of();
        if(StringUtils.isNotEmpty(franchiseStaff.getLoginPhone())){
            franchiseStaff.setLoginPhone(ParamDecryptUtil.encrypt(franchiseStaff.getLoginPhone(), PHONE_KEY));
        }
        if(franchiseStaff.getDeptId() != null){
            List<Long> allDeptIds = new ArrayList<>();
            collectChildDeptIds(franchiseStaff.getDeptId(), allDeptIds,franchiseId);
            franchiseStaff.setDeptIds(allDeptIds);
        }
        franchiseStaff.setFranchiseId(franchiseId);
        List<FranchiseStaffPageDto> list = franchiseStaffMapper.selectFranchiseStaffList(franchiseStaff);
        if(!list.isEmpty()){
            for(FranchiseStaffPageDto franchiseStaffDto : list){
                if(StringUtils.isNotEmpty(franchiseStaffDto.getLoginPhone())){
                    franchiseStaffDto.setLoginPhone(ParamDecryptUtil.decryptParam(franchiseStaffDto.getLoginPhone(), PHONE_KEY));
                }
            }
        }
        return list;
    }

    void collectChildDeptIds(Long parentId, List<Long> allIds,Long franchiseId) {
        allIds.add(parentId);
        List<FranchiseDeptVo> children = franchiseDeptMapper.getDeptByPid(parentId,franchiseId);
        for(FranchiseDeptVo child : children){
            collectChildDeptIds(child.getDeptId(), allIds,franchiseId);
        }
    }

    /**
     * 新增加盟商员工
     * 
     * @param dto 加盟商员工
     * @return 结果
     */
    @Override
    public int insertFranchiseStaff(FranchiseStaffVo dto) {
        FranchiseStaff franchiseStaff = BeanUtil.copyProperties(dto, FranchiseStaff.class);
        franchiseStaff.setCreateTime(DateUtils.getNowDate());
        franchiseStaff.setCreateBy(SecurityUtils.getNickName());
        return franchiseStaffMapper.insertFranchiseStaff(franchiseStaff);
    }

    /**
     * 修改加盟商员工
     * 
     * @param dto 加盟商员工
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateFranchiseStaff(FranchiseStaffVo dto) {
        FranchiseStaffDto oldStaff = franchiseStaffMapper.selectFranchiseStaffById(dto.getId());
        if (oldStaff == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        FranchiseStaff newStaff = BeanUtil.copyProperties(dto, FranchiseStaff.class);
        newStaff.setUpdateTime(DateUtils.getNowDate());
        newStaff.setUpdateBy(SecurityUtils.getNickName());
        int result = franchiseStaffMapper.updateFranchiseStaff(newStaff);
        if (result > 0) {
            StaffLog log = ChangeLogUtil.buildAggregatedLog(
                    oldStaff,
                    newStaff,
                    dto.getId(),
                    newStaff.getUpdateBy(),
                    newStaff.getUpdateTime(),
                    "修改员工"
            );
            if (log != null) {
                log.setBusinessType(2);
                log.setBusinessId(oldStaff.getFranchiseId());
                staffLogMapper.insertStaffLog(log);
            }
        }
        if(StringUtils.isNotEmpty(newStaff.getRoleIds()) && oldStaff.getBindUserId() != null){
            //删除旧的角色
            xgwcBrandRoleMapper.deleteBrandRoleUser(oldStaff.getBindUserId());
            String[] roleIds = newStaff.getRoleIds().split(",");
            for(String roleId : roleIds){
                xgwcBrandRoleMapper.insertBrandRoleUser(Long.valueOf(roleId), oldStaff.getBindUserId(),1);
            }
        }else if(oldStaff.getBindUserId() != null){
            //删除旧的角色
            xgwcBrandRoleMapper.deleteBrandRoleUser(oldStaff.getBindUserId());
        }
        return result;
    }

    /**
     * 批量删除加盟商员工
     * 
     * @param ids 需要删除的加盟商员工主键
     * @return 结果
     */
    @Override
    public int deleteFranchiseStaffByIds(Long[] ids) {
        return franchiseStaffMapper.deleteFranchiseStaffByIds(ids);
    }

    /**
     * 删除加盟商员工信息
     * 
     * @param id 加盟商员工主键
     * @return 结果
     */
    @Override
    public int deleteFranchiseStaffById(Long id) {
        validateStaffExists(id);
        return franchiseStaffMapper.deleteFranchiseStaffById(id);
    }

    @Override
    public FranchiseStaffDto selectFranchiseStaffByNameAndFranchiseId(String name,Long franchiseId) {
        return franchiseStaffMapper.findFranchiseStaffByNameAndFranchiseId(name,franchiseId);
    }

    @Override
    public int updateBindStatus(Long id, Long bindUserId, Integer bindStatus,String phone,String stageName) {
        validateStaffExists(id);
        return franchiseStaffMapper.updateBindStatus(id,bindUserId,bindStatus,phone,stageName);
    }

    /**
     * 获取加盟商员工下拉框
     * @return 加盟商员工
     */
    @Override
    public List<Map<String,Object>> selectFranchiseStaffListDropDown() {
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        List<FranchiseStaffDto> staffDtos = franchiseStaffMapper.selectFranchiseStaffListDropDown(franchiseId);
        List<Map<String, Object>> result = new ArrayList<>(staffDtos.size());

        for (FranchiseStaffDto staffDto : staffDtos) {
            String name = Optional.ofNullable(staffDto.getName()).orElse("");
            String stageName = Optional.ofNullable(staffDto.getStageName()).orElse("");

            String combinedName = name + "/" + stageName;

            Map<String, Object> item = new HashMap<>(2);
            item.put("id", staffDto.getId().toString());
            item.put("name", combinedName);

            result.add(item);
        }

        return result;
    }

    @Override
    public int updateFranchiseeStaffStatus(Long id, Integer status) {
        return franchiseStaffMapper.updateFranchiseeStaffStatus(id,status);
    }

    @Override
    public FranchiseStaffDto selectFranchiseStaffByBindUserId(Long bindUserId) {
        return franchiseStaffMapper.findFranchiseStaffByBindUserId(bindUserId);
    }

    private void validateStaffExists(Long id) {
        if (franchiseStaffMapper.selectFranchiseStaffById(id) == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
    }

    /**
     * 查询员工下载限制列表
     *
     * @param franchiseStaff 查询参数
     * @return 员工下载限制列表
     */
    @Override
    public List<FranchiseStaffDto> selectStaffDownloadLimitList(FranchiseStaffQueryVo franchiseStaff) {
        Long brandId = franchiseStaff.getBrandId();
        franchiseStaff.setBrandId(
                brandId != null
                        ? brandId
                        : SecurityUtils.getSysUser().getBrandId()
        );
        List<FranchiseStaffDto> franchiseStaffDtos = franchiseStaffMapper.selectStaffDownloadLimitList(franchiseStaff);

        if (CollectionUtils.isEmpty(franchiseStaffDtos)) {
            return Collections.emptyList();
        }

        for (FranchiseStaffDto staff : franchiseStaffDtos) {

            String name = getOrDefault(staff.getName());
            String stageName = getOrDefault(staff.getStageName());
            String franchiseShortName = getOrDefault(staff.getFranchiseShortName());
            String loginPhone = getOrDefault(staff.getLoginPhone());
            loginPhone = ParamDecryptUtil.decryptParam(loginPhone,PHONE_KEY);

            String concatenated =
                    name +
                    "-" +
                    stageName +
                    "-" +
                    loginPhone +
                    "-" +
                    franchiseShortName;
            staff.setName(concatenated);

            Integer maxRoleDateDownloadLimit = 0;
            if (!StringUtils.isEmpty(staff.getRoleIds())) {
                String roleIds = staff.getRoleIds();

                if (StringUtils.isNotEmpty(roleIds)) {
                    List<Integer> integerEoleIds = Arrays.stream(roleIds.split(","))
                            .map(String::trim)
                            .map(Integer::parseInt)
                            .toList();
                    FranchiseStaffDto franchiseStaffDto = franchiseStaffMapper.selectMaxRoleDateDownloadLimit(integerEoleIds);
                    if(!ObjectUtils.isEmpty(franchiseStaffDto)){
                        maxRoleDateDownloadLimit = franchiseStaffDto.getMaxRoleDateDownloadLimit();
                    }
                }
            }

            if (staff.getDownloadLimit()!=null && staff.getDownloadLimit()!= 0) {

            } else if(maxRoleDateDownloadLimit != 0){
                staff.setDownloadLimit(maxRoleDateDownloadLimit);
            } else {
                FranchiseOwnerVO franchiseOwnerVO = franchiseOwnerService.selectFranchiseOwnerById(staff.getFranchiseId());
                 staff.setDownloadLimit((franchiseOwnerVO != null && franchiseOwnerVO.getDownloadLimit() != 0)
                         ? franchiseOwnerVO.getDownloadLimit() : 0);
            }
        }
        return franchiseStaffDtos;
    }
    private String getOrDefault(String value) {
        return value != null ? value : "";
    }


    /**
     * 修改员工下载限制
     *
     * @param franchiseStaff 员工信息
     * @return 结果
     */
    @Override
    public ApiResult updateStaffDownloadLimit(FranchiseStaff franchiseStaff) {
        if (franchiseStaff == null || franchiseStaff.getId() == null) {
            return ApiResult.error("员工id不能为空");
        }

        try {
            franchiseStaff.setStaffId(franchiseStaff.getId());
            FranchiseStaffDto franchiseStaffDto = franchiseStaffMapper.selectFranchiseStaffById(franchiseStaff.getStaffId());
            if (franchiseStaffDto == null) {
                return ApiResult.error("员工不存在");
            }

            List<FranchiseStaffDto> franchiseStaffDownloadLimit = franchiseStaffMapper.
                    selectFranchiseStaffDownload(franchiseStaff.getId(), franchiseStaff.getBrandId());
            if (CollectionUtils.isEmpty(franchiseStaffDownloadLimit)) {
                franchiseStaff.setOperationTime(new Date());
                franchiseStaff.setId(null);

            }else {
                FranchiseStaffDto franchiseStaffDto1 = franchiseStaffDownloadLimit.get(0);
                franchiseStaff.setOperationTime(franchiseStaffDto1.getCreateTime());
                franchiseStaff.setId(franchiseStaffDto1.getId());
            }

            franchiseStaff.setLastOperation(SecurityUtils.getNickName());
            franchiseStaff.setBrandId(SecurityUtils.getSysUser().getBrandId());
            int updResult = franchiseStaffMapper.updateStaffDownloadLimit(franchiseStaff);
            if (updResult <= 0) {
                return ApiResult.error("修改失败");
            }
            return ApiResult.ok();
        } catch (Exception e) {
            return ApiResult.error("系统异常，请稍后再试");
        }
    }

    @Override
    public List<FranchiseStaffSimpleDto> dropDownList(Long deptId) {
        Long franchiseId = null;
        Integer userType = SecurityUtils.getSysUser().getUserType();
        //允许品牌商查询
        if(userType == 2 || userType == 5){
            franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        }
        return franchiseStaffMapper.findFranchiseStaffListDropDownByFranchiseId(franchiseId, deptId);
    }

    @Override
    @Scheduled(cron = "0 0 0 * * ?")
    @Retryable(value = {SQLTransientException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    public void refreshStaffDownloadCount() {
        log.info("开始刷新员工下载次数");

        int refreshedFranchiseStaff = franchiseStaffMapper.refreshFranchiseStaffDownloadCount();
        int refreshedBrandStaff = franchiseStaffMapper.refreshBrandStaffDownloadCount();

        log.info("刷新完成：franchise_staff 更新 {} 行，xgwc_staff 更新 {} 行",
                refreshedFranchiseStaff, refreshedBrandStaff);

        if (refreshedFranchiseStaff == 0 || refreshedBrandStaff == 0) {
            log.warn("刷新下载次数可能未生效：franchise_staff 更新 {} 行，xgwc_staff 更新 {} 行",
                    refreshedFranchiseStaff, refreshedBrandStaff);
        }
    }

    @Override
    public FranchiseStaffDto selectFranchiseStaffInfoByUserIdAndFranchiseId(Long userId, Long franchiseId) {
        return franchiseStaffMapper.findFranchiseStaffByUserIdAndFranchiseId(userId, franchiseId);
    }

    @Override
    public Long feignAddFranchiseStaff(FeignFranchiseStaffDto dto) {
        FranchiseStaff franchiseStaff = BeanUtil.copyProperties(dto, FranchiseStaff.class);
        franchiseStaffMapper.insertFranchiseStaff(franchiseStaff);
        return franchiseStaff.getId();
    }

    @Override
    public List<FranchiseStaffSimpleDto> selectStaffByDeptIdAndFranchiseId(Long deptId, Long franchiseId) {
        List<Long> allDeptIds = new ArrayList<>();
        collectChildDeptIds(deptId, allDeptIds,franchiseId);
        return franchiseStaffMapper.selectStaffByDeptIdsAndFranchiseId(allDeptIds,franchiseId);
    }


    @Recover
    public void recoverFromRefreshFailure(SQLException e) {
        log.error("刷新员工下载次数失败，已达到最大重试次数", e);
    }


}
