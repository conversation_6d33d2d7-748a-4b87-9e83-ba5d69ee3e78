package com.xgwc.user.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.feign.api.UserDeptFeign;
import com.xgwc.user.service.FranchiseDeptService;
import com.xgwc.user.service.XgwcBrandDeptService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class UserDeptApi implements UserDeptFeign {

    @Resource
    private XgwcBrandDeptService xgwcBrandDeptService;
    @Resource
    private FranchiseDeptService franchiseDeptService;


    @Override
    public ApiResult getUserDeptByDeptId(Long deptId, String deptType) {
        if("brand".equalsIgnoreCase(deptType)) {
            return ApiResult.ok(xgwcBrandDeptService.getXgwcBrandDeptById(deptId));
        } else if("franchise".equalsIgnoreCase(deptType)) {
            return ApiResult.ok(franchiseDeptService.getFranchiseDeptById(deptId));
        }
        return null;
    }

    @Override
    public ApiResult getUserDeptByUserId(Long userId, String deptType) {
        if("brand".equalsIgnoreCase(deptType)) {
            return ApiResult.ok(xgwcBrandDeptService.getUserDeptByUserId(userId));
        } else if("franchise".equalsIgnoreCase(deptType)) {
            return ApiResult.ok(franchiseDeptService.getUserDeptByUserId(userId));
        }
        return null;
    }

}
