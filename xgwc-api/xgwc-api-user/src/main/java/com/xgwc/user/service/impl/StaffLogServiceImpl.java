package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.util.DateUtils;
import com.xgwc.user.dao.StaffLogMapper;
import com.xgwc.user.entity.StaffLog;
import com.xgwc.user.entity.dto.StaffLogDto;
import com.xgwc.user.entity.vo.StaffLogQueryVo;
import com.xgwc.user.entity.vo.StaffLogVo;
import com.xgwc.user.service.IStaffLogService;
import com.xgwc.user.util.StaffLogParseUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class StaffLogServiceImpl implements IStaffLogService  {
    @Resource
    private StaffLogMapper staffLogMapper;

    /**
     * 查询员工日志列表
     * @param staffLog 员工日志
     * @return 员工日志
     */
    @Override
    public List<StaffLogDto> selectStaffLogList(StaffLogQueryVo staffLog) {
        return staffLogMapper.selectStaffLogList(staffLog);
    }

    /**
     * 新增员工日志
     * @param dto 员工日志
     * @return 结果
     */
    @Override
    public int insertStaffLog(com.xgwc.common.entity.StaffLog dto) {
        StaffLog staffLog = BeanUtil.copyProperties(dto, StaffLog.class);
        staffLog.setCreateTime(DateUtils.getNowDate());
        return staffLogMapper.insertStaffLog(staffLog);
    }

    /**
     * 修改员工日志
     * 
     * @param dto 员工日志
     * @return 结果
     */
    @Override
    public int updateStaffLog(StaffLogVo dto) {
        StaffLog staffLog = BeanUtil.copyProperties(dto, StaffLog.class);
        staffLog.setUpdateTime(DateUtils.getNowDate());
        return staffLogMapper.updateStaffLog(staffLog);
    }

    /**
     * 批量删除员工日志
     * @param ids 需要删除的员工日志主键
     * @return 结果
     */
    @Override
    public int deleteStaffLogByIds(Long[] ids) {
        return staffLogMapper.deleteStaffLogByIds(ids);
    }

    @Override
    public List<StaffLogDto> findStaffLogByStaffIdAndBusinessType(Long staffId, int businessType) {
        List<StaffLogDto> staffLogDtos = staffLogMapper.selectLogsByStaffIdAndBusinessType(staffId, businessType);
        if(!staffLogDtos.isEmpty()){
            for (StaffLogDto dto : staffLogDtos) {
                String parsed = StaffLogParseUtil.parseLogContentToReadable(dto.getRemark(),businessType);
                dto.setParsedRemark(parsed);
            }
        }
        return staffLogDtos;
    }
}
