package com.xgwc.user.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.security.mnodel.SysUserDetails;
import com.xgwc.user.entity.dto.SysUserMiddleDto;
import com.xgwc.user.feign.api.UserDetailFeign;
import com.xgwc.user.feign.entity.SysUserMiddle;
import com.xgwc.user.service.UserService;
import com.xgwc.user.service.XgwcBrandRoleService;
import com.xgwc.user.service.impl.UserDetailServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class UserDetailApi implements UserDetailFeign {

    @Resource
    private UserDetailServiceImpl userDetailService;

    @Resource
    private XgwcBrandRoleService xgwcBrandRoleService;

    @Resource
    private UserService userService;

    @Override
    public ApiResult get_use_detail(String userId){
        SysUserDetails sysUser = userDetailService.loadUserByUsername(userId);
        return ApiResult.ok(sysUser);
    }

    @Override
    public ApiResult deleteBrandRoleUserByUserId(Long userId) {
        xgwcBrandRoleService.deleteBrandRoleUserByUserId(userId);
        return ApiResult.ok();
    }

    @Override
    public ApiResult addBrandRoleUser(Long roleId, Long userId, Integer isFlag) {
        xgwcBrandRoleService.saveBrandRoleUser(roleId, userId, isFlag);
        return ApiResult.ok();
    }

    @Override
    public ApiResult<Long> insertUserMiddle(SysUserMiddle vo) {
        Long id = userService.saveSysUserMiddle(vo);
        return ApiResult.ok(id);
    }

    @Override
    public ApiResult<Long> getUserMiddleById(Long id) {
        SysUserMiddleDto sysUserMiddleDto = userService.selectSysUserByUserId(id);
        if(sysUserMiddleDto != null){
            return ApiResult.ok(sysUserMiddleDto.getMainUserId());
        }
        return ApiResult.ok();
    }
}
