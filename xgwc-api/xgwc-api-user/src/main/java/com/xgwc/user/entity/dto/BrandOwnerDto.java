package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class BrandOwnerDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌ID")
    @Excel(name = "品牌ID")
    private Long brandId;

    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String companyName;

    @FieldDesc("公司简称")
    @Excel(name = "公司简称")
    private String companySimpleName;

    @FieldDesc("联系人")
    @Excel(name = "联系人")
    private String contact;

    @FieldDesc("管理员手机号")
    @Excel(name = "管理员手机号")
    private String managerPhone;

    @FieldDesc("密码")
    @Excel(name = "密码")
    private String password;

    @FieldDesc("状态：0正常，1禁用")
    @Excel(name = "状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("删除状态：0正常，1删除")
    @Excel(name = "删除状态：0正常，1删除")
    private Integer isDel;

    @FieldDesc("审核原因")
    @Excel(name = "审核原因")
    private String reason;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    private Long userId;

    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("brandId",getBrandId())
            .append("companyName",getCompanyName())
            .append("companySimpleName",getCompanySimpleName())
            .append("contact",getContact())
            .append("managerPhone",getManagerPhone())
            .append("password",getPassword())
            .append("status",getStatus())
            .append("isDel",getIsDel())
            .append("reason",getReason())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
        .toString();
        }
}
