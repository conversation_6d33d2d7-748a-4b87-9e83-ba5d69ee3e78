package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

@Data
public class SysUserResetVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @FieldDesc("密码")
    private String password;
    @FieldDesc("确认密码")
    private String passwordConfirm;



}
