package com.xgwc.user.dao;

import java.util.List;
import com.xgwc.user.entity.Designer;
import com.xgwc.user.entity.dto.SimpleClassifyUserInfoDto;
import com.xgwc.user.entity.dto.SimpleUserInfoDto;
import com.xgwc.user.entity.vo.ApplyInfoVO;
import com.xgwc.user.entity.dto.SimpleDesignerBrandDto;
import com.xgwc.user.entity.vo.DesignerSimpleVO;
import com.xgwc.user.entity.vo.DesignerVO;
import com.xgwc.user.entity.dto.DesignerQueryDto;
import com.xgwc.user.entity.vo.FranchiseOwnerVO;
import org.apache.ibatis.annotations.Param;


public interface DesignerMapper {
    /**
     * 查询设计师管理
     * 
     * @param designerId 设计师管理主键
     * @return 设计师管理
     */
    public DesignerVO selectDesignerByDesignerId(Long designerId);

    /**
     * 查询设计师管理列表
     * 
     * @param designer 设计师管理
     * @return 设计师管理集合
     */
    public List<DesignerVO> selectDesignerList(DesignerQueryDto designer);

    /**
     * 新增设计师管理
     * 
     * @param designer 设计师管理
     * @return 结果
     */
    public int insertDesigner(Designer designer);

    /**
     * 修改设计师管理
     * 
     * @param designer 设计师管理
     * @return 结果
     */
    public int updateDesigner(Designer designer);

    /**
     * 删除设计师管理
     * 
     * @param designerId 设计师管理主键
     * @return 结果
     */
    public int deleteDesignerByDesignerId(Long designerId);

    /**
     * 批量删除设计师管理
     * 
     * @param designerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDesignerByDesignerIds(Long[] designerIds);

    /**
     * 查询我加入的品牌商
     * @param managerUserId 设计师用户id
     * @param brandId 品牌商id
     * @return 结果
     */
    List<ApplyInfoVO> findDesignerByManagerUserIdAndBrandIdList(@Param("managerUserId") Long managerUserId,@Param("brandId") Long brandId);

    /**
     * 根据用户查询所属品牌信息（精简）
     * @param userId 用户id
     * @return 品牌信息
     */
    List<SimpleDesignerBrandDto> getSimpleDesignerBrandDtoList(@Param("userId") Long userId);

    DesignerVO findDesignerByManagerPhone(@Param("managerPhone") String managerPhone,@Param("brandId") Long brandId);

    DesignerVO findDesignerByManagerUserIdAndBrandId(@Param("managerUserId") Long managerUserId,@Param("brandId") Long brandId);

    /**
     * 根据id查找设计师问题
     * @param managerUserId
     * @return
     */
    List<DesignerVO> findDesignerByManagerUserId(@Param("managerUserId") Long managerUserId);

    /**
     * 根据部门ID列表获取所有员工ID
     * @param goodBusinessList 部门ID列表
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByDeptIds(@Param(value = "list") List<Long> goodBusinessList, @Param(value = "brandId") Long brandId);

    /**
     * 根据品牌ID列表获取所有员工ID
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByBrand(@Param(value = "brandId") Long brandId);

    /**
     * 根据品牌ID列表获取所有员工ID
     * @return 用户ID
     */
    Integer countStaffUserIdListByBrand(@Param(value = "brandId") Long brandId);

    /**
     * 根据分类ID获取用户列表
     * @param businessId 业务ID
     * @return 用户列表
     */
    List<SimpleUserInfoDto> getSimpleUserInfoDtoListByBusinessId(@Param("businessId") Integer businessId,@Param(value = "brandId") Long brandId);

    /**
     * 根据品牌ID获取用户列表
     * @param brandId 品牌id
     * @return 用户列表
     */
    List<SimpleClassifyUserInfoDto> getSimpleUserInfoDtoList(@Param(value = "brandId") Long brandId);

    /**
     * 根据加盟商id查询分类
     * @param franchiseId 加盟商id
     * @return 分类列表
     */
    List<String> findClassifyByFranchiseId(Long franchiseId);

    /**
     * 查询加盟商设计师列表
     * @param designerQueryDto 查询条件
     * @return 设计师列表
     */
    List<DesignerVO> findFranchiseeDesignerList(DesignerQueryDto designerQueryDto);

    /**
     * 根据业务id和加盟商id获取设计师下拉列表
     * @param businessId 业务id
     * @param franchiseId 加盟商id
     * @return 设计师列表
     */
    List<DesignerSimpleVO> findDesignerDropdownByBusinessId(@Param(value = "businessId") Long businessId,@Param(value = "franchiseId") Long franchiseId);

    /**
     * 根据品牌id获取设计师下拉列表
     * @param brandId 品牌id
     * @return 设计师列表
     */
    List<DesignerSimpleVO> findBrandDesignerDropdown(Long brandId);

    /**
     * 根据业务id和品牌id获取设计师下拉列表
     * @param businessId 业务id
     * @param brandId 品牌id
     * @return 设计师列表
     */
    List<DesignerSimpleVO> findBrandDesignerByBusinessId(@Param(value = "businessId") Long businessId,@Param(value="brandId") Long brandId);

    /**
     * 根据品牌id和审核状态获取设计师数量
     * @param brandId 品牌id
     * @return 设计师数量
     */
    Integer countApplyDesignerByBrandId(@Param(value = "brandId")Long brandId,@Param(value = "checkStatus") Integer checkStatus);

    /**
     * 根据品牌id和接单状态获取通过审核的设计师数量
     * @param brandId 品牌id
     * @return 设计师数量
     */
    Integer countPassApplyDesignerByBrandId(@Param(value = "brandId") Long brandId,@Param(value = "receiveOrderStatus") Integer receiveOrderStatus);

    /**
     * 根据加盟商id和审核状态获取设计师数量
     * @param franchiseId 加盟商id
     * @return 设计师数量
     */
    Integer countFranchiseeDesignerByFranchiseId(@Param(value = "franchiseId") Long franchiseId,@Param(value = "receiveOrderStatus") int receiveOrderStatus);


    /**
     * 根据用户ID获取设计师信息
     * @param userId 用户ID
     * @return 设计师
     */
    Designer getDesignerByUserId(Long userId);
}
