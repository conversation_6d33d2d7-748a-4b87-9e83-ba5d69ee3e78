package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.dto.FranchiseDesignerRecordDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DesignerVO {

    private static final long serialVersionUID=1L;

    /** 设计师ID */
    @Excel(name = "设计师ID")
    private Long designerId;

    /** 身份证正面：加密 */
    @Excel(name = "身份证正面：加密")
    private String idcardFront;

    /** 身份证反面：加密 */
    @Excel(name = "身份证反面：加密")
    private String idcardBack;

    /** 身份证是否长期：0长期，1非长期 */
    @Excel(name = "身份证是否长期：0长期，1非长期")
    private Long idcardIsLongterm;

    /** 身份证开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "身份证开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date idcardStart;

    /** 身份证结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "身份证结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date idcardEnd;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 身份证号：加密 */
    @Excel(name = "身份证号：加密")
    private String idcardNo;

    /** 手机号：加密 */
    @Excel(name = "手机号：加密")
    private String phone;

    /** 邮箱：加密 */
    @Excel(name = "邮箱：加密")
    private String email;

    /** 紧急联系人 */
    @Excel(name = "紧急联系人")
    private String emergencyName;

    /** 紧急手机号 */
    @Excel(name = "紧急手机号")
    private String emergencyPhone;

    /** 擅长业务 */
    @Excel(name = "擅长业务")
    private Long goodBusiness;

    /** 特长描述 */
    @Excel(name = "特长描述")
    private String description;

    /** 开户名：加密 */
    @Excel(name = "开户名：加密")
    private String bankUserName;

    /** 开户行 */
    @Excel(name = "开户行")
    private String bankName;

    /** 开户账号：加密 */
    @Excel(name = "开户账号：加密")
    private String bankNo;

    /** 支付宝姓名 */
    @Excel(name = "支付宝姓名")
    private String zfbName;

    /** 支付宝账号：加密 */
    @Excel(name = "支付宝账号：加密")
    private String zfbAccount;

    /** 管理员姓名 */
    @Excel(name = "管理员姓名")
    private String managerName;

    /** 管理员手机号：加密 */
    @Excel(name = "管理员手机号：加密")
    private String managerPhone;

    /** 接单状态：0正常接单，1.暂停接单，2.离职 */
    @Excel(name = "接单状态：0正常接单，1.暂停接单，2.离职")
    private Long receiveOrderStatus;

    /** 申请状态 */
    @Excel(name = "申请状态")
    private Long checkStatus;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkTime;

    /** 状态：0正常，1禁用 */
    @Excel(name = "状态：0正常，1禁用")
    private Integer status;

    /** 删除状态：0正常，1删除 */
    @Excel(name = "删除状态：0正常，1删除")
    private Integer isDel;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    /** 行修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    /** 审核原因 */
    @Excel(name = "审核原因")
    private String reason;

    /** 品牌商 */
    private String brandName;

    /** 擅长业务 */
    private String businessName;

    @FieldDesc("设计师审核记录")
    private List<FranchiseDesignerRecordDto> designerAuditRecord;

    private Long brandId;

    private Long managerUserId;

    /** 设计师等级 */
    private Integer designerLevel;

    /** 微信ID */
    private Long wechatId;

    /** 企业ID */
    private Long companyId;

    /** 微信名称 */
    private String wechatName;

    /** 微信收款码 */
    private String wechatAccountUrl;

    /** 支付宝收款码 */
    private String alipayAccountUrl;

    @Data
    public static class BrandInfo {
        private String brandName;
        private Long brandId;
        private Long designerId;
        private String name;

        public BrandInfo(String brandName, Long brandId, Long designerId, String name) {
            this.brandName = brandName;
            this.brandId = brandId;
            this.designerId = designerId;
            this.name = name;
        }
    }
}
