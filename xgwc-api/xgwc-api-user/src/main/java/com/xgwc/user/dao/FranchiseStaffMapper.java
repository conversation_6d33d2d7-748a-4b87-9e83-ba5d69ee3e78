package com.xgwc.user.dao;

import com.xgwc.user.entity.FranchiseStaff;
import com.xgwc.user.entity.dto.FranchiseStaffDto;
import com.xgwc.user.entity.dto.FranchiseStaffPageDto;
import com.xgwc.user.entity.dto.FranchiseStaffSimpleDto;
import com.xgwc.user.entity.dto.SimpleDeptUserInfoDto;
import com.xgwc.user.entity.vo.FranchiseStaffQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface FranchiseStaffMapper  {
    /**
     * 查询加盟商员工
     * 
     * @param id 加盟商员工主键
     * @return 加盟商员工
     */
    public FranchiseStaffDto selectFranchiseStaffById(Long id);

    /**
     * 根据用户id获取加盟商员工信息
     * @param userId 用户id
     * @return 加盟商员工
     */
    FranchiseStaffDto selectFranchiseStaffByUserId(Long userId);

    /**
     * 查询加盟商员工列表
     * 
     * @param franchiseStaff 加盟商员工
     * @return 加盟商员工集合
     */
    public List<FranchiseStaffPageDto> selectFranchiseStaffList(FranchiseStaffQueryVo franchiseStaff);

    /**
     * 新增加盟商员工
     * 
     * @param franchiseStaff 加盟商员工
     * @return 结果
     */
    public int insertFranchiseStaff(FranchiseStaff franchiseStaff);

    /**
     * 修改加盟商员工
     * 
     * @param franchiseStaff 加盟商员工
     * @return 结果
     */
    public int updateFranchiseStaff(FranchiseStaff franchiseStaff);

    /**
     * 删除加盟商员工
     * 
     * @param id 加盟商员工主键
     * @return 结果
     */
    public int deleteFranchiseStaffById(Long id);

    /**
     * 批量删除加盟商员工
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFranchiseStaffByIds(Long[] ids);

    /**
     * 根据姓名和加盟商ID查询加盟商员工
     * @param name 姓名
     * @param franchiseId 加盟商ID
     * @return 加盟商员工
     */
    FranchiseStaffDto findFranchiseStaffByNameAndFranchiseId(@Param("name") String name,@Param("franchiseId") Long franchiseId);

    /**
     * 更新绑定状态
     * @param id 加盟商员工ID
     * @param bindUserId 绑定用户ID
     * @param bindStatus 绑定状态
     * @param phone 手机号
     * @return 结果
     */
    int updateBindStatus(@Param("id") Long id,@Param("bindUserId") Long bindUserId,@Param("bindStatus") Integer bindStatus,@Param("phone") String phone,@Param("stageName") String stageName);

    /**
     * 根据部门ID获取所有员工ID
     * @param deptId 部门id
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByDeptId(@Param(value = "deptId") Long deptId, @Param(value = "franchiseId") Long franchiseId);

    /**
     * 根据部门ID列表获取所有员工ID
     * @param deptIds 部门ID列表
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByDeptIds(@Param(value = "list") List<Long> deptIds, @Param(value = "franchiseId") Long franchiseId);

    /**
     * 根据品牌ID列表获取所有员工ID
     * @param deptIds 部门ID列表
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByDeptIdsAndBrandId(@Param(value = "list") List<Long> deptIds, @Param(value = "brandId") Long brandId);

    /**
     * 根据品牌ID获取所有员工ID
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByBrandId(@Param(value = "brandId") Long brandId);

    /**
     * 根据部门ID列表获取所有员工ID
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByFranchise(@Param(value = "franchiseId") Long franchiseId);

    /**
     * 获取加盟商员工数量
     * @return 用户ID
     */
    Integer countStaffUserIdByFranchise(@Param(value = "franchiseId") Long franchiseId);

    /**
     * 获取加盟商员工数量 根据品牌商统计
     * @return 用户ID
     */
    Integer countStaffUserIdByBrandId(@Param(value = "brandId") Long brandId);

    /**
     * 根据部门ID列表获取所有员工ID
     * @return 用户ID
     */
    List<SimpleDeptUserInfoDto> getStaffUserInfoListByFranchise(@Param(value = "franchiseId") Long franchiseId);

    /**
     * 查询加盟商员工下拉框
     * @return 加盟商员工下拉框
     */
    List<FranchiseStaffDto> selectFranchiseStaffListDropDown(@Param("franchiseId") Long franchiseId);

    /**
     * 根据用户ID和加盟商ID查询加盟商员工
     * @param userId 用户ID
     * @param franchiseId 加盟商ID
     * @return 加盟商员工
     */
    FranchiseStaffDto findFranchiseStaffByUserIdAndFranchiseId(@Param("userId") Long userId,@Param("franchiseId") Long franchiseId);

    /**
     * 修改加盟商员工状态
     * @param id 加盟商员工ID
     * @param status 状态
     * @return 结果
     */
    int updateFranchiseeStaffStatus(@Param("id") Long id,@Param("status") Integer status);

    /**
     * 根据绑定用户ID查询加盟商员工
     * @param bindUserId 绑定用户ID
     * @return 加盟商员工
     */
    FranchiseStaffDto findFranchiseStaffByBindUserId(Long bindUserId);

    /**
     *加盟商员工下载次数限制列表
     * @return 加盟商员工下载次数限制列表
     */
    List<FranchiseStaffDto> selectStaffDownloadLimitList(@Param("franchiseStaff") FranchiseStaffQueryVo franchiseStaff);

    /**
     * 修改加盟商员工下载次数限制
     * @param franchiseStaff 加盟商员工下载次数限制
     * @return 结果
     */
    int updateStaffDownloadLimit(FranchiseStaff franchiseStaff);

    /**
     * 根据角色ID列表查询角色下载次数限制
     *
     * @param integerEoleIds 角色ID列表
     * @return 角色下载次数限制
     */
    FranchiseStaffDto selectMaxRoleDateDownloadLimit(@Param("roleIds") List<Integer> integerEoleIds);

    /**
     * 根据加盟商ID查询加盟商员工下拉框
     *
     * @param franchiseId 加盟商ID
     * @param deptId
     * @return 加盟商员工下拉框
     */
    List<FranchiseStaffSimpleDto> findFranchiseStaffListDropDownByFranchiseId(@Param("franchiseId") Long franchiseId, @Param("deptId") Long deptId);

    /**
     * 判断用户是否是管理员
     * @param userId 用户ID
     * @return 是否是加盟商管理员
     */
    boolean selectAdmin(Long userId);

    /**
     * 刷新加盟商员工下载次数
     * @return 刷新结果
     */
    int refreshFranchiseStaffDownloadCount();

    /**
     * 刷新品牌员工下载次数
     * @return 刷新结果
     */
    int refreshBrandStaffDownloadCount();

    /**
     * 根据加盟商员工ID查询加盟商员工下载限制次数
     *
     * @param id      加盟商员工ID
     * @param brandId 品牌ID
     * @return 加盟商员工限制次数
     */
    List<FranchiseStaffDto> selectFranchiseStaffDownload(@Param("id") Long id, @Param("brandId") Long brandId);

    /**
     * 根据加盟商员工ID查询加盟商员工下载次数
     *
     * @param id      加盟商员工ID
     * @param brandId 品牌ID
     * @return 加盟商员工下载次数
     */
    List<FranchiseStaffDto> selectDownloadCount(@Param("staffId") Long id, @Param("brandId") Long brandId);

    /**
     * 根据加盟商ID列表获取一级部门的部门负责人
     * @param franchiseIds 加盟商ID列表
     * @return 员工信息
     */
    List<FranchiseStaffDto> getFirstLevelDirectorStaffs(@Param(value = "list") List<Long> franchiseIds);

    /**
     * 根据加盟商ID列表获取一级部门的部门负责人
     * @param deptIds 加盟商ID列表
     * @return 员工信息
     */
    List<FranchiseStaffDto> getDeptDirectorStaffs(@Param(value = "list") List<Long> deptIds);

    /**
     * 根据部门ID列表和加盟商ID查询员工
     * @param allDeptIds 部门ID集合
     * @param franchiseId 加盟商ID
     * @return 员工信息
     */
    List<FranchiseStaffSimpleDto> selectStaffByDeptIdsAndFranchiseId( @Param(value = "deptIds") List<Long> allDeptIds,@Param(value = "franchiseId") Long franchiseId);
}
