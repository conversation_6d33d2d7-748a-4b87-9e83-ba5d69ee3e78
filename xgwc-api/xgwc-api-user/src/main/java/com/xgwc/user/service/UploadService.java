package com.xgwc.user.service;

import com.xgwc.user.entity.dto.upload.UploadFileDto;
import org.springframework.web.multipart.MultipartFile;

public interface UploadService {

    /**
     * 上传文件
     * @param file 文件
     * @return 文件信息
     */
    UploadFileDto uploadFile(MultipartFile file);

    /**
     * 上传图片
     * @param file 图片文件
     * @return 文件信息
     */
    UploadFileDto uploadPic(MultipartFile file);

}
