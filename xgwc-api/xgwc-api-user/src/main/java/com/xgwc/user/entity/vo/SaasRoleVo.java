package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

@Data
public class SaasRoleVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键ID")
    private Long roleId;

    @FieldDesc("角色名称")
    private String roleName;

    @FieldDesc("排序")
    private Integer sort;

    @FieldDesc("管理员标识")
    private String isFlag;

    @FieldDesc("是否系统默认角色（0-否，1-是）")
    private Integer isDefault;

    @FieldDesc("角色权限范围:0-品普 1-品管 2-加普 3-加管 4-服普 5-服管 6-销普 7-销管 8-saas普 9-saas管")
    private Integer roleScope;

    @FieldDesc("生效范围 品牌商/加盟商/财务服务商/销售服务商（id）")
    private List<Long> effectiveScope;

    @FieldDesc("业务系统 0-猴霸 1-人资")
    private Integer businessSystem;

    @FieldDesc("角色状态（0-正常/1-禁用）")
    private Integer status;

    @FieldDesc("菜单权限组")
    private List<SysRoleMenuVo> menuIds;

    @FieldDesc("数据权限组")
    private List<SysRoleDataVo> dataMenuIds;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;
}
