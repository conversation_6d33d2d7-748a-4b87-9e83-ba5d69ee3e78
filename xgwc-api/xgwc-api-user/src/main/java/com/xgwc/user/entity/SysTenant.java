package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class SysTenant {

private static final long serialVersionUID=1L;

    /** 租户编号 */
    private Long id;

    /** 租户名 */
    private String name;

    /** 联系人的用户编号 */
    private Long contactUserId;

    /** 联系人 */
    private String contactName;

    /** 联系手机 */
    private String contactMobile;

    /** 租户状态（0正常 1停用） */
    private Integer status;

    /** 绑定域名 */
    private String website;

    /** 租户套餐编号 */
    private Long packageId;

    /** 过期时间 */
    private Date expireTime;

    /** 账号数量 */
    private Long accountCount;

    /** 创建者 */
    private String creator;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    private String updater;

    /** 更新时间 */
    private Date updateTime;

    /** 是否删除 */
    private Integer isDel;



}