package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.user.entity.dto.FranchiseDto;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.FranchiseMapper;
import com.xgwc.user.service.IFranchiseService;
import com.xgwc.user.entity.Franchise;
import com.xgwc.user.entity.vo.FranchiseVo;

import java.util.List;


@Service
public class FranchiseServiceImpl implements IFranchiseService  {
    @Resource
    private FranchiseMapper franchiseMapper;

    @Override
    public FranchiseDto getFranchiseByManagerUserId(Long managerUserId) {
        return franchiseMapper.getFranchiseDtoByUserId(managerUserId);
    }

    /**
     * 新增加盟商主体信息表
     * 
     * @param dto 加盟商主体信息表
     * @return 结果
     */
    @Override
    public int insertFranchise(FranchiseVo dto) {
        Franchise franchise = BeanUtil.copyProperties(dto, Franchise.class);
        return franchiseMapper.insertFranchise(franchise);
    }

    @Override
    public FranchiseDto selectFranchiseById(Long id) {
        return franchiseMapper.findFranchiseById(id);
    }

    @Override
    public List<com.xgwc.user.feign.entity.FranchiseDto> listByIds(List<Long> ids) {
        return franchiseMapper.listByIds(ids);
    }

}
