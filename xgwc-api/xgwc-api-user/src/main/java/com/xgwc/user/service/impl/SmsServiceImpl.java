package com.xgwc.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.enums.SmsStatusEnums;
import com.xgwc.common.enums.SmsTypeEnums;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.StringUtil;
import com.xgwc.redis.constants.Expire;
import com.xgwc.redis.constants.SmsCacheKey;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.user.config.SmsClientConfig;
import com.xgwc.user.dao.SmsSendMapper;
import com.xgwc.user.service.SmsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private SmsSendMapper smsSendMapper;

    @Resource
    private SmsClientConfig smsClientConfig;

    @Resource
    private Client smsClient;

    @Override
    public ApiResult sendLoginCode(String mobile) {
        boolean validCount = validSendCodeCount(mobile, SmsTypeEnums.VALID_CODE_LOGIN);
        if(!validCount){
            smsSendMapper.insertSmsSendRecord(ParamDecryptUtil.encrypt(mobile, ParamDecryptUtil.PHONE_KEY), ApiStatusEnums.SMS_SENDCODE_LIMIT.getMessage(),
                    SmsTypeEnums.VALID_CODE_LOGIN.getCode(), SmsStatusEnums.FAIL.getStatus());
            return ApiResult.error(ApiStatusEnums.SMS_SENDCODE_LIMIT);
        }
        boolean validTime = validSendCodeTime(mobile, SmsTypeEnums.VALID_CODE_LOGIN);
        if(!validTime){
            return ApiResult.error(ApiStatusEnums.SMS_SENDCODE_TIME);
        }
        return sendCode(mobile, SmsTypeEnums.VALID_CODE_LOGIN);
    }

    @Override
    public ApiResult sendRegisterCode(String mobile) {
        boolean validCount = validSendCodeCount(mobile, SmsTypeEnums.VALID_CODE_REGISTER);
        if(!validCount){
            smsSendMapper.insertSmsSendRecord(ParamDecryptUtil.encrypt(mobile, ParamDecryptUtil.PHONE_KEY), ApiStatusEnums.SMS_SENDCODE_LIMIT.getMessage(),
                    SmsTypeEnums.VALID_CODE_REGISTER.getCode(), SmsStatusEnums.FAIL.getStatus());
            return ApiResult.error(ApiStatusEnums.SMS_SENDCODE_LIMIT);
        }
        boolean validTime = validSendCodeTime(mobile, SmsTypeEnums.VALID_CODE_REGISTER);
        if(!validTime){
            return ApiResult.error(ApiStatusEnums.SMS_SENDCODE_TIME);
        }
        return sendCode(mobile, SmsTypeEnums.VALID_CODE_REGISTER);
    }

    @Override
    public boolean validLoginCode(String mobile, String code) {
        return validCode(mobile, code, SmsTypeEnums.VALID_CODE_LOGIN);
    }

    @Override
    public boolean validRegisterCode(String mobile, String code) {
        return validCode(mobile, code, SmsTypeEnums.VALID_CODE_REGISTER);
    }

    private boolean validCode(String mobile, String code, SmsTypeEnums smsTypeEnums) {
        String cacheKey = "";
        if(smsTypeEnums == SmsTypeEnums.VALID_CODE_LOGIN){
            cacheKey = SmsCacheKey.VALID_LOGIN_CODE;
        } else if (smsTypeEnums == SmsTypeEnums.VALID_CODE_REGISTER) {
            cacheKey = SmsCacheKey.VALID_REGISTER_CODE;
        }
        if(StringUtil.isEmpty(code)){
            return false;
        }
        String randomCode = redisUtil.get(cacheKey.concat(mobile));
        if(StringUtil.isNotEmpty(randomCode)){
            return randomCode.equals(code.trim());
        }
        return false;
    }

    private ApiResult sendCode(String mobile, SmsTypeEnums smsTypeEnums) {
        String cacheKey = "";
        String cacheTimeKey = "";
        String templateCode = "";
        if(smsTypeEnums == SmsTypeEnums.VALID_CODE_LOGIN){
            templateCode = smsClientConfig.getLoginTemplateCode();
            cacheKey = SmsCacheKey.VALID_LOGIN_CODE;
            cacheTimeKey = SmsCacheKey.SEND_LOGIN_CODE_TIME;
        } else if (smsTypeEnums == SmsTypeEnums.VALID_CODE_REGISTER) {
            templateCode = smsClientConfig.getRegisterTemplateCode();
            cacheKey = SmsCacheKey.VALID_REGISTER_CODE;
            cacheTimeKey = SmsCacheKey.SEND_REGISTER_CODE_TIME;
        }
//        String randomCode = String.valueOf((int) (Math.random() * 900000) + 100000); // 生成6位随机验证码
        String randomCode = "666666"; // 生成6位随机验证码
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", randomCode);

        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(mobile)
                .setSignName(smsClientConfig.getSignName())
                .setTemplateCode(templateCode)
                .setTemplateParam(jsonObject.toString());
        try {
            boolean isSuccess;
            if(smsClientConfig.isOpen()) {
                SendSmsResponse response = smsClient.sendSmsWithOptions(sendSmsRequest, new RuntimeOptions());
                isSuccess = "OK".equals(response.getBody().getCode());
            }else{
                //测试阶段，可以直接写库，去数据库查看验证码
                isSuccess = true;
            }
            if(isSuccess) {
                redisUtil.set(cacheKey.concat(mobile), randomCode, Expire.FIVE_MINUTE);
                redisUtil.set(cacheTimeKey.concat(mobile), randomCode, Expire.ONE_MINUTE);
                addCount(mobile, smsTypeEnums);
                smsSendMapper.insertSmsSendRecord(ParamDecryptUtil.encrypt(mobile, ParamDecryptUtil.PHONE_KEY), randomCode, smsTypeEnums.getCode(), SmsStatusEnums.SUCCESS.getStatus());
                return ApiResult.ok();
            }
        } catch (Exception e) {
            smsSendMapper.insertSmsSendRecord(ParamDecryptUtil.encrypt(mobile, ParamDecryptUtil.PHONE_KEY), randomCode, smsTypeEnums.getCode(), SmsStatusEnums.FAIL.getStatus());
            log.error("发送验证码失败,手机号:{}, 验证码:{}, error:", mobile, randomCode, e);
        }
        return ApiResult.error("发送验证码失败");
    }

    private void addCount(String phoneNumber, SmsTypeEnums smsTypeEnums){
        String key = "";
        if(smsTypeEnums == SmsTypeEnums.VALID_CODE_LOGIN){
            key = SmsCacheKey.SEND_LOGIN_CODE_COUNT;
        }else if(smsTypeEnums == SmsTypeEnums.VALID_CODE_REGISTER){
            key = SmsCacheKey.SEND_REGISTER_CODE_COUNT;
        }
        String codeCount = redisUtil.get(getSendCodeKey(phoneNumber, key));
        if(StringUtil.isNotEmpty(codeCount)){
            codeCount = (Integer.parseInt(codeCount) + 1) + "";
        }else {
            codeCount = "1";
        }
        redisUtil.set(getSendCodeKey(phoneNumber, key), codeCount, Expire.DAY);
    }

    /**
     * 验证手机号发送验证码次数
     */
    private boolean validSendCodeCount(String phoneNumber, SmsTypeEnums smsTypeEnums){
        int limit = 0;
        String key = "";
        if(smsTypeEnums == SmsTypeEnums.VALID_CODE_LOGIN){
            limit = smsClientConfig.getLoginCodeLimit();
            key = SmsCacheKey.SEND_LOGIN_CODE_COUNT;
        }else if(smsTypeEnums == SmsTypeEnums.VALID_CODE_REGISTER){
            limit = smsClientConfig.getRegisterCodeLimit();
            key = SmsCacheKey.SEND_REGISTER_CODE_COUNT;
        }
        String codeCount = redisUtil.get(getSendCodeKey(phoneNumber, key));
        if(StringUtil.isNotEmpty(codeCount)){
            //小于限制数可发送
            return Integer.parseInt(codeCount) < limit;
        }
        return true;
    }

    /**
     * 验证手机号发送验证码次数
     */
    private boolean validSendCodeTime(String mobile, SmsTypeEnums smsTypeEnums){
        String key = "";
        if(smsTypeEnums == SmsTypeEnums.VALID_CODE_LOGIN){
            key = SmsCacheKey.SEND_LOGIN_CODE_TIME;
        }else if(smsTypeEnums == SmsTypeEnums.VALID_CODE_REGISTER){
            key = SmsCacheKey.SEND_REGISTER_CODE_TIME;
        }
        String codetime = redisUtil.get(key.concat(mobile));
        //频繁发送
        return StringUtil.isEmpty(codetime);
    }

    /**
     * 获取验证码key
     */
    private String getSendCodeKey(String phoneNumber, String key){
        return key.concat(phoneNumber).concat("_").concat(DateUtils.getShortDateStr());
    }
}
