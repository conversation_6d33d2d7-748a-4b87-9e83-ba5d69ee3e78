package com.xgwc.user.dao;

import java.util.List;
import com.xgwc.user.entity.SysDictType;
import com.xgwc.user.entity.vo.SysDictTypeQuery2Vo;
import com.xgwc.user.entity.vo.SysDictTypeVo;
import com.xgwc.user.entity.dto.SysDictTypeDto;
import com.xgwc.user.entity.vo.SysDictTypeQueryVo;
import org.apache.ibatis.annotations.Param;


public interface SysDictTypeMapper  {
    /**
     * 查询字典管理
     * 
     * @param dictId 字典管理主键
     * @return 字典管理
     */
    public SysDictTypeDto selectSysDictTypeByDictId(Long dictId);
    public SysDictTypeDto selectSysDictTypeByDictValue(String dictValue);

    /**
     * 查询字典管理列表
     * 
     * @param sysDictType 字典管理
     * @return 字典管理集合
     */
    public List<SysDictTypeDto> selectSysDictTypeList(SysDictTypeQueryVo sysDictType);
    public List<SysDictTypeDto> selectSysDictTypeList2(SysDictTypeQuery2Vo sysDictType);

    /**
     * 新增字典管理
     * 
     * @param sysDictType 字典管理
     * @return 结果
     */
    public int insertSysDictType(SysDictType sysDictType);

    /**
     * 修改字典管理
     * 
     * @param sysDictType 字典管理
     * @return 结果
     */
    public int updateSysDictType(SysDictType sysDictType);



    public int updateStatus(@Param("array") Long[] ids, @Param("status") Integer status);
    /**
     * 删除字典管理
     * 
     * @param dictId 字典管理主键
     * @return 结果
     */
    public int deleteSysDictTypeByDictId(Long dictId);

    /**
     * 批量删除字典管理
     * 
     * @param dictIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysDictTypeByDictIds(Long[] dictIds);
}
