package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SysDictTypeQuery2Vo {

    private static final long serialVersionUID=1L;

    @FieldDesc("是否设置数据：0否 1是")
    private Long isData;

    @FieldDesc("字典名称")
    private String dictLabel;

    @FieldDesc("字典状态")
    private Integer status;


}
