package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.vo.SysRoleDataVo;
import com.xgwc.user.entity.vo.SysRoleMenuVo;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.dto
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  11:02
 */

/**
 * 组织管理-角色管理
 */
@Data
public class XgwcBrandRoleDto {

    /**
     * 角色ID
     */
    @FieldDesc("角色id")
    private Long roleId;

    /**
     * 角色名称
     */
    @FieldDesc("角色名称")
    private String roleName;

    @FieldDesc("标识字段")
    private String isFlag;

    @FieldDesc("岗位id")
    private Long stationId;

    @FieldDesc("品牌商id")
    private Long brandOwnerId;

/*    *//**
     * 数据权限（1：全部数据 2：本部门数据 3：本人数据）
     *//*
    @FieldDesc("数据权限（1：全部数据 2：本部门数据 3：指定部门数据）")
    private Integer dataScope;*/

    /** 菜单数据权限组 */
    @FieldDesc("数据权限组")
    private List<SysRoleDataVo> dataMenuIds;

    /** 菜单组 */
    @FieldDesc("菜单权限组")
    private List<SysRoleMenuVo> menuIds;

    /**
     * 排序：越小越前
     */
    @FieldDesc("排序：越小越前")
    private Integer sort;

    /**
     * 状态：0正常，1禁用
     */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    /**
     * 是否删除：0正常，1删除
     */
    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
