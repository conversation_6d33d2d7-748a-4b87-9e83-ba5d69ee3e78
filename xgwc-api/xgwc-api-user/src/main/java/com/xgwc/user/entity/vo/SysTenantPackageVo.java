package com.xgwc.user.entity.vo;

import com.alibaba.fastjson.JSON;
import com.xgwc.common.annotation.Excel;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.Set;

@Data
public class SysTenantPackageVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("套餐编号")
    private Long id;

    @FieldDesc("套餐名")
    private String name;

    @FieldDesc("租户状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("关联的菜单编号")
    private Set<Long> menuIds;

    @FieldDesc("备注")
    private String remark;

    public String getMenuIds() {
        return JSON.toJSONString(menuIds);
    }
}
