package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.util.PasswordUtil;
import com.xgwc.user.entity.dto.SysUserDto;
import com.xgwc.user.entity.vo.SysUserQueryVo;
import com.xgwc.user.entity.vo.SysUserResetVo;
import com.xgwc.user.entity.vo.SysUserStatusVo;
import com.xgwc.user.entity.vo.SysUserVo;
import com.xgwc.user.service.UserService;
import com.xgwc.user.service.impl.UserDetailServiceImpl;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("user")
@RestController
public class UserController extends BaseController {

    @Resource
    private UserService userService;

    @Resource
    private UserDetailServiceImpl userDetailService;


    /**
     * 查询用户列表
     */
    @MethodDesc("查询用户列表")
    @PreAuthorize("@ss.hasPermission('user:SysUser:list')")
    @GetMapping("/list")
    public ApiResult<SysUserDto> list(SysUserQueryVo sysUser) {
        startPage();
        List<SysUserDto> list = userService.selectSysUserList(sysUser);
        return getDataTable(list);
    }


    /**
     * 导出用户列表
     */
    @MethodDesc("导出用户列表")
    @PreAuthorize("@ss.hasPermission('user:SysUser:export')")
    @Log(title = "用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUserQueryVo sysUser) {
    }

    /**
     * 获取用户详细信息
     */
    @MethodDesc("获取用户详细信息")
    @PreAuthorize("@ss.hasPermission('user:SysUser:query')")
    @GetMapping(value = "/{userId}")
    public ApiResult<SysUserDto> getInfo(@PathVariable("userId") Long userId) {
        return success(userService.selectSysUserByUserId(userId));
    }

    /**
     * 新增用户
     */
    @MethodDesc("新增用户")
    @PreAuthorize("@ss.hasPermission('user:SysUser:add')")
    @Log(title = "用户", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody SysUserVo sysUser) {
        if(!PasswordUtil.validate(sysUser.getPassword())){
            return ApiResult.error("密码不符合要求");
        }
        return toAjax(userService.insertSysUser(sysUser));
    }

    /**
     * 修改用户
     */
    @MethodDesc("修改用户")
    @PreAuthorize("@ss.hasPermission('user:SysUser:edit')")
    @Log(title = "用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody SysUserVo sysUser) {
        return toAjax(userService.updateSysUser(sysUser));
    }


    @MethodDesc("重置密码")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    @PutMapping("/reset")
    public ApiResult reset(@RequestBody SysUserResetVo sysUser) {
        if(!PasswordUtil.validate(sysUser.getPassword())){
            return ApiResult.error("密码不符合要求");
        }
        if(StringUtils.isBlank(sysUser.getPassword())){
            return ApiResult.error("请输入密码");
        }
        if(!sysUser.getPassword().equals(sysUser.getPasswordConfirm())){
            return ApiResult.error("2次密码不一致");
        }
        SysUserVo vo = new SysUserVo();
        vo.setUserId(sysUser.getUserId());
        vo.setPassword(sysUser.getPassword());
        return toAjax(userService.updateSysUser(vo));
    }

    @MethodDesc("启用/禁用")
    @Log(title = "用户状态修改", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public ApiResult status(@RequestBody SysUserStatusVo status) {
        SysUserVo vo = new SysUserVo();
        vo.setUserId(status.getUserId());
        vo.setStatus(status.getStatus());
        return toAjax(userService.updateSysUser(vo));
    }

    /**
     * 删除用户
     */
    @MethodDesc("删除用户")
    @PreAuthorize("@ss.hasPermission('user:SysUser:remove')")
    @Log(title = "用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public ApiResult remove(@PathVariable Long[] userIds) {
        return toAjax(userService.deleteSysUserByUserIds(userIds));
    }

}
