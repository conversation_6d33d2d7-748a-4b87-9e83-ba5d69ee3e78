package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.vo.PrivacyNumberCallBackVo;
import com.xgwc.user.entity.vo.PrivacyNumberVo;

public interface PrivacyNumberService {

    /**
     * 绑定号码
     * @param privacyNumberVo 参数
     * @return 是否成功
     */
    ApiResult<String> bindNumber(PrivacyNumberVo privacyNumberVo);

    /**
     * 绑定号码
     * @param privacyNumberVo 参数
     * @return 是否成功
     */
    ApiResult<String> bindCustomerNumber(PrivacyNumberVo privacyNumberVo);

    /**
     * 绑定号码
     * @param designerId 绑定用户
     * @return 是否成功
     */
    ApiResult<String> bindDesignerNumber(Long designerId);

    /**
     * 解绑号码
     * @param privacyNumberVo 参数
     * @return 是否成功
     */
    ApiResult<String> unbindNumber(PrivacyNumberVo privacyNumberVo);


    /**
     * 添加回调记录
     * @param privacyNumberCallBackVo
     */
    void addCallbackRecord(PrivacyNumberCallBackVo privacyNumberCallBackVo);
}
