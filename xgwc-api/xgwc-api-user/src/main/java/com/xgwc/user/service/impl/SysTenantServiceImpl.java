package com.xgwc.user.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.SysTenantMapper;
import com.xgwc.user.service.ISysTenantService;
import com.xgwc.user.entity.SysTenant;
import com.xgwc.user.entity.vo.SysTenantVo;
import com.xgwc.user.entity.dto.SysTenantDto;
import com.xgwc.user.entity.vo.SysTenantQueryVo;


@Service
public class SysTenantServiceImpl implements ISysTenantService 
{
    @Resource
    private SysTenantMapper sysTenantMapper;

    /**
     * 查询租户管理
     * 
     * @param id 租户管理主键
     * @return 租户管理
     */
    @Override
    public SysTenantDto selectSysTenantById(Long id)
    {
        return sysTenantMapper.selectSysTenantById(id);
    }

    /**
     * 查询租户管理列表
     * 
     * @param sysTenant 租户管理
     * @return 租户管理
     */
    @Override
    public List<SysTenantDto> selectSysTenantList(SysTenantQueryVo sysTenant)
    {
        return sysTenantMapper.selectSysTenantList(sysTenant);
    }

    /**
     * 新增租户管理
     * 
     * @param dto 租户管理
     * @return 结果
     */
    @Override
    public int insertSysTenant(SysTenantVo dto)
    {

        SysTenant sysTenant = BeanUtil.copyProperties(dto, SysTenant.class);
        sysTenant.setCreateTime(DateUtils.getNowDate());
        sysTenantMapper.insertSysTenant(sysTenant);
        return sysTenant.getId().intValue();
    }

    /**
     * 修改租户管理
     * 
     * @param dto 租户管理
     * @return 结果
     */
    @Override
    public int updateSysTenant(SysTenantVo dto)
    {

        SysTenant sysTenant = BeanUtil.copyProperties(dto, SysTenant.class);
        sysTenant.setUpdateTime(DateUtils.getNowDate());
        return sysTenantMapper.updateSysTenant(sysTenant);
    }

    /**
     * 批量删除租户管理
     * 
     * @param ids 需要删除的租户管理主键
     * @return 结果
     */
    @Override
    public int deleteSysTenantByIds(Long[] ids)
    {
        return sysTenantMapper.deleteSysTenantByIds(ids);
    }

    /**
     * 删除租户管理信息
     * 
     * @param id 租户管理主键
     * @return 结果
     */
    @Override
    public int deleteSysTenantById(Long id)
    {
        return sysTenantMapper.deleteSysTenantById(id);
    }
}
