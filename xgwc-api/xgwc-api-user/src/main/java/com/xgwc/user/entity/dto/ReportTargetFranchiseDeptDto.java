package com.xgwc.user.entity.dto;

import com.xgwc.user.entity.ReportTargetFranchiseDept;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReportTargetFranchiseDeptDto extends ReportTargetFranchiseDept {

    //加盟商名称
    private String franchiseName;

    /**
     * 上月金额
     */
    private BigDecimal preMonthAmount;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 负责人名称
     */
    private String directorName;

    /**
     * 目标名称
     */
    private String targetName;
}
