package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class SysTenantPackage {

private static final long serialVersionUID=1L;

    /** 套餐编号 */
    private Long id;

    /** 套餐名 */
    private String name;

    /** 租户状态（0正常 1停用） */
    private Integer status;

    /** 备注 */
    private String remark;

    /** 关联的菜单编号 */
    private String menuIds;

    /** 创建者 */
    private String creator;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    private String updater;

    /** 更新时间 */
    private Date updateTime;

    /** 是否删除 */
    private Integer isDel;



}