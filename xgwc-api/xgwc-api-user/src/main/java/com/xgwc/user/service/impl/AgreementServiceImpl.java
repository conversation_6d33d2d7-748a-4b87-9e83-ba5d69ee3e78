package com.xgwc.user.service.impl;

import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.BrandAgreementMapper;
import com.xgwc.user.dao.BrandAgreementRecordMapper;
import com.xgwc.user.dao.DesignerMapper;
import com.xgwc.user.entity.dto.AgreementDto;
import com.xgwc.user.entity.dto.SimpleDesignerBrandDto;
import com.xgwc.user.entity.vo.AgreementVo;
import com.xgwc.user.service.AgreementService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AgreementServiceImpl implements AgreementService {

    @Resource
    private BrandAgreementMapper brandAgreementMapper;

    @Resource
    private DesignerMapper designerMapper;

    @Resource
    private BrandAgreementRecordMapper brandAgreementRecordMapper;

    @Override
    public int saveAgreement(AgreementVo agreementVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser != null){
            Long brandId = sysUser.getBrandId();
            if(brandId == null){
                log.warn("保存协议失败,用户:{}没有查询到所属品牌", sysUser.getUserId());
                return -1;
            }else {
                String nickName = SecurityUtils.getNickName();
                agreementVo.setBrandId(brandId);
                AgreementDto agreementDto = brandAgreementMapper.getAgreementByBrandId(brandId);
                if(agreementDto == null){
                    agreementVo.setCreateBy(nickName);
                    return brandAgreementMapper.insertAgreement(agreementVo);
                }else{
                    agreementVo.setUpdateBy(nickName);
                    return brandAgreementMapper.updateAgreement(agreementVo);
                }
            }
        }
        return -1;
    }

    @Override
    public AgreementDto getAgreementByBrandId() {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser != null){
            Long brandId = sysUser.getBrandId();
            if(brandId == null){
                log.warn("获取协议失败,用户没有查询到所属品牌:{}", sysUser.getUserId());
                return null;
            }else{
                return brandAgreementMapper.getAgreementByBrandId(brandId);
            }
        }
        return null;
    }

    @Override
    public List<AgreementDto> getNotAgreeList() {
        //获取所有的协议列表
        List<AgreementDto> agreementDtos = getAllAgreementList();
        if(agreementDtos !=null && !agreementDtos.isEmpty()){
            return agreementDtos.stream().filter(agreementDto -> agreementDto.getIsAgree() == 0).collect(Collectors.toList());
        }
        return agreementDtos;
    }

    @Override
    public List<AgreementDto> getAllAgreementList() {
        List<AgreementDto> agreementDtos = null;
        //获取该用户属于哪些品牌id
        Long userId = SecurityUtils.getUserId();
        List<SimpleDesignerBrandDto> simpleDesignerBrandDtoList = designerMapper.getSimpleDesignerBrandDtoList(userId);
        if(simpleDesignerBrandDtoList != null && !simpleDesignerBrandDtoList.isEmpty()){
            Map<Long, SimpleDesignerBrandDto> brandIdMap = simpleDesignerBrandDtoList.stream()
                    .collect(Collectors.toMap(
                            SimpleDesignerBrandDto::getBrandId,
                            simpleDesignerBrandDto -> simpleDesignerBrandDto,
                            (oldValue, newValue) -> oldValue
                    ));
            //获取所有品牌id
            List<Long> brandIds = brandIdMap.keySet().stream().toList();
            agreementDtos = brandAgreementMapper.getAgreementByBrandIds(brandIds);
            List<Integer> agreementIds = brandAgreementRecordMapper.selectAgreementIdByUserId(userId);
            agreementIds = agreementIds == null ? new ArrayList<>() : agreementIds;
            if(agreementDtos != null && !agreementDtos.isEmpty()){
                List<Integer> finalAgreementIds = agreementIds;
                agreementDtos.forEach(agreementDto -> {
                    SimpleDesignerBrandDto simpleDesignerBrandDto = brandIdMap.get(agreementDto.getBrandId());
                    agreementDto.setBrandName(simpleDesignerBrandDto.getBrandName());
                    //标记为已读
                    if(finalAgreementIds.contains(agreementDto.getId())){
                        agreementDto.setIsAgree(1);
                    }

                });

            }
        }
        //获取所有列表
        return agreementDtos;
    }

    @Override
    public int agree(Integer agreementId) {
        Long userId = SecurityUtils.getUserId();
        int exist = brandAgreementMapper.eixstAgreementById(agreementId);
        if(exist <= 0){
            log.error("同意协议操作，协议不存在, 操作用户id:{}, 协议id:{}", userId, agreementId);
        }
        return brandAgreementRecordMapper.insertAgreementRecord(agreementId, userId);
    }
}
