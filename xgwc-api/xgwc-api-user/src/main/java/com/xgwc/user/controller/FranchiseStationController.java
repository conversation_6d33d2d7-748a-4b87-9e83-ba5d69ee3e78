package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.FranchiseStationDto;
import com.xgwc.user.entity.param.FranchiseStationParam;
import com.xgwc.user.entity.vo.FranchiseStationVo;
import com.xgwc.user.service.FranchiseStationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 加盟商岗位管理
 */
@RestController
@RequestMapping("/franchiseStation")
@Slf4j
public class FranchiseStationController extends BaseController {

    @Resource
    private FranchiseStationService franchiseStationService;

    /**
     * @param franchiseStationParam 查询条件
     * @return 岗位管理列表
     * 查询岗位管理列表
     */
    @MethodDesc("查询岗位管理列表")
    @PreAuthorize("@ss.hasPermission('franchiseStation:franchiseStation:list')")
    @PostMapping("/getFranchiseStationList")
    public ApiResult<FranchiseStationVo> getFranchiseStationList(@RequestBody FranchiseStationParam franchiseStationParam) {
        startPage();
        return getDataTable(franchiseStationService.getFranchiseStationList(franchiseStationParam));
    }

    /**
     * 查询加盟商询岗位管理下拉框
     *
     * @return 岗位管理下拉框
     */
    @GetMapping("/getFranchiseStationDropDown")
    public ApiResult getFranchiseStationDropDown() {
        try {
            FranchiseStationParam franchiseStationParam = new FranchiseStationParam();
            franchiseStationParam.setStatus(0);
            List<FranchiseStationVo> result = franchiseStationService.getFranchiseStationList(franchiseStationParam);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取加盟商询岗位管理下拉框失败", e);
            return ApiResult.error("获取加盟商询岗位管理下拉框失败");
        }
    }

    /**
     * @param franchiseStationDto 新增岗位管理信息
     * @return 插入结果
     * 新增岗位管理信息
     */
    @MethodDesc("新增岗位管理信息")
    @PreAuthorize("@ss.hasPermission('franchiseStation:franchiseStation:add')")
    @Submit(fileds = "userId")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveFranchiseStation")
    public ApiResult saveFranchiseStation(@RequestBody FranchiseStationDto franchiseStationDto) {
        return franchiseStationService.saveFranchiseStation(franchiseStationDto);
    }

    /**
     * @param stationId 岗位管理id
     * @return 岗位管理信息
     * 根据id查询岗位管理信息
     */
    @MethodDesc("根据id查询岗位管理信息")
    @GetMapping("/getFranchiseStationById/{stationId}")
    public ApiResult getFranchiseStationById(@PathVariable Long stationId) {
        return franchiseStationService.getFranchiseStationById(stationId);
    }

    /**
     * @param franchiseStationDto 修改信息
     * @return 修改结果
     * 修改岗位管理信息
     */
    @MethodDesc("修改岗位管理信息")
    @PreAuthorize("@ss.hasPermission('franchiseStation:franchiseStation:edit')")
    @Submit(fileds = "userId")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateFranchiseStation")
    public ApiResult updateFranchiseStation(@RequestBody FranchiseStationDto franchiseStationDto) {
        return franchiseStationService.updateFranchiseStation(franchiseStationDto);
    }

    /**
     * @param stationId 岗位id
     * @return 岗位信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('franchiseStation:franchiseStation:editStatusById')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "stationId") Integer stationId,
                                 @RequestParam(value = "status") Integer status) {
        return franchiseStationService.updateStatusById(stationId,status);
    }

}
