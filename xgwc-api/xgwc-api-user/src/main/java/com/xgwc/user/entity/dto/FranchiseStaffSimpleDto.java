package com.xgwc.user.entity.dto;

import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class FranchiseStaffSimpleDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("姓名")
    @Excel(name = "姓名")
    private String name;

    @FieldDesc("花名")
    @Excel(name = "花名")
    private String stageName;

    @FieldDesc("绑定的用户id")
    @Excel(name = "绑定的用户id")
    private Long bindUserId;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("部门名称")
    private String deptName;


    @FieldDesc("岗位id")
    private Long postId;

    @FieldDesc("岗位名称")
    private String postName;

}
