package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class ServiceOwnerVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("公司名称")
    private String companyName;

    @FieldDesc("联系人")
    private String contact;

    @FieldDesc("服务商id")
    private Long id;

    @FieldDesc("管理员手机号")
    private String managerPhone;

    @FieldDesc("密码")
    private String password;

    @FieldDesc("服务商类型：1：财务服务商")
    private Integer serviceType;

    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("管理员用户ID")
    private Long userId;



}
