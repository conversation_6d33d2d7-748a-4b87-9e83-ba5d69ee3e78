package com.xgwc.user.dao;

import com.xgwc.user.entity.XgwcEmpAccounts;
import com.xgwc.user.entity.XgwcEmpAttachments;
import com.xgwc.user.entity.XgwcEmployees;
import com.xgwc.user.entity.dto.XgwcEmployeesDto;
import com.xgwc.user.entity.vo.XgwcEmployeesQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface XgwcEmployeesMapper {
    /**
     * 查询员工档案
     * 
     * @param employeeId 员工档案主键
     * @return 员工档案
     */
    public XgwcEmployeesDto selectEmployeesByEmployeeId(@Param("employeeId") Long employeeId);

    /**
     * 查询员工档案列表
     * 
     * @param employees 员工档案
     * @return 员工档案集合
     */
    public List<XgwcEmployeesDto> selectEmployeesList(@Param("employees") XgwcEmployeesQueryVo employees);

    /**
     * 新增员工档案
     * 
     * @param xgwcEmployees 员工档案
     * @return 结果
     */
    public int insertEmployees(@Param("xgwcEmployees") XgwcEmployees xgwcEmployees);

    /**
     * 修改员工档案
     * 
     * @param xgwcEmployees 员工档案
     * @return 结果
     */
    public int updateEmployees(@Param("xgwcEmployees") XgwcEmployees xgwcEmployees);

    /**
     * 新增员工工资账户
     *
     * @param xgwcEmpAccounts 员工档案
     * @return 结果
     */
    int insertXgwcEmpAccounts(@Param("xgwcEmpAccounts") XgwcEmpAccounts xgwcEmpAccounts);

    /**
     * 新增员工附件信息
     *
     * @param xgwcEmpAttachments 员工档案
     * @return 结果
     */
    int insertXgwcEmpAttachments(@Param("xgwcEmpAttachments") XgwcEmpAttachments xgwcEmpAttachments);

    /**
     * 修改员工工资账户
     *
     * @param xgwcEmpAccounts 员工档案
     * @return 结果
     */
    int updateXgwcEmpAccounts(@Param("xgwcEmpAccounts") XgwcEmpAccounts xgwcEmpAccounts);

    /**
     * 修改员工附件信息
     *
     * @param xgwcEmpAttachments 员工档案
     * @return 结果
     */
    int updateXgwcEmpAttachments(@Param("xgwcEmpAttachments") XgwcEmpAttachments xgwcEmpAttachments);

    /**
     * 查询员工附件信息
     *
     * @param employeeId 员工档案
     * @return 结果
     */
    List<XgwcEmpAttachments> selectXgwcEmpAttachments(@Param("employeeId") Long employeeId);


    /**
     * 查询员工档案是否存在
     *
     * @param idNumber 员工编号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 查询员工档案是否存在
     *
     * @param phone 员工手机号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 查询员工档案是否存在
     * @param accountNumber 员工支付宝账号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByAccountNumber(@Param("accountNumber") String accountNumber);

    /**
     * 查询员工档案是否存在
     * @param alipayAccount 员工支付宝账号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByAlipayAccount(@Param("alipayAccount") String alipayAccount);

    /**
     * 删除员工附件信息
     *
     * @param employeeId 员工档案
     * @return 结果
     */
    void deleteXgwcEmpAttachments(@Param("employeeId") Long employeeId);
}
