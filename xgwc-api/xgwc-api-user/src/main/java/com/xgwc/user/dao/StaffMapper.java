package com.xgwc.user.dao;

import com.xgwc.user.entity.Staff;
import com.xgwc.user.entity.dto.SimpleDeptUserInfoDto;
import com.xgwc.user.entity.dto.StaffDto;
import com.xgwc.user.entity.vo.StaffQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StaffMapper  {
    /**
     * 查询员工管理
     * 
     * @param id 员工管理主键
     * @return 员工管理
     */
    public StaffDto selectStaffById(Long id);

    /**
     * 根据用户id获取品牌商员工信息
     * @param userId 用户id
     * @return 员工信息
     */
    StaffDto getStaffByUserId(Long userId);

    /**
     * 查询员工管理列表
     * 
     * @param staff 员工管理
     * @return 员工管理集合
     */
    public List<StaffDto> selectStaffList(StaffQueryVo staff);

    /**
     * 新增员工管理
     * 
     * @param staff 员工管理
     * @return 结果
     */
    public int insertStaff(Staff staff);

    /**
     * 修改员工管理
     * 
     * @param staff 员工管理
     * @return 结果
     */
    public int updateStaff(Staff staff);

    /**
     * 删除员工管理
     * 
     * @param id 员工管理主键
     * @return 结果
     */
    public int deleteStaffById(Long id);

    /**
     * 批量删除员工管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaffByIds(Long[] ids);

    /**
     * 根据姓名和品牌id查询员工管理
     *
     * @param name 姓名
     * @param brandId 品牌id
     * @return 员工管理
     */
    StaffDto selectStaffByNameAndBrandId( @Param("name") String name,@Param("brandId") Long brandId);

    /**
     * 根据用户id和品牌商id查询员工管理
     *
     * @param userId 用户id
     * @param brandId 品牌商id
     * @return 员工管理
     */
    StaffDto selectStaffByUserIdAndBrandId(@Param("userId") Long userId,@Param("brandId") Long brandId);

    /**
     * 获取员工管理下拉框
     *
     * @return 员工管理下拉框
     */
    List<StaffDto> selectStaffListDropDown(@Param("brandId") Long brandId);

    /**
     * 更新绑定状态
     *
     * @param id 员工管理id
     * @param bindUserId 绑定用户id
     * @param bindStatus 绑定状态
     * @param phone 手机号
     * @return 结果
     */
    int updateBindStatus( @Param("id") Long id,@Param("bindUserId") Long bindUserId,@Param("bindStatus") Integer bindStatus,@Param("phone") String phone,@Param("stageName") String stageName);

    /**
     * 根据部门ID列表获取所有员工ID
     * @param deptIds 部门ID列表
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByDeptIds(@Param(value = "list") List<Long> deptIds, @Param(value = "brandId") Long brandId);

    /**
     * 根据品牌ID列表获取所有员工ID
     * @return 用户ID
     */
    List<Long> getStaffUserIdListByBrand(@Param(value = "brandId") Long brandId);

    /**
     * 员工数量
     * @return 用户ID
     */
    Integer countStaffUserIdListByBrand(@Param(value = "brandId") Long brandId);

    /**
     * 根据部门ID列表获取所有员工ID
     * @return 用户ID
     */
    List<SimpleDeptUserInfoDto> getStaffUserInfoListByBrandId(@Param(value = "brandId") Long brandId, @Param("deptId") Long deptId);

    /**
     * 更新员工状态
     * @param id 员工ID
     * @param status 状态
     * @return 结果
     */
    int updateStaffStatus(@Param("id") Long id,@Param("status") Integer status);

    /**
     * 根据绑定用户ID查询员工信息
     * @param userId 绑定用户ID
     * @return 员工信息
     */
    StaffDto findStaffByBindUserId(Long userId);

    /**
     * 根据品牌ID查询员工信息
     * @param brandId 品牌ID
     * @return 员工信息
     */
    List<StaffDto> selectStaffByBrandId(@Param("brandId") Long brandId);

    /**
     * 根据绑定用户ID查询加盟商员工下载次数
     * @param userId 绑定用户ID
     * @return 员工信息
     */
    StaffDto findFranchiseStaffByBindUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和品牌ID查询员工信息
     * @param userId 用户ID
     * @param brandId 品牌ID
     * @return 员工信息
     */
    StaffDto findStaffByUserIdAndBrandId(@Param("userId") Long userId,@Param("brandId") Long brandId);
}
