package com.xgwc.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.vod.model.v20170321.*;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.FileUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtil;
import com.xgwc.user.config.AliyunVodConfig;
import com.xgwc.user.dao.UploadVideoRecordMapper;
import com.xgwc.user.entity.UploadVideoRecord;
import com.xgwc.user.entity.VideoInfo;
import com.xgwc.user.service.VideoService;
import com.xgwc.user.util.UploadUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Service
public class VideoServiceImpl implements VideoService {

    @Resource
    private AliyunVodConfig aliyunVodConfig;

    @Resource
    private UploadVideoRecordMapper uploadVideoRecordMapper;

    /**
     * 初始化视频连接
     */
    public DefaultAcsClient initVodClient() {
        String regionId = "cn-shenzhen"; // 区域ID
        DefaultProfile profile = DefaultProfile.getProfile(regionId, aliyunVodConfig.getAccessKeyId(), aliyunVodConfig.getAccessKeySecret());
        return new DefaultAcsClient(profile);
    }

    // 获取视频上传地址和凭证
    public CreateUploadVideoResponse createUploadVideo(DefaultAcsClient client, String title,
                                                              String fileName) throws Exception {
        CreateUploadVideoRequest request = new CreateUploadVideoRequest();
        request.setTitle(title);
        request.setFileName(fileName);
        return client.getAcsResponse(request);
    }

    // 执行文件上传到OSS
    public void uploadToOSS(String uploadAuth, String uploadAddress,
                            MultipartFile multipartFile) {
        // 解析上传凭证
        String decodedAuth = new String(Base64.decodeBase64(uploadAuth), StandardCharsets.UTF_8);
        JSONObject authJson = JSONObject.parseObject(decodedAuth);
        String accessKeyId = authJson.getString("AccessKeyId");
        String accessKeySecret = authJson.getString("AccessKeySecret");
        String securityToken = authJson.getString("SecurityToken");
        // 解析上传地址
        String decodedAddress = new String(Base64.decodeBase64(uploadAddress), StandardCharsets.UTF_8);
        String[] addressParts = decodedAddress.split("\\|");
        JSONObject jsonObject = JSONObject.parseObject(addressParts[0]);
        String endpoint = jsonObject.getString("Endpoint");
        String bucket = jsonObject.getString("Bucket");
        String objectName = jsonObject.getString("FileName");

        // 初始化OSS客户端
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, securityToken);
        try {
            // 创建上传请求
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, objectName, multipartFile.getInputStream());
            // 上传文件
            ossClient.putObject(putObjectRequest);
        }catch (Exception e){
            log.error("上传视频失败:", e);
        }
        finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    // 提交转码作业
    public SubmitTranscodeJobsResponse submitTranscodeJob(
            DefaultAcsClient client, String videoId, String templateGroupId) throws Exception {
        SubmitTranscodeJobsRequest request = new SubmitTranscodeJobsRequest();
        request.setVideoId(videoId);
        request.setTemplateGroupId(templateGroupId);
        return client.getAcsResponse(request);
    }

    @Override
    public Integer uploadVideo(MultipartFile multipartFile) {
        try {
            // 1. 初始化客户端
            DefaultAcsClient vodClient = initVodClient();
            // 2. 上传视频
            String fileName = UploadUtil.buildFileName(multipartFile);
            log.info("开始获取上传地址和凭证...");
            CreateUploadVideoResponse uploadResponse =
                    createUploadVideo(vodClient, multipartFile.getOriginalFilename(), fileName);
            log.info("开始上传视频到OSS...");
            uploadToOSS(uploadResponse.getUploadAuth(), uploadResponse.getUploadAddress(), multipartFile);
            log.info("视频上传完成");
            //睡眠三秒提交转码视频
            Thread.sleep(3000);
            // 3. 提交转码
            log.info("提交转码作业...");
            SubmitTranscodeJobsResponse transcodeResponse =
                    submitTranscodeJob(vodClient, uploadResponse.getVideoId(), aliyunVodConfig.getTemplateId());
            log.info("转码作业ID:{}",transcodeResponse.getTranscodeJobs().get(0).getJobId());
            Integer videoId = insertUploadVideoRecord(multipartFile, transcodeResponse.getTranscodeJobs().get(0).getJobId(), uploadResponse.getVideoId());
            return videoId;
        } catch (Exception e) {
            log.error("上传视频失败:", e);
        }
        return null;
    }

    @Override
    public List<VideoInfo> getUploadVideoRecordList(List<Integer> videoIds) {
        if(videoIds == null || videoIds.isEmpty()) {
            return uploadVideoRecordMapper.getUploadVideoRecordList(videoIds);
        }
        return List.of();
    }

    @Override
    public List<VideoInfo> getUploadVideoRecordList(String videoIds) {
        if(StringUtil.isNotEmpty(videoIds)){
            List<Integer> videoIdList = StringUtil.convertToIntegerList(videoIds);
            return uploadVideoRecordMapper.getUploadVideoRecordList(videoIdList);
        }
        return List.of();
    }

    @Override
    public VideoInfo getUploadVideoRecord(Integer id) {
        if(id !=null && id > 0){
            return uploadVideoRecordMapper.getUploadVideoRecordById(id);
        }
        return null;
    }

    /**
     * 插入上传记录
     */
    private Integer insertUploadVideoRecord(MultipartFile multipartFile, String transcodeJobId, String videoId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        UploadVideoRecord uploadVideoRecord = new UploadVideoRecord();
        uploadVideoRecord.setOringinName(multipartFile.getOriginalFilename());
        uploadVideoRecord.setTranscodeId(transcodeJobId);
        uploadVideoRecord.setTranscodeStatus(0);
        uploadVideoRecord.setVideoId(videoId);
        uploadVideoRecord.setCreateBy(sysUser.getUserName());
        uploadVideoRecordMapper.insertUploadVideoRecord(uploadVideoRecord);
        return uploadVideoRecord.getId();
    }
}
