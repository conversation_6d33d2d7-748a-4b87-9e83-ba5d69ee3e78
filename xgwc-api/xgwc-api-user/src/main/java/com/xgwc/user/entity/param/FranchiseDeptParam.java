package com.xgwc.user.entity.param;

import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-28  11:22
 */

/**
 * 加盟商部门管理参数
 */
@Data
public class FranchiseDeptParam {

    /**
     * 品牌名称
     */
    private String deptName;

    /**
     * 状态：0正常，1禁用
     */
    private Integer status;

    /**
     * 加盟商id
     */
    private Long FranchiseId;
}
