package com.xgwc.user.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.SysMenuMapper;
import com.xgwc.user.service.ISysMenuService;
import com.xgwc.user.entity.SysMenu;
import com.xgwc.user.entity.vo.SysMenuVo;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.vo.SysMenuQueryVo;


@Service
public class SysMenuServiceImpl implements ISysMenuService  {
    @Resource
    private SysMenuMapper sysMenuMapper;

    /**
     * 查询字典数据
     * 
     * @param id 字典数据主键
     * @return 字典数据
     */
    @Override
    public SysMenuDto selectSysMenuById(Long id) {
        return sysMenuMapper.selectSysMenuById(id);
    }

    /**
     * 查询字典数据列表
     * 
     * @param sysMenu 字典数据
     * @return 字典数据
     */
    @Override
    public List<SysMenuDto> selectSysMenuList(SysMenuQueryVo sysMenu) {
        return sysMenuMapper.selectSysMenuList(sysMenu);
    }

    /**
     * 新增字典数据
     * 
     * @param dto 字典数据
     * @return 结果
     */
    @Override
    public int insertSysMenu(SysMenuVo dto) {

        SysMenu sysMenu = BeanUtil.copyProperties(dto, SysMenu.class);
        sysMenu.setCreateTime(DateUtils.getNowDate());
        return sysMenuMapper.insertSysMenu(sysMenu);
    }

    /**
     * 修改字典数据
     * 
     * @param dto 字典数据
     * @return 结果
     */
    @Override
    public int updateSysMenu(SysMenuVo dto) {

        SysMenu sysMenu = BeanUtil.copyProperties(dto, SysMenu.class);
        sysMenu.setUpdateTime(DateUtils.getNowDate());
        return sysMenuMapper.updateSysMenu(sysMenu);
    }

    /**
     * 批量删除字典数据
     * 
     * @param ids 需要删除的字典数据主键
     * @return 结果
     */
    @Override
    public int deleteSysMenuByIds(Long[] ids) {
        return sysMenuMapper.deleteSysMenuByIds(ids);
    }

    /**
     * 删除字典数据信息
     * 
     * @param id 字典数据主键
     * @return 结果
     */
    @Override
    public int deleteSysMenuById(Long id) {
        return sysMenuMapper.deleteSysMenuById(id);
    }

    @Override
    public List<SysMenuDto> selectSysMenuByModelType(String modelType) {
        return sysMenuMapper.selectSysMenuByModelType(modelType);
    }
}
