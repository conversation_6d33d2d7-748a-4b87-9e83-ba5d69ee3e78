package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class SysRoleDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("角色ID")
    @Excel(name = "角色ID")
    private Long id;

    @FieldDesc("角色名称")
    @Excel(name = "角色名称")
    private String name;

    @FieldDesc("角色权限字符串")
    @Excel(name = "角色权限字符串")
    private String code;

    @FieldDesc("显示顺序")
    @Excel(name = "显示顺序")
    private Long sort;

    @FieldDesc("角色状态（0正常 1停用）")
    @Excel(name = "角色状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("角色类型")
    @Excel(name = "角色类型")
    private Integer type;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("name",getName())
            .append("code",getCode())
            .append("sort",getSort())
            .append("status",getStatus())
            .append("type",getType())
            .append("remark",getRemark())
            .append("isDel",getCode())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
        .toString();
        }
}
