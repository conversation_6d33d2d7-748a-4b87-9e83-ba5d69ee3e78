package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class SysDictTypeDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("是否设置数据：0否 1是")
    private Long isData;

    @FieldDesc("数据设置方")
    @Excel(name = "数据设置方")
    private String dictData;

    @FieldDesc("字典编号")
    @Excel(name = "字典编号")
    private Long dictId;

    @FieldDesc("字典名称")
    @Excel(name = "字典名称")
    private String dictLabel;

    @FieldDesc("字典排序")
    @Excel(name = "字典排序")
    private Long dictSort;

    @FieldDesc("字典编码")
    @Excel(name = "字典编码")
    private String dictValue;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("字典状态")
    @Excel(name = "字典状态")
    private Integer status;

    @FieldDesc("创建用户id")
    @Excel(name = "创建用户id")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人id")
    @Excel(name = "修改人id")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("dictData",getDictData())
            .append("dictId",getDictId())
            .append("dictLabel",getDictLabel())
            .append("dictSort",getDictSort())
            .append("dictValue",getDictValue())
            .append("isDel",getIsDel())
            .append("modifyTime",getModifyTime())
            .append("remark",getRemark())
            .append("status",getStatus())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
        }
}
