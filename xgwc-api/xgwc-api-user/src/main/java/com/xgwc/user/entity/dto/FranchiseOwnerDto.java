package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.Date;


@Data
public class FranchiseOwnerDto {

    private static final long serialVersionUID=1L;

    /** 加盟商ID */
    @FieldDesc("加盟商ID")
    private Long id;

    /** 营业执照 */
    @FieldDesc("营业执照")
    private String businessLicense;

    /** 公司名称 */
    @FieldDesc("公司名称")
    private String companyName;

    /** 公司简称 */
    @FieldDesc("公司简称")
    private String companySimpleName;

    /** 营业执照编码 */
    @FieldDesc("营业执照编码")
    private String licenseCode;

    /** 执照有效期是否长期：0长期，1非长期 */
    @FieldDesc("执照有效期是否长期")
    private Integer licenseIsLongterm;

    /** 营业执照开始时间 */
    @FieldDesc("营业执照开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date licenseStart;

    /** 营业执照结束时间 */
    @FieldDesc("营业执照结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date licenseEnd;

    /** 营业省 */
    @FieldDesc("营业省")
    private Integer operateProvince;

    /** 营业市 */
    @FieldDesc("营业市")
    private Integer operateCity;

    /** 营业区 */
    @FieldDesc("营业区")
    private Integer operateRegion;

    /** 详细地址 */
    @FieldDesc("详细地址")
    private String operateAddress;

    /** 规模 */
    @FieldDesc("规模")
    private String scale;

    /** 身份证正面：加密 */
    @FieldDesc("身份证正面")
    @NotNull("身份证正面不能为空")
    private String idcardFront;

    /** 身份证反面：加密 */
    @FieldDesc("身份证反面")
    @NotNull("身份证反面不能为空")
    private String idcardBack;

    /** 身份证名称：加密 */
    @FieldDesc("身份证名称")
    @NotNull("身份证名称不能为空")
    private String idcardName;

    /** 身份证号：加密 */
    @FieldDesc("身份证号")
    @NotNull("身份证号不能为空")
    private String idcardNo;

    /** 身份证是否长期：0长期，1非长期 */
    @FieldDesc("身份证是否长期")
    private Integer idcardIsLongterm;

    /** 身份证开始时间 */
    @FieldDesc("身份证开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idcardStart;

    /** 身份证结束时间 */
    @FieldDesc("身份证结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idcardEnd;

    /** 法人手机号：加密 */
    @FieldDesc("法人手机号")
    @NotNull("法人手机号不能为空")
    private String idcardPhone;

    /** 邮箱：加密 */
    @FieldDesc("邮箱")
    @NotNull("邮箱不能为空")
    private String idcardEmail;

    /** 开户名：加密 */
    @FieldDesc("开户名")
    @NotNull("开户名不能为空")
    private String bankUserName;

    /** 开户行 */
    @FieldDesc("开户行")
    private String bankName;

    /** 对公账号：加密 */
    @FieldDesc("对公账号")
    @NotNull("对公账号不能为空")
    private String bankNo;

    /** 管理员姓名 */
    @FieldDesc("管理员姓名")
    private String managerName;

    /** 管理员手机号：加密 */
    @FieldDesc("管理员手机号")
    @NotNull("管理员手机号不能为空")
    private String managerPhone;

    /** 验证码 */
    @FieldDesc("验证码")
    //@NotNull("验证码不能为空")
    private String code;

   // @NotNull("密码不能为空")
    @FieldDesc("密码")
    private String password;

    @FieldDesc("所属品牌商id")
    private Long brandId;

    @FieldDesc("管理员用户id")
    private Long managerUserId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    private Long mainUserId;

    private Long franchiseId;
}
