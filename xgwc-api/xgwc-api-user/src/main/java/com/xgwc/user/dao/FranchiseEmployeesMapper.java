package com.xgwc.user.dao;

import com.xgwc.user.entity.FranchiseEmpAccounts;
import com.xgwc.user.entity.FranchiseEmpAttachments;
import com.xgwc.user.entity.FranchiseEmployees;
import com.xgwc.user.entity.dto.FranchiseEmployeesDto;
import com.xgwc.user.entity.vo.FranchiseEmployeesQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface FranchiseEmployeesMapper {
    /**
     * 查询员工档案
     * 
     * @param employeeId 员工档案主键
     * @return 员工档案
     */
    FranchiseEmployeesDto selectEmployeesByEmployeeId(@Param("employeeId") Long employeeId);

    /**
     * 查询员工档案列表
     * 
     * @param employees 员工档案
     * @return 员工档案集合
     */
    List<FranchiseEmployeesDto> selectEmployeesList(@Param("employees") FranchiseEmployeesQueryVo employees);

    /**
     * 新增员工档案
     * 
     * @param franchiseEmployees 员工档案
     * @return 结果
     */
    int insertEmployees(@Param("franchiseEmployees") FranchiseEmployees franchiseEmployees);

    /**
     * 修改员工档案
     * 
     * @param franchiseEmployees 员工档案
     * @return 结果
     */
    int updateEmployees(@Param("franchiseEmployees") FranchiseEmployees franchiseEmployees);

    /**
     * 新增员工工资账户
     *
     * @param franchiseEmpAccounts 员工档案
     * @return 结果
     */
    int insertFranchiseEmpAccounts(@Param("franchiseEmpAccounts") FranchiseEmpAccounts franchiseEmpAccounts);

    /**
     * 新增员工附件信息
     *
     * @param franchiseEmpAttachments 员工档案
     * @return 结果
     */
    int insertFranchiseEmpAttachments(@Param("franchiseEmpAttachments") FranchiseEmpAttachments franchiseEmpAttachments);

    /**
     * 修改员工工资账户
     *
     * @param franchiseEmpAccounts 员工档案
     * @return 结果
     */
    int updateFranchiseEmpAccounts(@Param("franchiseEmpAccounts") FranchiseEmpAccounts franchiseEmpAccounts);

    /**
     * 修改员工附件信息
     *
     * @param franchiseEmpAttachments 员工档案
     * @return 结果
     */
    int updateFranchiseEmpAttachments(@Param("franchiseEmpAttachments") FranchiseEmpAttachments franchiseEmpAttachments);

    /**
     * 查询员工附件信息
     *
     * @param employeeId 员工档案
     * @return 结果
     */
    List<FranchiseEmpAttachments> selectFranchiseEmpAttachments(@Param("employeeId") Long employeeId);

    /**
     * 查询员工档案是否存在
     *
     * @param idNumber 员工编号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 查询员工档案是否存在
     *
     * @param phone 员工手机号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 查询员工档案是否存在
     * @param accountNumber 员工支付宝账号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByAccountNumber(@Param("accountNumber") String accountNumber);

    /**
     * 查询员工档案是否存在
     * @param alipayAccount 员工支付宝账号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByAlipayAccount(@Param("alipayAccount") String alipayAccount);

    /**
     * 删除员工附件信息
     *
     * @param employeeId 员工档案
     * @return 删除结果
     */
    void deleteFranchiseEmpAttachments(@Param("employeeId") Long employeeId);
}
