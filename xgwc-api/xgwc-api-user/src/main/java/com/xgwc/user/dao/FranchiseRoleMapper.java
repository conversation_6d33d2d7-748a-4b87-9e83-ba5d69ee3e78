package com.xgwc.user.dao;


import com.xgwc.user.entity.dto.FranchiseRoleDto;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.param.FranchiseRoleParam;
import com.xgwc.user.entity.vo.FranchiseRoleVo;
import com.xgwc.user.entity.vo.SysRoleDataVo;
import com.xgwc.user.entity.vo.SysRoleDateDownloadLimitVo;
import com.xgwc.user.entity.vo.SysRoleMenuVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.dao
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  13:39
 */

public interface FranchiseRoleMapper {

    /**
     * 分页查询加盟商角色列表
     * @param franchiseRoleParam 加盟商角色参数
     * @return 加盟商角色列表
     */
    List<FranchiseRoleVo> getFranchiseRoleList(@Param("franchiseRoleParam") FranchiseRoleParam franchiseRoleParam);

    /**
     * 保存加盟商角色
     * @param franchiseRoleDto 加盟商角色信息
     * @return 插入信息
     */
    int saveFranchiseRole(@Param("franchiseRoleDto") FranchiseRoleDto franchiseRoleDto);

    /**
     * 根据角色ID查询加盟商角色信息
     * @param roleId 角色id
     * @return 加盟商角色信息
     */
    FranchiseRoleVo getFranchiseRoleById(@Param("roleId") Long roleId);

    /**
     * 修改加盟商角色信息
     * @param franchiseRoleDto 加盟商角色信息
     * @return 修改信息
     */
    int updateFranchiseRole(@Param("franchiseRoleDto") FranchiseRoleDto franchiseRoleDto);

    /**
     * 修改加盟商角色状态
     * @param roleId 角色id
     * @param status 状态
     * @return 修改信息
     */
    int updateStatusById(@Param("roleId") Integer roleId, @Param("status") Integer status);

    /**
     * 保存角色菜单关系
     * @param sysRoleMenuVoList 角色菜单关系
     * @return 保存信息
     */
    int saveRoleMenu(@Param("sysRoleMenuVoList") List<SysRoleMenuVo> sysRoleMenuVoList);

    /**
     * 删除角色菜单关系
     * @param roleId 角色id
     * @return 删除信息
     */
    void deleteRoleMenu(@Param("roleId") Long roleId);

    /**
     * 保存角色数据权限关系
     * @param sysRoleDataVo 角色数据权限关系
     * @return 保存信息
     */
    int saveFranchiseRoleDataScope(@Param("sysRoleDataVo") SysRoleDataVo sysRoleDataVo);

    /**
     * 获取角色数据权限
     * @param roleId 角色id
     * @return 角色数据权限
     */
    List<SysRoleDataVo> getFranchiseRoleDataById(@Param("roleId") Long roleId);

    /**
     * 获取角色菜单权限
     * @param roleId 角色id
     * @return 角色菜单权限
     */
    List<SysRoleMenuVo> getFranchiseRoleMenusById(@Param("roleId") Long roleId);

    /**
     * 删除角色数据权限
     * @param roleId 角色id
     */
    void deleteRoleData(@Param("roleId") Long roleId);

    /**
     * 批量保存角色数据权限
     * @param dataMenu 角色数据权限
     * @return 角色数据权限id
     */
    int savefranchiseBrandRoleDataScopeDeptId(@Param("dataMenu") SysRoleDataVo dataMenu);

    /**
     * 根据用户id查询角色信息
     * @param userId 用户id
     * @return 角色信息
     */
    List<FranchiseRoleVo> selectRoleByUserId(@Param("userId") Long userId);

    /**
     * 根据用户id查询菜单信息
     * @param userId 用户id
     * @return 菜单信息
     */
    List<SysMenuDto> selectMenuByUserId(@Param("userId") Long userId);

    /**
     * 保存角色数据权限下载权限
     * @param dataMenu 角色数据权限下载权限
     * @return 角色数据权限下载权限id
     */
    int saveDownloadLimit(@Param("dataMenu") SysRoleDataVo dataMenu);

    /**
     * 获取角色数据权限下载权限
     *
     * @param roleId      角色id
     * @param franchiseId 加盟商id
     * @return 角色数据权限下载权限
     */
    List<SysRoleDateDownloadLimitVo> getFranchiseRoleDownloadById(@Param("roleId") Long roleId , @Param("franchiseId") Long franchiseId);

    /**
     * 删除角色数据权限下载权限
     * @param roleId 角色id
     */
    void deleteRoleDownloadLimit(Long roleId);

    /**
     * 查询品牌商设置下载次数列表
     *
     * @return 品牌商设置下载次数列表
     */
    List<SysRoleDateDownloadLimitVo> selectBrandSetDownload(@Param("franchiseId") Long franchiseId);

    /**
     * 查询菜单是否有子级菜单
     *
     * @return true、false
     */
    Boolean selectLastLevelMenu(@Param("menuId") Long menuId);

    Boolean selectLastLevelMenuDate(@Param("menuId") Long menuId);

    /**
     * 根据userId查询角色列表
     *
     * @return 服务人员角色列表
     */
    List<FranchiseRoleVo> selectRoleServiceByUserId(@Param("userId") Long userId);

    /**
     * 根据userId查询菜单列表
     *
     * @return 服务人员菜单列表
     */
    List<SysMenuDto> selectMenuServiceByUserId(@Param("userId") Long userId);

    /**
     * 根据userId查询销售服务商角色列表
     *
     * @return 销售服务商菜单列表
     */
    List<FranchiseRoleVo> selectRoleMarketByUserId(Long userId);

    /**
     * 根据userId查询销售服务商菜单列表
     *
     * @return 销售服务商菜单列表
     */
    List<SysMenuDto> selectMenuMarketByUserId(Long userId);
}
