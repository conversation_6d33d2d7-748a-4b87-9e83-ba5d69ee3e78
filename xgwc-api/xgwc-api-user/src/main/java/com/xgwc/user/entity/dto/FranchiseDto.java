package com.xgwc.user.entity.dto;

import lombok.Data;

@Data
public class FranchiseDto {

    /**
     * 加盟商id
     */
    private Long id;

    /**
     * 管理员名称
     */
    private String managerName;

    /**
     * 加盟商名称
     */
    private String franchiseName;

    /**
     * 花名
     */
    private String stageName;

    /**
     * 管理员手机号
     */
    private String managerPhone;

    /**
     * 管理员用户id
     */
    private Long managerUserId;

    /**
     * 状态：0正常，其余非正常
     */
    private Integer status;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 是否删除
     */
    private Integer isDel;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更信人
     */
    private String updateBy;
}
