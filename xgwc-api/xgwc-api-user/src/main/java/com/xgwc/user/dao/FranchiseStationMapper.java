package com.xgwc.user.dao;


import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.dto.FranchiseStationDto;
import com.xgwc.user.entity.param.FranchiseStationParam;
import com.xgwc.user.entity.vo.FranchiseStationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.dao
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-28  14:16
 */

public interface FranchiseStationMapper {

    /**
     * 分页查询加盟商岗位信息
     * @param franchiseStationParam 查询参数
     * @return 加盟商岗位信息集合
     */
    List<FranchiseStationVo> getFranchiseStationList(@Param("franchiseStationParam") FranchiseStationParam franchiseStationParam);
    /**
     * 保存加盟商岗位信息
     * @param franchiseStationDto 加盟商岗位信息
     * @return 插入结果
     */
    int saveFranchiseStation(@Param("franchiseStationDto") FranchiseStationDto franchiseStationDto);

    /**
     * 修改加盟商岗位信息
     * @param franchiseStationDto 加盟商岗位信息
     * @return 修改结果
     */
    int updateFranchiseStation(@Param("franchiseStationDto") FranchiseStationDto franchiseStationDto);

    /**
     * 根据加盟商岗位ID查询加盟商岗位信息
     * @param stationId 加盟商岗位ID
     * @return 加盟商岗位信息
     */
    int updateStatusById(@Param("stationId") Integer stationId, @Param("status") Integer status);

    /**
     * 根据ID查询加盟商岗位信息
     * @param stationId 加盟商岗位ID
     * @return 加盟商岗位信息
     */
    FranchiseStationVo getFranchiseStationById(@Param("stationId") Integer stationId);


}
