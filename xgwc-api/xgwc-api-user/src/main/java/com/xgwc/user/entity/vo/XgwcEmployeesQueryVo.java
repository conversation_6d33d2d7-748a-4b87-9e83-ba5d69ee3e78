package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class XgwcEmployeesQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("员工唯一标识")
    private Long employeeId;

    @FieldDesc("员工外键")
    private Long staffId;

    @FieldDesc("品牌商id")
    private Long brandOwnerId;

    @FieldDesc("员工姓名")
    private String name;

    @FieldDesc("用户性别（0男 1女 2未知）")
    private Long sex;

    @FieldDesc("民族")
    private Integer ethnicity;

    @FieldDesc("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[]  birthdate;

    @FieldDesc("学历")
    private Integer education;

    @FieldDesc("政治面貌")
    private Integer politicalStatus;

    @FieldDesc("婚姻状况")
    private Integer maritalStatus;

    @FieldDesc("身份证号")
    private String idNumber;

    @FieldDesc("电子邮箱")
    private String email;

    @FieldDesc("手机号码")
    private String phone;

    @FieldDesc("联系地址")
    private String address;

    @FieldDesc("紧急联系人姓名")
    private String emerName;

    @FieldDesc("紧急联系人电话")
    private String emerPhone;

    @FieldDesc("入职日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[]  entryDate;

    @FieldDesc("合同到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[]  contractEndDate;

    @FieldDesc("社保状态，（0-未买，1-已买，2-停保）")
    private Long socialStatus;

    @FieldDesc("购买社保日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date[] buySocialDate;

    @FieldDesc("是否转正，（0-否，1-是）")
    private Long probationStatus;

    @FieldDesc("转正日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[]  probationEndDate;

    @FieldDesc("年假天数")
    private Long annualLeaveDays;

    @FieldDesc("离职日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[]  resignationDate;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;


    @FieldDesc("花名")
    private String stageName;

    @FieldDesc("状态：0在职，1离职")
    private String status;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    private String jobNature;

    @FieldDesc("岗位id")
    private Integer stationId;

    @FieldDesc("部门id")
    private Integer deptId;

    @FieldDesc("公司id")
    private Integer companyId;

    @FieldDesc("时间类型（0-入职时间，1-合同到期，2-离职时间，3-出生日期）")
    private Integer dateFlag;

    private Integer pageNum;
    private Integer pageSize;

}
