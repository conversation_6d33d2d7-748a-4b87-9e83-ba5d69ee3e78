package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.FranchiseEmployeesCountDto;
import com.xgwc.user.entity.dto.FranchiseEmployeesDto;
import com.xgwc.user.entity.vo.FranchiseEmployeesQueryVo;
import com.xgwc.user.entity.vo.FranchiseEmployeesVo;

public interface FranchiseEmployeesService {
    /**
     * 查询员工档案
     * 
     * @param employeeId 员工档案主键
     * @return 员工档案
     */
    FranchiseEmployeesDto selectEmployeesByEmployeeId(Long employeeId);

    /**
     * 查询员工档案列表
     *
     * @param employees 员工档案
     * @return 员工档案集合
     */
    FranchiseEmployeesCountDto selectEmployeesList(FranchiseEmployeesQueryVo employees);

    /**
     * 新增员工档案
     *
     * @param employees 员工档案
     * @return 结果
     */
    ApiResult insertEmployees(FranchiseEmployeesVo employees);

    /**
     * 修改员工档案
     *
     * @param employees 员工档案
     * @return 结果
     */
    ApiResult updateEmployees(FranchiseEmployeesVo employees);

    /**
     * 员工申请离职
     *
     * @param employees 员工档案
     * @return 结果
     */
    int resignationsEmployees(FranchiseEmployeesVo employees);
}
