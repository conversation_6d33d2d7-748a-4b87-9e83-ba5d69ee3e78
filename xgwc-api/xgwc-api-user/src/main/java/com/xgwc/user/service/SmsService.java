package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;

public interface SmsService {

    /**
     * 发送登录验证码
     * @param mobile 手机号
     * @return 结果
     */
    ApiResult sendLoginCode(String mobile);

    /**
     * 发送注册验证码
     * @param mobile 手机号
     * @return 结果
     */
    ApiResult sendRegisterCode(String mobile);

    /**
     * 校验登录验证码
     * @param mobile 手机号
     * @return 校验结果
     */
    boolean validLoginCode(String mobile, String code);

    /**
     * 校验注册验证码
     * @param mobile 手机号
     * @return 校验结果
     */
    boolean validRegisterCode(String mobile, String code);
}
