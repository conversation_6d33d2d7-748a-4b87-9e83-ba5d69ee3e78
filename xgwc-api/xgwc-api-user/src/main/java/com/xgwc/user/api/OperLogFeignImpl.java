package com.xgwc.user.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.feign.api.OperLogFeign;
import com.xgwc.user.feign.entity.SysOperLog;
import com.xgwc.user.service.ISysOperLogService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OperLogFeignImpl implements OperLogFeign {

    @Resource
    private ISysOperLogService sysOperLogService;

    @Override
    public ApiResult addLog(SysOperLog log) {
        return ApiResult.toAjax(sysOperLogService.insertSysOperLog(log));
    }
}
