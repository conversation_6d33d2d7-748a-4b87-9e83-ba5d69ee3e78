package com.xgwc.user.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.FranchiseOwner;
import lombok.Data;


@Data
public class MyFranchiseOwnerQueryDto{

    private static final long serialVersionUID=1L;

    /** 状态：0正常，1禁用 */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("品牌商ID")
    private Long brandId;

    @FieldDesc("加盟商ID")
    private Long franchiseId;


}
