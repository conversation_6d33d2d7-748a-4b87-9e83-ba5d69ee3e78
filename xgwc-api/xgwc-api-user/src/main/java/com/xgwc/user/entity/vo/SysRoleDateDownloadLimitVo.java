package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.entity.vo
 * @Author: kouwenzhuo
 * @CreateTime: 2025-06-19  14:35
 */
@Data
public class SysRoleDateDownloadLimitVo {

    /** 主键id */
    private Long id;

    /** 角色id */
    private Long roleId;

    /** 品牌商id */
    private Long brandId;

    /** 品牌商简称 */
    private String brandName;

    /** 加盟商id */
    private Long franchiseId;

    @FieldDesc("下载次数上限/天/人（品牌商设置给加盟商）")
    private Integer downloadLimit;

    /** 下载次数/天 （加盟商设置给角色）*/
    private Integer downloadLimitRole;

    /** 今日已下载次数 */
    private Integer downloadCount;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;
}
