package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

/**
 * 关联员工档案-附件表
 */
@Data
public class XgwcEmpAttachments {

private static final long serialVersionUID=1L;

    /** 附件唯一标识 */
    private Long attachmentId;

    /** 员工ID */
    private Long employeeId;

    /** 附件类型（0-简历 1-劳动合同 2-证明材料） */
    private Integer attachmentType;

    /** 文件名 */
    private String fileName;

    /** 文件路径 */
    private String filePath;

    /** 文件数量 */
    private Integer fileCount;

    /** 上传日期 */
    private Date uploadDate;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;



}