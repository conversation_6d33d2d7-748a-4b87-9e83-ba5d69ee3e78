package com.xgwc.user.dao;

import java.util.List;
import com.xgwc.user.entity.ServiceOwner;
import com.xgwc.user.entity.dto.ServiceOwnerSimpleDto;
import com.xgwc.user.entity.vo.ServiceOwnerVo;
import com.xgwc.user.entity.dto.ServiceOwnerDto;
import com.xgwc.user.entity.vo.ServiceOwnerQueryVo;


public interface ServiceOwnerMapper  {
    /**
     * 查询服务商
     * @param id 服务商主键
     * @return 服务商
     */
    public ServiceOwnerDto selectServiceOwnerById(Long id);

    /**
     * 查询服务商列表
     * @param serviceOwner 服务商
     * @return 服务商集合
     */
    public List<ServiceOwnerDto> selectServiceOwnerList(ServiceOwnerQueryVo serviceOwner);

    /**
     * 新增服务商
     * @param serviceOwner 服务商
     * @return 结果
     */
    public int insertServiceOwner(ServiceOwner serviceOwner);

    /**
     * 修改服务商
     * @param serviceOwner 服务商
     * @return 结果
     */
    public int updateServiceOwner(ServiceOwner serviceOwner);

    /**
     * 删除服务商
     * @param id 服务商主键
     * @return 结果
     */
    public int deleteServiceOwnerById(Long id);

    /**
     * 根据用户id查询服务商
     * @param userId 用户id
     * @return 结果
     */
    ServiceOwnerDto selectServiceOwnerByUserId(Long userId);

    /**
     * 根据用户id查询服务商
     * @param userId 用户id
     * @return 结果
     */
    ServiceOwnerDto selectServiceOwnerStaffByUserId(Long userId);


    /**
     * 查询简易服务商列表
     * @return 服务商集合
     */
    List<ServiceOwnerSimpleDto> findServiceOwnerListForSelect(Integer serviceType);

}
