package com.xgwc.user.controller;

import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.MessageTypeKey;
import com.xgwc.user.entity.SysMessage;
import com.xgwc.user.service.SysMessageService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("sysmessage")
@RestController
public class SysMessageController extends BaseController {

    @Resource
    private SysMessageService sysMessageService;

    /**
     * 添加系统通知
     */
    @PostMapping("addSysMessage")
    public ApiResult addSysMessage(@RequestBody @Valid SysMessage sysMessage) {
        int result = sysMessageService.insertSysMessage(sysMessage);
        return result > 0 ? ApiResult.ok() : ApiResult.error("");
    }

    /**
     * 添加系统通知
     */
    @PostMapping("addSysMessages")
    public ApiResult addSysMessages(@RequestBody List<SysMessage> sysMessages) {
        int result = sysMessageService.batchInsertSysMessage(sysMessages);
        return result > 0 ? ApiResult.ok() : ApiResult.error("");
    }

    /**
     * 获取通知详情
     */
    @GetMapping("get_detail")
    public ApiResult getMessageDetail(SysMessage sysMessage) {
        SysMessage result = sysMessageService.getMessageDetail(sysMessage.getId());
        return ApiResult.ok(result);
    }

    /**
     * 获取通知列表
     */
    @GetMapping("getMessageList")
    public ApiResult getMessageList(SysMessage sysMessage) {
        startPage();
        List<SysMessage> sysMessages = sysMessageService.getMessageList(sysMessage);
        return getDataTable(sysMessages);
    }

    /**
     * 获取typeKey分组
     */
    @GetMapping("getMessageTypeKeyGroup")
    public ApiResult getMessageTypeKeyGroup() {
        List<MessageTypeKey> messageTypeKeyGroup = sysMessageService.getMessageTypeKeyGroup();
        return ApiResult.ok(messageTypeKeyGroup);
    }
}
