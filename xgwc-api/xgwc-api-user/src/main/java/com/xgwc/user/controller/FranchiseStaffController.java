package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.FranchiseStaff;
import com.xgwc.user.entity.dto.FranchiseStaffDto;
import com.xgwc.user.entity.dto.FranchiseStaffPageDto;
import com.xgwc.user.entity.dto.FranchiseStaffSimpleDto;
import com.xgwc.user.entity.dto.StaffLogDto;
import com.xgwc.user.entity.vo.FranchiseStaffQueryVo;
import com.xgwc.user.entity.vo.FranchiseStaffVo;
import com.xgwc.user.feign.entity.FeignFranchiseStaffDto;
import com.xgwc.user.service.IFranchiseStaffService;
import com.xgwc.user.service.IStaffLogService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 加盟商员工Controller
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
@RestController
@RequestMapping("/franchiseStaff")
@Validated
@Slf4j
public class FranchiseStaffController extends BaseController {
    @Autowired
    private IFranchiseStaffService franchiseStaffService;
    @Resource
    private IStaffLogService staffLogService;

    /**
     * 查询加盟商员工列表
     */
    @MethodDesc("查询加盟商员工列表")
    @PreAuthorize("@ss.hasPermission('franchiseStaff:franchiseStaff:list')")
    @GetMapping("/list")
    public ApiResult<FranchiseStaffPageDto> list(FranchiseStaffQueryVo franchiseStaff) {
        startPage();
        List<FranchiseStaffPageDto> list = franchiseStaffService.selectFranchiseStaffList(franchiseStaff);
        return getDataTable(list);
    }

    /**
     * 获取加盟商员工详细信息
     */
    @MethodDesc("获取加盟商员工详细信息")
    @PreAuthorize("@ss.hasPermission('franchiseStaff:franchiseStaff:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<FranchiseStaffDto> getInfo(@PathVariable("id") Long id) {
        return success(franchiseStaffService.selectFranchiseStaffById(id));
    }

    /**
     * 新增加盟商员工
     */
    @MethodDesc("新增加盟商员工")
    @PreAuthorize("@ss.hasPermission('franchiseStaff:franchiseStaff:add')")
    @Log(title = "加盟商员工", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody @Valid FranchiseStaffVo franchiseStaff) {
        return toAjax(franchiseStaffService.insertFranchiseStaff(franchiseStaff));
    }

    /**
     * 修改加盟商员工
     */
    @MethodDesc("修改加盟商员工")
    @PreAuthorize("@ss.hasPermission('franchiseStaff:franchiseStaff:edit')")
    @Log(title = "加盟商员工", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody @Valid FranchiseStaffVo franchiseStaff) {
        return toAjax(franchiseStaffService.updateFranchiseStaff(franchiseStaff));
    }

    /**
     * 删除加盟商员工
     */
    @MethodDesc("删除加盟商员工")
    @PreAuthorize("@ss.hasPermission('franchiseStaff:franchiseStaff:remove')")
    @Log(title = "加盟商员工", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public ApiResult remove(@PathVariable Long id) {
        return toAjax(franchiseStaffService.deleteFranchiseStaffById(id));
    }

    @MethodDesc("根据员工id查询痕迹")
    @GetMapping(value = "/getStaffLog/{id}")
    @PreAuthorize("@ss.hasPermission('franchiseStaff:franchiseStaff:query')")
    public ApiResult getStaffLog(@PathVariable("id") Long id){
        startPage();
        List<StaffLogDto> list = staffLogService.findStaffLogByStaffIdAndBusinessType(id, 2);
        return getDataTable(list);
    }

    /**
     * 查询加盟商员工下拉框
     *
     * @return 员工管理下拉框 name + "/" + stageName
     */
    @MethodDesc("查询加盟商员工下拉框")
    @GetMapping("/selectFranchiseStaffListDropDown")
    public ApiResult selectFranchiseStaffListDropDown(){
        try {
            List<Map<String, Object>> result = franchiseStaffService.selectFranchiseStaffListDropDown();
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取员工管理下拉框失败", e);
            return ApiResult.error("获取员工管理下拉框失败");
        }
    }

    @MethodDesc("加盟商员工下载次数限制列表")
    @PreAuthorize("@ss.hasPermission('franchiseStaff:franchiseStaff:downloadLimit')")
    @GetMapping("/getStaffDownloadLimitList")
    public ApiResult getStaffDownloadLimitList(FranchiseStaffQueryVo franchiseStaff){
        startPage();
        List<FranchiseStaffDto> list = franchiseStaffService.selectStaffDownloadLimitList(franchiseStaff);
        return getDataTable(list);
    }

    @MethodDesc("修改加盟商员工下载次数限制")
    @PreAuthorize("@ss.hasPermission('franchiseStaff:franchiseStaff:updatedownloadLimit')")
    @PutMapping("/updateStaffDownloadLimit")
    public ApiResult updateStaffDownloadLimit(@RequestBody FranchiseStaff franchiseStaff){
        return franchiseStaffService.updateStaffDownloadLimit(franchiseStaff);
    }

    @MethodDesc("查询加盟商员工下拉框")
    @GetMapping("/dropDownList")
    public ApiResult dropDownList(@RequestParam(value = "deptId",required = false) Long deptId){
        try {
            List<FranchiseStaffSimpleDto> list = franchiseStaffService.dropDownList(deptId);
            return ApiResult.ok(list);
        } catch (Exception e) {
            log.error("获取员工管理下拉框失败", e);
            return ApiResult.error("获取员工管理下拉框失败");
        }
    }

    @MethodDesc("根据用户id和加盟商id查询加盟商员工")
    @GetMapping("/getFranchiseStaffInfoByUserIdAndFranchiseId")
    public ApiResult getFranchiseStaffInfoByUserIdAndFranchiseId(@RequestParam("userId") Long userId,
                                                             @RequestParam("franchiseId") Long franchiseId){
        return ApiResult.ok(franchiseStaffService.selectFranchiseStaffInfoByUserIdAndFranchiseId(userId, franchiseId));
    }

    /**
     * feign调用新增加盟商员工
     */
    @PostMapping("/addFranchiseStaff")
    public ApiResult<Long> addFranchiseStaff(@RequestBody FeignFranchiseStaffDto franchiseStaff) {
        return ApiResult.ok(franchiseStaffService.feignAddFranchiseStaff(franchiseStaff));
    }

    @MethodDesc("根据加盟商id和部门id查询员工")
    @GetMapping("/getStaffByDeptIdAndFranchiseId")
    public ApiResult getStaffByDeptIdAndFranchiseId(@RequestParam("deptId") Long deptId,
                                                   @RequestParam("franchiseId") Long franchiseId) {
        if(deptId == null || franchiseId == null){
            return ApiResult.error("参数不能为空");
        }
        return ApiResult.ok(franchiseStaffService.selectStaffByDeptIdAndFranchiseId(deptId, franchiseId));
    }
}
