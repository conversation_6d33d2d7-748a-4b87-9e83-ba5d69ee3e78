package com.xgwc.user.entity.dto;

import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class ServiceOwnerSimpleDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String companyName;

    @FieldDesc("服务商id")
    @Excel(name = "服务商id")
    private Long id;

    @FieldDesc("管理员手机号")
    @Excel(name = "管理员手机号")
    private String managerPhone;

}
