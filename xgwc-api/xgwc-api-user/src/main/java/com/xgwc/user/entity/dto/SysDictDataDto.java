package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class SysDictDataDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("创建用户id")
    @Excel(name = "创建用户id")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("样式属性（其他样式扩展）")
    @Excel(name = "样式属性（其他样式扩展）")
    private String cssClass;

    @FieldDesc("字典编码")
    @Excel(name = "字典编码")
    private Long dictCode;

    @FieldDesc("字典标签")
    @Excel(name = "字典标签")
    private String dictLabel;

    @FieldDesc("字典排序")
    @Excel(name = "字典排序")
    private Long dictSort;

    @FieldDesc("字典类型")
    @Excel(name = "字典类型")
    private String dictType;

    @FieldDesc("字典键值")
    @Excel(name = "字典键值")
    private String dictValue;

    @FieldDesc("是否默认（Y是 N否）")
    @Excel(name = "是否默认（Y是 N否）")
    private String isDefault;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;

    @FieldDesc("表格回显样式")
    @Excel(name = "表格回显样式")
    private String listClass;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("上级字典编码")
    @Excel(name = "上级字典编码")
    private Long parentDictCode;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("状态（0正常 1停用）")
    @Excel(name = "状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("修改人id")
    @Excel(name = "修改人id")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("品牌商id")
    private Long brandId;

    private String label;

    private String value;

    public String getValue() {
        return this.dictValue;
    }

    public String getLabel() {
        return this.dictLabel;
    }

    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("cssClass",getCssClass())
            .append("dictCode",getDictCode())
            .append("dictLabel",getDictLabel())
            .append("dictSort",getDictSort())
            .append("dictType",getDictType())
            .append("dictValue",getDictValue())
            .append("isDefault",getIsDefault())
            .append("isDel",getIsDel())
            .append("listClass",getListClass())
            .append("modifyTime",getModifyTime())
            .append("parentDictCode",getParentDictCode())
            .append("remark",getRemark())
            .append("status",getStatus())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
        }
}
