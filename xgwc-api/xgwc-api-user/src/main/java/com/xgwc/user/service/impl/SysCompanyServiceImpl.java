package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.SysCompanyMapper;
import com.xgwc.user.entity.SysCompany;
import com.xgwc.user.entity.dto.SysCompanyDto;
import com.xgwc.user.entity.vo.SysCompanyInfo;
import com.xgwc.user.entity.vo.SysCompanyQueryVo;
import com.xgwc.user.entity.vo.SysCompanyVo;
import com.xgwc.user.service.ISysCompanyService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.xgwc.user.service.impl.XgwcBrandRoleServiceImpl.PARENT_ID;


@Service
@Slf4j
public class SysCompanyServiceImpl implements ISysCompanyService  {
    @Resource
    private SysCompanyMapper sysCompanyMapper;


    /**
     * 查询公司管理
     * 
     * @param id 公司管理主键
     * @return 公司管理
     */
    @Override
    public SysCompanyDto selectSysCompanyById(Long id) {
        SysCompanyDto sysCompanyDto = sysCompanyMapper.selectSysCompanyById(id);
        if (sysCompanyDto != null) {
            Long pid = sysCompanyDto.getPid();
            if (pid != null && pid > 0) {
                SysCompanyDto parent = sysCompanyMapper.selectSysCompanyById(pid);
                sysCompanyDto.setParentCompany(parent.getName());
                sysCompanyDto.setPid(parent.getPid());
                return sysCompanyDto;
            }
            sysCompanyDto.setParentCompany("");
        }
        return sysCompanyDto;
    }

    /**
     * 查询公司管理列表
     *
     * @param sysCompany 公司管理
     * @return 公司管理
     */
    @Override
    public List<SysCompanyInfo> selectSysCompanyList(SysCompanyQueryVo sysCompany) {
        sysCompany.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        List<SysCompanyVo> sysCompanyDtos = sysCompanyMapper.selectSysCompanyList(sysCompany);

        if (CollectionUtils.isEmpty(sysCompanyDtos)){
            return null;
        }

        if (sysCompanyDtos.stream().anyMatch(business -> Objects.isNull(business.getPid()))) {
            log.error("获取公司信息失败，参数不完整缺少pid");
            return null;
        }

        if((StringUtils.isNotEmpty(sysCompany.getName())
                || sysCompany.getStatus()!= null)
                && sysCompany.getIsFlag() == 0){

            return sysCompanyDtos.stream()
                    .map(sysCompanyVo -> {
                        SysCompanyInfo childInfo = new SysCompanyInfo();
                        BeanUtils.copyProperties(sysCompanyVo, childInfo);
                        return childInfo;
                    })
                    .collect(Collectors.toList());
        }

        Map<Long, List<SysCompanyVo>> pidToChildren = sysCompanyDtos.stream()
                .collect(Collectors.groupingBy(SysCompanyVo::getPid)); // 按 pid 分组

        List<SysCompanyInfo> collect = sysCompanyDtos.stream()
                .filter(item -> PARENT_ID.equals(item.getPid())) // 筛选顶级节点
                .map(newItem -> {
                    SysCompanyInfo typeInfoVo = new SysCompanyInfo();
                    BeanUtils.copyProperties(newItem, typeInfoVo);
                    // 使用当前节点的 company_id 查找子节点
                    typeInfoVo.setChiledrenList(getCompanyParentList(typeInfoVo, pidToChildren));
                    return typeInfoVo;
                })
                .collect(Collectors.toList());
        return collect;
    }

    private List<SysCompanyInfo> getCompanyParentList(SysCompanyInfo typeInfoVo, Map<Long, List<SysCompanyVo>> pidToChildren) {
        Long parentId = typeInfoVo.getId(); // 使用 company_id 作为父ID
        List<SysCompanyVo> childrenVos = pidToChildren.get(parentId);
        if (childrenVos == null) {
            return Collections.emptyList();
        }
        return childrenVos.stream()
                .map(newItem -> {
                    SysCompanyInfo childInfo = new SysCompanyInfo();
                    BeanUtils.copyProperties(newItem, childInfo);
                    childInfo.setChiledrenList(getCompanyParentList(childInfo, pidToChildren));
                    return childInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 新增公司管理
     * 
     * @param dto 公司管理
     * @return 结果
     */
    @Override
    public int insertSysCompany(SysCompanyVo dto) {

        SysCompany sysCompany = BeanUtil.copyProperties(dto, SysCompany.class);
        sysCompany.setCreateTime(DateUtils.getNowDate());
        sysCompany.setCreateBy(SecurityUtils.getNickName());
        sysCompany.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        return sysCompanyMapper.insertSysCompany(sysCompany);
    }

    /**
     * 修改公司管理
     * 
     * @param dto 公司管理
     * @return 结果
     */
    @Override
    public int updateSysCompany(SysCompanyVo dto) {

        SysCompany sysCompany = BeanUtil.copyProperties(dto, SysCompany.class);
        sysCompany.setUpdateTime(DateUtils.getNowDate());
        sysCompany.setUpdateBy(SecurityUtils.getNickName());
        return sysCompanyMapper.updateSysCompany(sysCompany);
    }


    /**
     * 修改公司管理状态
     *
     * @param id 公司管理主键
     * @param status 修改状态
     * @return 结果
     */
    public ApiResult updateStatus(Integer id, Integer status) {
        // 1. 参数校验
        if (id == null ) {
            log.warn("更新公司状态失败：stationId或status为空");
            return ApiResult.error("参数不完整");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新公司状态失败");
        }
        // 2. 执行更新
        try {
            int affectedRows = sysCompanyMapper.updateStatus(id,status);
            if (affectedRows <= 0) {
                log.error("更新公司状态失败，未影响任何行，stationId={}", id);
                return ApiResult.error("更新状态失败，可能ID不存在");
            }
            log.info("更新公司状态成功，stationId={}, status={}", id, status);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新公司状态异常，stationId={}, 错误信息：{}", id, e.getMessage(), e);
            return ApiResult.error("系统异常，更新失败");
        }
    }

    @Override
    public List<SysCompanyVo> selectCompanyDropDown() {
        SysCompanyQueryVo sysCompanyQueryVo = new SysCompanyQueryVo();
        sysCompanyQueryVo.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        return sysCompanyMapper.selectSysCompanyList(sysCompanyQueryVo);
    }
}
