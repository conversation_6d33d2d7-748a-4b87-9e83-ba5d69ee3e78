package com.xgwc.user.controller;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.StringUtil;
import com.xgwc.user.entity.dto.aliyun.BusinessLicense;
import com.xgwc.user.entity.dto.aliyun.IdCard;
import com.xgwc.user.entity.dto.aliyun.Invoice;
import com.xgwc.user.util.AliyunOcrUtil;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@CrossOrigin
@RequestMapping("ocr")
@RestController
public class OcrController {

    @Resource
    private AliyunOcrUtil aliyunOcrUtil;

    /**
     * ocr识别身份证
     */
    @RequestMapping("ocr_idcar")
    public ApiResult ocrIdCar(String url){
        if(StringUtil.isEmpty(url)){
            return ApiResult.error("文件为空");
        }
        IdCard idCard = aliyunOcrUtil.recognizeIdCardText(url);
        return ApiResult.ok(idCard);
    }

    /**
     * ocr识别营业执照
     */
    @RequestMapping("ocr_businesslicense")
    public ApiResult ocrBusinessLicense(String url){
        if(StringUtil.isEmpty(url)){
            return ApiResult.error("文件为空");
        }
        BusinessLicense businessLicense = aliyunOcrUtil.recognizeBusinessLicenseText(url);
        return ApiResult.ok(businessLicense);
    }

    /**
     * ocr识别营业执照
     */
    @RequestMapping("ocr_invoice")
    public ApiResult ocrInvoice(String url){
        if(StringUtil.isEmpty(url)){
            return ApiResult.error("文件为空");
        }
        Invoice invoice = aliyunOcrUtil.recognizeInvoiceText(url);
        return ApiResult.ok(invoice);
    }
}
