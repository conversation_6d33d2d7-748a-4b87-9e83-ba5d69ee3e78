package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class SysMenuVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("激活图标")
    private String activeIcon;

    @FieldDesc("激活路径")
    private String activePath;

    @FieldDesc("固定在标签")
    private Long affixTab;

    @FieldDesc("权限名称：英文")
    private String authCode;

    @FieldDesc("页面组件")
    private String component;

    @FieldDesc("在面包屑里面隐藏菜单")
    private Long hideInBreadcrumb;

    @FieldDesc("隐藏菜单")
    private Long hideInMenu;

    @FieldDesc("在标签栏中隐藏")
    private Long hideInTab;

    @FieldDesc("隐藏子菜单")
    private Long hidechildrenInMenu;

    @FieldDesc("菜单图片")
    private String icon;

    @FieldDesc("菜单id")
    private Long id;

    @FieldDesc("是否缓存页面标签")
    private Long keepAlive;

    @FieldDesc("菜单名称")
    private String name;

    @FieldDesc("路由地址")
    private String path;

    @FieldDesc("父类id")
    private Long pid;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("菜单状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("标题")
    private String title;

    @FieldDesc("菜单类型")
    private String type;

    @FieldDesc("所属分类")
    private String modelType;

    @FieldDesc("排序")
    private Integer sort;
}
