package com.xgwc.user.entity.dto.notice;

import lombok.Data;

@Data
public class SelfNoticeDto {

    /**
     * 通知分类ID
     */
    private Integer id;

    /**
     * 通知类型：1站内信，2短信，3企微
     */
    private Integer noticeType;

    /**
     * 分类id
     */
    private String classifyId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 品牌商名称
     */
    private String brandName;

    /**
     * 品牌商ID
     */
    private Long brandId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 附件
     */
    private String attachment;


    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 发布人
     */
    private String createBy;

    /**
     * 来源：1品牌商，2加盟商
     */
    private Integer source;

    /**
     * 来源ID
     */
    private Long sourceId;

}
