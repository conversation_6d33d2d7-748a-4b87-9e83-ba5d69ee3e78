package com.xgwc.user.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.XgwcBrandRoleDto;
import com.xgwc.user.entity.param.XgwcBrandRoleParam;
import com.xgwc.user.entity.vo.XgwcBrandRoleVo;
import com.xgwc.user.service.XgwcBrandRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 组织管理-角色管理
 */
@RestController
@RequestMapping("/xgwcBrandRole")
@Slf4j
public class XgwcBrandRoleController extends BaseController {

    @Resource
    private XgwcBrandRoleService xgwcBrandRoleService;

    /**
     * @param xgwcBrandRoleParam 查询条件
     * @return 角色管理列表
     * 查询角色管理列表
     */
    @MethodDesc("查询角色管理列表")
    @PreAuthorize("@ss.hasPermission('xgwcBrandRole:xgwcBrandRole:list')")
    @PostMapping("/getXgwcBrandRoleList")
    public ApiResult<XgwcBrandRoleVo> getXgwcBrandRoleList(@RequestBody XgwcBrandRoleParam xgwcBrandRoleParam) {
        startPage();
        return getDataTable(xgwcBrandRoleService.getXgwcBrandRoleList(xgwcBrandRoleParam));
    }

    /**
     * 查询角色管理下拉框
     *
     * @return 角色管理下拉框
     */
    @MethodDesc("查询角色管理下拉框")
    @GetMapping("/getRoleDropDown")
    public ApiResult getRoleDropDown() {
        try {
            XgwcBrandRoleParam xgwcBrandRoleParam = new XgwcBrandRoleParam();
            xgwcBrandRoleParam.setStatus(0);
            List<XgwcBrandRoleVo> result = xgwcBrandRoleService.getXgwcBrandRoleList(xgwcBrandRoleParam);

            List<XgwcBrandRoleVo> safeResult = Optional.ofNullable(result).orElse(Collections.emptyList());

            return ApiResult.ok(safeResult);
        } catch (Exception e) {
            log.error("获取部门管理树失败", e);
            return ApiResult.error( "获取部门管理树失败");
        }
    }

    /**
     * @param xgwcBrandRoleDto 新增角色管理信息
     * @return 插入结果
     * 新增角色管理信息
     */
    @MethodDesc("新增角色管理信息")
    @PreAuthorize("@ss.hasPermission('xgwcBrandRole:xgwcBrandRole:add')")
    @Submit(fileds = "userId")
    @PostMapping("/saveXgwcBrandRole")
    public ApiResult saveXgwcBrandRole(@RequestBody XgwcBrandRoleDto xgwcBrandRoleDto) {
        return xgwcBrandRoleService.saveXgwcBrandRole(xgwcBrandRoleDto);
    }

    /**
     * 根据id查询角色管理信息
     *
     * @param roleId 角色管理id
     * @return 角色管理信息
     */
    @MethodDesc("根据id查询角色管理信息")
    @PreAuthorize("@ss.hasPermission('xgwcBrandRole:xgwcBrandRole:update')")
    @GetMapping("/getXgwcBrandRoleById/{roleId}")
    public ApiResult getXgwcBrandRoleById(@PathVariable Integer roleId) {
        return xgwcBrandRoleService.getXgwcBrandRoleById(roleId);
    }

    /**
     * @param xgwcBrandRoleDto 修改信息
     * @return 修改结果
     * 修改角色管理信息
     */
    @MethodDesc("修改角色管理信息")
    @PreAuthorize("@ss.hasPermission('xgwcBrandRole:xgwcBrandRole:update')")
    @Submit(fileds = "userId")
    @PostMapping("/updateXgwcBrandRole")
    public ApiResult updateXgwcBrandRole(@RequestBody XgwcBrandRoleDto xgwcBrandRoleDto) {
        return xgwcBrandRoleService.updateXgwcBrandRole(xgwcBrandRoleDto);
    }

    /**
     * @param roleId 角色id
     * @return
     * 根据id修改状态
     */
    @MethodDesc("修改角色状态")
    @PreAuthorize("@ss.hasPermission('xgwcBrandRole:xgwcBrandRole:updateStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "roleId") Integer roleId,
                                 @RequestParam(value = "status") Integer status) {
        return xgwcBrandRoleService.updateStatusById(roleId,status);
    }

    /**
     * 查询级联菜单权限
     * @param isFlag 菜单标识 品牌商 "brandAdmin"
     *                       加盟商 "franchiseeAdmin"
     *                       财务服务商 "serviceAdmin"
     *                       销售服务商 "marketAdmin"
     * @return 菜单权限
     */
    @MethodDesc("级联菜单权限查询")
    @GetMapping("/getMenuTreeData")
    public ApiResult getMenuTreeData(@RequestParam(value = "isFlag") String isFlag) {
        return xgwcBrandRoleService.getMenuTreeData(isFlag);
    }

    /**
     * 获取角色数据权限
     *
     * @return 角色权限
     */
    @MethodDesc("角色数据权限查询")
    @GetMapping("/getRoleDataScope")
    public ApiResult getRoleDataScope(){
         return xgwcBrandRoleService.getRoleDataScope();
     }

    /**
     * 根据userId查询角色信息
     *
     * @param userId 用户id
     * @return 角色信息
     */
    @MethodDesc("根据userId查询角色信息")
    @GetMapping("/selectRoleByUserId")
    public ApiResult selectRoleByUserId(@RequestParam(value = "userId") Long userId) {
        return ApiResult.ok(xgwcBrandRoleService.selectRoleByUserId(userId));
    }

    /**
     * 根据userId查询菜单信息
     *
     * @param userId 用户id
     * @return 菜单信息
     */
    @MethodDesc("根据userId查询菜单信息")
    @GetMapping("/selectMenuByUserId")
    public ApiResult selectMenuByUserId(@RequestParam(value = "userId") Long userId) {
        return ApiResult.ok(xgwcBrandRoleService.selectMenuByUserId(userId));
    }
}
