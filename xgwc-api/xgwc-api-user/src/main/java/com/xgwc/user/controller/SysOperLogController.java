package com.xgwc.user.controller;

import java.util.List;

import com.xgwc.common.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.dto.SysOperLogDto;
import com.xgwc.user.entity.vo.SysOperLogQueryVo;
import com.xgwc.user.service.ISysOperLogService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/operlog/SysOperLog")
public class SysOperLogController extends BaseController
{
    @Autowired
    private ISysOperLogService sysOperLogService;

    /**
     * 查询操作日志列表
     */
    @MethodDesc("查询操作日志列表")
    @PreAuthorize("@ss.hasPermission('operlog:SysOperLog:list')")
    @GetMapping("/list")
    public ApiResult<SysOperLogDto> list(SysOperLogQueryVo sysOperLog) {
        startPage();
        List<SysOperLogDto> list = sysOperLogService.selectSysOperLogList(sysOperLog);
        return getDataTable(list);
    }


    /**
     * 导出操作日志列表
     */
    @MethodDesc("导出操作日志列表")
    @PreAuthorize("@ss.hasPermission('operlog:SysOperLog:export')")
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysOperLogQueryVo sysOperLog)
    {
    }

    /**
     * 获取操作日志详细信息
     */
    @MethodDesc("获取操作日志详细信息")
    @PreAuthorize("@ss.hasPermission('operlog:SysOperLog:query')")
    @GetMapping(value = "/{operId}")
    public ApiResult<SysOperLogDto> getInfo(@PathVariable("operId") Long operId)
    {
        return success(sysOperLogService.selectSysOperLogByOperId(operId));
    }


    /**
     * 删除操作日志
     */
    @MethodDesc("删除操作日志")
    @PreAuthorize("@ss.hasPermission('operlog:SysOperLog:remove')")
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{operIds}")
    public ApiResult remove(@PathVariable Long[] operIds)
    {
        return toAjax(sysOperLogService.deleteSysOperLogByOperIds(operIds));
    }
}
