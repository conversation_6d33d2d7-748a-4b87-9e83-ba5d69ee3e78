package com.xgwc.user.service;

import com.xgwc.common.entity.SysUser;
import com.xgwc.user.entity.SysMenu;
import com.xgwc.user.entity.dto.LoginBusinessResult;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.dto.SysUserDto;
import com.xgwc.user.entity.dto.SysUserMiddleDto;
import com.xgwc.user.entity.param.LoginBusinessParam;
import com.xgwc.user.entity.vo.SysUserQueryVo;
import com.xgwc.user.entity.vo.SysUserVo;
import com.xgwc.user.feign.entity.SysUserMiddle;

import java.util.List;
import java.util.Set;

public interface UserService {


    /**
     * 根据用户名查询用户信息
     * @param userName 用户名
     * @return 用户信息
     */
    SysUser getUserInfoByUserName(String userName);

    /**
     * 根据用户id查询用户
     * @param userId 用户id
     * @return 用户信息
     */
    SysUser getUserInfoByUserId(Long userId);

    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    SysUser findUserByPhone(String phone);
    /**
     * 查询用户
     *
     * @param userId 用户主键
     * @return 用户
     */
    SysUserMiddleDto selectSysUserByUserId(Long userId);

    /**
     * 查询用户列表
     *
     * @param sysUser 用户
     * @return 用户集合
     */
    public List<SysUserDto> selectSysUserList(SysUserQueryVo sysUser);

    /**
     * 新增用户
     *
     * @param sysUser 用户
     * @return 结果
     */
    public int insertSysUser(SysUserVo sysUser);

    /**
     * 修改用户
     *
     * @param sysUser 用户
     * @return 结果
     */
    public int updateSysUser(SysUserVo sysUser);

    /**
     * 批量删除用户
     *
     * @param userIds 需要删除的用户主键集合
     * @return 结果
     */
    public int deleteSysUserByUserIds(Long[] userIds);

    /**
     * 删除用户信息
     *
     * @param userId 用户主键
     * @return 结果
     */
    public int deleteSysUserByUserId(Long userId);

    /**
     * 根据用户ID获取角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    Set<String> listRoleKeyByUserId(Long userId);


    /**
     * * 用户菜单权限
     * * @param userId 用户id
     * * @return 权限列表
     */
    Set<String> getMenuPermissionByUserId(Long userId);

    List<SysMenuDto> getMenuByUserId(Long userId);
    List<SysMenuDto> getMenuTreeByUserId(Long userId);

    /**
     * 根据用户ID获取权限列表
     * @param params 参数
     * @return 结果
     */
    LoginBusinessResult getBusinessLoginUser(LoginBusinessParam params);

    Long saveSysUserMiddle(SysUserMiddle sysUserMiddle);
}
