package com.xgwc.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyResponseBody;
import com.aliyun.cloudauth20190307.models.InitFaceVerifyResponseBody;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.config.AliyunFaceVerifyConfig;
import com.xgwc.user.dao.DesignerMapper;
import com.xgwc.user.dao.FaceVerityRecordMapper;
import com.xgwc.user.entity.Designer;
import com.xgwc.user.entity.FaceVerityRecord;
import com.xgwc.user.entity.vo.FaceVerifyCheckVo;
import com.xgwc.user.service.FaceVerifyService;
import com.xgwc.user.util.DescribeFaceVerifyUtil;
import com.xgwc.user.util.InitFaceVerifyUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Slf4j
@Service
public class FaceVerifyServiceImpl implements FaceVerifyService {

    @Resource
    private InitFaceVerifyUtil initFaceVerifyUtil;

    @Resource
    private DescribeFaceVerifyUtil describeFaceVerifyUtil;

    @Resource
    private FaceVerityRecordMapper faceVerityRecordMapper;

    @Resource
    private AliyunFaceVerifyConfig aliyunFaceVerifyConfig;

    @Resource
    private DesignerMapper designerMapper;


    @Override
    public ApiResult initFaceVerify(FaceVerifyCheckVo faceVerifyCheckVo) {
        if(!aliyunFaceVerifyConfig.isOpen()){
            return ApiResult.ok("实人认证通道已关闭");
        }
        ApiResult checkResult = checkIsVerifyed();
        if(checkResult != null) {
            return checkResult;
        }
        //插入验证记录
        FaceVerityRecord faceVerityRecord = insertFaceVerityRecord(faceVerifyCheckVo);
        InitFaceVerifyResponseBody responseBody = initFaceVerifyUtil.faceVerifyCheck(faceVerifyCheckVo);
        JSONObject jsonObject = new JSONObject();
        if(responseBody != null) {
            if(responseBody.getCode().equals("200")){
                log.info("初始化实人认证成功,接口返回结果{}", JSONObject.toJSONString(responseBody));
                String certifyId = responseBody.getResultObject().getCertifyId();
                String certifyUrl = responseBody.getResultObject().getCertifyUrl();
                faceVerityRecord.setCertifyId(certifyId);
                faceVerityRecord.setCertifyUrl(certifyUrl);
                jsonObject.put("certifyUrl", certifyUrl);
                jsonObject.put("certifyId", certifyId);
            }
            faceVerityRecord.setInitResult(JSONObject.toJSONString(responseBody));
            faceVerityRecordMapper.updateFaceVerityRecord(faceVerityRecord);
        }else{
            log.error("初始化结果为空,参数:{}", JSONObject.toJSONString(faceVerifyCheckVo));
            return ApiResult.error("人脸验证初始化失败");
        }
        return ApiResult.ok(jsonObject);
    }

    @Override
    public ApiResult initFaceVerifyDesigner(FaceVerifyCheckVo faceVerifyCheckVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser.getUserType() != 3){
            log.error("实人认证,操作人非设计师:{}", JSONObject.toJSONString(sysUser));
            return ApiResult.error("认证失败");
        }
        Designer designer = designerMapper.getDesignerByUserId(sysUser.getUserId());
        if(designer != null){
            faceVerifyCheckVo.setCertName(designer.getManagerName());
            faceVerifyCheckVo.setCertNo(ParamDecryptUtil.decryptParam(designer.getIdcardNo(), ParamDecryptUtil.DESIGNER_KEY));
            return ApiResult.ok(designer);
        }else{
            return ApiResult.error("设计师不存在");
        }
    }

    /**
     * 校验用户是否校验过
     */
    private ApiResult checkIsVerifyed() {
       Long userId = SecurityUtils.getUserId();
        FaceVerityRecord faceVerityRecord = faceVerityRecordMapper.getFaceVerityRecordByCertUserId(userId);
        if(faceVerityRecord == null) {
            log.info("该用户:{},暂未校验过人脸识别", userId);
            return null;
        }else{
            String verityPass = faceVerityRecord.getVerityPass();
            if(StringUtils.isNotEmpty(verityPass)) {
                if(verityPass.equals("T")) {
                    log.info("该用户:{},已经认证通过无需再认证", userId);
                    return ApiResult.ok("该用户已经人脸认证通过,无需再认证");
                }else{
                    //认证失败过
                    long diffHour = diffHour(faceVerityRecord.getCreateTime());
                    if(diffHour < 24){
                        log.info("用户ID:{},用户名称:{},24小时内有认证失败记录,请在:{}小时后再试", userId, SecurityUtils.getNickName(), (24 - diffHour));
                        return ApiResult.error("24小时内有认证失败记录,请在:" + (24 - diffHour) + "小时后再试");
                    }
                }
            }
        }
        return null;
    }

    private FaceVerityRecord insertFaceVerityRecord(FaceVerifyCheckVo faceVerifyCheckVo){
        SysUser sysUser = SecurityUtils.getSysUser();
        FaceVerityRecord faceVerityRecord = new FaceVerityRecord();
        String uuid = StringUtils.remove(UUID.randomUUID().toString(), '-');
        faceVerityRecord.setUserId(sysUser.getUserId());
        faceVerityRecord.setUuid(uuid);
        faceVerityRecord.setReturnUrl(faceVerifyCheckVo.getReturnUrl());
        faceVerityRecord.setCertNo(ParamDecryptUtil.encrypt(faceVerifyCheckVo.getCertNo(), ParamDecryptUtil.DESIGNER_KEY));
        faceVerityRecord.setCertName(faceVerifyCheckVo.getCertName());
        faceVerityRecordMapper.insertFaceVerityRecord(faceVerityRecord);
        return faceVerityRecord;
    }

    @Override
    public ApiResult getFaceVerifyResult(String certifyId) {
        FaceVerityRecord faceVerityRecord = faceVerityRecordMapper
                .getFaceVerityRecordByCertifyId(certifyId);
        return getFaceVerifyResult(faceVerityRecord);
    }

    private ApiResult getFaceVerifyResult(FaceVerityRecord faceVerityRecord){
        JSONObject jsonObject = new JSONObject();
        if(faceVerityRecord != null) {
            if(faceVerityRecord.getVerityPass() != null) {
                log.info("人脸验证已经完成,参数:{}, 结果:{}", faceVerityRecord.getUserId(), faceVerityRecord.getVerityPass());
                jsonObject.put("verityPass", faceVerityRecord.getVerityPass());
                return ApiResult.ok(jsonObject);
            }
        }else{
            return ApiResult.error("没有校验记录");
        }
        jsonObject.put("verityPass", "F");
        log.warn("人脸验证暂时未回调，主动调用接口查询,参数:{}", faceVerityRecord.getUserId());
        if(StringUtils.isNotEmpty(faceVerityRecord.getCertifyId())){
            DescribeFaceVerifyResponseBody describeFaceVerifyResponseBody = describeFaceVerifyUtil.geVerifyResult(faceVerityRecord.getCertifyId());
            if(describeFaceVerifyResponseBody != null) {
                if(describeFaceVerifyResponseBody.getCode().equals("200")){
                    DescribeFaceVerifyResponseBody.DescribeFaceVerifyResponseBodyResultObject resultObject = describeFaceVerifyResponseBody.getResultObject();
                    if(resultObject != null){
                        String passed = resultObject.getPassed();
                        faceVerityRecord.setVerityPass(passed);
                        faceVerityRecord.setVerityResult(JSONObject.toJSONString(resultObject));
                        jsonObject.put("verityPass", faceVerityRecord.getVerityPass());
                    }
                }
            }
            faceVerityRecordMapper.updateFaceVerityRecord(faceVerityRecord);
        }
        return ApiResult.ok(jsonObject);
    }

    @Override
    public ApiResult getFaceVerifyResult() {
        Long userId = SecurityUtils.getUserId();
        FaceVerityRecord faceVerityRecord = faceVerityRecordMapper.getFaceVerityRecordByCertUserId(userId);
        return getFaceVerifyResult(faceVerityRecord);
    }

    @Override
    public ApiResult callBack(FaceVerifyCheckVo faceVerifyCheckVo) {
        if(StringUtils.isNotEmpty(faceVerifyCheckVo.getCertifyId())){
            FaceVerityRecord faceVerityRecord = faceVerityRecordMapper.getFaceVerityRecordByCertifyId(faceVerifyCheckVo.getCertifyId());
            if(faceVerityRecord != null){
                faceVerityRecord.setVerityPass(faceVerifyCheckVo.getPassed());
                faceVerityRecordMapper.updateFaceVerityRecord(faceVerityRecord);
            }
        }
        return null;
    }


    private long diffHour(String date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(date, formatter);
        Duration duration = Duration.between(dateTime, LocalDateTime.now());
        return duration.toHours(); // 完整的小时差
    }
}
