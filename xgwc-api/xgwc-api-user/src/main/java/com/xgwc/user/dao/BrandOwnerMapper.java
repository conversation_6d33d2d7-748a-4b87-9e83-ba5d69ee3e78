package com.xgwc.user.dao;

import java.util.List;

import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.BrandOwner;
import com.xgwc.user.entity.dto.BrandOwnerSimpleDto;
import com.xgwc.user.entity.vo.BrandOwnerVo;
import com.xgwc.user.entity.dto.BrandOwnerDto;
import com.xgwc.user.entity.vo.BrandOwnerQueryVo;


public interface BrandOwnerMapper  {
    /**
     * 查询品牌商管理
     * 
     * @param brandId 品牌商管理主键
     * @return 品牌商管理
     */
    BrandOwnerDto selectBrandOwnerByBrandId(Long brandId);

    /**
     * 查询品牌商管理列表
     * 
     * @param brandOwner 品牌商管理
     * @return 品牌商管理集合
     */
    List<BrandOwnerDto> selectBrandOwnerList(BrandOwnerQueryVo brandOwner);

    /**
     * 新增品牌商管理
     * 
     * @param brandOwner 品牌商管理
     * @return 结果
     */
    @TenantIgnore
    int insertBrandOwner(BrandOwner brandOwner);

    /**
     * 修改品牌商管理
     * 
     * @param brandOwner 品牌商管理
     * @return 结果
     */
    int updateBrandOwner(BrandOwner brandOwner);

    /**
     * 删除品牌商管理
     * 
     * @param brandId 品牌商管理主键
     * @return 结果
     */
    int deleteBrandOwnerByBrandId(Long brandId);

    /**
     * 批量删除品牌商管理
     * 
     * @param brandIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBrandOwnerByBrandIds(Long[] brandIds);

    /**
     * 查询品牌商管理
     *
     * @param userId 用户id
     * @return 品牌商管理
     */
    BrandOwnerDto selectBrandOwnerByUserId(Long userId);

    /**
     * 根据品牌id集合查询品牌商管理列表
     * @param brandIds 品牌id集合
     * @return 品牌商管理列表
     */
    List<BrandOwnerDto> selectBrandOwnerByBrandIds(List<Long> brandIds);

    /**
     * 查询所有品牌商管理
     * @return 品牌商管理列表
     */
    List<BrandOwnerSimpleDto> selectAllBrandOwner();

    /**
     * 根据手机号查询品牌商管理
     * @param mobile 手机号
     * @return 品牌商管理
     */
    BrandOwnerVo selectBrandOwnerByMobile(String mobile);

    /**
     * 根据管理员id查询品牌商管理
     * @param managerUserId 管理员id
     * @return 品牌商管理
     */
    BrandOwnerDto selectBrandOwnerByManagerUserId(Long managerUserId);
}
