package com.xgwc.user.controller;

import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.AgreementDto;
import com.xgwc.user.entity.vo.AgreementVo;
import com.xgwc.user.service.AgreementService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 协议
 */
@RestController
@RequestMapping("agreement")
public class AgreementController extends BaseController {

    @Resource
    private AgreementService agreementService;

    /**
     * 添加协议
     */
    @PostMapping("save_agreement")
    public ApiResult saveAgreement(@RequestBody @Valid AgreementVo agreementVo) {
        int result = agreementService.saveAgreement(agreementVo);
        return result > 0 ? ApiResult.ok() : ApiResult.error("");
    }

    /**
     * 获取品牌协议
     */
    @RequestMapping("get_agreement_bybrand")
    public ApiResult getAgreementByBrand() {
        AgreementDto agreementDto = agreementService.getAgreementByBrandId();
        return ApiResult.ok(agreementDto);
    }

    /**
     * 获取未统一协议列表
     */
    @RequestMapping("designer/get_notagree_list")
    public ApiResult getNotAgreeList() {
        List<AgreementDto> agreementDtos = agreementService.getNotAgreeList();
        return getDataTable(agreementDtos);
    }

    /**
     * 获取所有协议
     */
    @RequestMapping("designer/get_allagreement_list")
    public ApiResult getAllAgreementList() {
        List<AgreementDto> agreementDtos = agreementService.getAllAgreementList();
        return getDataTable(agreementDtos);
    }

    /**
     * 同意协议
     */
    @PostMapping ("designer/agree")
    public ApiResult agree(Integer agreementId) {
        if(agreementId == null) {
            return ApiResult.error("协议id为空");
        }
        int result = agreementService.agree(agreementId);
        return result > 0 ? ApiResult.ok() : ApiResult.error("");
    }

}
