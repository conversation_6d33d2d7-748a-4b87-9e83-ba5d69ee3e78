package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ServiceApplyVo {

    private static final long serialVersionUID=1L;

    @NotNull(message = "服务类型不能为空")
    @FieldDesc("服务类型 (1: 财务服务商，2：销售服务商)")
    private Integer serviceType;

    @NotNull(message = "公司名称不能为空")
    @FieldDesc("公司名称")
    private String companyName;

    @NotNull(message = "联系人信息不能为空")
    @FieldDesc("联系人")
    private String contact;

    @NotNull(message = "主用户id不能为空")
    @FieldDesc("主用户id")
    private Long mainUserId;

}
