package com.xgwc.user.aspect;
import com.xgwc.common.annotation.Submit;
import com.xgwc.exception.config.SubmitException;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.common.util.SecurityUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class ValidResubmitAspect {

    @Pointcut("@annotation(com.xgwc.common.annotation.Submit)")
    public void annotationPointcut() {
    }

    @Resource
    private RedisUtil redisUtil;

    @Around("annotationPointcut()")
    public Object beforePointcut(ProceedingJoinPoint joinPoint) throws Throwable {
        Class clazz = joinPoint.getTarget().getClass();
        String methodName = joinPoint.getSignature().getName();
        Class[] parameterTypes = ((MethodSignature) joinPoint.getSignature()).getMethod().getParameterTypes();
        Method method = getMethod(clazz, methodName, parameterTypes);
        if (method != null) {
            Submit submit = method.getAnnotation(Submit.class);
            String fieldKey = "xgwc:submit:".concat(methodName).concat(":").concat(parseKey(submit.fileds().split(","), joinPoint.getArgs()));
            if (redisUtil.get(fieldKey) != null) {
               log.info("重复提交key:{}", fieldKey);
                throw new SubmitException("提交次数过快");
            } else {
                //5秒内只能对一个方法进行操作
                redisUtil.set(fieldKey, "true", submit.expire());
                return joinPoint.proceed();
            }
        }
        return null;
    }

    /**
     * 获取注解对象
     */
    private Method getMethod(Class clazz, String methodName, Class[] parameterTypes) {
        Method method = null;
        try {
            method = clazz.getMethod(methodName, parameterTypes);
        } catch (NoSuchMethodException e) {
           log.error("get method error,errorInfo:",e);
         }
        return method;
    }

    private String parseKey(String[] keys, Object[] args) {
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            String keyValue = null;
            sb.append(":").append(key);
            if(key.equals("userId")){
                Long userId = SecurityUtils.getUserId();
                if(userId != null){
                    keyValue = userId + "";
                }
            }else {
                for (Object object : args) {
                    keyValue = getFieldValueByFieldName(key, object);
                    if (keyValue != null) {
                        break;
                    }
                }
            }
            sb.append("=").append(keyValue);
        }
        return sb.toString();
    }

    /**
     * 从对象中获取属性
     */
    private String getFieldValueByFieldName(String fieldName, Object object) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = object.getClass().getMethod(getter);
            Object value = method.invoke(object);
            return value.toString();
        } catch (Exception e) {
           log.error("not existed filedName:{}, ObjectName:{},errorInfo:", fieldName, object.getClass().getName(),e);
          }
        return null;
    }

}
