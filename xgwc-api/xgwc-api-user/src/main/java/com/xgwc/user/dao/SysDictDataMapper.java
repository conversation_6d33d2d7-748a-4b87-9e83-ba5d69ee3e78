package com.xgwc.user.dao;

import java.util.List;
import com.xgwc.user.entity.SysDictData;
import com.xgwc.user.entity.vo.SysDictDataVo;
import com.xgwc.user.entity.dto.SysDictDataDto;
import com.xgwc.user.entity.vo.SysDictDataQueryVo;
import org.apache.ibatis.annotations.Param;


public interface SysDictDataMapper  {
    /**
     * 查询字典数据
     * 
     * @param dictCode 字典数据主键
     * @return 字典数据
     */
    public SysDictDataDto selectSysDictDataByDictCode(Long dictCode);

    /**
     * 查询字典数据列表
     * 
     * @param sysDictData 字典数据
     * @return 字典数据集合
     */
    public List<SysDictDataDto> selectSysDictDataList(SysDictDataQueryVo sysDictData);
    public List<SysDictDataDto> selectSysDictDataByType(@Param("dictType") String dictType,@Param("brandId") Long brandId);

    /**
     * 新增字典数据
     * 
     * @param sysDictData 字典数据
     * @return 结果
     */
    public int insertSysDictData(SysDictData sysDictData);

    /**
     * 修改字典数据
     * 
     * @param sysDictData 字典数据
     * @return 结果
     */
    public int updateSysDictData(SysDictData sysDictData);
    public int updateSysDictDataByDictType(@Param("dictType") String dictType,@Param("oldDictType") String oldDictType);



    public int updateStatus(@Param("array") Long[] ids, @Param("status") Integer status);
    /**
     * 删除字典数据
     * 
     * @param dictCode 字典数据主键
     * @return 结果
     */
    public int deleteSysDictDataByDictCode(Long dictCode);

    /**
     * 批量删除字典数据
     * 
     * @param dictCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysDictDataByDictCodes(Long[] dictCodes);
}
