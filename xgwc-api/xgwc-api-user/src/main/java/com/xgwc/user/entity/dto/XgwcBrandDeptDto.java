package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.Staff;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.dto
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  10:03
 */

/**
 * 组织管理-部门管理
 */
@Data
public class XgwcBrandDeptDto {

    /**
     * 部门主键
     */
    @FieldDesc("部门主键")
    private Long deptId;

    /**
     * 部门名称
     */
    @FieldDesc("部门名称")
    private String deptName;

    /**
     * 公司id
     */
    @FieldDesc("公司id")
    private Long companyId;

    /**
     * 品牌商id
     */
    @FieldDesc("品牌商id")
    private Long brandOwnerId;

    /**
     * 父类id
     */
    @FieldDesc("父类id")
    private Long pid;

    /**
     * 层级
     */
    @FieldDesc("层级")
    private Long level;

    /**
     * 排序：越小越前
     */
    @FieldDesc("排序：越小越前")
    private Integer sort;

    /**
     * 状态：0正常，1禁用
     */
    @FieldDesc("状态：0正常，1禁用")
    private String status;

    /**
     * 是否删除：0正常，1删除（表示部门是否被逻辑删除）
     */
    @FieldDesc("是否删除：0正常，1删除（表示部门是否被逻辑删除）")
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @FieldDesc("部门负责人")
    private Staff isPrincipal;

    @FieldDesc("助理")
    private List<Staff> isAssistant;
}
