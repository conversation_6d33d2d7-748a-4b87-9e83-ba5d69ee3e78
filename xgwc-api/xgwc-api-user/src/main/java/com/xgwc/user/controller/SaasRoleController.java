package com.xgwc.user.controller;

import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.vo.SaasRoleVo;
import com.xgwc.user.entity.dto.SaasRoleDto;
import com.xgwc.user.entity.vo.SaasRoleQueryVo;
import com.xgwc.user.service.ISaasRoleService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;



/**
 * saas后台角色信息
 */
@RestController
@RequestMapping("/saasRole")
public class SaasRoleController extends BaseController {
    @Resource
    private ISaasRoleService saasRoleService;

    /**
     * 查询saas后台角色列表
     */
    @MethodDesc("查询saas后台角色列表")
    //@PreAuthorize("@ss.hasPermission('SaasRole:SaasRole:list')")
    @GetMapping("/list")
    public ApiResult<SaasRoleDto> list(SaasRoleQueryVo saasRole) {
        startPage();
        List<SaasRoleDto> list = saasRoleService.selectSaasRoleList(saasRole);
        return getDataTable(list);
    }

    /**
     * 获取saas后台角色详细信息
     */
    @MethodDesc("获取saas后台角色详细信息")
    //@PreAuthorize("@ss.hasPermission('SaasRole:SaasRole:query')")
    @GetMapping(value = "/get")
    public ApiResult getInfo(@RequestParam("roleId") Long roleId) {
        return saasRoleService.selectSaasRoleByRoleId(roleId);
    }

    /**
     * 新增saas后台角色
     */
    @MethodDesc("新增saas后台角色")
    @PreAuthorize("@ss.hasPermission('SaasRole:SaasRole:add')")
    @Log(title = "saas后台角色", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody SaasRoleVo saasRole) {
        return saasRoleService.insertSaasRole(saasRole);
    }

    /**
     * 修改saas后台角色
     */
    @MethodDesc("修改saas后台角色")
    @PreAuthorize("@ss.hasPermission('SaasRole:SaasRole:edit')")
    @Log(title = "saas后台角色", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody SaasRoleVo saasRole) {
        return saasRoleService.updateSaasRole(saasRole);
    }

    /**
     * 删除saas后台角色
     */
    @MethodDesc("删除saas后台角色")
    @PreAuthorize("@ss.hasPermission('SaasRole:SaasRole:remove')")
    @Log(title = "saas后台角色", businessType = BusinessType.DELETE)
	@DeleteMapping("/remove")
    public ApiResult remove(@RequestParam(value = "roleId") Long roleId) {
        return toAjax(saasRoleService.deleteSaasRoleByRoleId(roleId));
    }

    /**
     * 修改saas后台角色状态
     */
    @MethodDesc("修改saas后台角色状态")
    @PreAuthorize("@ss.hasPermission('SaasRole:SaasRole:changeStatus')")
    @Log(title = "saas后台角色", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public ApiResult changeStatus(@RequestBody SaasRoleVo saasRole) {
        return saasRoleService.updateSaasRole(saasRole);
    }
}
