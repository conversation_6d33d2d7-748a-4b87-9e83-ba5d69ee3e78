package com.xgwc.user.service;

import com.xgwc.user.entity.UploadVideoRecord;
import com.xgwc.user.entity.VideoInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface VideoService {

    /**
     * 上传视频
     * @param file 文件
     */
    Integer uploadVideo(MultipartFile file);

    /**
     * 获取视频列表
     */
    List<VideoInfo> getUploadVideoRecordList(List<Integer> videoIds);

    /**
     * 获取视频列表
     */
    List<VideoInfo> getUploadVideoRecordList(String videoIds);

    /**
     * 获取视频信息
     * @param id 视频ID
     * @return 视频信息
     */
    VideoInfo getUploadVideoRecord(Integer id);


}
