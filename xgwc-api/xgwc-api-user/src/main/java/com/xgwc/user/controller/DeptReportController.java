package com.xgwc.user.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.SalesEfficiencyQueryDto;
import com.xgwc.user.entity.vo.DesignerVO;
import com.xgwc.user.entity.vo.SalesEfficiencyVo;
import com.xgwc.user.service.IDeptReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RequestMapping("/report/dept")
@RestController
public class DeptReportController extends BaseController {

    @Autowired
    private IDeptReportService deptReportService;

    @MethodDesc("销售效率")
    @GetMapping("/sales/efficiency")
    public ApiResult<SalesEfficiencyVo> SalesEfficiency(SalesEfficiencyQueryDto dto) {
        startPage();
        List<SalesEfficiencyVo> list = deptReportService.SalesEfficiency(dto);
        return getDataTable(list);
    }
}
