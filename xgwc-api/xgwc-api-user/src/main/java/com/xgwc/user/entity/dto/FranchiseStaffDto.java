package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.annotation.FieldLabel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class FranchiseStaffDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("姓名")
    @Excel(name = "姓名")
    private String name;

    @FieldDesc("花名")
    @Excel(name = "花名")
    private String stageName;

    @FieldDesc("部门id")
    @Excel(name = "部门id")
    @FieldLabel("部门")
    private Long deptId;

    @FieldDesc("岗位id")
    @Excel(name = "岗位id")
    @FieldLabel("岗位")
    private Long postId;

    @FieldDesc("角色权限")
    @Excel(name = "角色权限")
    @FieldLabel("角色权限")
    private String roleIds;

    @FieldDesc("工作性质：0全职，1外包，2兼职，3合作方")
    @Excel(name = "工作性质：0全职，1外包，2兼职，3合作方")
    @FieldLabel("工作性质")
    private Integer jobNature;

    @FieldDesc("状态：0在职，1离职，2合作中，3未合作")
    @Excel(name = "状态：0在职，1离职，2合作中，3未合作")
    @FieldLabel("状态")
    private Integer status;

    @FieldDesc("部门负责人：0是，1否")
    @Excel(name = "部门负责人：0是，1否")
    @FieldLabel("部门负责人")
    private Integer isPrincipal;

    @FieldDesc("直属上级")
    @Excel(name = "直属上级")
    @FieldLabel("直属上级")
    private Long superior;

    @FieldDesc("档案状态：0未录，1已录")
    @Excel(name = "档案状态：0未录，1已录")
    private Integer archiveStatus;

    @FieldDesc("绑定状态：0未绑，1已绑")
    @Excel(name = "绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("绑定的用户id")
    @Excel(name = "绑定的用户id")
    private Long bindUserId;

    @FieldDesc("登录手机号")
    @Excel(name = "登录手机号")
    private String loginPhone;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商简称")
    @Excel(name = "加盟商简称")
    private String franchiseShortName;

    @FieldDesc("下载次数上限/天")
    @Excel(name = "下载次数上限/天")
    private Integer downloadLimit;

    @FieldDesc("今日已下载次数")
    @Excel(name = "今日已下载次数")
    private Integer downloadCount;

    @FieldDesc("最大限制下载次数")
    private Integer maxRoleDateDownloadLimit;

    @FieldDesc("角色id")
    private Long roleId;

    @FieldDesc("修改下载次数操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    @FieldDesc("最后操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastOperationTime;

    @FieldDesc("最后操作人")
    @Excel(name = "最后操作人")
    private String lastOperation;

    @FieldDesc("是否删除：0正常，1删除")
    @Excel(name = "是否删除：0正常，1删除")
    private Integer isDel;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("品牌商名称")
    private String brandName;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("部门名称")
    private String deptName;

    @FieldDesc("离职时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date resignationTime;

    @FieldDesc("员工类型：0内部员工，1加盟商员工")
    private Integer staffType;

}
