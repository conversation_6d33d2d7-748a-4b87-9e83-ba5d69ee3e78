package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.entity.vo
 * @Author: k<PERSON><PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-04-29  14:08
 */
@Data
public class SysRoleMenuVo {

    /** 角色菜单关系表 */
    private Long id;

    /** 角色id */
    private Long roleId;

    /** 角色名称 */
    private String roleName;

    /** 菜单id */
    private Long menuId;

    /** 菜单名称 */
    private String menuName;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Long pid;
}
