package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class SysDictType {

private static final long serialVersionUID=1L;

    /** 创建用户id */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 数据设置方 */
    private String dictData;

    /**  */
    private Long dictId;

    /** 字典名称 */
    private String dictLabel;

    /** 字典排序 */
    private Long dictSort;

    /** 字典编码 */
    private String dictValue;

    /** 是否删除 */
    private Integer isDel;

    /** 行更新时间 */
    private Date modifyTime;

    /** 备注 */
    private String remark;

    /** 字典状态 */
    private Integer status;

    /** 修改人id */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;



}