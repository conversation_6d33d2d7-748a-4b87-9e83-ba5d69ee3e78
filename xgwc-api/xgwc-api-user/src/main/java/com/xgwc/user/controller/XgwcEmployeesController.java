package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.XgwcEmployeesDto;
import com.xgwc.user.entity.vo.XgwcEmployeesQueryVo;
import com.xgwc.user.entity.vo.XgwcEmployeesVo;
import com.xgwc.user.service.IEmployeesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * 员工档案管理
 */
@RestController
@RequestMapping("/employees")
public class XgwcEmployeesController extends BaseController {
    @Autowired
    private IEmployeesService employeesService;

    /**
     * 查询员工档案列表
     */
    @MethodDesc("查询员工档案列表")
    @PreAuthorize("@ss.hasPermission('employees:employees:list')")
    @PostMapping("/list")
    public ApiResult list(@RequestBody XgwcEmployeesQueryVo employees) {
        return ApiResult.ok(employeesService.selectEmployeesList(employees));
    }

    /**
     * 获取员工档案详细信息
     */
    @MethodDesc("获取员工档案详细信息")
    @GetMapping(value = "/{employeeId}")
    public ApiResult<XgwcEmployeesDto> getInfo(@PathVariable("employeeId") Long employeeId) {
        return success(employeesService.selectEmployeesByEmployeeId(employeeId));
    }

    /**
     * 新增员工档案
     */
    @MethodDesc("新增员工档案")
    @PreAuthorize("@ss.hasPermission('employees:employees:add')")
    @Log(title = "员工档案", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody XgwcEmployeesVo employees) {
        return employeesService.insertEmployees(employees);
    }

    /**
     * 修改员工档案
     */
    @MethodDesc("修改员工档案")
    @PreAuthorize("@ss.hasPermission('employees:employees:edit')")
    @Log(title = "员工档案", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody XgwcEmployeesVo employees) {
        return employeesService.updateEmployees(employees);
    }

    @MethodDesc("申请离职")
    @PreAuthorize("@ss.hasPermission('employees:employees:resignations')")
    @PostMapping("resignations")
    public ApiResult resignations(@RequestBody XgwcEmployeesVo employees) {
        return toAjax(employeesService.resignationsEmployees(employees));
    }


}
