package com.xgwc.user.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.ApplyRecordMapper;
import com.xgwc.user.service.IApplyRecordService;
import com.xgwc.user.entity.ApplyRecord;
import com.xgwc.user.entity.vo.ApplyRecordVo;
import com.xgwc.user.entity.dto.ApplyRecordDto;
import com.xgwc.user.entity.vo.ApplyRecordQueryVo;


@Service
public class ApplyRecordServiceImpl implements IApplyRecordService  {
    @Resource
    private ApplyRecordMapper applyRecordMapper;

    /**
     * 查询申请记录
     * 
     * @param id 申请记录主键
     * @return 申请记录
     */
    @Override
    public ApplyRecordDto selectApplyRecordById(Long id) {
        return applyRecordMapper.selectApplyRecordById(id);
    }

    /**
     * 查询申请记录列表
     * 
     * @param applyRecord 申请记录
     * @return 申请记录
     */
    @Override
    public List<ApplyRecordDto> selectApplyRecordList(ApplyRecordQueryVo applyRecord) {
        return applyRecordMapper.selectApplyRecordList(applyRecord);
    }

    /**
     * 新增申请记录
     * 
     * @param dto 申请记录
     * @return 结果
     */
    @Override
    public int insertApplyRecord(ApplyRecordVo dto) {
        ApplyRecord applyRecord = BeanUtil.copyProperties(dto, ApplyRecord.class);
        applyRecord.setCreateTime(DateUtils.getNowDate());
        return applyRecordMapper.insertApplyRecord(applyRecord);
    }

    /**
     * 修改申请记录
     * 
     * @param dto 申请记录
     * @return 结果
     */
    @Override
    public int updateApplyRecord(ApplyRecordVo dto) {

        ApplyRecord applyRecord = BeanUtil.copyProperties(dto, ApplyRecord.class);
        applyRecord.setUpdateTime(DateUtils.getNowDate());
        return applyRecordMapper.updateApplyRecord(applyRecord);
    }

    /**
     * 批量删除申请记录
     * 
     * @param ids 需要删除的申请记录主键
     * @return 结果
     */
    @Override
    public int deleteApplyRecordByIds(Long[] ids) {
        return applyRecordMapper.deleteApplyRecordByIds(ids);
    }

    /**
     * 删除申请记录信息
     * 
     * @param id 申请记录主键
     * @return 结果
     */
    @Override
    public int deleteApplyRecordById(Long id) {
        return applyRecordMapper.deleteApplyRecordById(id);
    }

    @Override
    public ApplyRecordDto selectApplyRecordByUserId(Long userId, Long brandId, Long businessId) {
        return applyRecordMapper.findApplyRecordByUserId(userId, brandId, businessId);
    }

    @Override
    public int updateApplyRecordStatusById(Long id, Integer status) {
        return applyRecordMapper.updateApplyRecordStatusById(id, status);
    }

    @Override
    public List<ApplyRecordDto> selectApplyRecordListByUserIdAndBusinessType(Long userId, Integer businessType) {
        return applyRecordMapper.findApplyRecordListByUserIdAndBusinessType(userId, businessType);
    }
}
