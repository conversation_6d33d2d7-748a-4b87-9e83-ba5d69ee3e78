package com.xgwc.user.util;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.lang.UUID;
import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.config.AliyunFaceVerifyConfig;
import com.xgwc.user.entity.vo.FaceVerifyCheckVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InitFaceVerifyUtil {

    @Resource
    private AliyunFaceVerifyConfig aliyunFaceVerifyConfig;
    // 使用单例模式优化性能
    @Resource
    private com.aliyun.credentials.Client faceVerifyClient;

    /**
     * 人脸验证
     */
    public InitFaceVerifyResponseBody faceVerifyCheck(FaceVerifyCheckVo faceVerifyCheckVo){
        InitFaceVerifyRequest request = new InitFaceVerifyRequest();
        // 场景ID+L。
        request.setSceneId(aliyunFaceVerifyConfig.getSceneId());
        // 设置商户请求的唯一标识。
        request.setOuterOrderNo(StringUtils.remove(UUID.randomUUID().toString(), '-'));
        // 认证方案。
        request.setProductCode("ID_PRO");
        // 模式, 多动作活体检测，眨眼+任意摇头检测（眨眼和摇头顺序随机）。
        request.setModel("MULTI_ACTION");
        request.setCertType("IDENTITY_CARD");
        request.setCertName(faceVerifyCheckVo.getCertName());
        request.setCertNo(faceVerifyCheckVo.getCertNo());
        // MetaInfo环境参数，此参数应由前端js获取并传入。
        request.setMetaInfo(faceVerifyCheckVo.getMetaInfo());
        request.setCallbackUrl(aliyunFaceVerifyConfig.getCallBackUrl());
        //业务页面回跳的目标地址。
        request.setReturnUrl(faceVerifyCheckVo.getReturnUrl());
        request.setCallbackToken(faceVerifyCheckVo.getCallBackToken());
        InitFaceVerifyResponse response = initFaceVerifyAutoRoute(request);
        if(response != null) {
            InitFaceVerifyResponseBody responseBody = response.getBody();
            if(responseBody != null) {
                return responseBody;
            }
        }
        return null;
    }

    private InitFaceVerifyResponse initFaceVerifyAutoRoute(InitFaceVerifyRequest request) {
        // 第一个为主区域Endpoint，第二个为备区域Endpoint。
        List<String> endpoints = Arrays.asList("cloudauth.cn-shanghai.aliyuncs.com", "cloudauth.cn-beijing.aliyuncs.com");
        InitFaceVerifyResponse lastResponse = null;
        for (int i = 0; i < endpoints.size(); i++) {
            try {
                InitFaceVerifyResponse response = initFaceVerify(endpoints.get(i), request);
                lastResponse = response;
                // 服务端错误，切换到下个区域调用。
                if(response != null){
                    if(500 == response.getStatusCode()){
                        continue;
                    }
                    if(response.getBody() != null){
                        if("500".equals(response.getBody().getCode())){
                            continue;
                        }
                    }
                }
                // 正常返回
                return lastResponse;
            }catch (Exception e) {
                log.error("人脸认证失败:", e);
                if(i == endpoints.size()-1){
                    throw new RuntimeException(e);
                }
            }
        }

        return lastResponse;
    }

    private InitFaceVerifyResponse initFaceVerify(String endpoint, InitFaceVerifyRequest request)
            throws Exception {
        Config config = new Config();
        config.setCredential(faceVerifyClient);
        config.setEndpoint(endpoint);
        Client client = new Client(config);
        // 创建RuntimeObject实例并设置运行参数。
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 10000;
        runtime.connectTimeout = 10000;
        return client.initFaceVerifyWithOptions(request, runtime);
    }
}
