package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.vo.FaceVerifyCheckVo;

public interface FaceVerifyService {

    /**
     * 初始化人脸验证
     * @return 结果
     */
    ApiResult initFaceVerify(FaceVerifyCheckVo faceVerifyCheckVo);

    /**
     * 设计师初始化人脸验证
     * @return 结果
     */
    ApiResult initFaceVerifyDesigner(FaceVerifyCheckVo faceVerifyCheckVo);

    /**
     * 根据唯一标识获取人脸验证结果
     * @return 结果
     */
    ApiResult getFaceVerifyResult(String certifyId);

    /**
     * 根据用户信息获取人脸验证结果
     * @return 结果
     */
    ApiResult getFaceVerifyResult();

    /**
     * 获取回调结果
     */
    ApiResult callBack(FaceVerifyCheckVo faceVerifyCheckVo);

}
