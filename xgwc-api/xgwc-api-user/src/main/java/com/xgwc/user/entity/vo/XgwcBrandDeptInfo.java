package com.xgwc.user.entity.vo;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-25  17:13
 */

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 组织管理-部门表级联
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XgwcBrandDeptInfo extends XgwcBrandDeptVo{

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<XgwcBrandDeptInfo> chiledrenList;
}
