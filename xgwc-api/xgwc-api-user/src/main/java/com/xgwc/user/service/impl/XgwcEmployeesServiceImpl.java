package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.EmployeesDateFlag;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.StaffMapper;
import com.xgwc.user.dao.XgwcEmployeesMapper;
import com.xgwc.user.entity.Staff;
import com.xgwc.user.entity.XgwcEmpAccounts;
import com.xgwc.user.entity.XgwcEmpAttachments;
import com.xgwc.user.entity.XgwcEmployees;
import com.xgwc.user.entity.dto.XgwcEmployeesCountDto;
import com.xgwc.user.entity.dto.XgwcEmployeesDto;
import com.xgwc.user.entity.vo.XgwcEmployeesQueryVo;
import com.xgwc.user.entity.vo.XgwcEmployeesVo;
import com.xgwc.user.service.IEmployeesService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;


@Service
@Slf4j
public class XgwcEmployeesServiceImpl extends BaseController implements IEmployeesService {
    @Resource
    private XgwcEmployeesMapper xgwcEmployeesMapper;
    @Resource
    private StaffMapper staffMapper;
    public static final Integer STATUS_ACTIVE = 1;
    public static final Integer STATUS_RESIGNING = 2;

    /**
     * 查询员工档案
     *
     * @param employeeId 员工档案主键
     * @return 员工档案
     */
    @Override
    public XgwcEmployeesDto selectEmployeesByEmployeeId(Long employeeId) {
        XgwcEmployeesDto xgwcEmployeesDto = xgwcEmployeesMapper.selectEmployeesByEmployeeId(employeeId);
        if (xgwcEmployeesDto != null) {
            List<XgwcEmpAttachments> xgwcEmpAttachments = xgwcEmployeesMapper.selectXgwcEmpAttachments(xgwcEmployeesDto.getEmployeeId());
            // 解密 true-加密 false-解密
            extracted(xgwcEmployeesDto);
            xgwcEmployeesDto.setXgwcEmpAttachments(xgwcEmpAttachments);
        }
        return xgwcEmployeesDto;
    }

    private void extracted(XgwcEmployeesDto xgwcEmployeesDto) {
        // 解密 true-加密 false-解密
        xgwcEmployeesDto.setPhone(ParamDecryptUtil.encryptField(xgwcEmployeesDto.getPhone(), "手机号", false));
        xgwcEmployeesDto.setEmail(ParamDecryptUtil.encryptField(xgwcEmployeesDto.getEmail(), "邮箱", false));
        xgwcEmployeesDto.setIdNumber(ParamDecryptUtil.encryptField(xgwcEmployeesDto.getIdNumber(), "身份证号", false));
        xgwcEmployeesDto.setAddress(ParamDecryptUtil.encryptField(xgwcEmployeesDto.getAddress(), "员工地址", false));
        xgwcEmployeesDto.setEmerPhone(ParamDecryptUtil.encryptField(xgwcEmployeesDto.getEmerPhone(), "紧急联系人电话", false));
        xgwcEmployeesDto.setAlipayAccount(ParamDecryptUtil.encryptField(xgwcEmployeesDto.getAlipayAccount(), "支付宝账号", false));
        xgwcEmployeesDto.setAccountNumber(ParamDecryptUtil.encryptField(xgwcEmployeesDto.getAccountNumber(), "银行卡号", false));
    }

    /**
     * 查询员工档案列表
     *
     * @param employees 员工档案
     * @return 员工档案
     */
    @Override
    public XgwcEmployeesCountDto selectEmployeesList(XgwcEmployeesQueryVo employees) {
/*        Date[] entryDate = employees.getEntryDate();
        Integer dateFlag = employees.getDateFlag();
        if (dateFlag != null) {
            EmployeesDateFlag textByCode = EmployeesDateFlag.getTextByCode(dateFlag);
            if (textByCode != null) {
                extracted(employees, entryDate, textByCode);
            }
        }*/
        String name = employees.getName();
        boolean letterOrChinese = isLetterOrChinese(name);
        if (!letterOrChinese) {
            // 对敏感入参加密 加密之后匹配数据库中加密字段
            employees.setName(ParamDecryptUtil.encryptField(name, "/手机号/银行卡号/支付宝账号/身份证号", true));
        }
        PageHelper.startPage(employees.getPageNum(),  employees.getPageSize());
        employees.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        List<XgwcEmployeesDto> xgwcEmployeesDtos = xgwcEmployeesMapper.selectEmployeesList(employees);
        for (XgwcEmployeesDto xgwcEmployeesDto : xgwcEmployeesDtos) {
            // 解密 true-加密 false-解密
            xgwcEmployeesDto.setPhone(ParamDecryptUtil.encryptField(xgwcEmployeesDto.getPhone(), "手机号", false));
        }
        XgwcEmployeesQueryVo xgwcEmployeesQueryVo = new XgwcEmployeesQueryVo();
        xgwcEmployeesQueryVo.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        List<XgwcEmployeesDto> xgwcEmployeesDtos1 = xgwcEmployeesMapper.selectEmployeesList(xgwcEmployeesQueryVo);
        XgwcEmployeesCountDto xgwcEmployeesCountDto = getCurrentEmployeesCount(xgwcEmployeesDtos1);
        ApiResult dataTable = getDataTable(xgwcEmployeesDtos);
        xgwcEmployeesCountDto.setEmployeesDtos(dataTable);
        return xgwcEmployeesCountDto;
    }

    private XgwcEmployeesCountDto getCurrentEmployeesCount(List<XgwcEmployeesDto> employees) {
        XgwcEmployeesCountDto result = new XgwcEmployeesCountDto();

        for (XgwcEmployeesDto e : employees) {
            updateStatusCount(result, e.getStatus());
            updateEntryCount(result, e.getEntryDate());
            updateExitCount(result, e.getResignationDate());
            updateContractCount(result, e.getContractEndDate());
        }

        return result;
    }

    private void updateStatusCount(XgwcEmployeesCountDto result, Integer status) {
        if (STATUS_ACTIVE.equals(status)) {
            result.incrementCurrentCount();
        } else if (STATUS_RESIGNING.equals(status)) {
            result.incrementResignationCount();
        }
    }

    private void updateEntryCount(XgwcEmployeesCountDto result, Date entryDate) {
        if (DateUtils.isDateInCurrentMonth(entryDate)) {
            result.incrementMonthlyEntryCount();
        }
    }

    private void updateExitCount(XgwcEmployeesCountDto result, Date resignationDate) {
        if (DateUtils.isDateInCurrentMonth(resignationDate)) {
            result.incrementMonthlyExitCount();
        }
    }

    private void updateContractCount(XgwcEmployeesCountDto result, Date contractEndDate) {
        if (DateUtils.isWithinNextThreeMonths(contractEndDate)) {
            result.incrementContractMonthsCount();
        }
    }

    private static boolean isLetterOrChinese(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }
        // 正则规则：仅由字母或中文组成
        return str.matches("^[a-zA-Z\\p{Script=Han}]+$");
    }

    private static void extracted(XgwcEmployeesQueryVo employees, Date[] entryDate, EmployeesDateFlag textByCode) {
        switch (textByCode) {
            case CONTRACT_END_DATE -> employees.setContractEndDate(entryDate);
            case RESIGNATION_DATE -> employees.setResignationDate(entryDate);
            case BIRTHDATE -> employees.setBirthdate(entryDate);
            default -> employees.setEntryDate(entryDate);
        }
    }

    /**
     * 新增员工档案
     *
     * @param vo 员工档案
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult insertEmployees(XgwcEmployeesVo vo) {
        if (vo == null) {
            log.error("员工信息录入失败：输入参数不能为空");
            throw new IllegalArgumentException("员工信息不能为空");
        }

        validateEmployee(vo);  // 实体校验

        // 参数唯一性校验
        try {
            String validated = validateUniqueFieldsAsync(vo);
            if (validated != null) {
                return ApiResult.error(validated);
            }
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {

            // ================= 员工信息处理 =================
            XgwcEmployees xgwcEmployees = processEmployeeInfo(vo);
            xgwcEmployees.setCreateBy(SecurityUtils.getNickName());
            xgwcEmployees.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
            int employeesResult = xgwcEmployeesMapper.insertEmployees(xgwcEmployees);
            Staff staff = new Staff();
            staff.setId(xgwcEmployees.getStaffId());
            staff.setArchiveStatus(1);
            staffMapper.updateStaff(staff);
            if (employeesResult <= 0) {
                log.error("员工信息插入失败");
                throw new ApiException("员工信息插入失败");
            }

            // ================= 账户信息处理 =================
            XgwcEmpAccounts xgwcEmpAccounts = processAccountInfo(vo);
            xgwcEmpAccounts.setEmployeeId(xgwcEmployees.getEmployeeId());
            xgwcEmpAccounts.setCreateBy(SecurityUtils.getNickName());
            xgwcEmpAccounts.setCreateTime(new Date());
            int accountsResult = xgwcEmployeesMapper.insertXgwcEmpAccounts(xgwcEmpAccounts);
            if (accountsResult <= 0) {
                log.error("账户信息插入失败");
                throw new ApiException("账户信息插入失败");
            }

            // ================= 附件信息处理 =================
            List<XgwcEmpAttachments> attachments = vo.getXgwcEmpAttachments();
            if (!CollectionUtils.isEmpty(attachments)) {
                for (XgwcEmpAttachments attachment : attachments) {
                    attachment.setEmployeeId(xgwcEmployees.getEmployeeId());
                    attachment.setCreateBy(SecurityUtils.getNickName());
                    int attachmentsResult = xgwcEmployeesMapper.insertXgwcEmpAttachments(attachment);
                    if (attachmentsResult <= 0) {
                        log.error("附件插入失败，员工ID: {}, 附件名称: {}",
                                xgwcEmployees.getEmployeeId(), attachment.getFileName());
                        throw new ApiException("附件插入失败");
                    }
                }
            }

            log.info("员工信息录入成功 ID:[{}] 姓名:[{}]",
                    xgwcEmployees.getEmployeeId(), vo.getName());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("系统异常 员工ID:[{}] 错误信息: {}",
                    vo.getStaffId() != null ? vo.getStaffId() : "N/A",
                    e.getMessage(), e);
            throw new ApiException("系统处理异常", e);
        }
    }

    private String validateUniqueFieldsAsync(XgwcEmployeesVo vo) throws ExecutionException, InterruptedException {
        XgwcEmployees xgwcEmployees = processEmployeeInfo(vo);
        XgwcEmpAccounts xgwcEmpAccounts = processAccountInfo(vo);

        CompletableFuture<String> idNumberFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniqueness(xgwcEmployees.getIdNumber(), "身份证号", xgwcEmployeesMapper::existsByIdNumber)
        );

        CompletableFuture<String> phoneFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniqueness(xgwcEmployees.getPhone(), "手机号", xgwcEmployeesMapper::existsByPhone)
        );

        CompletableFuture<String> accountNumberFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniqueness(xgwcEmpAccounts.getAccountNumber(), "银行卡号", xgwcEmployeesMapper::existsByAccountNumber)
        );

        CompletableFuture<String> alipayAccountFuture = CompletableFuture.supplyAsync(() ->
                validateFieldUniqueness(xgwcEmpAccounts.getAlipayAccount(), "支付宝账号", xgwcEmployeesMapper::existsByAlipayAccount)
        );

        // 等待所有异步任务完成，并收集结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(idNumberFuture, phoneFuture, accountNumberFuture, alipayAccountFuture);
        allFutures.get(); // 阻塞直到所有任务完成

        // 收集所有校验结果
        List<String> errorMessages = new ArrayList<>();

        String idNumberResult = idNumberFuture.get();
        if (idNumberResult != null) {
            errorMessages.add(idNumberResult);
        }

        String phoneResult = phoneFuture.get();
        if (phoneResult != null) {
            errorMessages.add(phoneResult);
        }

        String accountNumberResult = accountNumberFuture.get();
        if (accountNumberResult != null) {
            errorMessages.add(accountNumberResult);
        }

        String alipayAccountResult = alipayAccountFuture.get();
        if (alipayAccountResult != null) {
            errorMessages.add(alipayAccountResult);
        }

        // 拼接所有错误信息
        if (!errorMessages.isEmpty()) {
            return String.join("，", errorMessages);
        }

        return null;
    }

    private String validateFieldUniqueness(
            String fieldValue,
            String fieldName,
            Function<String, Boolean> existsChecker
    ) {
        if (fieldValue != null && !fieldValue.trim().isEmpty() && existsChecker.apply(fieldValue)) {
            log.error("========================{}已存在", fieldName);
            return (fieldName + "已存在");
        }
        return null;
    }

    // 员工信息处理（抽取方法）
    private XgwcEmployees processEmployeeInfo(XgwcEmployeesVo dto) {
        XgwcEmployees entity = BeanUtil.copyProperties(dto, XgwcEmployees.class);

        // 字段加密 true-加密 false-解密
        entity.setPhone(ParamDecryptUtil.encryptField(dto.getPhone(), "手机号", true));
        entity.setEmail(ParamDecryptUtil.encryptField(dto.getEmail(), "邮箱", true));
        entity.setIdNumber(ParamDecryptUtil.encryptField(dto.getIdNumber(), "身份证号", true));
        entity.setAddress(ParamDecryptUtil.encryptField(dto.getAddress(), "员工住址", true));
        entity.setEmerPhone(ParamDecryptUtil.encryptField(dto.getEmerPhone(), "紧急联系人手机号", true));
        return entity;
    }

    // 账户信息处理（抽取方法）
    private XgwcEmpAccounts processAccountInfo(XgwcEmployeesVo dto) {
        XgwcEmpAccounts account = BeanUtil.copyProperties(dto, XgwcEmpAccounts.class);

        // 字段加密 true-加密 false-解密
        account.setAccountNumber(ParamDecryptUtil.encryptField(dto.getAccountNumber(), "银行账号", true));
        account.setAlipayAccount(ParamDecryptUtil.encryptField(dto.getAlipayAccount(), "支付宝账号", true));
        return account;
    }

    // 员工实体校验
    private void validateEmployee(XgwcEmployeesVo employee) {
        if (StringUtils.isBlank(employee.getIdNumber())) {
            throw new IllegalArgumentException("身份证号不能为空");
        }
        if (StringUtils.isBlank(employee.getPhone())) {
            throw new IllegalArgumentException("手机号不能为空");
        }
        if (StringUtils.isBlank(employee.getAccountNumber())) {
            throw new IllegalArgumentException("银行账号不能为空");
        }
        if (StringUtils.isBlank(employee.getAlipayAccount())) {
            throw new IllegalArgumentException("支付宝账号不能为空");
        }

    }


    /**
     * 修改员工档案
     *
     * @param dto 员工档案
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateEmployees(XgwcEmployeesVo dto) {

        // 参数校验前置
        if (dto == null) {
            log.error("员工信息录入失败：输入参数不能为空");
            throw new IllegalArgumentException("员工信息不能为空");
        }

        XgwcEmployeesDto employees = selectEmployeesByEmployeeId(dto.getStaffId());
        if(employees ==  null){
            return ApiResult.error("员工信息不存在");
        }

        if (Objects.equals(employees.getPhone(), dto.getPhone())) {
            dto.setPhone(null);
        }
        if (Objects.equals(employees.getIdNumber(), dto.getIdNumber())) {
            dto.setIdNumber(null);
        }
        if (Objects.equals(employees.getAccountNumber(), dto.getAccountNumber())) {
            dto.setAccountNumber(null);
        }
        if (Objects.equals(employees.getAlipayAccount(), dto.getAlipayAccount())) {
            dto.setAlipayAccount(null);
        }

        // 参数唯一性校验
        try {
            String validated = validateUniqueFieldsAsync(dto);
            if (validated != null) {
                return ApiResult.error(validated);
            }
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
            // ================= 员工信息处理 =================
            XgwcEmployees xgwcEmployees = processEmployeeInfo(dto);
            xgwcEmployees.setUpdateBy(SecurityUtils.getNickName());
            int employeesResult = xgwcEmployeesMapper.updateEmployees(xgwcEmployees);
            if (employeesResult <= 0) {
                log.error("员工信息修改失败");
                throw new ApiException("员工信息修改失败");
            }
            // ================= 账户信息处理 =================
            XgwcEmpAccounts xgwcEmpAccounts = processAccountInfo(dto);
            xgwcEmpAccounts.setUpdateBy(SecurityUtils.getNickName());
            int accountsResult = xgwcEmployeesMapper.updateXgwcEmpAccounts(xgwcEmpAccounts);
            if (accountsResult <= 0) {
                log.error("账户信息修改失败");
                throw new ApiException("账户信息修改失败");
            }

            // ================= 附件信息处理 =================
            List<XgwcEmpAttachments> xgwcEmpAttachments = dto.getXgwcEmpAttachments();
            xgwcEmployeesMapper.deleteXgwcEmpAttachments(dto.getEmployeeId());
            if (!CollectionUtils.isEmpty(xgwcEmpAttachments)) {
                for (XgwcEmpAttachments attachment : xgwcEmpAttachments) {
                    attachment.setEmployeeId(dto.getEmployeeId());
                    attachment.setUpdateBy(SecurityUtils.getNickName());
                    int updAttachmentsResult = xgwcEmployeesMapper.insertXgwcEmpAttachments(attachment);
                    if (updAttachmentsResult <= 0) {
                        log.error("附件信息修改失败");
                        throw new ApiException("附件信息修改失败");
                    }
                }
            }

            log.info("员工信息修改成功 ID:[{}] 姓名:[{}]",
                    xgwcEmployees.getEmployeeId(),
                    dto.getName());
            return ApiResult.ok();
        } catch (Exception e) {  // 全局异常捕获
            log.error("系统异常 员工:[{}] 错误信息:{}",
                    dto.getEmployeeId(), e.getMessage(), e);
            throw new ApiException("系统处理异常");
        }
    }

    @Override
    public int resignationsEmployees(XgwcEmployeesVo employees) {
        return 0;
    }
}
