package com.xgwc.user.util;

import com.xgwc.user.security.config.SecurityProperties;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class JwtUtil {

    @Resource
    private SecurityProperties securityProperties;

    /**
     * 使用用户id作为加密对象, 默认时间为一天有效期
     */
    public String generateToken(Long userId) {
        return generateToken(userId, securityProperties.getJwt().getTtl());
    }


    /**
     * 使用用户id作为加密对象
     */
    public String generateToken(Long userId, long ttlMillis) {
        SecretKey key = Keys.hmacShaKeyFor(securityProperties.getJwt().getKey().getBytes(StandardCharsets.UTF_8));
        Map<String, Object> claims = new HashMap<>();
        claims.put("customClaim", "customValue"); // 添加自定义声明
        return Jwts.builder()
                .claims(claims) // 添加自定义声明
                .subject(userId + "") // 设置主题
                .issuedAt(new Date()) // 设置签发时间
                .expiration(new Date(System.currentTimeMillis() + ttlMillis)) // 设置过期时间
                .signWith(key) // 设置签名算法和密钥
                .compact(); // 生成 JWT
    }

    public Claims parseToken(String token) {
        SecretKey key = Keys.hmacShaKeyFor(securityProperties.getJwt().getKey().getBytes(StandardCharsets.UTF_8));
        return Jwts.parser()
                .verifyWith(key) // 设置密钥
                .build()
                .parseSignedClaims(token) // 解析 JWT
                .getPayload(); // 获取 Claims
    }

    public Long getUserId(String token) {
        SecretKey key = Keys.hmacShaKeyFor(securityProperties.getJwt().getKey().getBytes(StandardCharsets.UTF_8));
        Claims claims = Jwts.parser()
                .verifyWith(key) // 设置密钥
                .build()
                .parseSignedClaims(token) // 解析 JWT
                .getPayload(); // 获取 Claims
        if(claims != null){
            return Long.parseLong(claims.getSubject());
        }
        return null;
    }

    public boolean validateToken(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(securityProperties.getJwt().getKey().getBytes(StandardCharsets.UTF_8));
            Jwts.parser()
                    .verifyWith(key) // 设置密钥
                    .build()
                    .parseSignedClaims(token); // 解析 JWT
            return true;
        } catch (ExpiredJwtException e) {
            log.warn("token:{},已过期", token);
            return false;
        } catch (Exception e) {
            log.error("验证token异常,token:{}, error:", token, e);
            return false;
        }
    }

    public static SecretKey generateKey(int size) throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("HmacSHA256");
        keyGenerator.init(size); // 初始化为256位
        SecretKey secretKey = keyGenerator.generateKey();
        return secretKey;
    }

    /**
     * size 传值 256
     */
    public static String encodeKey(int size) {
        try {
            return Base64.getEncoder().encodeToString(generateKey(size).getEncoded());
        } catch (Exception e) {
            log.error("获取key失败", e);
        }
        return "";
    }

    public static SecretKey decodeKey(String encodedKey) {
        byte[] keyBytes = Base64.getDecoder().decode(encodedKey);
        return new SecretKeySpec(keyBytes, "HmacSHA256");
    }
}
