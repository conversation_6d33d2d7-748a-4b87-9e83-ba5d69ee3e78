package com.xgwc.user.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class ServiceAuthorizeVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("服务商id")
    @NotNull(message = "服务商id不能为空")
    private Long serviceId;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("状态：0正常，1禁用")
    @NotNull(message = "状态不能为空")
    private Long status;

}
