package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.FranchiseEmployeesDto;
import com.xgwc.user.entity.vo.FranchiseEmployeesQueryVo;
import com.xgwc.user.entity.vo.FranchiseEmployeesVo;
import com.xgwc.user.service.FranchiseEmployeesService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * 加盟商员工档案管理
 */
@RestController
@RequestMapping("/franchise/employees")
public class FranchiseEmployeesController extends BaseController {
    @Resource
    private FranchiseEmployeesService franchiseEmployeesService;

    /**
     * 查询员工档案列表
     */
    @MethodDesc("查询员工档案列表")
    @PreAuthorize("@ss.hasPermission('employees:employees:list')")
    @PostMapping("/list")
    public ApiResult list(@RequestBody FranchiseEmployeesQueryVo employees) {
        return ApiResult.ok(franchiseEmployeesService.selectEmployeesList(employees));
    }

    /**
     * 获取员工档案详细信息
     */
    @MethodDesc("获取员工档案详细信息")
    //@PreAuthorize("@ss.hasPermission('employees:employees:query')")
    @GetMapping(value = "/{employeeId}")
    public ApiResult<FranchiseEmployeesDto> getInfo(@PathVariable("employeeId") Long employeeId) {
        return success(franchiseEmployeesService.selectEmployeesByEmployeeId(employeeId));
    }

    /**
     * 新增员工档案
     */
    @MethodDesc("新增员工档案")
    @PreAuthorize("@ss.hasPermission('employees:employees:add')")
    @Log(title = "员工档案", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody FranchiseEmployeesVo employees) {
        return franchiseEmployeesService.insertEmployees(employees);
    }

    /**
     * 修改员工档案
     */
    @MethodDesc("修改员工档案")
    @PreAuthorize("@ss.hasPermission('employees:employees:edit')")
    @Log(title = "员工档案", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody FranchiseEmployeesVo employees) {
        return franchiseEmployeesService.updateEmployees(employees);
    }

    /**
     * 申请离职
     */
    @MethodDesc("申请离职")
    //@PreAuthorize("@ss.hasPermission('employees:employees:resignations')")
    @PostMapping("resignations")
    public ApiResult resignations(@RequestBody FranchiseEmployeesVo employees) {
        return toAjax(franchiseEmployeesService.resignationsEmployees(employees));
    }


}
