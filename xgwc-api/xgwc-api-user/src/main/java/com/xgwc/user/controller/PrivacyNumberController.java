package com.xgwc.user.controller;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.StringUtil;
import com.xgwc.user.entity.vo.PrivacyNumberCallBackVo;
import com.xgwc.user.entity.vo.PrivacyNumberVo;
import com.xgwc.user.service.PrivacyNumberService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("privacy")
@RestController
public class PrivacyNumberController {

    @Resource
    private PrivacyNumberService privacyNumberService;

    @RequestMapping("bind_number")
    public ApiResult bindNumber(PrivacyNumberVo privacyNumberVo) {
        if(StringUtil.isNotEmpty(privacyNumberVo.getBindNumberA()) && StringUtil.isNotEmpty(privacyNumberVo.getBindNumberB())) {
            return privacyNumberService.bindNumber(privacyNumberVo);
        }
        return ApiResult.ok();
    }

    @RequestMapping("customer/bind_number")
    public ApiResult bindCustomerNumber(PrivacyNumberVo privacyNumberVo) {
        if(StringUtil.isNotEmpty(privacyNumberVo.getBindNumberB())) {
            return privacyNumberService.bindNumber(privacyNumberVo);
        }
        return ApiResult.ok();
    }

    /**
     * 绑定登陆人的手机号和设计师的手机号
     * @param designerId 设计师id
     */
    @RequestMapping("designer/bind_number")
    public ApiResult bindNumber2(Long designerId) {
        if(designerId != null) {
            return privacyNumberService.bindDesignerNumber(designerId);
        }
        return ApiResult.ok();
    }

    @RequestMapping("unbind_number")
    public ApiResult unBindNumber(PrivacyNumberVo privacyNumberVo) {
        if(StringUtil.isNotEmpty(privacyNumberVo.getBindNumberA()) && StringUtil.isNotEmpty(privacyNumberVo.getBindNumberB())) {
            return privacyNumberService.unbindNumber(privacyNumberVo);
        }
        return ApiResult.ok();
    }

    @RequestMapping("callback")
    public JSONObject callback(@RequestBody PrivacyNumberCallBackVo privacyNumberVo) {
        privacyNumberService.addCallbackRecord(privacyNumberVo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("resultCode", "200");
        return jsonObject;
    }
}
