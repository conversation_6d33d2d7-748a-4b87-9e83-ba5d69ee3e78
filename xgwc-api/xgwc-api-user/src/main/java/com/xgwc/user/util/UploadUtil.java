package com.xgwc.user.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

@Slf4j
public class UploadUtil {

    public static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd/HHmmss");

    private static Random random;

    static{
        try {
            random = new Random();
        } catch (Exception e) {
            log.error("生成随机数错误error:", e);
        }
    }

    /**
     * 文件名规则 年月日时分秒 + 8位随机数
     */
    public static String buildFileName(MultipartFile file){
        LocalDateTime now = LocalDateTime.now();
        String fileName = now.format(formatter);
        StringBuilder randomPart = new StringBuilder();
        for (int i = 0; i < 3; i++) {
            randomPart.append(random.nextInt(10)); // 生成0-9的随机数字
        }
        return "file".concat("/").concat(fileName) + "/" +randomPart + "/" +file.getOriginalFilename();
    }
}
