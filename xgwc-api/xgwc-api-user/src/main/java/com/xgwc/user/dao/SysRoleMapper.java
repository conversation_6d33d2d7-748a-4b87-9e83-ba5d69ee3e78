package com.xgwc.user.dao;

import java.util.List;

import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.SysRole;
import com.xgwc.user.entity.SysRoleMenu;
import com.xgwc.user.entity.vo.SysRoleVo;
import com.xgwc.user.entity.dto.SysRoleDto;
import com.xgwc.user.entity.vo.SysRoleQueryVo;
import org.apache.ibatis.annotations.Param;


public interface SysRoleMapper  {
    /**
     * 查询角色管理
     * 
     * @param id 角色管理主键
     * @return 角色管理
     */
    public SysRoleDto selectSysRoleById(Long id);

    /**
     * 查询角色管理列表
     * 
     * @param sysRole 角色管理
     * @return 角色管理集合
     */
    public List<SysRoleDto> selectSysRoleList(SysRoleQueryVo sysRole);

    /**
     * 新增角色管理
     * 
     * @param sysRole 角色管理
     * @return 结果
     */
    @TenantIgnore
    public int insertSysRole(SysRole sysRole);

    /**
     * 修改角色管理
     * 
     * @param sysRole 角色管理
     * @return 结果
     */
    public int updateSysRole(SysRole sysRole);

    /**
     * 删除角色管理
     * 
     * @param id 角色管理主键
     * @return 结果
     */
    public int deleteSysRoleById(Long id);

    /**
     * 批量删除角色管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysRoleByIds(Long[] ids);

    @TenantIgnore
    public int insertSysRoleMenu(@Param("array") List<SysRoleMenu> sysRoleMenus);
    @TenantIgnore
    public int insertSysRoleUser(Long roleId,Long userId);
}
