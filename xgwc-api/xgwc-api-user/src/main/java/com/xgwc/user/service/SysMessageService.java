package com.xgwc.user.service;


import com.xgwc.user.entity.MessageTypeKey;
import com.xgwc.user.entity.SysMessage;

import java.util.List;

public interface SysMessageService {

    /**
     * 插入系统消息
     * @param sysMessage 消息
     * @return 是否成功
     */
    int insertSysMessage(SysMessage sysMessage);

    /**
     * 批量插入系统消息
     * @param sysMessages 消息
     * @return 是否成功
     */
    int batchInsertSysMessage(List<SysMessage> sysMessages);

    /**
     * 更新系统消息
     * @param id 消息
     * @return 是否成功
     */
    SysMessage getMessageDetail(Long id);

    /**
     * 获取消息列表
     * @param sysMessage 参数
     * @return 消息列表
     */
    List<SysMessage> getMessageList(SysMessage sysMessage);


    /**
     * 获取typeKey分组
     */
    List<MessageTypeKey> getMessageTypeKeyGroup();
}
