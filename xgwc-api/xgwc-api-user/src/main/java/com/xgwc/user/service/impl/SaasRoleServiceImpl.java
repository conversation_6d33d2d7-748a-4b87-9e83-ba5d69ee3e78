package com.xgwc.user.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.RoleScopeType;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.FilterMenuListUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.serviceProvider.feign.api.ServiceRoleFeign;
import com.xgwc.serviceProvider.feign.entity.ServiceRoleDto;
import com.xgwc.user.entity.dto.FranchiseRoleDto;
import com.xgwc.user.entity.dto.XgwcBrandRoleDto;
import com.xgwc.user.entity.vo.*;
import com.xgwc.user.service.FranchiseRoleService;
import com.xgwc.user.service.XgwcBrandRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.SaasRoleMapper;
import com.xgwc.user.service.ISaasRoleService;
import com.xgwc.user.entity.dto.SaasRoleDto;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


@Service
@Slf4j
public class SaasRoleServiceImpl implements ISaasRoleService {
    @Resource
    private SaasRoleMapper saasRoleMapper;
    @Resource
    private ServiceRoleFeign serviceRoleFeign;
    @Resource
    private FranchiseRoleService saasRoleService;
    @Resource
    private XgwcBrandRoleService xgwcBrandRoleService;

    /**
     * 查询saas后台角色
     *
     * @param roleId saas后台角色主键
     * @return saas后台角色
     */
    @Override
    public ApiResult selectSaasRoleByRoleId(Long roleId) {
        if (roleId == null || roleId <= 0) {
            log.error("无效的角色ID: {}", roleId);
            return ApiResult.error("角色ID不能为空或非法");
        }
        SaasRoleDto saasRole = saasRoleMapper.selectSaasRoleByRoleId(roleId);
        if (saasRole == null) {
            log.warn("未找到对应的角色信息，roleId: {}", roleId);
            return ApiResult.error("未找到该角色的信息");
        }

        // 获取角色权限范围
        List<SaasRoleDto.SaasRoleScopeDto> saasRoleScopeDtos = saasRoleMapper.selectRoleScopeById(roleId);

        String isFlag = RoleScopeType.getIsFlag(saasRole.getRoleScope());
        List<SysRoleMenuVo> filteredMenuList = FilterMenuListUtil.filterList(
                saasRoleMapper.getRoleMenusById(roleId),
                roleMenuVo -> saasRoleMapper.selectLastLevelMenu(roleMenuVo.getMenuId(), isFlag)
        );

        List<SysRoleDataVo> filteredMenuDateList = FilterMenuListUtil.filterList(
                saasRoleMapper.getRoleDataById(roleId),
                sysRoleDataVo -> saasRoleMapper.selectLastLevelMenuDate(sysRoleDataVo.getMenuId(), isFlag)
        );

        // 首先根据 menuId 分组，收集所有 deptId
        Map<Long, List<Long>> deptIdsByMenuId = filteredMenuDateList.stream()
                .collect(Collectors.groupingBy(
                        SysRoleDataVo::getMenuId,
                        Collectors.mapping(SysRoleDataVo::getDeptId, Collectors.toList())
                ));

        // 使用 toMap 收集器根据 menuId 去重
        Map<Long, SysRoleDataVo> uniqueByMenuId = filteredMenuDateList.stream()
                .collect(Collectors.toMap(
                        SysRoleDataVo::getMenuId,
                        vo -> {
                            SysRoleDataVo newVo = new SysRoleDataVo();
                            newVo.setMenuId(vo.getMenuId());
                            newVo.setDeptIds(deptIdsByMenuId.get(vo.getMenuId()));
                            newVo.setId(vo.getId());
                            newVo.setRoleId(vo.getRoleId());
                            newVo.setRoleName(vo.getRoleName());
                            newVo.setMenuName(vo.getMenuName());
                            newVo.setDataScope(vo.getDataScope());
                            newVo.setDataMasking(vo.getDataMasking());
                            newVo.setTimeLimit(vo.getTimeLimit());
                            return newVo;
                        },
                        (existing, replacement) -> existing // 保留第一个出现的对象
                ));
        List<SysRoleDataVo> uniqueList = new ArrayList<>(uniqueByMenuId.values());
        SaasRoleDto saasRoleDto = new SaasRoleDto();
        BeanUtils.copyProperties(saasRole, saasRoleDto);
        saasRoleDto.setDataMenuIds(uniqueList);
        saasRoleDto.setMenuIds(filteredMenuList);
        saasRoleDto.setRoleScopeDtos(saasRoleScopeDtos);

        return ApiResult.ok(saasRoleDto);
    }

    /**
     * 查询saas后台角色列表
     *
     * @param saasRole saas后台角色
     * @return saas后台角色
     */
    @Override
    public List<SaasRoleDto> selectSaasRoleList(SaasRoleQueryVo saasRole) {
        return saasRoleMapper.selectSaasRoleList(saasRole);
    }

    /**
     * 新增saas后台角色
     *
     * @param saasRole saas后台角色
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // 回滚所有异常
    public ApiResult insertSaasRole(SaasRoleVo saasRole) {
        try {
            // 1. 参数校验
            if (saasRole == null) {
                log.error("角色信息不能为空");
                return ApiResult.error("角色信息不能为空");
            }
            String roleName = saasRole.getRoleName();

            // 2. 保存角色基本信息
            saasRole.setCreateBy(SecurityUtils.getNickName());
            if (saasRole.getRoleScope() == RoleScopeType.SAAS_GUAN.getCode()) {
                saasRole.setIsFlag("saasAdmin");
            }
            int saveResult = saasRoleMapper.insertSaasRole(saasRole);
            if (saveResult <= 0) {
                log.error("新增saas后台角色失败，角色名称={}", roleName);
                throw new ApiException("新增角色失败"); // 抛出异常触发回滚
            }

            // 3. 保存角色-菜单关联关系
            List<SysRoleMenuVo> menuIds = saasRole.getMenuIds();
            Long roleId = saasRole.getRoleId();
            saveRoleMenuRelations(saasRole, roleId, menuIds, false);

            log.info("角色及菜单关联保存成功，角色名称={}", roleName);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("保存saas后台角色及菜单关联异常，角色名称={}, 错误信息：{}",
                    saasRole.getRoleId(), e.getMessage(), e);
            throw e; // 重新抛出异常确保事务回滚
        }
    }

    // 使用 public 确保通过代理调用（避免自调用事务失效）
    public void saveRoleMenuRelations(SaasRoleVo saasRole, Long roleId, List<SysRoleMenuVo> menuIds, boolean isUpdate) {

        // 插入角色权限范围
        int inserted = saasRoleMapper.insertSaasRoleScope(saasRole);
        if (inserted <= 0) {
            log.error("新增失败，角色权限范围表影响行数：{}", inserted);
            throw new ApiException("新增失败");
        }

        if (!CollectionUtils.isEmpty(menuIds)) {
            List<SysRoleMenuVo> sassRoleMenuVoList = new ArrayList<>();
            for (SysRoleMenuVo menuId : menuIds) {
                SysRoleMenuVo rm = new SysRoleMenuVo();
                rm.setRoleId(roleId);
                rm.setMenuId(menuId.getMenuId());
                rm.setCreateBy(saasRole.getCreateBy());
                sassRoleMenuVoList.add(rm);
            }
            // 批量插入，若失败则抛出异常
            int insertCount = saasRoleMapper.saveRoleMenu(sassRoleMenuVoList);
            if (insertCount <= 0) {
                log.error("saas后台角色-菜单关联保存不完整，预期={}，实际={}", menuIds.size(), insertCount);
                throw new ApiException("角色-菜单关联保存失败");
            }
        }

        List<SysRoleDataVo> dataMenuIds = saasRole.getDataMenuIds();
        if (!CollectionUtils.isEmpty(dataMenuIds)) {
            for (SysRoleDataVo dataMenu : dataMenuIds) {
                List<Long> deptIds = dataMenu.getDeptIds();
                dataMenu.setRoleId(roleId);
                dataMenu.setCreateBy(SecurityUtils.getNickName());
                if (!CollectionUtils.isEmpty(deptIds)) {
                    int roleDataScope = saasRoleMapper.saveRoleDataScopeDeptId(dataMenu);
                    if (roleDataScope <= 0) {
                        log.error("saas后台新增失败，指定部门数据权限影响行数：{}", roleDataScope);
                        throw new ApiException("新增失败");
                    }
                } else {
                    int dataScopeResult = saasRoleMapper.saveRoleDataScope(dataMenu);
                    if (dataScopeResult <= 0) {
                        log.error("saas后台新增失败，数据权限表影响行数：{}", dataScopeResult);
                        throw new ApiException("新增失败");
                    }
                }
            }
        }

        if (saasRole.getIsDefault() == 1) {
            Integer roleScope = saasRole.getRoleScope();

            try {
                RoleScopeType roleScopeType = RoleScopeType.fromCode(roleScope);
                switch (roleScopeType) {
                    case PIN_PU:
                    case PIN_GUAN:
                        XgwcBrandRoleDto xgwcBrandRoleDto = new XgwcBrandRoleDto();
                        BeanUtil.copyProperties(saasRole, xgwcBrandRoleDto);
                        if (isUpdate) {
                            xgwcBrandRoleService.updateXgwcBrandRole(xgwcBrandRoleDto);
                        } else {
                            xgwcBrandRoleDto.setIsFlag(RoleScopeType.PIN_GUAN.getIsFlag());
                            xgwcBrandRoleService.saveXgwcBrandRole(xgwcBrandRoleDto);
                        }
                        break;

                    case JIA_PU:
                    case JIA_GUAN:
                        FranchiseRoleDto franchiseRole = new FranchiseRoleDto();
                        BeanUtil.copyProperties(saasRole, saasRole);
                        if (isUpdate) {
                            saasRoleService.updateFranchiseRole(franchiseRole);
                        } else {
                            saasRole.setIsFlag(RoleScopeType.JIA_GUAN.getIsFlag());
                            saasRoleService.saveFranchiseRole(franchiseRole);
                        }
                        break;

                    case FU_PU:
                    case FU_GUAN:
                        ServiceRoleDto serviceRoleDto = new ServiceRoleDto();
                        BeanUtil.copyProperties(saasRole, serviceRoleDto);
                        if (isUpdate) {
                            serviceRoleFeign.updateServiceRole(serviceRoleDto);
                        } else {
                            serviceRoleDto.setIsFlag(RoleScopeType.FU_GUAN.getIsFlag());
                            serviceRoleFeign.saveServiceRole(serviceRoleDto);
                        }
                        break;

                    case XIAO_PU:
                    case XIAO_GUAN:
                        ServiceRoleDto marketRoleDto = new ServiceRoleDto();
                        BeanUtil.copyProperties(saasRole, marketRoleDto);
                        if (isUpdate) {
                            serviceRoleFeign.updateMarketRole(marketRoleDto);
                        } else {
                            marketRoleDto.setIsFlag(RoleScopeType.XIAO_GUAN.getIsFlag());
                            serviceRoleFeign.saveMarketRole(marketRoleDto);
                        }
                        break;

                    default:
                        throw new ApiException("未知的角色类型");
                }
            } catch (IllegalArgumentException e) {
                throw new ApiException("无效的 roleScope 值: " + roleScope);
            }
        }
    }

    /**
     * 修改saas后台角色
     *
     * @param saasRole saas后台角色
     * @return 结果
     */
    @Override
    public ApiResult updateSaasRole(SaasRoleVo saasRole) {
        saasRoleMapper.updateSaasRole(saasRole);
        if (saasRole == null) {
            return ApiResult.error("角色信息不能为空");
        }
        Long roleId = saasRole.getRoleId();
        if (roleId == null) {
            return ApiResult.error("角色ID不能为空");
        }
        List<SysRoleMenuVo> menuIds = saasRole.getMenuIds();

        // 删除菜单权限
        saasRoleMapper.deleteRoleMenu(roleId);
        // 删除数据权限
        saasRoleMapper.deleteRoleData(roleId);
        // 删除角色权限范围
        saasRoleMapper.deleteRoleScope(roleId);

        // 插入角色和菜单的关联关系
        String nickName = SecurityUtils.getNickName();
        saasRole.setUpdateBy(nickName);
        saasRole.setCreateBy(nickName);
        saveRoleMenuRelations(saasRole, roleId, menuIds, true);
        int updated = saasRoleMapper.updateSaasRole(saasRole);
        if (updated > 0) {
            log.info("修改成功，id为：{}", roleId);
            return ApiResult.ok();
        }
        log.error("修改失败，id为：{}", roleId);
        throw new ApiException("角色信息修改失败,id不存在或数据未找到");
    }

    /**
     * 删除saas后台角色信息
     *
     * @param roleId saas后台角色主键
     * @return 结果
     */
    @Override
    public int deleteSaasRoleByRoleId(Long roleId) {
        return saasRoleMapper.deleteSaasRoleByRoleId(roleId);
    }
}
