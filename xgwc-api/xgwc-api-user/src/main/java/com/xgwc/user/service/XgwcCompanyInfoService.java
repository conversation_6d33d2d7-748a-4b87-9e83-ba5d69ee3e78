package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.XgwcCompanyInfoDto;
import com.xgwc.user.entity.vo.XgwcCompanyInfoVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.service
 * @Author: kouwenzhuo
 * @CreateTime: 2025-07-04  09:48
 */
public interface XgwcCompanyInfoService {

    /**
     * 获取公司信息列表
     * @param xgwcCompanyInfoVo 查询参数
     * @return 列表
     */
    List<XgwcCompanyInfoDto> getCompanyInfoList(XgwcCompanyInfoVo xgwcCompanyInfoVo);

    /**
     * 新增公司信息
     * @param xgwcCompanyInfoVo 公司信息
     * @return 结果
     */
    ApiResult saveXgwcCompanyInfo(XgwcCompanyInfoVo xgwcCompanyInfoVo);

    /**
     * 根据id查询公司信息
     * @param companyId 公司id
     * @return 公司信息
     */
    ApiResult getXgwcCompanyById(Long companyId);

    /**
     * 修改公司信息
     * @param xgwcCompanyInfoVo 公司信息
     * @return 结果
     */
    ApiResult updateXgwcCompanyById(XgwcCompanyInfoVo xgwcCompanyInfoVo);

    /**
     * 根据id修改状态
     * @param companyId 公司id
     * @param status 状态
     * @return 结果
     */
    ApiResult updateStatusById(Long companyId, Integer status);
}
