package com.xgwc.user.util;

import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyRequest;
import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyResponse;
import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.xgwc.user.config.AliyunFaceVerifyConfig;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class DescribeFaceVerifyUtil {

    @Resource
    private AliyunFaceVerifyConfig aliyunFaceVerifyConfig;
    // 使用单例模式优化性能
    @Resource
    private com.aliyun.credentials.Client faceVerifyClient;

    /**
     * 获取验证结果
     * @param certifyId 标识
     * @return 结果
     */
    public DescribeFaceVerifyResponseBody geVerifyResult(String certifyId) {

        // 创建API请求并设置参数。
        DescribeFaceVerifyRequest request = new DescribeFaceVerifyRequest();
        // 场景ID+L。
        request.setSceneId(aliyunFaceVerifyConfig.getSceneId());
        // CertifyId在InitFaceVerify接口的返回值中。
        request.setCertifyId(certifyId);
        DescribeFaceVerifyResponse response = describeFaceVerifyAutoRoute(request);
        if(response != null) {
            DescribeFaceVerifyResponseBody responseBody = response.getBody();
            if(responseBody != null) {
                return responseBody;
            }
        }
        return null;
    }

    private DescribeFaceVerifyResponse describeFaceVerifyAutoRoute(DescribeFaceVerifyRequest request) {
        // 第一个为主区域Endpoint，第二个为备区域Endpoint。
        List<String> endpoints = Arrays.asList("cloudauth.cn-shanghai.aliyuncs.com", "cloudauth.cn-beijing.aliyuncs.com");
        DescribeFaceVerifyResponse lastResponse = null;
        for (int i = 0; i < endpoints.size(); i++) {
            try {
                DescribeFaceVerifyResponse response = describeFaceVerify(endpoints.get(i), request);
                lastResponse = response;

                // 服务端错误，切换到下个区域调用。
                if (response != null) {
                    if (500 == response.getStatusCode()) {
                        continue;
                    }
                    if (response.getBody() != null) {
                        if ("500".equals(response.getBody().getCode())) {
                            continue;
                        }
                    }
                }

                return lastResponse;
            } catch (Exception e) {
                if (i == endpoints.size() - 1) {
                    throw new RuntimeException(e);
                }
            }
        }

        return lastResponse;
    }

    private DescribeFaceVerifyResponse describeFaceVerify(String endpoint, DescribeFaceVerifyRequest request)
            throws Exception {
        Config config = new Config();
        config.setCredential(faceVerifyClient);
        config.setEndpoint(endpoint);
        Client client = new Client(config);
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 10000;
        runtime.connectTimeout = 10000;
        return client.describeFaceVerifyWithOptions(request, runtime);
    }
}
