package com.xgwc.user.entity.dto;

import com.xgwc.user.entity.vo.ApplyInfoVO;
import lombok.Data;

import java.util.List;

@Data
public class LoginBusinessResult {

    private Long businessId;

    private Integer businessStatus;

    private Integer userType;

    private Integer loginChannels;

    private String brandName;

    private List<ApplyInfoVO> applyInfoList;

    private boolean hasAccount;

    private Long oldStaffId;

    private String managerPhone;

    private Long franchiseId;

    private String franchiseName;

    private String managerName;

    private String stageName;

}
