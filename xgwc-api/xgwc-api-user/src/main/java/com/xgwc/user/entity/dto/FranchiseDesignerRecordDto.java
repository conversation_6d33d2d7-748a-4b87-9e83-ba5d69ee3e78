package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class FranchiseDesignerRecordDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("所属品牌id")
    @Excel(name = "所属品牌id")
    private Long brandId;

    @FieldDesc("业务id")
    @Excel(name = "业务id")
    private Long businessId;

    @FieldDesc("业务类型：1:加盟商记录，2:设计师记录")
    @Excel(name = "业务类型：1:加盟商记录，2:设计师记录")
    private Long businessType;

    @FieldDesc("申请状态")
    @Excel(name = "申请状态")
    private Long checkStatus;

    @FieldDesc("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkTime;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("主键id")
    @Excel(name = "主键id")
    private Long id;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("审核原因")
    @Excel(name = "审核原因")
    private String reason;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("brandId",getBrandId())
            .append("businessId",getBusinessId())
            .append("businessType",getBusinessType())
            .append("checkStatus",getCheckStatus())
            .append("checkTime",getCheckTime())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("id",getId())
            .append("modifyTime",getModifyTime())
            .append("reason",getReason())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
    }
}
