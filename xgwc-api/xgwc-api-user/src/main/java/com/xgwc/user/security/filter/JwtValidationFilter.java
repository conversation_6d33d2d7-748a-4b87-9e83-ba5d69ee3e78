package com.xgwc.user.security.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.security.mnodel.SysUserDetails;
import com.xgwc.common.util.RequestContextHolder;
import com.xgwc.common.util.SpringUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.redis.constants.Expire;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.user.security.constants.SecurityConstants;
import jakarta.annotation.Nonnull;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;


import java.io.IOException;

/**
 * JWT登录授权过滤器
 */
@Slf4j
public class JwtValidationFilter extends OncePerRequestFilter {

    private final UserDetailsService userDetailsService;

    // 密钥
    private final byte[] secretKey;

    public JwtValidationFilter(UserDetailsService userDetailsService, String secretKey) {
        this.userDetailsService = userDetailsService;
        this.secretKey = secretKey.getBytes();
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, @Nonnull HttpServletResponse response, @Nonnull FilterChain chain) throws ServletException, IOException {
        // 获取请求token
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        try {
            // 如果请求头中没有Authorization信息，或者Authorization以Bearer开头，则认为是匿名用户
            if (StrUtil.isBlank(token) || !token.startsWith(SecurityConstants.JWT_TOKEN_PREFIX)) {
                chain.doFilter(request, response);
                return;
            }
            // 去除 Bearer 前缀
            token = token.substring(SecurityConstants.JWT_TOKEN_PREFIX.length());
            // 通过token获取userid
            JWT jwt = JWTUtil.parseToken(token);
            cn.hutool.json.JSONObject payloads = jwt.getPayloads();
            String userId = payloads.getStr(JWTPayload.SUBJECT);
            if(StringUtils.isEmpty(userId) && !request.getRequestURI().contains("/login/switchRole")) {
                log.warn("登录之后未切换角色,userId:{}, token:{}", userId, token);
                responseNotSwitchRole(response);
                return;
            }
            RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
            String rtoken = redisUtil.get("user:login:" + userId + ":token");
//            if (StringUtils.isBlank(rtoken) || !rtoken.equals(token)) {
//                log.warn("JwtValidationFilter error: token is invalid,token:{}", token);
//                responseNoPermission(response);
//                return;
//            }
            // 检查 Token 是否有效(验签 + 是否过期)
            boolean isValidate = jwt.setKey(secretKey).validate(0);
            if(!isValidate) {
                log.warn("JwtValidationFilter error: token is invalid,token:{}", token);
                responseNoPermission(response);
                return;
            }

            SysUserDetails userDetails = (SysUserDetails) this.userDetailsService.loadUserByUsername(userId);
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authentication);
            RequestContextHolder.setContext(JSONObject.toJSONString(userDetails.getSysUser()));
        } catch (Exception e) {
            log.error("JwtValidationFilter error: {}", e.getMessage());
            SecurityContextHolder.clearContext();
            throw new ApiException(ApiStatusEnums.NO_PERMISSION.getMessage() ,ApiStatusEnums.NO_PERMISSION.getStatus());
        }
        // Token有效或无Token时继续执行过滤链
        chain.doFilter(request, response);
    }

    private void responseNoPermission(HttpServletResponse response) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpStatus.OK.value());
        ApiResult result = new ApiResult(ApiStatusEnums.NO_PERMISSION.getStatus(), ApiStatusEnums.NO_PERMISSION.getMessage());;
        response.getWriter().write(new ObjectMapper().writeValueAsString(result));
        response.getWriter().flush();
    }

    private void responseNotSwitchRole(HttpServletResponse response) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpStatus.OK.value());
        ApiResult result = new ApiResult(ApiStatusEnums.NOT_SWITCH_ROLE.getStatus(), ApiStatusEnums.NOT_SWITCH_ROLE.getMessage());;
        response.getWriter().write(new ObjectMapper().writeValueAsString(result));
        response.getWriter().flush();
    }

}
