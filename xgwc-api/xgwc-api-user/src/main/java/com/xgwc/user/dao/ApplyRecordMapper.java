package com.xgwc.user.dao;

import java.util.List;
import com.xgwc.user.entity.ApplyRecord;
import com.xgwc.user.entity.dto.ApplyRecordDto;
import com.xgwc.user.entity.vo.ApplyRecordQueryVo;
import org.apache.ibatis.annotations.Param;


public interface ApplyRecordMapper  {
    /**
     * 查询申请记录
     * 
     * @param id 申请记录主键
     * @return 申请记录
     */
    public ApplyRecordDto selectApplyRecordById(Long id);

    /**
     * 查询申请记录列表
     * 
     * @param applyRecord 申请记录
     * @return 申请记录集合
     */
    public List<ApplyRecordDto> selectApplyRecordList(ApplyRecordQueryVo applyRecord);

    /**
     * 新增申请记录
     * 
     * @param applyRecord 申请记录
     * @return 结果
     */
    public int insertApplyRecord(ApplyRecord applyRecord);

    /**
     * 修改申请记录
     * 
     * @param applyRecord 申请记录
     * @return 结果
     */
    public int updateApplyRecord(ApplyRecord applyRecord);

    /**
     * 删除申请记录
     * 
     * @param id 申请记录主键
     * @return 结果
     */
    public int deleteApplyRecordById(Long id);

    /**
     * 批量删除申请记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApplyRecordByIds(Long[] ids);

    ApplyRecordDto findApplyRecordByUserId(@Param("userId") Long userId,@Param("brandId") Long brandId, @Param("businessId") Long businessId);

    int updateApplyRecordStatusById(@Param("id") Long id,@Param("status") Integer status);

    List<ApplyRecordDto> findApplyRecordListByUserIdAndBusinessType( @Param("userId") Long userId,@Param("businessType") Integer businessType);
}
