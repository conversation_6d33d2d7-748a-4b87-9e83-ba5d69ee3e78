package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class SysRole {

private static final long serialVersionUID=1L;

    /** 角色ID */
    private Long id;

    /** 角色名称 */
    private String name;

    /** 角色权限字符串 */
    private String code;

    /** 显示顺序 */
    private Long sort;

    /** 角色状态（0正常 1停用） */
    private Integer status;

    /** 角色类型 */
    private Long type;

    /** 备注 */
    private String remark;

    /** 是否删除 */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;



}