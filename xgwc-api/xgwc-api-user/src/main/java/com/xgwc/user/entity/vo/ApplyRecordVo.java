package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class ApplyRecordVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("所属品牌id")
    private Long brandId;

    @FieldDesc("业务id")
    private Long businessId;

    @FieldDesc("业务类型：1:加盟商记录，2:设计师记录")
    private Integer businessType;

    @FieldDesc("申请状态")
    private Integer checkStatus;

    @FieldDesc("主键id")
    private Long id;

    @FieldDesc("申请用户id")
    private Long userId;



}
