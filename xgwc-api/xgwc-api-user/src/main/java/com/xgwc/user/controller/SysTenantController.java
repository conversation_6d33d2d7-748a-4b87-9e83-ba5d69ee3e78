package com.xgwc.user.controller;

import java.util.List;

import com.xgwc.common.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.vo.SysTenantVo;
import com.xgwc.user.entity.dto.SysTenantDto;
import com.xgwc.user.entity.vo.SysTenantQueryVo;
import com.xgwc.user.service.ISysTenantService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/tenant/sysTenant")
public class SysTenantController extends BaseController
{
    @Autowired
    private ISysTenantService sysTenantService;

    /**
     * 查询租户管理列表
     */
    @MethodDesc("查询租户管理列表")
    @PreAuthorize("@ss.hasPermission('tenant:sysTenant:list')")
    @GetMapping("/list")
    public ApiResult<SysTenantDto> list(SysTenantQueryVo sysTenant) {
        startPage();
        List<SysTenantDto> list = sysTenantService.selectSysTenantList(sysTenant);
        return getDataTable(list);
    }


    /**
     * 导出租户管理列表
     */
    @MethodDesc("导出租户管理列表")
    @PreAuthorize("@ss.hasPermission('tenant:sysTenant:export')")
    @Log(title = "租户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysTenantQueryVo sysTenant)
    {
    }

    /**
     * 获取租户管理详细信息
     */
    @MethodDesc("获取租户管理详细信息")
    @PreAuthorize("@ss.hasPermission('tenant:sysTenant:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<SysTenantDto> getInfo(@PathVariable("id") Long id)
    {
        return success(sysTenantService.selectSysTenantById(id));
    }

    /**
     * 新增租户管理
     */
    @MethodDesc("新增租户管理")
    @PreAuthorize("@ss.hasPermission('tenant:sysTenant:add')")
    @Log(title = "租户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody SysTenantVo sysTenant)
    {
        return toAjax(sysTenantService.insertSysTenant(sysTenant));
    }

    /**
     * 修改租户管理
     */
    @MethodDesc("修改租户管理")
    @PreAuthorize("@ss.hasPermission('tenant:sysTenant:edit')")
    @Log(title = "租户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody SysTenantVo sysTenant)
    {
        return toAjax(sysTenantService.updateSysTenant(sysTenant));
    }

    /**
     * 删除租户管理
     */
    @MethodDesc("删除租户管理")
    @PreAuthorize("@ss.hasPermission('tenant:sysTenant:remove')")
    @Log(title = "租户管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysTenantService.deleteSysTenantByIds(ids));
    }
}
