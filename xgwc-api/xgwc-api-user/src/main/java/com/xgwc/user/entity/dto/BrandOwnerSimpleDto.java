package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class BrandOwnerSimpleDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌ID")
    @Excel(name = "品牌ID")
    private Long brandId;

    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String companyName;
}
