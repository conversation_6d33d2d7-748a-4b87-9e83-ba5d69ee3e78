package com.xgwc.user.dao;

import com.xgwc.user.entity.FaceVerityRecord;
import com.xgwc.user.entity.vo.FaceVerifyCheckVo;

/**
 * 人脸验证记录
 */
public interface FaceVerityRecordMapper {

    /**
     * 插入人脸验证记录
     * @param faceVerityRecord 参数
     * @return 插入结果
     */
    int insertFaceVerityRecord(FaceVerityRecord faceVerityRecord);

    /**
     * 修改人脸验证记录
     * @param faceVerityRecord 参数
     * @return 修改结果
     */
    int updateFaceVerityRecord(FaceVerityRecord faceVerityRecord);

    /**
     * 根据唯一标识获取验证结果
     * @param certifyId 标识
     * @return 校验记录
     */
    FaceVerityRecord getFaceVerityRecordByCertifyId(String certifyId);

    /**
     * 根据身份证号查询校验信息
     * @param certNo 身份证号
     */
    FaceVerityRecord getFaceVerityRecordByCertNo(String certNo);

    /**
     * 根据身份证号查询校验信息
     * @param userId 身份证号
     */
    FaceVerityRecord getFaceVerityRecordByCertUserId(Long userId);
}
