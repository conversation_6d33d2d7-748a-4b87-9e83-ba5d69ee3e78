package com.xgwc.user.controller;

import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.entity.ReportTargetFranchise;
import com.xgwc.user.entity.ReportTargetFranchiseDept;
import com.xgwc.user.entity.ReportTargetFranchiseStaff;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.ReportTargetFranchiseStaffVo;
import com.xgwc.user.service.ReportTargetService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RequestMapping("target")
@RestController
public class ReportTargetContoller extends BaseController {

    @Resource
    private ReportTargetService reportTargetService;

    /**
     * 添加加盟商部门目标
     */
    @PostMapping("addReportTargetFranchiseDept")
    public ApiResult addReportTargetFranchiseDept(@RequestBody @Valid ReportTargetFranchise reportTargetFranchise){
        reportTargetService.insertReportTargetFranchise(reportTargetFranchise);
        return ApiResult.ok();
    }

    /**
     * 添加员工目标
     */
    @PostMapping("franchise/addReportTargetStaff")
    public ApiResult addReportTargetStaff(@RequestBody @Valid ReportTargetFranchiseStaffVo reportTargetFranchiseStaffVo){
        reportTargetService.addReportTargetStaff(reportTargetFranchiseStaffVo);
        return ApiResult.ok();
    }

    @PostMapping("updateDeptAmount")
    public ApiResult updateDeptAmount(@RequestBody ReportTargetFranchiseDept reportTargetFranchiseDept){
        if(reportTargetFranchiseDept.getTargetDeptId() != null && reportTargetFranchiseDept.getTargetId() != null) {
            int result = reportTargetService.updateReportTargetFranchiseDept(reportTargetFranchiseDept);
            return result > 0 ? ApiResult.ok(result) : ApiResult.error("");
        }
        return ApiResult.error("参数为空");
    }

    @PostMapping("updateStaffAmount")
    public ApiResult updateStaffAmount(@RequestBody ReportTargetFranchiseStaff reportTargetFranchiseStaff){
        if(reportTargetFranchiseStaff.getId() != null && reportTargetFranchiseStaff.getTargetId() != null && reportTargetFranchiseStaff.getTargetDeptId() != null) {
            int result = reportTargetService.updateReportTargetFranchiseStaff(reportTargetFranchiseStaff);
            return result > 0 ? ApiResult.ok(result) : ApiResult.error("");
        }
        return ApiResult.error("参数为空");
    }

    @RequestMapping("deleteFranchiseTarget")
    public ApiResult deleteFranchiseTarget(Long targetId){
        if(targetId != null && targetId > 0) {
            int result = reportTargetService.deleteReportTargetFranchise(targetId);
            return result > 0 ? ApiResult.ok(result) : ApiResult.error("");
        }
        return ApiResult.error("参数为空");
    }

    /**
     * 获取加盟商部门信息
     */
    @RequestMapping("getFranchiseDepts")
    public ApiResult getFranchiseDepts(String franchiseIds, String targetDate){
        if(StringUtils.isNotEmpty(franchiseIds)) {
            List<Long> longList = Arrays.stream(franchiseIds.split(","))
                    .map(String::trim)
                    .map(Long::parseLong)
                    .toList();
            targetDate = StringUtils.isEmpty(targetDate) ? DateUtils.geCurrentDateStr("yyyy-MM") : targetDate;
            List<ReportTargetFranchiseDeptList> reportTargetFranchiseDeptList = reportTargetService.selectReportTargetFranchiseDepts(longList, targetDate);
            return ApiResult.ok(reportTargetFranchiseDeptList);
        }
        return ApiResult.error("参数缺失");
    }

    /**
     * 获取加盟商部门员工列表
     */
    @RequestMapping("getFranchiseStaffs")
    public ApiResult getFranchiseDepts(Long targetId, Long deptId){
        if(targetId != null && targetId > 0 && deptId != null && deptId > 0) {
            ReportTargetFranchiseStaffList reportTargetFranchiseStaffList = reportTargetService.getReportTargetFranchiseStaffList(targetId, deptId);
            return ApiResult.ok(reportTargetFranchiseStaffList);
        }
        return ApiResult.error("参数为空");
    }

    /**
     * 获取加盟商目标详情
     */
    @RequestMapping("getTargetFranchiseDetail")
    public ApiResult getTargetFranchiseDetail(Long targetId){
        if(targetId != null && targetId > 0) {
            ReportTargetFranchiseDeptDetail reportTargetFranchiseDeptDetail = reportTargetService.getTargetFranchiseDetail(targetId);
            return ApiResult.ok(reportTargetFranchiseDeptDetail);
        }
        return ApiResult.error("参数缺失");
    }

    /**
     * 筛选加盟商目标
     */
    @RequestMapping("selectTargetFranchiseList")
    public ApiResult selectTargetFranchiseList(ReportTargetFranchise reportTargetFranchise){
        startPage();
        List<ReportTargetFranchiseDto> reportTargetFranchiseDtos = reportTargetService.selectTargetFranchiseList(reportTargetFranchise);
        return getDataTable(reportTargetFranchiseDtos);
    }

    /**
     * 筛选品牌商目标
     */
    @RequestMapping("brand/selectTargetFranchiseDeptList")
    public ApiResult selectTargetFranchiseDeptListByBrand(ReportTargetFranchiseDto reportTargetFranchiseDto){
        startPage();
        List<ReportTargetBrandDeptDto> reportTargetFranchiseDtos = reportTargetService.selectTargetFranchiseDeptListByBrand(reportTargetFranchiseDto);
        return getDataTable(reportTargetFranchiseDtos);
    }

    /**
     * 筛选品牌商目标
     */
    @RequestMapping("franchise/selectTargetFranchiseDeptList")
    public ApiResult selectTargetFranchiseDeptListByFranchise(ReportTargetFranchiseDeptDto reportTargetFranchiseDeptDto){
        startPage();
        List<ReportTargetBrandDeptDto> reportTargetFranchiseDtos = reportTargetService.selectTargetFranchiseDeptListByFranchise(reportTargetFranchiseDeptDto);
        return getDataTable(reportTargetFranchiseDtos);
    }

    /**
     * 获取未提交数
     */
    @RequestMapping("countNotSubmit")
    public ApiResult countNotSubmit(){
        Integer count = reportTargetService.countNotSubmit();
        return ApiResult.ok(count == null ? 0 : count);
    }

}
