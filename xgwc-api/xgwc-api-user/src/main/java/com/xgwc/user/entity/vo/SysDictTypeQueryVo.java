package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysDictTypeQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("数据设置方")
    private String dictData;

    @FieldDesc("字典编号")
    private Long dictId;

    @FieldDesc("字典名称")
    private String dictLabel;

    @FieldDesc("字典编码")
    private String dictValue;

    @FieldDesc("字典状态")
    private Integer status;

}
