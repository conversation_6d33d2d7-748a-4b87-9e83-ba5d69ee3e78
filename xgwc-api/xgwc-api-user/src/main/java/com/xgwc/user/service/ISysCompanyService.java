package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.SysCompanyDto;
import com.xgwc.user.entity.vo.SysCompanyInfo;
import com.xgwc.user.entity.vo.SysCompanyQueryVo;
import com.xgwc.user.entity.vo.SysCompanyVo;

import java.util.List;

public interface ISysCompanyService  {
    /**
     * 查询公司管理
     * 
     * @param id 公司管理主键
     * @return 公司管理
     */
    public SysCompanyDto selectSysCompanyById(Long id);

    /**
     * 查询公司管理列表
     *
     * @param sysCompany 公司管理
     * @return 公司管理集合
     */
    public List<SysCompanyInfo> selectSysCompanyList(SysCompanyQueryVo sysCompany);

    /**
     * 新增公司管理
     * 
     * @param sysCompany 公司管理
     * @return 结果
     */
    public int insertSysCompany(SysCompanyVo sysCompany);

    /**
     * 修改公司管理
     * 
     * @param sysCompany 公司管理
     * @return 结果
     */
    public int updateSysCompany(SysCompanyVo sysCompany);

    /**
     * 修改状态
     *
     * @param id     id
     * @param status 状态
     * @return 结果
     */
    public ApiResult updateStatus(Integer id, Integer status);

    /**
     * 查询公司管理列表下拉框
     *
     * @return 公司管理列表下拉框
     */
    List<SysCompanyVo> selectCompanyDropDown();
}
