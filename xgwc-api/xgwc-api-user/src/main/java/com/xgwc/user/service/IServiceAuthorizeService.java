package com.xgwc.user.service;

import com.xgwc.user.entity.dto.ServiceAuthorizeDto;
import com.xgwc.user.entity.vo.ServiceAuthorizeVo;
import com.xgwc.user.feign.entity.BrandOwnerDto;

import java.util.List;

public interface IServiceAuthorizeService {

    /**
     * 查询服务授权
     */
    ServiceAuthorizeDto selectServiceAuthorize();

    /**
     * 新增服务授权
     *
     * @param serviceAuthorize 服务授权
     * @return 结果
     */
    int insertServiceAuthorize(ServiceAuthorizeVo serviceAuthorize);


    List<Long> getServiceBrandIdList(Long serviceId);

    List<BrandOwnerDto> getServiceBrandList();
}