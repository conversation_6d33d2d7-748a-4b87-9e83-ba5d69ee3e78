package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class ServiceAuthorizeDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("服务商id")
    @Excel(name = "服务商id")
    private Long serviceId;

    @FieldDesc("品牌商id")
    @Excel(name = "品牌商id")
    private Long brandId;

    @FieldDesc("状态：0正常，1禁用")
    @Excel(name = "状态：0正常，1禁用")
    private Long status;

}
