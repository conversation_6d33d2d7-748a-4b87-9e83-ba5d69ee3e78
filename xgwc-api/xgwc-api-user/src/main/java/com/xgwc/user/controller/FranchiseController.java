package com.xgwc.user.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.feign.api.FranchiseFeign;
import com.xgwc.user.feign.entity.FranchiseDto;
import com.xgwc.user.service.IFranchiseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 加盟商管理
 */
@RestController
@RequestMapping("/franchise")
@Validated
public class FranchiseController extends BaseController implements FranchiseFeign {

    @Autowired
    private IFranchiseService franchiseService;

    @Override
    @MethodDesc("获取加盟商管理详细信息")
    @GetMapping(value = "/{id}")
    public ApiResult<FranchiseDto> getFranchiseById(@PathVariable("id") Long id) {
        return success(franchiseService.selectFranchiseById(id));
    }


    @PostMapping(value = "/listByIds")
    @Override
    public List<FranchiseDto> listByIds(@RequestBody List<Long> ids) {
        return franchiseService.listByIds(ids);
    }

}
