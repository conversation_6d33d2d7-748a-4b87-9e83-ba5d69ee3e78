package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SysUserStatusVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @FieldDesc("帐号状态（0正常 1停用）")
    @NotNull(message = "状态不能为空")
    private Integer status;



}
