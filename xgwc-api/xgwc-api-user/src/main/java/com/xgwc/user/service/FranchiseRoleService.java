package com.xgwc.user.service;

import com.alibaba.fastjson2.stream.StreamReader;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.FranchiseRoleDto;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.param.FranchiseRoleParam;
import com.xgwc.user.entity.vo.FranchiseRoleVo;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  11:55
 */
public interface FranchiseRoleService {

    /**
     * 获取加盟商角色列表
     * @param franchiseRoleParam 加盟商角色管理参数
     * @return 加盟商角色列表
     */
    List<FranchiseRoleVo> getFranchiseRoleList(FranchiseRoleParam franchiseRoleParam);

    /**
     * 保存加盟商角色
     * @param franchiseRoleDto 加盟商角色参数
     * @return 保存结果
     */
    ApiResult saveFranchiseRole(FranchiseRoleDto franchiseRoleDto);

    /**
     * 获取加盟商角色详情
     * @param roleId 角色ID
     * @return 角色详情
     */
    ApiResult getFranchiseRoleById(Long roleId);

    /**
     * 修改加盟商角色
     * @param franchiseRoleDto 加盟商角色参数
     * @return 修改结果
     */
    ApiResult updateFranchiseRole(FranchiseRoleDto franchiseRoleDto);

    /**
     * 修改加盟商角色状态
     * @param roleId 角色ID
     * @param status 状态
     * @return 修改结果
     */
    ApiResult updateStatusById(Integer roleId, Integer status);

    /**
     * 根据userId查询角色信息
     *
     * @param userId 用户id
     * @return 角色信息
     */
    List<FranchiseRoleVo> selectRoleByUserId(Long userId);

    /**
     * 根据userId查询菜单信息
     *
     * @param userId 用户id
     * @return 菜单信息
     */
    List<SysMenuDto> selectMenuByUserId(Long userId);

    /**
     * 获取角色数据权限下载权限
     *
     * @return 角色数据权限下载权限
     */
    ApiResult selectRoleDownloadLimit();

    /**
     * 查询品牌商设置下载次数列表
     *
     * @return 品牌商设置下载次数列表
     */
    ApiResult selectBrandSetDownload();

    /**
     * 根据userId查询服务角色
     *
     * @param userId 用户id
     * @return 服务权限
     */
    List<FranchiseRoleVo> selectRoleServiceByUserId(Long userId);

    /**
     * 根据userId查询服务角色菜单
     *
     * @param userId 用户id
     * @return 服务菜单
     */
    List<SysMenuDto> selectMenuServiceByUserId(Long userId);

    /**
     * 根据userId查询销售服务商角色
     *
     * @param userId 用户id
     * @return 销售权限
     */
    List<FranchiseRoleVo> selectRoleMarketByUserId(Long userId);

    /**
     * 根据userId查询销售服务商角色菜单
     *
     * @param userId 用户id
     * @return 销售菜单
     */
    List<SysMenuDto> selectMenuMarketByUserId(Long userId);
}
