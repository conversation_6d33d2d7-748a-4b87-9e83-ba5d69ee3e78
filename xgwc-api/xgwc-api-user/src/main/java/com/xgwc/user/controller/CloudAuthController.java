package com.xgwc.user.controller;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.entity.vo.FaceVerifyCheckVo;
import com.xgwc.user.service.FaceVerifyService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("cloudauth")
@RestController
@CrossOrigin
public class CloudAuthController {

    @Resource
    private FaceVerifyService faceVerifyService;

    @RequestMapping("callback")
    public ApiResult callback(FaceVerifyCheckVo faceVerifyCheckVo) {
        log.info("人脸验证回调参数:{}", JSONObject.toJSONString(faceVerifyCheckVo));
        faceVerifyService.callBack(faceVerifyCheckVo);
        return ApiResult.ok();
    }
    /**
     * 初始化验证地址
     */
    @RequestMapping("initFaceVerify")
    public ApiResult initFaceVerify(@RequestBody FaceVerifyCheckVo faceVerifyCheckVo) {
        if(StringUtils.isEmpty(faceVerifyCheckVo.getCertName()) || StringUtils.isEmpty(faceVerifyCheckVo.getCertNo())) {
            return ApiResult.error("身份信息不能为空");
        }
        return faceVerifyService.initFaceVerify(faceVerifyCheckVo);
    }

    /**
     * 初始化验证地址
     */
    @RequestMapping("designer/initFaceVerify")
    public ApiResult initFaceVerifyDesigner(@RequestBody FaceVerifyCheckVo faceVerifyCheckVo) {
        return faceVerifyService.initFaceVerify(faceVerifyCheckVo);
    }

    /**
     * 根据唯一标识获取验证结果
     */
    @RequestMapping("getFaceVerifyResultByCertifyId")
    public ApiResult getFaceVerifyResultByCertifyId(String certifyId){
        if (StringUtils.isEmpty(certifyId)) {
            return ApiResult.error("参数缺失");
        }
        return faceVerifyService.getFaceVerifyResult(certifyId);
    }

    /**
     * 获取登录人验证结果
     */
    @RequestMapping("getFaceVerifyResult")
    public ApiResult getFaceVerifyResult(){
        return faceVerifyService.getFaceVerifyResult();
    }
}
