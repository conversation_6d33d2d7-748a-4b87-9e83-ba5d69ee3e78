package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class StaffQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("其他")
    private String other;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("岗位id")
    private Long postId;

    @FieldDesc("角色权限")
    private String roleIds;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    private Long jobNature;

    @FieldDesc("状态：0在职，1离职")
    private Integer status;

    @FieldDesc("部门负责人：0是，1否")
    private Integer isPrincipal;

    @FieldDesc("助理：0是，1否")
    private Integer isAssistant;

    @FieldDesc("排班：0是，1否")
    private Integer isSchedule;

    @FieldDesc("直属上级")
    private Long superior;

    @FieldDesc("档案状态：0未录，1已录")
    private Integer archiveStatus;

    @FieldDesc("绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("绑定的用户id")
    private Long bindUserId;

    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    private Long companyId;

    private Long brandId;

    @FieldDesc("开始时间")
    private String startTime;

    @FieldDesc("结束时间")
    private String endTime;

    @FieldDesc("姓名")
    private String name;

    @FieldDesc("花名")
    private String stageName;

    @FieldDesc("手机号")
    private String loginPhone;

    @FieldDesc("业务id")
    private Long businessId;

}
