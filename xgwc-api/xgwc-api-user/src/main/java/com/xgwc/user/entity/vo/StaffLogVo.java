package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class StaffLogVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键id")
    private Long id;

    @FieldDesc("业务id")
    private Long staffId;

    @FieldDesc("新值")
    private String newValue;

    @FieldDesc("内容")
    private String remark;

}
