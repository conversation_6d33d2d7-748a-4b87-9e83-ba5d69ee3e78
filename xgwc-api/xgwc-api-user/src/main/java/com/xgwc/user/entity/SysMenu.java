package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;


@Data
public class SysMenu {

    private static final long serialVersionUID=1L;

    /** 菜单id */
    private Long id;
    /** 激活图标 */
    private String activeIcon;

    /** 激活路径 */
    private String activePath;

    /** 固定在标签 */
    private Long affixTab;

    /** 权限名称：英文 */
    private String authCode;

    /** 页面组件 */
    private String component;

    /** 创建用户id */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 在面包屑里面隐藏菜单 */
    private Long hideInBreadcrumb;

    /** 隐藏菜单 */
    private Long hideInMenu;

    /** 在标签栏中隐藏 */
    private Long hideInTab;

    /** 隐藏子菜单 */
    private Long hidechildrenInMenu;

    /** 菜单图片 */
    private String icon;

    /** 是否删除 */
    private Integer isDel;

    /** 是否缓存页面标签 */
    private Long keepAlive;

    /** 行更新时间 */
    private Date modifyTime;

    /** 菜单名称 */
    private String name;

    /** 路由地址 */
    private String path;

    /** 父类id */
    private Long pid;

    /** 备注 */
    private String remark;

    /** 菜单状态（0正常 1停用） */
    private Integer status;

    /** 标题 */
    private String title;

    /** 菜单类型： */
    private String type;

    /** 修改人id */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 排序 */
    private Integer sort;

    private String modelType;


}