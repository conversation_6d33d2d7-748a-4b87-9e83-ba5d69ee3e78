package com.xgwc.user.entity;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

/**
 * 加盟商角色表
 */
@Data
public class FranchiseRole {

private static final long serialVersionUID=1L;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 数据范围（1：全部数据 2：本部门数据 3：本人数据） */
    private Long dataScope;

    /** 加盟商id */
    private Long franchiseId;

    /** 是否删除：0正常，1删除 */
    private Integer isDel;

    /** 行更新时间 */
    private Date modifyTime;

    /** 角色id */
    private Long roleId;

    /** 角色名称 */
    private String roleName;

    @FieldDesc("标识字段")
    private String isFlag;

    /** 排序：越小越前 */
    private Long sort;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;



}