package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.FranchiseRoleDto;
import com.xgwc.user.entity.param.FranchiseRoleParam;
import com.xgwc.user.entity.vo.FranchiseRoleVo;
import com.xgwc.user.service.FranchiseRoleService;
import com.xgwc.user.service.XgwcBrandRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: k<PERSON><PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 加盟商角色管理
 */
@RestController
@RequestMapping("/franchiseRole")
@Slf4j
public class FranchiseRoleController extends BaseController {

    @Resource
    private FranchiseRoleService franchiseRoleService;

    @Resource
    private XgwcBrandRoleService xgwcBrandRoleService;

    /**
     * @param franchiseRoleParam 查询条件
     * @return 角色管理列表
     * 查询角色管理列表
     */
    @MethodDesc("查询角色管理列表")
    @PreAuthorize("@ss.hasPermission('franchiseRole:franchiseRole:list')")
    @PostMapping("/getFranchiseRoleList")
    public ApiResult<FranchiseRoleVo> getFranchiseRoleList(@RequestBody FranchiseRoleParam franchiseRoleParam) {
        startPage();
        return getDataTable(franchiseRoleService.getFranchiseRoleList(franchiseRoleParam));
    }

    /**
     * 获取加盟商角色管理下拉框
     *
     * @return 角色管理下拉框
     */
    @MethodDesc("获取加盟商角色管理下拉框")
    @GetMapping("/getFranchiseRoleDropDown")
    public ApiResult getFranchiseRoleDropDown() {
        try {
            FranchiseRoleParam franchiseRoleParam = new FranchiseRoleParam();
            franchiseRoleParam.setStatus(0);
            List<FranchiseRoleVo> result = franchiseRoleService.getFranchiseRoleList(franchiseRoleParam);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取加盟商角色管理下拉框失败", e);
            return ApiResult.error("获取加盟商角色管理下拉框失败");
        }
    }

    /**
     * @param franchiseRoleDto 新增角色管理信息
     * @return 插入结果
     * 新增角色管理信息
     */
    @MethodDesc("新增角色管理信息")
    @PreAuthorize("@ss.hasPermission('franchiseRole:franchiseRole:add')")
    @Submit(fileds = "userId")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveFranchiseRole")
    public ApiResult saveFranchiseRole(@RequestBody FranchiseRoleDto franchiseRoleDto) {
        return franchiseRoleService.saveFranchiseRole(franchiseRoleDto);
    }

    /**
     * @param roleId 角色管理id
     * @return 角色管理信息
     * 根据id查询角色管理信息
     */
    @MethodDesc("根据id查询角色管理信息")
    //@PreAuthorize("@ss.hasPermission('franchiseRole:franchiseRole:query')")
    @GetMapping("/getFranchiseRoleById/{roleId}")
    public ApiResult getFranchiseRoleById(@PathVariable Long roleId) {
        return franchiseRoleService.getFranchiseRoleById(roleId);
    }

    /**
     * @param franchiseRoleDto 修改信息
     * @return 修改结果
     * 修改角色管理信息
     */
    @MethodDesc("修改角色管理信息")
    @PreAuthorize("@ss.hasPermission('franchiseRole:franchiseRole:edit')")
    @Submit(fileds = "userId")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateFranchiseRole")
    public ApiResult updateFranchiseRole(@RequestBody FranchiseRoleDto franchiseRoleDto) {
        return franchiseRoleService.updateFranchiseRole(franchiseRoleDto);
    }

    /**
     * @param roleId 角色id
     * @return 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('franchiseRole:franchiseRole:editStatusById')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "roleId") Integer roleId,
                                      @RequestParam(value = "status") Integer status) {
        return franchiseRoleService.updateStatusById(roleId, status);
    }

    /**
     * 查询级联菜单权限
     *
     * @param isFlag 菜单标识 品牌商 "brandAdmin"
     *                       加盟商 "franchiseeAdmin"
     *                       服务商 "serviceAdmin"
     * @return 菜单权限
     */
    @MethodDesc("级联菜单权限查询")
    @GetMapping("/getMenuTreeData")
    public ApiResult getMenuTreeData(@RequestParam(value = "isFlag") String isFlag) {
        return xgwcBrandRoleService.getMenuTreeData(isFlag);
    }

    /**
     * 获取角色数据权限
     *
     * @return 角色权限
     */
    @MethodDesc("角色数据权限查询")
    @GetMapping("/getRoleDataScope")
    public ApiResult getRoleDataScope(){
        return xgwcBrandRoleService.getRoleDataScope();
    }

    /**
     * 查询品牌商设置下载次数列表
     *
     * @return 品牌商设置下载次数列表
     */
    @MethodDesc("根据userId查询品牌商设置下载次数列表")
    @GetMapping("/selectBrandSetDownload")
    public ApiResult selectBrandSetDownload() {
        return franchiseRoleService.selectBrandSetDownload();
    }

    /**
     * 根据userId查询角色信息
     *
     * @param userId 用户id
     * @return 角色信息
     */
    @MethodDesc("根据userId查询角色信息")
    @GetMapping("/selectRoleByUserId")
    public ApiResult selectRoleByUserId(@RequestParam(value = "userId") Long userId) {
        return ApiResult.ok(franchiseRoleService.selectRoleByUserId(userId));
    }

    /**
     * 根据userId查询菜单信息
     *
     * @param userId 用户id
     * @return 菜单信息
     */
    @MethodDesc("根据userId查询菜单信息")
    @GetMapping("/selectMenuByUserId")
    public ApiResult selectMenuByUserId(@RequestParam(value = "userId") Long userId) {
        return ApiResult.ok(franchiseRoleService.selectMenuByUserId(userId));
    }

    /**
     * 获取角色数据权限下载（今日案例下载概览）
     *
     * @return 菜单信息
     */
    @MethodDesc("获取角色数据权限下载")
    @GetMapping("/selectRoleDownloadLimit")
    public ApiResult selectRoleDownloadLimit() {
        return franchiseRoleService.selectRoleDownloadLimit();
    }

}
