package com.xgwc.user.entity;

import lombok.Data;

@Data
public class FaceVerityRecord {

    /**
     * 主键
     */
    private Long id;

    /**
     * 接口参数
     */
    private String params;

    /**
     * 返回地址
     */
    private String returnUrl;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 验证名称
     */
    private String certName;

    /**
     * 身份号码
     */
    private String certNo;

    /**
     * 实人认证唯一标识。
     */
    private String certifyId;

    /**
     * 实人认证url
     */
    private String certifyUrl;

    /**
     * 结果是否通过
     */
    private String verityPass;

    /**
     * 验证结果存储
     */
    private String verityResult;

    /**
     * 初始化结果
     */
    private String initResult;

    /**
     * 创建时间
     */
    private String createTime;

}
