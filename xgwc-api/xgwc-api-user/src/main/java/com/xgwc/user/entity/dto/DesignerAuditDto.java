package com.xgwc.user.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class DesignerAuditDto {

    @FieldDesc("主键")
    @NotNull(message = "ID不能为空")
    private Long id;

    @FieldDesc("审核状态")
    @NotNull(message = "审核状态不能为空")
    private Integer checkStatus;

    @FieldDesc("原因")
    private String reason;
}
