package com.xgwc.user.dao;

import java.util.List;
import com.xgwc.user.entity.StaffLog;
import com.xgwc.user.entity.dto.StaffLogDto;
import com.xgwc.user.entity.vo.StaffLogQueryVo;


public interface StaffLogMapper  {
    /**
     * 查询加盟商员工日志
     * 
     * @param id 加盟商员工日志主键
     * @return 加盟商员工日志
     */
    public StaffLogDto selectStaffLogById(Long id);

    /**
     * 查询加盟商员工日志列表
     * 
     * @param staffLog 加盟商员工日志
     * @return 加盟商员工日志集合
     */
    public List<StaffLogDto> selectStaffLogList(StaffLogQueryVo staffLog);

    /**
     * 新增加盟商员工日志
     * 
     * @param staffLog 加盟商员工日志
     * @return 结果
     */
    public int insertStaffLog(StaffLog staffLog);

    /**
     * 修改加盟商员工日志
     * 
     * @param staffLog 加盟商员工日志
     * @return 结果
     */
    public int updateStaffLog(StaffLog staffLog);

    /**
     * 删除加盟商员工日志
     * 
     * @param id 加盟商员工日志主键
     * @return 结果
     */
    public int deleteStaffLogById(Long id);

    /**
     * 批量删除加盟商员工日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaffLogByIds(Long[] ids);

    /**
     * 根据员工ID查询日志列表
     * @param staffId 员工ID
     * @param businessType 业务类型（1:品牌商；2:加盟商）
     * @return 日志列表
     */
    List<StaffLogDto> selectLogsByStaffIdAndBusinessType(Long staffId,int businessType);
}
