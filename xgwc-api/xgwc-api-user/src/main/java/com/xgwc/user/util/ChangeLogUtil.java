package com.xgwc.user.util;

import com.xgwc.common.annotation.FieldLabel;
import com.xgwc.user.entity.StaffLog;
import org.springframework.cglib.beans.BeanMap;

import java.lang.reflect.Field;
import java.util.*;

public class ChangeLogUtil {

    /**
     * 构建聚合字段变更日志（聚合成一条 StaffLog）
     */
    public static StaffLog buildAggregatedLog(
            Object oldObj, Object newObj, Long staffId, String updateBy,
            Date updateTime, String moduleName
    ) {
        BeanMap oldMap = BeanMap.create(oldObj);
        BeanMap newMap = BeanMap.create(newObj);

        Map<String, String> labelMap = getFieldLabels(oldObj.getClass());

        StringBuilder changeContent = new StringBuilder();
        for (Object key : oldMap.keySet()) {
            String field = key.toString();
            if (!labelMap.containsKey(field)) {
                continue; // 只记录加了注解的字段
            }
            Object oldValue = oldMap.get(field);
            Object newValue = newMap.get(field);
            if (!Objects.equals(oldValue, newValue)) {
                String label = labelMap.get(field);
                changeContent.append(label)
                        .append(": ")
                        .append(valueToString(oldValue))
                        .append(" > ")
                        .append(valueToString(newValue))
                        .append("；\n");
            }
        }
        if (changeContent.isEmpty()) {
            return null;
        }
        StaffLog log = new StaffLog();
        log.setStaffId(staffId);
        log.setFieldName(moduleName);
        log.setRemark(changeContent.toString().trim());
        log.setUpdateBy(updateBy);
        log.setUpdateTime(updateTime);

        return log;
    }

    private static Map<String, String> getFieldLabels(Class<?> clazz) {
        Map<String, String> map = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(FieldLabel.class)) {
                FieldLabel label = field.getAnnotation(FieldLabel.class);
                map.put(field.getName(), label.value());
            }
        }
        return map;
    }

    private static String valueToString(Object val) {
        if (val == null) return "空";
        return val.toString();
    }
}
