package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysOperLogQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("日志主键")
    private Long operId;

    @FieldDesc("模块标题")
    private String title;

    @FieldDesc("业务类型（0其它 1新增 2修改 3删除）")
    private Long businessType;

    @FieldDesc("方法名称")
    private String method;

    @FieldDesc("请求方式")
    private String requestMethod;

    @FieldDesc("操作类别（0其它 1后台用户 2手机端用户）")
    private Long operatorType;

    @FieldDesc("操作人员")
    private String operName;

    @FieldDesc("部门名称")
    private String deptName;

    @FieldDesc("请求URL")
    private String operUrl;

    @FieldDesc("主机地址")
    private String operIp;

    @FieldDesc("操作地点")
    private String operLocation;

    @FieldDesc("请求参数")
    private String operParam;

    @FieldDesc("返回参数")
    private String jsonResult;

    @FieldDesc("操作状态（0正常 1异常）")
    private Integer status;

    @FieldDesc("错误消息")
    private String errorMsg;

    @FieldDesc("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  operTime;



}
