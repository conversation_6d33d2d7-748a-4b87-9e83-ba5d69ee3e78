package com.xgwc.user.controller;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.ValidUtil;
import com.xgwc.user.service.SmsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("sms")
@RestController
public class SmsController {

    @Resource
    private SmsService smsService;

    @RequestMapping("login/send_code")
    public ApiResult sendLoginCode(String phoneNum){
        boolean validMobile = validPhoneNumber(phoneNum);
        if(validMobile) {
            return smsService.sendLoginCode(phoneNum);
        }
        return ApiResult.error("手机号校验失败");
    }

    @RequestMapping("register/send_code")
    public ApiResult sendRegisterCode(String phoneNum){
        boolean validMobile = validPhoneNumber(phoneNum);
        if(validMobile) {
            return smsService.sendRegisterCode(phoneNum);
        }
        return ApiResult.error("手机号校验失败");
    }

    @RequestMapping("login/valid_code")
    public ApiResult validLoginCode(String phoneNum, String code){
        boolean success = smsService.validLoginCode(phoneNum, code);
        if(success){
            return ApiResult.ok("校验成功");
        }else {
            return ApiResult.error("校验失败");
        }
    }

    @RequestMapping("register/valid_code")
    public ApiResult validRegisterCode(String phoneNum, String code){
        boolean success = smsService.validRegisterCode(phoneNum, code);
        if(success){
            return ApiResult.ok("校验成功");
        }else {
            return ApiResult.error("校验失败");
        }
    }



    private boolean validPhoneNumber(String phoneNum){
        if(StringUtils.isEmpty(phoneNum)){
            log.warn("发送验证码,手机号为空");
            return false;
        }
        if(!ValidUtil.isValidPhoneNumber(phoneNum)){
            log.warn("发送验证码,手机号格式不正确:{}", phoneNum);
            return false;
        }
        return true;
    }
}
