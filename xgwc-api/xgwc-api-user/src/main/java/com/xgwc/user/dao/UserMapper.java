package com.xgwc.user.dao;

import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.common.entity.SysUser;
import com.xgwc.user.entity.dto.SysUserDto;
import com.xgwc.user.entity.vo.SysUserQueryVo;

import java.util.List;

public interface UserMapper {

    /**
     * 根据用户名称查用户信息
     * @param userName 用户名称
     * @return 用户信息
     */
    @TenantIgnore
    SysUser getUserInfoByUserName(String userName);

    /**
     * 根据用户id后去用户信息
     * @param userId 用户id
     * @return 用户信息
     */
    SysUser getUserInfoByUserId(Long userId);

    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    @TenantIgnore
    SysUser findUserByPhone(String phone);

    /**
     * 添加用户
     * @param user 用户
     * @return 结果
     */
    @TenantIgnore
    int save(SysUser user);

    /**
     * 查询用户
     *
     * @param userId 用户主键
     * @return 用户
     */
    public SysUserDto selectSysUserByUserId(Long userId);

    /**
     * 查询用户列表
     *
     * @param sysUser 用户
     * @return 用户集合
     */
    public List<SysUserDto> selectSysUserList(SysUserQueryVo sysUser);

    /**
     * 修改用户
     *
     * @param sysUser 用户
     * @return 结果
     */
    @TenantIgnore
    public int updateSysUser(SysUser sysUser);

    /**
     * 删除用户
     *
     * @param userId 用户主键
     * @return 结果
     */
    public int deleteSysUserByUserId(Long userId);

    /**
     * 批量删除用户
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysUserByUserIds(Long[] userIds);

    /**
     * 根据花名称查询用户
     * @param stageName 花名
     * @return 用户信息
     */
    SysUser findUserByStageName(String stageName);
}
