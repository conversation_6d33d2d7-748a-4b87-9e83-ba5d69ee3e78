package com.xgwc.user.service;

import java.util.List;

import com.xgwc.user.entity.dto.SysOperLogDto;
import com.xgwc.user.entity.vo.SysOperLogQueryVo;
import com.xgwc.user.feign.entity.SysOperLog;

public interface ISysOperLogService 
{
    /**
     * 查询操作日志
     * 
     * @param operId 操作日志主键
     * @return 操作日志
     */
    public SysOperLogDto selectSysOperLogByOperId(Long operId);

    /**
     * 查询操作日志列表
     * 
     * @param sysOperLog 操作日志
     * @return 操作日志集合
     */
    public List<SysOperLogDto> selectSysOperLogList(SysOperLogQueryVo sysOperLog);

    /**
     * 新增操作日志
     * 
     * @param sysOperLog 操作日志
     * @return 结果
     */
    public int insertSysOperLog(SysOperLog sysOperLog);


    /**
     * 批量删除操作日志
     * 
     * @param operIds 需要删除的操作日志主键集合
     * @return 结果
     */
    public int deleteSysOperLogByOperIds(Long[] operIds);

    /**
     * 删除操作日志信息
     * 
     * @param operId 操作日志主键
     * @return 结果
     */
    public int deleteSysOperLogByOperId(Long operId);
}
