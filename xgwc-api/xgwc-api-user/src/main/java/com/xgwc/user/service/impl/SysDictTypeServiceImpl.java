package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.SysDictDataMapper;
import com.xgwc.user.dao.SysDictTypeMapper;
import com.xgwc.user.entity.SysDictType;
import com.xgwc.user.entity.dto.SysDictTypeDto;
import com.xgwc.user.entity.vo.SysDictTypeQuery2Vo;
import com.xgwc.user.entity.vo.SysDictTypeQueryVo;
import com.xgwc.user.entity.vo.SysDictTypeVo;
import com.xgwc.user.service.ISysDictTypeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


@Service
public class SysDictTypeServiceImpl implements ISysDictTypeService  {
    @Resource
    private SysDictTypeMapper sysDictTypeMapper;
    @Resource
    private SysDictDataMapper sysDictDataMapper;

    /**
     * 查询字典管理
     * 
     * @param dictId 字典管理主键
     * @return 字典管理
     */
    @Override
    public SysDictTypeDto selectSysDictTypeByDictId(Long dictId) {
        return sysDictTypeMapper.selectSysDictTypeByDictId(dictId);
    }

    /**
     * 查询字典管理列表
     * 
     * @param sysDictType 字典管理
     * @return 字典管理
     */
    @Override
    public List<SysDictTypeDto> selectSysDictTypeList(SysDictTypeQueryVo sysDictType) {
        return sysDictTypeMapper.selectSysDictTypeList(sysDictType);
    }

    @Override
    public List<SysDictTypeDto> selectSysDictTypeList2(SysDictTypeQuery2Vo sysDictType) {
        return sysDictTypeMapper.selectSysDictTypeList2(sysDictType);
    }

    /**
     * 新增字典管理
     * 
     * @param dto 字典管理
     * @return 结果
     */
    @Override
    public int insertSysDictType(SysDictTypeVo dto) {
        checkDictType(dto);
        SysDictType sysDictType = BeanUtil.copyProperties(dto, SysDictType.class);
        sysDictType.setCreateTime(DateUtils.getNowDate());
        sysDictType.setCreateBy(SecurityUtils.getNickName());
        return sysDictTypeMapper.insertSysDictType(sysDictType);
    }

    /**
     * 修改字典管理
     * 
     * @param dto 字典管理
     * @return 结果
     */
    @Override
    public int updateSysDictType(SysDictTypeVo dto) {
        checkDictType(dto);
        SysDictTypeDto dictTypeDto = sysDictTypeMapper.selectSysDictTypeByDictId(dto.getDictId());
        if(!dictTypeDto.getDictValue().equals(dto.getDictValue())) {
            sysDictDataMapper.updateSysDictDataByDictType(dto.getDictValue(), dictTypeDto.getDictValue());
        }
        SysDictType sysDictType = BeanUtil.copyProperties(dto, SysDictType.class);
        sysDictType.setUpdateTime(DateUtils.getNowDate());
        sysDictType.setUpdateBy(SecurityUtils.getNickName());
        return sysDictTypeMapper.updateSysDictType(sysDictType);
    }

    private void checkDictType(SysDictTypeVo vo) {
        SysDictTypeQueryVo queryVo = new SysDictTypeQueryVo();
        queryVo.setDictValue(vo.getDictValue());
        List<SysDictTypeDto> list = selectSysDictTypeList(queryVo);
        if(vo.getDictId() != null && list != null && !list.isEmpty())
            list = list.stream().filter(l -> !l.getDictId().equals(vo.getDictId())).collect(Collectors.toList());
        if (list != null && !list.isEmpty()) {
            throw new ApiException("字典编号已存在");
        }
    }

    @Override
    public int updateStatus(Long[] ids, Integer status) {
        return sysDictTypeMapper.updateStatus(ids, status);
    }

    /**
     * 批量删除字典管理
     * 
     * @param dictIds 需要删除的字典管理主键
     * @return 结果
     */
    @Override
    public int deleteSysDictTypeByDictIds(Long[] dictIds) {
        return sysDictTypeMapper.deleteSysDictTypeByDictIds(dictIds);
    }

    /**
     * 删除字典管理信息
     * 
     * @param dictId 字典管理主键
     * @return 结果
     */
    @Override
    public int deleteSysDictTypeByDictId(Long dictId) {
        return sysDictTypeMapper.deleteSysDictTypeByDictId(dictId);
    }
}
