package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysCompanyQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("自增id")
    private Long id;

    @FieldDesc("上级id")
    private Long pid;

    @FieldDesc("品牌商id")
    private Long brandOwnerId;

    @FieldDesc("公司名称")
    private String name;

    @FieldDesc("公司类型 0-母公司 1-子公司")
    private Integer companyType;

    @FieldDesc("排序")
    private Long orderNum;

    @FieldDesc("状态：0正常，1无效")
    private Integer status;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;

    private Integer isFlag;



}
