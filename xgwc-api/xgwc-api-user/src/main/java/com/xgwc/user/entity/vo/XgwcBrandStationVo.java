package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: kouwen<PERSON>hu<PERSON>
 * @CreateTime: 2025-04-22  15:59
 */

/**
 * 岗位管理
 */
@Data
public class XgwcBrandStationVo {

    /**
     * 岗位id
     */
    private Long stationId;

    /**
     * 岗位名称
     */
    private String stationName;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 品牌商id
     */
    private Long brandOwnerId;

    /**
     * 部门目录
     */
    private String deptPath;

    /**
     * 排序：越小越前
     */
    private Integer sort;

    /**
     * 状态：0正常，1禁用
     */
    private Integer status;

    /**
     * 是否删除：0正常，1删除
     */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
