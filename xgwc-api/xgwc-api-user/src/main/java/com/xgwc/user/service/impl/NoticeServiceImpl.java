package com.xgwc.user.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.SourceEnums;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtil;
import com.xgwc.redis.constants.LockCacheKey;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.user.dao.BrandOwnerMapper;
import com.xgwc.user.dao.DesignerMapper;
import com.xgwc.user.dao.FranchiseNoticeMapper;
import com.xgwc.user.dao.FranchiseStaffMapper;
import com.xgwc.user.dao.NoticeMapper;
import com.xgwc.user.dao.StaffMapper;
import com.xgwc.user.dao.SysMessageMapper;
import com.xgwc.user.entity.SysMessage;
import com.xgwc.user.entity.dto.BrandOwnerDto;
import com.xgwc.user.entity.dto.notice.BrandNoticeClassifyCountDto;
import com.xgwc.user.entity.dto.notice.NoticeClassfyDto;
import com.xgwc.user.entity.dto.notice.NoticeDto;
import com.xgwc.user.entity.dto.notice.SelfNoticeDto;
import com.xgwc.user.entity.dto.notice.SimpleBrandDto;
import com.xgwc.user.entity.vo.NoticeVo;
import com.xgwc.user.entity.vo.SelectNoticeVo;
import com.xgwc.user.service.NoticeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NoticeServiceImpl implements NoticeService {

    @Resource
    private NoticeMapper noticeMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private FranchiseNoticeMapper franchiseNoticeMapper;
    
    @Resource
    private FranchiseStaffMapper franchiseStaffMapper;

    @Resource
    private StaffMapper staffMapper;

    @Resource
    private DesignerMapper designerMapper;

    @Resource
    private SysMessageMapper sysMessageMapper;

    @Resource
    private BrandOwnerMapper brandOwnerMapper;

    @Resource
    private WebSocketClient webSocketClient;

    private static final String ALL = "all";

    private ExecutorService executorService = Executors.newFixedThreadPool(1);

    /**
     * 已读专用
     */
    private ExecutorService readedExecutorService = Executors.newFixedThreadPool(1);

    @Override
    public int insertNotice(NoticeVo noticeVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long sourceId;
        if(noticeVo.getSource() == SourceEnums.FRANCHISE.getValue()) {
            sourceId = sysUser.getFranchiseId();
            if (sourceId == null) {
                log.error("添加通知失败,员工不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
                return -1;
            }
        }else{
            sourceId = sysUser.getBrandId();
            if (sourceId == null) {
                log.error("添加通知失败,员工不存在品牌商ID:{}", JSONObject.toJSONString(sysUser));
                return -1;
            }
        }
        noticeVo.setSourceId(sourceId);
        noticeVo.setCreateBy(SecurityUtils.getNickName());
        String nowTime = DateUtils.getLongDateStr();
        noticeVo.setCreateTime(nowTime);
        if(StringUtil.isNotEmpty(noticeVo.getScheduleTime())){
            noticeVo.setPublishTime(noticeVo.getScheduleTime());
        }else{
            noticeVo.setPublishTime(nowTime);
        }
        handleParams(noticeVo);
        noticeMapper.insertNotice(noticeVo);
        sendNotice(noticeVo);
        return 1;
    }

    //直接发送通知
    public void sendNotice(NoticeVo noticeVo) {
        if(noticeVo.getNoticeType() == 1) {
            List<Long> userIdList = noticeVo.getUserIds();
            //去空和去重
            userIdList = userIdList.stream().filter(x-> x != null).distinct().collect(Collectors.toList());
            List<Long> finalUserIdList = userIdList;
            executorService.execute(() -> {
                //直接发送通知
                if (StringUtil.isEmpty(noticeVo.getScheduleTime())) {
                    if (!finalUserIdList.isEmpty()) {
                        sendToDb(finalUserIdList, noticeVo.getId());
                    }
                }
            });
        }
        //其他暂时忽略
    }

    /**
     * 分5张表插入
     */
    private void sendToDb(List<Long> userIdList, Integer noticeId) {
        NoticeDto noticeDto = noticeMapper.getNoticeById(noticeId);
        Map<Integer, List<Long>> distributedData = distributeData(userIdList, 5);
        int limit = 1000;
        // 打印分配结果（实际使用时替换为数据库插入操作）
        for (Map.Entry<Integer, List<Long>> entry : distributedData.entrySet()) {
            String tableName = "notice_detail_" + entry.getKey();
            List<Long> userIds = entry.getValue();
            if(userIds.isEmpty()){
                continue;
            }
           if(userIds.size() > limit){
               List<List<Long>> listList = Lists.partition(userIds, limit);
               listList.forEach(list -> {
                   sendWebsocket(list);
                   noticeMapper.batchInsertNoticeDetails(tableName, list, noticeId);
                   batchMessage(list, noticeDto);
               });
           }else{
               try {
                   sendWebsocket(userIds);
                   noticeMapper.batchInsertNoticeDetails(tableName, userIds, noticeId);
                   batchMessage(userIds, noticeDto);
               }catch (Exception e){
                   log.error("批量插入发送通知失败:", e);
               }
           }
        }

    }

    /**
     * 批量插入消息
     */
    @Override
    public void batchMessage(List<Long> userIdList, NoticeDto noticeDto){
        List<SysMessage> sysMessages = new ArrayList<>();
        for(Long userId : userIdList){
            SysMessage sysMessage = new SysMessage();
            sysMessage.setUserId(userId);
            sysMessage.setTypeKey("inner_notice");
            sysMessage.setTypeName("内部通知");
            sysMessage.setTypeId(Long.parseLong(noticeDto.getId() + ""));
            sysMessage.setMessage(noticeDto.getTitle());
            sysMessage.setIsRead(0);
            sysMessages.add(sysMessage);
        }
        sysMessageMapper.batchInsertMessages(sysMessages);
    }

    private void sendWebsocket(List<Long> userIds){
        try {
            log.info("发送websocket通知,发送用户:{}", JSONObject.toJSONString(userIds));
            JSONObject json = new JSONObject();
            json.put("msg", "success");
            json.put("users", userIds);
            webSocketClient.send(json.toJSONString());
        }catch (Exception e){
            log.error("发送websocket失败,失败用户:{}, 失败原因:", userIds, e);
        }
    }

    /**
     * 将数据分配到多个表中
     * @param dataList 原始数据列表
     * @param tableCount 表数量
     * @return 分配后的数据映射表
     */
    public static Map<Integer, List<Long>> distributeData(List<Long> dataList, int tableCount) {
        Map<Integer, List<Long>> result = new HashMap<>();
        // 初始化每个表对应的列表
        for (int i = 0; i < tableCount; i++) {
            result.put(i, new ArrayList<>());
        }
        // 分配数据
        for (Long data : dataList) {
            // 使用哈希取模确定表编号
            int tableIndex = (int) (data % tableCount);
            // 处理可能的负数
            tableIndex = tableIndex < 0 ? tableIndex + tableCount : tableIndex;
            result.get(tableIndex).add(data);
        }
        return result;
    }
    /**
     * 获取通知人员
     */
    private List<Long> getNoticeUserIdList(NoticeVo noticeVo){
        if(noticeVo.getSource() == SourceEnums.FRANCHISE.getValue()){
            return getFranchiseNoticeUserIdList(noticeVo, JSONObject.parseObject(noticeVo.getSendGroup()));
        }else{
            return getBranchNoticeUserIdList(noticeVo);
        }
    }

    /**
     * 获取加盟商关联员工
     */
    private List<Long> getFranchiseNoticeUserIdList(NoticeVo noticeVo, JSONObject groupJson) {
        List<Long> deptIdList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        //如果选择的是整个加盟商
        if(groupJson.getBoolean(ALL)){
            userIdList = franchiseStaffMapper.getStaffUserIdListByFranchise(noticeVo.getSourceId());
        }else {
            JSONArray jsonArray = groupJson.getJSONArray("staffList");
            for (Object obj : jsonArray) {
                JSONObject jsonObject = (JSONObject) obj;
                String userIds = (String) jsonObject.get("userIds");
                if (StringUtil.isNotEmpty(userIds)) {
                    userIdList.addAll(StringUtil.duplicateRemovalToLongList(userIds));
                } else {
                    deptIdList.add(jsonObject.getLongValue("deptId"));
                }
            }
            //step2 根据部门获取部门所有人
            if (!deptIdList.isEmpty()) {
                List<Long> userIds = franchiseStaffMapper.getStaffUserIdListByDeptIds(deptIdList, noticeVo.getSourceId());
                if (userIds != null && !userIds.isEmpty()) {
                    userIdList.addAll(userIds);
                }
            }
            //去重
            userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        }
        return userIdList;
    }

    /**
     * 获取加盟商关联员工
     */
    private List<Long> getFranchiseNoticeUserIdListByBrand(NoticeVo noticeVo, JSONObject groupJson) {
        List<Long> deptIdList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        //如果选择的是整个加盟商
        if(groupJson.getBoolean(ALL)){
            userIdList = franchiseStaffMapper.getStaffUserIdListByBrandId(noticeVo.getSourceId());
        }else {
            JSONArray jsonArray = groupJson.getJSONArray("franchiseList");
            for (Object obj : jsonArray) {
                JSONObject jsonObject = (JSONObject) obj;
                if(jsonObject.getBoolean(ALL)){
                    List<Long> users = franchiseStaffMapper.getStaffUserIdListByFranchise(jsonObject.getLong("franchiseId"));
                    if(users != null && !users.isEmpty()){
                        userIdList.addAll(users);
                    }
                }else{
                    JSONArray childJsonArray = jsonObject.getJSONArray("staffList");
                    for (Object obj2 : childJsonArray) {
                        JSONObject jsonObject2 = (JSONObject) obj2;
                        String userIds = (String) jsonObject2.get("userIds");
                        if (StringUtil.isNotEmpty(userIds)) {
                            userIdList.addAll(StringUtil.duplicateRemovalToLongList(userIds));
                        } else {
                            deptIdList.add(jsonObject2.getLongValue("deptId"));
                        }
                    }
                }
            }
            //step2 根据部门获取部门所有人
            if (!deptIdList.isEmpty()) {
                List<Long> userIds = franchiseStaffMapper.getStaffUserIdListByDeptIdsAndBrandId(deptIdList, noticeVo.getSourceId());
                if (userIds != null && !userIds.isEmpty()) {
                    userIdList.addAll(userIds);
                }
            }
            //去重
            userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        }
        return userIdList;
    }

    /**
     * 获取品牌商关联员工
     */
    private List<Long> getBranchNoticeUserIdList(NoticeVo noticeVo) {
        List<Long> userIdList = new ArrayList<>();
        JSONArray groupJsonArray = JSONArray.parseArray(noticeVo.getSendGroup());
        for(Object obj : groupJsonArray){
            JSONObject groupJson = (JSONObject) obj;
            String type = groupJson.getString("type");
            if(type.equals("brand")){
                userIdList.addAll(getUserIdListByBrand(noticeVo, groupJson));
            }else if(type.equals("franchise")){
                userIdList.addAll(getFranchiseNoticeUserIdListByBrand(noticeVo, groupJson));
            }else if(type.equals("designer")){
                userIdList.addAll(getUserIdListByDesigner(noticeVo, groupJson));
            }
        }
        return userIdList;
    }

    /**
     * 提取品牌商员工id
     */
    private List<Long> getUserIdListByBrand(NoticeVo noticeVo, JSONObject groupJson) {
        List<Long> deptIdList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        //如果选择的是整个加盟商
        if(groupJson.getBoolean(ALL)){
            userIdList = staffMapper.getStaffUserIdListByBrand(noticeVo.getSourceId());
        }else {
            JSONArray jsonArray = groupJson.getJSONArray("staffList");
            for (Object obj : jsonArray) {
                JSONObject jsonObject = (JSONObject) obj;
                String userIds = (String) jsonObject.get("userIds");
                if (StringUtil.isNotEmpty(userIds)) {
                    userIdList.addAll(StringUtil.duplicateRemovalToLongList(userIds));
                } else {
                    deptIdList.add(jsonObject.getLongValue("deptId"));
                }
            }
            //step2 根据部门获取部门所有人
            if (!deptIdList.isEmpty()) {
                List<Long> userIds = staffMapper.getStaffUserIdListByDeptIds(deptIdList, noticeVo.getSourceId());
                if (userIds != null && !userIds.isEmpty()) {
                    userIdList.addAll(userIds);
                }
            }
            //去重
            userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        }
        return userIdList;
    }

    /**
     * 提取设计师员工id
     */
    private List<Long> getUserIdListByDesigner(NoticeVo noticeVo, JSONObject groupJson){
        List<Long> deptIdList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        //如果选择的是整个加盟商
        if(groupJson.getBoolean(ALL)){
            userIdList = designerMapper.getStaffUserIdListByBrand(noticeVo.getSourceId());
        }else {
            JSONArray jsonArray = groupJson.getJSONArray("staffList");
            for (Object obj : jsonArray) {
                JSONObject jsonObject = (JSONObject) obj;
                String userIds = (String) jsonObject.get("userIds");
                if (StringUtil.isNotEmpty(userIds)) {
                    userIdList.addAll(StringUtil.duplicateRemovalToLongList(userIds));
                } else {
                    deptIdList.add(jsonObject.getLongValue("deptId"));
                }
            }
            //step2 根据部门获取部门所有人
            if (!deptIdList.isEmpty()) {
                List<Long> userIds = designerMapper.getStaffUserIdListByDeptIds(deptIdList, noticeVo.getSourceId());
                if (userIds != null && !userIds.isEmpty()) {
                    userIdList.addAll(userIds);
                }
            }
            //去重
            userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        }
        return userIdList;
    }

    /**
     * 处理参数
     * @param noticeVo 参数
     */
    private void handleParams(NoticeVo noticeVo){
        //step1 统计附件数
        if(StringUtil.isNotEmpty(noticeVo.getAttachment())){
            noticeVo.setAttachmentCount(noticeVo.getAttachment().split(",").length);
        }
        //step2 发布时间赋值 如果是定时任务则修改发布时间为定时任务时间
        if(StringUtil.isNotEmpty(noticeVo.getScheduleTime())){
            noticeVo.setPublishTime(noticeVo.getScheduleTime());
        }
        //step3 重新统计发布人数
        List<Long> userIdList = getNoticeUserIdList(noticeVo);
        noticeVo.setUserIds(userIdList);
        noticeVo.setSendCount(userIdList.size());
    }

    @Override
    public int updateNotice(NoticeVo noticeVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long sourceId;
        if(noticeVo.getSource() == SourceEnums.FRANCHISE.getValue()) {
            sourceId = sysUser.getFranchiseId();
            if (sourceId == null) {
                log.error("添加通知失败,员工不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
                return -1;
            }
        }else{
            sourceId = sysUser.getBrandId();
            if (sourceId == null) {
                log.error("添加通知失败,员工不存在品牌商ID:{}", JSONObject.toJSONString(sysUser));
                return -1;
            }
        }
        noticeVo.setStatus(null);
        noticeVo.setSourceId(sourceId);
        noticeVo.setUpdateBy(SecurityUtils.getNickName());
        //先获取通知详情
        NoticeDto noticeDto = noticeMapper.getNoticeByCondition(noticeVo);
        if(noticeDto == null){
            log.error("修改通知失败, 通知ID不属于该品牌商或者该通知已被删除, 通知id:{}, 操作人:{}", noticeVo.getId(), JSONObject.toJSONString(sysUser));
            return -1;
        }else{
            noticeVo.setUpdateBy(SecurityUtils.getNickName());
            handleParams(noticeVo);
            //针对立即发送的通知或者已经过了定时任务的通知，只能修改标题，分类，内容，附件
            if(noticeDto.getScheduleTime() == null || DateUtils.isBeforeNow(noticeDto.getScheduleTime())){
                return noticeMapper.updateSendedNotice(noticeVo);
            }else{
                return noticeMapper.updateNotice(noticeVo);
            }
        }
    }

    @Override
    public int deleteNotice(NoticeVo noticeVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long sourceId;
        if(noticeVo.getSource() == SourceEnums.FRANCHISE.getValue()) {
            sourceId = sysUser.getFranchiseId();
            if (sourceId == null) {
                log.error("删除通知失败,员工不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
                return -1;
            }
        }else{
            sourceId = sysUser.getBrandId();
            if (sourceId == null) {
                log.error("删除通知失败,员工不存在品牌商ID:{}", JSONObject.toJSONString(sysUser));
                return -1;
            }
        }
        noticeVo.setSourceId(sourceId);
        noticeVo.setUpdateBy(SecurityUtils.getNickName());
        return noticeMapper.deleteNotice(noticeVo);
    }

    @Override
    public List<NoticeDto> getNoticeList(SelectNoticeVo selectNoticeVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long sourceId;
        if(selectNoticeVo.getSource() == SourceEnums.FRANCHISE.getValue()) {
            sourceId = sysUser.getFranchiseId();
            if (sourceId == null) {
                log.error("获取通知列表失败,员工不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
                return null;
            }
        }else{
            sourceId = sysUser.getBrandId();
            if (sourceId == null) {
                log.error("获取通知列表失败,员工不存在品牌商ID:{}", JSONObject.toJSONString(sysUser));
                return null;
            }
        }
        selectNoticeVo.setSourceId(sourceId);
        List<NoticeDto> noticeDtos = noticeMapper.getNoticeList(selectNoticeVo);
        buildClassifyName(noticeDtos, selectNoticeVo);
        return noticeDtos;
    }

    @Override
    public NoticeDto getNoticeDetail(NoticeVo noticeVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long sourceId;
        if(noticeVo.getSource() == SourceEnums.FRANCHISE.getValue()) {
            sourceId = sysUser.getFranchiseId();
            if (sourceId == null) {
                log.error("加盟商查看通知详情失败:{}", JSONObject.toJSONString(sysUser));
                return null;
            }
        }else{
            sourceId = sysUser.getBrandId();
            if (sourceId == null) {
                log.error("品牌商查看通知详情失败:{}", JSONObject.toJSONString(sysUser));
                return null;
            }
        }
        noticeVo.setSourceId(sourceId);
        NoticeDto noticeDto = noticeMapper.getNoticeByCondition(noticeVo);
        if(noticeDto != null){
            if(noticeVo.getSource() == SourceEnums.BRAND.getValue()){
                noticeDto.setClassifyName(null);
            }
        }
        return noticeDto;
    }

    @Override
    public SelfNoticeDto getSelfNoticeDetail(NoticeVo noticeVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        SelfNoticeDto selfNoticeDto = noticeMapper.getSelfNoticeById(noticeVo.getId(), getTableName(sysUser.getUserId()), sysUser.getUserId());
        //未读消息
        if(selfNoticeDto != null && selfNoticeDto.getIsRead() != null && selfNoticeDto.getIsRead() == 0){
            readedExecutorService.execute(()->{
                read(selfNoticeDto, sysUser.getUserId());
            });
        }
        if(selfNoticeDto != null){
            if(selfNoticeDto.getSource() == SourceEnums.FRANCHISE.getValue()){
                selfNoticeDto.setBrandId(0L);
                selfNoticeDto.setBrandName("本部门");
            }else{
                selfNoticeDto.setClassifyName(null);
                BrandOwnerDto brandOwnerDto = brandOwnerMapper.selectBrandOwnerByBrandId(selfNoticeDto.getSourceId());
                if(brandOwnerDto != null){
                    selfNoticeDto.setBrandId(brandOwnerDto.getBrandId());
                    selfNoticeDto.setBrandName(brandOwnerDto.getCompanyName());
                }
            }
        }
        return selfNoticeDto;
    }

    @Override
    public List<SelfNoticeDto> getBrandSelfNoticeList(String classifyId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("该用户没有品牌id:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return noticeMapper.getBrandSelfNoticeList(getTableName(sysUser.getUserId()), sysUser.getUserId(), classifyId, brandId);
    }

    @Override
    public JSONObject getDesignerSelfNotice(Long brandId, String classifyId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        JSONObject jsonObject = new JSONObject();
        List<BrandNoticeClassifyCountDto> classifyCountDtoList = null;
        List<SelfNoticeDto> selfNoticeDtos = noticeMapper.getBrandSelfNoticeList(getTableName(sysUser.getUserId()), sysUser.getUserId(), classifyId, brandId);
        if(brandId != null && selfNoticeDtos != null && !selfNoticeDtos.isEmpty()){
            classifyCountDtoList = noticeMapper.getBrandNoticeClassifyCountDtoByUserId(getTableName(sysUser.getUserId()), sysUser.getUserId(), brandId);
        }
        long total = 0;
        if(selfNoticeDtos != null && !selfNoticeDtos.isEmpty()){
            total = new PageInfo(selfNoticeDtos).getTotal();
        }
        jsonObject.put("noticeList", selfNoticeDtos);
        jsonObject.put("classifyCountList", classifyCountDtoList);
        jsonObject.put("total", total);
        return jsonObject;
    }

    @Override
    public JSONObject getFranchiseSelfNotice(Long brandId, String classifyId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null){
            log.error("获取加盟商通知失败,用户不存在加盟商ID:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        List<SelfNoticeDto> resultList;
        List<BrandNoticeClassifyCountDto> classifyCountDtoList = null;
        if(brandId != null){
            //查询本部门消息
            if(brandId == 0){
                resultList = noticeMapper.getFranchiseNoticeList(getTableName(sysUser.getUserId()), sysUser.getUserId(), sysUser.getFranchiseId(), classifyId);
                classifyCountDtoList = noticeMapper.getFranchiseNoticeClassifyCountDtoByUserId(getTableName(sysUser.getUserId()), sysUser.getUserId(), franchiseId);
            }else{
                resultList = noticeMapper.getBrandSelfNoticeList(getTableName(sysUser.getUserId()), sysUser.getUserId(), classifyId, brandId);
                classifyCountDtoList = noticeMapper.getBrandNoticeClassifyCountDtoByUserId(getTableName(sysUser.getUserId()), sysUser.getUserId(), brandId);
            }
        }else{
            resultList = noticeMapper.getAllFranchiseNoticeList(getTableName(sysUser.getUserId()), sysUser.getUserId(), franchiseId);
        }
        long total = 0;
        if(resultList != null && !resultList.isEmpty()){
            total = new PageInfo(resultList).getTotal();
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("noticeList", resultList);
        jsonObject.put("classifyCountList", classifyCountDtoList);
        jsonObject.put("total", total);
        return jsonObject;
    }


    /**
     * 构建分类名称
     */
    private void buildClassifyName(List<NoticeDto> noticeDtos, SelectNoticeVo selectNoticeVo){
        //只有为加盟商的时候才执行
        if(noticeDtos != null && !noticeDtos.isEmpty()) {
            if (selectNoticeVo.getSource() == SourceEnums.FRANCHISE.getValue()) {
                List<String> classifyIdList = noticeDtos.stream().map(NoticeDto::getClassifyId).distinct().toList();
                //处理加盟商的分类
                if (selectNoticeVo.getSource() == SourceEnums.FRANCHISE.getValue()) {
                    List<Integer> convertClassifyIdList = classifyIdList.stream().map(Integer::valueOf).toList();
                    List<NoticeClassfyDto> noticeClassfyDtos = franchiseNoticeMapper.getNoticeClassifyListByIds(convertClassifyIdList);
                    Map<Integer, List<NoticeClassfyDto>> noticeClassfyDtoMap = noticeClassfyDtos.stream().collect(Collectors.groupingBy(NoticeClassfyDto::getId));
                    noticeDtos.forEach(noticeClassfyDto -> {
                        List<NoticeClassfyDto> noticeClassfyDtos1 = noticeClassfyDtoMap.get(Integer.parseInt(noticeClassfyDto.getClassifyId()));
                        if (noticeClassfyDtos1 != null) {
                            noticeClassfyDto.setClassifyName(noticeClassfyDtos1.get(0).getClassifyName());
                        }
                        if (DateUtils.isBeforeNow(noticeClassfyDto.getPublishTime())) {
                            //设置为已发布
                            noticeClassfyDto.setPublishStatus(0);
                        } else {
                            //设置为定时任务
                            noticeClassfyDto.setPublishStatus(1);
                        }
                    });
                }
            }else{
                noticeDtos.forEach(noticeClassfyDto -> {
                    if (DateUtils.isBeforeNow(noticeClassfyDto.getPublishTime())) {
                        //设置为已发布
                        noticeClassfyDto.setPublishStatus(0);
                    } else {
                        //设置为定时任务
                        noticeClassfyDto.setPublishStatus(1);
                    }
                });
            }
        }
    }

    @Override
    public int read(Integer noticeId) {
        Long userId = SecurityUtils.getUserId();
        String cacheKey = LockCacheKey.NOTICE_READ + noticeId + "_" + userId;
        //加锁2秒钟
        try {
            String tableName = getTableName(userId);
            if (redisUtil.tryLock(cacheKey, "1", 2000)) {
                Integer readStatus = noticeMapper.getReadStatus(tableName, noticeId, userId);
                if (readStatus != null) {
                    if (readStatus == 0) {
                        int result = noticeMapper.updateReadStatus(tableName, noticeId, userId);
                        if (result > 0) {
                            noticeMapper.updateReadCount(noticeId);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("已读通知失败,通知id:{},用户id:{}", noticeId, userId, e);
        }finally {
            redisUtil.unlock(cacheKey, "1");
        }
        return 1;
    }

    public int read(SelfNoticeDto selfNoticeDto, Long userId) {
        String cacheKey = LockCacheKey.NOTICE_READ + selfNoticeDto.getId() + "_" + userId;
        //加锁2秒钟
        try {
            String tableName = getTableName(userId);
            if (redisUtil.tryLock(cacheKey, "1", 2000)) {
                Integer readStatus = noticeMapper.getReadStatus(tableName, selfNoticeDto.getId(), userId);
                if (readStatus != null) {
                    if (readStatus == 0) {
                        int result = noticeMapper.updateReadStatus(tableName, selfNoticeDto.getId(), userId);
                        if (result > 0) {
                            noticeMapper.updateReadCount(selfNoticeDto.getId());
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("已读通知失败,通知id:{},用户id:{}", selfNoticeDto.getId(), userId, e);
        }finally {
            redisUtil.unlock(cacheKey, "1");
        }
        return 1;
    }

    @Override
    public List<BrandNoticeClassifyCountDto> getBrandNoticeClassifyCountDtoByUserId() {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser.getBrandId() == null){
            log.error("品牌商-全部通知-通知分类分组查询失败，该用户未查询到品牌商ID:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return noticeMapper.getBrandNoticeClassifyCountDtoByUserId(getTableName(sysUser.getUserId()), sysUser.getUserId(), sysUser.getBrandId());
    }

    @Override
    public List<SimpleBrandDto> getBrandGroupByUserId() {
        Long userId = SecurityUtils.getUserId();
        return noticeMapper.getSimpleBrandDtoByUserId(getTableName(userId), userId);
    }

    @Override
    public List<SimpleBrandDto> getBrandGroupByFranchiseId() {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null){
            log.error("获取个人所有通知列表失败:{}, 加盟商ID不存在", JSONObject.toJSONString(sysUser));
            return null;
        }
        List<SimpleBrandDto> simpleBrandDtos = new ArrayList<>();
        int exist = noticeMapper.existFranchiseNotice(getTableName(sysUser.getUserId()), sysUser.getUserId(), franchiseId);
        if(exist > 0){
            simpleBrandDtos.add(new SimpleBrandDto(0L, "本部门"));
        }
        List<SimpleBrandDto> brandDtos = getBrandGroupByUserId();
        if(brandDtos != null && !brandDtos.isEmpty()){
            simpleBrandDtos.addAll(brandDtos);
        }
        return simpleBrandDtos;
    }

    @Override
    public void sendScheduleNotice(Integer noticeId) {
        log.info("定时任务开始发送通知，通知id:{}", noticeId);
        NoticeDto noticeDto = noticeMapper.getNoticeById(noticeId);
        //不为空，并且为未发送
        if(noticeDto != null && noticeDto.getSendStatus() == 0){
            //确保为定时任务
            if(StringUtil.isNotEmpty(noticeDto.getScheduleTime())){
                long diff = DateUtils.calculateTimeDifferenceInMinutes(noticeDto.getPublishTime(), DateUtils.getLongDateStr());
                if(diff > 30000000){
                    log.info("定时任务时间与当前时间差距较大,暂停执行,差距:{}秒", diff);
                    return;
                }
                noticeMapper.updateSendStatusNotice(noticeId, 1);
                if(noticeDto.getNoticeType() == 1) {
                    NoticeVo noticeVo = new NoticeVo();
                    noticeVo.setId(noticeId);
                    noticeVo.setSource(noticeDto.getSource());
                    noticeVo.setSourceId(noticeDto.getSourceId());
                    noticeVo.setSendGroup(noticeDto.getSendGroup());
                    List<Long> userIdList = getNoticeUserIdList(noticeVo);
                    log.info("发送定时任务消息,一共发送:{}人", (userIdList != null ? userIdList.size() : 0));
                    //去空和去重
                    userIdList = userIdList.stream().filter(x-> x != null).distinct().collect(Collectors.toList());
                    log.info("发送定时任务消息,去重之后一共发送:{}人", userIdList.size());
                    List<Long> finalUserIdList = userIdList;
                    executorService.execute(() -> {
                        //直接发送通知
                        if (StringUtil.isEmpty(noticeVo.getScheduleTime())) {
                            if (!finalUserIdList.isEmpty()) {
                                log.info("开始写入数据库并且发送消息");
                                sendToDb(finalUserIdList, noticeVo.getId());
                            }
                        }
                    });
                }
            }
        }
    }

    private String getTableName(Long userId){
        return "notice_detail_" + userId % 5;
    }
}
