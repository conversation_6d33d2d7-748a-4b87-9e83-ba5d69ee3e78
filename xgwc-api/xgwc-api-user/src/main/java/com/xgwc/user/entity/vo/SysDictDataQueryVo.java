package com.xgwc.user.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysDictDataQueryVo {

    private static final long serialVersionUID=1L;


    @FieldDesc("字典类型")
    @NotNull(message = "字典类型必填")
    private String dictType;

    @FieldDesc("状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("字典编码")
    private Long dictCode;

    @FieldDesc("字典键值")
    private String dictValue;

    @FieldDesc("上级字典编码")
    private Long parentDictCode;

    private Long brandId;

}
