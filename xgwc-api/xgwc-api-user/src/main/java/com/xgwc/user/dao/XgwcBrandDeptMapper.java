package com.xgwc.user.dao;


import com.xgwc.user.entity.Staff;
import com.xgwc.user.entity.dto.XgwcBrandDeptDto;
import com.xgwc.user.entity.param.XgwcBrandDeptParam;
import com.xgwc.user.entity.vo.StaffVo;
import com.xgwc.user.entity.vo.XgwcBrandDeptVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.dao
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  11:19
 */

public interface XgwcBrandDeptMapper {

    /**
     * 更新部门状态
     * @param deptId 部门id
     * @param status 状态
     * @return 更新结果
     */
    int updateStatusById(@Param("deptId") Integer deptId, @Param("status") Integer status);

    /**
     * 获取部门列表
     * @param xgwcBrandDeptParam 部门参数
     * @return 部门列表
     */
    List<XgwcBrandDeptVo> getXgwcBrandDeptList(@Param("xgwcBrandDeptParam") XgwcBrandDeptParam xgwcBrandDeptParam);

    /**
     * 保存部门
     * @param xgwcBrandDeptDto 部门信息
     * @return 保存结果
     */
    int saveXgwcBrandDept(@Param("xgwcBrandDeptDto") XgwcBrandDeptDto xgwcBrandDeptDto);

    /**
     * 根据部门id获取部门信息
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    XgwcBrandDeptDto getXgwcBrandDeptById(@Param("deptId") Long deptId);
    XgwcBrandDeptDto getXgwcBrandDeptByUserId(@Param("userId") Long userId);

    /**
     * 更新部门信息
     * @param xgwcBrandDeptDto 部门信息
     * @return 更新结果
     */
    int updateXgwcBrandDeptById(@Param("xgwcBrandDeptDto") XgwcBrandDeptDto xgwcBrandDeptDto);

    /**
     * 根据部门id获取部门负责人信息
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    StaffVo selectXgwcDeptStaffManage(@Param("deptId") Integer deptId);

    /**
     * 根据部门id获取部门助理信息
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    List<StaffVo> selectXgwcDeptStaffSchedule(@Param("deptId") Integer deptId);

    /**
     * 更新部门助理信息
     *
     * @param staff 部门信息
     * @return 更新结果
     */
    int updateDeptStaffSchedule(@Param("staff") Staff staff);

    /**
     * 更新部门负责人信息
     *
     * @param isPrincipal 部门信息
     * @return 更新结果
     */
    int updateDeptStaffManage(@Param("principal") Staff isPrincipal);

    /**
     * 更新部门助理信息
     *
     * @param deptId 部门id
     * @return 更新结果
     */
    int updateDeptStaffAssistant(@Param("deptId") Long deptId);

    /**
     * 根据品牌id获取品牌下面的部门
     * @param brandId 品牌id
     * @return 部门
     */
    List<XgwcBrandDeptDto> getBrandDeptByBrandId(Long brandId);

    /**
     * 根据部门id获取每个部门下的员工数量
     * @param deptId 部门id
     * @return 员工数量
     */
    Integer selectDeptStaffNum(@Param("deptId") Long deptId);
}
