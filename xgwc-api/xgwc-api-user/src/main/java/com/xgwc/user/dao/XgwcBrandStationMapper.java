package com.xgwc.user.dao;


import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.dto.XgwcBrandStationDto;
import com.xgwc.user.entity.param.XgwcBrandStationParam;
import com.xgwc.user.entity.vo.XgwcBrandStationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.dao
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  11:19
 */

public interface XgwcBrandStationMapper {
    /**
     * 更新状态
     * @param stationId 岗位id
     * @param status 状态
     * @return 修改结果
     */
    int updateStatusById(@Param("stationId") Integer stationId, @Param("status") Integer status);

    /**
     * 查询岗位列表
     * @param xgwcBrandStationParam 查询参数
     * @return 岗位列表
     */
    List<XgwcBrandStationVo> getXgwcBrandStationList(@Param("xgwcBrandStationParam") XgwcBrandStationParam xgwcBrandStationParam);

    /**
     * 保存岗位信息
     * @param xgwcBrandStationDto 岗位信息
     * @return 添加结果
     */
    int saveXgwcBrandStation(@Param("xgwcBrandStationDto") XgwcBrandStationDto xgwcBrandStationDto);

    /**
     * 根据id查询
     * @param stationId 岗位id
     * @return 岗位信息
     */
    XgwcBrandStationVo getXgwcBrandStationById(@Param("stationId") Integer stationId);

    /**
     * 更新岗位信息
     * @param xgwcBrandStationDto 岗位信息
     * @return 修改结果
     */
    int updateXgwcBrandStation(@Param("xgwcBrandStationDto") XgwcBrandStationDto xgwcBrandStationDto);
}
