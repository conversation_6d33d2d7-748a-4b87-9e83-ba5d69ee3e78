package com.xgwc.user.service;

import com.xgwc.user.entity.dto.AgreementDto;
import com.xgwc.user.entity.vo.AgreementVo;

import java.util.List;

public interface AgreementService {

    /**
     * 存储协议
     * @param agreementVo 参数
     * @return 是否成功
     */
    int saveAgreement(AgreementVo agreementVo);

    /**
     * 根据品牌Id查询协议
     * @return 协议信息
     */
    AgreementDto getAgreementByBrandId();

    /**
     * 获取未同意的协议列表
     */
    List<AgreementDto> getNotAgreeList();


    /**
     * 获取所有协议列表
     * @return 协议
     */
    List<AgreementDto> getAllAgreementList();

    /**
     * 同意协议
     * @param agreementId 协议id
     * @return 是否成功
     */
    int agree(Integer agreementId);

}
