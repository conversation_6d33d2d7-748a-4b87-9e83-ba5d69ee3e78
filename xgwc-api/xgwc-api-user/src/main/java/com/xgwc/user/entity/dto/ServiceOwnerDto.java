package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class ServiceOwnerDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String companyName;

    @FieldDesc("联系人")
    @Excel(name = "联系人")
    private String contact;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("花名")
    @Excel(name = "花名")
    private String stageName;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("服务商id")
    @Excel(name = "服务商id")
    private Long id;

    @FieldDesc("是否删除：0正常，1删除")
    @Excel(name = "是否删除：0正常，1删除")
    private Long isDel;

    @FieldDesc("管理员手机号")
    @Excel(name = "管理员手机号")
    private String managerPhone;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("密码")
    @Excel(name = "密码")
    private String password;

    @FieldDesc("服务商类型：1：财务服务商")
    @Excel(name = "服务商类型：1：财务服务商")
    private Integer serviceType;

    @FieldDesc("状态：0正常，1禁用")
    @Excel(name = "状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("管理员用户ID")
    @Excel(name = "管理员用户ID")
    private Long userId;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("companyName",getCompanyName())
            .append("contact",getContact())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("id",getId())
            .append("isDel",getIsDel())
            .append("managerPhone",getManagerPhone())
            .append("modifyTime",getModifyTime())
            .append("password",getPassword())
            .append("serviceType",getServiceType())
            .append("status",getStatus())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("userId",getUserId())
        .toString();
    }
}
