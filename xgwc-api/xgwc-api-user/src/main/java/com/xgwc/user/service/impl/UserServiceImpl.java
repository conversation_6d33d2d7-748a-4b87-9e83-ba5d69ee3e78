package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.common.util.TreeUtils;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.user.dao.DesignerMapper;
import com.xgwc.user.dao.FranchiseOwnerMapper;
import com.xgwc.user.dao.SysUserMiddleMapper;
import com.xgwc.user.dao.UserMapper;
import com.xgwc.user.dao.UserMenuMapper;
import com.xgwc.user.dao.UserRoleMapper;
import com.xgwc.user.entity.dto.ApplyRecordDto;
import com.xgwc.user.entity.dto.FranchiseDto;
import com.xgwc.user.entity.dto.LoginBusinessResult;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.dto.SysUserDto;
import com.xgwc.user.entity.dto.SysUserMiddleDto;
import com.xgwc.user.entity.param.LoginBusinessParam;
import com.xgwc.user.entity.vo.*;
import com.xgwc.user.feign.entity.SysUserMiddle;
import com.xgwc.user.service.IApplyRecordService;
import com.xgwc.user.service.IFranchiseService;
import com.xgwc.user.service.UserService;
import com.xgwc.user.util.AutoIdUtil;
import jakarta.annotation.Resource;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class UserServiceImpl implements UserService {

    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private UserMapper sysUserMapper;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    private UserMenuMapper userMenuMapper;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private FranchiseOwnerMapper franchiseOwnerMapper;
    @Resource
    private DesignerMapper designerMapper;
    @Resource
    private IApplyRecordService applyRecordService;

    @Resource
    private SysUserMiddleMapper sysUserMiddleMapper;
    @Resource
    private IFranchiseService franchiseService;

    @Override
    public SysUser getUserInfoByUserName(String userName) {
        return sysUserMapper.getUserInfoByUserName(userName);
    }

    @Override
    public SysUser getUserInfoByUserId(Long userId) {
        return sysUserMapper.getUserInfoByUserId(userId);
    }

    @Override
    public SysUser findUserByPhone(String phone) {
        return sysUserMapper.findUserByPhone(phone);
    }

    /**
     * 查询用户
     *
     * @param userId 用户主键
     * @return 用户
     */
    @Override
    public SysUserMiddleDto selectSysUserByUserId(Long userId) {
        SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleById(userId);
        if (sysUserMiddleDto != null && StringUtils.isNotBlank(sysUserMiddleDto.getPhone())) {
            sysUserMiddleDto.setPhone(ParamDecryptUtil.decryptParam(sysUserMiddleDto.getPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        if(sysUserMiddleDto.getUserType() == 2){
            sysUserMiddleDto.setFranchiseId(sysUserMiddleDto.getSourceId());
        }
        return sysUserMiddleDto;
    }

    /**
     * 查询用户列表
     *
     * @param sysUser 用户
     * @return 用户
     */
    @Override
    public List<SysUserDto> selectSysUserList(SysUserQueryVo sysUser) {
        return sysUserMapper.selectSysUserList(sysUser);
    }

    /**
     * 新增用户
     *
     * @param dto 用户
     * @return 结果
     */
    @Override
    public int insertSysUser(SysUserVo dto) {

        SysUser sysUser = BeanUtil.copyProperties(dto, SysUser.class);
        sysUser.setCreateTime(DateUtils.getNowDate());
        if (StringUtils.isNotBlank(sysUser.getPassword()))
            sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
        else sysUser.setPassword(passwordEncoder.encode("12345678"));
        if (StringUtils.isNotBlank(sysUser.getPhone())) {
            sysUser.setPhone(ParamDecryptUtil.encrypt(sysUser.getPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        sysUser.setUserId(AutoIdUtil.getId());
        return sysUserMapper.save(sysUser);
    }

    /**
     * 修改用户
     *
     * @param dto 用户
     * @return 结果
     */
    @Override
    public int updateSysUser(SysUserVo dto) {
        SysUser sysUser = BeanUtil.copyProperties(dto, SysUser.class);
        if (StringUtils.isNotBlank(sysUser.getPassword())) {
            sysUser.setPasswordModifyTime(DateUtils.getNowDate());
            sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
        }
        sysUser.setUpdateBy(SecurityUtils.getNickName());
        sysUser.setUpdateTime(DateUtils.getNowDate());
        if (StringUtils.isNotBlank(sysUser.getPhone())) {
            sysUser.setPhone(ParamDecryptUtil.encrypt(sysUser.getPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        sysUserMapper.updateSysUser(sysUser);
        SysUserDto user = sysUserMapper.selectSysUserByUserId(sysUser.getUserId());
        if (sysUser.getStatus() == 1) {
            redisUtil.remove("user:login:" + user.getUserName() + ":token");
        }
        return 1;
    }

    /**
     * 批量删除用户
     *
     * @param userIds 需要删除的用户主键
     * @return 结果
     */
    @Override
    public int deleteSysUserByUserIds(Long[] userIds) {
        return sysUserMapper.deleteSysUserByUserIds(userIds);
    }

    /**
     * 删除用户信息
     *
     * @param userId 用户主键
     * @return 结果
     */
    @Override
    public int deleteSysUserByUserId(Long userId) {
        return sysUserMapper.deleteSysUserByUserId(userId);
    }

    @Override
    public Set<String> listRoleKeyByUserId(Long userId) {
        List<String> list = userRoleMapper.listRoleKeyByUserId(userId);
        if(list != null && !list.isEmpty()){
            return list.stream().collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    @Override
    public Set<String> getMenuPermissionByUserId(Long userId) {
        if (userId == 1L) {
            userId = null;
        }

        List<String> list = userMenuMapper.getMenuPermissionByUserId(userId);
        if(list != null && !list.isEmpty()){
            return list.stream().collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    @Override
    public List<SysMenuDto> getMenuByUserId(Long userId) {
        String isAdmin = "";
        if (userId == 1L) {
            userId = null;
            isAdmin = "true";
        }
        return userMenuMapper.getMenuByUserId(userId,isAdmin);
    }

    @Override
    public List<SysMenuDto> getMenuTreeByUserId(Long userId) {
        List<SysMenuDto> list = getMenuByUserId(userId);
        List<SysMenuDto> tree = TreeUtils.listToTree(list, 0L);
        return tree;
    }

    @Override
    public LoginBusinessResult getBusinessLoginUser(LoginBusinessParam params) {
        LoginBusinessResult result = new LoginBusinessResult();
        if(params.getMainUserId() != 1L) {
            SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleById(params.getUserId());
            if(params.getLoginChannels() == 1){ // web端登录
                if(sysUserMiddleDto.getUserType() == 2){ // 加盟商
                    List<ApplyRecordDto> list = applyRecordService.selectApplyRecordListByUserIdAndBusinessType(sysUserMiddleDto.getUserId(), 1);
                    if(!list.isEmpty()){
                        if(list.size() == 1){
                            FranchiseOwnerVO franchiseOwnerVO = franchiseOwnerMapper.findFranchiseOwnerByFranchiseIdAndBrandId(list.get(0).getBusinessId(), list.get(0).getBrandId());
                            result.setBusinessId(franchiseOwnerVO.getId());
                            result.setBusinessStatus(list.get(0).getCheckStatus());
                            result.setBrandName(franchiseOwnerVO.getBrandName());
//                        FranchiseOwnerVO franchiseOwnerVO = franchiseOwnerMapper.findFranchiseOwnerByManagerUserIdAndBrandId(list.get(0).getUserId(), list.get(0).getBrandId());
//                        if(franchiseOwnerVO != null){
//                            result.setBrandName(franchiseOwnerVO.getBrandName());
//                        }
                        } else {
                            // 存在多个申请记录，判断是否有通过的记录
                            List<ApplyRecordDto> passList = list.stream().filter(item -> item.getCheckStatus() == 1).collect(Collectors.toList());
                            if(!passList.isEmpty()){
                                result.setBusinessId(passList.get(0).getBusinessId());
                                result.setBusinessStatus(passList.get(0).getCheckStatus());
                            }else { // rule: 多个申请记录，没有通过的，默认都在审核中
                                result.setBusinessId(list.get(0).getBusinessId());
                                result.setBusinessStatus(list.get(0).getCheckStatus());
                            }
                        }
                    }
                }else if(sysUserMiddleDto.getUserType() == 3){ // 设计师
                    List<ApplyRecordDto> list = applyRecordService.selectApplyRecordListByUserIdAndBusinessType(sysUserMiddleDto.getUserId(), 2);
                    if(!list.isEmpty()){
                        if(list.size() == 1){
                            result.setBusinessId(list.get(0).getBusinessId());
                            result.setBusinessStatus(list.get(0).getCheckStatus());
                        } else {
                            // 存在多个申请记录，判断是否有通过的记录
                            List<ApplyRecordDto> passList = list.stream().filter(item -> item.getCheckStatus() == 1).collect(Collectors.toList());
                            if(!passList.isEmpty()){
                                result.setBusinessId(passList.get(0).getBusinessId());
                                result.setBusinessStatus(passList.get(0).getCheckStatus());
                            }
                        }
                    }
                }else if(sysUserMiddleDto.getUserType() == 4 || sysUserMiddleDto.getUserType() == 5){
                    // 员工正常登录 默认返回1
                    result.setBusinessStatus(1);
                }
            }else if(params.getLoginChannels() == 2){ // 链接登录
                if(sysUserMiddleDto == null){
                    SysUser sysUser = sysUserMapper.getUserInfoByUserId(params.getMainUserId());
                    result.setManagerPhone(ParamDecryptUtil.decryptParam(sysUser.getPhone(), ParamDecryptUtil.PHONE_KEY));
                    result.setUserType(params.getUserType());
                }else {
                    if(sysUserMiddleDto.getUserType() == 2){ // 加盟商
                        List<ApplyInfoVO> applyInfoVOList = franchiseOwnerMapper.findFranchiseOwnerByManagerUserIdAndBrandIdList(sysUserMiddleDto.getUserId(), params.getBusinessId());
                        if(!applyInfoVOList.isEmpty()){
                            result.setApplyInfoList(applyInfoVOList);
                        }else {
                            FranchiseOwnerVO franchiseOwnerVO = franchiseOwnerMapper.findFranchiseOwnerByManagerUserIdAndBrandId(sysUserMiddleDto.getUserId(), params.getBusinessId());
                            if(franchiseOwnerVO != null){
                                result.setBusinessId(franchiseOwnerVO.getId());
                                result.setBusinessStatus(franchiseOwnerVO.getCheckStatus().intValue());
                                result.setBrandName(franchiseOwnerVO.getBrandName());
                            }
                        }
                        FranchiseDto franchise = franchiseService.getFranchiseByManagerUserId(sysUserMiddleDto.getUserId());
                        if(franchise != null){
                            result.setFranchiseId(franchise.getId());
                            result.setFranchiseName(franchise.getFranchiseName());
                            result.setManagerName(franchise.getManagerName());
                        }
                    }else if(sysUserMiddleDto.getUserType() == 3){ // 设计师
                        List<ApplyInfoVO> applyInfoVOList = designerMapper.findDesignerByManagerUserIdAndBrandIdList(sysUserMiddleDto.getUserId(), params.getBusinessId());
                        if(!applyInfoVOList.isEmpty()){
                            result.setApplyInfoList(applyInfoVOList);
                        }else {
                            DesignerVO designerVO = designerMapper.findDesignerByManagerUserIdAndBrandId(sysUserMiddleDto.getUserId(), params.getBusinessId());
                            if(designerVO != null){
                                result.setBusinessId(designerVO.getDesignerId());
                                result.setBusinessStatus(designerVO.getCheckStatus().intValue());
                                result.setBrandName(designerVO.getBrandName());
                            }
                        }
                    }
                    result.setUserType(sysUserMiddleDto.getUserType());
                    result.setStageName(sysUserMiddleDto.getStageName());
                }
            }
        }
        result.setLoginChannels(params.getLoginChannels());
        return result;
    }

    @Override
    public Long saveSysUserMiddle(SysUserMiddle sysUserMiddle) {
        SysUserMiddleVo middleVo = BeanUtil.copyProperties(sysUserMiddle, SysUserMiddleVo.class);
        int i = sysUserMiddleMapper.insertSysUserMiddle(middleVo);
        if(i > 0){
            return middleVo.getId();
        }
        return null;
    }
}
