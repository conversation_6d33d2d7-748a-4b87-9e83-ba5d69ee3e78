package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysMenuQueryVo {

    private static final long serialVersionUID=1L;


    @FieldDesc("权限名称：英文")
    private String authCode;

    @FieldDesc("页面组件")
    private String component;

    @FieldDesc("菜单id")
    private Long id;

    @FieldDesc("菜单名称")
    private String name;

    @FieldDesc("路由地址")
    private String path;

    @FieldDesc("父类id")
    private Long pid;

    @FieldDesc("菜单状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("标题")
    private String title;

    @FieldDesc("菜单类型：0.目录，1菜单，2.按钮")
    private String type;

    @FieldDesc("所属分类")
    private String modelType;
}
