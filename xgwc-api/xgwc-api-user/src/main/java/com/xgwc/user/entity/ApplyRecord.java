package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class ApplyRecord {

private static final long serialVersionUID=1L;

    /** 所属品牌id */
    private Long brandId;

    /** 业务id */
    private Long businessId;

    /** 业务类型：1:加盟商记录，2:设计师记录 */
    private Integer businessType;

    /** 申请状态 */
    private Integer checkStatus;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 主键id */
    private Long id;

    /** 行修改时间 */
    private Date modifyTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 申请用户id */
    private Long userId;



}