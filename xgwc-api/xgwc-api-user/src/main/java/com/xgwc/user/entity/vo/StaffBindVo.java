package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class StaffBindVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("员工id")
    @NotNull(message = "员工id不能为空")
    private Long id;

    @FieldDesc("用户id")
    private Long userId;

    @FieldDesc("姓名")
    @NotNull(message = "姓名不能为空")
    private String name;

    private Long mainUserId;

    @FieldDesc("业务id")
    private Long businessId;

    @FieldDesc("员工类型")
    @NotNull(message = "员工类型不能为空")
    private Integer userType;

    @FieldDesc("花名")
    @NotNull(message = "花名不能为空")
    private String stageName;

}
