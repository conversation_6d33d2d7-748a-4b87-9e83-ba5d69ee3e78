package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysUserQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("用户ID")
    private Long userId;

    @FieldDesc("用户名")
    private String userName;

    @FieldDesc("昵称")
    private String nickName;

    @FieldDesc("邮箱")
    private String email;

    @FieldDesc("手机号，加密")
    private String phone;

    @FieldDesc("用户性别（0男 1女 2未知）")
    private Long sex;

    @FieldDesc("用户头像")
    private String avatar;

    @FieldDesc("帐号状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("删除标志（0代表存在 1代表删除）")
    private Integer isDel;

    @FieldDesc("登录IP")
    private String loginIp;

    @FieldDesc("登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  loginDate;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("工作类型(0全职，1兼职）")
    private Long jobType;

    @FieldDesc("微信id")
    private String wechatOpenid;

    @FieldDesc("创建人ID")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("修改人ID")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;

    @FieldDesc("密码修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  passwordModifyTime;



}
