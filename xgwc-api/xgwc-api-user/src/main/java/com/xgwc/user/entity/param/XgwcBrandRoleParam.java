package com.xgwc.user.entity.param;

import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  10:58
 */

/**
 * 组织管理-角色管理参数
 */
@Data
public class XgwcBrandRoleParam {

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 状态：0正常，1禁用
     */
    private Integer status;

    /**
     * 品牌商id
     */
    private Long brandOwnerId;
}
