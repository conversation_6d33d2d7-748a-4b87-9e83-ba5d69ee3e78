package com.xgwc.user.dao;


import com.xgwc.user.entity.BrandRoleMenu;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.dto.XgwcBrandRoleDto;
import com.xgwc.user.entity.param.XgwcBrandRoleParam;
import com.xgwc.user.entity.vo.SysRoleDataVo;
import com.xgwc.user.entity.vo.SysRoleMenuVo;
import com.xgwc.user.entity.vo.XgwcBrandRoleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.dao
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  11:19
 */

public interface XgwcBrandRoleMapper {

    /**
     * 更新状态
     * @param roleId 角色id
     * @param status 状态
     * @return
     */
    int updateStatusById(@Param("roleId") Integer roleId, @Param("status") Integer status);

    /**
     * 查询品牌角色列表
     * @param xgwcBrandRoleParam 查询参数
     * @return
     */
    List<XgwcBrandRoleVo> getXgwcBrandRoleList(@Param("xgwcBrandRoleParam") XgwcBrandRoleParam xgwcBrandRoleParam);

    /**
     * 保存品牌角色
     * @param xgwcBrandRoleDto 保存参数
     * @return
     */
    int saveXgwcBrandRole(@Param("xgwcBrandRoleDto") XgwcBrandRoleDto xgwcBrandRoleDto);

    /**
     * 根据id查询品牌角色
     * @param roleId id
     * @return
     */
    XgwcBrandRoleVo getXgwcBrandRoleById(@Param("roleId") Integer roleId);

    /**
     * 更新品牌角色
     * @param xgwcBrandRoleDto 更新参数
     * @return
     */
    int updateXgwcBrandRole(@Param("xgwcBrandRoleDto") XgwcBrandRoleDto xgwcBrandRoleDto);

    /**
     * 保存品牌角色菜单权限
     * @param xgwcBrandRoleDto 保存参数
     * @return
     */
    int saveXgwcBrandRoleMenu(@Param("xgwcBrandRoleDto") XgwcBrandRoleDto xgwcBrandRoleDto);

    /**
     * 保存品牌角色数据权限
     * @param dataMenu 保存参数
     * @return
     */
    int saveXgwcBrandRoleDataScope(@Param("dataMenu") SysRoleDataVo dataMenu);

    /**
     * 删除品牌角色菜单权限
     * @param roleId 角色id
     * @return
     */
    int deleteXgwcBrandRoleMenu(@Param("roleId") Long roleId);

    /**
     * 删除品牌角色数据权限
     * @param roleId 角色id
     * @return
     */
    int deleteXgwcBrandRoleDataScope(@Param("roleId") Long roleId);

    /**
     * 根据角色ID获取菜单权限
     *
     * @param roleId 角色ID
     * @return 菜单权限
     */
    List<SysRoleMenuVo> getXgwcBrandRoleMenuById(@Param("roleId") Integer roleId);

    /**
     * 根据角色ID获取数据权限
     *
     * @param roleId 角色ID
     * @return 数据权限
     */
    List<SysRoleDataVo> getXgwcBrandRoleDataById(@Param("roleId") Integer roleId);


    public int insertBrandRoleMenu(@Param("array") List<BrandRoleMenu> sysRoleMenus);

    /**
     * 添加角色用户关系
     * @param roleId 角色ID
     * @param userId 用户ID
     * @param isFlag 标识字段（0-品牌商 1-加盟商 2-财务服务商 3-销售服务商）
     * @return 结果
     */
    public int insertBrandRoleUser(@Param(value = "roleId") Long roleId,@Param(value = "userId") Long userId,Integer isFlag);

    /**
     * 根据用户ID获取品牌角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<String> findBrandRoleKeyByUserId(Long userId);

    List<SysMenuDto> findMenuByUserId(@Param("userId") Long userId,@Param("modelType") String modelType);

    List<SysMenuDto> findMenuByModelType(@Param("modelType") String modelType);

    /**
     * 批量保存品牌角色数据权限
     * @param dataMenu 数据权限
     * @return  影响行数
     */
    int saveXgwcBrandRoleDataScopeDeptId(@Param("dataMenu") SysRoleDataVo dataMenu);

    /**
     * 根据用户ID获取品牌角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<XgwcBrandRoleVo> selectRoleByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID获取菜单列表
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenuDto> selectMenuByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID删除角色用户关系
     * @param userId 用户ID
     * @return 结果
     */
    int deleteBrandRoleUser(Long userId);

    /**
     * 根据角色ID获取角色最大下载次数
     *
     * @param integerEoleIds 角色ID
     * @return 数据权限
     */
    Integer getXgwcBrandRoleDataByIds(@Param("roleIds") List<Integer> integerEoleIds);

    /**
     * 根据菜单ID获取角色权限
     *
     * @param menuIdDispatchWorkStaff 菜单id
     * @param brandId 品牌商ID
     * @return 角色权限
     */
    List<SysRoleMenuVo> selectRoleByMenu(@Param("menuId") Integer menuIdDispatchWorkStaff,
                                         @Param("brandId") Long brandId);

    /**
     * 根据菜单ID查询是否有子级
     *
     * @param menuId 菜单id
     * @return true、false
     */
    boolean selectLastLevelMenu(@Param("menuId") Long menuId);

    boolean selectLastLevelMenuData(@Param("menuId") Long menuId);
}
