package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.dto.LoginResult;
import com.xgwc.user.entity.dto.ServiceOwnerSimpleDto;
import com.xgwc.user.entity.vo.ServiceApplyVo;
import com.xgwc.user.entity.vo.ServiceOwnerVo;
import com.xgwc.user.entity.dto.ServiceOwnerDto;
import com.xgwc.user.entity.vo.ServiceOwnerQueryVo;

public interface IServiceOwnerService  {
    /**
     * 查询服务商
     * 
     * @param id 服务商主键
     * @return 服务商
     */
    public ServiceOwnerDto selectServiceOwnerById(Long id);

    /**
     * 查询服务商列表
     * 
     * @param serviceOwner 服务商
     * @return 服务商集合
     */
    public List<ServiceOwnerDto> selectServiceOwnerList(ServiceOwnerQueryVo serviceOwner);

    /**
     * 新增服务商
     * @param serviceOwner 服务商
     * @return 结果
     */
    public Long insertServiceOwner(ServiceOwnerVo serviceOwner);

    /**
     * 申请服务商
     * @param serviceApplyVo 申请信息
     * @return 结果
     */
    LoginResult applyServiceOwner(ServiceApplyVo serviceApplyVo);

    /**
     * 开启服务商
     * @param id 服务商主键
     * @return 结果
     */
    int enableServiceOwner(Long id);

    /**
     * 根据用户id查询服务商
     * @param userId 用户id
     * @return 结果
     */
    public ServiceOwnerDto selectServiceOwnerByUserId(Long userId);

    /**
     * 查询服务商简要信息
     * @return 结果
     */
    List<ServiceOwnerSimpleDto> selectServiceOwnerListForSelect();

    List<ServiceOwnerSimpleDto> selectSalesServiceOwnerListForSelect();
}
