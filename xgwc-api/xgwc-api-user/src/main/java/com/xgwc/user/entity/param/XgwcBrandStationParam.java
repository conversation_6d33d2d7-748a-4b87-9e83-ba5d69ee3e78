package com.xgwc.user.entity.param;

import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  10:44
 */

/**
 * 组织管理-岗位管理参数
 */
@Data
public class XgwcBrandStationParam {

    /**
     * 岗位名称
     */
    private String stationName;

    /**
     * 状态：0正常，1禁用
     */
    private Integer status;

    /**
     * 岗位id
     */
    private Integer stationId;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 品牌商id
     */
    private Long brandOwnerId;
}
