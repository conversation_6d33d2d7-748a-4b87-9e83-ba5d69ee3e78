package com.xgwc.user.entity.dto;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.util.StringUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;
import java.util.List;

@Data
public class SysTenantPackageDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("套餐编号")
    @Excel(name = "套餐编号")
    private Long id;

    @FieldDesc("套餐名")
    @Excel(name = "套餐名")
    private String name;

    @FieldDesc("租户状态（0正常 1停用）")
    @Excel(name = "租户状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("关联的菜单编号")
    @Excel(name = "关联的菜单编号")
    private List<Long> menuIds;

    @FieldDesc("创建者")
    @Excel(name = "创建者")
    private String creator;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("更新者")
    @Excel(name = "更新者")
    private String updater;

    @FieldDesc("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;

    public void setMenuIds(String menuIds) {
        List<Long> list = new ArrayList<>();
        if(StringUtils.isNotBlank(menuIds))
            list = JSONObject.parseArray(menuIds,Long.class);
        this.menuIds = list;
    }


    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("name",getName())
            .append("status",getStatus())
            .append("remark",getRemark())
            .append("menuIds",getMenuIds())
            .append("creator",getCreator())
            .append("createTime",getCreateTime())
            .append("updater",getUpdater())
            .append("updateTime",getUpdateTime())
            .append("isDel",getIsDel())
        .toString();
        }
}
