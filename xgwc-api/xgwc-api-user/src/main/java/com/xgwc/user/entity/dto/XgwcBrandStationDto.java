package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.dto
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  10:46
 */

/**
 * 组织管理-岗位管理
 */
@Data
public class XgwcBrandStationDto {

    /**
     * 岗位id
     */
    @FieldDesc("岗位id")
    private Long stationId;

    /**
     * 岗位名称
     */
    @NotNull("岗位名称不能为空")
    @FieldDesc("岗位名称")
    private String stationName;

    /**
     * 公司id
     */
    @NotNull("公司id不能为空")
    @FieldDesc("公司id")
    private Long companyId;

    /**
     * 部门id
     */
    @FieldDesc("部门id")
    private Long deptId;

    /**
     * 品牌商id
     */
    @FieldDesc("品牌商id")
    private Long brandOwnerId;

    /**
     * 排序：越小越前
     */
    @NotNull("排序不能为空")
    @FieldDesc("排序：越小越前")
    private Integer sort;

    /**
     * 状态：0正常，1禁用
     */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    /**
     * 是否删除：0否，1是
     */
    @FieldDesc("是否删除：0否，1是")
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
