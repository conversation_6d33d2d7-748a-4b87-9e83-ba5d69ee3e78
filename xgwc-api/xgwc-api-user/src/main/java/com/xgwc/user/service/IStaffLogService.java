package com.xgwc.user.service;

import com.xgwc.common.entity.StaffLog;
import com.xgwc.user.entity.dto.StaffLogDto;
import com.xgwc.user.entity.vo.StaffLogQueryVo;
import com.xgwc.user.entity.vo.StaffLogVo;

import java.util.List;

public interface IStaffLogService  {

    /**
     * 查询加盟商员工日志列表
     * 
     * @param staffLog 加盟商员工日志
     * @return 加盟商员工日志集合
     */
    public List<StaffLogDto> selectStaffLogList(StaffLogQueryVo staffLog);

    /**
     * 新增加盟商员工日志
     * 
     * @param staffLog 加盟商员工日志
     * @return 结果
     */
    public int insertStaffLog(StaffLog staffLog);

    /**
     * 修改加盟商员工日志
     * 
     * @param staffLog 加盟商员工日志
     * @return 结果
     */
    public int updateStaffLog(StaffLogVo staffLog);

    /**
     * 批量删除加盟商员工日志
     * 
     * @param ids 需要删除的加盟商员工日志主键集合
     * @return 结果
     */
    public int deleteStaffLogByIds(Long[] ids);

    /**
     * 根据员工ID和业务类型查询日志
     * @param staffId 员工ID
     * @param businessType 业务类型（1:品牌商；2:加盟商）
     * @return 日志列表
     */
    List<StaffLogDto> findStaffLogByStaffIdAndBusinessType(Long staffId, int businessType);
}
