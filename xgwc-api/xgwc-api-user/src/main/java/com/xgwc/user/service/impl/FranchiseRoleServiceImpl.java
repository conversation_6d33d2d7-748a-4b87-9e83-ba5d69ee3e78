package com.xgwc.user.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.FilterMenuListUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.common.util.TreeUtils;
import com.xgwc.user.dao.FranchiseOwnerMapper;
import com.xgwc.user.dao.FranchiseRoleMapper;
import com.xgwc.user.dao.FranchiseStaffMapper;
import com.xgwc.user.entity.dto.FranchiseRoleDto;
import com.xgwc.user.entity.dto.FranchiseStaffDto;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.param.FranchiseRoleParam;
import com.xgwc.user.entity.vo.*;
import com.xgwc.user.service.FranchiseRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  11:55
 */
@Service
@Slf4j
public class FranchiseRoleServiceImpl implements FranchiseRoleService {

    @Resource
    private FranchiseRoleMapper franchiseRoleMapper;
    @Resource
    private FranchiseStaffMapper franchiseStaffMapper;
    @Resource
    private FranchiseOwnerMapper franchiseOwnerService;

    /**
     * 获取角色列表
     * @param franchiseRoleParam 加盟商角色信息
     * @return 加盟商角色列表
     */
    @Override
    public List<FranchiseRoleVo> getFranchiseRoleList(FranchiseRoleParam franchiseRoleParam) {
        franchiseRoleParam.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
        return franchiseRoleMapper.getFranchiseRoleList(franchiseRoleParam);
    }

    /**
     * 新增角色
     * @param franchiseRoleDto 加盟商角色信息
     * @return 添加信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // 回滚所有异常
    public ApiResult saveFranchiseRole(FranchiseRoleDto franchiseRoleDto) {
        try {
            // 1. 参数校验
            if (franchiseRoleDto == null) {
                log.error("角色信息不能为空");
                return ApiResult.error("角色信息不能为空");
            }
            String roleName = franchiseRoleDto.getRoleName();

            // 2. 保存角色基本信息
            franchiseRoleDto.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
            franchiseRoleDto.setCreateBy(SecurityUtils.getNickName());
            int saveResult = franchiseRoleMapper.saveFranchiseRole(franchiseRoleDto);
            if (saveResult <= 0) {
                log.error("新增角色失败，角色名称={}", roleName);
                throw new ApiException("新增角色失败"); // 抛出异常触发回滚
            }

            // 3. 保存角色-菜单关联关系
            List<SysRoleMenuVo> menuIds = franchiseRoleDto.getMenuIds();
            Long roleId = franchiseRoleDto.getRoleId();
            franchiseRoleMapper.deleteRoleDownloadLimit(roleId);
            saveRoleMenuRelations(franchiseRoleDto, roleId, menuIds);

            log.info("角色及菜单关联保存成功，角色名称={}", roleName);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("保存角色及菜单关联异常，角色名称={}, 错误信息：{}",
                    franchiseRoleDto.getRoleId(), e.getMessage(), e);
            throw e; // 重新抛出异常确保事务回滚
        }
    }

    // 使用 public 确保通过代理调用（避免自调用事务失效）
    public void saveRoleMenuRelations(FranchiseRoleDto franchiseRoleDto, Long roleId, List<SysRoleMenuVo> menuIds) {
        if(!CollectionUtils.isEmpty(menuIds)){
            List<SysRoleMenuVo> sysRoleMenuVoList = new ArrayList<>();
            for (SysRoleMenuVo menuId : menuIds) {
                SysRoleMenuVo rm = new SysRoleMenuVo();
                rm.setRoleId(roleId);
                rm.setMenuId(menuId.getMenuId());
                rm.setCreateBy(franchiseRoleDto.getCreateBy());
                sysRoleMenuVoList.add(rm);
            }
            // 批量插入，若失败则抛出异常
            int insertCount = franchiseRoleMapper.saveRoleMenu(sysRoleMenuVoList);
            if (insertCount <= 0 ) {
                log.error("角色-菜单关联保存不完整，预期={}，实际={}", menuIds.size(), insertCount);
                throw new ApiException("角色-菜单关联保存失败");
            }
        }

        List<SysRoleDataVo> dataMenuIds = franchiseRoleDto.getDataMenuIds();
        if (!CollectionUtils.isEmpty(dataMenuIds)) {
            for (SysRoleDataVo dataMenu : dataMenuIds) {
                List<Long> deptIds = dataMenu.getDeptIds();
                dataMenu.setRoleId(roleId);
                dataMenu.setCreateBy(SecurityUtils.getNickName());
                if (!CollectionUtils.isEmpty(deptIds)) {
                    int roleDataScope = franchiseRoleMapper.savefranchiseBrandRoleDataScopeDeptId(dataMenu);
                    if (roleDataScope <= 0) {
                        log.error("新增失败，指定部门数据权限影响行数：{}", roleDataScope);
                        throw new ApiException("新增失败");
                    }
                }else {
                    int dataScopeResult = franchiseRoleMapper.saveFranchiseRoleDataScope(dataMenu);
                    if (dataScopeResult <= 0) {
                        log.error("新增失败，数据权限表影响行数：{}", dataScopeResult);
                        throw new ApiException("新增失败");
                    }
                }
                if(!CollectionUtils.isEmpty(dataMenu.getDownloadLimitFranchise())){
                    int downloadLimit = franchiseRoleMapper.saveDownloadLimit(dataMenu);
                    if (downloadLimit <= 0) {
                        log.error("新增失败，下载次数限制表影响行数：{}", downloadLimit);
                        throw new ApiException("新增失败");
                    }
                }
            }
        }
    }

    /**
     * 根据角色ID获取角色信息
     * @param roleId 角色ID
     * @return 角色信息
     */
    @Override
    public ApiResult getFranchiseRoleById(Long roleId) {
        if (roleId == null || roleId <= 0) {
            log.error("无效的角色ID: {}", roleId);
            return ApiResult.error("角色ID不能为空或非法");
        }
        FranchiseRoleVo franchiseRole = franchiseRoleMapper.getFranchiseRoleById(roleId);
        if (franchiseRole == null) {
            log.warn("未找到对应的角色信息，roleId: {}", roleId);
            return ApiResult.error("未找到该角色的信息");
        }

        List<SysRoleMenuVo> filteredMenuList = FilterMenuListUtil.filterList(
                franchiseRoleMapper.getFranchiseRoleMenusById(roleId),
                roleMenuVo -> franchiseRoleMapper.selectLastLevelMenu(roleMenuVo.getMenuId())
        );

        List<SysRoleDataVo> filteredMenuDateList = FilterMenuListUtil.filterList(
                franchiseRoleMapper.getFranchiseRoleDataById(roleId),
                sysRoleDataVo -> franchiseRoleMapper.selectLastLevelMenuDate(sysRoleDataVo.getMenuId())
        );

        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        List<SysRoleDateDownloadLimitVo> franchiseRoleDownloadById = franchiseRoleMapper.getFranchiseRoleDownloadById(roleId, franchiseId);
        // 首先根据 menuId 分组，收集所有 deptId
        Map<Long, List<Long>> deptIdsByMenuId = filteredMenuDateList.stream()
                .collect(Collectors.groupingBy(
                        SysRoleDataVo::getMenuId,
                        Collectors.mapping(SysRoleDataVo::getDeptId, Collectors.toList())
                ));

        // 使用 toMap 收集器根据 menuId 去重
        Map<Long, SysRoleDataVo> uniqueByMenuId = filteredMenuDateList.stream()
                .collect(Collectors.toMap(
                        SysRoleDataVo::getMenuId,
                        vo -> {
                            SysRoleDataVo newVo = new SysRoleDataVo();
                            newVo.setMenuId(vo.getMenuId());
                            newVo.setDeptIds(deptIdsByMenuId.get(vo.getMenuId()));
                            newVo.setId(vo.getId());
                            newVo.setRoleId(vo.getRoleId());
                            newVo.setRoleName(vo.getRoleName());
                            newVo.setMenuName(vo.getMenuName());
                            newVo.setDataScope(vo.getDataScope());
                            newVo.setDataMasking(vo.getDataMasking());
                            newVo.setTimeLimit(vo.getTimeLimit());
                            newVo.setDownloadLimitFranchise(franchiseRoleDownloadById);
                            return newVo;
                        },
                        (existing, replacement) -> existing // 保留第一个出现的对象
                ));
        List<SysRoleDataVo> uniqueList = new ArrayList<>(uniqueByMenuId.values());
        FranchiseRoleDto franchiseRoleDto = new FranchiseRoleDto();
        BeanUtils.copyProperties(franchiseRole, franchiseRoleDto);
        franchiseRoleDto.setDataMenuIds(uniqueList);
        franchiseRoleDto.setMenuIds(filteredMenuList);

        return ApiResult.ok(franchiseRoleDto);
    }

    /**
     * 修改角色信息
     * @param franchiseRoleDto 角色信息
     * @return 修改结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateFranchiseRole(FranchiseRoleDto franchiseRoleDto) {
        if (franchiseRoleDto == null) {
            return ApiResult.error("角色信息不能为空");
        }
        Long roleId = franchiseRoleDto.getRoleId();
        if (roleId == null) {
            return ApiResult.error("角色ID不能为空");
        }
        List<SysRoleMenuVo> menuIds = franchiseRoleDto.getMenuIds();
        franchiseRoleMapper.deleteRoleMenu(roleId);
        franchiseRoleMapper.deleteRoleData(roleId);
        franchiseRoleMapper.deleteRoleDownloadLimit(roleId);

        // 插入角色和菜单的关联关系
        String nickName = SecurityUtils.getNickName();
        franchiseRoleDto.setUpdateBy(nickName);
        franchiseRoleDto.setCreateBy(nickName);
        saveRoleMenuRelations(franchiseRoleDto, roleId, menuIds);
        int i = franchiseRoleMapper.updateFranchiseRole(franchiseRoleDto);
        if (i > 0) {
            log.info("修改成功，id为：{}", roleId);
            return ApiResult.ok();
        }
        log.error("修改失败，id为：{}", roleId);
        throw new ApiException("角色信息修改失败,id不存在或数据未找到");
    }

    /**
     * 修改角色状态
     * @param roleId 角色ID
     * @param status 状态
     * @return 修改结果
     */
    @Override
    public ApiResult updateStatusById(Integer roleId, Integer status) {
        // 参数校验：确保 roleId 合法且 status 在允许范围内
        if (roleId == null || roleId <= 0) {
            log.error("无效的角色ID: {}", roleId);
            return ApiResult.error("更新角色状态失败");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新角色状态失败");
        }

        try {
            int i = franchiseRoleMapper.updateStatusById(roleId, status);
            if (i > 0) {
                log.info("更新角色状态成功，roleId: {}, 新状态: {}", roleId, status);
                return ApiResult.ok();
            } else {
                log.error("更新角色状态失败，roleId: {}，未找到记录或状态未更新", roleId);
                return ApiResult.error("状态更新失败，id不存在或数据未找到");
            }
        } catch (Exception e) {
            log.error("更新角色状态时发生异常，roleId: {}, status: {}", roleId, status, e);
            return ApiResult.error("更新角色状态失败");
        }
    }

    /**
     * 根据userId查询角色信息
     *
     * @param userId 用户id
     * @return 角色信息
     */
    @Override
    public List<FranchiseRoleVo> selectRoleByUserId(Long userId) {
        return franchiseRoleMapper.selectRoleByUserId(userId);
    }

    /**
     * 根据userId查询菜单信息
     *
     * @param userId 用户id
     * @return 菜单信息
     */
    @Override
    public List<SysMenuDto> selectMenuByUserId(Long userId) {
        List<SysMenuDto> sysMenuDtos = franchiseRoleMapper.selectMenuByUserId(userId);
        List<SysMenuDto> distinctMenus = sysMenuDtos.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                SysMenuDto::getId,
                                menu -> menu,
                                (existing, replacement) -> existing // 保留第一个
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        return TreeUtils.listToTree(distinctMenus, 0L);
    }

    /**
     * 查询角色下载权限
     *
     * @return 角色下载权限
     */
    @Override
    public ApiResult selectRoleDownloadLimit() {
        // 1. 获取当前用户信息
        Long userId = SecurityUtils.getSysUser().getUserId();
        FranchiseStaffDto franchiseStaffDto = franchiseStaffMapper.findFranchiseStaffByBindUserId(userId);

        if (franchiseStaffDto == null) {
            return ApiResult.ok();
        }

        // 2. 获取加盟商 ID 和主 List（franchiseOwnerVO）
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        List<FranchiseOwnerVO> franchiseOwnerVO = franchiseOwnerService.selectFranchiseInfoByFranchiseId(franchiseId);

        // 如果主 List 为空，直接返回
        if (CollectionUtils.isEmpty(franchiseOwnerVO)) {
            return ApiResult.ok(franchiseOwnerVO);
        }

        // 3. 获取从 List 1（franchiseStaffDtos），并转换为 Map<brandId, downloadLimit>
        List<FranchiseStaffDto> franchiseStaffDtos = franchiseStaffMapper.selectFranchiseStaffDownload(franchiseStaffDto.getId(), null);
        Map<Long, Integer> staffBrandIdToLimitMap = Collections.emptyMap(); // 默认空 Map，避免 null

        if (!CollectionUtils.isEmpty(franchiseStaffDtos)) {
            staffBrandIdToLimitMap = franchiseStaffDtos.stream()
                    .collect(Collectors.toMap(
                            FranchiseStaffDto::getBrandId,
                            FranchiseStaffDto::getDownloadLimit,
                            (existing, replacement) -> existing // 重复 brandId 保留第一个
                    ));
        }

        // 获取当前登录人的已下载次数
        List<FranchiseStaffDto> franchiseStaffDLCount = franchiseStaffMapper.selectDownloadCount(franchiseStaffDto.getId(), null);
        Map<Long, Integer> staffBrandIdToCountMap = Collections.emptyMap(); // 默认空 Map，避免 null

        if (!CollectionUtils.isEmpty(franchiseStaffDLCount)) {
            staffBrandIdToCountMap = franchiseStaffDLCount.stream()
                    .collect(Collectors.toMap(
                            FranchiseStaffDto::getBrandId,
                            FranchiseStaffDto::getDownloadCount,
                            (existing, replacement) -> existing // 重复 brandId 保留第一个
                    ));
        }

        // 4. 判断是否为管理员，如果是，直接返回主 List（已按从 List 1 替换）
        if (franchiseStaffMapper.selectAdmin(userId)) {
            replaceDownloadLimit(franchiseOwnerVO, staffBrandIdToLimitMap,staffBrandIdToCountMap, null);
            return ApiResult.ok(franchiseOwnerVO);
        }

        // 5. 获取角色 ID 列表，并查询从 List 2（franchiseRoleDownloadById）
        String roleIds = franchiseStaffDto.getRoleIds();
        Map<Long, Integer> roleBrandIdToLimitMap = Collections.emptyMap(); // 默认空 Map

        if (StringUtils.isNotEmpty(roleIds)) {
            List<Integer> integerRoleIds = Arrays.stream(roleIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            FranchiseStaffDto maxRoleDto = franchiseStaffMapper.selectMaxRoleDateDownloadLimit(integerRoleIds);

            if (maxRoleDto != null && maxRoleDto.getRoleId() != null) {
                List<SysRoleDateDownloadLimitVo> franchiseRoleDownloadById = franchiseRoleMapper.getFranchiseRoleDownloadById(maxRoleDto.getRoleId(), franchiseId);

                if (!CollectionUtils.isEmpty(franchiseRoleDownloadById)) {
                    roleBrandIdToLimitMap = franchiseRoleDownloadById.stream()
                            .collect(Collectors.toMap(
                                    SysRoleDateDownloadLimitVo::getBrandId,
                                    SysRoleDateDownloadLimitVo::getDownloadLimit,
                                    (existing, replacement) -> existing
                            ));
                }
            }
        }

        // 6. 按优先级替换 downloadLimit（从 List 1 > 从 List 2）
        replaceDownloadLimit(franchiseOwnerVO, staffBrandIdToLimitMap,staffBrandIdToCountMap, roleBrandIdToLimitMap);

        return ApiResult.ok(franchiseOwnerVO);
    }

    /**
     * 按优先级替换 downloadLimit
     *
     * @param franchiseOwnerVO 主 List
     * @param staffMap         从 List 1（优先级 1）
     * @param roleMap          从 List 2（优先级 2）
     * @param staffCountMap    员工对应品牌商已下载次数
     */
    private void replaceDownloadLimit(List<FranchiseOwnerVO> franchiseOwnerVO,
                                      Map<Long, Integer> staffMap,
                                      Map<Long, Integer> staffCountMap,
                                      Map<Long, Integer> roleMap) {
        franchiseOwnerVO.forEach(owner -> {
            Long brandId = owner.getBrandId();
            if (brandId == null) return;

            // 员工已下载次数
            if (staffCountMap != null && staffCountMap.containsKey(brandId)) {
                owner.setDownloadCount(staffCountMap.get(brandId));
            }

            // 优先级 1：从 List 1
            if (staffMap != null && staffMap.containsKey(brandId)) {
                owner.setDownloadLimit(staffMap.get(brandId));
                return;
            }

            // 优先级 2：从 List 2
            if (roleMap != null && roleMap.containsKey(brandId)) {
                owner.setDownloadLimit(roleMap.get(brandId));
            }
        });
    }

    @Override
    public ApiResult selectBrandSetDownload() {
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        List<SysRoleDateDownloadLimitVo> dateDownloadLimitVos = franchiseRoleMapper.selectBrandSetDownload(franchiseId);
        dateDownloadLimitVos.forEach(item -> {
            if (item.getDownloadLimit() != null) {
                item.setDownloadCount(item.getDownloadLimit());
            } else {
                item.setDownloadLimit(0);
                item.setDownloadCount(0);
            }
        });
        return ApiResult.ok(dateDownloadLimitVos);
    }

    @Override
    public List<FranchiseRoleVo> selectRoleServiceByUserId(Long userId) {
        return franchiseRoleMapper.selectRoleServiceByUserId(userId);
    }

    @Override
    public List<SysMenuDto> selectMenuServiceByUserId(Long userId) {
        List<SysMenuDto> sysMenuDtos = franchiseRoleMapper.selectMenuServiceByUserId(userId);
        List<SysMenuDto> distinctMenus = sysMenuDtos.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                SysMenuDto::getId,
                                menu -> menu,
                                (existing, replacement) -> existing // 保留第一个
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        return TreeUtils.listToTree(distinctMenus, 0L);
    }

    @Override
    public List<FranchiseRoleVo> selectRoleMarketByUserId(Long userId) {
        return franchiseRoleMapper.selectRoleMarketByUserId(userId);
    }

    @Override
    public List<SysMenuDto> selectMenuMarketByUserId(Long userId) {
        List<SysMenuDto> sysMenuDtos = franchiseRoleMapper.selectMenuMarketByUserId(userId);
        List<SysMenuDto> distinctMenus = sysMenuDtos.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                SysMenuDto::getId,
                                menu -> menu,
                                (existing, replacement) -> existing // 保留第一个
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        return TreeUtils.listToTree(distinctMenus, 0L);
    }
}
