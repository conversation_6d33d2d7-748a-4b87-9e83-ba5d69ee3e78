package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.DesignerSimpleVO;
import com.xgwc.user.entity.vo.DesignerVO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

public interface IDesignerService {
    /**
     * 查询设计师管理
     * 
     * @param designerId 设计师管理主键
     * @return 设计师管理
     */
    public DesignerVO selectDesignerByDesignerId(Long designerId);

    /**
     * 查询设计师管理列表
     * 
     * @param designer 设计师管理
     * @return 设计师管理集合
     */
    public List<DesignerVO> selectDesignerList(DesignerQueryDto designer);

    /**
     * 新增设计师管理
     * 
     * @param designer 设计师管理
     * @return 结果
     */
    public LoginResult insertDesigner(DesignerDto designer);

    /**
     * 修改设计师管理
     * 
     * @param designer 设计师管理
     * @return 结果
     */
    public int updateDesigner(DesignerVO designer);

    /**
     * 批量删除设计师管理
     * 
     * @param designerIds 需要删除的设计师管理主键集合
     * @return 结果
     */
    public int deleteDesignerByDesignerIds(Long[] designerIds);

    /**
     * 删除设计师管理信息
     * 
     * @param designerId 设计师管理主键
     * @return 结果
     */
    public int deleteDesignerByDesignerId(Long designerId);

    /**
     * 审核
     * @param updateReqVO
     */
    void audit(DesignerAuditDto updateReqVO);

    /**
     * 接单
     * @param updateReqVO
     */
    void receive(DesignerReceiveDto updateReqVO);

    /**
     * 重新申请
     * @param designer 设计师管理
     * @return 结果
     */
    int reapply(@Valid DesignerDto designer);

    /**
     * 根据分类ID获取用户列表
     * @param businessId 业务ID
     * @return 用户列表
     */
    List<SimpleUserInfoDto> getSimpleUserInfoListByBusinessId(Integer businessId);

    /**
     * 根据managerUserId获取加入的品牌商
     * @return 品牌商列表
     */
    ApiResult getInJoinBrandDropDown();

    /**
     * 获取本品牌下的分类设计师树
     * @return 列表
     */
    List<ClassifyDesignerTree> getClassifyDesignerTree();

    /**
     * 加盟商设计师列表
     * @param designerQueryDto 条件
     * @return 列表
     */
    List<DesignerVO> selectFranchiseeDesignerList(DesignerQueryDto designerQueryDto);

    /**
     * 设计师特长描述更新
     * @param updateReqVO 修改参数
     * @return 结果
     */
    int updateSpecialty(@Valid DesignerSpecialtyUpdateDto updateReqVO);

    /**
     * 设计师等级更新
     * @param updateReqVO 修改参数
     * @return 结果
     */
    int setGrade(@Valid DesignerGradeUpdateDto updateReqVO);

    /**
     * 设计师状态更新
     * @param updateReqVO 修改参数
     * @return 结果
     */
    int updateStatus(@Valid DesignerStatusUpdateDto updateReqVO);

    /**
     * 设计师申请记录列表
     * @param designer 搜索参数
     * @return 列表
     */
    List<DesignerVO> findApplyLogList(DesignerQueryDto designer);

    /**
     * 根据业务id获取设计师下拉列表
     * @param businessId 业务id
     * @return 详情
     */
    List<DesignerSimpleVO> getDesignerDropdownByBusinessId(Long businessId);

    /**
     * 获取品牌商下拉列表
     * @return 列表
     */
    List<DesignerSimpleVO> getBrandDesignerDropdown();

    /**
     * 根据业务id获取品牌商下拉列表
     * @param businessId 业务id
     * @return 列表
     */
    List<DesignerSimpleVO> getBrandDesignerByBusinessId(Long businessId);

    /**
     * 品牌商设计师列表统计
     * @return 统计数据
     */
    Map<String, Integer> statistics();

    /**
     * 加盟商设计师列表统计
     * @return 统计数据
     */
    Map<String, Integer> franchiseeStatistics();

    /**
     * 获取品牌商下的设计师下拉列表
     *
     * @param brandId 品牌id
     * @return 列表
     */
    List<Map<String, Object>> getBrandDesignerForBrand(Long brandId);
}
