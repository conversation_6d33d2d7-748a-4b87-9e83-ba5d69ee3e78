package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;


@Data
public class SysCompany {

private static final long serialVersionUID=1L;

    /** 自增id */
    private Long id;

    /** 上级id */
    private Long pid;

    /** 公司名称 */
    private String name;

    /** 品牌商id */
    private Long brandOwnerId;

    /** 公司类型 0-母公司 1-子公司 */
    private Integer companyType;

    /** 排序 */
    private Long orderNum;

    /** 状态：0正常，1无效 */
    private Integer status;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;



}