package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.SysMenu;
import com.xgwc.user.entity.vo.SysMenuVo;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.vo.SysMenuQueryVo;

public interface ISysMenuService  {
    /**
     * 查询字典数据
     * 
     * @param id 字典数据主键
     * @return 字典数据
     */
    public SysMenuDto selectSysMenuById(Long id);

    /**
     * 查询字典数据列表
     * 
     * @param sysMenu 字典数据
     * @return 字典数据集合
     */
    public List<SysMenuDto> selectSysMenuList(SysMenuQueryVo sysMenu);

    /**
     * 新增字典数据
     * 
     * @param sysMenu 字典数据
     * @return 结果
     */
    public int insertSysMenu(SysMenuVo sysMenu);

    /**
     * 修改字典数据
     * 
     * @param sysMenu 字典数据
     * @return 结果
     */
    public int updateSysMenu(SysMenuVo sysMenu);

    /**
     * 批量删除字典数据
     * 
     * @param ids 需要删除的字典数据主键集合
     * @return 结果
     */
    public int deleteSysMenuByIds(Long[] ids);

    /**
     * 删除字典数据信息
     * 
     * @param id 字典数据主键
     * @return 结果
     */
    public int deleteSysMenuById(Long id);

    /**
     * 根据modelType查询菜单列表
     * @param modelType
     * @return
     */
    public List<SysMenuDto> selectSysMenuByModelType(String modelType);
}
