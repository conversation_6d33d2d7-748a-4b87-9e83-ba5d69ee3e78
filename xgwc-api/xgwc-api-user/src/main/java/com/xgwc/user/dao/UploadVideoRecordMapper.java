package com.xgwc.user.dao;

import com.xgwc.user.entity.UploadVideoRecord;
import com.xgwc.user.entity.VideoInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UploadVideoRecordMapper {

    /**
     * 插入视频上传记录
     * @param uploadVideoRecord 参数
     * @return 插入结果
     */
    int insertUploadVideoRecord(UploadVideoRecord uploadVideoRecord);

    /**
     * 获取视频信息
     * @param id 视频ID
     * @return 视频信息
     */
    VideoInfo getUploadVideoRecordById(@Param(value = "id") int id);

    /**
     * 获取视频列表
     * @param videoIdList 视频ID
     * @return 视频信息
     */
    List<VideoInfo> getUploadVideoRecordList(@Param(value = "list") List<Integer> videoIdList);
}
