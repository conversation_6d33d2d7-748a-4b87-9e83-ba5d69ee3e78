package com.xgwc.user.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.FilterMenuListUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.common.util.TreeUtils;
import com.xgwc.user.dao.SysMenuMapper;
import com.xgwc.user.dao.XgwcBrandRoleMapper;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.dto.XgwcBrandRoleDto;
import com.xgwc.user.entity.param.XgwcBrandRoleParam;
import com.xgwc.user.entity.vo.SysMenuInfo;
import com.xgwc.user.entity.vo.SysMenuVo;
import com.xgwc.user.entity.vo.SysRoleDataVo;
import com.xgwc.user.entity.vo.SysRoleMenuVo;
import com.xgwc.user.entity.vo.XgwcBrandRoleVo;
import com.xgwc.user.service.XgwcBrandRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  10:52
 */
@Service
@Slf4j
public class XgwcBrandRoleServiceImpl implements XgwcBrandRoleService {

    @Resource
    private XgwcBrandRoleMapper xgwcBrandRoleMapper;

    @Resource
    private SysMenuMapper sysMenuMapper;

    public static final Long PARENT_ID = 0L;


    /**
     * 获取角色列表
     *
     * @param xgwcBrandRoleParam 查询参数
     * @return 角色列表
     */
    @Override
    public List<XgwcBrandRoleVo> getXgwcBrandRoleList(XgwcBrandRoleParam xgwcBrandRoleParam) {
        xgwcBrandRoleParam.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        return xgwcBrandRoleMapper.getXgwcBrandRoleList(xgwcBrandRoleParam);
    }

    /**
     * 新增角色
     *
     * @param xgwcBrandRoleDto 角色信息
     * @return 新增结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult saveXgwcBrandRole(XgwcBrandRoleDto xgwcBrandRoleDto) {
        if (xgwcBrandRoleDto == null) {
            return ApiResult.error("角色信息不能为空");
        }
        xgwcBrandRoleDto.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        xgwcBrandRoleDto.setCreateBy(SecurityUtils.getNickName());
        int roleResult = xgwcBrandRoleMapper.saveXgwcBrandRole(xgwcBrandRoleDto);
        if (roleResult <= 0) {
            log.error("新增失败，角色表影响行数：{}", roleResult);
            throw new ApiException("新增失败");
        }
        // 保存角色菜单权限/数据权限信息
        saveXgwcBrandRoleInfo(xgwcBrandRoleDto);
        return ApiResult.ok();
    }


    /**
     * 根据品牌角色ID获取品牌角色信息
     *
     * @param roleId 品牌角色ID
     * @return 角色信息
     */
    @Override
    public ApiResult getXgwcBrandRoleById(Integer roleId) {
        if (roleId == null) {
            return ApiResult.error("角色id不能为空");
        }
        XgwcBrandRoleVo xgwcBrandRoleVo = xgwcBrandRoleMapper.getXgwcBrandRoleById(roleId);
        if (xgwcBrandRoleVo == null) {
            return ApiResult.error("角色信息不存在");
        }

        List<SysRoleMenuVo> filteredMenuList = FilterMenuListUtil.filterList(
                xgwcBrandRoleMapper.getXgwcBrandRoleMenuById(roleId),
                roleMenuVo -> xgwcBrandRoleMapper.selectLastLevelMenu(roleMenuVo.getMenuId())
        );

        List<SysRoleDataVo> filteredMenuDateList = FilterMenuListUtil.filterList(
                xgwcBrandRoleMapper.getXgwcBrandRoleDataById(roleId),
                sysRoleDataVo -> xgwcBrandRoleMapper.selectLastLevelMenuData(sysRoleDataVo.getMenuId())
        );

        // 首先根据 menuId 分组，收集所有 deptId
        Map<Long, List<Long>> deptIdsByMenuId = filteredMenuDateList.stream()
                .collect(Collectors.groupingBy(
                        SysRoleDataVo::getMenuId,
                        Collectors.mapping(SysRoleDataVo::getDeptId, Collectors.toList())
                ));

        // 使用 toMap 收集器根据 menuId 去重
        Map<Long, SysRoleDataVo> uniqueByMenuId = filteredMenuDateList.stream()
                .collect(Collectors.toMap(
                        SysRoleDataVo::getMenuId,
                        vo -> {
                            SysRoleDataVo newVo = new SysRoleDataVo();
                            newVo.setMenuId(vo.getMenuId());
                            newVo.setDeptIds(deptIdsByMenuId.get(vo.getMenuId()));
                            newVo.setId(vo.getId());
                            newVo.setRoleId(vo.getRoleId());
                            newVo.setRoleName(vo.getRoleName());
                            newVo.setMenuName(vo.getMenuName());
                            newVo.setDataScope(vo.getDataScope());
                            newVo.setDataMasking(vo.getDataMasking());
                            newVo.setTimeLimit(vo.getTimeLimit());
                            newVo.setDownloadLimit(vo.getDownloadLimit());
                            newVo.setBusinessIds(vo.getBusinessIds());
                            return newVo;
                        },
                        (existing, replacement) -> existing // 保留第一个出现的对象
                ));
        List<SysRoleDataVo> uniqueList = new ArrayList<>(uniqueByMenuId.values());
        XgwcBrandRoleDto xgwcBrandRoleDto = new XgwcBrandRoleDto();
        BeanUtils.copyProperties(xgwcBrandRoleVo, xgwcBrandRoleDto);
        xgwcBrandRoleDto.setMenuIds(filteredMenuList);
        xgwcBrandRoleDto.setDataMenuIds(uniqueList);
        return ApiResult.ok(xgwcBrandRoleDto);
    }

    /**
     * 修改品牌角色信息
     *
     * @param xgwcBrandRoleDto 角色信息
     * @return 修改结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateXgwcBrandRole(XgwcBrandRoleDto xgwcBrandRoleDto) {
        if (xgwcBrandRoleDto == null) {
            return ApiResult.error("角色信息不能为空");
        }
        Long roleId = xgwcBrandRoleDto.getRoleId();
        if (roleId == null) {
            return ApiResult.error("角色ID不能为空");
        }
        try {
            String nickName = SecurityUtils.getNickName();
            xgwcBrandRoleDto.setCreateBy(nickName);
            xgwcBrandRoleDto.setUpdateBy(nickName);
            //  更新角色信息
            int roleResult = xgwcBrandRoleMapper.updateXgwcBrandRole(xgwcBrandRoleDto);
            // 删除菜单权限信息
            int delMenuResult = xgwcBrandRoleMapper.deleteXgwcBrandRoleMenu(roleId);
            // 删除数据权限信息
            int delDataScopeResult = xgwcBrandRoleMapper.deleteXgwcBrandRoleDataScope(roleId);
            // 保存角色菜单权限/数据权限信息
            saveXgwcBrandRoleInfo(xgwcBrandRoleDto);
            if (roleResult > 0 && delMenuResult >= 0 && delDataScopeResult >= 0) {
                log.info("角色信息修改成功，角色ID为：{}", roleId);
                return ApiResult.ok();
            } else {
                log.error("角色信息修改部分失败，角色ID为：{}，操作结果：role={}, menuDel={}, dataDel={}",
                        roleId, roleResult, delMenuResult, delDataScopeResult);
                throw new ApiException("角色信息修改失败");
            }
        } catch (Exception e) {
            log.error("角色信息修改过程中发生异常，角色ID为：{}", roleId, e);
            throw e; // 继续抛出以触发事务回滚
        }
    }

    private void saveXgwcBrandRoleInfo(XgwcBrandRoleDto xgwcBrandRoleDto) {
        List<SysRoleMenuVo> menuIds = xgwcBrandRoleDto.getMenuIds();
        if(!CollectionUtils.isEmpty(menuIds)){
            xgwcBrandRoleDto.setCreateBy(SecurityUtils.getNickName());
            int menuResult = xgwcBrandRoleMapper.saveXgwcBrandRoleMenu(xgwcBrandRoleDto);
            if (menuResult <= 0) {
                log.error("新增失败，菜单表影响行数：{}", menuResult);
                throw new ApiException("新增失败");
            }
        }

        List<SysRoleDataVo> dataMenuIds = xgwcBrandRoleDto.getDataMenuIds();
        if (!CollectionUtils.isEmpty(dataMenuIds)) {
            for (SysRoleDataVo dataMenu : dataMenuIds) {
                dataMenu.setRoleId(xgwcBrandRoleDto.getRoleId());
                dataMenu.setCreateBy(SecurityUtils.getNickName());
                /*if (!CollectionUtils.isEmpty(deptIds)) {
                    int roleDataScope = xgwcBrandRoleMapper.saveXgwcBrandRoleDataScopeDeptId(dataMenu);
                    if (roleDataScope <= 0) {
                        log.error("新增失败，数据权限表影响行数：{}", roleDataScope);
                        throw new ApiException("新增失败");
                    }
                }else {*/
                int dataScopeResult = xgwcBrandRoleMapper.saveXgwcBrandRoleDataScope(dataMenu);
                if (dataScopeResult <= 0) {
                    log.error("新增失败，数据权限表影响行数：{}", dataScopeResult);
                    throw new ApiException("新增失败");
                }
            }
        }
    }

    /**
     * 更新品牌角色状态
     *
     * @param roleId 品牌角色ID
     * @param status 状态
     * @return 更新结果
     */
    @Override
    public ApiResult updateStatusById(Integer roleId, Integer status) {
        // 参数校验：确保 roleId 合法且 status 在允许范围内
        if (roleId == null || roleId <= 0) {
            log.error("无效的角色ID: {}", roleId);
            return ApiResult.error("更新角色状态失败");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新角色状态失败");
        }

        try {
            int i = xgwcBrandRoleMapper.updateStatusById(roleId, status);
            if (i > 0) {
                log.info("更新角色状态成功，roleId: {}, 新状态: {}", roleId, status);
                return ApiResult.ok();
            } else {
                log.error("更新角色状态失败，roleId: {}，未找到记录或状态未更新", roleId);
                return ApiResult.error("状态更新失败，id不存在或数据未找到");
            }
        } catch (Exception e) {
            log.error("更新角色状态时发生异常，roleId: {}, status: {}", roleId, status, e);
            return ApiResult.error("更新角色状态失败");
        }
    }

    /**
     * 获取菜单树形结构
     *
     * @return 菜单树形结构
     */
    @Override
    public ApiResult getMenuTreeData(String isFlag) {
        long startTime = System.currentTimeMillis();
        if (StringUtils.isEmpty(isFlag)
                || !"brandAdmin".equals(isFlag)
                && !"franchiseeAdmin".equals(isFlag)
                && !"serviceAdmin".equals(isFlag)){
            return ApiResult.error("参数错误");
        }
        Result result = getResult(isFlag,true);
        // 构建树形结构
        List<SysMenuInfo> menuInfos = result.menu().stream()
                .filter(item -> PARENT_ID.equals(item.getPid())) // 筛选顶级节点
                .map(item -> buildBusinessTree(item, result.businessMap(), result.pidToChildren(), false))
                .collect(Collectors.toList());
        log.info("<菜单树形结构>总耗时: " + (System.currentTimeMillis() - startTime) + " milliseconds");
        return ApiResult.ok(menuInfos);
    }

    @NotNull
    private Result getResult(String isFlag, Boolean menuORData) {
        // 获取所有业务信息

        List<SysMenuVo> menu = menuORData
                ? sysMenuMapper.getMenuTree(isFlag)
                : sysMenuMapper.getMenuTreeData(isFlag);

        // 构建ID到业务对象的映射，便于快速查找
        Map<Integer, SysMenuVo> businessMap = menu.stream()
                .collect(Collectors.toMap(
                        menuVo -> menuVo.getId().intValue(),
                        Function.identity()
                ));

        // 按父ID分组
        Map<Integer, List<SysMenuVo>> pidToChildren = menu.stream()
                .collect(Collectors.groupingBy(
                        menuVo -> menuVo.getPid().intValue()
                ));
        Result result = new Result(menu, businessMap, pidToChildren);
        return result;
    }

    private record Result(List<SysMenuVo> menu, Map<Integer, SysMenuVo> businessMap,
                          Map<Integer, List<SysMenuVo>> pidToChildren) {
    }

    private SysMenuInfo buildBusinessTree(SysMenuVo source,
                                          Map<Integer, SysMenuVo> businessMap,
                                          Map<Integer, List<SysMenuVo>> pidToChildren,
                                          Boolean isRoot) {
        SysMenuInfo target = new SysMenuInfo();
        BeanUtils.copyProperties(source, target);

        // 递归构建子树
        List<SysMenuVo> children = pidToChildren.getOrDefault(source.getId().intValue(), Collections.emptyList());
        target.setChiledrenList(children.stream()
                .map(child -> buildBusinessTree(child, businessMap, pidToChildren, isRoot))
                .collect(Collectors.toList()));

        // 判断是否为叶子节点（没有子节点）
        if (target.getChiledrenList().isEmpty() && isRoot) {
            // 创建固定的子节点
            List<SysMenuInfo> fixedChildren = new ArrayList<>();

            // 添加“全部数据”节点
            SysMenuInfo allData = new SysMenuInfo();
            allData.setName("全部数据");
            // 设置其他属性
            fixedChildren.add(allData);

            // 添加“部门数据”节点
            SysMenuInfo departmentData = new SysMenuInfo();
            departmentData.setName("部门数据");
            // 设置其他属性
            fixedChildren.add(departmentData);

            // 添加“指定部门数据”节点
            SysMenuInfo personalData = new SysMenuInfo();
            personalData.setName("指定部门数据");
            // 设置其他属性
            fixedChildren.add(personalData);

            // 设置固定子节点列表
            target.setChiledrenList(fixedChildren);
        }

        return target;
    }

    /**
     * 获取角色数据权限
     *
     * @return 角色数据权限
     */
    @Override
    public ApiResult getRoleDataScope() {
        long startTime = System.currentTimeMillis();
        Result result = getResult(null,false);

        // 定义需要的第一级 menu_id
        List<Integer> topLevelMenuIds = Arrays.asList(39, 32, 34, 88);

        // 构建树形结构，只处理第一级为指定ID的节点
        List<SysMenuInfo> menuInfos = result.menu.stream()
                .filter(item -> topLevelMenuIds.contains(item.getId().intValue())) // 筛选指定的第一级节点
                .map(item -> buildBusinessTree(item, result.businessMap, result.pidToChildren, false))
                .collect(Collectors.toList());
        log.info("<角色数据权限>总耗时: " + (System.currentTimeMillis() - startTime) + " milliseconds");
        return ApiResult.ok(menuInfos);
    }

    @Override
    public Set<String> selectBrandRoleKeyByUserId(Long userId) {
        List<String> list = xgwcBrandRoleMapper.findBrandRoleKeyByUserId(userId);
        if(list != null && !list.isEmpty()){
            return list.stream().collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    @Override
    public List<SysMenuDto> selectMenuTreeByUserIdAndModelType(Long userId, String modelType) {
        List<SysMenuDto> list = xgwcBrandRoleMapper.findMenuByUserId(userId, modelType);
        List<SysMenuDto> tree = TreeUtils.listToTree(list, 0L);
        return tree;
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<XgwcBrandRoleVo> selectRoleByUserId(Long userId) {
        return xgwcBrandRoleMapper.selectRoleByUserId(userId);
    }

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    @Override
    public List<SysMenuDto> selectMenuByUserId(Long userId) {
        List<SysMenuDto> sysMenuDtos = xgwcBrandRoleMapper.selectMenuByUserId(userId);
        List<SysMenuDto> distinctMenus = sysMenuDtos.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                SysMenuDto::getId,
                                menu -> menu,
                                (existing, replacement) -> existing // 保留第一个
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        List<SysMenuDto> tree = TreeUtils.listToTree(distinctMenus, 0L);
        return tree;
    }

    @Override
    public void deleteBrandRoleUserByUserId(Long userId) {
        xgwcBrandRoleMapper.deleteBrandRoleUser(userId);
    }

    @Override
    public void saveBrandRoleUser(Long roleId, Long userId, Integer isFlag) {
        xgwcBrandRoleMapper.insertBrandRoleUser(roleId, userId, isFlag);
    }

    @Override
    public Set<String> selectSysMenuServiceByUserId(Long userId) {
        return sysMenuMapper.selectSysMenuServiceByUserId(userId);
    }

    @Override
    public Set<String> selectSysMenuMarketByUserId(Long userId) {
        return sysMenuMapper.selectSysMenuMarketByUserId(userId);
    }

    @Override
    public Set<String> selectMenuTreeByModelType(String modelType) {
        return sysMenuMapper.selectMenuTreeByModelType(modelType);
    }

    @Override
    public Set<String> selectSysMenuBrandByUserId(Long userId) {
        return sysMenuMapper.selectSysMenuBrandByUserId(userId);
    }

    @Override
    public Set<String> selectSysMenuFranchiseByUserId(Long userId) {
        return sysMenuMapper.selectSysMenuFranchiseByUserId(userId);
    }

}
