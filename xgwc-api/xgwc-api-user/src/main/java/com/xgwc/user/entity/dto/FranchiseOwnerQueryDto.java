package com.xgwc.user.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.FranchiseOwner;
import lombok.Data;


@Data
public class FranchiseOwnerQueryDto extends FranchiseOwner {

    private static final long serialVersionUID=1L;

    /** 公司名称 */
    @FieldDesc("公司名称")
    private String companyName;

    /** 公司简称 */
    @FieldDesc("公司简称")
    private String companySimpleName;

    /** 管理员手机号：加密 */
    @FieldDesc("管理员手机号")
    private String managerPhone;

    /** 状态：0正常，1禁用 */
    @FieldDesc("状态")
    private Integer status;

    private Long brandId;

    private Long franchiseId;

    @FieldDesc("是否设置")
    private Integer isConfigured;

}
