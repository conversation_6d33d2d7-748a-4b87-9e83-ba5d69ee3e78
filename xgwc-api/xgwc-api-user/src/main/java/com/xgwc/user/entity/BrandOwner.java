package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class BrandOwner {

    private static final long serialVersionUID=1L;

    /** 品牌ID */
    private Long brandId;

    /** 公司名称 */
    private String companyName;

    /** 公司简称 */
    private String companySimpleName;

    /** 联系人 */
    private String contact;

    /** 管理员手机号 */
    private String managerPhone;

    /** 密码 */
    private String password;

    /** 租户id */
    private Long tenantId;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 删除状态：0正常，1删除 */
    private Integer isDel;

    /** 审核原因 */
    private String reason;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;

    /** 用户ID */
    private Long userId;

}