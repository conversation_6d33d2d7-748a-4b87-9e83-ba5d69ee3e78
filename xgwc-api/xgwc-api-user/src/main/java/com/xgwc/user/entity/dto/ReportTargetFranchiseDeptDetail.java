package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.List;

@Data
public class ReportTargetFranchiseDeptDetail {

    //加盟商名称
    private String franchiseName;


    //加盟商id
    private Long franchiseId;

    /**
     * 目标ID
     */
    private Long targetId;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 目标日期
     */
    private String targetDate;

    //部门列表
    private List<ReportTargetFranchiseDeptDto> deptList;
}
