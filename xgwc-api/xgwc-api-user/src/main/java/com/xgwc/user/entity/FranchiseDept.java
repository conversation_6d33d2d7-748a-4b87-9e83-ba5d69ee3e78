package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

/**
 * 加盟商部门表
 */
@Data
public class FranchiseDept {

private static final long serialVersionUID=1L;



    /** 部门id */
    private Long deptId;

    /** 部门名称 */
    private String deptName;

    /** 加盟商id */
    private Long franchiseId;

    /** 是否删除：0正常，1删除 */
    private Integer isDel;

    /** 层级 */
    private Long level;

    /** 父类id */
    private Long pid;

    /** 排序：越小越前 */
    private Long sort;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;



}