package com.xgwc.user.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.jetbrains.annotations.NotNull;


@Data
public class DesignerDto {

    private static final long serialVersionUID=1L;

    /** 设计师ID */
    @FieldDesc("设计师ID")
    private Long designerId;

    /** 身份证正面：加密 */
    @FieldDesc("身份证正面")
    @NotNull("身份证正面不能为空")
    private String idcardFront;

    /** 身份证反面：加密 */
    @FieldDesc("身份证反面")
    @NotNull("身份证反面不能为空")
    private String idcardBack;

    /** 身份证是否长期：0长期，1非长期 */
    private Long idcardIsLongterm;

    /** 身份证开始时间 */
    @FieldDesc("身份证开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date idcardStart;

    /** 身份证结束时间 */
    @FieldDesc("身份证结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date idcardEnd;

    /** 姓名 */
    @FieldDesc("姓名")
    private String name;

    /** 身份证号：加密 */
    @FieldDesc("身份证号")
    @NotNull("身份证号不能为空")
    private String idcardNo;

    /** 手机号：加密 */
    @FieldDesc("手机号")
    @NotNull("手机号不能为空")
    private String phone;

    /** 邮箱：加密 */
    @FieldDesc("邮箱")
    @NotNull("邮箱不能为空")
    private String email;

    /** 紧急联系人 */
    @FieldDesc("紧急联系人")
    private String emergencyName;

    /** 紧急手机号 */
    @FieldDesc("紧急手机号")
    private String emergencyPhone;

    /** 擅长业务 */
    @FieldDesc("擅长业务")
    @NotNull("擅长业务不能为空")
    private Long goodBusiness;

    /** 描述 */
    @FieldDesc("描述")
    private String description;

    /** 开户名：加密 */
    @FieldDesc("开户名")
    @NotNull("开户名不能为空")
    private String bankUserName;

    /** 开户行 */
    @FieldDesc("开户行")
    private String bankName;

    /** 开户账号：加密 */
    @FieldDesc("开户账号")
    @NotNull("开户账号不能为空")
    private String bankNo;

    /** 支付宝姓名 */
    @FieldDesc("支付宝姓名")
    private String zfbName;

    /** 支付宝账号：加密 */
    @FieldDesc("支付宝账号")
    @NotNull("支付宝账号不能为空")
    private String zfbAccount;

    /** 管理员姓名 */
    @FieldDesc("管理员姓名")
    private String managerName;

    /** 管理员手机号：加密 */
    @FieldDesc("管理员手机号")
    @NotNull("管理员手机号不能为空")
    private String managerPhone;

    /** 验证码 */
    @FieldDesc("验证码")
    @NotNull("验证码不能为空")
    private String code;

    @FieldDesc("密码")
    @NotNull("密码不能为空")
    private String password;

    @FieldDesc("所属品牌商id")
    private Long brandId;

    @FieldDesc("管理员用户id")
    private Long managerUserId;

    private Long mainUserId;

    @FieldDesc("微信名称")
    @NotNull("微信名称不能为空")
    private String wechatName;

    @FieldDesc("微信收款码")
    @NotNull("微信收款码不能为空")
    private String wechatAccountUrl;

    @FieldDesc("支付宝收款码")
    @NotNull("支付宝收款码不能为空")
    private String alipayAccountUrl;
}
