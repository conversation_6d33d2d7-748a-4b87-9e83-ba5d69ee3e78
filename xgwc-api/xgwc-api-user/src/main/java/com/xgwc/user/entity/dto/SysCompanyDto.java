package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class SysCompanyDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("自增id")
    @Excel(name = "自增id")
    private Long id;

    @FieldDesc("上级id")
    @Excel(name = "上级id")
    private Long pid;

    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String name;

    @FieldDesc("所属公司名称")
    @Excel(name = "所属公司名称")
    private String parentCompany;

    @FieldDesc("公司类型 0-母公司 1-子公司")
    @Excel(name = "公司类型 0-母公司 1-子公司")
    private Integer companyType;

    @FieldDesc("排序")
    @Excel(name = "排序")
    private Long orderNum;

    @FieldDesc("状态：0正常，1无效")
    @Excel(name = "状态：0正常，1无效")
    private Integer status;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;





    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("pid",getPid())
            .append("name",getName())
            .append("orderNum",getOrderNum())
            .append("status",getStatus())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
        .toString();
        }
}
