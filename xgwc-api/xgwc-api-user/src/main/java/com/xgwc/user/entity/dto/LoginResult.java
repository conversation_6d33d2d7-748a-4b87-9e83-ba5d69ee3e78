package com.xgwc.user.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.util.List;

@Data
public class LoginResult {

    /**
     * token值
     */
    private String token;

    /**
     * token类型
     */
    private String tokenType;

    private Integer userType;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;

    /**
     * 主用户id
     */
    private Long mainUserId;

    /**
     * 角色信息
     */
    private List<SysUserMiddleDto> userMiddleList;

}
