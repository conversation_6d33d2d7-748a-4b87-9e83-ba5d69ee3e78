package com.xgwc.user.dao;

import com.xgwc.user.entity.dto.notice.BrandNoticeClassifyCountDto;
import com.xgwc.user.entity.dto.notice.NoticeDto;
import com.xgwc.user.entity.dto.notice.SelfNoticeDto;
import com.xgwc.user.entity.dto.notice.SimpleBrandDto;
import com.xgwc.user.entity.vo.NoticeVo;
import com.xgwc.user.entity.vo.SelectNoticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NoticeMapper {

    /**
     * 插入通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int insertNotice(NoticeVo noticeVo);

    /**
     * 更新通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int updateNotice(NoticeVo noticeVo);

    /**
     * 更新已发送通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int updateSendedNotice(NoticeVo noticeVo);


    /**
     * 更新已发送通知
     * @param noticeId 通知id
     * @param sendStatus 发送状态
     * @return 是否成功
     */
    int updateSendStatusNotice(@Param(value = "noticeId") Integer noticeId, @Param(value = "sendStatus") Integer sendStatus);

    /**
     * 删除通用
     * @param noticeVo 参数
     * @return 是否成功
     */
    int deleteNotice(NoticeVo noticeVo);

    /**
     * 根据通知id获取通知详情
     * @param noticeVo 参数
     * @return 通知详情
     */
    NoticeDto getNoticeByCondition(NoticeVo noticeVo);

    /**
     * 根据通知id获取通知详情
     * @param noticeId 通知id
     * @return 通知详情
     */
    NoticeDto getNoticeById(Integer noticeId);

    /**
     * 根据通知id获取通知详情
     * @param noticeId 参数
     * @return 通知详情
     */
    SelfNoticeDto getSelfNoticeById(@Param(value = "noticeId") Integer noticeId, @Param(value = "tableName") String tableName, @Param(value = "userId") Long userId);

    /**
     * 获取列表
     * @param selectNoticeVo 参数
     * @return 列表
     */
    List<NoticeDto> getNoticeList(SelectNoticeVo selectNoticeVo);

    /**
     * 获取列表
     * @param tableName 表名
     * @param userId 用户ID
     * @param classifyId 分类ID
     * @return 列表
     */
    List<SelfNoticeDto> getBrandSelfNoticeList(@Param(value = "tableName") String tableName, @Param(value = "userId") Long userId,
                                               @Param(value = "classifyId") String classifyId, @Param(value = "brandId") Long brandId);

    /**
     * 仅获取加盟商的消息列表
     * @param tableName 表名
     * @param userId 用户ID
     * @return 列表
     */
    List<SelfNoticeDto> getFranchiseNoticeList(@Param(value = "tableName") String tableName, @Param(value = "userId") Long userId
            , @Param(value = "sourceId") Long sourceId, @Param(value = "classifyId") String classifyId);

    /**
     * 获取列表
     * @param tableName 表名
     * @param userId 用户ID
     * @return 列表
     */
    List<SelfNoticeDto> getAllFranchiseNoticeList(@Param(value = "tableName") String tableName, @Param(value = "userId") Long userId, @Param(value = "sourceId") Long sourceId);



    /**
     * 批量插入通知详情
     * @param userIds 用户id
     * @param noticeId 通知id
     * @return 是否成功
     */
    int batchInsertNoticeDetails(@Param(value = "tableName") String tableName, @Param(value = "userIds") List<Long> userIds, @Param(value = "noticeId") Integer noticeId);

    /**
     * 读取状态
     * @param noticeId 通知id
     * @return 是否成功
     */
    Integer getReadStatus(@Param(value = "tableName") String tableName, @Param(value = "noticeId") Integer noticeId, @Param(value = "userId") Long userId);

    /**
     * 更新读取状态
     * @param noticeId 通知id
     * @return 是否成功
     */
    int updateReadStatus(@Param(value = "tableName") String tableName, @Param(value = "noticeId") Integer noticeId, @Param(value = "userId") Long userId);

    /**
     * 更新已读数量
     * @param noticeId 通知id
     * @return 是否成功
     */
    int updateReadCount(Integer noticeId);

    /**
     * 获取品牌商通知分类
     * @param tableName 表名
     * @param userId 用户ID
     * @return 通知数
     */
    List<BrandNoticeClassifyCountDto> getBrandNoticeClassifyCountDtoByUserId(@Param(value = "tableName") String tableName
            , @Param(value = "userId") Long userId, @Param(value = "sourceId") Long sourceId);

    /**
     * 获取加盟商通知分类
     * @param tableName 表名
     * @param userId 用户ID
     * @return 通知数
     */
    List<BrandNoticeClassifyCountDto> getFranchiseNoticeClassifyCountDtoByUserId(@Param(value = "tableName") String tableName
            , @Param(value = "userId") Long userId, @Param(value = "sourceId") Long sourceId);

    /**
     * 获取品牌分组
     * @param tableName 表名
     * @param userId 用户名
     * @return 品牌分组
     */
    List<SimpleBrandDto> getSimpleBrandDtoByUserId(@Param(value = "tableName")String tableName,@Param(value = "userId") Long userId);

    /**
     * 个人是否存在本部门通知
     * @param tableName 表名
     * @param userId 用户ID
     * @return 是否存在
     */
    int existFranchiseNotice(@Param(value = "tableName") String tableName, @Param(value = "userId") Long userId, @Param(value = "sourceId") Long sourceId);

}
