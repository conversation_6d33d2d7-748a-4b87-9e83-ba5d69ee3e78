package com.xgwc.user.controller;

import java.util.List;

import com.xgwc.user.entity.dto.LoginResult;
import com.xgwc.user.entity.dto.ServiceOwnerSimpleDto;
import com.xgwc.user.entity.vo.ServiceApplyVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.dto.ServiceOwnerDto;
import com.xgwc.user.entity.vo.ServiceOwnerQueryVo;
import com.xgwc.user.service.IServiceOwnerService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

/**
 * 服务商
 */
@RestController
@RequestMapping("/serviceOwner")
public class ServiceOwnerController extends BaseController {
    @Autowired
    private IServiceOwnerService serviceOwnerService;

    /**
     * 查询服务商列表
     */
    @MethodDesc("查询服务商列表")
    @PreAuthorize("@ss.hasPermission('serviceOwner:serviceOwner:list')")
    @GetMapping("/list")
    public ApiResult<ServiceOwnerDto> list(ServiceOwnerQueryVo serviceOwner) {
        startPage();
        List<ServiceOwnerDto> list = serviceOwnerService.selectServiceOwnerList(serviceOwner);
        return getDataTable(list);
    }

    /**
     * 获取服务商详细信息
     */
    @MethodDesc("获取服务商详细信息")
    @PreAuthorize("@ss.hasPermission('serviceOwner:serviceOwner:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<ServiceOwnerDto> getInfo(@PathVariable("id") Long id) {
        return success(serviceOwnerService.selectServiceOwnerById(id));
    }

    /**
     * 申请服务商
     */
    @MethodDesc("申请服务商")
    @Log(title = "服务商", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult<LoginResult> add(@RequestBody ServiceApplyVo serviceApplyVo) {
        return ApiResult.ok(serviceOwnerService.applyServiceOwner(serviceApplyVo));
    }

    /**
     * 开启服务商
     */
    @MethodDesc("开启服务商")
    @PreAuthorize("@ss.hasPermission('serviceOwner:serviceOwner:edit')")
    @Log(title = "服务商", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/enable/{id}")
    public ApiResult enable(@PathVariable("id") Long id) {
        return toAjax(serviceOwnerService.enableServiceOwner(id));
    }

    /**
     * 下拉框选择财务服务商
     * @return 财务服务商列表
     */
    @MethodDesc("下拉框选择服务商")
    @GetMapping("/select")
    public ApiResult<List<ServiceOwnerSimpleDto>> select() {
        return success(serviceOwnerService.selectServiceOwnerListForSelect());
    }

    /**
     * 下拉销售服务商
     * @return 销售服务商列表
     */
    @MethodDesc("下拉销售服务商")
    @GetMapping("/selectSales")
    public ApiResult<List<ServiceOwnerSimpleDto>> selectSales() {
        return success(serviceOwnerService.selectSalesServiceOwnerListForSelect());
    }
}
