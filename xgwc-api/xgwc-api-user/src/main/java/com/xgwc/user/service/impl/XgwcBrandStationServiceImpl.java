package com.xgwc.user.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.XgwcBrandStationMapper;
import com.xgwc.user.entity.dto.XgwcBrandStationDto;
import com.xgwc.user.entity.param.XgwcBrandStationParam;
import com.xgwc.user.entity.vo.XgwcBrandStationVo;
import com.xgwc.user.service.XgwcBrandStationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  10:43
 */
@Service
@Slf4j
public class XgwcBrandStationServiceImpl implements XgwcBrandStationService {


    @Resource
    private XgwcBrandStationMapper xgwcBrandStationMapper;

    /**
     * 获取品牌列表
     * @param xgwcBrandStationParam 查询参数
     * @return 岗位列表
     */
    @Override
    public List<XgwcBrandStationVo> getXgwcBrandStationList(XgwcBrandStationParam xgwcBrandStationParam) {
        xgwcBrandStationParam.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        return xgwcBrandStationMapper.getXgwcBrandStationList(xgwcBrandStationParam);
    }

    /**
     * 保存岗位信息
     * @param xgwcBrandStationDto 岗位信息
     * @return ApiResult
     */
    @Override
    public ApiResult saveXgwcBrandStation(XgwcBrandStationDto xgwcBrandStationDto) {
        // 1. 参数校验
        if (xgwcBrandStationDto == null) {
            log.warn("保存品牌失败：参数不能为空");
            return ApiResult.error("品牌信息不能为空");
        }

        // 2. 执行保存操作
        try {
            xgwcBrandStationDto.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
            xgwcBrandStationDto.setCreateBy(SecurityUtils.getNickName());
            int affectedRows = xgwcBrandStationMapper.saveXgwcBrandStation(xgwcBrandStationDto);
            if (affectedRows <= 0) {
                log.error("保存品牌失败，未影响任何行，stationId={}", xgwcBrandStationDto.getStationId());
                return ApiResult.error("保存品牌失败");
            }
            log.info("保存品牌成功，stationId={}", xgwcBrandStationDto.getStationId());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("保存品牌异常，stationId={}, 错误信息：{}",
                    xgwcBrandStationDto.getStationId(), e.getMessage(), e);
            return ApiResult.error("系统异常，保存失败");
        }
    }

    /**
     * 根据ID查询岗位信息
     * @param stationId 岗位id
     * @return ApiResult
     */
    @Override
    public ApiResult getXgwcBrandStationById(Integer stationId) {
        // 1. 参数校验
        if (stationId == null) {
            log.warn("查询品牌失败：stationId不能为空");
            return ApiResult.error("品牌ID不能为空");
        }

        // 2. 查询数据
        try {
            XgwcBrandStationVo resultList = xgwcBrandStationMapper.getXgwcBrandStationById(stationId);

            // 3. 处理空结果
            if (ObjectUtils.isEmpty(resultList)) {
                log.warn("未找到品牌信息，stationId={}", stationId);
                return ApiResult.error("品牌不存在");
            }
            return ApiResult.ok(resultList);
        } catch (Exception e) {
            log.error("查询品牌异常，stationId={}, 错误信息：{}", stationId, e.getMessage(), e);
            return ApiResult.error("系统异常，查询失败");
        }
    }

    /**
     * 更新岗位信息
     * @param xgwcBrandStationDto 岗位信息
     * @return ApiResult
     */
    @Override
    public ApiResult updateXgwcBrandStation(XgwcBrandStationDto xgwcBrandStationDto) {
        // 1. 参数校验
        if (xgwcBrandStationDto == null || xgwcBrandStationDto.getStationId() == null) {
            log.warn("更新品牌失败：参数或stationId为空");
            return ApiResult.error("品牌信息不完整");
        }

        // 2. 执行更新
        try {
            xgwcBrandStationDto.setUpdateBy(SecurityUtils.getNickName());
            int affectedRows = xgwcBrandStationMapper.updateXgwcBrandStation(xgwcBrandStationDto);
            if (affectedRows <= 0) {
                log.error("更新品牌失败，未影响任何行，stationId={}", xgwcBrandStationDto.getStationId());
                return ApiResult.error("更新品牌失败，可能ID不存在");
            }
            log.info("更新品牌成功，stationId={}", xgwcBrandStationDto.getStationId());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新品牌异常，stationId={}, 错误信息：{}",
                    xgwcBrandStationDto.getStationId(), e.getMessage(), e);
            return ApiResult.error("系统异常，更新失败");
        }
    }

    /**
     * 更新品牌状态
     * @param stationId 岗位id
     * @param status 状态
     * @return ApiResult
     */
    @Override
    public ApiResult updateStatusById(Integer stationId, Integer status) {
        // 1. 参数校验
        if (stationId == null ) {
            log.warn("更新品牌状态失败：stationId或status为空");
            return ApiResult.error("参数不完整");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新品牌状态失败");
        }
        // 2. 执行更新
        try {
            int affectedRows = xgwcBrandStationMapper.updateStatusById(stationId, status);
            if (affectedRows <= 0) {
                log.error("更新品牌状态失败，未影响任何行，stationId={}", stationId);
                return ApiResult.error("更新状态失败，可能ID不存在");
            }
            log.info("更新品牌状态成功，stationId={}, status={}", stationId, status);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新品牌状态异常，stationId={}, 错误信息：{}", stationId, e.getMessage(), e);
            return ApiResult.error("系统异常，更新失败");
        }
    }
}
