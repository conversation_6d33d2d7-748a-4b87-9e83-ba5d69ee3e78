package com.xgwc.user.entity.dto;

import com.xgwc.user.entity.ReportTargetFranchiseStaff;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportTargetFranchiseStaffList {

    private Long brandId;

    private String brandName;

    private Long targetId;

    private String targetName;

    /**目标金额*/
    private BigDecimal targeAmount;

    /** 新客目标 */
    private BigDecimal targetNewCustomer;

    /** 老客目标 */
    private BigDecimal targetOldCustomer;

    /** 转介绍目标 */
    private BigDecimal targetTransferAmount;

    /** 佣金比例目标 */
    private BigDecimal targetComissionRate;

    /** 新客转化率目标 */
    private BigDecimal targetConversionRate;

    /**
     * 员工列表
     */
    private List<ReportTargetFranchiseStaffDto> staffList;

}
