package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: k<PERSON><PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-04-22  15:12
 */

/**
 * 加盟商岗位表
 */
@Data
public class FranchiseStationDto {

    /**
     * 岗位id
     */
    @FieldDesc(value = "岗位id")
    private Long stationId;

    /**
     * 岗位名称
     */
    @FieldDesc(value = "岗位名称")
    private String stationName;

    @FieldDesc(value = "部门id")
    private Long deptId;

    /**
     * 加盟商id
     */
    @FieldDesc(value = "加盟商id")
    private Long franchiseId;

    /**
     * 排序：越小越前
     */
    @FieldDesc(value = "排序：越小越前")
    private Integer sort;

    /**
     * 状态：0正常，1禁用
     */
    @FieldDesc(value = "状态：0正常，1禁用")
    private Integer status;

    /**
     * 是否删除：0否，1是
     */
    @FieldDesc(value = "是否删除：0否，1是")
    private Integer isDel;

    /** 创建人 */
    @FieldDesc(value = "创建人")
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @FieldDesc(value = "创建时间")
    private Date createTime;

    /** 修改人 */
    @FieldDesc(value = "修改人")
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @FieldDesc(value = "修改时间")
    private String updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @FieldDesc(value = "行更新时间")
    private String modifyTime;
}
