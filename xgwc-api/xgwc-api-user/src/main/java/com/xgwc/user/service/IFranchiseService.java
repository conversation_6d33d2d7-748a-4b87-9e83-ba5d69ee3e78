package com.xgwc.user.service;

import com.xgwc.user.entity.dto.FranchiseDto;
import com.xgwc.user.entity.vo.FranchiseVo;

import java.util.List;

public interface IFranchiseService  {

    /**
     * 根据管理员id查询加盟商信息
     * @param managerUserId 管理员id
     */
    FranchiseDto getFranchiseByManagerUserId(Long managerUserId);

    /**
     * 新增加盟商主体信息表
     * 
     * @param franchise 加盟商主体信息表
     * @return 结果
     */
    int insertFranchise(FranchiseVo franchise);

    /**
     * 根据id查询加盟商信息
     * @param id 加盟商id
     * @return 加盟商信息
     */
    FranchiseDto selectFranchiseById(Long id);

    List<com.xgwc.user.feign.entity.FranchiseDto> listByIds(List<Long> ids);

}
