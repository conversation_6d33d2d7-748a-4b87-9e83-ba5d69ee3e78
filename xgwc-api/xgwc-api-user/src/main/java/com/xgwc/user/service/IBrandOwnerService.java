package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.BrandOwner;
import com.xgwc.user.entity.dto.BrandOwnerAddResultDto;
import com.xgwc.user.entity.dto.BrandOwnerSimpleDto;
import com.xgwc.user.entity.dto.LoginResult;
import com.xgwc.user.entity.vo.BrandApplyVo;
import com.xgwc.user.entity.vo.BrandOwnerVo;
import com.xgwc.user.entity.dto.BrandOwnerDto;
import com.xgwc.user.entity.vo.BrandOwnerQueryVo;
import jakarta.validation.Valid;

public interface IBrandOwnerService  {
    /**
     * 查询品牌商管理
     * 
     * @param brandId 品牌商管理主键
     * @return 品牌商管理
     */
    public BrandOwnerDto selectBrandOwnerByBrandId(Long brandId);

    /**
     * 查询品牌商管理列表
     * 
     * @param brandOwner 品牌商管理
     * @return 品牌商管理集合
     */
    public List<BrandOwnerDto> selectBrandOwnerList(BrandOwnerQueryVo brandOwner);

    /**
     * 新增品牌商管理
     * 
     * @param brandOwner 品牌商管理
     * @return 结果
     */
    public long insertBrandOwner(BrandOwnerVo brandOwner);

    /**
     * 修改品牌商管理
     * 
     * @param brandOwner 品牌商管理
     * @return 结果
     */
    public int updateBrandOwner(BrandOwnerVo brandOwner);

    /**
     * 下拉选择加盟商加入了哪些品牌商
     * @return 品牌商列表
     */
    List<BrandOwnerDto> selectBrandList();

    List<BrandOwnerSimpleDto> getAllBrandOwner();

    /**
     * 申请加入品牌商
     * @param brandApplyVo 申请信息
     * @return 结果
     */
    LoginResult brandApple(@Valid BrandApplyVo brandApplyVo);

    /**
     * 开启
     * @param brandId 品牌商id
     * @return 结果
     */
    int openBrandOwner(Long brandId);

    /**
     * 根据管理员id查询品牌商信息
     * @param managerUserId 管理员id
     * @return 结果
     */
    public BrandOwnerDto selectBrandOwnerByManagerUserId(Long managerUserId);
}
