package com.xgwc.user.controller;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.XgwcBrandDeptDto;
import com.xgwc.user.entity.param.XgwcBrandDeptParam;
import com.xgwc.user.entity.vo.XgwcBrandDeptInfo;
import com.xgwc.user.service.XgwcBrandDeptService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.controller
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 组织管理-部门管理
 */
@RestController
@RequestMapping("/xgwcBrandDept")
@Slf4j
public class XgwcBrandDeptController extends BaseController {

    @Resource
    private XgwcBrandDeptService xgwcBrandDeptService;

    /**
     * @param xgwcBrandDeptParam 查询条件
     * @return 部门管理列表
     * 查询部门管理列表
     */
    @MethodDesc("查询部门管理列表")
    @PreAuthorize("@ss.hasPermission('user:dept:list')")
    @PostMapping("/getXgwcBrandDeptList")
    public ApiResult<XgwcBrandDeptInfo> getXgwcBrandDeptList(@RequestBody XgwcBrandDeptParam xgwcBrandDeptParam) {
        startPage();
        return getDataTable(xgwcBrandDeptService.getXgwcBrandDeptList(xgwcBrandDeptParam));
    }

    /**
     * 查询部门管理树
     *
     * @return 部门管理树
     */
    @MethodDesc("查询部门管理树")
    @GetMapping("/getBrandDeptTree")
    public ApiResult getBrandDeptTree(@RequestParam(required = false) Integer companyId) {

        try {
            XgwcBrandDeptParam xgwcBrandDeptParam = new XgwcBrandDeptParam();
            xgwcBrandDeptParam.setCompanyId(companyId);
            xgwcBrandDeptParam.setStatus(0);
            List<XgwcBrandDeptInfo> result = xgwcBrandDeptService.getXgwcBrandDeptList(xgwcBrandDeptParam);

            List<XgwcBrandDeptInfo> safeResult = Optional.ofNullable(result).orElse(Collections.emptyList());

            return ApiResult.ok(safeResult);
        } catch (Exception e) {
            log.error("获取部门管理树失败", e);
            return ApiResult.error( "获取部门管理树失败");
        }
    }

    /**
     * 查询部门管理树（一级目录为所属公司）
     *
     * @return 部门管理树
     */
    @MethodDesc("查询部门管理树（一级目录为所属公司）")
    @GetMapping("/getBrandDeptAndCompanyTree")
    public ApiResult getBrandDeptAndCompanyTree() {

        try {
            List<XgwcBrandDeptInfo> result = xgwcBrandDeptService.getXgwcBrandDeptAndCompanyList();

            List<XgwcBrandDeptInfo> safeResult = Optional.ofNullable(result).orElse(Collections.emptyList());

            return ApiResult.ok(safeResult);
        } catch (Exception e) {
            log.error("获取部门管理树失败", e);
            return ApiResult.error( "获取部门管理树失败");
        }
    }


    /**
     * @param xgwcBrandDeptDto 新增部门管理信息
     * @return 插入结果
     * 新增部门管理信息
     */
    @MethodDesc("新增部门管理信息")
    @Submit(fileds = "userId")
    @PreAuthorize("@ss.hasPermission('user:dept:insert')")
    @PostMapping("/saveXgwcBrandDept")
    public ApiResult saveXgwcBrandDept(@RequestBody XgwcBrandDeptDto xgwcBrandDeptDto) {
        return xgwcBrandDeptService.saveXgwcBrandDept(xgwcBrandDeptDto);
    }

    /**
     * @param deptId 部门管理id
     * @return 部门管理信息
     * 根据id查询部门管理信息
     */
    @MethodDesc("根据id查询部门管理信息")
    @PreAuthorize("@ss.hasPermission('user:dept:update')")
    @GetMapping("/getXgwcBrandDeptById/{deptId}")
    public ApiResult getXgwcBrandDeptById(@PathVariable("deptId") Long deptId) {
        return xgwcBrandDeptService.getXgwcBrandDeptById(deptId);
    }

    /**
     * @param xgwcBrandDeptDto 修改信息
     * @return 修改结果
     * 修改部门管理信息
     */
    @MethodDesc("修改部门管理信息")
    @Submit(fileds = "userId")
    @PreAuthorize("@ss.hasPermission('user:dept:update')")
    @PostMapping("/updateXgwcBrandDept")
    public ApiResult updateXgwcBrandDeptById(@RequestBody XgwcBrandDeptDto xgwcBrandDeptDto) {
        return xgwcBrandDeptService.updateXgwcBrandDeptById(xgwcBrandDeptDto);
    }

    /**
     * @param deptId 部门id
     * @return 品牌信息
     * 根据id修改状态
     */
    @MethodDesc("修改部门状态")
    @PreAuthorize("@ss.hasPermission('user:dept:updateStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "deptId") Integer deptId,
                                 @RequestParam(value = "status") Integer status) {
        return xgwcBrandDeptService.updateStatusById(deptId, status);
    }

    /**
     * 查询回显部门详情信息-负责人、助理、是否排班
     *
     * @param deptId 部门id
     * @return 部门详情信息-负责人、助理、是否排班
     */

    @MethodDesc("查询回显部门详情信息-负责人、助理、是否排班")
    @PreAuthorize("@ss.hasPermission('user:dept:query')")
    @GetMapping("/getXgwcBrandDeptInfo")
    public ApiResult getXgwcDeptStaffInfo(@RequestParam(value = "deptId") Integer deptId) {
        return xgwcBrandDeptService.getXgwcDeptStaffInfo(deptId);
    }

    /**
     * 修改部门详情信息-负责人、助理、是否排班
     *
     * @param xgwcBrandDeptDto 部门详情信息-负责人、助理、是否排班
     * @return 部门详情信息-负责人、助理、是否排班
     */
    @MethodDesc("修改部门详情信息-负责人、助理、是否排班")
    @PreAuthorize("@ss.hasPermission('user:dept:update')")
    @PostMapping("/getXgwcDeptStaffSchedule")
    public ApiResult getXgwcDeptStaffSchedule(@RequestBody XgwcBrandDeptDto xgwcBrandDeptDto) {
        return xgwcBrandDeptService.updateXgwcDeptStaffInfo(xgwcBrandDeptDto);
    }

}
