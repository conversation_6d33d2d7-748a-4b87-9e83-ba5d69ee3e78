package com.xgwc.user.dao;

import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.dto.SysSmsSend;
import org.apache.ibatis.annotations.Param;

@TenantIgnore
public interface SmsSendMapper {

    /**
     * 插入短信发送记录
     * @param phoneNum 手机号
     * @param message 短信内容
     * @param smsType 短信类型
     * @param status 发送状态
     * @return 是否成功
     */
    int insertSmsSendRecord(@Param(value = "photoNum") String phoneNum, @Param(value = "message") String message,
                            @Param(value = "smsType") Integer smsType, @Param(value = "status") Integer status);

    /**
     * 根据手机号查询记录
     * @param phoneNum 手机号
     * @param message 短信内容
     * @return 记录
     */
    SysSmsSend findSmsSendByPhoneNum(@Param(value = "phoneNum") String phoneNum, @Param(value = "message") String message);
}
