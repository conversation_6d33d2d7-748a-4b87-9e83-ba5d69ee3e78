package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.BrandOwnerDto;
import com.xgwc.user.entity.dto.LoginResult;
import com.xgwc.user.entity.vo.BrandApplyVo;
import com.xgwc.user.entity.vo.BrandOwnerQueryVo;
import com.xgwc.user.entity.vo.BrandOwnerVo;
import com.xgwc.user.service.IBrandOwnerService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/user/brandOwner")
@Validated
public class BrandOwnerController extends BaseController
{
    @Autowired
    private IBrandOwnerService brandOwnerService;

    /**
     * 查询品牌商管理列表
     */
    @MethodDesc("查询品牌商管理列表")
    @PreAuthorize("@ss.hasPermission('user:brandOwner:list')")
    @GetMapping("/list")
    public ApiResult<BrandOwnerDto> list(BrandOwnerQueryVo brandOwner) {
        startPage();
        List<BrandOwnerDto> list = brandOwnerService.selectBrandOwnerList(brandOwner);
        return getDataTable(list);
    }

    /**
     * 获取品牌商管理详细信息
     */
    @MethodDesc("获取品牌商管理详细信息")
    @PreAuthorize("@ss.hasPermission('user:brandOwner:query')")
    @GetMapping(value = "/{brandId}")
    public ApiResult<BrandOwnerDto> getInfo(@PathVariable("brandId") Long brandId) {
        return success(brandOwnerService.selectBrandOwnerByBrandId(brandId));
    }

    /**
     * 新增品牌商管理
     */
    @MethodDesc("品牌商申请")
    @Log(title = "品牌商管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult<LoginResult> add(@RequestBody @Valid BrandApplyVo brandApplyVo) {
        return ApiResult.ok(brandOwnerService.brandApple(brandApplyVo));
    }

    /**
     * 修改品牌商管理
     */
    @MethodDesc("修改品牌商管理")
    @PreAuthorize("@ss.hasPermission('user:brandOwner:edit')")
    @Log(title = "品牌商管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody @Valid BrandOwnerVo brandOwner) {
        return toAjax(brandOwnerService.updateBrandOwner(brandOwner));
    }

    /**
     * 下拉选择加盟商加入了哪些品牌商
     */
    @MethodDesc("下拉选择加盟商加入了哪些品牌商")
    @GetMapping("/selectBrandList")
    public ApiResult selectBrandList() {
        return success(brandOwnerService.selectBrandList());
    }

    /**
     * 品牌商下拉选择
     */
    @MethodDesc("品牌商下拉选择")
    @GetMapping("/getAll")
    public ApiResult getAllBrandOwner() {
        return success(brandOwnerService.getAllBrandOwner());
    }

    /**
     * 开启品牌商
     */
    @MethodDesc("开启品牌商")
    @PreAuthorize("@ss.hasPermission('user:brandOwner:edit')")
    @Log(title = "品牌商管理", businessType = BusinessType.UPDATE)
    @PutMapping("/openBrandOwner/{brandId}")
    public ApiResult openBrandOwner(@PathVariable("brandId") Long brandId) {
        return toAjax(brandOwnerService.openBrandOwner(brandId));
    }


}
