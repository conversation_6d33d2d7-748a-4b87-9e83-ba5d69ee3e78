package com.xgwc.user.entity.dto;

import com.xgwc.user.entity.ReportTargetFranchise;
import com.xgwc.user.entity.ReportTargetFranchiseDept;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportTargetFranchiseDto {

    /** 主键 */
    private Long targetId;

    //品牌商ID
    private Long brandId;

    /** 加盟商ID */
    private Long franchiseId;

    //加盟商名称
    private String franchiseName;

    /** 目标名称 */
    private String targetName;

    /** 目标日期 */
    private String targetDate;

    /** 负责人ID */
    private Long userId;

    /** 目标额度 */
    private BigDecimal targetAmount;

    /** 订单金额 */
    private BigDecimal amountOrder;

    /** 实收金额 */
    private BigDecimal amountReal;

    /** 新客目标 */
    private BigDecimal targetNewCustomer;

    /** 老客目标 */
    private BigDecimal targetOldCustomer;

    /** 转介绍目标 */
    private BigDecimal targetTransferAmount;

    /** 佣金比例目标 */
    private BigDecimal targetComissionRate;

    /** 新客转化率目标 */
    private BigDecimal targetConversionRate;

    /** 实际新客金额 */
    private BigDecimal newCustomerAmount;

    /** 实际老客金额 */
    private BigDecimal oldCustomerAmount;

    /** 转介绍金额 */
    private BigDecimal transferAmount;

    /** 实际转换率 */
    private BigDecimal conversionRate;

    /** 实际佣金比例 */
    private BigDecimal comissionRate;

    /** 0待提交，1 */
    private Long checkStatus;

    /** 状态：0正常，1非正常 */
    private Long status;

    /** 原因 */
    private String reason;

    /** 创建时间 */
    private String createTime;

    /** 创建人 */
    private String createBy;

}
