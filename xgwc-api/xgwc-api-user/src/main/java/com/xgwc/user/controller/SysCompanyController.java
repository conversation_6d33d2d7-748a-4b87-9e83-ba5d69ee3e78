package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.SysCompanyDto;
import com.xgwc.user.entity.vo.SysCompanyInfo;
import com.xgwc.user.entity.vo.SysCompanyQueryVo;
import com.xgwc.user.entity.vo.SysCompanyVo;
import com.xgwc.user.service.ISysCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;


/**
 * 组织管理-公司管理
 * */
@RestController
@RequestMapping("/user/company")
@Slf4j
public class SysCompanyController extends BaseController {
    @Autowired
    private ISysCompanyService sysCompanyService;

    /**
     * 查询公司管理列表
     */
    @MethodDesc("查询公司管理列表")
    @PreAuthorize("@ss.hasPermission('user:company:list')")
    @GetMapping("/list")
    public ApiResult<SysCompanyInfo> list(SysCompanyQueryVo sysCompany) {
        startPage();
        sysCompany.setIsFlag(0);
        List<SysCompanyInfo> list = sysCompanyService.selectSysCompanyList(sysCompany);
        return getDataTable(list);
    }

    /**
     * 获取公司管理信息树
     */
    @MethodDesc("查询公司管理列表树")
    @GetMapping("/getCompanyTree")
    public ApiResult getCompanyTree() {
        try {
            SysCompanyQueryVo sysCompanyQueryVo = new SysCompanyQueryVo();
            sysCompanyQueryVo.setStatus(0);
            sysCompanyQueryVo.setIsFlag(1);
            List<SysCompanyInfo> result = sysCompanyService.selectSysCompanyList(sysCompanyQueryVo);

            List<SysCompanyInfo> safeResult = Optional.ofNullable(result).orElse(Collections.emptyList());

            return ApiResult.ok(safeResult);
        } catch (Exception e) {
            log.error("获取公司信息树失败", e);
            return ApiResult.error( "获取公司信息树失败");
        }
    }


    /**
     * 查询公司管理列表下拉框
     *
     * @return 公司管理列表下拉框
     */
    @MethodDesc("查询公司管理列表下拉框")
    @GetMapping("/getCompanyDropDown")
    public ApiResult getCompanyDropDown() {
        return ApiResult.ok(sysCompanyService.selectCompanyDropDown());
    }


    /**
     * 获取公司管理详细信息
     */
    @MethodDesc("获取公司管理详细信息")
    @PreAuthorize("@ss.hasPermission('user:company:update')")
    @GetMapping(value = "/{id}")
    public ApiResult<SysCompanyDto> getInfo(@PathVariable("id") Long id) {
        return success(sysCompanyService.selectSysCompanyById(id));
    }

    /**
     * 新增公司管理
     */
    @MethodDesc("新增公司管理")
    @PreAuthorize("@ss.hasPermission('user:company:add')")
    @Log(title = "公司管理", businessType = BusinessType.INSERT)
    @PostMapping("/insertCompany")
    public ApiResult add(@RequestBody SysCompanyVo sysCompany) {
        return toAjax(sysCompanyService.insertSysCompany(sysCompany));
    }

    /**
     * 修改公司管理
     */
    @MethodDesc("修改公司管理")
    @PreAuthorize("@ss.hasPermission('user:company:edit')")
    @Log(title = "公司管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateCompany")
    public ApiResult updateCompany(@RequestBody SysCompanyVo sysCompany) {
        return toAjax(sysCompanyService.updateSysCompany(sysCompany));
    }

    /**
     * 删除公司管理
     */
    @MethodDesc("修改公司管理状态")
    @PreAuthorize("@ss.hasPermission('user:company:updateStatus')")
    @GetMapping("/status")
    public ApiResult updateStatus(@RequestParam("id") Integer id,
                                  @RequestParam("status") Integer status) {
        return ApiResult.ok(sysCompanyService.updateStatus(id, status));
    }
}
