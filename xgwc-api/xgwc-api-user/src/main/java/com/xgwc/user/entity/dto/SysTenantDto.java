package com.xgwc.user.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class SysTenantDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("租户编号")
    @Excel(name = "租户编号")
    private Long id;

    @FieldDesc("租户名")
    @Excel(name = "租户名")
    private String name;

    @FieldDesc("联系人的用户编号")
    @Excel(name = "联系人的用户编号")
    private Long contactUserId;

    @FieldDesc("联系人")
    @Excel(name = "联系人")
    private String contactName;

    @FieldDesc("联系手机")
    @Excel(name = "联系手机")
    private String contactMobile;

    @FieldDesc("租户状态（0正常 1停用）")
    @Excel(name = "租户状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("绑定域名")
    @Excel(name = "绑定域名")
    private String website;

    @FieldDesc("租户套餐编号")
    @Excel(name = "租户套餐编号")
    private Long packageId;

    @FieldDesc("过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireTime;

    @FieldDesc("账号数量")
    @Excel(name = "账号数量")
    private Long accountCount;

    @FieldDesc("创建者")
    @Excel(name = "创建者")
    private String creator;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("更新者")
    @Excel(name = "更新者")
    private String updater;

    @FieldDesc("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;





@Override
public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id",getId())
            .append("name",getName())
            .append("contactUserId",getContactUserId())
            .append("contactName",getContactName())
            .append("contactMobile",getContactMobile())
            .append("status",getStatus())
            .append("website",getWebsite())
            .append("packageId",getPackageId())
            .append("expireTime",getExpireTime())
            .append("accountCount",getAccountCount())
            .append("creator",getCreator())
            .append("createTime",getCreateTime())
            .append("updater",getUpdater())
            .append("updateTime",getUpdateTime())
            .append("isDel",getIsDel())
        .toString();
        }
}
