package com.xgwc.user.aspect;

import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xgwc.common.annotation.LoginLog;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.AspectUtil;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.LoginLogMapper;
import com.xgwc.user.entity.dto.login.LoginLogDto;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.Map;

@Slf4j
@Aspect
@Component
public class LoginLogAspect {

    private static final String PHONE = "phone";

    @Resource
    private LoginLogMapper loginLogMapper;

    @After("@annotation(com.xgwc.common.annotation.LoginLog)")
    public void interceptor(JoinPoint joinPoint) {
        try {
            MethodSignature sign = (MethodSignature) joinPoint.getSignature();
            Method method = sign.getMethod();
            LoginLog loginLog = method.getAnnotation(LoginLog.class);
            Long userId = getUserId();
            if (userId != null) {
                //获取登陆类型
                LoginLogDto loginLogDto = new LoginLogDto();
                loginLogDto.setLoginType(loginLog.loginType().getType());
                loginLogDto.setLoginParam(getRequestParam(joinPoint));
                loginLogDto.setUserId(userId);
                buildOtherInfo(loginLogDto);
                loginLogMapper.insertLoginLog(loginLogDto);
            }
        }catch (Exception e){
            log.error("记录登录日志失败，失败信息:", e);
        }
    }

    private Long getUserId(){
        String sysUserJson = com.xgwc.common.util.RequestContextHolder.getContext();
        if(StringUtils.isNotEmpty(sysUserJson)){
            SysUser sysUser = JSON.parseObject(sysUserJson, new TypeReference<>(){});
            if(sysUser != null){
                return sysUser.getUserId();
            }
        }
        return null;
    }

    /**
     * 获取请求参数
     */
    private String getRequestParam(JoinPoint joinPoint){
        Object[] paramValues = joinPoint.getArgs();
        String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
        Map<String, Object> params = AspectUtil.getAllParams(paramNames, paramValues);
        //移除密码敏感参数
        params.remove("password");
        //手机号进行加密
        if(params.get(PHONE) != null){
            params.put(PHONE, ParamDecryptUtil.encrypt(params.get(PHONE).toString(), ParamDecryptUtil.PHONE_KEY));
        }
        return JSONObject.toJSONString(params);
    }

    private void buildOtherInfo(LoginLogDto loginLogDto){
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            String ip = getIpAddr(request);
            loginLogDto.setIp(ip);
            String brower = getBrowerInfo(request);
            loginLogDto.setBrowser(brower);
        }
    }

    // 获取请求 IP 的工具方法
    private String getIpAddr(HttpServletRequest request) {
        String ipAddress = request.getHeader("x-forwarded-for");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        return ipAddress;
    }

    /**
     * 获取浏览器信息
     */
    private String getBrowerInfo(HttpServletRequest request){
        String userAgentString = request.getHeader("User-Agent");

        // 使用 Hutool 的 UserAgentUtil 解析 User-Agent 字符串
        UserAgent userAgent = UserAgentUtil.parse(userAgentString);
        return JSONObject.toJSONString(userAgent);
    }
}
