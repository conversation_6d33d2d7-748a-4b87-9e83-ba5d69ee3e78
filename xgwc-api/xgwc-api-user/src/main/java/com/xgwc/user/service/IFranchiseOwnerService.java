package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.FranchiseOwner;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.FranchiseOwnerSimpleVO;
import com.xgwc.user.entity.vo.FranchiseOwnerVO;
import jakarta.validation.Valid;

import java.util.List;

public interface IFranchiseOwnerService {
    /**
     * 查询加盟商管理
     * 
     * @param id 加盟商管理主键
     * @return 加盟商管理
     */
    public FranchiseOwnerVO selectFranchiseOwnerById(Long id);

    /**
     * 查询加盟商管理列表
     * 
     * @param brandOwner 加盟商管理
     * @return 加盟商管理集合
     */
    public List<FranchiseOwnerVO> selectFranchiseOwnerList(FranchiseOwnerQueryDto brandOwner);

    /**
     * 新增加盟商管理
     * 
     * @param brandOwner 加盟商管理
     * @return 结果
     */
    public LoginResult insertFranchiseOwner(FranchiseOwnerDto brandOwner);

    /**
     * 修改加盟商管理
     * 
     * @param brandOwner 加盟商管理
     * @return 结果
     */
    public int updateFranchiseOwner(FranchiseOwnerDto brandOwner);

    /**
     * 批量删除加盟商管理
     * 
     * @param ids 需要删除的加盟商管理主键集合
     * @return 结果
     */
    public int deleteFranchiseOwnerByIds(Long[] ids);

    /**
     * 删除加盟商管理信息
     * 
     * @param id 加盟商管理主键
     * @return 结果
     */
    public int deleteFranchiseOwnerById(Long id);

    /**
     * 修改状态
     * @param updateReqVO 加盟商
     */
    void updateStatus(FranchiseOwnerStatusUpdateDto updateReqVO);

    /**
     * 审核
     * @param updateReqVO
     */
    void audit(FranchiseOwnerAuditDto updateReqVO);

    /**
     * 授权
     * @param updateReqVO
     */
    void authorization(FranchiseOwnerAuthorizationDto updateReqVO);

    /**
     * 根据管理员ID和品牌商ID查询
     * @param managerUserId 管理员ID
     * @param brandId 品牌商ID
     * @return 加盟商管理
     */
    public FranchiseOwnerVO selectFranchiseOwnerByManagerUserIdAndBrandId(Long managerUserId,Long brandId);

    /**
     * 重新申请
     * @param franchiseOwner 加盟商信息
     * @return 结果
     */
    int reapply(@Valid FranchiseOwnerDto franchiseOwner);

    /**
     * 查询我的申请信息
     * @param queryDto 查询参数
     * @return 申请信息
     */
    List<FranchiseOwnerVO> selectMyApplyList(MyFranchiseOwnerQueryDto queryDto);

    /**
     * 根据品牌商ID查询加盟商下拉列表
     * @param brandId 申请ID
     * @return 加盟商列表
     */
    List<FranchiseOwnerSimpleVO> selectFranchiseOwnerListByBrandId(Long brandId);

    /**
     * 查询加盟商下载限制
     *
     * @param franchiseOwnerQueryDto 查询参数
     * @return 下载限制列表
     */
    List<FranchiseOwnerVO> selectFranchiseDownloadLimit(FranchiseOwnerQueryDto franchiseOwnerQueryDto);

    /**
     * 修改加盟商下载限制
     *
     * @param franchiseOwnerVo 修改参数
     * @return 结果
     */
    ApiResult updateDownloadLimit(FranchiseOwner franchiseOwnerVo);

    /**
     * 根据加盟商ID查询加盟商信息
     * @param franchiseId 加盟商ID
     * @return 加盟商信息
     */
    int selectFranchiseOwnerByFranchiseId(Long franchiseId);

    /**
     * 线下支付-来自加盟商下拉框
     *
     * @param brandId 品牌ID
     * @return 品牌商名称-加盟商简称
     */
    List<FranchiseOwnerVO> offlinePayment(Long brandId);

    /**
     * 查询合作加盟商列表
     * @return 合作加盟商列表
     */
    List<FranchiseOwnerSimpleVO> findCooperateFranchiseList();
}
