package com.xgwc.user.dao;


import com.xgwc.user.entity.Staff;
import com.xgwc.user.entity.dto.FranchiseDeptDto;
import com.xgwc.user.entity.param.FranchiseDeptParam;
import com.xgwc.user.entity.vo.FranchiseDeptVo;
import com.xgwc.user.entity.vo.StaffVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.dao
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  11:38
 */

public interface FranchiseDeptMapper {

    /**
     * 查询所有部门
     * @param franchiseDeptParam 查询条件
     * @return 部门列表
     */
    List<FranchiseDeptVo> getFranchiseDeptList(@Param("franchiseDeptParam") FranchiseDeptParam franchiseDeptParam);

    /**
     * 保存部门
     * @param franchiseDeptDto 部门信息
     * @return 插入结果
     */
    int saveFranchiseDept(@Param("franchiseDeptDto") FranchiseDeptDto franchiseDeptDto);

    /**
     * 根据部门id查询部门信息
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    FranchiseDeptDto getFranchiseDeptById(@Param("deptId") Long deptId);

    /**
     * 根据userId查询部门信息
     * @param userId
     * @return
     */
    FranchiseDeptDto getUserDeptByUserId(@Param("userId") Long userId);

    /**
     * 修改部门信息
     * @param franchiseDeptDto 部门信息
     * @return 修改结果
     */
    int updateFranchiseDeptById(@Param("franchiseDeptDto") FranchiseDeptDto franchiseDeptDto);

    /**
     * 修改部门状态
     * @param deptId 部门id
     * @param status 状态
     * @return 修改结果
     */
    int updateStatusById(@Param("deptId") Integer deptId, @Param("status") Integer status);

    /**
     * 根据部门id查询部门员工信息
     * @param deptId 部门id
     * @return 部门员工信息
     */
    StaffVo selectXgwcDeptStaffManage(@Param("deptId") Integer deptId);

    /**
     * 根据部门id查询部门员工排班信息
     * @param deptId 部门id
     * @return 部门员工排班信息
     */
    List<StaffVo> selectXgwcDeptStaffSchedule(@Param("deptId") Integer deptId);

    /**
     * 修改部门员工排班信息
     * @param staff 部门员工排班信息
     * @return 修改结果
     */
    int updateDeptStaffSchedule(@Param("staff") Staff staff);

    /**
     * 批量修改部门员工管理信息
     * @param isPrincipal 部门员工管理信息
     * @return 修改结果
     */
    int updateDeptStaffManage(@Param("principal") Staff isPrincipal);

    /**
     * 根据加盟商id获取加盟商下面的部门
     * @param franchiseId 加盟商id
     * @return 部门
     */
    List<FranchiseDeptDto> getFranchiseDeptByfranchiseId(Long franchiseId);

    /**
     * 根据部门id获取部门员工数量
     * @param deptId 部门id
     * @return 部门员工数量
     */
    Integer selectDeptStaffNum(@Param("deptId") Long deptId);

    /**
     * 修改部门助理
     * @param deptId 部门id
     * @return 修改结果
     */
    int updateDeptStaffAssistant(@Param("deptId") Long deptId);

    /**
     * 根据pid查询部门信息
     * @param pid 父级部门id
     * @param franchiseId 加盟商id
     * @return 部门信息
     */
    List<FranchiseDeptVo> getDeptByPid(@Param("pid") Long pid,@Param("franchiseId") Long franchiseId);

    /**
     * 根据加盟商id查询所有一级部门
     * @param franchiseId 加盟商id
     * @return 部门信息
     */
    List<FranchiseDeptVo> getDeptByFranchiseId(@Param("franchiseId") Long franchiseId);
}
