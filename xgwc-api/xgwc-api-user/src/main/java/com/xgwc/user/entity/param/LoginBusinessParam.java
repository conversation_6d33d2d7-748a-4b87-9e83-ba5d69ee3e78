package com.xgwc.user.entity.param;

import lombok.Data;

@Data
public class LoginBusinessParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 登录渠道 1-web 2-链接
     */
    private Integer loginChannels;

    /**
     * 业务ID （品牌ID 加盟商ID）
     */
   private Long businessId;

   /**
     * 链接类型 1-加盟商 2-设计师 3-品牌商员工 4-加盟商员工
     */
   private Integer linkType;

   private Long mainUserId;

   private Integer userType;

}
