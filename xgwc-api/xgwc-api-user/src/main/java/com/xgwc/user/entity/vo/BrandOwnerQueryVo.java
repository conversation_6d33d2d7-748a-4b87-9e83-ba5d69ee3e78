package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class BrandOwnerQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌ID")
    private Long brandId;

    @FieldDesc("公司名称")
    private String companyName;

    @FieldDesc("公司简称")
    private String companySimpleName;

    @FieldDesc("联系人")
    private String contact;

    @FieldDesc("管理员手机号")
    private String managerPhone;

    @FieldDesc("密码")
    private String password;

    @FieldDesc("租户id")
    private Long tenantId;

    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("删除状态：0正常，1删除")
    private Integer isDel;

    @FieldDesc("审核原因")
    private String reason;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;



}
