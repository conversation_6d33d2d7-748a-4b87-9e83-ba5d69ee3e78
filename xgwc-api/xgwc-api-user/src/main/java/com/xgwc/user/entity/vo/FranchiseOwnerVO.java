package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.dto.FranchiseDesignerRecordDto;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class FranchiseOwnerVO {

    private static final long serialVersionUID=1L;

    /** 主键 */
    @Excel(name = "主键")
    private Long id;

    /** 加盟商ID */
    @Excel(name = "加盟商ID")
    private Long franchiseId;

    /** 营业执照 */
    @Excel(name = "营业执照")
    private String businessLicense;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 公司简称 */
    @Excel(name = "公司简称")
    private String companySimpleName;

    /** 营业执照编码 */
    @Excel(name = "营业执照编码")
    private String licenseCode;

    /** 执照有效期是否长期：0长期，1非长期 */
    @Excel(name = "执照有效期是否长期：0长期，1非长期")
    private Long licenseIsLongterm;

    /** 营业执照开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "营业执照开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date licenseStart;

    /** 营业执照结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "营业执照结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date licenseEnd;

    /** 营业省 */
    @Excel(name = "营业省")
    private Long operateProvince;

    /** 营业市 */
    @Excel(name = "营业市")
    private Long operateCity;

    /** 营业区 */
    @Excel(name = "营业区")
    private Long operateRegion;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String operateAddress;

    /** 规模 */
    @Excel(name = "规模")
    private String scale;

    /** 身份证正面：加密 */
    @Excel(name = "身份证正面：加密")
    private String idcardFront;

    /** 身份证反面：加密 */
    @Excel(name = "身份证反面：加密")
    private String idcardBack;

    /** 身份证名称：加密 */
    @Excel(name = "身份证名称：加密")
    private String idcardName;

    /** 身份证号：加密 */
    @Excel(name = "身份证号：加密")
    private String idcardNo;

    /** 身份证是否长期：0长期，1非长期 */
    @Excel(name = "身份证是否长期：0长期，1非长期")
    private Long idcardIsLongterm;

    /** 身份证开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "身份证开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date idcardStart;

    /** 身份证结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "身份证结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date idcardEnd;

    /** 法人手机号：加密 */
    @Excel(name = "法人手机号：加密")
    private String idcardPhone;

    /** 邮箱：加密 */
    @Excel(name = "邮箱：加密")
    private String idcardEmail;

    /** 开户名：加密 */
    @Excel(name = "开户名：加密")
    private String bankUserName;

    /** 开户行 */
    @Excel(name = "开户行")
    private String bankName;

    /** 对公账号：加密 */
    @Excel(name = "对公账号：加密")
    private String bankNo;

    /** 管理员姓名 */
    @Excel(name = "管理员姓名")
    private String managerName;

    /** 管理员手机号：加密 */
    @Excel(name = "管理员手机号：加密")
    private String managerPhone;

    /** 申请状态 */
    @Excel(name = "申请状态")
    private Integer checkStatus;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkTime;

    /** 状态：0正常，1禁用 */
    @Excel(name = "状态：0正常，1禁用")
    private Integer status;

    /** 删除状态：0正常，1删除 */
    @Excel(name = "删除状态：0正常，1删除")
    private Integer isDel;

    /** 审核原因 */
    @Excel(name = "审核原因")
    private String reason;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    /** 行修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    /** 授权店铺 */
    private String shopIds;

    /** 授权店铺名称 */
    @FieldDesc("授权店铺名称")
    private String shopName;

    /** 所属品牌 */
    @FieldDesc("所属品牌")
    private String brandName;

    /** 所属品牌ID */
    @FieldDesc("所属品牌ID")
    private Long brandId;

    @FieldDesc("加盟商审核记录")
    private List<FranchiseDesignerRecordDto> franchiseAuditRecord;

    private Long managerUserId;

    @FieldDesc("下载次数上限/天/人（加盟商）")
    private Integer downloadLimit;

    @FieldDesc("已下载次数")
    private Integer downloadCount;

    @FieldDesc("是否设置（0-是 1-否）")
    private Integer isConfigured;

    @FieldDesc("加盟商名称")
    private String franchiseName;
}
