package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  15:40
 */

/**
 * 部门管理
 */
@Data
public class XgwcBrandDeptVo {

    /**
     * 部门主键
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 品牌商id
     */
    private Long brandOwnerId;

    /**
     * 父类id
     */
    private Long pid;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 层级数
     */
    private Integer levelNum;

    /**
     * 排序：越小越前
     */
    private Integer sort;

    /**
     * 状态：0正常，1禁用
     */
    private Integer status;

    /**
     * 是否删除：0否，1是
     */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @FieldDesc("部门负责人")
    private StaffVo isPrincipal;

    @FieldDesc("助理")
    private List<StaffVo> isAssistant;

    @FieldDesc("总人数")
    private Integer totalNum;

    @FieldDesc("标识")
    private String isFlag;
}
