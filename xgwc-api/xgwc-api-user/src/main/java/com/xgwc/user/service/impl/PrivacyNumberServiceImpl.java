package com.xgwc.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.*;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.user.dao.DesignerMapper;
import com.xgwc.user.dao.PrivacyNumberMapper;
import com.xgwc.user.dao.SysUserMiddleMapper;
import com.xgwc.user.entity.dto.PrivacyNumberDto;
import com.xgwc.user.entity.dto.SysUserMiddleDto;
import com.xgwc.user.entity.vo.DesignerVO;
import com.xgwc.user.entity.vo.PrivacyNumberCallBackVo;
import com.xgwc.user.entity.vo.PrivacyNumberVo;
import com.xgwc.user.service.PrivacyNumberService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Random;

@Slf4j
@Service
public class PrivacyNumberServiceImpl implements PrivacyNumberService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private PrivacyNumberMapper privacyNumberMapper;

    @Resource
    private DesignerMapper designerMapper;

    @Resource
    private SysUserMiddleMapper sysUserMiddleMapper;

    @Resource
    private RedisUtil redisUtil;

    private static final String PHONE_KEY = ParamDecryptUtil.PHONE_KEY;

    private static final Integer APPID = 442842;

    private static final String TOKEN = "f0c0cb640ee24215a8761f26e1ce09ac";

    private Random random;

    {
        try {
            random = new Random();
        } catch (Exception e) {
            log.error("生成随机数错误error:", e);
        }
    }

    /**
     * 绑定号码
     * @param privacyNumberVo 参数
     * @return 是否成功
     */
    @Override
    public ApiResult<String> bindNumber(PrivacyNumberVo privacyNumberVo){
        String bindNumberA = ParamDecryptUtil.encrypt(privacyNumberVo.getBindNumberA(), ParamDecryptUtil.PHONE_KEY);
        String bindNumberB = ParamDecryptUtil.encrypt(privacyNumberVo.getBindNumberB(), ParamDecryptUtil.PHONE_KEY);
        privacyNumberVo.setBindNumberA(bindNumberA);
        privacyNumberVo.setBindNumberB(bindNumberB);
        return bindEncryptNumber(privacyNumberVo);
    }

    @Override
    public ApiResult<String> bindCustomerNumber(PrivacyNumberVo privacyNumberVo) {
        String bindNumberB = ParamDecryptUtil.encrypt(privacyNumberVo.getBindNumberB(), ParamDecryptUtil.PHONE_KEY);
        privacyNumberVo.setBindNumberB(bindNumberB);
        SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleById(SecurityUtils.getUserId());
        if(sysUserMiddleDto != null){
            privacyNumberVo.setBindNumberA(sysUserMiddleDto.getPhone());
            return bindEncryptNumber(privacyNumberVo);
        }
        return ApiResult.ok();
    }

    public ApiResult<String> bindEncryptNumber(PrivacyNumberVo privacyNumberVo){
        String middleNumber;
        PrivacyNumberDto privacyNumberDto = privacyNumberMapper.selectPrivacyNumber(privacyNumberVo.getBindNumberA(), privacyNumberVo.getBindNumberB());
        //数据库中存在绑定关系
        if(privacyNumberDto != null){
            //返回中间号
            middleNumber = privacyNumberDto.getMiddleNumber();
        }else{
            //不存在绑定关系，或者绑定关系已过期, 重新绑定
            middleNumber = getMiddleNumber(privacyNumberVo);
            if(StringUtil.isNotEmpty(middleNumber)){
                privacyNumberVo.setMiddleNumber(middleNumber);
                boolean isBind = middleNumberAXB(privacyNumberVo);
                if(isBind){
                    String exipreTime = DateUtils.addSeconds(System.currentTimeMillis(), privacyNumberVo.getMaxBindingTime());
                    privacyNumberVo.setExpireTime(exipreTime);
                    privacyNumberVo.setMiddleNumber(middleNumber);
                    privacyNumberMapper.insertPrivacyNumber(privacyNumberVo);
                }else{
                    return ApiResult.ok("");
                }
            }else{
                return ApiResult.error("暂无可用号码");
            }
        }
        return ApiResult.ok(ParamDecryptUtil.decryptParam(middleNumber, PHONE_KEY));
    }

    @Override
    public ApiResult<String> bindDesignerNumber(Long designerId) {
        PrivacyNumberVo privacyNumberVo = new PrivacyNumberVo();
        //根据用户id查询手机号
        DesignerVO designerVOS = designerMapper.selectDesignerByDesignerId(designerId);
        if(designerVOS != null){
            privacyNumberVo.setBindNumberB(designerVOS.getPhone());
            SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleById(SecurityUtils.getUserId());
            if(sysUserMiddleDto != null){
                privacyNumberVo.setBindNumberA("bdIl9UV5N71/HaVGOO8lAg==");
                return bindEncryptNumber(privacyNumberVo);
            }
        }
        return ApiResult.error("");
    }

    /**
     * 随机获取中间号码
     */
    private String getMiddleNumber(PrivacyNumberVo privacyNumberVo){
        List<String> middleNumberList = privacyNumberMapper.getAllMiddleNumberList();
        //获取已经存在绑定关系的手机号
        List<String> bindNumberList = privacyNumberMapper.getBindMiddleNumber(privacyNumberVo);
        middleNumberList.removeAll(bindNumberList);
        int randomIndex = random.nextInt(middleNumberList.size());
        // 获取随机元素
        return middleNumberList.get(randomIndex);
    }

    //调用云呼绑定号码
    private boolean middleNumberAXB(PrivacyNumberVo privacyNumberVo) {
        privacyNumberVo.setMaxBindingTime(300);
        JSONObject param = new JSONObject();
        param.put("middleNumber", ParamDecryptUtil.decryptParam(privacyNumberVo.getMiddleNumber(), PHONE_KEY));
        param.put("bindNumberA", ParamDecryptUtil.decryptParam(privacyNumberVo.getBindNumberA(), PHONE_KEY));
        param.put("bindNumberB", ParamDecryptUtil.decryptParam(privacyNumberVo.getBindNumberB(), PHONE_KEY));
        param.put("callbackUrl", "https://designer.houba.cn/api/users/privacy/callback");
        //绑定三天 TODO 需要写定时任务三天自动解绑
        param.put("maxBindingTime", privacyNumberVo.getMaxBindingTime());
        HttpHeaders headers = new HttpHeaders();
        long currentTime = System.currentTimeMillis();
        headers.add("Authorization", getAuthorization(currentTime));
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 创建 HttpEntity 对象，包含请求头和请求体
        HttpEntity<String> requestEntity = new HttpEntity<>(param.toJSONString(), headers);
        // 发送 POST 请求
        try {
            String response = restTemplate.postForObject("http://101.37.133.245:11108/voice/1.0.0/middleNumberAXB/" + APPID + "/" + getSig(currentTime), requestEntity, String.class);
            if (StringUtil.isNotEmpty(response)) {
                JSONObject jsonObject = JSONObject.parseObject(response);
                if (jsonObject.getString("result").equals("000000")) {
                    return true;
                }
                log.error("绑定号码失败,参数:{}", param.toJSONString());
            }
        }catch (Exception e){
            log.error("绑定号码失败,失败原因:", e);
        }
        return false;
    }

    private String getAuthorization(long currentTime){
        System.out.println("" + APPID + currentTime);
        System.out.println(Base64.getEncoder().encodeToString((APPID + ":" +currentTime).getBytes()));
        return Base64.getEncoder().encodeToString((APPID + ":" +currentTime).getBytes());
    }

    private String getSig(long currentTime){
        return Md5Util.getMd5(APPID + TOKEN + currentTime);
    }

    /**
     * 解绑
     */
    private boolean middleNumberUnbind(PrivacyNumberVo privacyNumberVo){
        JSONObject param = new JSONObject();
        param.put("middleNumber", ParamDecryptUtil.decryptParam(privacyNumberVo.getMiddleNumber(), PHONE_KEY));
        param.put("bindNumberA", ParamDecryptUtil.decryptParam(privacyNumberVo.getBindNumberA(), PHONE_KEY));
        param.put("bindNumberB", ParamDecryptUtil.decryptParam(privacyNumberVo.getBindNumberB(), PHONE_KEY));
        param.put("mode", 0);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        long currentTime = System.currentTimeMillis();
        headers.add("Authorization", getAuthorization(currentTime));
        // 创建 HttpEntity 对象，包含请求头和请求体
        HttpEntity<String> requestEntity = new HttpEntity<>(param.toJSONString(), headers);
        try {
            // 发送 POST 请求
            String response = restTemplate.postForObject("http://101.37.133.245:11108/voice/1.0.0/middleNumberUnbind/" + APPID + "/" + getSig(currentTime), requestEntity, String.class);
            if (StringUtil.isNotEmpty(response)) {
                JSONObject jsonObject = JSONObject.parseObject(response);
                if (jsonObject.getString("result").equals("000000")) {
                    return true;
                }
                log.error("解绑定号码失败,参数:{}", param.toJSONString());
            }
        }catch (Exception e){
            log.error("解绑定号码失败,失败原因:", e);
        }
        return false;
    }

    /**
     * 解绑号码
     * @param privacyNumberVo 参数
     * @return 是否成功
     */
    @Override
    public ApiResult<String> unbindNumber(PrivacyNumberVo privacyNumberVo){
        middleNumberUnbind(privacyNumberVo);
        int result = privacyNumberMapper.updateBindStatus(privacyNumberVo);
        if(result > 0) {
            privacyNumberMapper.updateMiddleNumberMinusOne(privacyNumberVo.getMiddleNumber());
        }
        return ApiResult.ok("解绑成功");
    }

    @Override
    public void addCallbackRecord(PrivacyNumberCallBackVo privacyNumberCallBackVo) {
        privacyNumberMapper.insertCallBackRecord(privacyNumberCallBackVo);
    }
}
