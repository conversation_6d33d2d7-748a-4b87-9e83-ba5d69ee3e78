package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class BrandApplyVo {

    private static final long serialVersionUID=1L;

    @NotNull(message = "母公司名称不能为空")
    @FieldDesc("母公司名称")
    private String companyName;

    @NotNull(message = "联系人信息不能为空")
    @FieldDesc("联系人")
    private String contact;

    @NotNull(message = "主用户id不能为空")
    @FieldDesc("主用户id")
    private Long mainUserId;

}
