package com.xgwc.user.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportTargetFranchise {

    /** 主键 */
    private Long targetId;

    private Long brandId;

    /** 加盟商ID */
    private Long franchiseId;

    /** 目标名称 */
    @NotNull(message = "目标名称不能为空")
    private String targetName;

    /** 目标日期 */
    @NotNull(message = "目标日期不能为空")
    private String targetDate;

    /** 目标额度 */
    private BigDecimal targetAmount;

    /** 订单金额 */
    private BigDecimal amountOrder;

    /** 实收金额 */
    private BigDecimal amountReal;

    /** 新客目标 */
    private BigDecimal targetNewCustomer;

    /** 老客目标 */
    private BigDecimal targetOldCustomer;

    /** 转介绍目标 */
    private BigDecimal targetTransferAmount;

    /** 佣金比例目标 */
    private BigDecimal targetComissionRate;

    /** 新客转化率目标 */
    private BigDecimal targetConversionRate;

    /** 实际新客金额 */
    private BigDecimal newCustomerAmount;

    /** 实际老客金额 */
    private BigDecimal oldCustomerAmount;

    /** 转介绍金额 */
    private BigDecimal transferAmount;

    /** 实际转换率 */
    private BigDecimal conversionRate;

    /** 实际佣金比例 */
    private BigDecimal comissionRate;

    /** 0待提交，1 */
    private Integer checkStatus;

    /** 状态：0正常，1非正常 */
    private Integer status;

    /**
     * 部门数
     */
    private Integer deptCount;

    /**
     * 提交部门数
     */
    private Integer submitDeptCount;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建人ID */
    private Long createById;

    /** 修改人 */
    private String updateBy;

    /** 修改人用户ID */
    private Long updateById;

    /**
     * 部门数据
     */
    @NotNull(message = "加盟商部门数据为空")
    private List<ReportTargetFranchiseDept> depts;
}
