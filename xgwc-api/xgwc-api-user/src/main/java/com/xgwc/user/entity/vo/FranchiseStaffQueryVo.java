package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

@Data
public class FranchiseStaffQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("部门id")
    private Long deptId;

    @FieldDesc("岗位id")
    private Long postId;

    @FieldDesc("角色权限")
    private String roleIds;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    private Integer jobNature;

    @FieldDesc("状态：0在职，1离职")
    private Integer status;

    @FieldDesc("部门负责人：0是，1否")
    private Long isPrincipal;

    @FieldDesc("直属上级")
    private Long superior;

    @FieldDesc("档案状态：0未录，1已录")
    private Long archiveStatus;

    @FieldDesc("绑定状态：0未绑，1已绑")
    private Long bindStatus;

    @FieldDesc("绑定的用户id")
    private Long bindUserId;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("是否删除：0正常，1删除")
    private Integer isDel;

    @FieldDesc("其他")
    private String other;

    @FieldDesc("限制对象的姓名/花名/手机号/最后操作人花名")
    private String limitName;

    @FieldDesc("是否有修改")
    private Integer isModify;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("开始时间")
    private String startTime;

    @FieldDesc("结束时间")
    private String endTime;

    @FieldDesc("姓名")
    private String name;

    @FieldDesc("花名")
    private String stageName;

    @FieldDesc("手机号")
    private String loginPhone;

    private List<Long> deptIds;
}
