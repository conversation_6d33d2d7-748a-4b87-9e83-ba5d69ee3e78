package com.xgwc.user.util;

import com.alibaba.cloud.commons.lang.StringUtils;

public class StaffLogParseUtil {

    public static String parseLogContentToReadable(String rawContent,int businessType) {
        if (StringUtils.isBlank(rawContent)) return "";
        StringBuilder sb = new StringBuilder();
        String[] parts = rawContent.split("；");
        for (String part : parts) {
            if (!part.contains(":")) continue;
            String[] fieldSplit = part.split(":");
            String fieldName = fieldSplit[0].trim();
            String valuePart = fieldSplit[1].trim();
            String[] values = valuePart.split(">");
            if (values.length != 2) {
                sb.append(part).append("；");
                continue;
            }
            String oldVal = values[0].trim();
            String newVal = values[1].trim();
            String oldLabel = "";
            String newLabel = "";
            if(businessType == 1){
                oldLabel = BrandFieldValueResolver.resolve(fieldName, oldVal);
                newLabel = BrandFieldValueResolver.resolve(fieldName, newVal);
            }else {
                oldLabel = FranchiseFieldValueResolver.resolve(fieldName, oldVal);
                newLabel = FranchiseFieldValueResolver.resolve(fieldName, newVal);
            }
            sb.append(String.format("%s: %s > %s；", fieldName, oldLabel, newLabel));
        }
        return sb.toString();
    }
}
