package com.xgwc.user.dao;

import com.xgwc.user.entity.Franchise;
import com.xgwc.user.entity.dto.FranchiseDto;

import java.util.List;


public interface FranchiseMapper {

    /**
     * 根据用户id后去加盟商信息
     * @param userId 用户id
     * @return 加盟商信息
     */
    FranchiseDto getFranchiseDtoByUserId(Long userId);

    /**
     * 添加加盟商信息
     * @param franchise 加盟商信息
     * @return 结果
     */
    int insertFranchise(Franchise franchise);

    /**
     * 根据id查询加盟商信息
     * @param id 加盟商id
     * @return 加盟商信息
     */
    FranchiseDto findFranchiseById(Long id);

    List<com.xgwc.user.feign.entity.FranchiseDto> listByIds(List<Long> ids);
}
