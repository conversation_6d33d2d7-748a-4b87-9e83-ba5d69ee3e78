package com.xgwc.user.config;

import lombok.extern.log4j.Log4j2;
import org.java_websocket.client.WebSocketClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.UnknownHostException;
import java.util.Timer;
import java.util.TimerTask;

@Configuration
@Log4j2
public class WebSocketClientConfig {

    /**
     * socket连接地址
     */
    @Value("${com.xgwc.socket.url}")
    private String webSocketUri;

    /**
     * 注入Socket客户端
     */
    @Bean
    public WebSocketClient initWebSocketClient() throws UnknownHostException {
        InetAddress localHost = InetAddress.getLocalHost();
        URI uri = null;
        try {
            uri = new URI(webSocketUri + localHost.getHostAddress());
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        WebSocketClient webSocketClient = new MyWebSocketClient(uri);
        webSocketClient.connect();
        Timer t = new Timer();
        t.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
               /* if(webSocketClient.isClosed()){
                    log.error("断线重连");
                    webSocketClient.reconnect();
                }*/
            }
        },1000,5000);
        return webSocketClient;
    }
}
