package com.xgwc.user.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class DesignerGradeUpdateDto {

    @FieldDesc("主键")
    @NotNull(message = "ID不能为空")
    private Long id;

    @FieldDesc("设计师等级")
    @NotNull(message = "设计师等级不能为空")
    private Integer designerLevel;

    @FieldDesc("企业微信ID")
    //@NotNull(message = "企业微信ID不能为空")
    private Long wechatId;

    @FieldDesc("企业ID")
    //@NotNull(message = "企业ID不能为空")
    private Long companyId;

    @FieldDesc("接单状态")
    @NotNull(message = "接单状态不能为空")
    private Integer receiveOrderStatus;
}
