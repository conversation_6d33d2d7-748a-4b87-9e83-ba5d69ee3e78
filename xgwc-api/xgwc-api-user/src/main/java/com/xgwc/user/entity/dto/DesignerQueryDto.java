package com.xgwc.user.entity.dto;

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.Designer;
import lombok.Data;

@Data
public class DesignerQueryDto extends Designer {

    private static final long serialVersionUID=1L;

    /** 姓名 */
    @FieldDesc("姓名")
    private String name;

    /** 擅长业务 */
    @FieldDesc("擅长业务")
    private Integer goodBusiness;

    /** 管理员姓名 */
    private String managerName;

    /** 管理员手机号：加密 */
    @FieldDesc("管理员手机号")
    private String managerPhone;

    /** 申请状态 */
    private Integer checkStatus;

    /** 状态：0正常，1禁用 */
    @FieldDesc("状态")
    private Integer status;

    private Long brandId;

    private String businessIds;

    private Long franchiseId;

    /** 品牌商名称 */
    private String brandName;

    @FieldDesc("接单状态：0正常接单，1暂停接单，2离职")
    private Integer receiveOrderStatus;

}
