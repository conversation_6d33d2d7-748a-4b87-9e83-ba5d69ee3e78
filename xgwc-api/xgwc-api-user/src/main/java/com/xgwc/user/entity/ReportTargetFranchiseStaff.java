package com.xgwc.user.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportTargetFranchiseStaff {

    /** 主键 */
    private Long id;

    private Long brandId;

    /** 加盟商ID */
    private Long franchiseId;

    /** 目标名称 */
    private Long targetId;

    /**
     * 部门目标id
     */
    private Long targetDeptId;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 员工ID
     */
    private Long userId;

    /** 目标日期 */
    private String targetDate;

    /** 目标额度 */
    private BigDecimal targetAmount;

    /** 订单金额 */
    private BigDecimal amountOrder;

    /** 实收金额 */
    private BigDecimal amountReal;

    /** 新客目标 */
    private BigDecimal targetNewCustomer;

    /** 老客目标 */
    private BigDecimal targetOldCustomer;

    /** 转介绍目标 */
    private BigDecimal targetTransferAmount;

    /** 佣金比例目标 */
    private BigDecimal targetComissionRate;

    /** 新客转化率目标 */
    private BigDecimal targetConversionRate;

    /** 实际新客金额 */
    private BigDecimal newCustomerAmount;

    /** 实际老客金额 */
    private BigDecimal oldCustomerAmount;

    /** 转介绍金额 */
    private BigDecimal transferAmount;

    /** 实际转换率 */
    private BigDecimal conversionRate;

    /** 实际佣金比例 */
    private BigDecimal comissionRate;

    /** 0待提交，1 */
    private Long checkStatus;

    /** 状态：0正常，1非正常 */
    private Long status;

    /** 原因 */
    private String reason;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /** 创建人 */
    private String createBy;

    /** 创建人ID */
    private Long createById;

    /** 修改人 */
    private String updateBy;

    /** 修改人用户ID */
    private Long updateById;

}
