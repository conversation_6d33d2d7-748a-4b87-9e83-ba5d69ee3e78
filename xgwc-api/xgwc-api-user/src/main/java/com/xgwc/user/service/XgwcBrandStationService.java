package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.XgwcBrandStationDto;
import com.xgwc.user.entity.param.XgwcBrandStationParam;
import com.xgwc.user.entity.vo.XgwcBrandStationVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  10:43
 */
public interface XgwcBrandStationService {

    /**
     * 根据条件查询品牌岗位信息
     * @param xgwcBrandStationParam 查询条件
     * @return 品牌岗位信息
     */
    List<XgwcBrandStationVo> getXgwcBrandStationList(XgwcBrandStationParam xgwcBrandStationParam);

    /**
     * 保存品牌岗位信息
     * @param xgwcBrandStationDto 品牌岗位信息
     * @return 保存结果
     */
    ApiResult saveXgwcBrandStation(XgwcBrandStationDto xgwcBrandStationDto);

    /**
     * 根据ID查询品牌岗位信息
     * @param stationId 岗位id
     * @return 品牌岗位信息
     */
    ApiResult getXgwcBrandStationById(Integer stationId);

    /**
     * 更新品牌岗位信息
     * @param xgwcBrandStationDto 品牌岗位信息
     * @return 更新结果
     */
    ApiResult updateXgwcBrandStation(XgwcBrandStationDto xgwcBrandStationDto);

    /**
     * 更新品牌岗位状态
     * @param stationId 岗位id
     * @param status 状态
     * @return 更新结果
     */
    ApiResult updateStatusById(Integer stationId, Integer status);
}
