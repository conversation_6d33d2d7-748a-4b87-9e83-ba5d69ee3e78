package com.xgwc.user.service;

import java.util.List;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.vo.SaasRoleVo;
import com.xgwc.user.entity.dto.SaasRoleDto;
import com.xgwc.user.entity.vo.SaasRoleQueryVo;

public interface ISaasRoleService {
    /**
     * 查询saas后台角色
     *
     * @param roleId saas后台角色主键
     * @return saas后台角色
     */
    ApiResult selectSaasRoleByRoleId(Long roleId);

    /**
     * 查询saas后台角色列表
     *
     * @param saasRole saas后台角色
     * @return saas后台角色集合
     */
    List<SaasRoleDto> selectSaasRoleList(SaasRoleQueryVo saasRole);

    /**
     * 新增saas后台角色
     *
     * @param saasRole saas后台角色
     * @return 结果
     */
    ApiResult insertSaasRole(SaasRoleVo saasRole);

    /**
     * 修改saas后台角色
     *
     * @param saasRole saas后台角色
     * @return 结果
     */
    ApiResult updateSaasRole(SaasRoleVo saasRole);

    /**
     * 删除saas后台角色信息
     *
     * @param roleId saas后台角色主键
     * @return 结果
     */
    int deleteSaasRoleByRoleId(Long roleId);
}
