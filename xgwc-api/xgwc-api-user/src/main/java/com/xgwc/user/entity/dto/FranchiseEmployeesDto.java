package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.FranchiseEmpAttachments;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 员工档案表
 */
@Data
public class FranchiseEmployeesDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("员工唯一标识")
    @Excel(name = "员工唯一标识")
    private Long employeeId;

    @FieldDesc("员工外键")
    @Excel(name = "员工外键")
    private Long staffId;

    @FieldDesc("员工姓名")
    @Excel(name = "员工姓名")
    private String name;

    @FieldDesc("用户性别（0男 1女 2未知）")
    @Excel(name = "用户性别（0男 1女 2未知）")
    private Long sex;

    @FieldDesc("民族")
    @Excel(name = "民族")
    private Integer ethnicity;

    @FieldDesc("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthdate;

    @FieldDesc("学历")
    @Excel(name = "学历")
    private Integer education;

    @FieldDesc("政治面貌")
    @Excel(name = "政治面貌")
    private Integer politicalStatus;

    @FieldDesc("婚姻状况")
    @Excel(name = "婚姻状况")
    private Integer maritalStatus;

    @FieldDesc("身份证号")
    @Excel(name = "身份证号")
    private String idNumber;

    @FieldDesc("电子邮箱")
    @Excel(name = "电子邮箱")
    private String email;

    @FieldDesc("手机号码")
    @Excel(name = "手机号码")
    private String phone;

    @FieldDesc("联系地址")
    @Excel(name = "联系地址")
    private String address;

    @FieldDesc("紧急联系人姓名")
    @Excel(name = "紧急联系人姓名")
    private String emerName;

    @FieldDesc("紧急联系人电话")
    @Excel(name = "紧急联系人电话")
    private String emerPhone;

    @FieldDesc("入职日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryDate;

    @FieldDesc("合同到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date contractEndDate;

    @FieldDesc("社保状态，（0-未买，1-已买，2-停保）")
    @Excel(name = "社保状态，（0-未买，1-已买，2-停保）")
    private Long socialStatus;

    @FieldDesc("购买社保日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date buySocialDate;

    @FieldDesc("是否转正，（0-否，1-是）")
    @Excel(name = "是否转正，（0-否，1-是）")
    private Long probationStatus;

    @FieldDesc("转正日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "转正日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date probationEndDate;

    @FieldDesc("年假天数")
    @Excel(name = "年假天数")
    private Long annualLeaveDays;

    @FieldDesc("离职日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "离职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date resignationDate;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;



    @FieldDesc("花名")
    @Excel(name = "花名")
    private String stageName;

    @FieldDesc("状态：0在职，1离职")
    @Excel(name = "状态：0在职，1离职")
    private Integer status;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    @Excel(name = "工作性质：0全职，1外包，2兼职")
    private Integer jobNature;

    @FieldDesc("岗位名称")
    @Excel(name = "岗位名称")
    private String stationName;

    @FieldDesc("部门名称")
    @Excel(name = "部门名称")
    private String deptName;

    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String companyName;



    /**
     * ===================================================附件=====================
     */
/*    @FieldDesc("附件类型（0-简历 1-劳动合同 2-证明材料）")
    private String attachmentType;

    @FieldDesc("文件名")
    private String fileName;

    @FieldDesc("文件路径")
    private String filePath;

    @FieldDesc("上传日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadDate;*/

    @FieldDesc("附件")
    private List<FranchiseEmpAttachments> xgwcEmpAttachments;

    /**
     * ===================================================账户=====================
     */
    @FieldDesc("开户名")
    private String accountName;

    @FieldDesc("开户行")
    private String bankName;

    @FieldDesc("银行卡号")
    private String accountNumber;

    @FieldDesc("支付宝姓名")
    private String alipayName;

    @FieldDesc("支付宝账号")
    private String alipayAccount;




    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("employeeId",getEmployeeId())
            .append("staffId",getStaffId())
            .append("name",getName())
            .append("sex",getSex())
            .append("ethnicity",getEthnicity())
            .append("birthdate",getBirthdate())
            .append("politicalStatus",getPoliticalStatus())
            .append("maritalStatus",getMaritalStatus())
            .append("idNumber",getIdNumber())
            .append("email",getEmail())
            .append("phone",getPhone())
            .append("address",getAddress())
            .append("emerName",getEmerName())
            .append("emerPhone",getEmerPhone())
            .append("entryDate",getEntryDate())
            .append("contractEndDate",getContractEndDate())
            .append("socialStatus",getSocialStatus())
            .append("probationStatus",getProbationStatus())
            .append("probationEndDate",getProbationEndDate())
            .append("annualLeaveDays",getAnnualLeaveDays())
            .append("resignationDate",getResignationDate())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
            .append("modifyTime",getModifyTime())
        .toString();
        }
}
