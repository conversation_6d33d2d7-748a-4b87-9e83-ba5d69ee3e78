package com.xgwc.user.dao;

import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.dto.SysMenuDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserMenuMapper {

    /**
     * 用户菜单权限
     * @param userId 用户id
     * @return 权限列表
     */
    @TenantIgnore
    List<String> getMenuPermissionByUserId(Long userId);

    /**
     * 角色菜单权限
     * @param roleId 角色id
     * @return 权限列表
     */
    List<String> getMenuPermissionByRoleId(Long roleId);

    List<SysMenuDto> getMenuByUserId(@Param("userId") Long userId,@Param("isAdmin") String isAdmin);

    int deleteByRoleId(Long roleId);

    int insertRoles(@Param("roleId") Long roleId,@Param("array")Long[] menuIds);
}
