package com.xgwc.user.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.FranchiseDesignerRecordMapper;
import com.xgwc.user.service.IFranchiseDesignerRecordService;
import com.xgwc.user.entity.FranchiseDesignerRecord;
import com.xgwc.user.entity.vo.FranchiseDesignerRecordVo;
import com.xgwc.user.entity.dto.FranchiseDesignerRecordDto;
import com.xgwc.user.entity.vo.FranchiseDesignerRecordQueryVo;


@Service
public class FranchiseDesignerRecordServiceImpl implements IFranchiseDesignerRecordService  {
    @Resource
    private FranchiseDesignerRecordMapper franchiseDesignerRecordMapper;

    /**
     * 查询加盟商设计师审核记录
     * 
     * @param id 加盟商设计师审核记录主键
     * @return 加盟商设计师审核记录
     */
    @Override
    public FranchiseDesignerRecordDto selectFranchiseDesignerRecordById(Long id) {
        return franchiseDesignerRecordMapper.selectFranchiseDesignerRecordById(id);
    }

    /**
     * 查询加盟商设计师审核记录列表
     * 
     * @param franchiseDesignerRecord 加盟商设计师审核记录
     * @return 加盟商设计师审核记录
     */
    @Override
    public List<FranchiseDesignerRecordDto> selectFranchiseDesignerRecordList(FranchiseDesignerRecordQueryVo franchiseDesignerRecord) {
        return franchiseDesignerRecordMapper.selectFranchiseDesignerRecordList(franchiseDesignerRecord);
    }

    /**
     * 新增加盟商设计师审核记录
     * 
     * @param dto 加盟商设计师审核记录
     * @return 结果
     */
    @Override
    public int insertFranchiseDesignerRecord(FranchiseDesignerRecordVo dto) {
        FranchiseDesignerRecord franchiseDesignerRecord = BeanUtil.copyProperties(dto, FranchiseDesignerRecord.class);
        franchiseDesignerRecord.setCreateTime(DateUtils.getNowDate());
        if(StringUtils.isEmpty(franchiseDesignerRecord.getCreateBy())){
            franchiseDesignerRecord.setCreateBy(SecurityUtils.getNickName());
        }
        return franchiseDesignerRecordMapper.insertFranchiseDesignerRecord(franchiseDesignerRecord);
    }

    /**
     * 修改加盟商设计师审核记录
     * 
     * @param dto 加盟商设计师审核记录
     * @return 结果
     */
    @Override
    public int updateFranchiseDesignerRecord(FranchiseDesignerRecordVo dto) {

        FranchiseDesignerRecord franchiseDesignerRecord = BeanUtil.copyProperties(dto, FranchiseDesignerRecord.class);
        franchiseDesignerRecord.setUpdateTime(DateUtils.getNowDate());
        return franchiseDesignerRecordMapper.updateFranchiseDesignerRecord(franchiseDesignerRecord);
    }

    /**
     * 批量删除加盟商设计师审核记录
     * 
     * @param ids 需要删除的加盟商设计师审核记录主键
     * @return 结果
     */
    @Override
    public int deleteFranchiseDesignerRecordByIds(Long[] ids) {
        return franchiseDesignerRecordMapper.deleteFranchiseDesignerRecordByIds(ids);
    }

    /**
     * 删除加盟商设计师审核记录信息
     * 
     * @param id 加盟商设计师审核记录主键
     * @return 结果
     */
    @Override
    public int deleteFranchiseDesignerRecordById(Long id) {
        return franchiseDesignerRecordMapper.deleteFranchiseDesignerRecordById(id);
    }

    @Override
    public List<FranchiseDesignerRecordDto> findFranchiseDesignerRecordListByBusinessId(Long businessId, Integer businessType) {
        return franchiseDesignerRecordMapper.selectFranchiseDesignerRecordListByBusinessId(businessId, businessType);
    }
}
