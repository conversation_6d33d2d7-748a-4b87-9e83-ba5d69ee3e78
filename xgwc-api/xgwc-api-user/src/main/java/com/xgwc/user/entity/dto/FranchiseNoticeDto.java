package com.xgwc.user.entity.dto;

import lombok.Data;

@Data
public class FranchiseNoticeDto {

    /**
     * 通知分类ID
     */
    private Integer id;

    /**
     * 通知类型：1站内信，2短信，3企微
     */
    private Integer noticeType;

    /**
     * 分类id
     */
    private Integer classifyId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发送数量
     */
    private Integer sendCount;

    /**
     * 发送人 json
     */
    private String sendGroup;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 加盟商id
     */
    private Long franchiseId;

    /**
     * 定时时间
     */
    private String scheduleTime;

    /**
     * 发送状态：0 未发送，1发送中，2.已发送，3.发送失败
     */
    private Integer sendStatus;

    /**
     * 状态
     */
    private Integer status;

}
