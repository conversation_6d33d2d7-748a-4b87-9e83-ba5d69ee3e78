package com.xgwc.user.dao;

import com.xgwc.user.entity.ReportTargetFranchiseDept;
import com.xgwc.user.entity.dto.CountAndCheckStatusDto;
import com.xgwc.user.entity.dto.ReportTargetBrandDeptDto;
import com.xgwc.user.entity.dto.ReportTargetFranchiseDeptDto;
import com.xgwc.user.entity.dto.ReportTargetFranchiseDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReportTargetFranchiseDeptMapper {

    /**
     * 批量写入
     */
    int batchInsertReportTargetFranchiseDept(List<ReportTargetFranchiseDept> reportTargetFranchiseDepts);

    /**
     * 修改部门目标
     * @param reportTargetFranchiseDept 参数
     * @return 是否成功
     */
    int updateReportTargetFranchiseDept(ReportTargetFranchiseDept reportTargetFranchiseDept);

    /**
     * 根据目标id删除目标数据
     * @param targetId 目标id
     * @return 是否成功
     */
    int deleteByTargetId(Long targetId);

    /**
     * 获取上个月的部门数据
     */
    List<ReportTargetFranchiseDept> getPreMonthReportTargetFranchiseDepts(@Param(value = "preMonth") String preMonth, @Param(value = "list") List<Long> franchiseIds);

    /**
     * 获取加盟商部门信息
     * @param franchiseIds 加盟商列表
     * @return 部门信息
     */
    List<ReportTargetFranchiseDeptDto> getFranchiseInfoByFranchiseIds(@Param(value = "list") List<Long> franchiseIds, @Param(value = "brandId") Long brandId);

    /**
     * 获取品牌商部门目标
     * @param reportTargetFranchiseDto 参数
     * @return 列表
     */
    List<ReportTargetBrandDeptDto> selectBrandTargetDeptList(ReportTargetFranchiseDto reportTargetFranchiseDto);

    /**
     * 获取加上没部门目标
     * @param reportTargetFranchiseDto 参数
     * @return 列表
     */
    List<ReportTargetBrandDeptDto> selectFranchiseTargetDeptList(ReportTargetFranchiseDto reportTargetFranchiseDto);

    /**
     * 获取加盟商部门列表-加上部门ID参数
     * @param reportTargetFranchiseDeptDto 参数
     * @return 列表
     */
    List<ReportTargetBrandDeptDto> selectFranchiseTargetDeptListByFranchiseId(ReportTargetFranchiseDeptDto reportTargetFranchiseDeptDto);

    /**
     * 根据目标id获取部门目标
     * @param targetId 目标
     */
    List<ReportTargetFranchiseDeptDto> getReportTargetFranchiseDeptsByTaskId(Long targetId);

    /**
     * 未提交的部门数
     * @param deptId 部门id
     * @return 未提交数
     */
    Integer countNotSubmitDept(Long deptId);

    /**
     * 统计数量
     */
    CountAndCheckStatusDto countAndCheckStatusDto(Long targetId);


    /**
     * 部门目标id
     * @param deptTargeId
     * @return
     */
    int freshAmount(Long deptTargeId);

}
