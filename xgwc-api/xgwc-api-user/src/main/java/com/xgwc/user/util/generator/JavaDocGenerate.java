package com.xgwc.user.util.generator;

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.util.StringUtils;
import jakarta.validation.constraints.NotNull;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.*;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class JavaDocGenerate {

    private static String type = "controller"; //生成分类：package 包扫描；controller 类

    private static String typePath = "com.xgwc.user.controller.FranchiseStaffController";//package/controller 路径

    private static String filePath = "/Users/<USER>/Documents";//生成文件路径

    private static String serviceUrl = "/api/users";

    public static void main(String[] args) throws IOException, ClassNotFoundException, NoSuchFieldException {

        createFile();
    }

    public static void createFile() throws IOException, NoSuchFieldException, ClassNotFoundException {


        List<DocTable> docTables = getDatas();
        for (DocTable docTable: docTables) {
            XWPFDocument docxDocument = new XWPFDocument();
            docxDocument.createStyles();
            setTitle(docxDocument, docTable.getTitle());
            setXWPFParagraph(docxDocument,docTable.getBases());
            setXWPFParagraph(docxDocument,docTable.getMethods());
            setXWPFParagraph(docxDocument,docTable.getResponses());
            setNullParagraph(docxDocument,"4.响应参数示例");
            FileOutputStream stream = getOSFile(docTable.getTitle());
            docxDocument.write(stream);
            stream.close();
        }
        System.out.println("文件生成完成!");
    }

    private static void setTitle(XWPFDocument docxDocument, String title) {
        XWPFParagraph paragraph = docxDocument.createParagraph();
        paragraph.setStyle("1");
        CTPPr ppr = paragraph.getCTP().isSetPPr() ? paragraph.getCTP().getPPr() : paragraph.getCTP().addNewPPr();
        ppr.addNewOutlineLvl().setVal(BigInteger.valueOf(0));

        XWPFRun runTitle = paragraph.createRun();
        runTitle.setText(title);
        runTitle.setFontFamily("宋体");
        runTitle.setBold(true);
        runTitle.setFontSize(24);


    }
    private static void setNullParagraph(XWPFDocument docxDocument) {
        XWPFParagraph paragraph = docxDocument.createParagraph();
        XWPFRun runTitle = paragraph.createRun();
        runTitle.addCarriageReturn();//回车键
        runTitle.setKerning(30);

    }

    private static void setNullParagraph(XWPFDocument docxDocument, String title) {
        setNullParagraph(docxDocument);
        XWPFParagraph paragraph = docxDocument.createParagraph();
        paragraph.setStyle("2");
        CTPPr ppr = paragraph.getCTP().isSetPPr() ? paragraph.getCTP().getPPr() : paragraph.getCTP().addNewPPr();
        ppr.addNewOutlineLvl().setVal(BigInteger.valueOf(1));

        XWPFRun runTitle = paragraph.createRun();
        runTitle.setText(title);
        runTitle.setFontFamily("宋体");
        runTitle.setBold(true);
        runTitle.setFontSize(22);

        XWPFTable table = docxDocument.createTable(1,1);table.setWidth("95%");
        table.setWidthType(TableWidthType.PCT);//设置表格相对宽度
        table.setTableAlignment(TableRowAlign.CENTER);
        XWPFTableRow row = table.getRow(0);
        row.setHeight(150);
        XWPFTableCell cell = row.getCell(0);
        cell.setWidth("95%");
        cell.setText("{\n" +
                "\n" +
                "    \"status\": 1,\n" +
                "\n" +
                "    \"message\": \"OK\",\n" +
                "\n" +
                "    \"data\": {\n" +
                "\n" +
                "        \n" +
                "\n" +
                "    }\n" +
                "\n" +
                "}");
    }
    private static void setXWPFParagraph(XWPFDocument docxDocument, Doc doc) {
        setNullParagraph(docxDocument);
        XWPFParagraph paragraph = docxDocument.createParagraph();
        paragraph.setStyle("2");
        CTPPr ppr = paragraph.getCTP().isSetPPr() ? paragraph.getCTP().getPPr() : paragraph.getCTP().addNewPPr();
        ppr.addNewOutlineLvl().setVal(BigInteger.valueOf(1));

        XWPFRun runTitle = paragraph.createRun();
        runTitle.setText(doc.getTitle());
        runTitle.setFontFamily("宋体");
        runTitle.setBold(true);
        runTitle.setFontSize(22);


        XWPFTable table = docxDocument.createTable(doc.getRow(),doc.getCol());
        table.setWidth("95%");
        table.setWidthType(TableWidthType.PCT);//设置表格相对宽度
        table.setTableAlignment(TableRowAlign.CENTER);

        if(doc.getDatas() != null)
            for(int i = 0; i < doc.getDatas().size(); i++) {
                XWPFTableRow row = table.getRow(i);
                List<String> ls =((DocData)doc.getDatas().get(i)).getData();
                for (int j = 0; j < ls.size(); j++) {
                    XWPFTableCell cell0 = row.getCell(j);
                    if(i == 0) cell0.setColor("f4f5f7");
                    if(j == 0 && doc.getCol() == 2) cell0.setWidth("30%");
                    cell0.setText(ls.get(j));
                    CTTcPr cellCtPr = getCellCTTcPr(cell0);
                    cellCtPr.addNewHMerge().setVal(STMerge.CONTINUE);
                }
            }
    }


    private static void setTableText(XWPFDocument docxDocument) {
        //获取第一个表格
        XWPFTable table = docxDocument.getTableArray(0);
        List<XWPFTableRow> rows = table.getRows();
        int i=1;
        for(XWPFTableRow row :rows){
            List<XWPFTableCell> cells = row.getTableCells();
            for(XWPFTableCell cell: cells){
                cell.setText("第"+String.valueOf(i++)+"格");
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                //cell.setWidthType(TableWidthType.PCT);
                //cell.setWidth("30%");
            }
        }

    }

    public static List<DocTable> getDatas() throws ClassNotFoundException, NoSuchFieldException {
        Class clz = null;
        if("controller".equals(type)) {
            clz = Class.forName(typePath);
        } else if("package".equals(type)) {
            clz = Class.forName("com.chenshuyi.reflect.Apple");
        }
        Method[] methods = clz.getDeclaredMethods();
        List<DocTable> docTables = new ArrayList<>();
        RequestMapping requestMapping = (RequestMapping) clz.getAnnotation(RequestMapping.class);
        String address = null;
        String path = requestMapping.value()[0];
        for (Method m : methods) {
            MethodDesc methodDesc = m.getAnnotation(MethodDesc.class);
            DocBasis basis = new DocBasis();
            basis.setTitle("1.基础信息");

            if(m.isAnnotationPresent(GetMapping.class)) {
                GetMapping annotation = m.getAnnotation(GetMapping.class);
                address = Arrays.stream(annotation.value()).map(a -> path+a).collect(Collectors.joining(","));
                if(StringUtils.isBlank(address)) address = path;
                basis.setDatas(new BasisData("请求地址",serviceUrl+address));
                basis.setDatas(new BasisData("请求方法","GET"));
            }
            if(m.isAnnotationPresent(PostMapping.class)) {
                PostMapping annotation = m.getAnnotation(PostMapping.class);
                address = Arrays.stream(annotation.value()).map(a -> path+a).collect(Collectors.joining(","));
                if(StringUtils.isBlank(address)) address = path;
                basis.setDatas(new BasisData("请求地址",serviceUrl+address));
                basis.setDatas(new BasisData("请求方法","PSOT"));
            }
            if(m.isAnnotationPresent(PutMapping.class)) {
                PutMapping annotation = m.getAnnotation(PutMapping.class);
                address = Arrays.stream(annotation.value()).map(a -> path+a).collect(Collectors.joining(","));
                if(StringUtils.isBlank(address)) address = path;
                basis.setDatas(new BasisData("请求地址",serviceUrl+address));
                basis.setDatas(new BasisData("请求方法","PUT"));
            }
            if(m.isAnnotationPresent(DeleteMapping.class)) {
                DeleteMapping annotation = m.getAnnotation(DeleteMapping.class);
                address = Arrays.stream(annotation.value()).map(a -> path+a).collect(Collectors.joining(","));
                if(StringUtils.isBlank(address)) address = path;
                basis.setDatas(new BasisData("请求地址",serviceUrl+address));
                basis.setDatas(new BasisData("请求方法","DELETE"));
            }
            if(m.isAnnotationPresent(PreAuthorize.class)) {
                PreAuthorize annotation = m.getAnnotation(PreAuthorize.class);
                String value = annotation.value();
                if(StringUtils.isNotBlank(value))
                    value = value.substring(value.indexOf("'")+1,value.lastIndexOf("'"));
                basis.setDatas(new BasisData("权限",value));
            }

            DocMethods methods1 = new DocMethods();
            methods1.setTitle("2.请求参数");
            for (Parameter parameter : m.getParameters()) {
                if(parameter.getType().getName().indexOf("java.lang") > 0) {
                    methods1.setDatas(new MethodData(parameter.getName(),parameter.getType().getSimpleName(),"true","",""));
                } else {
                    Class clzz = parameter.getType();
                    String prefix = "";
                    if(clzz.getName().equals("java.util.List")) {
                        ParameterizedType parameterizedType = (ParameterizedType) parameter.getParameterizedType();
                        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                        clzz = (Class) actualTypeArguments[0];
                        methods1.setDatas(new MethodData("data",parameter.getType().getSimpleName(),"","",""));
                        prefix = "data.";
                    }
                    Class superClzz = clzz.getSuperclass();
                    if(superClzz != null) {
                        Field[] fields = superClzz.getDeclaredFields();
                        for (Field field : fields) {
                            if(!java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                                FieldDesc dtoDesc = field.getAnnotation(FieldDesc.class);
                                Boolean notNull = field.isAnnotationPresent(NotNull.class);
                                if(field.getName().equalsIgnoreCase("id") && methodDesc != null && methodDesc.value().indexOf("新增") > -1) continue;
                                methods1.setDatas(new MethodData(prefix+field.getName(),field.getType().getSimpleName(), notNull ? notNull.toString() : "","",dtoDesc != null ? dtoDesc.value(): "" ));
                                if(field.getGenericType() instanceof ParameterizedType) {
                                    Class fxz = (Class)((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0];
                                    for (Field fxzf : fxz.getDeclaredFields()) {
                                        if(!java.lang.reflect.Modifier.isStatic(fxzf.getModifiers())) {
                                            FieldDesc fxzDesc = fxzf.getAnnotation(FieldDesc.class);
                                            Boolean fxzNull = field.isAnnotationPresent(NotNull.class);
                                            methods1.setDatas(new MethodData(field.getName()+"."+fxzf.getName(),fxzf.getType().getSimpleName(), fxzNull ? fxzNull.toString() : "","",fxzDesc != null ? fxzDesc.value(): "" ));
                                        }
                                    }
                                }
                            }
                        }
                    }
                    Field[] fields = clzz.getDeclaredFields();
                    for (Field field : fields) {
                        if(!java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                            FieldDesc dtoDesc = field.getAnnotation(FieldDesc.class);
                            Boolean notNull = field.isAnnotationPresent(NotNull.class);
                            if(field.getName().equalsIgnoreCase("id") && methodDesc != null && methodDesc.value().indexOf("新增") > -1) continue;
                            methods1.setDatas(new MethodData(prefix+field.getName(),field.getType().getSimpleName(), notNull ? notNull.toString() : "","",dtoDesc != null ? dtoDesc.value(): "" ));
                            if(field.getGenericType() instanceof ParameterizedType) {
                                Class fxz = (Class)((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0];
                                for (Field fxzf : fxz.getDeclaredFields()) {
                                    if(!java.lang.reflect.Modifier.isStatic(fxzf.getModifiers())) {
                                        FieldDesc fxzDesc = fxzf.getAnnotation(FieldDesc.class);
                                        Boolean fxzNull = field.isAnnotationPresent(NotNull.class);
                                        methods1.setDatas(new MethodData(field.getName()+"."+fxzf.getName(),fxzf.getType().getSimpleName(), fxzNull ? fxzNull.toString() : "","",fxzDesc != null ? fxzDesc.value(): "" ));
                                    }
                                }
                            }
                        }
                    }
                }
            }
            DocResponse response = new DocResponse();
            response.setTitle("3.响应参数");
            Type genericReturnType = m.getGenericReturnType();
            if(genericReturnType instanceof ParameterizedType) {
                ParameterizedType parameterizedType =(ParameterizedType) genericReturnType;
                Class typeClazz = (Class) parameterizedType.getRawType();
                Class fxClaz = null;
                if(typeClazz.getSimpleName().equals("ApiResult")) {
                    Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                    if(actualTypeArguments[0] instanceof ParameterizedType) {
                        fxClaz = (Class) ((ParameterizedType) actualTypeArguments[0]).getActualTypeArguments()[0];
                    } else
                        fxClaz = (Class) actualTypeArguments[0];
                }

                for (Field field : typeClazz.getDeclaredFields()) {
                    Class<?> clzz = field.getType();
                    if(!java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                        response.setDatas(new ResponseData(field.getName(), clzz.getSimpleName(), ""));
                        if("data".equals(field.getName())) {
                            String prefix = "data";
                            if(m.getName().equalsIgnoreCase("list")) {
                                response.setDatas(new ResponseData(prefix+".total","int","总记录数"));
                                response.setDatas(new ResponseData(prefix+".rows","int","列表数据"));
                            };
                            Class superClzz = fxClaz.getSuperclass();
                            if(superClzz != null) {
                                Field[] fields = superClzz.getDeclaredFields();
                                for (Field fxField : fields) {
                                    if(!java.lang.reflect.Modifier.isStatic(fxField.getModifiers())) {
                                        FieldDesc dtoDesc = fxField.getAnnotation(FieldDesc.class);
                                        response.setDatas(new ResponseData(prefix+"."+fxField.getName(),fxField.getType().getSimpleName(),dtoDesc != null ? dtoDesc.value() : ""));
                                        if(fxField.getGenericType() instanceof ParameterizedType) {
                                            Class fxz = (Class)((ParameterizedType) fxField.getGenericType()).getActualTypeArguments()[0];
                                            for (Field fxzf : fxz.getDeclaredFields()) {
                                                if(!java.lang.reflect.Modifier.isStatic(fxzf.getModifiers())) {
                                                    FieldDesc fxzDesc = fxzf.getAnnotation(FieldDesc.class);
                                                    response.setDatas(new ResponseData(fxField.getName()+"."+fxzf.getName(),fxzf.getType().getSimpleName(),fxzDesc != null ? fxzDesc.value() : ""));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            Field[] fields = fxClaz.getDeclaredFields();
                            for (Field fxField : fields) {
                                if(!java.lang.reflect.Modifier.isStatic(fxField.getModifiers())) {
                                    FieldDesc dtoDesc = fxField.getAnnotation(FieldDesc.class);
                                    response.setDatas(new ResponseData(prefix+"."+fxField.getName(),fxField.getType().getSimpleName(),dtoDesc != null ? dtoDesc.value() : ""));
                                    if(fxField.getGenericType() instanceof ParameterizedType) {
                                        Class fxz = (Class)((ParameterizedType) fxField.getGenericType()).getActualTypeArguments()[0];
                                        for (Field fxzf : fxz.getDeclaredFields()) {
                                            if(!java.lang.reflect.Modifier.isStatic(fxzf.getModifiers())) {
                                                FieldDesc fxzDesc = fxzf.getAnnotation(FieldDesc.class);
                                                response.setDatas(new ResponseData(fxField.getName()+"."+fxzf.getName(),fxzf.getType().getSimpleName(),fxzDesc != null ? fxzDesc.value() : ""));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }else {
                //不是参数化类型,直接获取返回值类型
                response.setDatas(new ResponseData("status","int","状态吗"));
                response.setDatas(new ResponseData("message","String","消息"));
                response.setDatas(new ResponseData("data","Object","null"));

            }



            DocTable doc = new DocTable(basis, methods1, response);
            if(methodDesc != null)
                doc.setTitle(methodDesc.value());
            else doc.setTitle(address);
            docTables.add(doc);

        }


        return docTables;
    }

    public static CTTcPr getCellCTTcPr(XWPFTableCell cell) {
        CTTc cttc = cell.getCTTc();
        CTTcPr tcPr = cttc.isSetTcPr() ? cttc.getTcPr() : cttc.addNewTcPr();
        return tcPr;
    }
    private static FileInputStream getISFile() throws IOException {
        File file = new File(filePath);
        if(!file.getParentFile().exists()) file.mkdirs();
        if(!file.exists()) file.createNewFile();
        FileInputStream stream = new FileInputStream(file);
        return stream;
    }
    private static FileOutputStream getOSFile(String fileName) throws IOException {
        if(StringUtils.isNotBlank(fileName)) fileName = fileName.replace("<", "")
                .replace(">", "")
                .replace(":","")
                .replace("/","")
                .replace("?","")
                .replace("\"","")
                .replace("|","")
                .replace("*","")
                .replace("\\","");
        File file = new File(filePath+"/"+ fileName + ".doc");
        if(!file.getParentFile().exists()) file.getParentFile().mkdirs();
        if(!file.exists()) file.createNewFile();
        FileOutputStream stream = new FileOutputStream(file);
        return stream;
    }

}
