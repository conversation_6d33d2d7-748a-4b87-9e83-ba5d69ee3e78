package com.xgwc.user.entity;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.entity
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-04  09:40
 */

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 公司主体
 */
@Data
public class XgwcCompanyInfo {

    // 主键ID
    @FieldDesc("主键ID")
    private Long id;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("品牌商id")
    private Long brandId;

    @FieldDesc("营业执照")
    private String businessLicense;

    // 公司名称
    @FieldDesc("公司名称")
    private String companyName;

    // 公司简称
    @FieldDesc("公司简称")
    private String companySimpleName;

    // 营业执照编码
    @FieldDesc("营业执照编码")
    private String licenseCode;

    // 执照有效期是否长期：0长期，1非长期
    @FieldDesc("营业执照是否长期：0长期，1非长期")
    private Long licenseIsLongterm;

    // 营业执照有效期开始日期
    @FieldDesc("营业执照有效期开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseStart;

    // 营业执照有效期结束日期
    @FieldDesc("营业执照有效期结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseEnd;

    /** 营业省 */
    @FieldDesc("营业省")
    private Integer operateProvince;

    /** 营业市 */
    @FieldDesc("营业市")
    private Integer operateCity;

    /** 营业区 */
    @FieldDesc("营业区")
    private Integer operateRegion;

    /** 详细地址 */
    @FieldDesc("详细地址")
    private String operateAddress;

    // 人员规模
    @FieldDesc("人员规模")
    private String scale;

    /** 身份证正面：加密 */
    @Excel(name = "身份证正面：加密")
    private String idcardFront;

    /** 身份证反面：加密 */
    @Excel(name = "身份证反面：加密")
    private String idcardBack;

    // 法人姓名
    @FieldDesc("法人姓名：加密")
    private String idcardName;

    // 法人身份证号(加密)
    @FieldDesc("法人身份证号(加密)")
    private String idcardNo;

    // 身份证是否长期：0长期，1非长期
    @FieldDesc("身份证是否长期：0长期，1非长期")
    private Long idcardIsLongterm;

    // 法人身份证有效期开始日期
    @FieldDesc("法人身份证有效期开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date idcardStart;

    // 法人身份证有效期结束日期
    @FieldDesc("法人身份证有效期结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date idcardEnd;

    // 法人手机号(加密)
    @FieldDesc("法人手机号(加密)")
    private String idcardPhone;

    // 法人邮箱(加密)
    @FieldDesc("法人邮箱(加密)")
    private String idcardEmail;

    // 开户名
    @FieldDesc("开户名：加密")
    private String bankUserName;

    // 开户行
    @FieldDesc("开户行")
    private String bankName;

    // 对公账号(加密)
    @FieldDesc("对公账号(加密)")
    private String bankNo;

    @FieldDesc("状态：0-禁用 1-启用")
    private Integer status;

    // 创建人
    private String createBy;

    // 创建时间
    private Date createTime;

    // 修改人
    private String updateBy;

    // 修改时间
    private Date updateTime;

    // 行更新时间
    private Date modifyTime;
}
