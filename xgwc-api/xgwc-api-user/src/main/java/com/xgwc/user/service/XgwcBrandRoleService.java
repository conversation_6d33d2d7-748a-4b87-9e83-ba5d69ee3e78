package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.dto.XgwcBrandRoleDto;
import com.xgwc.user.entity.param.XgwcBrandRoleParam;
import com.xgwc.user.entity.vo.XgwcBrandRoleVo;

import java.util.List;
import java.util.Set;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  10:52
 */
public interface XgwcBrandRoleService {

    /**
     * 获取品牌角色列表
     * @param xgwcBrandRoleParam 查询参数
     * @return 品牌角色列表
     */
    List<XgwcBrandRoleVo> getXgwcBrandRoleList(XgwcBrandRoleParam xgwcBrandRoleParam);

    /**
     * 保存品牌角色
     * @param xgwcBrandRoleDto 保存参数
     * @return 保存结果
     */
    ApiResult saveXgwcBrandRole(XgwcBrandRoleDto xgwcBrandRoleDto);

    /**
     * 根据品牌角色ID获取品牌角色
     * @param roleId 品牌角色ID
     * @return 品牌角色
     */
    ApiResult getXgwcBrandRoleById(Integer roleId);

    /**
     * 更新品牌角色
     * @param xgwcBrandRoleDto 更新参数
     * @return 更新结果
     */
    ApiResult updateXgwcBrandRole(XgwcBrandRoleDto xgwcBrandRoleDto);

    /**
     * 更新品牌角色状态
     * @param roleId 品牌角色ID
     * @param status 品牌角色状态
     * @return 更新结果
     */
    ApiResult updateStatusById(Integer roleId, Integer status);

    /**
     * 获取品牌角色菜单树
     * @return 菜单树
     */
    ApiResult getMenuTreeData(String isFlag);

    /**
     * 获取品牌角色数据权限
     * @return 数据权限
     */
    ApiResult getRoleDataScope();

    /**
     * 根据用户ID获取品牌角色标识
     * @param userId 用户ID
     * @return 品牌角色Key集合
     */
    Set<String> selectBrandRoleKeyByUserId(Long userId);

    /**
     * 根据用户ID和类型获取菜单
     * @param userId 用户ID
     * @param modelType 类型
     * @return 菜单集合
     */
    List<SysMenuDto> selectMenuTreeByUserIdAndModelType(Long userId,String modelType);

    /**
     * 根据用户ID获取品牌角色
     * @param userId 用户ID
     * @return 品牌角色集合
     */
    List<XgwcBrandRoleVo> selectRoleByUserId(Long userId);

    /**
     * 根据用户ID获取菜单
     *
     * @param userId 用户ID
     * @return 菜单集合
     */
    List<SysMenuDto> selectMenuByUserId(Long userId);

    /**
     * 根据用户登录类型获取菜单表权限
     *
     * @return 权限集合
     */
    Set<String> selectMenuTreeByModelType(String modelType);

    /**
     * 根据用户ID获取品牌商权限
     * @param userId 用户ID
     * @return 权限集合
     */
    Set<String> selectSysMenuBrandByUserId(Long userId);

    /**
     * 根据用户ID获取加盟商权限
     * @param userId 用户ID
     * @return 权限集合
     */
    Set<String> selectSysMenuFranchiseByUserId(Long userId);


    /**
     * 根据用户ID删除品牌角色用户关系
     * @param userId 用户ID
     */
    void deleteBrandRoleUserByUserId(Long userId);

    /**
     * 添加角色用户关系
     * @param roleId 角色ID
     * @param userId 用户ID
     * @param isFlag 标识字段（0-品牌商 1-加盟商 2-服务商）
     */
    void saveBrandRoleUser(Long roleId,Long userId, Integer isFlag);

    /**
     * 根据用户ID获取服务商权限
     * @param userId 用户ID
     * @return 权限集合
     */
    Set<String> selectSysMenuServiceByUserId(Long userId);

    /**
     * 根据用户ID获取销售服务商权限
     * @param userId 用户ID
     * @return 权限集合
     */
    Set<String> selectSysMenuMarketByUserId(Long userId);
}
