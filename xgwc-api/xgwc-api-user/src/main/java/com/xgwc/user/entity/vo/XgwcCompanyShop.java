package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 公司主体和店铺关联表
 */
@Data
public class XgwcCompanyShop {

    @FieldDesc("主键ID")
    private Long id;

    @FieldDesc("公司主体ID")
    private Long companyId;

    @FieldDesc("店铺ID")
    private Long shopId;

    // 创建人
    @FieldDesc("创建人")
    private String createBy;

    // 创建时间
    @FieldDesc("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    // 修改人
    @FieldDesc("修改人")
    private String updateBy;

    // 修改时间
    @FieldDesc("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;



}
