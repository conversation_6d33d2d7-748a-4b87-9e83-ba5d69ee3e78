package com.xgwc.user.dao;

import com.xgwc.user.entity.dto.SysUserMiddleDto;
import com.xgwc.user.entity.vo.SysUserMiddleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysUserMiddleMapper {

    /**
     * 插入用户中间表信息
     * @param sysUserMiddleVo 参数
     * @return 是否成功
     */
    int insertSysUserMiddle(SysUserMiddleVo sysUserMiddleVo);

    /**
     * 更新用户中间表来来源ID
     * @param id 中间表主键
     * @param sourceId 来源ID
     * @return 是否成功
     */
    int updateSysUserMiddleSourceId(@Param(value = "id") Long id, @Param(value = "sourceId") Long sourceId);

    /**
     * 修改中间态状态
     * @param sysUserMiddleVo 参数
     * @return 是否成功
     */
    int updateSysUserMiddleStatus(SysUserMiddleVo sysUserMiddleVo);


    /**
     * 根据sys_user表中的主键id查询中间表的userid列表
     * @param mainUserId 主用户id
     * @return 用户中间信息
     */
    List<SysUserMiddleDto> selectSysUserMiddleListByUserId(Long mainUserId);

    /**
     * 根据用户id，userType, sourceId确定唯一值
     * @param sysUserMiddleVo 参数
     * @return 中间用户
     */
    List<SysUserMiddleDto> selectSysUserMiddleByCondition(SysUserMiddleVo sysUserMiddleVo);

    /**
     * 根据用户id获取中间用户信息
     * @param id 中间用户ID
     * @return 中间用户
     */
    SysUserMiddleDto selectSysUserMiddleById(Long id);

    /**
     * 根据主用户id和userType获取中间用户信息
     * @param mainUserId 主用户ID
     * @param userType 用户类型
     * @return 中间用户
     */
    SysUserMiddleDto selectSysUserMiddleByUserIdAndUserType(@Param(value = "userId") Long mainUserId,@Param(value = "userType") Integer userType);

    /**
     * 根据主用户id和userType和来源ID获取中间用户信息
     * @param mainUserId 主用户ID
     * @param userType 用户类型
     * @param sourceId 来源ID
     * @return 中间用户
     */
    SysUserMiddleDto selectSysUserMiddleByUserIdAndUserTypeAndSourceId(@Param(value = "userId") Long mainUserId,@Param(value = "userType") Integer userType,@Param(value = "sourceId") Long sourceId);

    /**
     * 根据主用户id和userType和来源ID获取中间用户信息
     * @param mainUserId 主用户ID
     * @param userType 用户类型
     * @param sourceId 来源ID
     * @return 中间用户
     */
    SysUserMiddleDto findSysUserMiddleByUserIdAndUserTypeAndSourceId(@Param(value = "userId") Long mainUserId,@Param(value = "userType") Integer userType,@Param(value = "sourceId") Long sourceId);
}
