package com.xgwc.user.service.impl;

import java.util.Arrays;
import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import com.xgwc.user.dao.UserMenuMapper;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.SysRoleMapper;
import com.xgwc.user.service.ISysRoleService;
import com.xgwc.user.entity.SysRole;
import com.xgwc.user.entity.vo.SysRoleVo;
import com.xgwc.user.entity.dto.SysRoleDto;
import com.xgwc.user.entity.vo.SysRoleQueryVo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;


@Service
public class SysRoleServiceImpl implements ISysRoleService  {
    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private UserMenuMapper userMenuMapper;

    /**
     * 查询角色管理
     * 
     * @param id 角色管理主键
     * @return 角色管理
     */
    @Override
    public SysRoleDto selectSysRoleById(Long id) {
        return sysRoleMapper.selectSysRoleById(id);
    }

    /**
     * 查询角色管理列表
     * 
     * @param sysRole 角色管理
     * @return 角色管理
     */
    @Override
    public List<SysRoleDto> selectSysRoleList(SysRoleQueryVo sysRole) {
        return sysRoleMapper.selectSysRoleList(sysRole);
    }

    /**
     * 新增角色管理
     * 
     * @param dto 角色管理
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSysRole(SysRoleVo dto) {

        SysRole sysRole = BeanUtil.copyProperties(dto, SysRole.class);
        sysRole.setCreateTime(DateUtils.getNowDate());
        sysRoleMapper.insertSysRole(sysRole);
        //绑定菜单
//        userMenuMapper.insertRoles(sysRole.getId(), dto.getMenuIds());
        return 1;
    }

    /**
     * 修改角色管理
     * 
     * @param dto 角色管理
     * @return 结果
     */
    @Override
    @Transactional
    public int updateSysRole(SysRoleVo dto) {
        SysRole sysRole = BeanUtil.copyProperties(dto, SysRole.class);
        sysRole.setUpdateTime(DateUtils.getNowDate());
        //绑定菜单
//        userMenuMapper.deleteByRoleId(sysRole.getId());
//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//            @Override
//            public void afterCommit() {
//                userMenuMapper.insertRoles(sysRole.getId(), dto.getMenuIds());
//            }
//        });
        return sysRoleMapper.updateSysRole(sysRole);
    }

    /**
     * 批量删除角色管理
     * 
     * @param ids 需要删除的角色管理主键
     * @return 结果
     */
    @Override
    public int deleteSysRoleByIds(Long[] ids) {
        return sysRoleMapper.deleteSysRoleByIds(ids);
    }

    /**
     * 删除角色管理信息
     * 
     * @param id 角色管理主键
     * @return 结果
     */
    @Override
    public int deleteSysRoleById(Long id) {
        return sysRoleMapper.deleteSysRoleById(id);
    }
}
