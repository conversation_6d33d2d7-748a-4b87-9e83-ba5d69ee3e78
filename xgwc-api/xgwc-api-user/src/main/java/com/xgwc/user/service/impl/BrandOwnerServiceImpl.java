package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.xgwc.common.constants.ModelTypeConstant;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.redis.constants.UserCacheKey;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.user.dao.*;
import com.xgwc.user.entity.BrandOwner;
import com.xgwc.user.entity.Staff;
import com.xgwc.user.entity.SysCompany;
import com.xgwc.user.entity.dto.BrandOwnerDto;
import com.xgwc.user.entity.dto.BrandOwnerSimpleDto;
import com.xgwc.user.entity.dto.LoginResult;
import com.xgwc.user.entity.dto.SysUserMiddleDto;
import com.xgwc.user.entity.vo.BrandApplyVo;
import com.xgwc.user.entity.vo.BrandOwnerQueryVo;
import com.xgwc.user.entity.vo.BrandOwnerVo;
import com.xgwc.user.security.constants.SecurityConstants;
import com.xgwc.user.service.IBrandOwnerService;
import com.xgwc.user.service.ISysTenantService;
import com.xgwc.user.service.SysUserService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Service
public class BrandOwnerServiceImpl implements IBrandOwnerService  {
    @Resource
    private BrandOwnerMapper brandOwnerMapper;
    @Resource
    private ISysTenantService sysTenantService;
    @Resource
    private SysUserMiddleMapper sysUserMiddleMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysCompanyMapper sysCompanyMapper;
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 查询品牌商管理
     * 
     * @param brandId 品牌商管理主键
     * @return 品牌商管理
     */
    @Override
    public BrandOwnerDto selectBrandOwnerByBrandId(Long brandId) {
        return brandOwnerMapper.selectBrandOwnerByBrandId(brandId);
    }

    /**
     * 查询品牌商管理列表
     * 
     * @param brandOwner 品牌商管理
     * @return 品牌商管理
     */
    @Override
    public List<BrandOwnerDto> selectBrandOwnerList(BrandOwnerQueryVo brandOwner) {
        List<BrandOwnerDto> list = brandOwnerMapper.selectBrandOwnerList(brandOwner);
        if(!list.isEmpty()){
            for (BrandOwnerDto item : list) {
                if(StringUtils.isNotEmpty(item.getManagerPhone())){
                    item.setManagerPhone(ParamDecryptUtil.decryptParam(item.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
                }
            }
        }
        return list;
    }

    /**
     * 新增品牌商管理
     * 
     * @param dto 品牌商管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public long insertBrandOwner(BrandOwnerVo dto) {
        // 校验是否存在
        BrandOwnerVo brandOwnerVo = brandOwnerMapper.selectBrandOwnerByMobile(dto.getManagerPhone());
        if (brandOwnerVo!= null) {
            throw new ApiException("手机号已存在");
        }
        BrandOwner brandOwner = BeanUtil.copyProperties(dto, BrandOwner.class);
        brandOwner.setCreateTime(DateUtils.getNowDate());
        brandOwnerMapper.insertBrandOwner(brandOwner);
        return brandOwner.getBrandId().longValue();
    }

    /**
     * 修改品牌商管理
     * 
     * @param dto 品牌商管理
     * @return 结果
     */
    @Override
    public int updateBrandOwner(BrandOwnerVo dto) {
        BrandOwner brandOwner = BeanUtil.copyProperties(dto, BrandOwner.class);
        brandOwner.setUpdateTime(DateUtils.getNowDate());
        return brandOwnerMapper.updateBrandOwner(brandOwner);
    }

    @Override
    public List<BrandOwnerDto> selectBrandList() {
        String brandIds = SecurityUtils.getSysUser().getBrandIds();
        if(StringUtils.isEmpty(brandIds)){
            return List.of();
        }
        List<Long> brandIdList = Arrays.asList(brandIds.split(",")).stream().map(Long::parseLong).toList();
        return brandOwnerMapper.selectBrandOwnerByBrandIds(brandIdList);
    }

    @Override
    public List<BrandOwnerSimpleDto> getAllBrandOwner() {
        return brandOwnerMapper.selectAllBrandOwner();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResult brandApple(BrandApplyVo brandApplyVo) {
        if(brandApplyVo.getMainUserId() != null){
            SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleByUserIdAndUserType(brandApplyVo.getMainUserId(), 1);
            if(sysUserMiddleDto != null){
                throw new ApiException("已注册品牌商");
            }
            // 校验用户名唯一
            if(userMapper.getUserInfoByUserName(brandApplyVo.getContact()) != null){
                throw new ApiException(ApiStatusEnums.USER_NAME_EXISTS.getMessage() ,ApiStatusEnums.USER_NAME_EXISTS.getStatus());
            }
            SysUser sysUser = userMapper.getUserInfoByUserId(brandApplyVo.getMainUserId());
            if(sysUser == null){
                throw new ApiException("用户不存在");
            }
            sysUser.setUserName(brandApplyVo.getContact());
            sysUser.setMainUserId(brandApplyVo.getMainUserId());
            sysUser.setUserType(1);
            userMapper.updateSysUser(sysUser);
            // 新增用户中间表
            Long userId = sysUserService.insertSysUserMiddle(sysUser, null);
            // 新增品牌商
            BrandOwnerVo brandOwnerVo = new BrandOwnerVo();
            brandOwnerVo.setCompanyName(brandApplyVo.getCompanyName());
            brandOwnerVo.setContact(brandApplyVo.getContact());
            brandOwnerVo.setManagerPhone(sysUser.getPhone());
            brandOwnerVo.setPassword(sysUser.getPassword());
            brandOwnerVo.setUserId(userId);
            brandOwnerVo.setStatus(1);
            long brandId = insertBrandOwner(brandOwnerVo);
            // 更新用户中间表
            sysUserService.updateSysUserMiddleSourceId(userId, brandId);
            LoginResult loginResult = new LoginResult();
            loginResult.setToken(sysUserService.createToken(userId, brandApplyVo.getMainUserId()));
            loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
            return loginResult;
        }
        return new LoginResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int openBrandOwner(Long brandId) {
        BrandOwnerDto brandOwnerDto = brandOwnerMapper.selectBrandOwnerByBrandId(brandId);
        if(brandOwnerDto == null){
            throw new ApiException("品牌商不存在");
        }
        if(brandOwnerDto.getStatus() == 0){
            throw new ApiException("品牌商已开通");
        }
        BrandOwner brandOwner = BeanUtil.copyProperties(brandOwnerDto, BrandOwner.class);
        brandOwner.setStatus(0);
        brandOwner.setUpdateTime(DateUtils.getNowDate());
        int result = brandOwnerMapper.updateBrandOwner(brandOwner);
        // 更新用户表
        SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleById(brandOwnerDto.getUserId());
        SysUser sysUser = new SysUser();
        sysUser.setMainUserId(sysUserMiddleDto.getMainUserId());
        sysUser.setUserType(1);
        sysUser.setBrandId(brandId);
        userMapper.updateSysUser(sysUser);
        // 开通成功后，给品牌商开通对应的权限
        Long roleId = sysUserService.initRole(sysUserMiddleDto.getUserId(), ModelTypeConstant.BRAND_ADMIN, brandOwnerDto.getCompanyName(), brandId, 1);
//        // 添加母公司信息
        Long companyId = addCompany(brandOwnerDto, brandId);
//        // 员工表添加数据
        addStaff(roleId, companyId, brandId,brandOwnerDto.getContact(),sysUserMiddleDto.getUserId(),brandOwnerDto.getManagerPhone());
        // 清除缓存
        redisUtil.remove(UserCacheKey.LOAD_USERBYUSERID + sysUserMiddleDto.getUserId());
        return result;
    }

    @Override
    public BrandOwnerDto selectBrandOwnerByManagerUserId(Long managerUserId) {
        return brandOwnerMapper.selectBrandOwnerByManagerUserId(managerUserId);
    }

    private Long addCompany(BrandOwnerDto reqVO, Long brandId) {
        SysCompany sysCompany = new SysCompany();
        sysCompany.setPid(0L);
        sysCompany.setBrandOwnerId(brandId);
        sysCompany.setName(reqVO.getCompanyName());
        sysCompany.setCompanyType(0);
        sysCompany.setStatus(0);
        sysCompany.setCreateTime(new Date());
        sysCompany.setCreateBy(SecurityUtils.getNickName());
        sysCompanyMapper.insertSysCompany(sysCompany);
        return Long.valueOf(sysCompany.getId());
    }

    private void addStaff(Long roleId, Long companyId,Long brandId, String name,Long userId,String phone) {
        Staff staff = new Staff();
        staff.setName(name);
        staff.setStageName(name);
        staff.setJobNature(0);
        staff.setRoleIds(roleId.toString());
        staff.setCompanyId(companyId);
        staff.setBrandId(brandId.intValue());
        staff.setCreateBy(name);
        staff.setBindUserId(userId);
        staff.setBindStatus(1);
        staff.setLoginPhone(phone);
        staffMapper.insertStaff(staff);
    }
}
