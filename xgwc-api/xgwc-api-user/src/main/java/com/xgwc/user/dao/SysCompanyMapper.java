package com.xgwc.user.dao;

import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.SysCompany;
import com.xgwc.user.entity.dto.SysCompanyDto;
import com.xgwc.user.entity.vo.SysCompanyQueryVo;
import com.xgwc.user.entity.vo.SysCompanyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@TenantIgnore
public interface SysCompanyMapper  {
    /**
     * 查询公司管理
     * 
     * @param id 公司管理主键
     * @return 公司管理
     */
    public SysCompanyDto selectSysCompanyById(Long id);

    /**
     * 查询公司管理列表
     * 
     * @param sysCompany 公司管理
     * @return 公司管理集合
     */
    public List<SysCompanyVo> selectSysCompanyList(@Param("sysCompany") SysCompanyQueryVo sysCompany);

    /**
     * 新增公司管理
     * 
     * @param sysCompany 公司管理
     * @return 结果
     */
    public int insertSysCompany(SysCompany sysCompany);

    /**
     * 修改公司管理
     * 
     * @param sysCompany 公司管理
     * @return 结果
     */
    public int updateSysCompany(SysCompany sysCompany);

    /**
     * 批量修改状态
     *
     * @param id id
     * @param status 状态
     * @return 结果
     */
    public int updateStatus(@Param("id") Integer id,@Param("status") Integer status);

    /**
     * 查询公司管理下拉框
     *
     * @return 结果
     */
    List<SysCompanyDto> selectCompanyDropDown(Long brandOwnerId);
}
