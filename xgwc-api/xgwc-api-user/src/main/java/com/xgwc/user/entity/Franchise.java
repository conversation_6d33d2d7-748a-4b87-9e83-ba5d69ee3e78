package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class Franchise {

private static final long serialVersionUID=1L;

    /**
     * 加盟商id
     */
    private Long id;

    /**
     * 管理员名称
     */
    private String managerName;

    /**
     * 加盟商名称
     */
    private String franchiseName;

    /**
     * 管理员手机号
     */
    private String managerPhone;

    /**
     * 管理员用户id
     */
    private Long managerUserId;

    /**
     * 状态：0正常，其余非正常
     */
    private Integer status;

    /**
     * 是否删除
     */
    private Integer isDel;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更信人
     */
    private String updateBy;

}