package com.xgwc.user.dao;

import com.xgwc.user.entity.dto.AgreementDto;
import com.xgwc.user.entity.vo.AgreementVo;

import java.util.List;

public interface BrandAgreementMapper {

    /**
     * 插入协议
     * @param agreementVo 参数
     * @return 是否成功
     */
    int insertAgreement(AgreementVo agreementVo);

    /**
     * 根据品牌ID查询协议
     * @param brandId 品牌ID
     * @return 协议
     */
    AgreementDto getAgreementByBrandId(Long brandId);

    /**
     * 根据品牌id列表查询协议列表
     * @param brandIds 品牌id列表
     * @return 协议列表
     */
    List<AgreementDto> getAgreementByBrandIds(List<Long> brandIds);

    /**
     * 修改协议
     * @param agreementVo 参数
     * @return 是否成功
     */
    int updateAgreement(AgreementVo agreementVo);

    /**
     * 根据协议id获取协议
     * @param agreementId 协议id
     * @return 协议
     */
    AgreementDto getAgreementById(Integer agreementId);

    /**
     * 获取协议是否存在
     * @param agreementId 协议id
     * @return 协议
     */
    int eixstAgreementById(Integer agreementId);
}
