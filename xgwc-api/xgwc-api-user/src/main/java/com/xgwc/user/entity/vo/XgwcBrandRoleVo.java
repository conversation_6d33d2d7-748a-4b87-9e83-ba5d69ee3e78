package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: kouwen<PERSON>huo
 * @CreateTime: 2025-04-22  15:51
 */


/**
 *角色管理
 */
@Data
public class XgwcBrandRoleVo{

    /**
     * 角色主键
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    @FieldDesc("岗位ID")
    private Long stationId;

    /**
     * 品牌商id
     */
    private Long brandOwnerId;

    /**
     * 排序：越小越前
     */
    private Integer sort;

    /**
     * 状态：0正常，1禁用
     */
    private Integer status;

    /**
     * 标识字段
     */
    private String isFlag;


    /**
     * 是否删除：0正常，1删除
     */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 行更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
