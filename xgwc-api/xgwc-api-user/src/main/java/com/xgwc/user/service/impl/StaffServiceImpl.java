package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.*;
import com.xgwc.user.entity.Staff;
import com.xgwc.user.entity.StaffLog;
import com.xgwc.user.entity.dto.BrandDeptStaff;
import com.xgwc.user.entity.dto.SimpleDeptUserInfoDto;
import com.xgwc.user.entity.dto.StaffDto;
import com.xgwc.user.entity.dto.XgwcBrandDeptDto;
import com.xgwc.user.entity.vo.FranchiseOwnerVO;
import com.xgwc.user.entity.vo.StaffQueryVo;
import com.xgwc.user.entity.vo.StaffVo;
import com.xgwc.user.entity.vo.SysRoleMenuVo;
import com.xgwc.user.service.FranchiseRoleService;
import com.xgwc.user.service.IStaffService;
import com.xgwc.user.util.ChangeLogUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.xgwc.common.constants.CommonConstant.FLAG_BRAND;
import static com.xgwc.common.constants.CommonConstant.FLAG_FRANCHISE;
import static com.xgwc.common.util.ParamDecryptUtil.PHONE_KEY;


@Slf4j
@Service
public class StaffServiceImpl implements IStaffService  {
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private StaffLogMapper staffLogMapper;

    @Resource
    private XgwcBrandDeptMapper xgwcBrandDeptMapper;

    @Resource
    private XgwcBrandRoleMapper xgwcBrandRoleMapper;
    @Resource
    private FranchiseRoleService franchiseRoleService;

    /**
     * 派单管理-派单工作台（品牌商）菜单id
     */
    public static final Integer MENU_ID_DISPATCH_WORK_STAFF = 70;

    /**
     * 查询员工管理
     * 
     * @param id 员工管理主键
     * @return 员工管理
     */
    @Override
    public StaffDto selectStaffById(Long id) {
        return staffMapper.selectStaffById(id);
    }

    /**
     * 查询员工管理列表
     * 
     * @param staff 员工管理
     * @return 员工管理
     */
    @Override
    public List<StaffDto> selectStaffList(StaffQueryVo staff) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null) return List.of();
        staff.setBrandId(brandId);
        if(StringUtils.isNotEmpty(staff.getLoginPhone())){
            staff.setLoginPhone(ParamDecryptUtil.encrypt(staff.getLoginPhone(), PHONE_KEY));
        }
        List<StaffDto> list = staffMapper.selectStaffList(staff);
        if(!list.isEmpty()){
            for(StaffDto staffDto : list){
                if(StringUtils.isNotEmpty(staffDto.getLoginPhone())){
                    staffDto.setLoginPhone(ParamDecryptUtil.decryptParam(staffDto.getLoginPhone(), ParamDecryptUtil.PHONE_KEY));
                }
            }
        }
        return list;
    }

    /**
     * 新增员工管理
     * 
     * @param dto 员工管理
     * @return 结果
     */
    @Override
    public int insertStaff(StaffVo dto) {
        Staff staff = BeanUtil.copyProperties(dto, Staff.class);
        staff.setCreateBy(SecurityUtils.getNickName());
        staff.setCreateTime(DateUtils.getNowDate());
        return staffMapper.insertStaff(staff);
    }

    /**
     * 修改员工管理
     * 
     * @param dto 员工管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStaff(StaffVo dto) {
        StaffDto oldStaff = staffMapper.selectStaffById(dto.getId());
        if (oldStaff == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        Staff newStaff = BeanUtil.copyProperties(dto, Staff.class);
        newStaff.setUpdateBy(SecurityUtils.getNickName());
        newStaff.setUpdateTime(DateUtils.getNowDate());
        int result = staffMapper.updateStaff(newStaff);
        // 添加修改日志
        if (result > 0) {
            StaffLog log = ChangeLogUtil.buildAggregatedLog(
                    oldStaff,
                    newStaff,
                    dto.getId(),
                    newStaff.getUpdateBy(),
                    newStaff.getUpdateTime(),
                    "修改员工"
            );
            if (log != null) {
                log.setBusinessType(1);
                log.setBusinessId(oldStaff.getBrandId());
                staffLogMapper.insertStaffLog(log);
            }
        }
        if(StringUtils.isNotEmpty(newStaff.getRoleIds()) && oldStaff.getBindUserId() != null){
            //删除旧的角色
            xgwcBrandRoleMapper.deleteBrandRoleUser(oldStaff.getBindUserId());
            String[] roleIds = newStaff.getRoleIds().split(",");
            for(String roleId : roleIds){
                xgwcBrandRoleMapper.insertBrandRoleUser(Long.valueOf(roleId), oldStaff.getBindUserId(),0);
            }
        } else if(oldStaff.getBindUserId() != null){
            //删除旧的角色
            xgwcBrandRoleMapper.deleteBrandRoleUser(oldStaff.getBindUserId());
        }
        return result;
    }

    /**
     * 批量删除员工管理
     * 
     * @param ids 需要删除的员工管理主键
     * @return 结果
     */
    @Override
    public int deleteStaffByIds(Long[] ids) {
        return staffMapper.deleteStaffByIds(ids);
    }

    /**
     * 删除员工管理信息
     * 
     * @param id 员工管理主键
     * @return 结果
     */
    @Override
    public int deleteStaffById(Long id) {
        validateStaffExists(id);
        return staffMapper.deleteStaffById(id);
    }

    @Override
    public StaffDto selectStaffByNameAndBrandId(String name,Long brandId) {
        return staffMapper.selectStaffByNameAndBrandId(name,brandId);
    }

    /**
     * 查询员工管理下拉框
     *
     * @return 员工管理下拉框
     */
    @Override
    public List<Map<String, Object>> selectStaffListDropDown() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        List<StaffDto> staffDtos = staffMapper.selectStaffListDropDown(brandId);
        List<Map<String, Object>> result = new ArrayList<>(staffDtos.size());

        for (StaffDto staffDto : staffDtos) {
            String name = Optional.ofNullable(staffDto.getName()).orElse("");
            String stageName = Optional.ofNullable(staffDto.getStageName()).orElse("");
            String companyName = Optional.ofNullable(staffDto.getCompanyName()).orElse("");

            String combinedName = name + "/" + stageName + "/" + companyName;

            Map<String, Object> item = new HashMap<>(2);
            item.put("id", staffDto.getId());
            item.put("name", combinedName);

            result.add(item);
        }

        return result;
    }

    @Override
    public int updateBindStatus(Long id, Long bindUserId, Integer bindStatus,String phone,String stageName) {
        validateStaffExists(id);
        return staffMapper.updateBindStatus(id, bindUserId, bindStatus,phone,stageName);
    }

    @Override
    public List<BrandDeptStaff> getDeptStaffTree() {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null) {
            log.error("非品牌商员工不能获取:{}", JSONObject.toJSONString(sysUser));
        }
        List<XgwcBrandDeptDto> xgwcBrandDeptDtos = xgwcBrandDeptMapper.getBrandDeptByBrandId(brandId);
        if(xgwcBrandDeptDtos != null && !xgwcBrandDeptDtos.isEmpty()){
            List<BrandDeptStaff> brandDeptStaffs = convertToDeptStaff(xgwcBrandDeptDtos);
            //获取所有的员工
            List<SimpleDeptUserInfoDto> deptUserInfoDtos = staffMapper.getStaffUserInfoListByBrandId(brandId, null);
            if(deptUserInfoDtos != null && !deptUserInfoDtos.isEmpty()){
                Map<Long, List<SimpleDeptUserInfoDto>> deptUserMap = deptUserInfoDtos.stream().collect(Collectors.groupingBy(SimpleDeptUserInfoDto::getDeptId));
                populateUsers(brandDeptStaffs, deptUserMap);
            }
            return brandDeptStaffs;
        }
        return List.of();
    }

    @Override
    public List<SimpleDeptUserInfoDto> getBrandStaffDropDown(Long deptId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId != null) {
            return staffMapper.getStaffUserInfoListByBrandId(brandId, deptId);
        }
        return List.of();
    }

    @Override
    public int updateStaffStatus(Long id, Integer status) {
        return staffMapper.updateStaffStatus(id, status);
    }

    @Override
    public StaffDto selectStaffByBindUserId(Long userId) {
        return staffMapper.findStaffByBindUserId(userId);
    }

    /**
     * 获取员工可下载次数
     *
     * @param userId  用户id
     * @param isFlag  0-品牌商 1-加盟商
     * @param brandId 品牌id
     * @return 结果
     */
    @Override
    public StaffDto getStaffDownloadCountByUserId(Long userId, Integer isFlag, Long brandId) {
        StaffDto staffDto = new StaffDto();

        if (Objects.equals(isFlag, FLAG_BRAND)) {
            staffDto = staffMapper.findStaffByBindUserId(userId);
            if (staffDto == null) {
                return null;
            }

            String roleIds = staffDto.getRoleIds();
            if (StringUtils.isNotEmpty(roleIds)) {
                List<Integer> integerRoleIds = Arrays.stream(roleIds.split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .toList();
                Integer maxDownloadLimit = xgwcBrandRoleMapper.getXgwcBrandRoleDataByIds(integerRoleIds);
                staffDto.setMaxDownloadCount(maxDownloadLimit == null ? 0 : maxDownloadLimit);
            } else {
                staffDto.setMaxDownloadCount(0);
            }
            return staffDto;

        } else if (Objects.equals(isFlag, FLAG_FRANCHISE)) {
            staffDto = staffMapper.findFranchiseStaffByBindUserId(userId);
            ApiResult apiResult = franchiseRoleService.selectRoleDownloadLimit();
            if (apiResult == null || apiResult.getData() == null) {
                staffDto.setDownloadCount(0);
                staffDto.setMaxDownloadCount(0);
                return staffDto;
            }

            List<FranchiseOwnerVO> franchiseOwnerVOS;
            try {
                franchiseOwnerVOS = (List<FranchiseOwnerVO>) apiResult.getData();
            } catch (ClassCastException e) {
                log.error("无法将 ApiResult.data 强制转换为 List<FranchiseOwnerVO>", e);
                staffDto.setDownloadCount(0);
                staffDto.setMaxDownloadCount(0);
                return staffDto;
            }

            FranchiseOwnerVO matchedOwner = franchiseOwnerVOS.stream()
                    .filter(owner -> brandId != null && brandId.equals(owner.getBrandId()))
                    .findFirst()
                    .orElse(null);

            if (matchedOwner != null) {
                staffDto.setDownloadCount(matchedOwner.getDownloadCount() == null ? 0 : matchedOwner.getDownloadCount());
                staffDto.setMaxDownloadCount(matchedOwner.getDownloadLimit() == null ? 0 : matchedOwner.getDownloadLimit());
            } else {
                staffDto.setDownloadCount(0);
                staffDto.setMaxDownloadCount(0);
            }
            return staffDto;

        } else {
            return null;
        }
    }


    /**
     * 根据派单管理-派单工作台菜单 查询有该菜单权限的员工信息
     *
     * @return 员工信息
     */
    @Override
    public ApiResult getStaffInfoByMenu() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        List<SysRoleMenuVo> sysRoleMenuVos = xgwcBrandRoleMapper.selectRoleByMenu(MENU_ID_DISPATCH_WORK_STAFF, brandId);
        if (CollectionUtils.isEmpty(sysRoleMenuVos)) {
            return ApiResult.ok(Collections.emptyList());
        }

        List<StaffDto> staffDtos = staffMapper.selectStaffByBrandId(brandId);
        if (CollectionUtils.isEmpty(staffDtos)) {
            return ApiResult.ok(Collections.emptyList());
        }

        // 提取所有 sysRoleMenuVos roleId
        Set<Long> targetRoleIds = sysRoleMenuVos.stream()
                .map(SysRoleMenuVo::getRoleId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 过滤 staffDtos
        List<StaffDto> filteredStaffList = staffDtos.stream()
                .filter(staff -> {
                    String roleIds = staff.getRoleIds();
                    if (StringUtils.isEmpty(roleIds)) {
                        return false;
                    }
                    // 是否有任意一个 roleId 匹配
                    return Arrays.stream(roleIds.split(","))
                            .map(String::trim)
                            .anyMatch(roleIdStr -> {
                                try {
                                    return targetRoleIds.contains(Long.parseLong(roleIdStr));
                                } catch (NumberFormatException e) {
                                    return false;
                                }
                            });
                })
                .distinct()
                .collect(Collectors.toList());

        return ApiResult.ok(filteredStaffList);
    }

    @Override
    public StaffDto selectStaffByUserIdAndBrandId(Long userId, Long brandId) {
        return staffMapper.findStaffByUserIdAndBrandId(userId, brandId);
    }


    private void validateStaffExists(Long id) {
        if (staffMapper.selectStaffById(id) == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
    }

    public static void populateUsers(List<BrandDeptStaff> staffList, Map<Long, List<SimpleDeptUserInfoDto>> deptUserMap) {
        staffList.forEach(staff -> {
            populateUser(staff, deptUserMap);
        });
    }

    public static void populateUser(BrandDeptStaff root, Map<Long, List<SimpleDeptUserInfoDto>> deptUserMap) {
        Optional.ofNullable(root.getDeptId())
                .map(Long::valueOf)
                .map(deptUserMap::get)
                .ifPresentOrElse(
                        root::setUsers,
                        () -> root.setUsers(new ArrayList<>())
                );

        Optional.ofNullable(root.getDepts())
                .ifPresent(depts -> depts.forEach(dept -> populateUser(dept, deptUserMap)));
    }


    public static List<BrandDeptStaff> convertToDeptStaff(List<XgwcBrandDeptDto> xgwcBrandDeptDtos) {
        // Find root departments (where pid is null or 0, depending on your data structure)
        List<XgwcBrandDeptDto> rootDepts = xgwcBrandDeptDtos.stream()
                .filter(dept -> dept.getPid() == null || dept.getPid() == 0)
                .collect(Collectors.toList());
        return buildDeptTree(rootDepts, xgwcBrandDeptDtos);
    }

    private static List<BrandDeptStaff> buildDeptTree(List<XgwcBrandDeptDto> currentLevelDepts, List<XgwcBrandDeptDto> allDepts) {
        return currentLevelDepts.stream()
                .map(deptVo -> {
                    BrandDeptStaff deptStaff = new BrandDeptStaff();
                    deptStaff.setDeptId(deptVo.getDeptId().intValue()); // Convert Long to Integer
                    deptStaff.setDeptName(deptVo.getDeptName());
                    // Find children departments
                    List<XgwcBrandDeptDto> children = allDepts.stream()
                            .filter(d -> deptVo.getDeptId().equals(d.getPid()))
                            .collect(Collectors.toList());

                    if (!children.isEmpty()) {
                        deptStaff.setDepts(buildDeptTree(children, allDepts));
                    }
                    // Initialize empty users list (you can populate this separately)
                    deptStaff.setUsers(new ArrayList<>());
                    return deptStaff;
                })
                .collect(Collectors.toList());
    }
}
