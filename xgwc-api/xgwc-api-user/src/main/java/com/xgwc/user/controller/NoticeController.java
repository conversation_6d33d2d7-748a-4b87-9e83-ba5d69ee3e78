package com.xgwc.user.controller;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.SourceEnums;
import com.xgwc.user.config.TaskKeyConfig;
import com.xgwc.user.entity.dto.notice.NoticeDto;
import com.xgwc.user.entity.dto.notice.SelfNoticeDto;
import com.xgwc.user.entity.vo.NoticeVo;
import com.xgwc.user.entity.vo.SelectNoticeVo;
import com.xgwc.user.service.NoticeService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequestMapping("notice")
@RestController
public class NoticeController extends BaseController {

    @Resource
    private NoticeService noticeService;

    @Resource
    private TaskKeyConfig taskKeyConfig;

    /**
     * 加盟商添加通知
     */
    @RequestMapping("/franchise/add")
    @PreAuthorize("@ss.hasPermission('notice:franchise:add')")
    public ApiResult addFranchiseNotice(@RequestBody @Valid NoticeVo noticeVo) {
        noticeVo.setSource(SourceEnums.FRANCHISE.getValue());
        int result = noticeService.insertNotice(noticeVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 加盟商添加通知
     */
    @RequestMapping("/brand/add")
    @PreAuthorize("@ss.hasPermission('notice:brand:add')")
    public ApiResult addBrandNotice(@RequestBody @Valid NoticeVo noticeVo) {
        noticeVo.setSource(SourceEnums.BRAND.getValue());
        int result = noticeService.insertNotice(noticeVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 加盟商修改通知
     */
    @RequestMapping("/franchise/update")
    @PreAuthorize("@ss.hasPermission('notice:franchise:update')")
    public ApiResult updateFranchiseNotice(@RequestBody @Valid NoticeVo noticeVo) {
        noticeVo.setSource(SourceEnums.FRANCHISE.getValue());
        if(noticeVo.getId() == null){
            return ApiResult.error("通知id不能为空");
        }
        int result = noticeService.updateNotice(noticeVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 品牌商修改通知
     */
    @RequestMapping("/brand/update")
    @PreAuthorize("@ss.hasPermission('notice:brand:update')")
    public ApiResult updateBrandNotice(@RequestBody @Valid NoticeVo noticeVo) {
        noticeVo.setSource(SourceEnums.BRAND.getValue());
        if(noticeVo.getId() == null){
            return ApiResult.error("通知id不能为空");
        }
        int result = noticeService.updateNotice(noticeVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 加盟商删除通知
     */
    @RequestMapping("/franchise/delete")
    @PreAuthorize("@ss.hasPermission('notice:franchise:delete')")
    public ApiResult deleteFranchiseNotice(@RequestBody NoticeVo noticeVo){
        noticeVo.setSource(SourceEnums.FRANCHISE.getValue());
        int result = noticeService.deleteNotice(noticeVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 品牌商删除通知
     */
    @RequestMapping("/brand/delete")
    @PreAuthorize("@ss.hasPermission('notice:brand:delete')")
    public ApiResult deleteBrandNotice(@RequestBody NoticeVo noticeVo){
        noticeVo.setSource(SourceEnums.BRAND.getValue());
        int result = noticeService.deleteNotice(noticeVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 加盟商通知列表
     */
    @RequestMapping("/franchise/list")
    @PreAuthorize("@ss.hasPermission('notice:franchise:list')")
    public ApiResult listFranchiseNotice(SelectNoticeVo selectNoticeVo){
        startPage();
        selectNoticeVo.setSource(SourceEnums.FRANCHISE.getValue());
        List<NoticeDto> franchiseNoticeDtos = noticeService.getNoticeList(selectNoticeVo);
        return getDataTable(franchiseNoticeDtos);
    }

    /**
     * 品牌商通知列表
     */
    @RequestMapping("/brand/list")
    @PreAuthorize("@ss.hasPermission('notice:brand:list')")
    public ApiResult listBrandNotice(SelectNoticeVo selectNoticeVo){
        startPage();
        selectNoticeVo.setSource(SourceEnums.BRAND.getValue());
        List<NoticeDto> franchiseNoticeDtos = noticeService.getNoticeList(selectNoticeVo);
        return getDataTable(franchiseNoticeDtos);
    }

    /**
     * 加盟商通知详情
     */
    @RequestMapping("/franchise/detail")
    @PreAuthorize("@ss.hasPermission('notice:franchise:detail')")
    public ApiResult franchiseNoticeDetail(NoticeVo noticeVo){
        if(noticeVo.getId() == null){
            return ApiResult.error("通知ID不能为空");
        }
        noticeVo.setSource(SourceEnums.FRANCHISE.getValue());
        return ApiResult.ok(noticeService.getNoticeDetail(noticeVo));
    }

    /**
     * 品牌商通知详情
     */
    @RequestMapping("/brand/detail")
    @PreAuthorize("@ss.hasPermission('notice:brand:detail')")
    public ApiResult brandNoticeDetail(NoticeVo noticeVo){
        if(noticeVo.getId() == null){
            return ApiResult.error("通知ID不能为空");
        }
        noticeVo.setSource(SourceEnums.BRAND.getValue());
        return ApiResult.ok(noticeService.getNoticeDetail(noticeVo));
    }

    /**
     * 查看个人的消息
     */
    @RequestMapping("/self/detail")
    public ApiResult selfDetail(NoticeVo noticeVo){
        if(noticeVo.getId() == null){
            return ApiResult.error("通知ID不能为空");
        }
        return ApiResult.ok(noticeService.getSelfNoticeDetail(noticeVo));
    }

    /**
     * 获取品牌商-全部通知-通知分类分组
     */
    @RequestMapping("brand/get_classifycount_list")
    public ApiResult getClassifyList(){
        return ApiResult.ok(noticeService.getBrandNoticeClassifyCountDtoByUserId());
    }

    /**
     * 设计师-全部通知-品牌分组
     */
    @RequestMapping("designer/get_brandgroup_list")
    public ApiResult getBrandGroupListByDesignerId(){
        return ApiResult.ok(noticeService.getBrandGroupByUserId());
    }

    /**
     * 加盟商-全部通知-品牌分组
     */
    @RequestMapping("franchise/get_brandgroup_list")
    public ApiResult getBrandGroupListByFranchiseId(){
        return ApiResult.ok(noticeService.getBrandGroupByFranchiseId());
    }

    /**
     * 品牌商-个人自己收到的通知列表
     */
    @RequestMapping("brand/get_self_list")
    public ApiResult getBrandSelfNotice(String classifyId){
        startPage();
        List<SelfNoticeDto> franchiseNoticeDtos = noticeService.getBrandSelfNoticeList(classifyId);
        return getDataTable(franchiseNoticeDtos);
    }

    /**
     * 设计师-个人自己收到的通知列表
     */
    @RequestMapping("designer/get_self_list")
    public ApiResult getDesignerSelfNotice(Long brandId, String classifyId){
        startPage();
        JSONObject jsonObject = noticeService.getDesignerSelfNotice(brandId, classifyId);
        return ApiResult.ok(jsonObject);
    }

    /**
     * 加盟商-个人自己收到的通知列表
     */
    @RequestMapping("franchise/get_self_list")
    public ApiResult getFranchiseSelfNotice(Long brandId, String classifyId){
        startPage();
       JSONObject jsonObject = noticeService.getFranchiseSelfNotice(brandId, classifyId);
        return ApiResult.ok(jsonObject);
    }

    /**
     * 供定时任务调用
     */
    @RequestMapping("send_notice")
    public ApiResult sendNotice(@RequestParam(name = "noticeId") Integer noticeId,@RequestParam(name = "key") String key){
        log.info("定时任务调用发通知接口,参数:{}, {}", noticeId, key);
        if(noticeId != null && key.equals(taskKeyConfig.getKey())){
            log.info("定时任务调用发通知接口验证通过开始发通知");
            noticeService.sendScheduleNotice(noticeId);
        }else{
            log.info("定时任务调用发通知接口验证失败");
        }
        return ApiResult.ok();
    }

}
