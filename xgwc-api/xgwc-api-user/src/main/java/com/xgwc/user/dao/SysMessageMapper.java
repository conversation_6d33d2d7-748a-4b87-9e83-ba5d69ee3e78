package com.xgwc.user.dao;

import com.xgwc.user.entity.MessageTypeKey;
import com.xgwc.user.entity.SysMessage;

import java.util.List;

public interface SysMessageMapper {

    /**
     * 创建消息
     * @param sysMessage 消息参数， 其中type和typeName分别取值为MessageTypeEnums
     * @return 是否成功
     */
    int insertMessage(SysMessage sysMessage);

    /**
     * 创建消息
     * @param sysMessage 消息参数， 其中type和typeName分别取值为MessageTypeEnums
     * @return 是否成功
     */
    int batchInsertMessages(List<SysMessage> sysMessages);

    /**
     * 修改消息状态
     * @return 更新读取状态
     */
    int updateReadStatus(Long id);

    /**
     * 根据ID获取消息
     * @param id 消息ID
     * @return 系统消息
     */
    SysMessage selectById(Long id);

    /**
     * 获取消息列表
     * @return 消息列表
     */
    List<SysMessage> getSysMessageList(SysMessage sysMessage);

    /**
     * 消息type 分组
     */
    List<MessageTypeKey> getMessageTypeKeyGroup(Long userId);

}
