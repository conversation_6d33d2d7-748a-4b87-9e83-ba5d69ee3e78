package com.xgwc.user.dao;

import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.FranchiseOwner;
import com.xgwc.user.entity.dto.FranchiseOwnerQueryDto;
import com.xgwc.user.entity.dto.MyFranchiseOwnerQueryDto;
import com.xgwc.user.entity.vo.FranchiseOwnerSimpleVO;
import com.xgwc.user.entity.vo.FranchiseOwnerVO;
import com.xgwc.user.entity.vo.ApplyInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface FranchiseOwnerMapper {
    /**
     * 查询加盟商管理
     * 
     * @param id 加盟商管理主键
     * @return 加盟商管理
     */
    FranchiseOwnerVO selectFranchiseOwnerById(Long id);

    /**
     * 查询加盟商管理列表
     * 
     * @param brandOwner 加盟商管理
     * @return 加盟商管理集合
     */
    @TenantIgnore
    List<FranchiseOwnerVO> selectFranchiseOwnerList(FranchiseOwnerQueryDto brandOwner);

    /**
     * 新增加盟商管理
     * 
     * @param franchiseOwner 加盟商管理
     * @return 结果
     */
    int insertFranchiseOwner(FranchiseOwner franchiseOwner);

    /**
     * 修改加盟商管理
     * 
     * @param franchiseOwner 加盟商管理
     * @return 结果
     */
    int updateFranchiseOwner(FranchiseOwner franchiseOwner);

    /**
     * 删除加盟商管理
     * 
     * @param id 加盟商管理主键
     * @return 结果
     */
    int deleteFranchiseOwnerById(Long id);

    /**
     * 批量删除加盟商管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteFranchiseOwnerByIds(Long[] ids);

    /**
     * 根据管理员用户ID查询加盟商管理
     * @param managerUserId 管理员用户ID
     * @param brandId 品牌商ID
     * @return 加盟商管理
     */
    List<ApplyInfoVO> findFranchiseOwnerByManagerUserIdAndBrandIdList(@Param("managerUserId") Long managerUserId, @Param("brandId") Long brandId);

    FranchiseOwnerVO findFranchiseOwnerByManagerPhone(@Param("managerPhone") String managerPhone,@Param("brandId") Long brandId);

    /**
     * 根据管理员用户ID和品牌商ID查询加盟商管理
     * @param managerUserId 管理员用户ID
     * @param brandId 品牌商ID
     * @return 加盟商管理
     */
    FranchiseOwnerVO findFranchiseOwnerByManagerUserIdAndBrandId(@Param("managerUserId") Long managerUserId,@Param("brandId") Long brandId);

    /**
     * 根据管理员用户ID查询申请列表
     * @param managerUserId 管理员用户ID
     * @return 加盟商申请列表
     */
    List<FranchiseOwnerVO> findMyApplyList(Long managerUserId);

    /**
     * 根据加盟商ID查询申请列表
     * @param queryDto 加盟商ID
     */
    List<FranchiseOwnerVO> selectMyFranchiseOwnerApplyList(MyFranchiseOwnerQueryDto queryDto);

    /**
     * 根据用户id获取加盟商管理员信息
     * @param userId 用户id
     * @return 加盟商管理员信息
     */
    List<FranchiseOwnerVO> getFranchiseOwnerVOByUserId(Long userId);

    /**
     * 根据品牌商ID查询加盟商列表
     * @param brandId 品牌商ID
     * @return 加盟商列表
     */
    List<FranchiseOwnerSimpleVO> findFranchiseOwnerListByBrandId(Long brandId);

    /**
     * 查询加盟商下载限制
     *
     * @param franchiseOwnerQueryDto 查询参数
     * @return 加盟商列表
     */
    List<FranchiseOwnerVO> selectFranchiseDownloadLimit(@Param("franchiseOwnerQueryDto") FranchiseOwnerQueryDto franchiseOwnerQueryDto);

    /**
     * 根据加盟商ID和品牌商ID查询加盟商信息
     * @param franchiseId 加盟商ID
     * @param brandId 品牌商ID
     */
    FranchiseOwnerVO findFranchiseOwnerByFranchiseIdAndBrandId(@Param("franchiseId") Long franchiseId, @Param("brandId") Long brandId);

    /**
     * 根据加盟商ID查询加盟商信息
     * @param franchiseId 加盟商ID
     */
    List<FranchiseOwnerVO> findFranchiseOwnerByFranchiseId(Long franchiseId);

    /**
     * 根据加盟商ID查询加盟商下载限制
     * @param franchiseId 加盟商ID
     */
    List<FranchiseOwnerVO> selectFranchiseInfoByFranchiseId(@Param("franchiseId") Long franchiseId);

    /**
     * 根据品牌商ID查询品牌商名称和加盟商名称
     * @param brandId 品牌ID
     * @return 品牌商名称-加盟商简称
     */
    List<FranchiseOwnerVO> selectBrandNameAndFranchiseName(@Param("brandId") Long brandId);

    List<FranchiseOwnerSimpleVO> findCooperateFranchiseByServiceId(Long serviceId);
}
