package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysDictDataVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("样式属性（其他样式扩展）")
    private String cssClass;

    @FieldDesc("字典编码")
    private Long dictCode;

    @FieldDesc("字典标签")
    private String dictLabel;

    @FieldDesc("字典排序")
    private Long dictSort;

    @FieldDesc("字典类型")
    private String dictType;

    @FieldDesc("字典键值")
    private String dictValue;

    @FieldDesc("是否默认（Y是 N否）")
    private String isDefault;

    @FieldDesc("表格回显样式")
    private String listClass;

    @FieldDesc("上级字典编码")
    private Long parentDictCode;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("品牌商id")
    private Long brandId;


}
