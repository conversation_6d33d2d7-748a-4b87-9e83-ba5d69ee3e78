package com.xgwc.user.entity.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SalesEfficiencyVo {


    /** 总成交金额 */
    private BigDecimal orderAmount;
    private BigDecimal orderAmountZc;

    private Long orderNum;
    private Long orderNumYx;
    private Long orderNumZc;


    private BigDecimal zhMoney;
    private BigDecimal zhRate;
    private BigDecimal zcRate;
    private BigDecimal yxRate;

    /** 进线人数 */
    private Long lineNum;

    /** 新客成交金额 */
    private BigDecimal xkAmount;
    private BigDecimal xkAmountYx;
    private BigDecimal xkAmountZc;
    private BigDecimal xkRate;
    private BigDecimal xkRateYx;
    private BigDecimal xkRateZc;
    /** 新客客单价 */
    private BigDecimal xkPrice;
    private BigDecimal xkPriceZc;
    /** 客户价值 */
    private BigDecimal xkValue;
    private BigDecimal xkValueZc;

    /** 新客成交笔数 */
    private Long xkNum;
    private Long xkNumYx;
    private Long xkNumZc;

    /** 回购笔数 */
    private Long hgNum;
    private Long hgNumYx;
    private Long hgNumZc;

    /** 回购金额 */
    private BigDecimal hgAmount;
    private BigDecimal hgAmountYx;
    private BigDecimal hgAmountZc;  /** 回购率 */
    private BigDecimal hgRate;


    private Long zjsNum;
    private Long zjsNumYx;
    private Long zjsNumZc;

    private BigDecimal zjsAmount;
    private BigDecimal zjsAmountYx;
    private BigDecimal zjsAmountZc;



    /** 有效总金额 */
    private BigDecimal yxAmount;
    private BigDecimal zcAmount;

    /** 佣金金额 */
    private BigDecimal money;
    private BigDecimal moneyYx;
    private BigDecimal moneyZc;

    /** 佣金比例 */
    private BigDecimal yjRate;
    private BigDecimal yjRateYx;
    private BigDecimal yjRateZc;

    /** 退款笔数 */
    private Long tkNum;
    private Long tkNumZc;

    /** 退款金额 */
    private BigDecimal tkAmount;
    private BigDecimal tkAmountZc;


    /** 退款率 */
    private BigDecimal tkRate;
    private BigDecimal tkRateZc;


    /** 客服ID */
    private Long userId;
    /** 客服 */
    private String userName;

    private Long franchiseId;

    private String franchiseName;

    private Long deptId;
    private String deptName;

}
