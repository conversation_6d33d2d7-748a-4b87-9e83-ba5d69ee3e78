package com.xgwc.user.dao;

import com.xgwc.user.entity.SysMenu;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.vo.SysMenuQueryVo;
import com.xgwc.user.entity.vo.SysMenuVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


public interface SysMenuMapper  {
    /**
     * 查询字典数据
     * 
     * @param id 字典数据主键
     * @return 字典数据
     */
    public SysMenuDto selectSysMenuById(Long id);

    /**
     * 查询字典数据列表
     * 
     * @param sysMenu 字典数据
     * @return 字典数据集合
     */
    public List<SysMenuDto> selectSysMenuList(SysMenuQueryVo sysMenu);

    /**
     * 新增字典数据
     * 
     * @param sysMenu 字典数据
     * @return 结果
     */
    public int insertSysMenu(SysMenu sysMenu);

    /**
     * 修改字典数据
     * 
     * @param sysMenu 字典数据
     * @return 结果
     */
    public int updateSysMenu(SysMenu sysMenu);

    /**
     * 删除字典数据
     * 
     * @param id 字典数据主键
     * @return 结果
     */
    public int deleteSysMenuById(Long id);

    /**
     * 批量删除字典数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysMenuByIds(Long[] ids);


    /**
     * 查询菜单树结构信息
     * @return 结果
     */
    List<SysMenuVo> getMenuTreeData(@Param("isFlag") String isFlag);

    List<SysMenuDto> selectSysMenuByModelType(String modelType);

    /**
     * 根据菜单类型查询菜单树结构信息
     *
     * @return 菜单树结构信息
     */

    Set<String> selectMenuTreeByModelType(@Param("modelType") String modelType);

    /**
     * 根据用户ID查询品牌商权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectSysMenuBrandByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询加盟商权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectSysMenuFranchiseByUserId(@Param("userId") Long userId);

    /**
     * 查询菜单树
     *
     * @param isFlag 菜单标识
     * @return 菜单树
     */
    List<SysMenuVo> getMenuTree(@Param("isFlag") String isFlag);

    /**
     * 根据用户ID查询服务权限
     *
     * @param userId 用户ID
     * @return 菜单树结构信息
     */
    Set<String> selectSysMenuServiceByUserId(Long userId);

    /**
     * 根据用户ID查询销售服务商权限
     *
     * @param userId 用户ID
     * @return 菜单树结构信息
     */
    Set<String> selectSysMenuMarketByUserId(Long userId);
}
