package com.xgwc.user.service.impl;

import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.SysMessageMapper;
import com.xgwc.user.entity.MessageTypeKey;
import com.xgwc.user.entity.SysMessage;
import com.xgwc.user.service.SysMessageService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysMessageServiceImpl implements SysMessageService {

    @Resource
    private SysMessageMapper sysMessageMapper;

    @Override
    public int insertSysMessage(SysMessage sysMessage) {
        if(StringUtils.isEmpty(sysMessage.getTypeKey()) || StringUtils.isEmpty(sysMessage.getMessage())){
            return 0;
        }
        return sysMessageMapper.insertMessage(sysMessage);
    }

    @Override
    public int batchInsertSysMessage(List<SysMessage> sysMessages) {
        return sysMessageMapper.batchInsertMessages(sysMessages);
    }

    @Override
    public SysMessage getMessageDetail(Long id) {
        if(id != null){
            //更新为已读状态
            sysMessageMapper.updateReadStatus(id);
            return sysMessageMapper.selectById(id);
        }
        return null;
    }

    @Override
    public List<SysMessage> getMessageList(SysMessage sysMessage) {
        Long userId = SecurityUtils.getUserId();
        sysMessage.setUserId(userId);
        return sysMessageMapper.getSysMessageList(sysMessage);
    }

    @Override
    public List<MessageTypeKey> getMessageTypeKeyGroup() {
        Long userId = SecurityUtils.getUserId();
        return sysMessageMapper.getMessageTypeKeyGroup(userId);
    }

}
