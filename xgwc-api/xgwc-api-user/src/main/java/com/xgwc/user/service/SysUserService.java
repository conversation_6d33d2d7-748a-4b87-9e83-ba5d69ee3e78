package com.xgwc.user.service;

import com.xgwc.common.entity.SysUser;
import com.xgwc.user.entity.dto.LoginResult;
import com.xgwc.user.entity.dto.SysUserMiddleDto;
import com.xgwc.user.entity.dto.SysUserRegisterDTO;
import com.xgwc.user.entity.dto.UserInfoResult;
import com.xgwc.user.entity.vo.*;

import java.util.List;

public interface SysUserService {

    /**
     * login
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录信息
     */
    LoginResult login(String username, String password);

    /**
     *
     * @param loginVo
     * @return
     */
    LoginResult loginPhone(LoginVo loginVo);

    /**
     * get user info
     *
     * @return
     */
    UserInfoResult getInfo();

    /**
     * 短信登录
     * @param reqVO 登录信息
     * @return 登录结果
     */
    LoginResult smsLogin(SmsLoginVO reqVO);

    /**
     * 用户注册
     * @param createReqVO 注册用户
     */
    Long addSysUser(SysUserRegisterDTO createReqVO);

    /**
     * 用户注册
     * @param createReqVO 注册用户
     */
    Long addSysUser2(SysUserRegisterDTO createReqVO);

    /**
     * 根据中间用户ID，修改来源ID
     * @param userId sysUserMiddle 的主键
     * @param sourceId 来源ID
     */
    void updateSysUserMiddleSourceId(Long userId, Long sourceId);

    /**
     * 品牌商注册
     * @param reqVO 品牌商注册信息
     * @return 登录结果
     */
    //LoginResult brandRegister(BrandRegisterVo reqVO);

    /**
     * 员工注册
     * @param reqVO 员工注册信息
     * @return 用户id
     */
    LoginResult StaffRegister(StaffRegisterVo reqVO);

    /**
     * 绑定员工
     * @param reqVO 员工注册信息
     * @return 登录结果
     */
    LoginResult bindStaff(StaffBindVo reqVO);

    /**
     * 初始化角色
     * @param userId 用户id
     * @param roleCode 角色编码
     * @param roleName 角色名称
     * @param businessId 业务id （品牌商id或加盟商id）
     * @param businessType 业务类型 （1：品牌商，2：加盟商）
     */
    public Long initRole(Long userId, String roleCode, String roleName,Long businessId,Integer businessType);

    /**
     * 创建token
     * @param userId 用户ID
     * @param mainUserId 主用户ID
     * @return token
     */
    public String createToken(Long userId, Long mainUserId);

    /**
     * 添加用户中间表
     * @param sysUser 用户信息
     * @param sourceId 来源ID
     * @return 用户ID
     */
    Long insertSysUserMiddle(SysUser sysUser, Long sourceId);

    /**
     * 注册
     * @param reqVO 注册信息
     * @return 注册结果
     */
    int register(RegisterVo reqVO);

     List<SysUserMiddleDto> check(LoginVo loginVo);
}
