package com.xgwc.user.dao;

import java.util.List;

import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.user.entity.SysTenantPackage;
import com.xgwc.user.entity.vo.SysTenantPackageVo;
import com.xgwc.user.entity.dto.SysTenantPackageDto;
import com.xgwc.user.entity.vo.SysTenantPackageQueryVo;


public interface SysTenantPackageMapper 
{
    /**
     * 查询租户套餐管理
     * 
     * @param id 租户套餐管理主键
     * @return 租户套餐管理
     */
    public SysTenantPackageDto selectSysTenantPackageById(Long id);

    @TenantIgnore
    public SysTenantPackageDto selectSysTenantPackageByTenantId(Long Tenantid);
    /**
     * 查询租户套餐管理列表
     * 
     * @param sysTenantPackage 租户套餐管理
     * @return 租户套餐管理集合
     */
    public List<SysTenantPackageDto> selectSysTenantPackageList(SysTenantPackageQueryVo sysTenantPackage);

    /**
     * 新增租户套餐管理
     * 
     * @param sysTenantPackage 租户套餐管理
     * @return 结果
     */
    public int insertSysTenantPackage(SysTenantPackage sysTenantPackage);

    /**
     * 修改租户套餐管理
     * 
     * @param sysTenantPackage 租户套餐管理
     * @return 结果
     */
    public int updateSysTenantPackage(SysTenantPackage sysTenantPackage);

    /**
     * 删除租户套餐管理
     * 
     * @param id 租户套餐管理主键
     * @return 结果
     */
    public int deleteSysTenantPackageById(Long id);

    /**
     * 批量删除租户套餐管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysTenantPackageByIds(Long[] ids);
}
