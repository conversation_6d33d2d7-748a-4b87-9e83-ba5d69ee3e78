package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.TenantIgnore;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.BrandDeptStaff;
import com.xgwc.user.entity.dto.StaffDto;
import com.xgwc.user.entity.dto.StaffLogDto;
import com.xgwc.user.entity.vo.StaffQueryVo;
import com.xgwc.user.entity.vo.StaffVo;
import com.xgwc.user.service.IStaffLogService;
import com.xgwc.user.service.IStaffService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 员工管理Controller
 */
@RestController
@RequestMapping("staff")
@Slf4j
public class StaffController extends BaseController {
    @Resource
    private IStaffService staffService;

    @Resource
    private IStaffLogService staffLogService;

    /**
     * 查询员工管理列表
     */
    @MethodDesc("查询员工管理列表")
    @PreAuthorize("@ss.hasPermission('Staff:Staff:list')")
    @GetMapping("/list")
    public ApiResult<StaffDto> list(StaffQueryVo staff) {
        startPage();
        List<StaffDto> list = staffService.selectStaffList(staff);
        return getDataTable(list);
    }

    /**
     * 获取员工管理下拉框
     *
     * @return 员工管理下拉框 name + "/" + stageName + "/" + companyName
     */
    @TenantIgnore
    @MethodDesc("获取员工管理下拉框")
    @GetMapping("/getStaffDropDown")
    public ApiResult getStaffDropDown() {
        try {
            List<Map<String, Object>> result = staffService.selectStaffListDropDown();
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取员工管理下拉框失败", e);
            return ApiResult.error("获取员工管理下拉框失败");
        }
    }


    /**
     * 导出员工管理列表
     */
  //  @MethodDesc("导出员工管理列表")
    @PreAuthorize("@ss.hasPermission('Staff:Staff:export')")
    @Log(title = "员工管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StaffQueryVo staff) {
    }

    /**
     * 获取员工管理详细信息
     */
    @MethodDesc("获取员工管理详细信息")
//    @PreAuthorize("@ss.hasPermission('Staff:Staff:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<StaffDto> getInfo(@PathVariable("id") Long id) {
        return success(staffService.selectStaffById(id));
    }

    /**
     * 新增员工管理
     */
    @MethodDesc("新增员工管理")
    @PreAuthorize("@ss.hasPermission('Staff:Staff:add')")
    @Log(title = "员工管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody StaffVo staff) {
        return toAjax(staffService.insertStaff(staff));
    }

    /**
     * 修改员工管理
     */
    @MethodDesc("修改员工管理")
    @PreAuthorize("@ss.hasPermission('Staff:Staff:edit')")
    @Log(title = "员工管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody StaffVo staff) {
        return toAjax(staffService.updateStaff(staff));
    }

    /**
     * 删除员工管理
     */
    @MethodDesc("删除员工管理")
    @PreAuthorize("@ss.hasPermission('Staff:Staff:remove')")
    @Log(title = "员工管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public ApiResult remove(@PathVariable Long id) {
        return toAjax(staffService.deleteStaffById(id));
    }

    /**
     * 获取加盟商员工数
     * @return
     */
    @RequestMapping("getDeptStaffTree")
    public ApiResult getDeptStaffTree(){
        List<BrandDeptStaff> brandDeptStaffs = staffService.getDeptStaffTree();
        return getDataTable(brandDeptStaffs);
    }

    @MethodDesc("获取品牌商员工下拉框")
    @GetMapping(value = "/getBrandStaffDropDown")
    public ApiResult getBrandStaffDropDown(@RequestParam(value = "deptId", required = false) Long deptId){
       return ApiResult.ok(staffService.getBrandStaffDropDown(deptId));
    }

    @MethodDesc("根据员工id查询痕迹")
//    @PreAuthorize("@ss.hasPermission('Staff:Staff:trail')")
    @GetMapping(value = "/getStaffLog/{id}")
    public ApiResult getStaffLog(@PathVariable("id") Long id){
        startPage();
        List<StaffLogDto> list = staffLogService.findStaffLogByStaffIdAndBusinessType(id, 1);
        return getDataTable(list);
    }

    /**
     * 根据userId查询员工可下载次数
     *
     * @param userId 当前用户id
     * @return 下载次数
     */
    @MethodDesc("根据userId查询员工可下载次数")
    @GetMapping(value = "/getStaffByUserId")
    public ApiResult getStaffDownloadCountByUserId(@RequestParam("userId") Long userId,
                                                   @RequestParam("isFlag") Integer isFlag,
                                                   @RequestParam(value = "brandId", required = false) Long brandId){
        return ApiResult.ok(staffService.getStaffDownloadCountByUserId(userId, isFlag, brandId));
    }

    /**
     * 根据派单管理-派单工作台菜单 查询有该菜单权限的员工信息
     *
     * @return 员工信息
     */
    @MethodDesc("根据派单管理-派单工作台菜单 查询有该菜单权限的员工信息")
    @GetMapping("/getStaffInfoByMenu")
    public ApiResult getStaffInfoByMenu() {
        return staffService.getStaffInfoByMenu();
    }

    /**
     * 根据userId和品牌商id查询员工信息
     * @param userId 用户id
     * @param brandId 品牌商id
     * @return 员工信息
     */
    @GetMapping(value = "/getStaffInfoByUserIdAndBrandId")
    public ApiResult<StaffDto> selectStaffByUserIdAndBrandId(@RequestParam("userId") Long userId,
                                                   @RequestParam("brandId") Long brandId){
        return success(staffService.selectStaffByUserIdAndBrandId(userId, brandId));
    }

}
