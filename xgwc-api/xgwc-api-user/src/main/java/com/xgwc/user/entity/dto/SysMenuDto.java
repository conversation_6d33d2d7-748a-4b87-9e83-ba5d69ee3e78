package com.xgwc.user.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.util.TreeUtils;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

@Data
public class SysMenuDto implements TreeUtils.TreeNode<Long> {

    private static final long serialVersionUID=1L;

    @FieldDesc("激活图标")
    @Excel(name = "激活图标")
    private String activeIcon;

    @FieldDesc("激活路径")
    @Excel(name = "激活路径")
    private String activePath;

    @FieldDesc("固定在标签")
    @Excel(name = "固定在标签")
    private Long affixTab;

    @FieldDesc("权限名称：英文")
    @Excel(name = "权限名称：英文")
    private String authCode;

    @FieldDesc("页面组件")
    @Excel(name = "页面组件")
    private String component;

    @FieldDesc("创建用户id")
    @Excel(name = "创建用户id")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("在面包屑里面隐藏菜单")
    @Excel(name = "在面包屑里面隐藏菜单")
    private Long hideInBreadcrumb;

    @FieldDesc("隐藏菜单")
    @Excel(name = "隐藏菜单")
    private Long hideInMenu;

    @FieldDesc("在标签栏中隐藏")
    @Excel(name = "在标签栏中隐藏")
    private Long hideInTab;

    @FieldDesc("隐藏子菜单")
    @Excel(name = "隐藏子菜单")
    private Long hidechildrenInMenu;

    @FieldDesc("菜单图片")
    @Excel(name = "菜单图片")
    private String icon;

    @FieldDesc("菜单id")
    @Excel(name = "菜单id")
    private Long id;

    @FieldDesc("是否删除")
    @Excel(name = "是否删除")
    private Integer isDel;

    @FieldDesc("是否缓存页面标签")
    @Excel(name = "是否缓存页面标签")
    private Long keepAlive;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    @FieldDesc("菜单名称")
    @Excel(name = "菜单名称")
    private String name;

    @FieldDesc("路由地址")
    @Excel(name = "路由地址")
    private String path;

    @FieldDesc("父类id")
    @Excel(name = "父类id")
    private Long pid;

    @FieldDesc("备注")
    @Excel(name = "备注")
    private String remark;

    @FieldDesc("菜单状态（0正常 1停用）")
    @Excel(name = "菜单状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("标题")
    @Excel(name = "标题")
    private String title;

    @FieldDesc("菜单类型")
    @Excel(name = "菜单类型")
    private String type;

    @FieldDesc("修改人id")
    @Excel(name = "修改人id")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("排序")
    @Excel(name = "排序")
    private Integer sort;

    @FieldDesc("所属分类")
    private String modelType;

    private List<SysMenuDto> children;



    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("activeIcon",getActiveIcon())
            .append("activePath",getActivePath())
            .append("affixTab",getAffixTab())
            .append("authCode",getAuthCode())
            .append("component",getComponent())
            .append("createBy",getCreateBy())
            .append("createTime",getCreateTime())
            .append("hideInBreadcrumb",getHideInBreadcrumb())
            .append("hideInMenu",getHideInMenu())
            .append("hideInTab",getHideInTab())
            .append("hidechildrenInMenu",getHidechildrenInMenu())
            .append("icon",getIcon())
            .append("id",getId())
            .append("isDel",getIsDel())
            .append("keepAlive",getKeepAlive())
            .append("modifyTime",getModifyTime())
            .append("name",getName())
            .append("path",getPath())
            .append("pid",getPid())
            .append("remark",getRemark())
            .append("status",getStatus())
            .append("title",getTitle())
            .append("type",getType())
            .append("updateBy",getUpdateBy())
            .append("updateTime",getUpdateTime())
        .toString();
        }

    @Override
    public void setChildren(List<? extends TreeUtils.TreeNode<Long>> children) {
        this.children = (List<SysMenuDto>) children;
    }
}
