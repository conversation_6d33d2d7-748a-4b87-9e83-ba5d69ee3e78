package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class RegisterVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("管理员手机号")
    private String managerPhone;

    @FieldDesc("验证码")
    private String code;

    @FieldDesc("密码")
    private String password;

    @FieldDesc("确认密码")
    private String confirmPassword;

}
