package com.xgwc.user.config;

import com.aliyun.ocr_api20210707.Client;
import com.aliyun.teaopenapi.models.Config;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "aliyun.ocr")
@RefreshScope
public class AliyunOcrConfig {

    /**
     * 地域节点
     */
    private String endpoint;
    /**
     * AccessKey
     */
    private String accessKeyId;
    /**
     * AccessKey秘钥
     */
    private String accessKeySecret;

    /**
     * 是否开启
     */
    private boolean open;

    @Bean
    public Client createOcrClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = endpoint;
        return new Client(config);
    }

}
