package com.xgwc.user.entity.vo;

import lombok.Data;

@Data
public class PrivacyNumberVo {

    /**
     * 小号号码
     */
    private String middleNumber;

    /**
     * 绑定小号A
     */
    private String bindNumberA;

    /**
     * 绑定小号B
     */
    private String bindNumberB;

    /**
     * 绑定关系时长，单位为秒，为空则为永久
     */
    private int maxBindingTime;

    /**
     * 过期时间
     */
    private String expireTime;

    /**
     * 呼叫结果推送地址
     */
    private String callbackUrl;

    /**
     * 绑定状态：0已绑定，1解绑
     */
    private int bindStatus;

    /**
     * 呼叫结果
     */
    private String callResult;

}
