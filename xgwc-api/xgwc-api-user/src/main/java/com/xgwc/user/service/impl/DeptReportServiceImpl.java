package com.xgwc.user.service.impl;

import com.xgwc.user.dao.DeptReportMapper;
import com.xgwc.user.entity.dto.SalesEfficiencyQueryDto;
import com.xgwc.user.entity.vo.SalesEfficiencyVo;
import com.xgwc.user.service.IDeptReportService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class DeptReportServiceImpl implements IDeptReportService {

    @Resource
    private DeptReportMapper deptReportMapper;

    @Override
    public List<SalesEfficiencyVo> SalesEfficiency(SalesEfficiencyQueryDto dto) {
        List<SalesEfficiencyVo> list = deptReportMapper.SalesEfficiency(dto);
        SalesEfficiencyVo vo = deptReportMapper.SalesEfficiencyTotal(dto);

        list.add(vo);
        return list;
    }
}
