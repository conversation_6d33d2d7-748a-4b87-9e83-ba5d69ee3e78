package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.user.entity.dto.FranchiseDesignerRecordDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DesignerSimpleVO {

    private static final long serialVersionUID=1L;

    /** 设计师ID */
    @Excel(name = "设计师ID")
    private Long designerId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 手机号：加密 */
    @Excel(name = "手机号：加密")
    private String phone;

    /** 擅长业务 */
    @Excel(name = "擅长业务")
    private Long goodBusiness;

    /** 申请状态 */
    @Excel(name = "申请状态")
    private Long checkStatus;

    /** 状态：0正常，1禁用 */
    @Excel(name = "状态：0正常，1禁用")
    private Integer status;

    /** 品牌商 */
    @Excel(name = "品牌商")
    private String brandName;

    /** 擅长业务 */
    private String businessName;

    private Long brandId;

}
