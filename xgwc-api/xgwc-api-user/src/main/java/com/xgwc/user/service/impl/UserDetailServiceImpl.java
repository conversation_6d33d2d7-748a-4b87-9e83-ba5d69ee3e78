package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.constants.CommonConstant;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.security.mnodel.SysUserDetails;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.redis.config.CacheEnabled;
import com.xgwc.redis.constants.Expire;
import com.xgwc.redis.constants.UserCacheKey;
import com.xgwc.user.dao.*;
import com.xgwc.user.entity.ServiceStaffDto;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.*;
import com.xgwc.user.security.enums.SysExceptionEnum;
import com.xgwc.user.service.FranchiseRoleService;
import com.xgwc.user.service.UserDetailService;
import com.xgwc.user.service.UserService;
import com.xgwc.user.service.XgwcBrandRoleService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class UserDetailServiceImpl implements UserDetailsService, UserDetailService {

//    @Resource
//    private UserMapper userMapper;

    @Resource
    @Lazy
    private UserService userService;
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private BrandOwnerMapper brandOwnerMapper;
    @Resource
    private FranchiseOwnerMapper franchiseOwnerMapper;
    @Resource
    private FranchiseStaffMapper franchiseStaffMapper;
    @Resource
    private DesignerMapper designerMapper;
    @Resource
    private SysUserMiddleMapper sysUserMiddleMapper;
    @Resource
    private FranchiseMapper franchiseMapper;
    @Resource
    private ServiceOwnerMapper serviceOwnerMapper;

    @Resource
    private ServiceStaffMapper serviceStaffMapper;

    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Resource
    private XgwcBrandRoleService xgwcBrandRoleService;
    @Resource
    private FranchiseRoleService franchiseRoleService;
    @Resource
    private MarketStaffMapper marketStaffMapper;


    @CacheEnabled(key = UserCacheKey.LOAD_USERBYUSERID, fileds = "userId", expire = Expire.FIVE_MINUTE)
    @Override
    public SysUserDetails loadUserByUsername(String userId) throws UsernameNotFoundException {
        // 获取登录用户信息
        SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleById(Long.parseLong(userId));
        SysUser sysUser = new SysUser();
        if(sysUserMiddleDto != null){
            sysUser.setUserId(sysUserMiddleDto.getUserId());
            sysUser.setMainUserId(sysUserMiddleDto.getMainUserId());
            sysUser.setUserType(sysUserMiddleDto.getUserType());
            //品牌商员工
            if(sysUserMiddleDto.getUserType() == 4){
                StaffDto staffDto = staffMapper.getStaffByUserId(sysUserMiddleDto.getUserId());
                if(staffDto != null){
                    sysUser.setDeptId(staffDto.getDeptId());
                    sysUser.setUserName(staffDto.getStageName());
                    sysUser.setBrandId(staffDto.getBrandId());
                    List<FranchiseOwnerSimpleVO> franchiseOwnerList = franchiseOwnerMapper.findFranchiseOwnerListByBrandId(staffDto.getBrandId());
                    if(!franchiseOwnerList.isEmpty()){
                        String ids = franchiseOwnerList.stream().map(FranchiseOwnerSimpleVO::getId).map(String::valueOf).collect(Collectors.joining(","));
                        sysUser.setFranchiseIds(ids);
                    }
                }
            }else if(sysUserMiddleDto.getUserType() == 5){
                //加盟商员工
                FranchiseStaffDto franchiseStaffDto = franchiseStaffMapper.selectFranchiseStaffByUserId(sysUserMiddleDto.getUserId());
                if(franchiseStaffDto != null){
                    sysUser.setDeptId(franchiseStaffDto.getDeptId());
                    sysUser.setUserName(franchiseStaffDto.getStageName());
                    sysUser.setFranchiseId(franchiseStaffDto.getFranchiseId());
                    List<FranchiseOwnerVO> franchiseOwnerList = franchiseOwnerMapper.findFranchiseOwnerByFranchiseId(franchiseStaffDto.getFranchiseId());
                    if(!franchiseOwnerList.isEmpty()){
                        String ids = franchiseOwnerList.stream().map(FranchiseOwnerVO::getBrandId).map(String::valueOf).collect(Collectors.joining(","));
                        sysUser.setBrandIds(ids);
                    }
                }
            }else if(sysUserMiddleDto.getUserType() == 1){
                //品牌商管理员
                BrandOwnerDto brandOwnerDto = brandOwnerMapper.selectBrandOwnerByUserId(sysUserMiddleDto.getUserId());
                if(brandOwnerDto != null){
                    sysUser.setUserName(brandOwnerDto.getContact());
                    sysUser.setBrandId(brandOwnerDto.getBrandId());
                    StaffDto staffDto = staffMapper.getStaffByUserId(sysUserMiddleDto.getUserId());
                    if(staffDto != null){
                        sysUser.setDeptId(staffDto.getDeptId());
                        sysUser.setUserName(staffDto.getStageName());
                    }
                    List<FranchiseOwnerSimpleVO> franchiseOwnerList = franchiseOwnerMapper.findFranchiseOwnerListByBrandId(brandOwnerDto.getBrandId());
                    if(!franchiseOwnerList.isEmpty()){
                        String ids = franchiseOwnerList.stream().map(FranchiseOwnerSimpleVO::getId).map(String::valueOf).collect(Collectors.joining(","));
                        sysUser.setFranchiseIds(ids);
                    }
                }
            } else if (sysUserMiddleDto.getUserType() == 2) {
                //加盟商管理员
                FranchiseDto franchiseDto = franchiseMapper.getFranchiseDtoByUserId(sysUserMiddleDto.getUserId());
                if (franchiseDto != null) {
                    sysUser.setUserName(franchiseDto.getStageName());
                    sysUser.setFranchiseId(franchiseDto.getId());
                    sysUser.setDeptId(franchiseDto.getDeptId());
                    List<FranchiseOwnerVO> myApplyList = franchiseOwnerMapper.findMyApplyList(sysUserMiddleDto.getUserId());
                    if(!myApplyList.isEmpty()){
                        String ids = myApplyList.stream().filter(f -> f.getCheckStatus() == 1)
                                .map(FranchiseOwnerVO::getBrandId).map(String::valueOf).collect(Collectors.joining(","));
                        sysUser.setBrandIds(ids);
                    }
                }
            } else if (sysUserMiddleDto.getUserType() == 3) {
                // 设计师
                List<DesignerVO> designerVOs = designerMapper.findDesignerByManagerUserId(sysUserMiddleDto.getUserId());
                if(designerVOs != null){
                    sysUser.setUserName(designerVOs.get(0).getName());
                    String ids = designerVOs.stream().filter(f -> f.getCheckStatus() == 1)
                            .map(DesignerVO::getDesignerId).map(String::valueOf).collect(Collectors.joining(","));
                    sysUser.setDesignerIds(ids);
                    Map<Long, Long> brandIdToDesignerIdMap = designerVOs.stream()
                            .filter(designer -> designer.getBrandId() != null) // filter out null brandIds if needed
                            .collect(Collectors.toMap(
                                    DesignerVO::getBrandId,
                                    DesignerVO::getDesignerId,
                                    (existing, replacement) -> existing // merge function in case of duplicate keys
                            ));
                    sysUser.setDesignerMap(brandIdToDesignerIdMap);
                }
            }else if(sysUserMiddleDto.getUserType() == 6){
                ServiceOwnerDto serviceOwnerDto = serviceOwnerMapper.selectServiceOwnerStaffByUserId(sysUserMiddleDto.getUserId());
                if(serviceOwnerDto != null){
                    sysUser.setUserName(serviceOwnerDto.getContact());
                    sysUser.setServiceId(serviceOwnerDto.getId());
                }
            }else if(sysUserMiddleDto.getUserType() == 7){
                ServiceStaffDto serviceStaffDto = serviceStaffMapper.selectServiceStaffById(sysUserMiddleDto.getUserId());
                if(serviceStaffDto != null){
                    sysUser.setUserName(serviceStaffDto.getStageName());
                    sysUser.setServiceId(serviceStaffDto.getServiceOwnerId());
                }
            }else if(sysUserMiddleDto.getUserType() == 8){
                ServiceOwnerDto serviceOwnerDto = serviceOwnerMapper.selectServiceOwnerStaffByUserId(sysUserMiddleDto.getUserId());
                if(serviceOwnerDto != null){
                    sysUser.setUserName(serviceOwnerDto.getContact());
                    sysUser.setServiceId(serviceOwnerDto.getId());
                }
            }else if(sysUserMiddleDto.getUserType() == 9){
                MarketStaffDto marketStaffDto = marketStaffMapper.selectMarketStaffById(sysUserMiddleDto.getUserId());
                if(marketStaffDto != null){
                    sysUser.setUserName(marketStaffDto.getStageName());
                    sysUser.setServiceId(marketStaffDto.getServiceOwnerId());
                }
            }
        }
        return buildSysUserDetails(sysUser);
    }

    private SysUserDetails buildSysUserDetails(SysUser user){
        if (BeanUtil.isEmpty(user)) {
            throw new ApiException(SysExceptionEnum.USER_NOT_EXIST);
        }
        Long userId = user.getUserId();
        // 获取角色
        Set<String> roles = new HashSet<>();
        //userService.listRoleKeyByUserId(userId);
        // 获取菜单权限标识
        Set<String> permissions = new HashSet<>();
        switch (user.getUserType()){
            case 1:
                roles.add("brandAdmin");
                permissions = sysMenuMapper.selectMenuTreeByModelType("brandAdmin");
                break;
            case 2:
                roles.add("franchiseeAdmin");
                permissions = sysMenuMapper.selectMenuTreeByModelType("franchiseeAdmin");
                break;
            case 3:
                roles.add("designAdmin");
                permissions = sysMenuMapper.selectMenuTreeByModelType("designAdmin");
                break;
            case 4:
                roles = xgwcBrandRoleService.selectRoleByUserId(userId)
                        .stream().map(r -> String.valueOf(r.getRoleId())).collect(Collectors.toSet());
                permissions = xgwcBrandRoleService.selectSysMenuBrandByUserId(userId);
                break;
            case 5:
                roles = franchiseRoleService.selectRoleByUserId(userId)
                        .stream().map(r -> String.valueOf(r.getRoleId())).collect(Collectors.toSet());
                permissions = xgwcBrandRoleService.selectSysMenuFranchiseByUserId(userId);
                break;
            case 6:
                roles.add("serviceAdmin");
                permissions = sysMenuMapper.selectMenuTreeByModelType("serviceAdmin");
                break;
        }

        if (true) {
            permissions.add(CommonConstant.ALL_PERMISSION);
        }
        // 过滤空字符串
        permissions.remove("");
        return new SysUserDetails(user, permissions, roles, user.getUserName());
    }

    public SysUserDetails loadUserByPhone(String phone) throws UsernameNotFoundException {
        // 获取登录用户信息
        SysUser user = userService.findUserByPhone(phone);
        return buildSysUserDetails(user);
    }
}
