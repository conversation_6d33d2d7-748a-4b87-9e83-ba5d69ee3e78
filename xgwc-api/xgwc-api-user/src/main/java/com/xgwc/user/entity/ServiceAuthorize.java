package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class ServiceAuthorize {

private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;

    /** 服务商id */
    private Long serviceId;

    /** 品牌商id */
    private Long brandId;

    /** 状态：0正常，1禁用 */
    private Long status;

    /** 是否删除：0正常，1删除 */
    private Long isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行更新时间 */
    private Date modifyTime;



}