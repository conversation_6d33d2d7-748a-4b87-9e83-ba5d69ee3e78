package com.xgwc.user.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class ReportTargetFranchiseDept {

    /** 主键 */
    private Long targetDeptId;

    /**品牌商ID*/
    private Long brandId;

    /** 加盟商id */
    private Long franchiseId;

    /** 部门id */
    private Long deptId;

    /** 目标id */
    private Long targetId;

    /**
     * 目标日期
     */
    private String targetDate;

    /** 目标额度 */
    private BigDecimal targetAmount;

    /** 订单金额 */
    private BigDecimal amountOrder;

    /** 实收金额 */
    private BigDecimal amountReal;

    /** 新客目标 */
    private BigDecimal targetNewCustomer;

    /** 老客目标 */
    private BigDecimal targetOldCustomer;

    /** 转介绍目标 */
    private BigDecimal targetTransferAmount;

    /** 佣金比例目标 */
    private BigDecimal targetComissionRate;

    /** 新客转化率目标 */
    private BigDecimal targetConversionRate;

    /** 实际新客金额 */
    private BigDecimal newCustomerAmount;

    /** 实际老客金额 */
    private BigDecimal oldCustomerAmount;

    /** 转介绍金额 */
    private BigDecimal transferAmount;

    /** 实际转换率 */
    private BigDecimal conversionRate;

    /** 实际佣金比例 */
    private BigDecimal comissionRate;

    /** 0正常，1非正常 */
    private Long status;

    /** 创建人 */
    private String createBy;

    /** 创建人id */
    private Long createById;

    /** 修改人 */
    private String updateBy;

    /** 修改人id */
    private Long updateById;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;

    /**
     * 提交状态
     */
    private Integer checkStatus;

}