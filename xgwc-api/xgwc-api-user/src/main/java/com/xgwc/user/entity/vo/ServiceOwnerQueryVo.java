package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class ServiceOwnerQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("公司名称")
    private String companyName;

    @FieldDesc("联系人")
    private String contact;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("服务商id")
    private Long id;

    @FieldDesc("是否删除：0正常，1删除")
    private Long isDel;

    @FieldDesc("管理员手机号")
    private String managerPhone;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;

    @FieldDesc("密码")
    private String password;

    @FieldDesc("服务商类型：1：财务服务商")
    private Long serviceType;

    @FieldDesc("状态：0正常，1禁用")
    private Long status;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("管理员用户ID")
    private Long userId;



}
