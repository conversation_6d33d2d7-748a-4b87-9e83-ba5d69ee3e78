package com.xgwc.user.entity.param;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-28  14:05
 */

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

/**
 * 加盟商岗位管理参数
 */
@Data
public class FranchiseStationParam {

    /**
     * 岗位名称
     */
    @FieldDesc("岗位名称")
    private String stationName;

    /**
     * 状态：0正常，1禁用
     */
    @FieldDesc("状态：0正常，1禁用")
    private Integer status;

    /**
     * 岗位id
     */
    @FieldDesc("岗位id")
    private Long stationId;

    /**
     * 加盟商ID
     */
    @FieldDesc("加盟商ID")
    private Long franchiseId;
}
