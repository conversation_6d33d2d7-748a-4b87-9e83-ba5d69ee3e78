package com.xgwc.user.entity.dto;

import com.xgwc.user.entity.vo.SysRoleDataVo;
import com.xgwc.user.entity.vo.SysRoleMenuVo;
import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;
import java.util.List;

@Data
public class SaasRoleDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键ID")
    @Excel(name = "主键ID")
    private Long roleId;

    @FieldDesc("角色名称")
    @Excel(name = "角色名称")
    private String roleName;

    @FieldDesc("排序")
    @Excel(name = "排序")
    private Integer sort;

    @FieldDesc("是否系统默认角色（0-否，1-是）")
    @Excel(name = "是否系统默认角色（0-否，1-是）")
    private Integer isDefault;

    @FieldDesc("角色权限范围:0-品普 1-品管 2-加普 3-加管 4-服普 5-服管 6-销普 7-销管 8-saas普 9-saas管")
    @Excel(name = "角色权限范围:0-品普 1-品管 2-加普 3-加管 4-服普 5-服管 6-销普 7-销管 8-saas普 9-saas管")
    private Integer roleScope;

    @FieldDesc("生效范围 品牌商/加盟商/财务服务商/销售服务商（id）")
    @Excel(name = "生效范围 品牌商/加盟商/财务服务商/销售服务商（id）")
    private List<SaasRoleScopeDto> roleScopeDtos;

    @FieldDesc("菜单权限组")
    private List<SysRoleMenuVo> menuIds;

    @FieldDesc("数据权限组")
    private List<SysRoleDataVo> dataMenuIds;

    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String companyName;

    @FieldDesc("业务系统 0-猴霸 1-人资")
    @Excel(name = "业务系统 0-猴霸 1-人资")
    private Integer businessSystem;

    @FieldDesc("角色状态（0-正常/1-禁用）")
    @Excel(name = "角色状态（0-正常/1-禁用）")
    private Integer status;

    @FieldDesc("删除 0-正常 1-删除")
    @Excel(name = "删除 0-正常 1-删除")
    private Integer isDel;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("修改人")
    @Excel(name = "修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    @FieldDesc("行更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "行更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;


    @Data
    public static class SaasRoleScopeDto {

        @FieldDesc("生效范围 品牌商/加盟商/财务服务商/销售服务商（id）")
        private Long effectiveScope;

        @FieldDesc("公司名称")
        private String companyName;

    }
}
