package com.xgwc.user.entity.vo;

import lombok.Data;

@Data
public class PrivacyNumberCallBackVo {

    /**
     * 用户账号
     */
    private String appId;

    /**
     * 绑定id
     */
    private String bindId;

    /**
     * 呼叫标识
     */
    private String callId;

    /**
     * 主显号码
     */
    private String displayNumber;

    /**
     * 主叫号码
     */
    private String callerNumber;

    /**
     * 被叫号码
     */
    private String calleeNumber;

    /**
     * 呼叫开始时间
     */
    private String startCallTime;

    /**
     * 主叫接通时间
     */
    private String callerAnsweredTime;

    /**
     * 被叫接通时间
     */
    private String calleeAnsweredTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 主叫呼叫状态
     */
    private Integer callerState;

    /**
     * 被叫呼叫状态
     */
    private Integer calleeState;

    /**
     * 主叫通话时长
     */
    private Integer callerDuration;

    /**
     * 被叫通话时长
     */
    private Integer calleeDuration;

    /**
     * 通话时长
     */
    private Integer duration;

    /**
     * 录音文件下载
     */
    private String recUrl;

    /**
     * 用户自定义数据
     */
    private String customerData;
}
