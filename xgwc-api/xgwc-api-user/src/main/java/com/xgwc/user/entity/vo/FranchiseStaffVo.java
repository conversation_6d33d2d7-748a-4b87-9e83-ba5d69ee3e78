package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

@Data
public class FranchiseStaffVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    private Long id;

    @FieldDesc("姓名")
    @NotNull("姓名不能为空")
    private String name;

    @FieldDesc("花名")
    @NotNull("姓名不能为空")
    private String stageName;

    @FieldDesc("部门id")
    @NotNull("部门id不能为空")
    private Long deptId;

    @FieldDesc("岗位id")
    @NotNull("岗位id不能为空")
    private Long postId;

    @FieldDesc("角色权限")
    private String roleIds;

    @FieldDesc("工作性质：0全职，1外包，2兼职")
    private Long jobNature;

    @FieldDesc("状态：0在职，1离职")
    @NotNull("状态不能为空")
    private Integer status;

    @FieldDesc("部门负责人：0是，1否")
    @NotNull("部门负责人不能为空")
    private Integer isPrincipal;

    @FieldDesc("直属上级")
    private Long superior;

    @FieldDesc("档案状态：0未录，1已录")
    private Integer archiveStatus;

    @FieldDesc("绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("绑定的用户id")
    private Long bindUserId;

    @FieldDesc("登录手机号")
    private String loginPhone;

    @FieldDesc("加盟商id")
    @NotNull("加盟商id不能为空")
    private Long franchiseId;



}
