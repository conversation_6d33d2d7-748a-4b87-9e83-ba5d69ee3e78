package com.xgwc.user.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.vo.FranchiseDesignerRecordVo;
import com.xgwc.user.entity.dto.FranchiseDesignerRecordDto;
import com.xgwc.user.entity.vo.FranchiseDesignerRecordQueryVo;
import com.xgwc.user.service.IFranchiseDesignerRecordService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;

/**
 * 加盟商设计师审核记录Controller
 */
@RestController
@RequestMapping("/franchiseDesignerRecord")
public class FranchiseDesignerRecordController extends BaseController {
    @Autowired
    private IFranchiseDesignerRecordService franchiseDesignerRecordService;

    /**
     * 查询加盟商设计师审核记录列表
     */
    @MethodDesc("查询加盟商设计师审核记录列表")
    @PreAuthorize("@ss.hasPermission('franchiseDesignerRecord:franchiseDesignerRecord:list')")
    @GetMapping("/list")
    public ApiResult<FranchiseDesignerRecordDto> list(FranchiseDesignerRecordQueryVo franchiseDesignerRecord) {
        startPage();
        List<FranchiseDesignerRecordDto> list = franchiseDesignerRecordService.selectFranchiseDesignerRecordList(franchiseDesignerRecord);
        return getDataTable(list);
    }


    /**
     * 导出加盟商设计师审核记录列表
     */
    @MethodDesc("导出加盟商设计师审核记录列表")
    @PreAuthorize("@ss.hasPermission('franchiseDesignerRecord:franchiseDesignerRecord:export')")
    @Log(title = "加盟商设计师审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FranchiseDesignerRecordQueryVo franchiseDesignerRecord) {
    }

    /**
     * 获取加盟商设计师审核记录详细信息
     */
    @MethodDesc("获取加盟商设计师审核记录详细信息")
    @PreAuthorize("@ss.hasPermission('franchiseDesignerRecord:franchiseDesignerRecord:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<FranchiseDesignerRecordDto> getInfo(@PathVariable("id") Long id) {
        return success(franchiseDesignerRecordService.selectFranchiseDesignerRecordById(id));
    }

    /**
     * 新增加盟商设计师审核记录
     */
    @MethodDesc("新增加盟商设计师审核记录")
    @PreAuthorize("@ss.hasPermission('franchiseDesignerRecord:franchiseDesignerRecord:add')")
    @Log(title = "加盟商设计师审核记录", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody FranchiseDesignerRecordVo franchiseDesignerRecord) {
        return toAjax(franchiseDesignerRecordService.insertFranchiseDesignerRecord(franchiseDesignerRecord));
    }

    /**
     * 修改加盟商设计师审核记录
     */
    @MethodDesc("修改加盟商设计师审核记录")
    @PreAuthorize("@ss.hasPermission('franchiseDesignerRecord:franchiseDesignerRecord:edit')")
    @Log(title = "加盟商设计师审核记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody FranchiseDesignerRecordVo franchiseDesignerRecord) {
        return toAjax(franchiseDesignerRecordService.updateFranchiseDesignerRecord(franchiseDesignerRecord));
    }

    /**
     * 删除加盟商设计师审核记录
     */
    @MethodDesc("删除加盟商设计师审核记录")
    @PreAuthorize("@ss.hasPermission('franchiseDesignerRecord:franchiseDesignerRecord:remove')")
    @Log(title = "加盟商设计师审核记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(franchiseDesignerRecordService.deleteFranchiseDesignerRecordByIds(ids));
    }
}
