package com.xgwc.user.config;

import com.aliyun.credentials.Client;
import com.aliyun.credentials.models.Config;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "aliyun.face")
@RefreshScope
public class AliyunFaceVerifyConfig {

    /**
     * AccessKey
     */
    private String accessKeyId;

    /**
     * AccessKey秘钥
     */
    private String accessKeySecret;

    /**
     * 回调地址
     */
    private String callBackUrl = "https://houba.cn/api/users/cloudauth/callback";

    /**
     * 场景ID
     */
    private Long sceneId;

    /**
     * 是否开启
     */
    private boolean open;

    @Bean
    public Client createFaceVerifyClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.setType("access_key");
        return new Client(config);
    }
}
