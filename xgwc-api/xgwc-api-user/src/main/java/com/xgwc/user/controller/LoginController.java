package com.xgwc.user.controller;

import com.xgwc.common.annotation.LoginLog;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.constants.ModelTypeConstant;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.LoginTypeEnums;
import com.xgwc.common.util.PasswordUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.FranchiseMapper;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.param.LoginBusinessParam;
import com.xgwc.user.entity.vo.*;
import com.xgwc.user.service.*;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequestMapping("login")
@RestController
public class LoginController {

    @Resource
    private SysUserService sysUserService;
    @Resource
    private UserService userService;
    @Resource
    private XgwcBrandRoleService xgwcBrandRoleService;
    @Resource
    private FranchiseRoleService franchiseRoleService;
    @Resource
    private IBrandOwnerService brandOwnerService;
    @Resource
    private IServiceOwnerService serviceOwnerService;
    @Resource
    private IFranchiseOwnerService franchiseOwnerService;
    @Resource
    private IApplyRecordService applyRecordService;
    @Resource
    private FranchiseMapper franchiseMapper;

    @LoginLog(loginType = LoginTypeEnums.PASSWD)
    @PostMapping(value = "/login")
    @PermitAll
    public ApiResult login(@RequestBody LoginVo loginVo) {
        if(StringUtils.isEmpty(loginVo.getUsername()) || StringUtils.isEmpty(loginVo.getPassword())){
            return ApiResult.error("账号或密码错误");
        }
        //拦截一部分垃圾参数
        if(!PasswordUtil.validate(loginVo.getPassword())){
            return ApiResult.error("密码强度太弱不允许登录");
        }
        return ApiResult.ok(sysUserService.login(loginVo.getUsername(), loginVo.getPassword()));
    }

    @LoginLog(loginType = LoginTypeEnums.PASSWD)
    @PostMapping(value = "/phone")
    @PermitAll
    public ApiResult loginPhone(@RequestBody LoginVo loginVo) {
        if(StringUtils.isEmpty(loginVo.getPhone()) || StringUtils.isEmpty(loginVo.getPassword())){
            return ApiResult.error("账号或密码错误");
        }
        //拦截一部分垃圾参数
        if(!PasswordUtil.validate(loginVo.getPassword())){
            return ApiResult.error("密码强度太弱不允许登录");
        }
        if(loginVo.getPhone().equals("admin")){
            loginVo.setUsername("admin");
            return login(loginVo);
        }
        return ApiResult.ok(sysUserService.loginPhone(loginVo));
    }

    @LoginLog(loginType = LoginTypeEnums.PASSWD)
    @PostMapping(value = "/check")
    @PermitAll
    public ApiResult checkLogin(@RequestBody LoginVo loginVo) {
        if(StringUtils.isEmpty(loginVo.getPhone()) || StringUtils.isEmpty(loginVo.getPassword())){
            return ApiResult.error("账号或密码错误");
        }
        return ApiResult.ok(sysUserService.check(loginVo));
    }


    @GetMapping("/getLoginUser")
    public ApiResult getLoginUser() {
        Map<String, Object> data = new HashMap<>();
        Long userId = SecurityUtils.getUserId();
        Set<String> roles = new HashSet<>();
        Set<String> permissions = new HashSet<>();
        List<SysMenuDto> menus = new ArrayList<>();
        SysUserMiddleDto sysUserMiddleDto = userService.selectSysUserByUserId(userId);
        int userType = sysUserMiddleDto.getUserType();
        switch (userType) {
            case 1: // 品牌商
                BrandOwnerDto brandOwnerDto = brandOwnerService.selectBrandOwnerByManagerUserId(userId);
                if(brandOwnerDto != null && brandOwnerDto.getStatus() == 0){
                    roles = getBrandRoleFlags(userId);
                    menus = xgwcBrandRoleService.selectMenuTreeByUserIdAndModelType(null, ModelTypeConstant.BRAND_ADMIN);
                    permissions = xgwcBrandRoleService.selectMenuTreeByModelType(ModelTypeConstant.BRAND_ADMIN);
                    sysUserMiddleDto.setBrandId(sysUserMiddleDto.getSourceId());
                    sysUserMiddleDto.setCheckStatus(1);
                    sysUserMiddleDto.setCompanyName(brandOwnerDto.getCompanyName());
                }else {
                    sysUserMiddleDto.setCheckStatus(0);
                }
                break;
            case 2: // 加盟商
                FranchiseDto franchise = franchiseMapper.findFranchiseById(sysUserMiddleDto.getSourceId());
                if (franchise != null) {
                    int checkStatus = franchiseOwnerService.selectFranchiseOwnerByFranchiseId(franchise.getId());
                    sysUserMiddleDto.setCheckStatus(checkStatus);
                    sysUserMiddleDto.setCompanyName(franchise.getFranchiseName());
                    if (checkStatus == 1) {
                        roles = xgwcBrandRoleService.selectBrandRoleKeyByUserId(userId);
                        menus = xgwcBrandRoleService.selectMenuTreeByUserIdAndModelType(null, ModelTypeConstant.FRANCHISE_ADMIN);
                        permissions = xgwcBrandRoleService.selectMenuTreeByModelType(ModelTypeConstant.FRANCHISE_ADMIN);
                    }
                }else {
                    log.error("FranchiseOwnerVO is null for userId: {}", userId);
                }
                break;
            case 3: // 设计师
                List<ApplyRecordDto> list = applyRecordService.selectApplyRecordListByUserIdAndBusinessType(sysUserMiddleDto.getUserId(), 2);
                // 存在多个申请记录，判断是否有通过的记录
                List<ApplyRecordDto> passList = list.stream().filter(item -> item.getCheckStatus() == 1).collect(Collectors.toList());
                if(!passList.isEmpty()){
                    sysUserMiddleDto.setCheckStatus(passList.get(0).getCheckStatus());
                    roles = getBrandRoleFlags(userId);
                    menus = xgwcBrandRoleService.selectMenuTreeByUserIdAndModelType(null, ModelTypeConstant.DESIGNER_ADMIN);
                    permissions = xgwcBrandRoleService.selectMenuTreeByModelType(ModelTypeConstant.DESIGNER_ADMIN);
                }else { // rule: 多个申请记录，没有通过的，默认都在审核中
                    sysUserMiddleDto.setCheckStatus(0);
                }
                break;
            case 4: // 品牌商员工
                BrandOwnerDto brandOwner = brandOwnerService.selectBrandOwnerByBrandId(sysUserMiddleDto.getSourceId());
                if(brandOwner != null){
                    sysUserMiddleDto.setCompanyName(brandOwner.getCompanyName());
                }
                roles = getBrandRoleFlags(userId);
                menus = xgwcBrandRoleService.selectMenuByUserId(userId);
                permissions = xgwcBrandRoleService.selectSysMenuBrandByUserId(userId);
                break;
            case 5: // 加盟商员工
                FranchiseDto franchiseDto = franchiseMapper.findFranchiseById(sysUserMiddleDto.getSourceId());
                if(franchiseDto != null){
                    sysUserMiddleDto.setCompanyName(franchiseDto.getFranchiseName());
                }
                roles = franchiseRoleService.selectRoleByUserId(userId)
                        .stream().map(FranchiseRoleVo::getIsFlag).collect(Collectors.toSet());
                menus = franchiseRoleService.selectMenuByUserId(userId);
                permissions = xgwcBrandRoleService.selectSysMenuFranchiseByUserId(userId);
                break;
            case 6: // 财务服务商
                ServiceOwnerDto serviceOwnerDto = serviceOwnerService.selectServiceOwnerByUserId(userId);
                if(serviceOwnerDto != null && serviceOwnerDto.getStatus() == 0){
                    roles = getBrandRoleFlags(userId);
                    menus = xgwcBrandRoleService.selectMenuTreeByUserIdAndModelType(null, ModelTypeConstant.SERVICE_ADMIN);
                    permissions = xgwcBrandRoleService.selectMenuTreeByModelType(ModelTypeConstant.SERVICE_ADMIN);
                    sysUserMiddleDto.setServiceOwnerId(sysUserMiddleDto.getSourceId());
                    sysUserMiddleDto.setCompanyName(serviceOwnerDto.getCompanyName());
                }
                break;
            case 7: // 服务商员工
                ServiceOwnerDto serviceOwner = serviceOwnerService.selectServiceOwnerById(sysUserMiddleDto.getSourceId());
                if(serviceOwner != null){
                    sysUserMiddleDto.setCompanyName(serviceOwner.getCompanyName());
                }
                roles = franchiseRoleService.selectRoleServiceByUserId(userId)
                        .stream().map(FranchiseRoleVo::getIsFlag).collect(Collectors.toSet());
                menus = franchiseRoleService.selectMenuServiceByUserId(userId);
                permissions = xgwcBrandRoleService.selectSysMenuServiceByUserId(userId);
                break;
            case 8: // 销售服务商
                ServiceOwnerDto marketOwner = serviceOwnerService.selectServiceOwnerByUserId(userId);
                if(marketOwner != null && marketOwner.getStatus() == 0){
                    roles = getBrandRoleFlags(userId);
                    menus = xgwcBrandRoleService.selectMenuTreeByUserIdAndModelType(null, ModelTypeConstant.MARKET_ADMIN);
                    permissions = xgwcBrandRoleService.selectMenuTreeByModelType(ModelTypeConstant.MARKET_ADMIN);
                    sysUserMiddleDto.setServiceOwnerId(sysUserMiddleDto.getSourceId());
                    sysUserMiddleDto.setCompanyName(marketOwner.getCompanyName());
                }
                break;
            case 9: // 销售服务商员工
                ServiceOwnerDto serviceOwnerStaff = serviceOwnerService.selectServiceOwnerById(sysUserMiddleDto.getSourceId());
                if(serviceOwnerStaff != null){
                    sysUserMiddleDto.setCompanyName(serviceOwnerStaff.getCompanyName());
                }
                roles = franchiseRoleService.selectRoleMarketByUserId(userId)
                        .stream().map(FranchiseRoleVo::getIsFlag).collect(Collectors.toSet());
                menus = franchiseRoleService.selectMenuMarketByUserId(userId);
                permissions = xgwcBrandRoleService.selectSysMenuMarketByUserId(userId);
                break;
            default: // 其他
                roles = userService.listRoleKeyByUserId(userId);
                permissions = userService.getMenuPermissionByUserId(userId);
                menus = userService.getMenuTreeByUserId(userId);
                break;
        }
        data.put("user", sysUserMiddleDto);
        data.put("roles", roles);
        data.put("permissions", permissions);
        data.put("menus", menus);
        return ApiResult.ok(data);
    }

    private Set<String> getBrandRoleFlags(Long userId) {
        return xgwcBrandRoleService.selectRoleByUserId(userId)
                .stream().map(XgwcBrandRoleVo::getIsFlag).collect(Collectors.toSet());
    }


    @PostMapping("getBusinessLoginUser")
    public ApiResult getBusinessLoginUser(@RequestBody LoginBusinessParam param){
        return ApiResult.ok(userService.getBusinessLoginUser(param));
    }

    @GetMapping(value = "/info")
    public ApiResult<UserInfoResult> getInfo() {
        UserInfoResult result = sysUserService.getInfo();
        return ApiResult.ok(result);
    }

    @PostMapping(value = "/logout")
    public ApiResult logout(HttpServletRequest request) {
        // 需要 将当前用户token 设置无效
        SecurityContextHolder.clearContext();
        return ApiResult.ok();
    }

    @LoginLog(loginType = LoginTypeEnums.VALID_CODE)
    @PostMapping("/sms_login")
    @PermitAll
    public ApiResult<LoginResult> smsLogin(@RequestBody SmsLoginVO reqVO) {
        if(StringUtils.isEmpty(reqVO.getPhone()) || StringUtils.isEmpty(reqVO.getCode())){
            return ApiResult.error("参数错误");
        }
        return ApiResult.ok(sysUserService.smsLogin(reqVO));
    }

//    @MethodDesc("品牌商注册")
//    @LoginLog(loginType = LoginTypeEnums.REGISTER)
//    @PostMapping("/brand_register")
//    @PermitAll
//    public ApiResult brandRegister(@RequestBody BrandRegisterVo reqVO) {
//        if(StringUtils.isEmpty(reqVO.getCompanyName()) || StringUtils.isEmpty(reqVO.getCode())
//                || StringUtils.isEmpty(reqVO.getContact()) || StringUtils.isEmpty(reqVO.getPassword())
//                || StringUtils.isEmpty(reqVO.getManagerPhone()) || StringUtils.isEmpty(reqVO.getConfirmPassword())){
//            return ApiResult.error("参数错误");
//        }
//        return ApiResult.ok(sysUserService.brandRegister(reqVO));
//    }

    @MethodDesc("注册")
    @LoginLog(loginType = LoginTypeEnums.REGISTER)
    @PostMapping("/register")
    @PermitAll
    public ApiResult register(@RequestBody RegisterVo reqVO) {
        if(StringUtils.isEmpty(reqVO.getCode()) || StringUtils.isEmpty(reqVO.getPassword())
                || StringUtils.isEmpty(reqVO.getManagerPhone()) || StringUtils.isEmpty(reqVO.getConfirmPassword())){
            return ApiResult.error("参数错误");
        }
        return ApiResult.ok(sysUserService.register(reqVO));
    }

    @MethodDesc("员工注册")
    @LoginLog(loginType = LoginTypeEnums.REGISTER)
    @PostMapping("/staff_register")
    @PermitAll
    public ApiResult smsLogin(@RequestBody @Valid StaffRegisterVo reqVO) {
        return ApiResult.ok(sysUserService.StaffRegister(reqVO));
    }

    @MethodDesc("确认加入")
    @LoginLog(loginType = LoginTypeEnums.REGISTER)
    @PostMapping("/bind_staff")
    @PermitAll
    public ApiResult bindStaff(@RequestBody @Valid StaffBindVo reqVO) {
         return ApiResult.ok(sysUserService.bindStaff(reqVO));
    }
}
