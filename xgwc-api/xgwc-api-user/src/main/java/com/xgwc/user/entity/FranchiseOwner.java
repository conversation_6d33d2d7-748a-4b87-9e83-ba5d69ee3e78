package com.xgwc.user.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


@Data
public class FranchiseOwner {

private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;

    /** 营业执照 */
    private String businessLicense;

    /** 公司名称 */
    private String companyName;

    /** 公司简称 */
    private String companySimpleName;

    /** 营业执照编码 */
    private String licenseCode;

    /** 执照有效期是否长期：0长期，1非长期 */
    private Integer licenseIsLongterm;

    /** 营业执照开始时间 */
    private Date licenseStart;

    /** 营业执照结束时间 */
    private Date licenseEnd;

    /** 营业省 */
    private Integer operateProvince;

    /** 营业市 */
    private Integer operateCity;

    /** 营业区 */
    private Integer operateRegion;

    /** 详细地址 */
    private String operateAddress;

    /** 规模 */
    private String scale;

    /** 身份证正面：加密 */
    private String idcardFront;

    /** 身份证反面：加密 */
    private String idcardBack;

    /** 身份证名称：加密 */
    private String idcardName;

    /** 身份证号：加密 */
    private String idcardNo;

    /** 身份证是否长期：0长期，1非长期 */
    private Integer idcardIsLongterm;

    /** 身份证开始时间 */
    private Date idcardStart;

    /** 身份证结束时间 */
    private Date idcardEnd;

    /** 法人手机号：加密 */
    private String idcardPhone;

    /** 邮箱：加密 */
    private String idcardEmail;

    /** 开户名：加密 */
    private String bankUserName;

    /** 开户行 */
    private String bankName;

    /** 对公账号：加密 */
    private String bankNo;

    /** 管理员姓名 */
    private String managerName;

    /** 管理员手机号：加密 */
    private String managerPhone;

    /** 申请状态 0审核中 1审核通过 2审核不通过 3撤回 */
    private Integer checkStatus;

    /** 审核时间 */
    private Date checkTime;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 删除状态：0正常，1删除 */
    private Integer isDel;

    /** 审核原因 */
    private String reason;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /** 授权店铺 */
    private String shopIds;

    /** 管理员id */
    private Long managerUserId;

    /** 所属品牌id */
    private Long brandId;

    /** 下载次数上限/天/人 */
    private Integer downloadLimit;

    /** 是否设置（0-是 1-否） */
    private Integer isConfigured;

    private Long franchiseId;
}