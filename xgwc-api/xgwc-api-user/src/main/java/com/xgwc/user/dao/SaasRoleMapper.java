package com.xgwc.user.dao;

import java.util.List;

import com.xgwc.user.entity.vo.SaasRoleVo;
import com.xgwc.user.entity.dto.SaasRoleDto;
import com.xgwc.user.entity.vo.SaasRoleQueryVo;
import com.xgwc.user.entity.vo.SysRoleDataVo;
import com.xgwc.user.entity.vo.SysRoleMenuVo;
import org.apache.ibatis.annotations.Param;


public interface SaasRoleMapper {
    /**
     * 查询saas后台角色
     *
     * @param roleId saas后台角色主键
     * @return saas后台角色
     */
    SaasRoleDto selectSaasRoleByRoleId(Long roleId);

    /**
     * 查询saas后台角色列表
     *
     * @param saasRole saas后台角色
     * @return saas后台角色集合
     */
    List<SaasRoleDto> selectSaasRoleList(SaasRoleQueryVo saasRole);

    /**
     * 新增saas后台角色
     *
     * @param saasRole saas后台角色
     * @return 结果
     */
    int insertSaasRole(SaasRoleVo saasRole);

    /**
     * 修改saas后台角色
     *
     * @param saasRole saas后台角色
     * @return 结果
     */
    int updateSaasRole(SaasRoleVo saasRole);

    /**
     * 删除saas后台角色
     *
     * @param roleId saas后台角色主键
     * @return 结果
     */
    int deleteSaasRoleByRoleId(Long roleId);

    /**
     * 保存角色菜单关系
     * @param sysRoleMenuVoList 角色菜单关系
     * @return 保存信息
     */
    int saveRoleMenu(@Param("sysRoleMenuVoList") List<SysRoleMenuVo> sysRoleMenuVoList);

    /**
     * 保存角色数据权限关系
     * @param sysRoleDataVo 角色数据权限关系
     * @return 保存信息
     */
    int saveRoleDataScope(@Param("sysRoleDataVo") SysRoleDataVo sysRoleDataVo);


    /**
     * 批量保存角色数据权限
     * @param dataMenu 角色数据权限
     * @return 角色数据权限id
     */
    int saveRoleDataScopeDeptId(@Param("dataMenu") SysRoleDataVo dataMenu);

    /**
     * 批量保存角色权限范围
     * @param saasRole 角色数据范围
     * @return 角色数据权限id
     */
    int insertSaasRoleScope(@Param("saasRole") SaasRoleVo saasRole);

    /**
     * 删除角色菜单关系
     * @param roleId 角色id
     */
    void deleteRoleMenu(Long roleId);

    /**
     * 删除角色数据权限关系
     * @param roleId 角色id
     */
    void deleteRoleData(Long roleId);

    /**
     * 删除角色权限范围
     * @param roleId 角色id
     */
    void deleteRoleScope(Long roleId);

    /**
     * 根据角色id查询角色菜单关系
     * @param roleId 角色id
     * @return 角色菜单关系
     */
    List<SysRoleMenuVo> getRoleMenusById(Long roleId);

    /**
     * 查询菜单是否有子级菜单
     *
     * @return true、false
     */
    Boolean selectLastLevelMenu(Long menuId, String isFlag);

    /**
     * 根据角色id查询角色数据权限关系
     * @param roleId 角色id
     * @return 角色数据权限关系
     */
    List<SysRoleDataVo> getRoleDataById(Long roleId);

    /**
     * 查询菜单是否有子级菜单
     *
     * @return true、false
     */
    Boolean selectLastLevelMenuDate(Long menuId, String isFlag);

    /**
     * 根据角色id查询角色权限范围
     *
     *  @param roleId 角色id
     * @return 角色权限范围
     */
    List<SaasRoleDto.SaasRoleScopeDto> selectRoleScopeById(Long roleId);
}
