package com.xgwc.user.controller;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.StringUtil;
import com.xgwc.user.entity.dto.aliyun.BusinessLicense;
import com.xgwc.user.entity.dto.aliyun.IdCard;
import com.xgwc.user.service.UploadService;
import com.xgwc.user.service.VideoService;
import com.xgwc.user.util.AliyunOcrUtil;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@CrossOrigin
@RequestMapping("upload")
@RestController
public class UploadController {

    @Resource
    private UploadService uploadService;

    @Resource
    private VideoService videoService;

    /**
     * 上传文件
     */
    @PostMapping("uploadfile")
    public ApiResult uploadFile(@RequestParam("file") MultipartFile file){
        if(file.isEmpty()){
            return ApiResult.error("文件为空");
        }
        return ApiResult.ok(uploadService.uploadFile(file));
    }

    /**
     * 上传图片
     */
    @PostMapping("uploadpic")
    public ApiResult uploadPic(@RequestParam("file") MultipartFile file){
        if(file.isEmpty()){
            return ApiResult.error("文件为空");
        }
        return ApiResult.ok(uploadService.uploadPic(file));
    }

    /**
     * 上传视频
     */
    @PostMapping("uploadvideo")
    public ApiResult uploadVideo(@RequestParam("file") MultipartFile file){
        if(file.isEmpty()){
            return ApiResult.error("文件为空");
        }
        Integer videoId = videoService.uploadVideo(file);
        return videoId == null ? ApiResult.error("上传失败") : ApiResult.ok(videoId);
    }

}
