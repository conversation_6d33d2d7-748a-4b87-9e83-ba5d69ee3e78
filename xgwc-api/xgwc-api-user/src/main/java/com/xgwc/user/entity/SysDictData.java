package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class SysDictData {

private static final long serialVersionUID=1L;

    /** 创建用户id */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 样式属性（其他样式扩展） */
    private String cssClass;

    /** 字典编码 */
    private Long dictCode;

    /** 字典标签 */
    private String dictLabel;

    /** 字典排序 */
    private Long dictSort;

    /** 字典类型 */
    private String dictType;

    /** 字典键值 */
    private String dictValue;

    /** 是否默认（Y是 N否） */
    private String isDefault;

    /** 是否删除 */
    private Integer isDel;

    /** 表格回显样式 */
    private String listClass;

    /** 行更新时间 */
    private Date modifyTime;

    /** 上级字典编码 */
    private Long parentDictCode;

    /** 备注 */
    private String remark;

    /** 状态（0正常 1停用） */
    private Integer status;

    /** 修改人id */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 品牌商id */
    private Long brandId;



}