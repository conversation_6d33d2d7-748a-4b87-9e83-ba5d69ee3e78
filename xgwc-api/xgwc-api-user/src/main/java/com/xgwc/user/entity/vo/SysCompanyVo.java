package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;

@Data
public class SysCompanyVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("自增id")
    private Long id;

    @FieldDesc("上级id")
    private Long pid;

    @FieldDesc("公司名称")
    private String name;

    @FieldDesc("公司类型 0-母公司 1-子公司")
    private Integer companyType;

    @FieldDesc("排序")
    private Long orderNum;

    @FieldDesc("状态：0正常，1无效")
    private Integer status;

    /**
     * 创建时间
     */
    @FieldDesc("创建时间")
    private String createTime;



}
