package com.xgwc.user.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.vo.SysRoleVo;
import com.xgwc.user.entity.dto.SysRoleDto;
import com.xgwc.user.entity.vo.SysRoleQueryVo;
import com.xgwc.user.service.ISysRoleService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/user/role")
public class SysRoleController extends BaseController {
    @Autowired
    private ISysRoleService sysRoleService;

    /**
     * 查询角色管理列表
     */
    @MethodDesc("查询角色管理列表")
    @PreAuthorize("@ss.hasPermission('user:role:list')")
    @GetMapping("/list")
    public ApiResult<SysRoleDto> list(SysRoleQueryVo sysRole) {
        startPage();
        List<SysRoleDto> list = sysRoleService.selectSysRoleList(sysRole);
        return getDataTable(list);
    }

    /**
     * 获取角色管理详细信息
     */
    @MethodDesc("获取角色管理详细信息")
    @PreAuthorize("@ss.hasPermission('user:role:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<SysRoleDto> getInfo(@PathVariable("id") Long id) {
        return success(sysRoleService.selectSysRoleById(id));
    }

    /**
     * 新增角色管理
     */
    @MethodDesc("新增角色管理")
    @PreAuthorize("@ss.hasPermission('user:role:add')")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody SysRoleVo sysRole) {
        return toAjax(sysRoleService.insertSysRole(sysRole));
    }

    /**
     * 修改角色管理
     */
    @MethodDesc("修改角色管理")
    @PreAuthorize("@ss.hasPermission('user:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody SysRoleVo sysRole) {
        return toAjax(sysRoleService.updateSysRole(sysRole));
    }

    /**
     * 删除角色管理
     */
    @MethodDesc("删除角色管理")
    @PreAuthorize("@ss.hasPermission('user:role:remove')")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(sysRoleService.deleteSysRoleByIds(ids));
    }
}
