package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.annotation.Submit;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.BranchFranchiseDeptStaff;
import com.xgwc.user.entity.dto.FranchiseDeptDto;
import com.xgwc.user.entity.dto.FranchiseDeptStaff;
import com.xgwc.user.entity.param.FranchiseDeptParam;
import com.xgwc.user.entity.vo.FranchiseDeptInfo;
import com.xgwc.user.entity.vo.FranchiseDeptVo;
import com.xgwc.user.service.FranchiseDeptService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: -parent
 * @BelongsPackage: com..order.controller
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-22  16:38
 */

/**
 * 加盟商部门管理
 */
@RestController
@RequestMapping("/franchiseDept")
@Slf4j
public class FranchiseDeptController extends BaseController {

    @Resource
    private FranchiseDeptService franchiseDeptService;

    /**
     * @param franchiseDeptParam 查询条件
     * @return 部门管理列表
     * 查询部门管理列表
     */
    @MethodDesc("查询部门管理列表")
    @PreAuthorize("@ss.hasPermission('franchiseDept:franchiseDept:list')")
    @PostMapping("/getFranchiseDeptList")
    public ApiResult<FranchiseDeptInfo> getFranchiseDeptList(@RequestBody FranchiseDeptParam franchiseDeptParam) {
        startPage();
        return getDataTable(franchiseDeptService.getFranchiseDeptList(franchiseDeptParam));
    }

    /**
     * 查询部门管理树
     *
     * @return 部门管理列表
     */
    @GetMapping("/getFranchiseDeptDropDown")
    public ApiResult getFranchiseDeptListInfo(@RequestParam(value = "franchiseId", required = false) Long franchiseId,
                                              @RequestParam(value = "deptName", required = false) String deptName) {
        try {
            FranchiseDeptParam franchiseDeptParam = new FranchiseDeptParam();
            franchiseDeptParam.setStatus(0);
            franchiseDeptParam.setFranchiseId(franchiseId);
            franchiseDeptParam.setDeptName(deptName);
            List<FranchiseDeptInfo> result = franchiseDeptService.getFranchiseDeptList(franchiseDeptParam);
            return ApiResult.ok(Objects.requireNonNullElse(result, Collections.emptyList()));
        } catch (Exception e) {
            log.error("获取加盟商部门管理树失败", e);
            return ApiResult.error("获取加盟商部门管理树失败");
        }
    }

    /**
     * @param franchiseDeptDto 新增部门管理信息
     * @return 插入结果
     * 新增部门管理信息
     */
    @Submit(fileds = "userId")
    @MethodDesc("新增部门管理信息")
    @PreAuthorize("@ss.hasPermission('franchiseDept:franchiseDept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping("/saveFranchiseDept")
    public ApiResult saveFranchiseDept(@RequestBody @Valid FranchiseDeptDto franchiseDeptDto) {
        return franchiseDeptService.saveFranchiseDept(franchiseDeptDto);
    }

    /**
     * @param deptId 部门管理id
     * @return 部门管理信息
     * 根据id查询部门管理信息
     */
    @MethodDesc("根据id查询部门管理信息")
    @PreAuthorize("@ss.hasPermission('franchiseDept:franchiseDept:query')")
    @GetMapping("/getFranchiseDeptById/{deptId}")
    public ApiResult getFranchiseDeptById(@PathVariable("deptId") Long deptId) {
        return franchiseDeptService.getFranchiseDeptById(deptId);
    }

    /**
     * @param franchiseDeptDto 修改信息
     * @return 修改结果
     * 修改部门管理信息
     */
    @MethodDesc("修改部门管理信息")
    @Submit(fileds = "userId")
    @PreAuthorize("@ss.hasPermission('franchiseDept:franchiseDept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateFranchiseDept")
    public ApiResult updateFranchiseDeptById(@RequestBody FranchiseDeptDto franchiseDeptDto) {
        return franchiseDeptService.updateFranchiseDeptById(franchiseDeptDto);
    }

    /**
     * @param deptId 部门id
     * @return 品牌信息
     * 根据id修改状态
     */
    @MethodDesc("根据id修改状态")
    @PreAuthorize("@ss.hasPermission('franchiseDept:franchiseDept:editStatus')")
    @GetMapping("/updateStatusById")
    public ApiResult updateStatusById(@RequestParam(value = "deptId") Integer deptId,
                                      @RequestParam(value = "status") Integer status) {
        return franchiseDeptService.updateStatusById(deptId, status);
    }

    /**
     * 查询回显部门详情信息-负责人、助理、是否排班
     *
     * @param deptId 部门id
     * @return 部门详情信息-负责人、助理、是否排班
     */
    @MethodDesc("查询回显部门详情信息-负责人、助理、是否排班")
    @PreAuthorize("@ss.hasPermission('franchiseDept:franchiseDept:query')")
    @GetMapping("/getXgwcBrandDeptInfo")
    public ApiResult getXgwcDeptStaffInfo(@RequestParam(value = "deptId") Integer deptId) {
        return franchiseDeptService.getXgwcDeptStaffInfo(deptId);
    }

    /**
     * 修改部门详情信息-负责人、助理、是否排班
     *
     * @param franchiseDeptDto 部门详情信息-负责人、助理、是否排班
     * @return 部门详情信息-负责人、助理、是否排班
     */
    @MethodDesc("修改部门详情信息-负责人、助理、是否排班")
    @PreAuthorize("@ss.hasPermission('franchiseDept:franchiseDept:edit')")
    @PostMapping("/getXgwcDeptStaffSchedule")
    public ApiResult getXgwcDeptStaffSchedule(@RequestBody FranchiseDeptDto franchiseDeptDto) {
        return franchiseDeptService.updateXgwcDeptStaffInfo(franchiseDeptDto);
    }

    /**
     * 获取加盟商员工数
     */
    @RequestMapping("getDeptStaffTree")
    public ApiResult getDeptStaffTree(Long franchiseId){
        List<FranchiseDeptStaff> franchiseDeptStaffs = franchiseDeptService.getDeptStaffTree(franchiseId);
        return getDataTable(franchiseDeptStaffs);
    }

    /**
     * 获取品牌商下所有加盟商员工
     * @return
     */
    @RequestMapping("getAllFranchiseStaffForBrand")
    public ApiResult getAllFranchiseStaffForBrand(){
        List<BranchFranchiseDeptStaff> branchFranchiseDeptStaffs = franchiseDeptService.getAllFranchiseStaffForBrand();
        return getDataTable(branchFranchiseDeptStaffs);
    }


    @MethodDesc("根据加盟商id查询所有一级部门")
    @GetMapping("/getDeptByFranchiseId/{franchiseId}")
    public ApiResult getDeptByFranchiseId(@PathVariable("franchiseId") Long franchiseId){
        List<FranchiseDeptVo> franchiseDeptInfos = franchiseDeptService.getDeptByFranchiseId(franchiseId);
        return ApiResult.ok(franchiseDeptInfos);
    }


}
