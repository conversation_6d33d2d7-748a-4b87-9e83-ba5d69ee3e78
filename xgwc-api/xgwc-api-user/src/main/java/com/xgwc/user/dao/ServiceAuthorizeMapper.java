package com.xgwc.user.dao;

import com.xgwc.user.entity.ServiceAuthorize;
import com.xgwc.user.entity.dto.ServiceAuthorizeDto;
import com.xgwc.user.feign.entity.BrandOwnerDto;

import java.util.List;


public interface ServiceAuthorizeMapper {
    /**
     * 查询服务授权
     *
     * @param brandId 品牌商ID
     * @return 服务授权
     */
    ServiceAuthorizeDto selectServiceAuthorizeByBrandId(Long brandId);

    /**
     * 新增服务授权
     *
     * @param serviceAuthorize 服务授权
     * @return 结果
     */
    int insertServiceAuthorize(ServiceAuthorize serviceAuthorize);

    /**
     * 修改服务授权
     *
     * @param serviceAuthorize 服务授权
     * @return 结果
     */
    int updateServiceAuthorize(ServiceAuthorize serviceAuthorize);


    /**
     * 获取授权给服务商的品牌商Id列表
     *
     * @param serviceId
     * @return
     */
    List<Long> getServiceBrandIdList(Long serviceId);

    List<BrandOwnerDto> getServiceBrandList(Long serviceId);
}
