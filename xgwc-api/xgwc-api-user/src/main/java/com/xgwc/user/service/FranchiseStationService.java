package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.FranchiseStationDto;
import com.xgwc.user.entity.param.FranchiseStationParam;
import com.xgwc.user.entity.vo.FranchiseStationVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhu<PERSON>
 * @CreateTime: 2025-04-28  14:11
 */
public interface FranchiseStationService {

    /**
     * 获取加盟商岗位列表
     * @param franchiseStationParam 加盟商岗位参数
     * @return 加盟商岗位列表
     */
    List<FranchiseStationVo> getFranchiseStationList(FranchiseStationParam franchiseStationParam);

    /**
     * 保存加盟商岗位
     * @param franchiseStationDto 加盟商岗位参数
     * @return 加盟商岗位列表
     */
    ApiResult saveFranchiseStation(FranchiseStationDto franchiseStationDto);

    /**
     * 根据加盟商岗位id获取加盟商岗位
     * @param stationId 加盟商岗位id
     * @return 加盟商岗位
     */
    ApiResult getFranchiseStationById(Long stationId);

    /**
     * 更新加盟商岗位
     * @param franchiseStationDto 加盟商岗位参数
     * @return 加盟商岗位列表
     */
    ApiResult updateFranchiseStation(FranchiseStationDto franchiseStationDto);

    /**
     * 更加盟商岗位状态
     * @param stationId 加盟商岗位id
     * @param status 加盟商岗位状态
     * @return 加盟商岗位列表
     */
    ApiResult updateStatusById(Integer stationId, Integer status);
}
