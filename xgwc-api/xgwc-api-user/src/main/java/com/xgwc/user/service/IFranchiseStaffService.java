package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.FranchiseStaff;
import com.xgwc.user.entity.dto.FranchiseStaffDto;
import com.xgwc.user.entity.dto.FranchiseStaffPageDto;
import com.xgwc.user.entity.dto.FranchiseStaffSimpleDto;
import com.xgwc.user.entity.vo.FranchiseStaffQueryVo;
import com.xgwc.user.entity.vo.FranchiseStaffVo;
import com.xgwc.user.feign.entity.FeignFranchiseStaffDto;

import java.util.List;
import java.util.Map;

public interface IFranchiseStaffService  {
    /**
     * 查询加盟商员工
     * 
     * @param id 加盟商员工主键
     * @return 加盟商员工
     */
    public FranchiseStaffDto selectFranchiseStaffById(Long id);

    /**
     * 查询加盟商员工列表
     * 
     * @param franchiseStaff 加盟商员工
     * @return 加盟商员工集合
     */
    public List<FranchiseStaffPageDto> selectFranchiseStaffList(FranchiseStaffQueryVo franchiseStaff);

    /**
     * 新增加盟商员工
     * 
     * @param franchiseStaff 加盟商员工
     * @return 结果
     */
    public int insertFranchiseStaff(FranchiseStaffVo franchiseStaff);

    /**
     * 修改加盟商员工
     * 
     * @param franchiseStaff 加盟商员工
     * @return 结果
     */
    public int updateFranchiseStaff(FranchiseStaffVo franchiseStaff);

    /**
     * 批量删除加盟商员工
     * 
     * @param ids 需要删除的加盟商员工主键集合
     * @return 结果
     */
    public int deleteFranchiseStaffByIds(Long[] ids);

    /**
     * 删除加盟商员工信息
     * 
     * @param id 加盟商员工主键
     * @return 结果
     */
    public int deleteFranchiseStaffById(Long id);

    /**
     * 根据姓名和加盟商id查询加盟商员工
     * @param name 姓名
     * @param franchiseId 加盟商id
     * @return 加盟商员工
     */
    public FranchiseStaffDto selectFranchiseStaffByNameAndFranchiseId(String name,Long franchiseId);

    /**
     * 更新绑定状态
     * @param id 加盟商员工id
     * @param bindUserId 绑定用户id
     * @param bindStatus 绑定状态\
     * @param phone 手机号
     * @return 结果
     */
    public int updateBindStatus(Long id,Long bindUserId,Integer bindStatus,String phone,String stageName);

    /**
     * 获取加盟商员工下拉框
     * @return 加盟商员工下拉框
     */
    List<Map<String, Object>> selectFranchiseStaffListDropDown();

    /**
     * 更新加盟商员工状态
     * @param id 加盟商员工id
     * @param status 状态
     * @return 结果
     */
    int updateFranchiseeStaffStatus(Long id, Integer status);

    /**
     * 根据绑定用户id查询加盟商员工
     * @param bindUserId 绑定用户id
     * @return 加盟商员工
     */
    FranchiseStaffDto selectFranchiseStaffByBindUserId(Long bindUserId);

    /**
     *加盟商员工下载次数限制列表
     * @param franchiseStaff 加盟商员工
     * @return 加盟商员工下载次数限制列表
     */
    List<FranchiseStaffDto> selectStaffDownloadLimitList(FranchiseStaffQueryVo franchiseStaff);

    /**
     * 修改加盟商员工下载次数限制
     * @param franchiseStaff 加盟商员工
     * @return 结果
     */
    ApiResult updateStaffDownloadLimit(FranchiseStaff franchiseStaff);

    /**
     * 加盟商员工下拉框
     * @return 加盟商员工下拉框
     */
    List<FranchiseStaffSimpleDto> dropDownList(Long deptId);

    /**
     * 每天凌晨12点刷新员工下载次数
     */
    void refreshStaffDownloadCount();

    /**
     * 根据用户id和加盟商id查询加盟商员工信息
     * @param userId 用户id
     * @param franchiseId 加盟商id
     * @return 加盟商员工信息
     */
    FranchiseStaffDto selectFranchiseStaffInfoByUserIdAndFranchiseId(Long userId, Long franchiseId);

    Long feignAddFranchiseStaff(FeignFranchiseStaffDto franchiseStaff);

    /**
     * 根据部门id和加盟商id查询加盟商员工
     * @param deptId 部门id
     * @param franchiseId 加盟商id
     * @return 加盟商员工
     */
    List<FranchiseStaffSimpleDto> selectStaffByDeptIdAndFranchiseId(Long deptId, Long franchiseId);
}
