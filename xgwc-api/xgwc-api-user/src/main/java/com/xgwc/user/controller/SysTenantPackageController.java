package com.xgwc.user.controller;

import java.util.List;

import com.xgwc.common.annotation.Log;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.vo.SysTenantPackageVo;
import com.xgwc.user.entity.dto.SysTenantPackageDto;
import com.xgwc.user.entity.vo.SysTenantPackageQueryVo;
import com.xgwc.user.service.ISysTenantPackageService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/tenant/tenantPackage")
public class SysTenantPackageController extends BaseController
{
    @Autowired
    private ISysTenantPackageService sysTenantPackageService;

    /**
     * 查询租户套餐管理列表
     */
    @MethodDesc("查询租户套餐管理列表")
    @PreAuthorize("@ss.hasPermission('tenant:tenantPackage:list')")
    @GetMapping("/list")
    public ApiResult<SysTenantPackageDto> list(SysTenantPackageQueryVo sysTenantPackage) {
        startPage();
        List<SysTenantPackageDto> list = sysTenantPackageService.selectSysTenantPackageList(sysTenantPackage);
        return getDataTable(list);
    }


    /**
     * 导出租户套餐管理列表
     */
    @MethodDesc("导出租户套餐管理列表")
    @PreAuthorize("@ss.hasPermission('tenant:tenantPackage:export')")
    @Log(title = "租户套餐管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysTenantPackageQueryVo sysTenantPackage)
    {
    }

    /**
     * 获取租户套餐管理详细信息
     */
    @MethodDesc("获取租户套餐管理详细信息")
    @PreAuthorize("@ss.hasPermission('tenant:tenantPackage:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<SysTenantPackageDto> getInfo(@PathVariable("id") Long id)
    {
        return success(sysTenantPackageService.selectSysTenantPackageById(id));
    }

    /**
     * 新增租户套餐管理
     */
    @MethodDesc("新增租户套餐管理")
    @PreAuthorize("@ss.hasPermission('tenant:tenantPackage:add')")
    @Log(title = "租户套餐管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody SysTenantPackageVo sysTenantPackage)
    {
        return toAjax(sysTenantPackageService.insertSysTenantPackage(sysTenantPackage));
    }

    /**
     * 修改租户套餐管理
     */
    @MethodDesc("修改租户套餐管理")
    @PreAuthorize("@ss.hasPermission('tenant:tenantPackage:edit')")
    @Log(title = "租户套餐管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody SysTenantPackageVo sysTenantPackage)
    {
        return toAjax(sysTenantPackageService.updateSysTenantPackage(sysTenantPackage));
    }

    /**
     * 删除租户套餐管理
     */
    @MethodDesc("删除租户套餐管理")
    @PreAuthorize("@ss.hasPermission('tenant:tenantPackage:remove')")
    @Log(title = "租户套餐管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysTenantPackageService.deleteSysTenantPackageByIds(ids));
    }
}
