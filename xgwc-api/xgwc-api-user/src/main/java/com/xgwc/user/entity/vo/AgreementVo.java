package com.xgwc.user.entity.vo;

import lombok.Data;
import org.jetbrains.annotations.NotNull;

@Data
public class AgreementVo {

    /**
     * 协议id
     */
    private String id;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 标题
     */
    @NotNull("标题不能为空")
    private String title;

    /**
     * 内容
     */
    @NotNull("内容不能为空")
    private String content;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;
}
