package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;


@Data
public class ApplyInfoVO {

    private static final long serialVersionUID=1L;

    /** 加盟商ID */
    @Excel(name = "加盟商ID")
    @FieldDesc("加盟商ID")
    private Long id;

    /** 公司名称 */
    @Excel(name = "公司名称")
    @FieldDesc("公司名称")
    private String companyName;

    /** 所属品牌 */
    @FieldDesc("所属品牌商")
    private String brandName;

}
