package com.xgwc.user.service;

import com.xgwc.user.entity.ReportTargetFranchise;
import com.xgwc.user.entity.ReportTargetFranchiseDept;
import com.xgwc.user.entity.ReportTargetFranchiseStaff;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.ReportTargetFranchiseStaffVo;

import java.util.List;


public interface ReportTargetService {

    /**
     * 插入部门目标
     * @return 是否成功
     */
    int insertReportTargetFranchise(ReportTargetFranchise reportTargetFranchises);

    /**
     * 插入员工目标
     * @return 是否成功
     */
    int addReportTargetStaff(ReportTargetFranchiseStaffVo reportTargetFranchiseStaffVo);

    /**
     * 根据部门目标
     * @param reportTargetFranchiseDept 参数
     * @return 是否成功
     */
    int updateReportTargetFranchiseDept(ReportTargetFranchiseDept reportTargetFranchiseDept);

    /**
     * 根据员工目标
     * @param reportTargetFranchiseStaff 参数
     * @return 是否成功
     */
    int updateReportTargetFranchiseStaff(ReportTargetFranchiseStaff reportTargetFranchiseStaff);

    /**
     * 删除部门目标
     * @param id 目标id
     * @return 是否成功
     */
    int deleteReportTargetFranchise(Long id);

    /**
     * 根据多个加盟商id获取部门目标信息
     * @param franchiseIds 加盟商id列表
     * @return 加盟商目标信息
     */
    List<ReportTargetFranchiseDeptList> selectReportTargetFranchiseDepts(List<Long> franchiseIds, String targetDate);

    /**
     * 加盟商目标详情
     * @param targetId 目标ID
     */
    ReportTargetFranchiseDeptDetail getTargetFranchiseDetail(Long targetId);

    /**
     * 筛选目标列表
     */
    List<ReportTargetFranchiseDto> selectTargetFranchiseList(ReportTargetFranchise reportTargetFranchises);

    /**
     * 筛选员工目标列表
     */
    List<ReportTargetBrandDeptDto> selectTargetFranchiseDeptList(ReportTargetFranchiseDto reportTargetFranchiseDto);

    /**
     * 筛选员工目标列表
     */
    List<ReportTargetBrandDeptDto> selectTargetFranchiseDeptListByBrand(ReportTargetFranchiseDto reportTargetFranchiseDto);

    /**
     * 加盟商筛选员工目标列表
     */
    List<ReportTargetBrandDeptDto> selectTargetFranchiseDeptListByFranchise(ReportTargetFranchiseDeptDto reportTargetFranchiseDeptDto);

    /**
     * 获取部门员工列表
     */
    ReportTargetFranchiseStaffList getReportTargetFranchiseStaffList(Long targetId, Long deptId);


    /**
     * 获取目标状态
     */
    Integer countNotSubmit();
}
