package com.xgwc.user.entity.param;

import lombok.Data;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.param
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-24  10:01
 */

/**
 * 组织管理-部门管理参数
 */
@Data
public class XgwcBrandDeptParam {

    /**
     * 品牌名称
     */
    private String deptName;

    /** 归属公司id */
    private Integer companyId;

    /**
     * 下拉框公司id
     */
    private Integer companyIds;

    /**
     * 状态 0-正常 1-禁用
     */
    private Integer status;

    /**
     * 品牌商id
     */
    private Long brandOwnerId;
}
