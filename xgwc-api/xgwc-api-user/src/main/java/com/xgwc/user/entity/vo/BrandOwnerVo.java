package com.xgwc.user.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class BrandOwnerVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌ID")
    private Long brandId;

    @FieldDesc("母公司名称")
    @NotNull(message = "母公司名称不能为空")
    private String companyName;

    @FieldDesc("联系人")
    @NotNull(message = "联系人不能为空")
    private String contact;

    @FieldDesc("管理员手机号")
    @NotNull(message = "管理员手机号不能为空")
    private String managerPhone;

    @FieldDesc("密码")
    @NotNull(message = "密码不能为空")
    private String password;

    @FieldDesc("用户id")
    private Long userId;

    private Integer status;

}
