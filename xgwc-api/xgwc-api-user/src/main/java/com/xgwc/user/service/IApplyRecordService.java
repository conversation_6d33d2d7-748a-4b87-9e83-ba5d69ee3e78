package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.ApplyRecord;
import com.xgwc.user.entity.vo.ApplyRecordVo;
import com.xgwc.user.entity.dto.ApplyRecordDto;
import com.xgwc.user.entity.vo.ApplyRecordQueryVo;

public interface IApplyRecordService  {
    /**
     * 查询申请记录
     * 
     * @param id 申请记录主键
     * @return 申请记录
     */
    public ApplyRecordDto selectApplyRecordById(Long id);

    /**
     * 查询申请记录列表
     * 
     * @param applyRecord 申请记录
     * @return 申请记录集合
     */
    public List<ApplyRecordDto> selectApplyRecordList(ApplyRecordQueryVo applyRecord);

    /**
     * 新增申请记录
     * 
     * @param applyRecord 申请记录
     * @return 结果
     */
    public int insertApplyRecord(ApplyRecordVo applyRecord);

    /**
     * 修改申请记录
     * 
     * @param applyRecord 申请记录
     * @return 结果
     */
    public int updateApplyRecord(ApplyRecordVo applyRecord);

    /**
     * 批量删除申请记录
     * 
     * @param ids 需要删除的申请记录主键集合
     * @return 结果
     */
    public int deleteApplyRecordByIds(Long[] ids);

    /**
     * 删除申请记录信息
     * 
     * @param id 申请记录主键
     * @return 结果
     */
    public int deleteApplyRecordById(Long id);

    ApplyRecordDto selectApplyRecordByUserId(Long userId, Long brandId, Long businessId);

    int updateApplyRecordStatusById(Long id, Integer status);

    /**
     * 根据用户ID和业务类型查询申请记录列表
     * @param userId 用户ID
     * @param businessType 业务类型：1:加盟商记录，2:设计师记录
     * @return 申请记录列表
     */
    List<ApplyRecordDto> selectApplyRecordListByUserIdAndBusinessType(Long userId, Integer businessType);
}
