package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.constants.ModelTypeConstant;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.security.mnodel.SysUserDetails;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.PasswordUtil;
import com.xgwc.common.util.RequestContextHolder;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.redis.constants.Expire;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.serviceProvider.feign.api.ServiceStaffFeign;
import com.xgwc.serviceProvider.feign.entity.BindStaffDto;
import com.xgwc.serviceProvider.feign.entity.FeignMarketStaffDto;
import com.xgwc.serviceProvider.feign.entity.FeignServiceStaffDto;
import com.xgwc.user.dao.BrandOwnerMapper;
import com.xgwc.user.dao.FranchiseMapper;
import com.xgwc.user.dao.FranchiseRoleMapper;
import com.xgwc.user.dao.SysCompanyMapper;
import com.xgwc.user.dao.SysRoleMapper;
import com.xgwc.user.dao.SysUserMiddleMapper;
import com.xgwc.user.dao.UserMapper;
import com.xgwc.user.dao.XgwcBrandRoleMapper;
import com.xgwc.user.entity.BrandRoleMenu;
import com.xgwc.user.entity.SysCompany;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.BrandRegisterVo;
import com.xgwc.user.entity.vo.LoginVo;
import com.xgwc.user.entity.vo.RegisterVo;
import com.xgwc.user.entity.vo.SmsLoginVO;
import com.xgwc.user.entity.vo.StaffBindVo;
import com.xgwc.user.entity.vo.StaffRegisterVo;
import com.xgwc.user.entity.vo.SysRoleMenuVo;
import com.xgwc.user.entity.vo.SysUserMiddleVo;
import com.xgwc.user.security.config.SecurityProperties;
import com.xgwc.user.security.constants.SecurityConstants;
import com.xgwc.user.service.IFranchiseStaffService;
import com.xgwc.user.service.IServiceOwnerService;
import com.xgwc.user.service.IStaffService;
import com.xgwc.user.service.SmsService;
import com.xgwc.user.service.SysUserService;
import com.xgwc.user.util.AutoIdUtil;
import com.xingyuv.captcha.model.common.ResponseModel;
import com.xingyuv.captcha.model.vo.CaptchaVO;
import com.xingyuv.captcha.service.CaptchaService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl implements SysUserService {

    private final SecurityProperties securityProperties;
    private final AuthenticationManager authenticationManager;

    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private CaptchaService captchaService;
    @Resource
    private UserMapper userMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private SmsService smsService;

    @Resource
    private IStaffService staffService;

    @Resource
    private XgwcBrandRoleMapper xgwcBrandRoleMapper;

    @Resource
    private BrandOwnerMapper brandOwnerMapper;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private IFranchiseStaffService franchiseStaffService;
    @Resource
    private SysCompanyMapper sysCompanyMapper;

    @Resource
    private SysUserMiddleMapper sysUserMiddleMapper;

    @Resource
    private FranchiseRoleMapper franchiseRoleMapper;

    @Resource
    private FranchiseMapper franchiseMapper;

    @Resource
    private ServiceStaffFeign serviceStaffFeign;

    @Resource
    private IServiceOwnerService serviceOwnerService;

    @Override
    public LoginResult login(String username, String password) {
//        // 校验验证码
//        //validateCaptcha(code);
//        LoginResult res = new LoginResult();
//        String accessToken;
//        // 密码需要客户端加密后传递
//        try {
//            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
//            Authentication authenticate = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(username, password));
//            SecurityContextHolder.getContext().setAuthentication(authenticate);
//            // 认证成功后生成JWT令牌
//           // accessToken = createToken(authenticate.getAuthorities().);
//
//          //  res.setToken(accessToken);
//            res.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
//            return res;
//        } catch (Exception e) {
//            log.error("登录异常:{}", e.getMessage());
//            throw new ApiException(e.getMessage());
//        }
        // 校验验证码
        //validateCaptcha(code);
        SysUser user = userMapper.getUserInfoByUserName(username);
        if(user == null){
            throw new ApiException(ApiStatusEnums.STAFF_NAME_EXISTS.getMessage() ,ApiStatusEnums.STAFF_NAME_EXISTS.getStatus());
        }
        //验证密码是否一样
        if(!passwordEncoder.matches(password,user.getPassword())){
            throw new ApiException(ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getMessage() ,ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getStatus());
        }
        Assert.notNull(user, "获取用户失败，结果为空");
        LoginResult loginResult = new LoginResult();
        List<SysUserMiddleDto> sysUserMiddleDtos = sysUserMiddleMapper.selectSysUserMiddleListByUserId(user.getMainUserId());
        Long userId = null;
        if(sysUserMiddleDtos != null && !sysUserMiddleDtos.isEmpty()){
            userId = sysUserMiddleDtos.get(0).getUserId();
            loginResult.setUserId(userId);
        }
        loginResult.setToken(createToken(userId, user.getMainUserId()));
        loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
        loginResult.setUserType(user.getUserType());
        loginResult.setMainUserId(user.getMainUserId());
        RequestContextHolder.setContext(JSONObject.toJSONString(user));
        return loginResult;
    }

    @Override
    public LoginResult loginPhone(LoginVo loginVo) {
        // 校验验证码
        //validateCaptcha(code);
        // 获取并加密手机号
        String phone = ParamDecryptUtil.encrypt(loginVo.getPhone(), ParamDecryptUtil.PHONE_KEY);
        // 查询用户
        SysUser user = userMapper.findUserByPhone(phone);
        if (user == null) {
            throw new ApiException(ApiStatusEnums.STAFF_NAME_EXISTS.getMessage(), ApiStatusEnums.STAFF_NAME_EXISTS.getStatus());
        }
        // 验证密码
        if (!passwordEncoder.matches(loginVo.getPassword(), user.getPassword())) {
            throw new ApiException(ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getMessage(), ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getStatus());
        }
        List<SysUserMiddleDto> userMiddleList = sysUserMiddleMapper.selectSysUserMiddleListByUserId(user.getMainUserId());
        // 处理注册类型校验逻辑（品牌商 / 服务商）
        handleRegisterTypeValidation(loginVo, userMiddleList);
        SysUserMiddleDto sysUserMiddleDto = resolveLoginUserId(loginVo, userMiddleList);
        // 构造登录结果
        Long userId = null;
        Integer userType = null;
        Long mainUserId = null;
        if(sysUserMiddleDto != null){
            userId = sysUserMiddleDto.getUserId();
            userType = sysUserMiddleDto.getUserType();
            mainUserId = sysUserMiddleDto.getMainUserId();
        }else {
            userType = user.getUserType();
            mainUserId = user.getMainUserId();
        }
        LoginResult loginResult = new LoginResult();
        loginResult.setUserId(userId);
        loginResult.setToken(createToken(userId, mainUserId));
        loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
        loginResult.setUserType(userType);
        loginResult.setMainUserId(mainUserId);
        // 设置上下文
        RequestContextHolder.setContext(JSONObject.toJSONString(user));
        return loginResult;
    }

    /**
     * 解析当前登录使用的 userId
     */
    private SysUserMiddleDto resolveLoginUserId(LoginVo loginVo, List<SysUserMiddleDto> middleList) {
        if (middleList == null || middleList.isEmpty()) return null;
        if (StringUtils.isEmpty(loginVo.getRegisterType())) {
            if (middleList.size() == 1) {
                return middleList.get(0);
            } else if (loginVo.getUserType() != null) {
                SysUserMiddleDto userMiddle;
                if(loginVo.getUserType() == 3){
                    userMiddle = sysUserMiddleMapper.selectSysUserMiddleByUserIdAndUserType(middleList.get(0).getMainUserId(), loginVo.getUserType());
                } else {
                    userMiddle = sysUserMiddleMapper.findSysUserMiddleByUserIdAndUserTypeAndSourceId(middleList.get(0).getMainUserId(), loginVo.getUserType(), loginVo.getSourceId());
                }
                return userMiddle;
            }
        }
        return null;
    }

    /**
     * 校验用户是否已注册品牌商/服务商
     */
    private void handleRegisterTypeValidation(LoginVo loginVo, List<SysUserMiddleDto> middleList) {
        if (StringUtils.isEmpty(loginVo.getRegisterType()) || middleList == null) return;
        int targetUserType = 0;
        String msg = "";
        if ("vendorApply".equals(loginVo.getRegisterType())) {
            targetUserType = 1;
            msg = "您已注册过品牌商！";
        } else if ("serviceApply".equals(loginVo.getRegisterType())) {
            targetUserType = 6;
            msg = "您已注册过服务商！";
        }
        if (targetUserType > 0) {
            for (SysUserMiddleDto dto : middleList) {
                if (dto.getUserType() == targetUserType) {
                    throw new ApiException(msg);
                }
            }
        }
    }


    private Map<String, Object> buildJwtTokenParam(Long userId){
        Map<String, Object> param = new HashMap<>();
        param.put(JWTPayload.SUBJECT, userId);
        return param;
    }

    @Override
    public UserInfoResult getInfo() {
        SysUserDetails sysUserDetails = SecurityUtils.getSysUserDetails();
        SysUser user = sysUserDetails.getSysUser();
        UserInfoResult result = new UserInfoResult();
        result.setUsername(user.getUserName());
        result.setPermissions(sysUserDetails.getPermissions());
        result.setRoles(sysUserDetails.getRoles());
        result.setUserInfo(BeanUtil.copyProperties(user, SysUserInfo.class));
        return result;
    }

    @Override
    public LoginResult smsLogin(SmsLoginVO reqVO) {
        //校验验证码是否正确
        boolean validResult = smsService.validLoginCode(reqVO.getPhone(), reqVO.getCode());
        if(!validResult){
            throw new ApiException(ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getMessage() ,ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getStatus());
        }
        String phone = ParamDecryptUtil.encrypt(reqVO.getPhone(), ParamDecryptUtil.PHONE_KEY);
        SysUser user = userMapper.findUserByPhone(phone);
        Assert.notNull(user, "获取用户失败，结果为空");
        LoginResult loginResult = new LoginResult();
        List<SysUserMiddleDto> sysUserMiddleDtos = sysUserMiddleMapper.selectSysUserMiddleListByUserId(user.getUserId());
        Long userId = null;
        if(sysUserMiddleDtos != null && !sysUserMiddleDtos.isEmpty()){
            if(sysUserMiddleDtos.size() == 1){
                userId = sysUserMiddleDtos.get(0).getUserId();
                loginResult.setUserId(userId);
            }
        }
        loginResult.setToken(createToken(userId, user.getMainUserId()));
        loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
        loginResult.setUserType(user.getUserType());
        loginResult.setMainUserId(user.getMainUserId());
        RequestContextHolder.setContext(JSONObject.toJSONString(user));
        return loginResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addSysUser(SysUserRegisterDTO createReqVO) {
        // 校验手机号唯一
        SysUser sysUser = userMapper.findUserByPhone(createReqVO.getPhone());
        if(sysUser != null){
            throw new ApiException(ApiStatusEnums.USER_MOBILE_EXISTS.getMessage(),ApiStatusEnums.USER_MOBILE_EXISTS.getStatus());
        }
        // 校验用户名唯一
        if(userMapper.getUserInfoByUserName(createReqVO.getUserName()) != null){
            throw new ApiException(ApiStatusEnums.ADMIN_NAME_EXISTS.getMessage() ,ApiStatusEnums.ADMIN_NAME_EXISTS.getStatus());
        }
        // 校验密码符合规则
        if(!PasswordUtil.validate(createReqVO.getPassword())){
            throw new ApiException(ApiStatusEnums.USER_PASSWORD_FAILED.getMessage() ,ApiStatusEnums.USER_PASSWORD_FAILED.getStatus());
        }
        SysUser user = new SysUser();
        user.setMainUserId(AutoIdUtil.getId());
        user.setUserName(createReqVO.getUserName());
        user.setPassword(passwordEncoder.encode(createReqVO.getPassword()));
        user.setPhone(createReqVO.getPhone());
        user.setCreateTime(new Date());
        user.setUserType(createReqVO.getUserType());
        user.setBrandId(createReqVO.getSourceId());
        userMapper.save(user);
        return user.getMainUserId();
    }

    @Override
    public Long addSysUser2(SysUserRegisterDTO createReqVO) {
        // 校验手机号唯一
        SysUser sysUser = userMapper.findUserByPhone(createReqVO.getPhone());
        if(sysUser == null){
            if(userMapper.getUserInfoByUserName(createReqVO.getUserName()) != null){
                throw new ApiException(ApiStatusEnums.ADMIN_NAME_EXISTS.getMessage() ,ApiStatusEnums.ADMIN_NAME_EXISTS.getStatus());
            }
            // 校验密码符合规则
            if(!PasswordUtil.validate(createReqVO.getPassword())){
                throw new ApiException(ApiStatusEnums.USER_PASSWORD_FAILED.getMessage() ,ApiStatusEnums.USER_PASSWORD_FAILED.getStatus());
            }
            SysUser user = new SysUser();
            user.setMainUserId(AutoIdUtil.getId());
            user.setUserName(createReqVO.getUserName());
            user.setPassword(passwordEncoder.encode(createReqVO.getPassword()));
            user.setPhone(createReqVO.getPhone());
            user.setCreateTime(new Date());
            user.setUserType(createReqVO.getUserType());
            user.setBrandId(createReqVO.getBrandId());
            userMapper.save(user);
            sysUser = user;
        }
        sysUser.setUserType(createReqVO.getUserType());
        return insertSysUserMiddle(sysUser, createReqVO.getSourceId());
    }

    /**
     * 插入中间用户
     */
    @Override
    public Long insertSysUserMiddle(SysUser sysUser, Long sourceId){
        SysUserMiddleVo sysUserMiddleVo = new SysUserMiddleVo();
        sysUserMiddleVo.setId(AutoIdUtil.getId());
        sysUserMiddleVo.setMainUserId(sysUser.getMainUserId());
        sysUserMiddleVo.setUserType(sysUser.getUserType());
        sysUserMiddleVo.setSourceId(sourceId);
        List<SysUserMiddleDto> sysUserMiddleDtoList = sysUserMiddleMapper.selectSysUserMiddleByCondition(sysUserMiddleVo);
        if(sysUserMiddleDtoList != null && !sysUserMiddleDtoList.isEmpty()){
            if(sysUser.getUserType() == 3){
                return sysUserMiddleDtoList.get(0).getUserId();
            }
            throw new ApiException(ApiStatusEnums.ROLE_NAME_EXISTS.getMessage() ,ApiStatusEnums.ROLE_NAME_EXISTS.getStatus());
        }
        if(sysUser.getUserType() == 3){
            sysUserMiddleVo.setSourceId(null);
        }
        sysUserMiddleMapper.insertSysUserMiddle(sysUserMiddleVo);
        return sysUserMiddleVo.getId();
    }

    @Override
    public int register(RegisterVo reqVO) {
        // 校验手机号唯一
        String encryptedPhone = ParamDecryptUtil.encrypt(reqVO.getManagerPhone(), ParamDecryptUtil.PHONE_KEY);
        SysUser sysUser = userMapper.findUserByPhone(encryptedPhone);
        if(sysUser != null){
            throw new ApiException(ApiStatusEnums.USER_MOBILE_EXISTS.getMessage(),ApiStatusEnums.USER_MOBILE_EXISTS.getStatus());
        }
        //校验验证码
//        boolean validResult = smsService.validRegisterCode(reqVO.getManagerPhone(), reqVO.getCode());
//        if(!validResult){
//            throw new ApiException(ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getMessage() ,ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getStatus());
//        }
        // 校验密码是否一致
        if(!reqVO.getPassword().equals(reqVO.getConfirmPassword())){
            throw new ApiException(ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getMessage() ,ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getStatus());
        }
        // 校验密码符合规则
        if(!PasswordUtil.validate(reqVO.getPassword())){
            throw new ApiException(ApiStatusEnums.USER_PASSWORD_FAILED.getMessage() ,ApiStatusEnums.USER_PASSWORD_FAILED.getStatus());
        }
        SysUser user = new SysUser();
        user.setMainUserId(AutoIdUtil.getId());
        user.setPassword(passwordEncoder.encode(reqVO.getPassword()));
        user.setPhone(encryptedPhone);
        user.setCreateTime(new Date());
        return userMapper.save(user);
    }

    @Override
    public List<SysUserMiddleDto> check(LoginVo loginVo) {
        String phone = ParamDecryptUtil.encrypt(loginVo.getPhone(), ParamDecryptUtil.PHONE_KEY);
        SysUser user = userMapper.findUserByPhone(phone);
        if(user == null){
            throw new ApiException(ApiStatusEnums.STAFF_NAME_EXISTS.getMessage() ,ApiStatusEnums.STAFF_NAME_EXISTS.getStatus());
        }
        //验证密码是否一样
        if(!passwordEncoder.matches(loginVo.getPassword(),user.getPassword())){
            throw new ApiException(ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getMessage() ,ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getStatus());
        }
        Assert.notNull(user, "获取用户失败，结果为空");
        List<SysUserMiddleDto> sysUserMiddleDtos = sysUserMiddleMapper.selectSysUserMiddleListByUserId(user.getMainUserId());
        if(sysUserMiddleDtos != null && !sysUserMiddleDtos.isEmpty()) {
            if (sysUserMiddleDtos.size() > 1) {
                sysUserMiddleDtos.forEach(s -> {
                    if (s.getUserType() == 1) {
                        s.setCompanyName("品牌商后台");
                    } else if (s.getUserType() == 2) {
                        s.setCompanyName("加盟商后台");
                    } else if (s.getUserType() == 3) {
                        s.setCompanyName("设计师后台");
                    } else if (s.getUserType() == 4) {
                        BrandOwnerDto brandOwnerDto = brandOwnerMapper.selectBrandOwnerByBrandId(s.getSourceId());
                        s.setCompanyName(brandOwnerDto.getCompanyName() + "员工后台");
                    } else if (s.getUserType() == 5) {
                        FranchiseDto franchise = franchiseMapper.findFranchiseById(s.getSourceId());
                        FranchiseStaffDto franchiseStaffDto = franchiseStaffService.selectFranchiseStaffByBindUserId(s.getUserId());
                        if(franchiseStaffDto.getStaffType() == 0){
                            s.setCompanyName(franchise.getFranchiseName() + "员工后台");
                        }else if(franchiseStaffDto.getStaffType() == 1 && franchiseStaffDto.getStatus() == 2){

                        }
                    } else if (s.getUserType() == 6) {
                        s.setCompanyName("财务服务商后台");
                    } else if (s.getUserType() == 7) {
                        ServiceOwnerDto serviceOwnerDto = serviceOwnerService.selectServiceOwnerById(s.getSourceId());
                        s.setCompanyName(serviceOwnerDto.getCompanyName() + "员工后台");
                    } else if (s.getUserType() == 8) {
                        s.setCompanyName("销售服务商后台");
                    } else if (s.getUserType() == 9) {
                        ServiceOwnerDto serviceOwnerDto = serviceOwnerService.selectServiceOwnerById(s.getSourceId());
                        s.setCompanyName(serviceOwnerDto.getCompanyName() + "员工后台");
                    }
                });
                return sysUserMiddleDtos;
            }
        }
        return List.of();
    }

    @Override
    public void updateSysUserMiddleSourceId(Long userId, Long sourceId) {
        sysUserMiddleMapper.updateSysUserMiddleSourceId(userId, sourceId);
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public LoginResult brandRegister(BrandRegisterVo reqVO) {
//        // 校验手机号唯一
//        String encryptedPhone = ParamDecryptUtil.encrypt(reqVO.getManagerPhone(), ParamDecryptUtil.PHONE_KEY);
//        SysUser sysUser = userMapper.findUserByPhone(encryptedPhone);
//        if(sysUser == null){
//            // 校验用户名唯一
//            if(userMapper.getUserInfoByUserName(reqVO.getContact()) != null){
//                throw new ApiException(ApiStatusEnums.USER_NAME_EXISTS.getMessage() ,ApiStatusEnums.USER_NAME_EXISTS.getStatus());
//            }
//            //校验验证码
//            boolean validResult = smsService.validRegisterCode(reqVO.getManagerPhone(), reqVO.getCode());
//            if(!validResult){
//                throw new ApiException(ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getMessage() ,ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getStatus());
//            }
//            // 校验密码是否一致
//            if(!reqVO.getPassword().equals(reqVO.getConfirmPassword())){
//                throw new ApiException(ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getMessage() ,ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getStatus());
//            }
//            // 校验密码符合规则
//            if(!PasswordUtil.validate(reqVO.getPassword())){
//                throw new ApiException(ApiStatusEnums.USER_PASSWORD_FAILED.getMessage() ,ApiStatusEnums.USER_PASSWORD_FAILED.getStatus());
//            }
//            SysUser user = new SysUser();
//            user.setMainUserId(AutoIdUtil.getId());
//            user.setUserName(reqVO.getContact());
//            user.setPassword(passwordEncoder.encode(reqVO.getPassword()));
//            user.setPhone(encryptedPhone);
//            user.setCreateTime(new Date());
//            user.setUserType(1);
//            // 保存用户信息
//            userMapper.save(user);
//            sysUser = user;
//        }
//        long userId = insertSysUserMiddle(sysUser, null);
//        // 添加品牌商
//        long brandId = addBrand(reqVO, encryptedPhone, userId);
//        updateSysUserMiddleSourceId(userId, brandId);
//        // todo 初始化角色  (没有审核，暂时先写这里)
//        Long roleId = this.initRole(userId, ModelTypeConstant.BRAND_ADMIN, reqVO.getCompanyName(), brandId, 1);
//        // 添加母公司信息
//        Long companyId = addCompany(reqVO, brandId);
//        // 员工表添加数据
//        addStaff(roleId, companyId, brandId,reqVO.getContact(),userId,encryptedPhone);
//        return new LoginResult();
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResult StaffRegister(StaffRegisterVo reqVO) {
        int userType = 0;
        StaffDto staffDto = new StaffDto();
        FranchiseStaffDto franchiseStaffDto =new FranchiseStaffDto();
        FeignServiceStaffDto serviceStaffDto =new FeignServiceStaffDto();
        FeignMarketStaffDto marketStaffDto =new FeignMarketStaffDto();
        Long sourceId = 0L;
        if(userMapper.findUserByStageName(reqVO.getStageName()) != null){
            throw new ApiException(ApiStatusEnums.STAFF_STAGE_NAME_EXISTS.getMessage() ,ApiStatusEnums.STAFF_STAGE_NAME_EXISTS.getStatus());
        }
        // 校验员工姓名
        if(reqVO.getType() == 1){
            userType = 4;
            staffDto = staffService.selectStaffById(reqVO.getId());
            if(staffDto == null){
                throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage() ,ApiStatusEnums.NOT_EXISTS.getStatus());
            }
            if(staffService.selectStaffByNameAndBrandId(reqVO.getName(), reqVO.getBusinessId()) == null){
                throw new ApiException(ApiStatusEnums.STAFF_NAME_NOT_EXISTS.getMessage() ,ApiStatusEnums.STAFF_NAME_NOT_EXISTS.getStatus());
            }
            sourceId = staffDto.getBrandId();
        }else if(reqVO.getType() == 2){
            userType = 5;
            franchiseStaffDto = franchiseStaffService.selectFranchiseStaffById(reqVO.getId());
            if(franchiseStaffDto == null){
                throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage() ,ApiStatusEnums.NOT_EXISTS.getStatus());
            }
            if(franchiseStaffService.selectFranchiseStaffByNameAndFranchiseId(reqVO.getName(), reqVO.getBusinessId()) == null){
                throw new ApiException(ApiStatusEnums.STAFF_NAME_NOT_EXISTS.getMessage() ,ApiStatusEnums.STAFF_NAME_NOT_EXISTS.getStatus());
            }
            sourceId = franchiseStaffDto.getFranchiseId();
        }else if(reqVO.getType() == 3){
            userType = 7;
            ApiResult<FeignServiceStaffDto> result = serviceStaffFeign.getStaffInfo(reqVO.getId());
            serviceStaffDto = result.getData();
            if(serviceStaffDto == null){
                throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage() ,ApiStatusEnums.NOT_EXISTS.getStatus());
            }
            if(serviceStaffFeign.selectServiceStaffByNameAndServiceId(reqVO.getName(), reqVO.getBusinessId())){
                throw new ApiException(ApiStatusEnums.STAFF_NAME_NOT_EXISTS.getMessage() ,ApiStatusEnums.STAFF_NAME_NOT_EXISTS.getStatus());
            }
            sourceId = serviceStaffDto.getServiceOwnerId();
        }else if(reqVO.getType() == 4){
            userType = 9;
            ApiResult<FeignMarketStaffDto> result = serviceStaffFeign.getMarketStaffInfo(reqVO.getId());
            marketStaffDto = result.getData();
            if(marketStaffDto == null){
                throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage() ,ApiStatusEnums.NOT_EXISTS.getStatus());
            }
            if(serviceStaffFeign.selectMarketStaffByNameAndServiceId(reqVO.getName(), reqVO.getBusinessId())){
                throw new ApiException(ApiStatusEnums.STAFF_NAME_NOT_EXISTS.getMessage() ,ApiStatusEnums.STAFF_NAME_NOT_EXISTS.getStatus());
            }
            sourceId = marketStaffDto.getServiceOwnerId();
        }
        // 校验手机号唯一
        String encryptedPhone = ParamDecryptUtil.encrypt(reqVO.getPhone(), ParamDecryptUtil.PHONE_KEY);
        SysUser sysUser = userMapper.findUserByPhone(encryptedPhone);
        // 添加用户
        if(sysUser == null){
            sysUser = addUser(reqVO, encryptedPhone, userType);
        }else {
            throw new ApiException(ApiStatusEnums.USER_MOBILE_EXISTS.getMessage(),ApiStatusEnums.USER_MOBILE_EXISTS.getStatus());
        }
        sysUser.setUserType(userType);
        long userId = insertSysUserMiddle(sysUser, sourceId);
        if(reqVO.getType() == 1){
            staffService.updateBindStatus(reqVO.getId(), userId,1, sysUser.getPhone(), reqVO.getStageName());
            if(StringUtils.isNotEmpty(staffDto.getRoleIds())){
                addRoleUser(staffDto.getRoleIds(), userId,0);
            }
        }else if(reqVO.getType() == 2){
            franchiseStaffService.updateBindStatus(reqVO.getId(), userId,1,sysUser.getPhone(), reqVO.getStageName());
            if(StringUtils.isNotEmpty(franchiseStaffDto.getRoleIds())){
                addRoleUser(franchiseStaffDto.getRoleIds(), userId,1);
            }
        }else if(reqVO.getType() == 3){
            BindStaffDto bindStaffDto =new BindStaffDto();
            bindStaffDto.setId(reqVO.getId());
            bindStaffDto.setBindUserId(userId);
            bindStaffDto.setBindStatus(1);
            bindStaffDto.setStageName(reqVO.getStageName());
            bindStaffDto.setLoginPhone(sysUser.getPhone());
            serviceStaffFeign.updateServiceStaffBindStatus(bindStaffDto);
            if(StringUtils.isNotEmpty(serviceStaffDto.getRoleIds())){
                addRoleUser(serviceStaffDto.getRoleIds(), userId,2);
            }
        }else if(reqVO.getType() == 4){
            BindStaffDto bindStaffDto =new BindStaffDto();
            bindStaffDto.setId(reqVO.getId());
            bindStaffDto.setBindUserId(userId);
            bindStaffDto.setBindStatus(1);
            bindStaffDto.setStageName(reqVO.getStageName());
            bindStaffDto.setLoginPhone(sysUser.getPhone());
            serviceStaffFeign.updateMarketStaffBindStatus(bindStaffDto);
            if(StringUtils.isNotEmpty(marketStaffDto.getRoleIds())){
                addRoleUser(marketStaffDto.getRoleIds(), userId,3);
            }
        }
        LoginResult loginResult = new LoginResult();
        loginResult.setToken(createToken(userId, sysUser.getMainUserId()));
        loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
        RequestContextHolder.setContext(JSONObject.toJSONString(sysUser));
        return loginResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResult bindStaff(StaffBindVo reqVO) {
        // 情况一：无 mainUserId，有 userId，表示是员工已有账号，直接绑定
        if (reqVO.getMainUserId() == null && reqVO.getUserId() != null) {
            return bindWithUserId(reqVO);
        }
        // 情况二：有 mainUserId，表示主账号注册绑定员工
        return bindWithMainUserId(reqVO);
    }

    SysUser addUser(StaffRegisterVo reqVO,String phone,Integer userType) {
        //校验验证码
        boolean validResult = smsService.validRegisterCode(reqVO.getPhone(), reqVO.getCode());
        if(!validResult){
            throw new ApiException(ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getMessage() ,ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getStatus());
        }
        // 校验密码是否一致
        if(!reqVO.getPassword().equals(reqVO.getConfirmPassword())){
            throw new ApiException(ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getMessage() ,ApiStatusEnums.USER_PASSWORD_NOT_MATCH.getStatus());
        }
        // 校验密码符合规则
        if(!PasswordUtil.validate(reqVO.getPassword())){
            throw new ApiException(ApiStatusEnums.USER_PASSWORD_FAILED.getMessage() ,ApiStatusEnums.USER_PASSWORD_FAILED.getStatus());
        }
        // 校验用户名是否存在
        if(userMapper.getUserInfoByUserName(reqVO.getName()) != null){
            throw new ApiException(ApiStatusEnums.USER_NAME_EXISTS.getMessage() ,ApiStatusEnums.USER_NAME_EXISTS.getStatus());
        }
        SysUser user = new SysUser();
        user.setMainUserId(AutoIdUtil.getId());
        user.setUserName(reqVO.getName());
        user.setPassword(passwordEncoder.encode(reqVO.getPassword()));
        user.setPhone(phone);
        user.setCreateTime(new Date());
        user.setUserType(userType);
        user.setBrandId(reqVO.getBusinessId());
        user.setStageName(reqVO.getStageName());
        userMapper.save(user);
        return user;
    }

    /**
     * 员工通过 userId 绑定
     */
    private LoginResult bindWithUserId(StaffBindVo reqVO) {
        SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleById(reqVO.getUserId());
        Assert.notNull(sysUserMiddleDto, "获取用户失败，结果为空");
        if(StringUtils.isEmpty(sysUserMiddleDto.getStageName())){
            if(userMapper.findUserByStageName(reqVO.getStageName()) != null){
                throw new ApiException(ApiStatusEnums.STAFF_STAGE_NAME_EXISTS.getMessage() ,ApiStatusEnums.STAFF_STAGE_NAME_EXISTS.getStatus());
            }
        }
        int userType = sysUserMiddleDto.getUserType();
        switch (userType) {
            case 4:
                return bindBrandStaff(reqVO, sysUserMiddleDto);
            case 5:
                return bindFranchiseStaff(reqVO, sysUserMiddleDto);
            case 7:
                return bindServiceStaff(reqVO, sysUserMiddleDto);
            case 9:
                return bindMarketStaff(reqVO, sysUserMiddleDto);
            default:
                log.error("未知的用户类型:{}", userType);
        }
        return null;
    }

    private LoginResult bindMarketStaff(StaffBindVo reqVO, SysUserMiddleDto middleDto) {
        ApiResult<FeignMarketStaffDto> result = serviceStaffFeign.getMarketStaffInfo(reqVO.getId());
        if(result.getMessage().equals("success")){
            FeignMarketStaffDto marketStaffDto = result.getData();
            if(marketStaffDto == null){
                throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
            }
            if(!marketStaffDto.getName().equals(reqVO.getName())){
                throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
            }
            BindStaffDto bindStaffDto =new BindStaffDto();
            bindStaffDto.setId(reqVO.getId());
            bindStaffDto.setBindUserId(reqVO.getUserId());
            bindStaffDto.setBindStatus(1);
            bindStaffDto.setLoginPhone(middleDto.getPhone());
            bindStaffDto.setStageName(reqVO.getStageName());
            serviceStaffFeign.updateServiceStaffBindStatus(bindStaffDto);
            if (StringUtils.isNotEmpty(marketStaffDto.getRoleIds())) {
                updateRoleUser(marketStaffDto.getRoleIds(), reqVO.getUserId(), 3);
            }
        }
        return buildLoginResult(middleDto);
    }

    /**
     * 主账号注册绑定员工
     */
    private LoginResult bindWithMainUserId(StaffBindVo reqVO) {
        SysUser sysUser = userMapper.getUserInfoByUserId(reqVO.getMainUserId());
        if (sysUser == null) {
            throw new ApiException(ApiStatusEnums.AUTH_USER_NOT_EXISTS.getMessage(), ApiStatusEnums.AUTH_USER_NOT_EXISTS.getStatus());
        }
        if(StringUtils.isEmpty(reqVO.getStageName())){
            if(userMapper.findUserByStageName(reqVO.getStageName()) != null){
                throw new ApiException(ApiStatusEnums.STAFF_STAGE_NAME_EXISTS.getMessage() ,ApiStatusEnums.STAFF_STAGE_NAME_EXISTS.getStatus());
            }
        }
        // 判断业务类型：品牌员工（4）加盟商员工（5）财务服务商员工（7）销售服务商员工（9）
        Integer userType = reqVO.getUserType();
        if (userType == null) {
            throw new ApiException("无法识别的员工类型");
        }
        SysUserMiddleDto existingMiddle = sysUserMiddleMapper.selectSysUserMiddleByUserIdAndUserTypeAndSourceId(reqVO.getMainUserId(), userType, reqVO.getBusinessId());
        if (existingMiddle != null) {
            throw new ApiException("该账号已被绑定！");
        }
        SysUserMiddleVo middleVo = new SysUserMiddleVo();
        middleVo.setId(AutoIdUtil.getId());
        middleVo.setMainUserId(reqVO.getMainUserId());
        middleVo.setUserType(userType);
        middleVo.setSourceId(reqVO.getBusinessId());
        sysUserMiddleMapper.insertSysUserMiddle(middleVo);
        switch (userType) {
            case 4:
                bindBrandStaffByMainUser(reqVO, middleVo, sysUser.getPhone());
                break;
            case 5:
                bindFranchiseStaffByMainUser(reqVO, middleVo, sysUser.getPhone());
                break;
            case 7:
                bindServiceStaffByMainUser(reqVO, middleVo, sysUser.getPhone());
                break;
            case 9:
                bindMarketStaffByMainUser(reqVO, middleVo, sysUser.getPhone());
                break;
            default:
                log.error("未知的用户类型:{}", userType);
        }
        LoginResult loginResult = new LoginResult();
        loginResult.setToken(createToken(middleVo.getId(), middleVo.getMainUserId()));
        loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
        return loginResult;
    }

    private void bindMarketStaffByMainUser(StaffBindVo reqVO, SysUserMiddleVo middleVo, String phone) {
        ApiResult<FeignMarketStaffDto> result = serviceStaffFeign.getMarketStaffInfo(reqVO.getId());
        if(result.getMessage().equals("OK")){
            FeignMarketStaffDto marketStaffDto = result.getData();
            if(marketStaffDto == null){
                throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
            }
            if(!marketStaffDto.getName().equals(reqVO.getName())){
                throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
            }
            BindStaffDto bindStaffDto =new BindStaffDto();
            bindStaffDto.setId(marketStaffDto.getId());
            bindStaffDto.setBindUserId(middleVo.getId());
            bindStaffDto.setBindStatus(1);
            bindStaffDto.setLoginPhone(phone);
            bindStaffDto.setStageName(reqVO.getStageName());
            serviceStaffFeign.updateMarketStaffBindStatus(bindStaffDto);
            if (StringUtils.isNotEmpty(marketStaffDto.getRoleIds())) {
                updateRoleUser(marketStaffDto.getRoleIds(), middleVo.getId(), 3);
            }
        }
    }

    // 品牌员工绑定（userId存在）
    private LoginResult bindBrandStaff(StaffBindVo reqVO, SysUserMiddleDto middleDto) {
        StaffDto staffDto = staffService.selectStaffById(reqVO.getId());
        if (staffDto == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        if(!staffDto.getName().equals(reqVO.getName())){
            throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
        }
        staffService.updateBindStatus(staffDto.getId(), reqVO.getUserId(), 1, middleDto.getPhone(),reqVO.getStageName());
        if (StringUtils.isNotEmpty(staffDto.getRoleIds())) {
            updateRoleUser(staffDto.getRoleIds(), reqVO.getUserId(), 0);
        }
        return buildLoginResult(middleDto);
    }

    // 服务商员工绑定（userId存在）
    private LoginResult bindServiceStaff(StaffBindVo reqVO, SysUserMiddleDto middleDto) {
        ApiResult<FeignServiceStaffDto> result = serviceStaffFeign.getStaffInfo(reqVO.getId());
        if(result.getMessage().equals("success")){
            FeignServiceStaffDto serviceStaffDto = result.getData();
            if(serviceStaffDto == null){
                throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
            }
            if(!serviceStaffDto.getName().equals(reqVO.getName())){
                throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
            }
            BindStaffDto bindStaffDto =new BindStaffDto();
            bindStaffDto.setId(reqVO.getId());
            bindStaffDto.setBindUserId(reqVO.getUserId());
            bindStaffDto.setBindStatus(1);
            bindStaffDto.setLoginPhone(middleDto.getPhone());
            bindStaffDto.setStageName(reqVO.getStageName());
            serviceStaffFeign.updateServiceStaffBindStatus(bindStaffDto);
            if (StringUtils.isNotEmpty(serviceStaffDto.getRoleIds())) {
                updateRoleUser(serviceStaffDto.getRoleIds(), reqVO.getUserId(), 2);
            }
        }
        return buildLoginResult(middleDto);
    }

    // 加盟商员工绑定（userId存在）
    private LoginResult bindFranchiseStaff(StaffBindVo reqVO, SysUserMiddleDto middleDto) {
        FranchiseStaffDto staffDto = franchiseStaffService.selectFranchiseStaffById(reqVO.getId());
        if (staffDto == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        if(!staffDto.getName().equals(reqVO.getName())){
            throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
        }
        franchiseStaffService.updateBindStatus(staffDto.getId(), reqVO.getUserId(), 1, middleDto.getPhone(),reqVO.getStageName());
        if (StringUtils.isNotEmpty(staffDto.getRoleIds())) {
            updateRoleUser(staffDto.getRoleIds(), reqVO.getUserId(), 1);
        }

        return buildLoginResult(middleDto);
    }

    // 品牌员工绑定（mainUserId存在）
    private void bindBrandStaffByMainUser(StaffBindVo reqVO, SysUserMiddleVo middleVo, String phone) {
        StaffDto staffDto = staffService.selectStaffById(reqVO.getId());
        if (staffDto == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        if(!staffDto.getName().equals(reqVO.getName())){
            throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
        }
        staffService.updateBindStatus(staffDto.getId(), middleVo.getId(), 1, phone,reqVO.getStageName());
        if (StringUtils.isNotEmpty(staffDto.getRoleIds())) {
            updateRoleUser(staffDto.getRoleIds(), middleVo.getId(), 0);
        }
    }

    // 加盟商员工绑定（mainUserId存在）
    private void bindFranchiseStaffByMainUser(StaffBindVo reqVO, SysUserMiddleVo middleVo, String phone) {
        FranchiseStaffDto staffDto = franchiseStaffService.selectFranchiseStaffById(reqVO.getId());
        if (staffDto == null) {
            throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
        }
        if(!staffDto.getName().equals(reqVO.getName())){
            throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
        }
        franchiseStaffService.updateBindStatus(staffDto.getId(), middleVo.getId(), 1, phone,reqVO.getStageName());
        if (StringUtils.isNotEmpty(staffDto.getRoleIds())) {
            updateRoleUser(staffDto.getRoleIds(), middleVo.getId(), 1);
        }
    }

    // 服务商员工绑定（mainUserId存在）
    private void bindServiceStaffByMainUser(StaffBindVo reqVO, SysUserMiddleVo middleVo, String phone) {
        ApiResult<FeignServiceStaffDto> result = serviceStaffFeign.getStaffInfo(reqVO.getId());
        if(result.getMessage().equals("OK")){
            FeignServiceStaffDto serviceStaffDto = result.getData();
            if(serviceStaffDto == null){
                throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
            }
            if(!serviceStaffDto.getName().equals(reqVO.getName())){
                throw new ApiException(ApiStatusEnums.NAME_NOT_CORRECT.getMessage(), ApiStatusEnums.NAME_NOT_CORRECT.getStatus());
            }
            BindStaffDto bindStaffDto =new BindStaffDto();
            bindStaffDto.setId(serviceStaffDto.getId());
            bindStaffDto.setBindUserId(middleVo.getId());
            bindStaffDto.setBindStatus(1);
            bindStaffDto.setLoginPhone(phone);
            bindStaffDto.setStageName(reqVO.getStageName());
            serviceStaffFeign.updateServiceStaffBindStatus(bindStaffDto);
            if (StringUtils.isNotEmpty(serviceStaffDto.getRoleIds())) {
                updateRoleUser(serviceStaffDto.getRoleIds(), middleVo.getId(), 2);
            }
        }
    }

    /**
     * 根据业务ID判断员工类型（品牌、加盟商、客服等）
     */
    private Integer determineUserType(Long businessId) {
        if (brandOwnerMapper.selectBrandOwnerByBrandId(businessId) != null) {
            return 4; // 品牌员工
        } else if (franchiseMapper.findFranchiseById(businessId) != null) {
            return 5; // 加盟商员工
        } else if (serviceOwnerService.selectServiceOwnerById(businessId) != null) {
            return 7; // 服务商员工
        }
        return null;
    }


    // 登录返回
    private LoginResult buildLoginResult(SysUserMiddleDto middleDto) {
        LoginResult loginResult = new LoginResult();
        loginResult.setToken(createToken(middleDto.getUserId(), middleDto.getMainUserId()));
        loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
        return loginResult;
    }


    /**
     * 创建token
     */
    @Override
    public String createToken(Long userId, Long mainUserId){
        Map<String, Object> payload = buildJwtTokenParam(userId);
        Date expiration = new Date(System.currentTimeMillis() + securityProperties.getJwt().getTtl());
        payload.put(JWTPayload.EXPIRES_AT, expiration);
        payload.put("mainUserId", mainUserId);
        // 生成 JWT Token
        String token =  JWTUtil.createToken(payload, securityProperties.getJwt().getKey().getBytes());
        redisUtil.set("user:login:" + userId + ":token", token,Expire.DAY);
        return token;
    }

//    private long addBrand(BrandRegisterVo reqVO, String phone,Long userId) {
//        BrandOwnerVo brandOwnerVo = new BrandOwnerVo();
//        brandOwnerVo.setCompanyName(reqVO.getCompanyName());
//        brandOwnerVo.setContact(reqVO.getContact());
//        brandOwnerVo.setManagerPhone(phone);
//        brandOwnerVo.setPassword(passwordEncoder.encode(reqVO.getPassword()));
//        brandOwnerVo.setUserId(userId);
//        return brandOwnerService.insertBrandOwner(brandOwnerVo);
//    }

    private Long addCompany(BrandRegisterVo reqVO,Long brandId) {
        SysCompany sysCompany = new SysCompany();
        sysCompany.setPid(0L);
        sysCompany.setBrandOwnerId(brandId);
        sysCompany.setName(reqVO.getCompanyName());
        sysCompany.setCompanyType(0);
        sysCompany.setStatus(0);
        sysCompany.setCreateTime(new Date());
        sysCompany.setCreateBy(reqVO.getContact());
        sysCompanyMapper.insertSysCompany(sysCompany);
        return sysCompany.getId();
    }

    @Override
    public Long initRole(Long userId,String roleCode,String roleName,Long businessId,Integer businessType) {
        //初始化角色
        if(businessType == 1){
            XgwcBrandRoleDto xgwcBrandRoleDto = new XgwcBrandRoleDto();
            xgwcBrandRoleDto.setRoleName(roleName+"管理员");
            xgwcBrandRoleDto.setIsFlag(roleCode);
            xgwcBrandRoleDto.setSort(0);
            xgwcBrandRoleDto.setBrandOwnerId(businessId);
            xgwcBrandRoleMapper.saveXgwcBrandRole(xgwcBrandRoleDto);
            //绑定用户角色
            xgwcBrandRoleMapper.insertBrandRoleUser(xgwcBrandRoleDto.getRoleId(), userId,0);
            List<SysMenuDto> menuList = xgwcBrandRoleMapper.findMenuByModelType(ModelTypeConstant.BRAND_ADMIN);
            List<BrandRoleMenu> brandRoleMenuList = menuList.stream()
                    .map(menuDto -> new BrandRoleMenu(xgwcBrandRoleDto.getRoleId(), menuDto.getId()))
                    .collect(Collectors.toList());
            xgwcBrandRoleMapper.insertBrandRoleMenu(brandRoleMenuList);
            return xgwcBrandRoleDto.getRoleId();
        } else if(businessType == 2){
            FranchiseRoleDto franchiseRoleDto = new FranchiseRoleDto();
            franchiseRoleDto.setRoleName(roleName+"管理员");
            franchiseRoleDto.setIsFlag(roleCode);
            franchiseRoleDto.setSort(0);
            franchiseRoleDto.setFranchiseId(businessId);
            franchiseRoleDto.setCreateBy(roleName);
            franchiseRoleMapper.saveFranchiseRole(franchiseRoleDto);
            //绑定用户角色
            xgwcBrandRoleMapper.insertBrandRoleUser(franchiseRoleDto.getRoleId(), userId,1);
            List<SysMenuDto> menuList = xgwcBrandRoleMapper.findMenuByModelType(ModelTypeConstant.FRANCHISE_ADMIN);
            List<SysRoleMenuVo> roleMenuVos = new ArrayList<>();
            for (SysMenuDto item : menuList) {
                SysRoleMenuVo rm = new SysRoleMenuVo();
                rm.setRoleId(franchiseRoleDto.getRoleId());
                rm.setMenuId(item.getId());
                rm.setCreateBy(roleName);
                roleMenuVos.add(rm);
            }
            franchiseRoleMapper.saveRoleMenu(roleMenuVos);
            return franchiseRoleDto.getRoleId();
        } else if(businessType == 3){ // 设计师初始化绑定角色即可
            XgwcBrandRoleDto xgwcBrandRoleDto = new XgwcBrandRoleDto();
            xgwcBrandRoleDto.setRoleName(roleName+"管理员");
            xgwcBrandRoleDto.setIsFlag(roleCode);
            xgwcBrandRoleDto.setSort(0);
            xgwcBrandRoleMapper.saveXgwcBrandRole(xgwcBrandRoleDto);
            //绑定用户角色
            xgwcBrandRoleMapper.insertBrandRoleUser(xgwcBrandRoleDto.getRoleId(), userId,0);
        }
        return null;
    }


    void validateCaptcha(String code) {
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(code);
        ResponseModel response = captchaService.verification(captchaVO);
        // 验证不通过
        if (!response.isSuccess()) {
            throw new ApiException(ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getMessage() ,ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getStatus());
        }
    }

    void validateMobileUnique(String mobile) {
        SysUser user = userMapper.findUserByPhone(mobile);
        if(user != null){
            throw new ApiException(ApiStatusEnums.USER_MOBILE_EXISTS.getMessage(), ApiStatusEnums.USER_MOBILE_EXISTS.getStatus());
        }
    }

    /**
     * 添加角色用户     */
    void addRoleUser(String roleCode,Long userId,Integer type) {
        String[] roleIds = roleCode.split(",");
        for(String roleId : roleIds){
            xgwcBrandRoleMapper.insertBrandRoleUser(Long.valueOf(roleId), userId,type);
        }
    }

    /**
     * 更新角色用户
     */
    void updateRoleUser(String roleCode,Long userId,Integer type) {
        //删除旧的角色用户信息
        xgwcBrandRoleMapper.deleteBrandRoleUser(userId);
        String[] roleIds = roleCode.split(",");
        for(String roleId : roleIds){
            xgwcBrandRoleMapper.insertBrandRoleUser(Long.valueOf(roleId), userId,type);
        }
    }
}




