package com.xgwc.user.controller;

import java.util.List;

import com.xgwc.common.util.TreeUtils;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xgwc.user.entity.vo.SysMenuVo;
import com.xgwc.user.entity.dto.SysMenuDto;
import com.xgwc.user.entity.vo.SysMenuQueryVo;
import com.xgwc.user.service.ISysMenuService;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/user/SysMenu")
public class SysMenuController extends BaseController {
    @Autowired
    private ISysMenuService sysMenuService;

    /**
     * 查询菜单管理列表
     */
    @MethodDesc("查询菜单管理列表")
    @PreAuthorize("@ss.hasPermission('user:SysMenu:list')")
    @GetMapping("/list")
    public ApiResult<SysMenuDto> list(SysMenuQueryVo sysMenu) {
        startPage();
        List<SysMenuDto> list = sysMenuService.selectSysMenuList(sysMenu);
        return getDataTable(list);
    }

    @MethodDesc("查询菜单管理列表tree")
    @PreAuthorize("@ss.hasPermission('user:SysMenu:list')")
    @GetMapping("/tree")
    public ApiResult<List<SysMenuDto>> tree(SysMenuQueryVo sysMenu) {
        List<SysMenuDto> list = sysMenuService.selectSysMenuList(sysMenu);
        return ApiResult.ok(TreeUtils.listToTree(list,0L));
    }

    /**
     * 导出菜单管理列表
     */
    @MethodDesc("导出菜单管理列表")
    @PreAuthorize("@ss.hasPermission('user:SysMenu:export')")
    @Log(title = "菜单管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysMenuQueryVo sysMenu) {
    }

    /**
     * 获取菜单管理详细信息
     */
    @MethodDesc("获取菜单管理详细信息")
    @PreAuthorize("@ss.hasPermission('user:SysMenu:query')")
    @GetMapping(value = "/{id}")
    public ApiResult<SysMenuDto> getInfo(@PathVariable("id") Long id) {
        return success(sysMenuService.selectSysMenuById(id));
    }

    /**
     * 新增菜单管理
     */
    @MethodDesc("新增菜单管理")
    @PreAuthorize("@ss.hasPermission('user:SysMenu:add')")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody SysMenuVo sysMenu) {
        return toAjax(sysMenuService.insertSysMenu(sysMenu));
    }

    /**
     * 修改菜单管理
     */
    @MethodDesc("修改菜单管理")
    @PreAuthorize("@ss.hasPermission('user:SysMenu:edit')")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody SysMenuVo sysMenu) {
        return toAjax(sysMenuService.updateSysMenu(sysMenu));
    }

    /**
     * 删除菜单管理
     */
    @MethodDesc("删除菜单管理")
    @PreAuthorize("@ss.hasPermission('user:SysMenu:remove')")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(sysMenuService.deleteSysMenuByIds(ids));
    }
}
