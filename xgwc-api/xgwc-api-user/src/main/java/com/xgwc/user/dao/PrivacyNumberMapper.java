package com.xgwc.user.dao;

import com.xgwc.user.entity.dto.PrivacyNumberDto;
import com.xgwc.user.entity.vo.PrivacyNumberCallBackVo;
import com.xgwc.user.entity.vo.PrivacyNumberVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PrivacyNumberMapper {

    /**
     * 根据手机号查询是否有绑定关系
     * @param phoneA 手机号A
     * @param phoneB 手机号B
     * @return 绑定关系
     */
    PrivacyNumberDto selectPrivacyNumber(@Param(value = "photoA") String phoneA, @Param(value = "phoneB") String phoneB);

    /**
     * 插入绑定关系
     * @param privacyNumberVo 参数
     * @return 是否成功
     */
    int insertPrivacyNumber(PrivacyNumberVo privacyNumberVo);

    /**
     * 修改绑定关系
     * @param privacyNumberVo 参数
     * @return 是否成功
     */
    int updateBindStatus(PrivacyNumberVo privacyNumberVo);

    /**
     * 插入回调记录
     * @param privacyNumberCallBackVo 参数
     * @return 是否成功
     */
    int insertCallBackRecord(PrivacyNumberCallBackVo privacyNumberCallBackVo);

    /**
     * 获取所有中间号码
     * @return 返回中间号码
     */
    List<String> getAllMiddleNumberList();
    /**
     * 获取当前有效的绑定关系
     * @return 返回中间号码
     */
    List<String> getBindMiddleNumber(PrivacyNumberVo privacyNumberVo);

    /**
     * 更新绑定数量
     * @param middleNumber 中间号码
     * @param count 数量
     * @return 是否成功
     */
    int updateMiddleNumberCount(@Param(value = "middleNumber") String middleNumber, @Param(value = "count") int count);

    /**
     * 更新数量，每次减一
     * @param middleNumber 中间号码
     * @return 是否成功
     */
    int updateMiddleNumberMinusOne(@Param(value = "middleNumber")String middleNumber);

}
