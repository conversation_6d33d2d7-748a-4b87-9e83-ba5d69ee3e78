package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.entity.FranchiseOwner;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.FranchiseOwnerSimpleVO;
import com.xgwc.user.entity.vo.FranchiseOwnerVO;
import com.xgwc.user.service.IFranchiseOwnerService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 加盟商管理
 */
@RestController
@RequestMapping("/franchiseOwner")
@Validated
public class FranchiseOwnerController extends BaseController {
    @Autowired
    private IFranchiseOwnerService franchiseOwnerService;

    /**
     * 查询加盟商管理列表
     */
    @MethodDesc("加盟商列表")
    @GetMapping("/list")
    public ApiResult list(FranchiseOwnerQueryDto franchiseOwner) {
        startPage();
        List<FranchiseOwnerVO> list = franchiseOwnerService.selectFranchiseOwnerList(franchiseOwner);
        return getDataTable(list);
    }

    /**
     * 获取加盟商管理详细信息
     */
    @MethodDesc("获取加盟商管理详细信息")
//    @PreAuthorize("@ss.hasPermission('user:FranchiseOwner:query')") //注册需要查询 不需要权限
    @GetMapping(value = "/{id}")
    public ApiResult getInfo(@PathVariable("id") Long id) {
        return success(franchiseOwnerService.selectFranchiseOwnerById(id));
    }


    /**
     * 新增加盟商管理
     */
    @MethodDesc("加盟商注册")
    @Log(title = "加盟商管理", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult<LoginResult> add(@RequestBody @Valid FranchiseOwnerDto franchiseOwner) {
        return ApiResult.ok(franchiseOwnerService.insertFranchiseOwner(franchiseOwner));
    }

    /**
     * 修改加盟商管理
     */
    @PreAuthorize("@ss.hasPermission('user:FranchiseOwner:edit')")
    @Log(title = "加盟商管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody FranchiseOwnerDto franchiseOwner) {
        return toAjax(franchiseOwnerService.updateFranchiseOwner(franchiseOwner));
    }

    /**
     * 删除加盟商管理
     */
    @PreAuthorize("@ss.hasPermission('user:Franchise:remove')")
    @Log(title = "加盟商管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public ApiResult remove(@PathVariable Long[] ids) {
        return toAjax(franchiseOwnerService.deleteFranchiseOwnerByIds(ids));
    }

    /**
     * 修改状态
     */
    @MethodDesc("修改状态")
    @PutMapping("/update-status")
    @PreAuthorize("@ss.hasPermission('user:Franchise:editStatus')")
    @Log(title = "加盟商管理", businessType = BusinessType.UPDATE)
    public ApiResult<Boolean> updateCategoryStatus(@RequestBody @Valid FranchiseOwnerStatusUpdateDto updateReqVO) {
        franchiseOwnerService.updateStatus(updateReqVO);
        return success(true);
    }

    /**
     * 审核
     */
    @MethodDesc("审核")
    @PutMapping("/audit")
    @PreAuthorize("@ss.hasPermission('user:Franchise:audit')")
    @Log(title = "加盟商管理", businessType = BusinessType.UPDATE)
    public ApiResult<Boolean> audit(@RequestBody @Valid FranchiseOwnerAuditDto updateReqVO) {
        franchiseOwnerService.audit(updateReqVO);
        return success(true);
    }


    @MethodDesc("授权")
    @PutMapping("/authorization")
    @Log(title = "加盟商管理", businessType = BusinessType.UPDATE)
    public ApiResult<Boolean> authorization(@RequestBody @Valid FranchiseOwnerAuthorizationDto updateReqVO) {
        franchiseOwnerService.authorization(updateReqVO);
        return success(true);
    }

    /**
     * 重新申请
     */
    @MethodDesc("重新申请")
    @Log(title = "加盟商管理", businessType = BusinessType.INSERT)
    @PostMapping("/reapply")
    public ApiResult reapply(@RequestBody @Valid FranchiseOwnerDto franchiseOwner) {
        return toAjax(franchiseOwnerService.reapply(franchiseOwner));
    }

    /**
     * 查询我的申请列表
     */
    @MethodDesc("我的申请列表")
    @GetMapping("/my-apply-list")
    @PreAuthorize("@ss.hasPermission('user:FranchiseOwner:myApplyList')")
    public ApiResult myApplyList(MyFranchiseOwnerQueryDto queryDto) {
        startPage();
        List<FranchiseOwnerVO> list = franchiseOwnerService.selectMyApplyList(queryDto);
        return getDataTable(list);
    }

    /**
     * 查询品牌商的加盟商列表
     */
    @MethodDesc("品牌商的加盟商列表")
    @GetMapping("/brand-franchise-list")
    public ApiResult brandFranchiseList(Long brandId) {
        if (brandId == null) {
            return error("品牌商ID不能为空");
        }
        List<FranchiseOwnerSimpleVO> franchiseOwnerSimpleVOS = franchiseOwnerService.selectFranchiseOwnerListByBrandId(brandId);
        return success(franchiseOwnerSimpleVOS);
    }

    /**
     * 查询加盟商下载限制
     *
     * @param franchiseOwnerQueryDto 查询参数
     * @return 下载限制列表
     */
    @MethodDesc("查询加盟商下载限制")
    @PreAuthorize("@ss.hasPermission('user:FranchiseOwner:downloadLimit')")
    @GetMapping("/downloadLimit")
    public ApiResult downloadLimit(FranchiseOwnerQueryDto franchiseOwnerQueryDto) {
        startPage();
        return getDataTable(franchiseOwnerService.selectFranchiseDownloadLimit(franchiseOwnerQueryDto));
    }

    /**
     * 修改加盟商下载限制
     *
     * @param franchiseOwnerVo 修改参数
     * @return 修改结果
     */
    @MethodDesc("修改加盟商下载限制")
    @PreAuthorize("@ss.hasPermission('user:FranchiseOwner:updateDownloadLimit')")
    @PutMapping("/downloadLimit")
    public ApiResult updateDownloadLimit(@RequestBody FranchiseOwner franchiseOwnerVo) {
        return franchiseOwnerService.updateDownloadLimit(franchiseOwnerVo);
    }

    /**
     * 线下收款-来自加盟商下拉框
     * @param brandId 品牌商ID
     * @return 品牌商名称-加盟商简称
     */
    @MethodDesc("线下收款-来自加盟商下拉框")
    @GetMapping("/offline-payment")
    public ApiResult offlinePayment(@RequestParam(value = "brandId") Long brandId) {
        return ApiResult.ok(franchiseOwnerService.offlinePayment(brandId));
    }


    @MethodDesc("合作加盟商下拉框")
    @GetMapping("/cooperateFranchiseList")
    public ApiResult findCooperateFranchiseList() {
        List<FranchiseOwnerSimpleVO> franchiseOwnerSimpleVOS = franchiseOwnerService.findCooperateFranchiseList();
        return success(franchiseOwnerSimpleVOS);
    }
}
