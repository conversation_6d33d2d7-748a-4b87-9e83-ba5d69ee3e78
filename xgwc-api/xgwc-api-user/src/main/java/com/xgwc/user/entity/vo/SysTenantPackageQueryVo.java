package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysTenantPackageQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("套餐编号")
    private Long id;

    @FieldDesc("套餐名")
    private String name;

    @FieldDesc("租户状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("备注")
    private String remark;

    @FieldDesc("创建者")
    private String creator;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("更新者")
    private String updater;

    @FieldDesc("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;

    @FieldDesc("是否删除")
    private Integer isDel;



}
