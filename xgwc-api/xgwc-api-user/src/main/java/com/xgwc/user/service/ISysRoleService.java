package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.SysRole;
import com.xgwc.user.entity.vo.SysRoleVo;
import com.xgwc.user.entity.dto.SysRoleDto;
import com.xgwc.user.entity.vo.SysRoleQueryVo;

public interface ISysRoleService  {
    /**
     * 查询角色管理
     * 
     * @param id 角色管理主键
     * @return 角色管理
     */
    public SysRoleDto selectSysRoleById(Long id);

    /**
     * 查询角色管理列表
     * 
     * @param sysRole 角色管理
     * @return 角色管理集合
     */
    public List<SysRoleDto> selectSysRoleList(SysRoleQueryVo sysRole);

    /**
     * 新增角色管理
     * 
     * @param sysRole 角色管理
     * @return 结果
     */
    public int insertSysRole(SysRoleVo sysRole);

    /**
     * 修改角色管理
     * 
     * @param sysRole 角色管理
     * @return 结果
     */
    public int updateSysRole(SysRoleVo sysRole);

    /**
     * 批量删除角色管理
     * 
     * @param ids 需要删除的角色管理主键集合
     * @return 结果
     */
    public int deleteSysRoleByIds(Long[] ids);

    /**
     * 删除角色管理信息
     * 
     * @param id 角色管理主键
     * @return 结果
     */
    public int deleteSysRoleById(Long id);
}
