package com.xgwc.user.dao;

import com.xgwc.user.entity.ReportTargetFranchise;
import com.xgwc.user.entity.ReportTargetFranchiseDept;
import com.xgwc.user.entity.dto.ReportTargetFranchiseDto;

import java.util.List;

public interface ReportTargetFranchiseMapper {

    /**
     * 单条插入
     */
    int insertReportTargetFranchise(ReportTargetFranchise reportTargetFranchise);

    /**
     * 修改目标
     * @param reportTargetFranchise 参数
     * @return 是否成功
     */
    int updateReportTargetFranchise(ReportTargetFranchise reportTargetFranchise);

    /**
     * 刷新金额
     * @param targetId
     * @return
     */
    int freshAmount(Long targetId);

    /**
     * 根据ID获取目标
     */
    ReportTargetFranchise selectReportTargetFranchiseById(Long targetId);

    /**
     * 筛选加盟商列表
     * @param reportTargetFranchises 参数
     * @return 列表
     */
    List<ReportTargetFranchiseDto> selectTargetFranchiseList(ReportTargetFranchise reportTargetFranchises);
}
