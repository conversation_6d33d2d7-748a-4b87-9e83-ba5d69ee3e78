package com.xgwc.user.service;

import com.xgwc.user.entity.dto.notice.NoticeClassfyDto;
import com.xgwc.user.entity.vo.FranchiseNoticeClassifyVo;

import java.util.List;

public interface FranchiseNoticeService {

    /**
     * 插入通知分类
     * @param noticeClassifyVo 参数
     * @return 是否成功
     */
    int insertNoticeClassify(FranchiseNoticeClassifyVo noticeClassifyVo);

    /**
     * 修改通知分类
     * @param noticeClassifyVo 参数
     * @return 是否成功
     */
    int updateNoticeClassify(FranchiseNoticeClassifyVo noticeClassifyVo);

    /**
     * 删除通知分类
     * @param noticeClassifyVo 参数
     * @return 是否删除
     */
    int updateNoticeClassifyStatus(FranchiseNoticeClassifyVo noticeClassifyVo);

    /**
     * 获取分类列表
     * @return 列表
     */
    List<NoticeClassfyDto> getNoticeClassifyList(FranchiseNoticeClassifyVo noticeClassifyVo);

}
