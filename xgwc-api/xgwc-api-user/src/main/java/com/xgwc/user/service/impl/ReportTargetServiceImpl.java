package com.xgwc.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.FranchiseStaffMapper;
import com.xgwc.user.dao.ReportTargetFranchiseDeptMapper;
import com.xgwc.user.dao.ReportTargetFranchiseMapper;
import com.xgwc.user.dao.ReportTargetFranchiseStaffMapper;
import com.xgwc.user.entity.ReportTargetFranchise;
import com.xgwc.user.entity.ReportTargetFranchiseDept;
import com.xgwc.user.entity.ReportTargetFranchiseStaff;
import com.xgwc.user.entity.SysMessage;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.ReportTargetFranchiseStaffVo;
import com.xgwc.user.service.ReportTargetService;
import com.xgwc.user.service.SysMessageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReportTargetServiceImpl implements ReportTargetService {

    @Resource
    private ReportTargetFranchiseMapper reportTargetFranchiseMapper;

    @Resource
    private ReportTargetFranchiseDeptMapper reportTargetFranchiseDeptMapper;

    @Resource
    private ReportTargetFranchiseStaffMapper reportTargetFranchiseStaffMapper;

    @Resource
    private FranchiseStaffMapper franchiseStaffMapper;

    @Resource
    private SysMessageService sysMessageService;

    private final String MESSAGE_KEY = "fanchise_target";


    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertReportTargetFranchise(ReportTargetFranchise reportTargetFranchises) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能创建目标:{}", JSONObject.toJSONString(sysUser));
            return 0;
        }
        boolean validParam = validParams(reportTargetFranchises);
        if(validParam){
            List<ReportTargetFranchiseDept> depts = reportTargetFranchises.getDepts();
            List<ReportTargetFranchiseDept> result = new ArrayList<>();
            Map<Long, List<ReportTargetFranchiseDept>> deptMap = depts.stream().collect(Collectors.groupingBy(ReportTargetFranchiseDept::getFranchiseId));
            for(Map.Entry<Long, List<ReportTargetFranchiseDept>> entry : deptMap.entrySet()){
                ReportTargetFranchise reportTargetFranchise = new ReportTargetFranchise();
                reportTargetFranchise.setFranchiseId(entry.getKey());
                initAmount(reportTargetFranchise, entry.getValue());
                reportTargetFranchise.setCreateBy(sysUser.getUserName());
                reportTargetFranchise.setCreateById(sysUser.getUserId());
                reportTargetFranchise.setBrandId(brandId);
                reportTargetFranchise.setTargetName(reportTargetFranchises.getTargetName());
                reportTargetFranchise.setTargetDate(reportTargetFranchises.getTargetDate());
                reportTargetFranchise.setDeptCount(entry.getValue().size());
                //单条插入能获取到id
                reportTargetFranchiseMapper.insertReportTargetFranchise(reportTargetFranchise);
                entry.getValue().forEach(reportTargetFranchiseDept -> {
                    reportTargetFranchiseDept.setBrandId(brandId);
                    reportTargetFranchiseDept.setTargetId(reportTargetFranchise.getTargetId());
                    reportTargetFranchiseDept.setTargetDate(reportTargetFranchise.getTargetDate());
                    reportTargetFranchiseDept.setCreateById(sysUser.getUserId());
                    reportTargetFranchiseDept.setCreateBy(sysUser.getUserName());
                });
                result.addAll(entry.getValue());
            }
            reportTargetFranchiseDeptMapper.batchInsertReportTargetFranchiseDept(result);
            sendMessage(result, reportTargetFranchises.getTargetName());
        }
        return 0;
    }

    @Async("asynExecutor")
    public void sendMessage(List<ReportTargetFranchiseDept> result, String targetName){
        List<Long> deptIds = result.stream().map(ReportTargetFranchiseDept::getDeptId).collect(Collectors.toList());
        List<FranchiseStaffDto> staffList = franchiseStaffMapper.getDeptDirectorStaffs(deptIds);
        List<SysMessage> sysMessageList = staffList.stream().map(x->{
            SysMessage sysMessage = new SysMessage();
            sysMessage.setTypeKey(MESSAGE_KEY);
            sysMessage.setTypeName("品牌商目标下发");
            sysMessage.setIsRead(0);
            sysMessage.setUserId(x.getBindUserId());
            sysMessage.setMessage(targetName);
            return sysMessage;
        }).collect(Collectors.toList());
        if(!staffList.isEmpty()){
            sysMessageService.batchInsertSysMessage(sysMessageList);
        }
    }

    private void initAmount(ReportTargetFranchise reportTargetFranchise, List<ReportTargetFranchiseDept> depts) {
        reportTargetFranchise.setTargetAmount(depts.stream().map(ReportTargetFranchiseDept::getTargetAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        reportTargetFranchise.setTargetOldCustomer(depts.stream().map(ReportTargetFranchiseDept::getTargetOldCustomer).reduce(BigDecimal.ZERO, BigDecimal::add));
        reportTargetFranchise.setTargetNewCustomer(depts.stream().map(ReportTargetFranchiseDept::getTargetNewCustomer).reduce(BigDecimal.ZERO, BigDecimal::add));
        reportTargetFranchise.setTargetConversionRate(depts.stream().map(ReportTargetFranchiseDept::getTargetConversionRate).reduce(BigDecimal.ZERO, BigDecimal::add));
        reportTargetFranchise.setTargetTransferAmount(depts.stream().map(ReportTargetFranchiseDept::getTargetTransferAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        reportTargetFranchise.setTargetComissionRate(depts.stream().map(ReportTargetFranchiseDept::getTargetComissionRate).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    @Override
    public int addReportTargetStaff(ReportTargetFranchiseStaffVo reportTargetFranchiseStaffVo) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchised = sysUser.getFranchiseId();
        if(franchised == null){
            log.error("非加盟商员工不能创建部门目标:{}", JSONObject.toJSONString(sysUser));
            return 0;
        }
        List<ReportTargetFranchiseStaff> staffList = reportTargetFranchiseStaffVo.getStaffList();
        if(staffList != null && !staffList.isEmpty()){
            staffList.forEach(x->{
                x.setFranchiseId(franchised);
                x.setBrandId(reportTargetFranchiseStaffVo.getBrandId());
                x.setCreateBy(sysUser.getUserName());
                x.setCreateById(sysUser.getUserId());
                x.setTargetDeptId(reportTargetFranchiseStaffVo.getTargetDeptId());
                x.setTargetId(reportTargetFranchiseStaffVo.getTargetId());
                x.setTargetDate(reportTargetFranchiseStaffVo.getTargetDate());
            });
        }
        reportTargetFranchiseStaffMapper.batchInsertReportTargetFranchiseDept(staffList);
        ReportTargetFranchiseDept reportTargetFranchiseDept = new ReportTargetFranchiseDept();
        reportTargetFranchiseDept.setTargetDeptId(reportTargetFranchiseStaffVo.getTargetDeptId());
        reportTargetFranchiseDept.setBrandId(reportTargetFranchiseStaffVo.getBrandId());
        reportTargetFranchiseDept.setCheckStatus(1);
        reportTargetFranchiseDeptMapper.updateReportTargetFranchiseDept(reportTargetFranchiseDept);
        //更新target主表提交数量
        CountAndCheckStatusDto countAndCheckStatusDto = reportTargetFranchiseDeptMapper.countAndCheckStatusDto(reportTargetFranchiseStaffVo.getTargetId());
        if(countAndCheckStatusDto != null){
            int count = countAndCheckStatusDto.getCount() == null ? 0 : countAndCheckStatusDto.getCount();
            int checkStatusCount = countAndCheckStatusDto.getCheckStatusCount() == null ? 0 : countAndCheckStatusDto.getCheckStatusCount();
            ReportTargetFranchise reportTargetFranchise = new ReportTargetFranchise();
            reportTargetFranchise.setTargetId(reportTargetFranchiseStaffVo.getTargetId());
            reportTargetFranchise.setCheckStatus((count == checkStatusCount) ? 2 : (checkStatusCount > 0 ? 1 : 0));
            reportTargetFranchise.setSubmitDeptCount(checkStatusCount);
            reportTargetFranchiseMapper.updateReportTargetFranchise(reportTargetFranchise);
        }
        return 1;
    }

    @Override
    public int updateReportTargetFranchiseDept(ReportTargetFranchiseDept reportTargetFranchiseDept) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能修改目标:{}", JSONObject.toJSONString(sysUser));
            return 0;
        }
        reportTargetFranchiseDept.setBrandId(brandId);
        reportTargetFranchiseDept.setUpdateBy(sysUser.getUserName());
        reportTargetFranchiseDept.setUpdateById(sysUser.getUserId());
        reportTargetFranchiseDeptMapper.updateReportTargetFranchiseDept(reportTargetFranchiseDept);
        reportTargetFranchiseMapper.freshAmount(reportTargetFranchiseDept.getTargetId());
        return 1;
    }

    @Override
    public int updateReportTargetFranchiseStaff(ReportTargetFranchiseStaff reportTargetFranchiseStaff) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能修改目标:{}", JSONObject.toJSONString(sysUser));
            return 0;
        }
        reportTargetFranchiseStaff.setBrandId(brandId);
        reportTargetFranchiseStaff.setUpdateBy(sysUser.getUserName());
        reportTargetFranchiseStaff.setUpdateById(sysUser.getUserId());
        reportTargetFranchiseStaffMapper.updateReportTargetFranchiseStaff(reportTargetFranchiseStaff);
        reportTargetFranchiseDeptMapper.freshAmount(reportTargetFranchiseStaff.getTargetId());
        reportTargetFranchiseMapper.freshAmount(reportTargetFranchiseStaff.getTargetId());
        return 1;
    }

    @Override
    public int deleteReportTargetFranchise(Long id) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能删除目标:{}", JSONObject.toJSONString(sysUser));
            return 0;
        }
        ReportTargetFranchise reportTargetFranchise = reportTargetFranchiseMapper.selectReportTargetFranchiseById(id);
        if(reportTargetFranchise != null){
            if(!Objects.equals(reportTargetFranchise.getBrandId(), brandId)){
                log.error("删除加盟商目标错误,目标:{}不属于该品牌商:{},操作人:{}", id, brandId, JSONObject.toJSONString(sysUser));
                return 0;
            }
            ReportTargetFranchise newReportTargetFranchise = new ReportTargetFranchise();
            newReportTargetFranchise.setUpdateBy(sysUser.getUserName());
            newReportTargetFranchise.setUpdateById(sysUser.getUserId());
            newReportTargetFranchise.setTargetId(id);
            newReportTargetFranchise.setBrandId(brandId);
            newReportTargetFranchise.setStatus(1);
            reportTargetFranchiseMapper.updateReportTargetFranchise(newReportTargetFranchise);
            //同步移除加盟商部门目标和员工目标
            reportTargetFranchiseDeptMapper.deleteByTargetId(id);
            reportTargetFranchiseStaffMapper.deleteByTargetId(id);
        }
        return 1;
    }

    @Override
    public List<ReportTargetFranchiseDeptList> selectReportTargetFranchiseDepts(List<Long> franchiseIds, String targetDate) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能查看:{}, selectReportTargetFranchiseDepts", JSONObject.toJSONString(sysUser));
            return null;
        }
        List<ReportTargetFranchiseDeptList> result = new ArrayList<>();
        List<ReportTargetFranchiseDeptDto> reportTargetFranchiseDepts = reportTargetFranchiseDeptMapper.getFranchiseInfoByFranchiseIds(franchiseIds, brandId);
        if(reportTargetFranchiseDepts != null && !reportTargetFranchiseDepts.isEmpty()){
            //赋值部门信息，负责人信息，上月订单金额
            handleResultParam(reportTargetFranchiseDepts, franchiseIds, targetDate);
            Map<Long, List<ReportTargetFranchiseDeptDto>> franchiseMap = reportTargetFranchiseDepts.stream().collect(Collectors.groupingBy(ReportTargetFranchiseDept::getFranchiseId));
            for(Map.Entry<Long, List<ReportTargetFranchiseDeptDto>> entry : franchiseMap.entrySet()){
                ReportTargetFranchiseDeptList reportTargetFranchiseDeptList = new ReportTargetFranchiseDeptList();
                reportTargetFranchiseDeptList.setFranchiseId(entry.getKey());
                reportTargetFranchiseDeptList.setFranchiseName(entry.getValue().get(0).getFranchiseName());
                reportTargetFranchiseDeptList.setDeptList(entry.getValue());
                result.add(reportTargetFranchiseDeptList);
            }
        }
        return result;
    }

    @Override
    public ReportTargetFranchiseDeptDetail getTargetFranchiseDetail(Long targetId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能查看目标:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        ReportTargetFranchise reportTargetFranchise = reportTargetFranchiseMapper.selectReportTargetFranchiseById(targetId);
        if(reportTargetFranchise != null){
            if(!Objects.equals(sysUser.getBrandId(), reportTargetFranchise.getBrandId())){
                log.error("非当前品牌商不能查看目标:{}, 目标ID:{}", JSONObject.toJSONString(sysUser), targetId);
                return null;
            }
            ReportTargetFranchiseDeptDetail reportTargetFranchiseDeptDetail = new ReportTargetFranchiseDeptDetail();
            reportTargetFranchiseDeptDetail.setTargetId(reportTargetFranchise.getTargetId());
            reportTargetFranchiseDeptDetail.setTargetDate(reportTargetFranchise.getTargetDate());
            reportTargetFranchiseDeptDetail.setTargetName(reportTargetFranchise.getTargetName());
            List<ReportTargetFranchiseDeptDto> reportTargetFranchiseDepts = reportTargetFranchiseDeptMapper.getReportTargetFranchiseDeptsByTaskId(targetId);
            if(reportTargetFranchiseDepts != null && !reportTargetFranchiseDepts.isEmpty()){
                handleResultParam(reportTargetFranchiseDepts, Collections.singletonList(reportTargetFranchise.getFranchiseId()), reportTargetFranchise.getTargetDate());
                reportTargetFranchiseDeptDetail.setDeptList(reportTargetFranchiseDepts);
                reportTargetFranchiseDeptDetail.setFranchiseId(reportTargetFranchiseDepts.get(0).getFranchiseId());
                reportTargetFranchiseDeptDetail.setFranchiseName(reportTargetFranchiseDepts.get(0).getFranchiseName());
            }
            return reportTargetFranchiseDeptDetail;
        }
        return null;
    }

    @Override
    public List<ReportTargetFranchiseDto> selectTargetFranchiseList(ReportTargetFranchise reportTargetFranchises) {
        SysUser sysUser = SecurityUtils.getSysUser();
        reportTargetFranchises.setBrandId(sysUser.getBrandId());
        return reportTargetFranchiseMapper.selectTargetFranchiseList(reportTargetFranchises);
    }

    @Override
    public List<ReportTargetBrandDeptDto> selectTargetFranchiseDeptList(ReportTargetFranchiseDto reportTargetFranchiseDto) {
        return List.of();
    }

    @Override
    public List<ReportTargetBrandDeptDto> selectTargetFranchiseDeptListByBrand(ReportTargetFranchiseDto reportTargetFranchiseDto) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null){
            log.error("非品牌商员工不能查看:{}, selectBrandTargetDeptListByBrand", JSONObject.toJSONString(sysUser));
            return null;
        }
        reportTargetFranchiseDto.setBrandId(brandId);
        return reportTargetFranchiseDeptMapper.selectBrandTargetDeptList(reportTargetFranchiseDto);
    }

    @Override
    public List<ReportTargetBrandDeptDto> selectTargetFranchiseDeptListByFranchise(ReportTargetFranchiseDeptDto reportTargetFranchiseDeptDto) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser.getUserType() != 2 && sysUser.getUserType() != 5){
            log.error("非加盟商员工不能查看目标列表:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        reportTargetFranchiseDeptDto.setFranchiseId(sysUser.getFranchiseId());
        if(sysUser.getUserType() == 5){
            //如果是加盟商员工
            FranchiseStaffDto franchiseStaffDto = franchiseStaffMapper.findFranchiseStaffByBindUserId(sysUser.getUserId());
            if(franchiseStaffDto == null){
                reportTargetFranchiseDeptDto.setDeptId(franchiseStaffDto.getDeptId());

            }
        }
        List<ReportTargetBrandDeptDto> reportTargetBrandDeptDtos = reportTargetFranchiseDeptMapper.selectFranchiseTargetDeptListByFranchiseId(reportTargetFranchiseDeptDto);
        return reportTargetBrandDeptDtos;
    }

    @Override
    public ReportTargetFranchiseStaffList getReportTargetFranchiseStaffList(Long targetId, Long deptId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        ReportTargetFranchiseStaffList reportTargetFranchiseStaffList = new ReportTargetFranchiseStaffList();
        ReportTargetFranchise reportTargetFranchise = reportTargetFranchiseMapper.selectReportTargetFranchiseById(targetId);
        if(reportTargetFranchise != null){
            reportTargetFranchiseStaffList.setTargetId(reportTargetFranchise.getTargetId());
            reportTargetFranchiseStaffList.setTargetName(reportTargetFranchise.getTargetName());
            reportTargetFranchiseStaffList.setTargetConversionRate(reportTargetFranchise.getTargetConversionRate());
            reportTargetFranchiseStaffList.setTargetComissionRate(reportTargetFranchise.getTargetComissionRate());
            reportTargetFranchiseStaffList.setTargetNewCustomer(reportTargetFranchise.getTargetNewCustomer());
            reportTargetFranchiseStaffList.setTargetTransferAmount(reportTargetFranchise.getTargetTransferAmount());
            reportTargetFranchiseStaffList.setTargeAmount(reportTargetFranchise.getTargetAmount());
            FranchiseStaffDto franchiseStaffDto = franchiseStaffMapper.findFranchiseStaffByBindUserId(sysUser.getUserId());
            if(franchiseStaffDto != null){
                List<ReportTargetFranchiseStaffDto> reportTargetFranchiseStaffs = reportTargetFranchiseStaffMapper.getReportTargetFranchiseStaffByTargetId(targetId, deptId);
                if(reportTargetFranchiseStaffs == null || reportTargetFranchiseStaffs.isEmpty()){
                    List<FranchiseStaffSimpleDto> franchiseStaffSimpleDtos = franchiseStaffMapper.findFranchiseStaffListDropDownByFranchiseId(sysUser.getFranchiseId(), deptId);
                    if(franchiseStaffSimpleDtos != null && !franchiseStaffSimpleDtos.isEmpty()){
                        reportTargetFranchiseStaffs = franchiseStaffSimpleDtos.stream().map(x->{
                            ReportTargetFranchiseStaffDto reportTargetFranchiseStaff = new ReportTargetFranchiseStaffDto();
                            reportTargetFranchiseStaff.setUserId(x.getBindUserId());
                            reportTargetFranchiseStaff.setUserName(x.getStageName());
                            reportTargetFranchiseStaff.setStationName(x.getPostName());
                            reportTargetFranchiseStaff.setDeptName(x.getDeptName());
                            return reportTargetFranchiseStaff;
                        }).collect(Collectors.toList());
                    }
                }
                reportTargetFranchiseStaffList.setStaffList(reportTargetFranchiseStaffs);
                return reportTargetFranchiseStaffList;
            }
        }
        return null;
    }

    @Override
    public Integer countNotSubmit() {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null){
            log.error("非加盟商员工员工不用判断:{}", JSONObject.toJSONString(sysUser));
            return 0;
        }
        FranchiseStaffDto franchiseStaffDto = franchiseStaffMapper.findFranchiseStaffByBindUserId(sysUser.getUserId());
        //为部门负责人
        if(franchiseStaffDto != null && franchiseStaffDto.getIsPrincipal() == 0){
             return reportTargetFranchiseDeptMapper.countNotSubmitDept(franchiseStaffDto.getDeptId());
        }
        return 0;
    }

    /**
     * 处理返回参数
     */
    private void handleResultParam(List<ReportTargetFranchiseDeptDto> reportTargetFranchiseDepts, List<Long> franchiseIds, String targetDate){
        String preMonth = DateUtils.getPreviousMonth(targetDate, 1);
        //赋值上个月业绩
        List<ReportTargetFranchiseDept> preMonths = reportTargetFranchiseDeptMapper.getPreMonthReportTargetFranchiseDepts(preMonth, franchiseIds);
        List<FranchiseStaffDto> franchiseStaffDtos = franchiseStaffMapper.getFirstLevelDirectorStaffs(franchiseIds);
        Map<Long, List<ReportTargetFranchiseDept>> deptMap;
        Map<Long, List<FranchiseStaffDto>> deptStaffMap = new HashMap<>();
        if(preMonths != null && !preMonths.isEmpty()){
            deptMap = preMonths.stream().collect(Collectors.groupingBy(ReportTargetFranchiseDept::getDeptId));
        } else {
            deptMap = new HashMap<>();
        }
        if(franchiseStaffDtos != null && !franchiseStaffDtos.isEmpty()){
            deptStaffMap = franchiseStaffDtos.stream().collect(Collectors.groupingBy(FranchiseStaffDto::getDeptId));
        }
        Map<Long, List<FranchiseStaffDto>> finalDeptStaffMap = deptStaffMap;
        reportTargetFranchiseDepts.forEach(dept -> {
            List<ReportTargetFranchiseDept> depts = deptMap.get(dept.getDeptId());
            if(depts != null && !depts.isEmpty()){
                dept.setPreMonthAmount(depts.get(0).getAmountOrder());
            }
            List<FranchiseStaffDto> deptStaffs = finalDeptStaffMap.get(dept.getDeptId());
            if(deptStaffs != null && !deptStaffs.isEmpty()){
                dept.setDeptName(deptStaffs.get(0).getDeptName());
                dept.setDirectorName(deptStaffs.stream()
                        .map(FranchiseStaffDto::getStageName)
                        .collect(Collectors.joining(",")));
            }
        });
    }

    /**
     * 校验参数
     * @param reportTargetFranchises 参数
     * @return 是否成功
     */
    private boolean validParams(ReportTargetFranchise reportTargetFranchises){
        List<ReportTargetFranchiseDept> depts = reportTargetFranchises.getDepts();
        if(depts != null && !depts.isEmpty()){
            for(ReportTargetFranchiseDept dept : depts){
                if(dept.getFranchiseId() == null || dept.getDeptId() == null){
                    log.error("加盟商id或者部门id为空:{}", JSONObject.toJSONString(dept));
                    return false;
                }
                if(dept.getTargetAmount() == null){
                    log.error("目标金额为空:{}", JSONObject.toJSONString(dept));
                    return false;
                }
            }
        }else{
            log.error("加盟商数据为空:{}", JSONObject.toJSONString(reportTargetFranchises));
            return false;
        }
        return true;
    }
}
