package com.xgwc.user.entity.dto;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.entity.dto
 * @Author: kou<PERSON><PERSON>hu<PERSON>
 * @CreateTime: 2025-05-07  17:57
 */

import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.entity.ApiResult;
import lombok.Data;

import java.util.concurrent.atomic.AtomicInteger;

@Data
public class FranchiseEmployeesCountDto {

    @FieldDesc("当前在职人数")
    private AtomicInteger currentCount = new AtomicInteger(0);

    @FieldDesc("当前离职申请中人数")
    private AtomicInteger resignationCount = new AtomicInteger(0);

    @FieldDesc("本月入职人数")
    private AtomicInteger monthlyEntryCount = new AtomicInteger(0);

    @FieldDesc("本月离职人数")
    private AtomicInteger monthlyExitCount = new AtomicInteger(0);

    @FieldDesc("最近三个月合同到期人数")
    private AtomicInteger contractMonthsCount = new AtomicInteger(0);

    /** 员工档案列表 */
    private ApiResult employeesDtos;

    public void incrementCurrentCount() {
        currentCount.incrementAndGet();
    }

    public void incrementResignationCount() {
        resignationCount.incrementAndGet();
    }

    public void incrementMonthlyEntryCount() {
        monthlyEntryCount.incrementAndGet();
    }

    public void incrementMonthlyExitCount() {
        monthlyExitCount.incrementAndGet();
    }

    public void incrementContractMonthsCount() {
        contractMonthsCount.incrementAndGet();
    }
}
