package com.xgwc.user.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.XgwcCompanyInfoMapper;
import com.xgwc.user.entity.SysDictData;
import com.xgwc.user.entity.dto.XgwcCompanyInfoDto;
import com.xgwc.user.entity.vo.XgwcCompanyInfoVo;
import com.xgwc.user.service.XgwcCompanyInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-07-04  09:49
 */
@Service
@Slf4j
public class XgwcCompanyInfoServiceImpl implements XgwcCompanyInfoService {

    @Resource
    private XgwcCompanyInfoMapper xgwcCompanyInfoMapper;

    /**
     * 获取公司信息列表
     * @param xgwcCompanyInfoVo 查询参数
     * @return 列表
     */
    @Override
    public List<XgwcCompanyInfoDto> getCompanyInfoList(XgwcCompanyInfoVo xgwcCompanyInfoVo) {
        Long brandId = xgwcCompanyInfoVo.getBrandId();
        xgwcCompanyInfoVo.setBrandId(brandId == null ? SecurityUtils.getSysUser().getBrandId() : brandId);
        List<XgwcCompanyInfoDto> companyInfoList = xgwcCompanyInfoMapper.getCompanyInfoList(xgwcCompanyInfoVo);
        for (XgwcCompanyInfoDto xgwcCompanyInfoDto : companyInfoList) {
            xgwcCompanyInfoDto.setIdcardName(ParamDecryptUtil.encryptField(xgwcCompanyInfoDto.getIdcardName(),
                    "法人姓名(加密)", false));
        }

        if (xgwcCompanyInfoVo.getIsFlag() == 0) {
            List<SysDictData> paymentcodeBody = xgwcCompanyInfoMapper.selectDictData("paymentcode_body",
                    xgwcCompanyInfoVo.getFranchiseId());
            if (!CollectionUtils.isEmpty(paymentcodeBody)) {
                XgwcCompanyInfoDto xgwcCompanyInfoDto = new XgwcCompanyInfoDto();
                paymentcodeBody.forEach(dictData -> {
                    xgwcCompanyInfoDto.setId(dictData.getDictCode());
                    xgwcCompanyInfoDto.setCompanySimpleName(dictData.getDictLabel());
                    xgwcCompanyInfoDto.setPaymentCodeBody(0);
                    companyInfoList.add(xgwcCompanyInfoDto);
                });
            }
        }
        return companyInfoList;
    }

    /**
     * 保存公司主体信息
     *
     * @param xgwcCompanyInfoVo 包含公司主体信息的DTO对象
     * @return 操作结果的ApiResult对象
     */
    @Override
    public ApiResult saveXgwcCompanyInfo(XgwcCompanyInfoVo xgwcCompanyInfoVo) {
        if (xgwcCompanyInfoVo == null) {
            log.error("保存公司主体信息失败：公司主体信息为空");
            return ApiResult.error("公司主体信息不能为空");
        }

        // 入参加密
        extracted(xgwcCompanyInfoVo);

        // 保存公司主体基本信息
        xgwcCompanyInfoVo.setCreateBy(SecurityUtils.getNickName());
        xgwcCompanyInfoVo.setIsFlag(1);
        xgwcCompanyInfoVo.setBrandId(SecurityUtils.getSysUser().getBrandId());
        int affectedRows = xgwcCompanyInfoMapper.saveXgwcCompanyInfo(xgwcCompanyInfoVo);
        if (affectedRows <= 0) {
            log.error("保存公司主体基本信息失败，公司主体名称: {}", xgwcCompanyInfoVo.getCompanyName());
            throw new ApiException("保存公司主体基本信息失败");
        }
        log.info("保存公司主体信息成功，公司主体名称: {}", xgwcCompanyInfoVo.getCompanyName());
        return ApiResult.ok();
    }

    private static void extracted(XgwcCompanyInfoVo xgwcCompanyInfoVo) {
        xgwcCompanyInfoVo.setIdcardFront(ParamDecryptUtil.encryptField(xgwcCompanyInfoVo.getIdcardFront(),
                "身份证正面(加密)", true));
        xgwcCompanyInfoVo.setIdcardBack(ParamDecryptUtil.encryptField(xgwcCompanyInfoVo.getIdcardBack(),
                "身份证反面(加密)", true));
        xgwcCompanyInfoVo.setIdcardName(ParamDecryptUtil.encryptField(xgwcCompanyInfoVo.getIdcardName(),
                "法人姓名(加密)", true));
        xgwcCompanyInfoVo.setIdcardNo(ParamDecryptUtil.encryptField(xgwcCompanyInfoVo.getIdcardNo(),
                "法人身份证号(加密)", true));
        xgwcCompanyInfoVo.setIdcardPhone(ParamDecryptUtil.encryptField(xgwcCompanyInfoVo.getIdcardPhone(),
                "法人手机号(加密)", true));
        xgwcCompanyInfoVo.setIdcardEmail(ParamDecryptUtil.encryptField(xgwcCompanyInfoVo.getIdcardEmail(),
                "法人邮箱(加密)", true));
        xgwcCompanyInfoVo.setBankName(ParamDecryptUtil.encryptField(xgwcCompanyInfoVo.getBankName(),
                "开户行(加密)", true));
        xgwcCompanyInfoVo.setBankNo(ParamDecryptUtil.encryptField(xgwcCompanyInfoVo.getBankNo(),
                "对公账号(加密)", true));
    }

    /**
     * 根据公司主体ID查询公司主体信息
     *
     * @param companyId 公司主体ID，不能为null
     * @return 包含公司主体信息的ApiResult对象
     */
    @Override
    public ApiResult getXgwcCompanyById(Long companyId) {
        if (companyId == null) {
            log.error("查询公司主体信息失败：公司主体ID为空");
            return ApiResult.error("公司主体ID不能为空");
        }

        try {
            XgwcCompanyInfoDto xgwcCompanyInfo =  xgwcCompanyInfoMapper.getXgwcCompanyById(companyId,
                    SecurityUtils.getSysUser().getBrandId());
            if (xgwcCompanyInfo == null) {
                log.warn("查询公司主体信息：公司主体不存在，ID: {}", companyId);
                return ApiResult.error("公司主体不存在");
            }

            // 出参解密
            xgwcCompanyInfo.setIdcardFront(ParamDecryptUtil.encryptField(xgwcCompanyInfo.getIdcardFront(),
                    "身份证正面(加密)", false));
            xgwcCompanyInfo.setIdcardBack(ParamDecryptUtil.encryptField(xgwcCompanyInfo.getIdcardBack(),
                    "身份证反面(加密)", false));
            xgwcCompanyInfo.setIdcardName(ParamDecryptUtil.encryptField(xgwcCompanyInfo.getIdcardName(),
                    "法人姓名(加密)", false));
            xgwcCompanyInfo.setIdcardNo(ParamDecryptUtil.encryptField(xgwcCompanyInfo.getIdcardNo(),
                    "法人身份证号(加密)", false));
            xgwcCompanyInfo.setIdcardPhone(ParamDecryptUtil.encryptField(xgwcCompanyInfo.getIdcardPhone(),
                    "法人手机号(加密)", false));
            xgwcCompanyInfo.setIdcardEmail(ParamDecryptUtil.encryptField(xgwcCompanyInfo.getIdcardEmail(),
                    "法人邮箱(加密)", false));
            xgwcCompanyInfo.setBankName(ParamDecryptUtil.encryptField(xgwcCompanyInfo.getBankName(),
                    "开户行(加密)", false));
            xgwcCompanyInfo.setBankNo(ParamDecryptUtil.encryptField(xgwcCompanyInfo.getBankNo(),
                    "对公账号(加密)", false));

            log.info("查询公司主体信息成功，公司主体ID: {}", companyId);
            return ApiResult.ok(xgwcCompanyInfo);
        } catch (Exception e) {
            log.error("查询公司主体信息异常，公司主体ID: {}", companyId, e);
            return ApiResult.error("查询公司主体信息失败，请稍后再试");
        }
    }

    /**
     * 更新公司主体信息
     *
     * @param xgwcCompanyInfoVo 包含更新信息的公司主体DTO对象
     * @return 操作结果的ApiResult对象
     */
    @Override
    public ApiResult updateXgwcCompanyById(XgwcCompanyInfoVo xgwcCompanyInfoVo) {
        if (xgwcCompanyInfoVo == null) {
            log.error("更新公司主体信息失败：公司主体信息为空");
            return ApiResult.error("公司主体信息不能为空");
        }

        if (xgwcCompanyInfoVo.getId() == null) {
            log.error("更新公司主体信息失败：公司主体ID为空");
            return ApiResult.error("公司主体ID不能为空");
        }

        try {
            // 更新公司主体基本信息
            xgwcCompanyInfoVo.setUpdateBy(SecurityUtils.getNickName());

            // 入参加密
            extracted(xgwcCompanyInfoVo);

            int affectedRows = xgwcCompanyInfoMapper.updateXgwcCompanyById(xgwcCompanyInfoVo);
            if (affectedRows <= 0) {
                log.error("更新公司主体基本信息失败，公司主体ID: {}", xgwcCompanyInfoVo.getId());
                throw new ApiException ("修改公司主体信息失败");
            }

            log.info("更新公司主体信息成功，公司主体ID: {}, 公司主体名称: {}",
                    xgwcCompanyInfoVo.getId(), xgwcCompanyInfoVo.getCompanyName());
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("更新公司主体信息异常，公司主体ID: {}, 公司主体名称: {}",
                    xgwcCompanyInfoVo.getId(), xgwcCompanyInfoVo.getCompanyName(), e);
            throw new ApiException("更新公司主体信息失败，请稍后再试");
        }
    }

    /**
     * 更新公司主体状态
     *
     * @param companyId 公司主体ID，不能为null
     * @param status 要更新的状态值
     * @return 操作结果的ApiResult对象
     */
    @Override
    public ApiResult updateStatusById(Long companyId, Integer status) {
        if (companyId == null) {
            log.error("更新公司主体状态失败：公司主体ID为空");
            return ApiResult.error("公司主体ID不能为空");
        }

        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新公司主体状态失败");
        }

        int affectedRows = xgwcCompanyInfoMapper.updateStatusById(companyId, status);
        if (affectedRows > 0) {
            log.info("公司主体状态更新成功，ID: {}, 新状态: {}", companyId, status);
            return ApiResult.ok();
        }

        log.error("公司主体状态更新失败，ID: {}", companyId);
        return ApiResult.error("状态更新失败");
    }
}
