package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.user.dao.ServiceAuthorizeMapper;
import com.xgwc.user.entity.ServiceAuthorize;
import com.xgwc.user.entity.dto.ServiceAuthorizeDto;
import com.xgwc.user.entity.vo.ServiceAuthorizeVo;
import com.xgwc.user.feign.entity.BrandOwnerDto;
import com.xgwc.user.service.IServiceAuthorizeService;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ServiceAuthorizeServiceImpl implements IServiceAuthorizeService  {
    @Resource
    private ServiceAuthorizeMapper serviceAuthorizeMapper;

    @Override
    public ServiceAuthorizeDto selectServiceAuthorize() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null){
            return null;
        }
        return serviceAuthorizeMapper.selectServiceAuthorizeByBrandId(brandId);
    }

    /**
     * 新增服务授权
     * @param dto 服务授权
     * @return 结果
     */
    @Override
    public int insertServiceAuthorize(ServiceAuthorizeVo dto) {
        int result = 0;
        if(dto.getId() == null){
            ServiceAuthorizeDto serviceAuthorizeDto = serviceAuthorizeMapper.selectServiceAuthorizeByBrandId(SecurityUtils.getSysUser().getBrandId());
            if(serviceAuthorizeDto!= null){
                throw new ApiException("该品牌已经有服务授权");
            }
            ServiceAuthorize serviceAuthorize = BeanUtil.copyProperties(dto, ServiceAuthorize.class);
            serviceAuthorize.setBrandId(SecurityUtils.getSysUser().getBrandId());
            serviceAuthorize.setCreateTime(DateUtils.getNowDate());
            result = serviceAuthorizeMapper.insertServiceAuthorize(serviceAuthorize);
        }else {
            ServiceAuthorize serviceAuthorize = BeanUtil.copyProperties(dto, ServiceAuthorize.class);
            serviceAuthorize.setUpdateTime(DateUtils.getNowDate());
            result = serviceAuthorizeMapper.updateServiceAuthorize(serviceAuthorize);
        }
        return result;
    }

    @Override
    public List<BrandOwnerDto> getServiceBrandList() {
        Long serviceId = SecurityUtils.getSysUser().getServiceId();
        if (serviceId == null){
            return Lists.newArrayList();
        }
        return serviceAuthorizeMapper.getServiceBrandList(serviceId);
    }

    @Override
    public List<Long> getServiceBrandIdList(Long serviceId) {
        return serviceAuthorizeMapper.getServiceBrandIdList(serviceId);
    }

}
