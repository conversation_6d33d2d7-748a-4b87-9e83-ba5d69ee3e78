package com.xgwc.user.entity.dto;

import lombok.Data;

@Data
public class SysUserMiddleDto {

    /**
     * 主键id，以后作为真正唯一的userId
     */
    private Long userId;

    /**
     * 统一用户id
     */
    private Long mainUserId;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 来源id
     */
    private Long sourceId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 加盟商id
     */
    private Long franchiseId;

    /**
     * 品牌商ID
     */
    private Long brandId;

    private String userName;

    private Integer entryDay;

    private String companyName;

    private Integer checkStatus;

    private Long serviceOwnerId;

    private String stageName;
}
