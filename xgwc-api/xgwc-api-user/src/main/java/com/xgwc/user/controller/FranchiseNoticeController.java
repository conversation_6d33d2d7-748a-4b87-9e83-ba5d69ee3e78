package com.xgwc.user.controller;

import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.notice.NoticeClassfyDto;
import com.xgwc.user.entity.vo.FranchiseNoticeClassifyVo;
import com.xgwc.user.service.FranchiseNoticeService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("notice/franchise")
@RestController
public class FranchiseNoticeController extends BaseController {

    @Resource
    private FranchiseNoticeService franchiseNoticeService;

    /**
     * 加盟商添加通知分类
     */
    @RequestMapping("add_classify")
    public ApiResult addNoticeClassify(@RequestBody @Valid FranchiseNoticeClassifyVo noticeClassifyVo) {
        int result = franchiseNoticeService.insertNoticeClassify(noticeClassifyVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 加盟商修改通知分类
     */
    @RequestMapping("update_classify")
    public ApiResult updateNoticeClassify(@RequestBody @Valid FranchiseNoticeClassifyVo noticeClassifyVo) {
        if(noticeClassifyVo.getId() == null){
            return ApiResult.error("分类ID为空");
        }
        int result = franchiseNoticeService.updateNoticeClassify(noticeClassifyVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 加盟商修改通知分类
     */
    @RequestMapping("update_status")
    public ApiResult updateNoticeClassifyStatus(@RequestBody FranchiseNoticeClassifyVo noticeClassifyVo) {
        if(noticeClassifyVo.getId() == null || noticeClassifyVo.getStatus() == null){
            return ApiResult.error("参数缺失");
        }
        int result = franchiseNoticeService.updateNoticeClassifyStatus(noticeClassifyVo);
        return result <= 0 ? ApiResult.error("") : ApiResult.ok();
    }

    /**
     * 获取通知分类列表
     */
    @GetMapping("get_noticeclassify_list")
    public ApiResult<List<NoticeClassfyDto>> getNoticeClassifyList(FranchiseNoticeClassifyVo noticeClassifyVo) {
        startPage();
        return getDataTable(franchiseNoticeService.getNoticeClassifyList(noticeClassifyVo));
    }
}
