package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.ServiceAuthorizeDto;
import com.xgwc.user.entity.vo.ServiceAuthorizeVo;
import com.xgwc.user.feign.api.ServiceAuthorizeFeign;
import com.xgwc.user.feign.entity.BrandOwnerDto;
import com.xgwc.user.service.IServiceAuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 服务授权Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/serviceAuthorize")
public class ServiceAuthorizeController extends BaseController implements ServiceAuthorizeFeign {

    @Autowired
    private IServiceAuthorizeService serviceAuthorizeService;

    /**
     * 获取服务授权信息
     */
    @MethodDesc("获取服务授权信息")
    @PreAuthorize("@ss.hasPermission('serviceAuthorize:serviceAuthorize:query')")
    @GetMapping
    public ApiResult<ServiceAuthorizeDto> getInfo() {
        return success(serviceAuthorizeService.selectServiceAuthorize());
    }

    /**
     * 服务授权
     */
    @MethodDesc("服务授权")
    @PreAuthorize("@ss.hasPermission('serviceAuthorize:serviceAuthorize:add')")
    @Log(title = "服务授权", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody ServiceAuthorizeVo serviceAuthorize) {
        return toAjax(serviceAuthorizeService.insertServiceAuthorize(serviceAuthorize));
    }

    @MethodDesc("获取授权给服务商的品牌商列表")
    @GetMapping("/getServiceBrandList")
    public ApiResult<BrandOwnerDto> getServiceBrandList() {
        return success(serviceAuthorizeService.getServiceBrandList());
    }

    @MethodDesc("获取授权给服务商的品牌商Id列表")
    @GetMapping("/getServiceBrandIdList")
    public List<Long> getServiceBrandIdList(@RequestParam("serviceId") Long serviceId) {
        return serviceAuthorizeService.getServiceBrandIdList(serviceId);
    }

}
