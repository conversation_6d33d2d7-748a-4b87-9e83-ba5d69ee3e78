package com.xgwc.user.entity.dto.aliyun;

import lombok.Data;

/**
 * 营业执照
 * <AUTHOR>
 */
@Data
public class BusinessLicense {

    /**
     * 标题
     */
    private String title;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 营业名称
     */
    private String companyName;

    /**
     * 类型
     */
    private String companyType;

    /**
     * 营业场所/住所
     */
    private String businessAddress;

    /**
     * 法人/负责人
     */
    private String legalPerson;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 注册资本
     */
    private String registeredCapital;

    /**
     * 注册日期
     */
    private String RegistrationDate;

    /**
     * 发证日期
     */
    private String issueDate;

    /**
     * 营业期限
     */
    private String validPeriod;

    /**
     * 格式化营业期限起始日期
     */
    private String validFromDate;

    /**
     * 格式化营业期限终止日期
     */
    private String validToDate;

    /**
     * 组成形式
     */
    private String companyForm;
}
