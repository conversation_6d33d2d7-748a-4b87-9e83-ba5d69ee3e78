package com.xgwc.user.dao;

import com.xgwc.user.entity.SysDictData;
import com.xgwc.user.entity.dto.XgwcCompanyInfoDto;
import com.xgwc.user.entity.vo.XgwcCompanyInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.dao
 * @Author: kouwenzhuo
 * @CreateTime: 2025-07-04  09:50
 */
public interface XgwcCompanyInfoMapper {

    /**
     * 获取公司主体列表
     *
     * @param xgwcCompanyInfoVo 查询参数
     * @return 列表
     */
    List<XgwcCompanyInfoDto> getCompanyInfoList(@Param("xgwcCompanyInfoVo") XgwcCompanyInfoVo xgwcCompanyInfoVo);


    /**
     * 新增公司主体
     *
     * @param xgwcCompanyInfoVo 公司主体信息
     * @return 影响行数
     */
    int saveXgwcCompanyInfo(@Param("xgwcCompanyInfoVo") XgwcCompanyInfoVo xgwcCompanyInfoVo);

    /**
     * 根据id查询公司主体信息
     *
     * @param companyId 公司主体id
     * @param brandId   品牌商id
     * @return 公司主体信息
     */
    XgwcCompanyInfoDto getXgwcCompanyById(@Param("companyId") Long companyId, @Param("brandId") Long brandId);

    /**
     * 修改公司主体信息
     *
     * @param xgwcCompanyInfoVo 公司主体信息
     * @return 影响行数
     */
    int updateXgwcCompanyById(@Param("xgwcCompanyInfoVo") XgwcCompanyInfoVo xgwcCompanyInfoVo);

    /**
     * 修改公司主体状态
     *
     * @param companyId 公司主体id
     * @param status    状态
     * @return 影响行数
     */
    int updateStatusById(@Param("companyId") Long companyId, @Param("status") Integer status);

    /**
     * 查询字典数据
     *
     * @param paymentCodeBody 付款码主体
     */
    List<SysDictData> selectDictData(@Param("paymentCodeBody") String paymentCodeBody, @Param("franchiseId") Long franchiseId);
}
