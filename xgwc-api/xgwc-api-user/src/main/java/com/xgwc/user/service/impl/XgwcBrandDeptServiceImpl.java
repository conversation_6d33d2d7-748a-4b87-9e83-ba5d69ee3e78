package com.xgwc.user.service.impl;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.XgwcBrandDeptMapper;
import com.xgwc.user.entity.Staff;
import com.xgwc.user.entity.dto.XgwcBrandDeptDto;
import com.xgwc.user.entity.param.XgwcBrandDeptParam;
import com.xgwc.user.entity.vo.StaffVo;
import com.xgwc.user.entity.vo.XgwcBrandDeptInfo;
import com.xgwc.user.entity.vo.XgwcBrandDeptVo;
import com.xgwc.user.service.XgwcBrandDeptService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;



/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  10:00
 */
@Service
@Slf4j
public class XgwcBrandDeptServiceImpl implements XgwcBrandDeptService {

    @Resource
    private XgwcBrandDeptMapper xgwcBrandDeptMapper;

    public static final Long PARENT_ID = 0L;

    /**
     * 获取部门列表
     * @param xgwcBrandDeptParam 查询参数
     * @return 部门列表
     */
    @Override
    public List<XgwcBrandDeptInfo> getXgwcBrandDeptList(XgwcBrandDeptParam xgwcBrandDeptParam) {
        xgwcBrandDeptParam.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        List<XgwcBrandDeptVo> typeInfos = xgwcBrandDeptMapper.getXgwcBrandDeptList(xgwcBrandDeptParam);

        if (CollectionUtils.isEmpty(typeInfos)) {
            return null;
        }

        if (typeInfos.stream().anyMatch(business -> Objects.isNull(business.getPid()))) {
            log.error("获取部门信息失败，参数不完整缺少pid");
            return null;
        }

        // 如果是搜索或筛选条件（如 deptName），直接返回扁平结构（默认 levelNum = 1）
        if (StringUtils.isNotEmpty(xgwcBrandDeptParam.getDeptName())) {
            return typeInfos.stream()
                    .map(xgwcBrandDeptVo -> {
                        XgwcBrandDeptInfo childInfo = new XgwcBrandDeptInfo();
                        BeanUtils.copyProperties(xgwcBrandDeptVo, childInfo);
                        childInfo.setLevelNum(1); // 扁平结构默认层级为1
                        return childInfo;
                    })
                    .collect(Collectors.toList());
        }

        // 按 pid 分组，用于构建树形结构
        Map<Long, List<XgwcBrandDeptVo>> pidToChildren = typeInfos.stream()
                .collect(Collectors.groupingBy(XgwcBrandDeptVo::getPid));

        // 构建树形结构，从顶级节点开始（pid = PARENT_ID），初始层级为1
        List<XgwcBrandDeptInfo> collect = typeInfos.stream()
                .filter(item -> PARENT_ID.equals(item.getPid())) // 筛选顶级节点
                .map(newItem -> {
                    XgwcBrandDeptInfo typeInfoVo = new XgwcBrandDeptInfo();
                    BeanUtils.copyProperties(newItem, typeInfoVo);
                    typeInfoVo.setLevelNum(1); // 顶级节点层级为1
                    // 递归构建子树，并设置子节点的 levelNum
                    typeInfoVo.setChiledrenList(getDeptParentList(typeInfoVo, pidToChildren, 1));
                    return typeInfoVo;
                })
                .collect(Collectors.toList());
        return collect;
    }

    private List<XgwcBrandDeptInfo> getDeptParentList(XgwcBrandDeptInfo parentInfo,
                                                      Map<Long, List<XgwcBrandDeptVo>> pidToChildren,
                                                      int currentLevel) {
        Long parentId = parentInfo.getDeptId();
        List<XgwcBrandDeptVo> childrenVos = pidToChildren.get(parentId);
        if (childrenVos == null) {
            return Collections.emptyList();
        }

        return childrenVos.stream()
                .map(newItem -> {
                    XgwcBrandDeptInfo childInfo = new XgwcBrandDeptInfo();
                    BeanUtils.copyProperties(newItem, childInfo);
                    childInfo.setLevelNum(currentLevel + 1); // 子节点层级 = 父节点层级 + 1
                    // 递归构建子树
                    childInfo.setChiledrenList(getDeptParentList(childInfo, pidToChildren, currentLevel + 1));
                    return childInfo;
                })
                .collect(Collectors.toList());
    }
    /**
     * 新增部门
     * @param xgwcBrandDeptDto 部门信息
     * @return 添加结果
     */
    @Override
    public ApiResult saveXgwcBrandDept(XgwcBrandDeptDto xgwcBrandDeptDto) {
        // 参数校验
        if (xgwcBrandDeptDto == null) {
            log.error("参数校验失败，入参不能为空");
            return ApiResult.error("参数无效，入参不能为空");
        }

        ApiResult<Object> error = getObjectApiResult(xgwcBrandDeptDto);
        if (error != null) return error;

        try {
            xgwcBrandDeptDto.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
            xgwcBrandDeptDto.setCreateBy(SecurityUtils.getNickName());
            int i = xgwcBrandDeptMapper.saveXgwcBrandDept(xgwcBrandDeptDto);
            if (i > 0) {
                log.info("新增成功，deptId：{}，参数：{}", xgwcBrandDeptDto.getDeptId(), xgwcBrandDeptDto);
                return ApiResult.ok();
            } else {
                log.error("新增失败，deptId：{}可能存在重复或其他约束冲突，参数：{}", xgwcBrandDeptDto.getDeptId(), xgwcBrandDeptDto);
                return ApiResult.error("新增失败，可能原因：ID已存在或其他约束冲突");
            }
        } catch (Exception e) {
            log.error("数据库操作异常，deptId：{}，参数：{}", xgwcBrandDeptDto.getDeptId(), xgwcBrandDeptDto, e);
            return ApiResult.error("数据库操作失败，请检查日志");
        }
    }

    @Override
    public ApiResult getUserDeptByUserId(Long userId) {
        return ApiResult.ok(xgwcBrandDeptMapper.getXgwcBrandDeptByUserId(userId));
    }

    @Nullable
    private static ApiResult<Object> getObjectApiResult(XgwcBrandDeptDto xgwcBrandDeptDto) {
        if (xgwcBrandDeptDto.getPid() == null) {
            log.error("参数校验失败，pid不能为空");
            return ApiResult.error("参数无效，pid不能为空");
        }
        if (xgwcBrandDeptDto.getCompanyId() == null) {
            log.error("参数校验失败，companyId不能为空");
            return ApiResult.error("参数无效，companyId不能为空");
        }
        if (xgwcBrandDeptDto.getDeptName() == null) {
            log.error("参数校验失败，deptName不能为空");
            return ApiResult.error("参数无效，deptName不能为空");
        }
        return null;
    }


    /**
     * 根据id查询部门
     * @param deptId 部门id
     * @return 部门信息
     */
    @Override
    public ApiResult getXgwcBrandDeptById(Long deptId) {
        if (deptId == null) {
            return ApiResult.error("id不能为空");
        }
        XgwcBrandDeptDto xgwcBrandDeptDto = xgwcBrandDeptMapper.getXgwcBrandDeptById(deptId);
        if (xgwcBrandDeptDto == null) {
            log.error("查询失败，id：{}不存在", deptId);
            return ApiResult.error("id不存在");
        }

        Long parentId = xgwcBrandDeptDto.getPid();
        if(parentId > 0){
            XgwcBrandDeptDto parentDept = xgwcBrandDeptMapper.getXgwcBrandDeptById(parentId);
            if (parentDept == null) {
                log.error("部门ID：{}的父部门ID：{}不存在", deptId, parentId);
                return ApiResult.error("父部门ID不存在");
            }
            xgwcBrandDeptDto.setDeptName(parentDept.getDeptName());
            xgwcBrandDeptDto.setPid(parentDept.getDeptId());
            return ApiResult.ok(xgwcBrandDeptDto);
        }
        log.info("查询成功，id:{}", deptId); // 简化日志格式
        return ApiResult.ok(xgwcBrandDeptDto);
    }



    /**
     * 修改部门
     * @param xgwcBrandDeptDto 部门信息
     * @return 修改结果
     */
    @Override
    public ApiResult updateXgwcBrandDeptById(XgwcBrandDeptDto xgwcBrandDeptDto) {
        ApiResult<Object> error = getObjectApiResult(xgwcBrandDeptDto);
        if (error != null) return error;
        try {
            xgwcBrandDeptDto.setUpdateBy(SecurityUtils.getNickName());
            int i = xgwcBrandDeptMapper.updateXgwcBrandDeptById(xgwcBrandDeptDto);
            if (i > 0) {
                log.info("========================id:{},修改成功", xgwcBrandDeptDto.getDeptId());
                return ApiResult.ok();
            }
            log.error("修改失败，id：{}不存在或数据未变化", xgwcBrandDeptDto.getDeptId());
            return ApiResult.error("修改失败,id不存在");
        } catch (Exception e) {
            log.error("修改过程中发生异常，id：{}", xgwcBrandDeptDto.getDeptId(), e);
            return ApiResult.error("修改失败,系统异常");
        }
    }



    /**
     * 修改部门状态
     * @param deptId 部门id
     * @param status 状态
     * @return 修改结果
     */
    @Override
    public ApiResult updateStatusById(Integer deptId, Integer status) {
        // 参数校验：确保 deptId 合法且 status 在允许范围内
        if (deptId == null || deptId <= 0) {
            log.error("无效的部门ID: {}", deptId);
            return ApiResult.error("更新部门状态失败");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新部门状态失败");
        }

        try {
            int i = xgwcBrandDeptMapper.updateStatusById(deptId, status);
            if (i > 0) {
                log.info("更新部门状态成功，brandId: {}, 新状态: {}", deptId, status);
                return ApiResult.ok();
            } else {
                log.error("更新部门状态失败，brandId: {}，未找到记录或状态未更新", deptId);
                return ApiResult.error("更新部门状态失败");
            }
        } catch (Exception e) {
            log.error("更新部门状态时发生异常，brandId: {}, status: {}", deptId, status, e);
            return ApiResult.error("更新部门状态失败");
        }
    }

    /**
     * 获取部门信息
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    @Override
    public ApiResult getXgwcDeptStaffInfo(Integer deptId) {
        try {
            // 1. 查询数据
            StaffVo manager = xgwcBrandDeptMapper.selectXgwcDeptStaffManage(deptId);
            List<StaffVo> assistants = xgwcBrandDeptMapper.selectXgwcDeptStaffSchedule(deptId);

            // 2. 构建响应对象
            XgwcBrandDeptVo response = new XgwcBrandDeptVo();
            response.setIsPrincipal(getValidStaff(manager));
            response.setIsAssistant(getValidStaffList(assistants));

            return ApiResult.ok(response);
        } catch (Exception e) {
            log.error("获取部门员工信息失败，deptId: {}", deptId, e);
            return ApiResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 确保 StaffVo 不为 null，为空时返回默认实例
     */
    private StaffVo getValidStaff(StaffVo staff) {
        return staff != null ? staff : new StaffVo();
    }

    /**
     * 确保 Staff 列表不为 null，为空时返回不可变的空列表
     */
    private List<StaffVo> getValidStaffList(List<StaffVo> staffList) {
        return staffList != null ? staffList : Collections.emptyList();
    }

    /**
     * 修改部门员工信息
     *
     * @param xgwcBrandDeptDto 部门员工信息
     * @return 修改结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult updateXgwcDeptStaffInfo(XgwcBrandDeptDto xgwcBrandDeptDto) {
        // 参数校验
        if (xgwcBrandDeptDto == null) {
            return ApiResult.error("参数不能为空");
        }

        Staff isPrincipal = xgwcBrandDeptDto.getIsPrincipal();
        if (isPrincipal != null) {
            int result = xgwcBrandDeptMapper.updateDeptStaffAssistant(isPrincipal.getDeptId());
            int manageResult = xgwcBrandDeptMapper.updateDeptStaffManage(isPrincipal);
            if (manageResult <= 0 && result <= 0) {
                log.warn("更新部门负责人、排班信息失败，id不存在，参数：{}", isPrincipal);
                throw new ApiException("更新部门负责人、排班信息失败id不存在");
            }
        }

        List<Staff> isAssistant = xgwcBrandDeptDto.getIsAssistant();
        if (!CollectionUtils.isEmpty(isAssistant)) {
            for (Staff staff : isAssistant) {
                if (isPrincipal != null && isPrincipal.getIsSchedule()!= null ){
                    staff.setIsSchedule(isPrincipal.getIsSchedule());
                }
                int scheduleResult = xgwcBrandDeptMapper.updateDeptStaffSchedule(staff);
                if (scheduleResult <= 0) {
                    log.warn("更新部门助理信息失败，id不存在，参数：{}", isAssistant);
                    throw new ApiException("更新部门助理信息失败，id不存在");
                }
            }
        }

        log.info("部门人员信息更新成功");
        return ApiResult.ok();
    }

    @Override
    public List<XgwcBrandDeptInfo> getXgwcBrandDeptAndCompanyList() {
        XgwcBrandDeptParam xgwcBrandDeptParam = new XgwcBrandDeptParam();
        xgwcBrandDeptParam.setBrandOwnerId(SecurityUtils.getSysUser().getBrandId());
        xgwcBrandDeptParam.setStatus(0);
        List<XgwcBrandDeptVo> typeInfos = xgwcBrandDeptMapper.getXgwcBrandDeptList(xgwcBrandDeptParam);
        if (typeInfos.stream().anyMatch(business -> Objects.isNull(business.getPid()))) {
            log.error("获取部门信息失败，参数不完整缺少pid");
            return null;
        }

        // 收集所有唯一的公司ID
        Set<Long> companyIds = typeInfos.stream()
                .filter(vo -> vo.getCompanyId() != null)
                .map(XgwcBrandDeptVo::getCompanyId)
                .collect(Collectors.toSet());

        // 创建公司作为一级节点
        List<XgwcBrandDeptInfo> collect = companyIds.stream()
                .map(companyId -> {
                    XgwcBrandDeptVo companyVo = typeInfos.stream()
                            .filter(vo -> companyId.equals(vo.getCompanyId()) && vo.getPid() == 0)
                            .findFirst()
                            .orElse(null);

                    if (companyVo == null) {
                        return null;
                    }

                    XgwcBrandDeptInfo companyInfo = new XgwcBrandDeptInfo();
                    BeanUtils.copyProperties(companyVo, companyInfo);

                    // 设置公司作为一级节点，pid设为0
                    companyInfo.setPid(0L);
                    companyInfo.setIsFlag("company");
                    companyInfo.setDeptId(companyVo.getCompanyId()); // 使用companyId作为节点ID
                    companyInfo.setDeptName(companyVo.getCompanyName()); // 使用公司名称作为节点名称

                    // 查找属于该公司的部门（pid等于companyId的节点）
                    List<XgwcBrandDeptVo> deptList = typeInfos.stream()
                            .filter(vo -> companyId.equals(vo.getCompanyId()) && vo.getPid() == 0)
                            .collect(Collectors.toList());

                    // 构建部门树
                    List<XgwcBrandDeptInfo> xgwcBrandDeptInfos = buildDeptTree(deptList, typeInfos);
                    int totalSum = xgwcBrandDeptInfos.stream()
                            .filter(info -> info.getTotalNum() != null)
                            .mapToInt(XgwcBrandDeptInfo::getTotalNum)
                            .sum();
                    companyInfo.setTotalNum(totalSum);
                    companyInfo.setChiledrenList(xgwcBrandDeptInfos);
                    return companyInfo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return collect;
    }

    private List<XgwcBrandDeptInfo> buildDeptTree(List<XgwcBrandDeptVo> deptList, List<XgwcBrandDeptVo> allItems) {
        if (deptList.isEmpty()) {
            return Collections.emptyList();
        }

        // 按pid分组
        Map<Long, List<XgwcBrandDeptVo>> pidToChildren = allItems.stream()
                .collect(Collectors.groupingBy(XgwcBrandDeptVo::getPid));

        return deptList.stream()
                .map(deptVo -> {
                    XgwcBrandDeptInfo deptInfo = new XgwcBrandDeptInfo();
                    BeanUtils.copyProperties(deptVo, deptInfo);

                    // 递归构建子树
                    List<XgwcBrandDeptInfo> children = buildDeptTree(pidToChildren.getOrDefault(deptVo.getDeptId(),
                            Collections.emptyList()), allItems);
                    deptInfo.setChiledrenList(children);

                    // 如果是叶子节点，查询人数
                    if (children.isEmpty()) {
                        Integer staffNum = xgwcBrandDeptMapper.selectDeptStaffNum(deptVo.getDeptId());
                        deptInfo.setTotalNum(staffNum != null ? staffNum : 0);
                    } else {
                        // 非叶子节点，累加子节点人数
                        int sum = children.stream()
                                .filter(info -> info.getTotalNum() != null)
                                .mapToInt(XgwcBrandDeptInfo::getTotalNum)
                                .sum();
                        deptInfo.setTotalNum(sum);
                    }

                    return deptInfo;
                })
                .collect(Collectors.toList());
    }

}
