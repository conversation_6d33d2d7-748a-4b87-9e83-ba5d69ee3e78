package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.BranchFranchiseDeptStaff;
import com.xgwc.user.entity.dto.FranchiseDeptDto;
import com.xgwc.user.entity.dto.FranchiseDeptStaff;
import com.xgwc.user.entity.param.FranchiseDeptParam;
import com.xgwc.user.entity.vo.FranchiseDeptInfo;
import com.xgwc.user.entity.vo.FranchiseDeptVo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  11:16
 */

public interface FranchiseDeptService {

    /**
     * 获取部门列表
     * @param franchiseDeptParam 部门参数
     * @return 部门列表
     */
    List<FranchiseDeptInfo> getFranchiseDeptList(FranchiseDeptParam franchiseDeptParam);

    /**
     * 保存部门
     * @param franchiseDeptDto 部门信息
     * @return 保存结果
     */
    ApiResult saveFranchiseDept(FranchiseDeptDto franchiseDeptDto);

    /**
     * 根据部门ID获取部门信息
     * @param deptId 部门ID
     * @return 部门信息
     */
    ApiResult getFranchiseDeptById(Long deptId);

    /**
     * 通过userId查询部门信息
     * @param userId
     * @return
     */
    ApiResult getUserDeptByUserId(Long userId);

    /**
     * 更新部门信息
     * @param franchiseDeptDto 部门信息
     * @return 更新结果
     */
    ApiResult updateFranchiseDeptById(FranchiseDeptDto franchiseDeptDto);

    /**
     * 更新部门状态
     * @param deptId 部门ID
     * @param status 部门状态
     * @return 更新结果
     */
    ApiResult updateStatusById(Integer deptId, Integer status);

    /**
     * 查询回显部门详情信息-负责人、助理、是否排班
     *
     * @param deptId 部门id
     * @return 部门详情信息-负责人、助理、是否排班
     */
    ApiResult getXgwcDeptStaffInfo(Integer deptId);

    /**
     * 更新部门信息-负责人、助理、是否排班
     *
     * @param franchiseDeptDto 部门信息
     * @return 更新结果
     */
    ApiResult updateXgwcDeptStaffInfo(FranchiseDeptDto franchiseDeptDto);

    /**
     * 获取加盟商部门员工数
     * @return 列表
     */
    List<FranchiseDeptStaff> getDeptStaffTree(Long franchinseId);

    /**
     * 获取品牌商所有员工信息
     * @return 员工信息
     */
    List<BranchFranchiseDeptStaff> getAllFranchiseStaffForBrand();

    /**
     * 根据加盟商id查询所有一级部门
     * @param franchiseId 加盟商id
     * @return 部门信息
     */
    List<FranchiseDeptVo> getDeptByFranchiseId(Long franchiseId);

}
