package com.xgwc.user.controller;

import com.xgwc.common.entity.ApiResult;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("menu")
@RestController
public class MenuController {

//    @PreAuthorize("@ss.hasPermission('sys_dict_page')")
    @PostMapping("edit_menu")
    public ApiResult editMenu(){
        return ApiResult.ok("123");
    }
}
