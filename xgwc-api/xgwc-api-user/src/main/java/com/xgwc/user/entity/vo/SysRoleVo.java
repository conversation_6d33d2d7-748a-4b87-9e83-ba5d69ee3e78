package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class SysRoleVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("角色ID")
    private Long id;

    @FieldDesc("角色名称")
    private String name;

    @FieldDesc("菜单id")
    private Long[] menuIds;

    @FieldDesc("角色权限字符串")
    private String code;

    @FieldDesc("显示顺序")
    private Long sort;

    @FieldDesc("角色状态（0正常 1停用）")
    private Integer status;

    @FieldDesc("角色类型")
    private Integer type;

    @FieldDesc("备注")
    private String remark;




}
