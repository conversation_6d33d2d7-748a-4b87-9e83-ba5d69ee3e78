package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;



@Data
public class FranchiseOwnerSimpleVO {

    private static final long serialVersionUID=1L;

    /** 加盟商ID */
    @FieldDesc("加盟商ID")
    @Excel(name = "加盟商ID")
    private Long id;

    /** 公司名称 */
    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String companyName;

    /** 公司简称 */
    @FieldDesc("公司简称")
    @Excel(name = "公司简称")
    private String companySimpleName;

    private Long franchiseOwnerId;

}
