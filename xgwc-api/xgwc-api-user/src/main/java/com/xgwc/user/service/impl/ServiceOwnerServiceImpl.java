package com.xgwc.user.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.xgwc.common.constants.ModelTypeConstant;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.redis.constants.UserCacheKey;
import com.xgwc.redis.util.RedisUtil;
import com.xgwc.serviceProvider.feign.api.ServiceStaffFeign;
import com.xgwc.serviceProvider.feign.entity.StaffDto;
import com.xgwc.user.dao.SysUserMiddleMapper;
import com.xgwc.user.dao.UserMapper;
import com.xgwc.user.dao.XgwcBrandRoleMapper;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.ServiceApplyVo;
import com.xgwc.user.security.constants.SecurityConstants;
import com.xgwc.user.service.SysUserService;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.ServiceOwnerMapper;
import com.xgwc.user.service.IServiceOwnerService;
import com.xgwc.user.entity.ServiceOwner;
import com.xgwc.user.entity.vo.ServiceOwnerVo;
import com.xgwc.user.entity.vo.ServiceOwnerQueryVo;
import org.springframework.transaction.annotation.Transactional;


@Service
public class ServiceOwnerServiceImpl implements IServiceOwnerService  {
    @Resource
    private ServiceOwnerMapper serviceOwnerMapper;
    @Resource
    private SysUserMiddleMapper sysUserMiddleMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    @Lazy
    private SysUserService sysUserService;
    @Resource
    private ServiceStaffFeign serviceStaffFeign;
    @Resource
    private XgwcBrandRoleMapper xgwcBrandRoleMapper;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 查询服务商
     * 
     * @param id 服务商主键
     * @return 服务商
     */
    @Override
    public ServiceOwnerDto selectServiceOwnerById(Long id) {
        return serviceOwnerMapper.selectServiceOwnerById(id);
    }

    /**
     * 查询服务商列表
     * 
     * @param serviceOwner 服务商
     * @return 服务商
     */
    @Override
    public List<ServiceOwnerDto> selectServiceOwnerList(ServiceOwnerQueryVo serviceOwner) {
        List<ServiceOwnerDto> list = serviceOwnerMapper.selectServiceOwnerList(serviceOwner);
        if(!list.isEmpty()){
            for(ServiceOwnerDto item:list){
                if(StringUtils.isNotEmpty(item.getManagerPhone())){
                    item.setManagerPhone(ParamDecryptUtil.decryptParam(item.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
                }
            }
        }
        return list;
    }

    /**
     * 新增服务商
     * 
     * @param dto 服务商
     * @return 结果
     */
    @Override
    public Long insertServiceOwner(ServiceOwnerVo dto) {
        ServiceOwner serviceOwner = BeanUtil.copyProperties(dto, ServiceOwner.class);
        serviceOwner.setCreateTime(DateUtils.getNowDate());
        serviceOwnerMapper.insertServiceOwner(serviceOwner);
        return serviceOwner.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResult applyServiceOwner(ServiceApplyVo serviceApplyVo) {
        if(serviceApplyVo.getMainUserId() != null){
            SysUserMiddleDto sysUserMiddleDto = sysUserMiddleMapper.selectSysUserMiddleByUserIdAndUserType(serviceApplyVo.getMainUserId(), 6);
            if(sysUserMiddleDto != null){
                throw new ApiException("已注册服务商");
            }
            // 校验用户名唯一
            if(userMapper.getUserInfoByUserName(serviceApplyVo.getContact()) != null){
                throw new ApiException(ApiStatusEnums.USER_NAME_EXISTS.getMessage() ,ApiStatusEnums.USER_NAME_EXISTS.getStatus());
            }
            SysUser sysUser = userMapper.getUserInfoByUserId(serviceApplyVo.getMainUserId());
            if(sysUser == null){
                throw new ApiException("用户不存在");
            }
            sysUser.setUserName(serviceApplyVo.getContact());
            sysUser.setMainUserId(serviceApplyVo.getMainUserId());
            if(serviceApplyVo.getServiceType() == 1){
                sysUser.setUserType(6);
            }else if(serviceApplyVo.getServiceType() == 2){
                sysUser.setUserType(8);
            }
            userMapper.updateSysUser(sysUser);
            // 新增用户中间表
            Long userId = sysUserService.insertSysUserMiddle(sysUser, null);
            // 新增服务商
            ServiceOwnerVo serviceOwner = new ServiceOwnerVo();
            serviceOwner.setCompanyName(serviceApplyVo.getCompanyName());
            serviceOwner.setServiceType(serviceApplyVo.getServiceType());
            serviceOwner.setContact(serviceApplyVo.getContact());
            serviceOwner.setManagerPhone(sysUser.getPhone());
            serviceOwner.setPassword(sysUser.getPassword());
            serviceOwner.setUserId(userId);
            serviceOwner.setStatus(1);
            Long serviceId = insertServiceOwner(serviceOwner);
            // 更新用户中间表
            sysUserService.updateSysUserMiddleSourceId(userId, serviceId);
            LoginResult loginResult = new LoginResult();
            loginResult.setToken(sysUserService.createToken(userId, serviceApplyVo.getMainUserId()));
            loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
            return loginResult;
        }
        return new LoginResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enableServiceOwner(Long id) {
        ServiceOwnerDto serviceOwnerDto = serviceOwnerMapper.selectServiceOwnerById(id);
        if(serviceOwnerDto == null){
            throw new ApiException("服务商不存在");
        }
        if(serviceOwnerDto.getStatus() == 0){
            throw new ApiException("服务商已启用");
        }
        ServiceOwner serviceOwner = BeanUtil.copyProperties(serviceOwnerDto, ServiceOwner.class);
        serviceOwner.setStatus(0);
        serviceOwner.setUpdateTime(DateUtils.getNowDate());
        int result = serviceOwnerMapper.updateServiceOwner(serviceOwner);
        // todo 启用服务商，需要添加角色权限 、添加用户
        List<SysMenuDto> menuList = xgwcBrandRoleMapper.findMenuByModelType(ModelTypeConstant.SERVICE_ADMIN);
        List<Long> menuIds = menuList.stream().map(SysMenuDto::getId).collect(Collectors.toList());
        StaffDto staffDto = new StaffDto();
        staffDto.setName(serviceOwnerDto.getContact());
        staffDto.setServiceOwnerId(serviceOwnerDto.getId());
        staffDto.setBindUserId(serviceOwnerDto.getUserId());
        staffDto.setLoginPhone(serviceOwnerDto.getManagerPhone());
        staffDto.setMenuIds(menuIds);
        serviceStaffFeign.saveServiceStaff(staffDto);
        // 清除缓存
        redisUtil.remove(UserCacheKey.LOAD_USERBYUSERID + serviceOwnerDto.getUserId());
        return result;
    }

    @Override
    public ServiceOwnerDto selectServiceOwnerByUserId(Long userId) {
        return serviceOwnerMapper.selectServiceOwnerByUserId(userId);
    }

    @Override
    public List<ServiceOwnerSimpleDto> selectServiceOwnerListForSelect() {
        List<ServiceOwnerSimpleDto> list = serviceOwnerMapper.findServiceOwnerListForSelect(1);
        if(!list.isEmpty()){
            for(ServiceOwnerSimpleDto item:list){
                if(StringUtils.isNotEmpty(item.getManagerPhone())){
                    item.setManagerPhone(ParamDecryptUtil.decryptParam(item.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
                }
            }
        }
        return list;
    }

    @Override
    public List<ServiceOwnerSimpleDto> selectSalesServiceOwnerListForSelect() {
        List<ServiceOwnerSimpleDto> list = serviceOwnerMapper.findServiceOwnerListForSelect(2);
        if(!list.isEmpty()){
            for(ServiceOwnerSimpleDto item:list){
                if(StringUtils.isNotEmpty(item.getManagerPhone())){
                    item.setManagerPhone(ParamDecryptUtil.decryptParam(item.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
                }
            }
        }
        return list;
    }

}
