package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.XgwcEmployeesCountDto;
import com.xgwc.user.entity.dto.XgwcEmployeesDto;
import com.xgwc.user.entity.vo.XgwcEmployeesQueryVo;
import com.xgwc.user.entity.vo.XgwcEmployeesVo;

public interface IEmployeesService  {
    /**
     * 查询员工档案
     * 
     * @param employeeId 员工档案主键
     * @return 员工档案
     */
    public XgwcEmployeesDto selectEmployeesByEmployeeId(Long employeeId);

    /**
     * 查询员工档案列表
     *
     * @param employees 员工档案
     * @return 员工档案集合
     */
    public XgwcEmployeesCountDto selectEmployeesList(XgwcEmployeesQueryVo employees);

    /**
     * 新增员工档案
     *
     * @param employees 员工档案
     * @return 结果
     */
    public ApiResult insertEmployees(XgwcEmployeesVo employees);

    /**
     * 修改员工档案
     *
     * @param employees 员工档案
     * @return 结果
     */
    public ApiResult updateEmployees(XgwcEmployeesVo employees);

    /**
     * 员工申请离职
     *
     * @param employees 员工档案
     * @return 结果
     */
    int resignationsEmployees(XgwcEmployeesVo employees);
}
