package com.xgwc.user.service;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.user.entity.dto.XgwcBrandDeptDto;
import com.xgwc.user.entity.param.XgwcBrandDeptParam;
import com.xgwc.user.entity.vo.XgwcBrandDeptInfo;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-24  10:00
 */
public interface XgwcBrandDeptService {

    /**
     * 根据条件查询品牌部门信息
     * @param xgwcBrandDeptParam 查询条件
     * @return 品牌部门信息
     */
    List<XgwcBrandDeptInfo> getXgwcBrandDeptList(XgwcBrandDeptParam xgwcBrandDeptParam);

    /**
     * 保存品牌部门信息
     * @param xgwcBrandDeptDto 保存品牌部门信息
     * @return 保存品牌部门信息
     */
    ApiResult saveXgwcBrandDept(XgwcBrandDeptDto xgwcBrandDeptDto);

    /**
     * 通过用户id查询部门
     * @param userId
     * @return
     */
    ApiResult getUserDeptByUserId(Long userId);

    /**
     * 根据部门id查询部门信息
     * @param deptId 部门id
     * @return 部门信息
     */
    ApiResult getXgwcBrandDeptById(Long deptId);

    /**
     * 根据部门id更新部门信息
     * @param xgwcBrandDeptDto 更新部门信息
     * @return 更新部门信息
     */
    ApiResult updateXgwcBrandDeptById(XgwcBrandDeptDto xgwcBrandDeptDto);

    /**
     * 根据部门id更新部门状态
     * @param deptId 部门id
     * @param status 状态
     * @return 更新部门状态
     */
    ApiResult updateStatusById(Integer deptId, Integer status);

    /**
     * 根据部门id查询部门信息
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    ApiResult getXgwcDeptStaffInfo(Integer deptId);

    /**
     * 更新部门负责人、助理信息
     *
     * @param xgwcBrandDeptDto 更新部门信息
     * @return 更新部门信息
     */
    ApiResult updateXgwcDeptStaffInfo(XgwcBrandDeptDto xgwcBrandDeptDto);

    /**
     * 查询部门树结构（一级目录为所属公司）
     *
     * @return 部门树结构
     */
    List<XgwcBrandDeptInfo> getXgwcBrandDeptAndCompanyList();
}
