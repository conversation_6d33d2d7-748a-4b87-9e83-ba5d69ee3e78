package com.xgwc.user.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class FranchiseDesignerRecordQueryVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("所属品牌id")
    private Long brandId;

    @FieldDesc("业务id")
    private Long businessId;

    @FieldDesc("业务类型：1:加盟商记录，2:设计师记录")
    private Long businessType;

    @FieldDesc("申请状态")
    private Long checkStatus;

    @FieldDesc("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  checkTime;

    @FieldDesc("创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  createTime;

    @FieldDesc("主键id")
    private Long id;

    @FieldDesc("行修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  modifyTime;

    @FieldDesc("审核原因")
    private String reason;

    @FieldDesc("修改人")
    private String updateBy;

    @FieldDesc("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[]  updateTime;



}
