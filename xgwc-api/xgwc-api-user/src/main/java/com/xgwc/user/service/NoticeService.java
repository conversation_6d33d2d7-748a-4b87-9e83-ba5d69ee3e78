package com.xgwc.user.service;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.user.entity.dto.notice.BrandNoticeClassifyCountDto;
import com.xgwc.user.entity.dto.notice.NoticeDto;
import com.xgwc.user.entity.dto.notice.SelfNoticeDto;
import com.xgwc.user.entity.dto.notice.SimpleBrandDto;
import com.xgwc.user.entity.vo.*;

import java.util.List;

public interface NoticeService {

    /**
     * 插入通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int insertNotice(NoticeVo noticeVo);

    /**
     * 修改通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int updateNotice(NoticeVo noticeVo);

    /**
     * 删除通知
     * @param noticeVo 参数
     * @return 是否成功
     */
    int deleteNotice(NoticeVo noticeVo);

    /**
     * 获取通知列表
     * @param selectNoticeVo 参数
     * @return 通知列表
     */
    List<NoticeDto> getNoticeList(SelectNoticeVo selectNoticeVo);

    /**
     * 获取通知详情
     * @param noticeVo 参数
     * @return 通知详情
     */
    NoticeDto getNoticeDetail(NoticeVo noticeVo);

    /**
     * 获取通知详情
     * @param noticeVo 参数
     * @return 通知详情
     */
    SelfNoticeDto getSelfNoticeDetail(NoticeVo noticeVo);
    /**
     * 获取品牌商通知列表
     * @param classifyId 分类ID
     */
    List<SelfNoticeDto> getBrandSelfNoticeList(String classifyId);

    /**
     * 获取设计师通知列表
     * @param brandId 品牌ID
      */
    JSONObject getDesignerSelfNotice(Long brandId, String classifyId);

    /**
     * 获取加盟商通知列表
     * @param brandId 品牌ID
     */
    JSONObject getFranchiseSelfNotice(Long brandId, String classifyId);

    /**
     * 已读通知
     * @param noticeId 通知id
     * @return 是否已读
     */
    int read(Integer noticeId);

    /**
     * 根据用户获取标签数量
     * @return 标签数
     */
    List<BrandNoticeClassifyCountDto> getBrandNoticeClassifyCountDtoByUserId();

    /**
     * 根据用户ID获取品牌分组
     * @return 品牌
     */
    List<SimpleBrandDto> getBrandGroupByUserId();

    /**
     * 根据用户ID获取品牌分组
     * @return 品牌
     */
    List<SimpleBrandDto> getBrandGroupByFranchiseId();

    /**
     * 通知id
     * @param noticeId 通知id
     */
    void sendScheduleNotice(Integer noticeId);

    /**
     * 批量插入消息
     */
    void batchMessage(List<Long> userIdList, NoticeDto noticeDto);
}
