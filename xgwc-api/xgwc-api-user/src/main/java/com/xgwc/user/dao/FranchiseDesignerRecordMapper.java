package com.xgwc.user.dao;

import java.util.List;
import com.xgwc.user.entity.FranchiseDesignerRecord;
import com.xgwc.user.entity.vo.FranchiseDesignerRecordVo;
import com.xgwc.user.entity.dto.FranchiseDesignerRecordDto;
import com.xgwc.user.entity.vo.FranchiseDesignerRecordQueryVo;
import org.apache.ibatis.annotations.Param;


public interface FranchiseDesignerRecordMapper  {
    /**
     * 查询加盟商设计师审核记录
     * 
     * @param id 加盟商设计师审核记录主键
     * @return 加盟商设计师审核记录
     */
    public FranchiseDesignerRecordDto selectFranchiseDesignerRecordById(Long id);

    /**
     * 查询加盟商设计师审核记录列表
     * 
     * @param franchiseDesignerRecord 加盟商设计师审核记录
     * @return 加盟商设计师审核记录集合
     */
    public List<FranchiseDesignerRecordDto> selectFranchiseDesignerRecordList(FranchiseDesignerRecordQueryVo franchiseDesignerRecord);

    /**
     * 新增加盟商设计师审核记录
     * 
     * @param franchiseDesignerRecord 加盟商设计师审核记录
     * @return 结果
     */
    public int insertFranchiseDesignerRecord(FranchiseDesignerRecord franchiseDesignerRecord);

    /**
     * 修改加盟商设计师审核记录
     * 
     * @param franchiseDesignerRecord 加盟商设计师审核记录
     * @return 结果
     */
    public int updateFranchiseDesignerRecord(FranchiseDesignerRecord franchiseDesignerRecord);

    /**
     * 删除加盟商设计师审核记录
     * 
     * @param id 加盟商设计师审核记录主键
     * @return 结果
     */
    public int deleteFranchiseDesignerRecordById(Long id);

    /**
     * 批量删除加盟商设计师审核记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFranchiseDesignerRecordByIds(Long[] ids);

    /**
     * 根据业务ID查询加盟商设计师审核记录列表
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 加盟商设计师审核记录集合
     */
    public List<FranchiseDesignerRecordDto> selectFranchiseDesignerRecordListByBusinessId(@Param("businessId") Long businessId,@Param("businessType") Integer businessType);
}
