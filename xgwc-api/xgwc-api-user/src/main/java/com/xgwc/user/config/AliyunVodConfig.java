package com.xgwc.user.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "aliyun.vod")
@RefreshScope
public class AliyunVodConfig {

    /**
     * AccessKey
     */
    private String accessKeyId;
    /**
     * AccessKey秘钥
     */
    private String accessKeySecret;

    /**
     * 模板ID
     */
    private String templateId;
}
