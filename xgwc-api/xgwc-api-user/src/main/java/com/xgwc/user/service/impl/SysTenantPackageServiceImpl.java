package com.xgwc.user.service.impl;

import java.util.List;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import com.xgwc.common.util.DateUtils;
import org.springframework.stereotype.Service;

import com.xgwc.user.dao.SysTenantPackageMapper;
import com.xgwc.user.service.ISysTenantPackageService;
import com.xgwc.user.entity.SysTenantPackage;
import com.xgwc.user.entity.vo.SysTenantPackageVo;
import com.xgwc.user.entity.dto.SysTenantPackageDto;
import com.xgwc.user.entity.vo.SysTenantPackageQueryVo;


@Service
public class SysTenantPackageServiceImpl implements ISysTenantPackageService 
{
    @Resource
    private SysTenantPackageMapper sysTenantPackageMapper;

    /**
     * 查询租户套餐管理
     * 
     * @param id 租户套餐管理主键
     * @return 租户套餐管理
     */
    @Override
    public SysTenantPackageDto selectSysTenantPackageById(Long id)
    {
        return sysTenantPackageMapper.selectSysTenantPackageById(id);
    }

    /**
     * 查询租户套餐管理列表
     * 
     * @param sysTenantPackage 租户套餐管理
     * @return 租户套餐管理
     */
    @Override
    public List<SysTenantPackageDto> selectSysTenantPackageList(SysTenantPackageQueryVo sysTenantPackage)
    {
        return sysTenantPackageMapper.selectSysTenantPackageList(sysTenantPackage);
    }

    /**
     * 新增租户套餐管理
     * 
     * @param dto 租户套餐管理
     * @return 结果
     */
    @Override
    public int insertSysTenantPackage(SysTenantPackageVo dto)
    {

        SysTenantPackage sysTenantPackage = BeanUtil.copyProperties(dto, SysTenantPackage.class);
        sysTenantPackage.setCreateTime(DateUtils.getNowDate());
        return sysTenantPackageMapper.insertSysTenantPackage(sysTenantPackage);
    }

    /**
     * 修改租户套餐管理
     * 
     * @param dto 租户套餐管理
     * @return 结果
     */
    @Override
    public int updateSysTenantPackage(SysTenantPackageVo dto)
    {

        SysTenantPackage sysTenantPackage = BeanUtil.copyProperties(dto, SysTenantPackage.class);
        sysTenantPackage.setUpdateTime(DateUtils.getNowDate());
        return sysTenantPackageMapper.updateSysTenantPackage(sysTenantPackage);
    }

    /**
     * 批量删除租户套餐管理
     * 
     * @param ids 需要删除的租户套餐管理主键
     * @return 结果
     */
    @Override
    public int deleteSysTenantPackageByIds(Long[] ids)
    {
        return sysTenantPackageMapper.deleteSysTenantPackageByIds(ids);
    }

    /**
     * 删除租户套餐管理信息
     * 
     * @param id 租户套餐管理主键
     * @return 结果
     */
    @Override
    public int deleteSysTenantPackageById(Long id)
    {
        return sysTenantPackageMapper.deleteSysTenantPackageById(id);
    }
}
