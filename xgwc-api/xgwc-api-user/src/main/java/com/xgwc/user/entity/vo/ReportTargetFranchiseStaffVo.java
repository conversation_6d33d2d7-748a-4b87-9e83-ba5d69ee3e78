package com.xgwc.user.entity.vo;

import com.xgwc.user.entity.ReportTargetFranchiseStaff;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportTargetFranchiseStaffVo {

   @NotNull(message = "品牌商ID不能为空")
   private Long brandId;

   @NotNull(message = "目标ID不能为空")
   private Long targetId;

   @NotNull(message = "目标日期不能为空")
   private String targetDate;

   @NotNull(message = "部门目标id不能为空")
   private Long targetDeptId;

   @NotNull(message = "员工数据不能为空")
   private List<ReportTargetFranchiseStaff> staffList;

}
