package com.xgwc.user.controller;

import com.xgwc.common.annotation.Log;
import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.user.entity.dto.SysDictDataDto;
import com.xgwc.user.entity.vo.SysDictDataQueryVo;
import com.xgwc.user.entity.vo.SysDictDataVo;
import com.xgwc.user.feign.api.SysDictDataFeign;
import com.xgwc.user.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/user/dictData")
public class SysDictDataController extends BaseController implements SysDictDataFeign {
    @Autowired
    private ISysDictDataService sysDictDataService;

    /**
     * 查询字典数据列表
     */
    @MethodDesc("查询字典数据列表")
    @PreAuthorize("@ss.hasPermission('user:dictData:list')")
    @GetMapping("/list")
    public ApiResult<SysDictDataDto> list(SysDictDataQueryVo sysDictData) {
        startPage();
        List<SysDictDataDto> list = sysDictDataService.selectSysDictDataList(sysDictData);
        return getDataTable(list);
    }


    /**
     * 获取字典数据详细信息
     */
    @MethodDesc("获取字典数据详细信息")
//    @PreAuthorize("@ss.hasPermission('user:dictData:query')")
    @GetMapping(value = "/{dictCode}")
    public ApiResult<SysDictDataDto> getInfo(@PathVariable("dictCode") Long dictCode) {
        return success(sysDictDataService.selectSysDictDataByDictCode(dictCode));
    }

    @MethodDesc("获取字典数据")
    @GetMapping(value = "/type/{dictType}")
    public ApiResult<SysDictDataDto> dictType(@PathVariable("dictType") String dictType,@RequestParam(value = "brandId", required = false) Long brandId) {
        return success(sysDictDataService.selectSysDictDataByType(dictType,brandId));
    }

    /**
     * 新增字典数据
     */
    @MethodDesc("新增字典数据")
    @PreAuthorize("@ss.hasPermission('user:dictData:add')")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody SysDictDataVo sysDictData) {
        return toAjax(sysDictDataService.insertSysDictData(sysDictData));
    }

    /**
     * 修改字典数据
     */
    @MethodDesc("修改字典数据")
    @PreAuthorize("@ss.hasPermission('user:dictData:edit')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody SysDictDataVo sysDictData) {
        return toAjax(sysDictDataService.updateSysDictData(sysDictData));
    }

    @MethodDesc("禁用/启用")
    @PreAuthorize("@ss.hasPermission('user:dictData:status')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{dictCode}")
    public ApiResult status(@PathVariable("dictCode") Long[] dictCode, @RequestBody Integer status) {
        return toAjax(sysDictDataService.updateStatus(dictCode,status));
    }
     /**
     * 删除字典数据
     */
    @MethodDesc("删除字典数据")
    @PreAuthorize("@ss.hasPermission('user:dictData:remove')")
    @Log(title = "字典数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dictCodes}")
    public ApiResult remove(@PathVariable Long[] dictCodes) {
        return toAjax(sysDictDataService.deleteSysDictDataByDictCodes(dictCodes));
    }

}
