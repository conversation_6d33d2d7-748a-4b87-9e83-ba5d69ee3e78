package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

/**
 * 关联员工档案-工资账户表
 */
@Data
public class XgwcEmpAccounts {

private static final long serialVersionUID=1L;

    /** 账户唯一标识 */
    private Long accountId;

    /** 员工唯一标识 */
    private Long employeeId;

    /** 开户名 */
    private String accountName;

    /** 开户行 */
    private String bankName;

    /** 银行卡号 */
    private String accountNumber;

    /** 支付宝姓名 */
    private String alipayName;

    /** 支付宝账号 */
    private String alipayAccount;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    private Date modifyTime;



}