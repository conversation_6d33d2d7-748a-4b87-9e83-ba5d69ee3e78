package com.xgwc.user.service.impl;

import com.xgwc.user.dao.SysOperLogMapper;
import com.xgwc.user.entity.dto.SysOperLogDto;
import com.xgwc.user.entity.vo.SysOperLogQueryVo;
import com.xgwc.user.feign.entity.SysOperLog;
import com.xgwc.user.service.ISysOperLogService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SysOperLogServiceImpl implements ISysOperLogService 
{
    @Resource
    private SysOperLogMapper sysOperLogMapper;

    /**
     * 查询操作日志
     * 
     * @param operId 操作日志主键
     * @return 操作日志
     */
    @Override
    public SysOperLogDto selectSysOperLogByOperId(Long operId)
    {
        return sysOperLogMapper.selectSysOperLogByOperId(operId);
    }

    /**
     * 查询操作日志列表
     * 
     * @param sysOperLog 操作日志
     * @return 操作日志
     */
    @Override
    public List<SysOperLogDto> selectSysOperLogList(SysOperLogQueryVo sysOperLog)
    {
        return sysOperLogMapper.selectSysOperLogList(sysOperLog);
    }

    /**
     * 新增操作日志
     * 
     * @param sysOperLog 操作日志
     * @return 结果
     */
    @Override
    public int insertSysOperLog(SysOperLog sysOperLog)
    {

        return sysOperLogMapper.insertSysOperLog(sysOperLog);
    }


    /**
     * 批量删除操作日志
     * 
     * @param operIds 需要删除的操作日志主键
     * @return 结果
     */
    @Override
    public int deleteSysOperLogByOperIds(Long[] operIds)
    {
        return sysOperLogMapper.deleteSysOperLogByOperIds(operIds);
    }

    /**
     * 删除操作日志信息
     * 
     * @param operId 操作日志主键
     * @return 结果
     */
    @Override
    public int deleteSysOperLogByOperId(Long operId)
    {
        return sysOperLogMapper.deleteSysOperLogByOperId(operId);
    }
}
