package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.constants.ModelTypeConstant;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.feign.api.OrderFeign;
import com.xgwc.order.feign.entity.XgwcBusinessVo;
import com.xgwc.user.dao.DesignerMapper;
import com.xgwc.user.dao.SysUserMiddleMapper;
import com.xgwc.user.dao.UserMapper;
import com.xgwc.user.entity.Designer;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.*;
import com.xgwc.user.security.constants.SecurityConstants;
import com.xgwc.user.service.*;
import com.xgwc.user.util.AutoIdUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DesignerServiceImpl implements IDesignerService {
    @Resource
    private DesignerMapper designerMapper;
    @Resource
    private SmsService smsService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private IFranchiseDesignerRecordService franchiseDesignerRecordService;
    @Resource
    private IApplyRecordService applyRecordService;
    @Resource
    private OrderFeign orderFeign;
    @Resource
    private UserMapper userMapper;
    @Resource
    private SysUserMiddleMapper sysUserMiddleMapper;

    /**
     * 查询设计师管理
     * 
     * @param designerId 设计师管理主键
     * @return 设计师管理
     */
    @Override
    public DesignerVO selectDesignerByDesignerId(Long designerId) {
        DesignerVO designerVO = designerMapper.selectDesignerByDesignerId(designerId);
        if (designerVO != null) {
            designerDecrypt(designerVO);
            List<FranchiseDesignerRecordDto> franchiseDesignerRecordListByBusinessId = franchiseDesignerRecordService.findFranchiseDesignerRecordListByBusinessId(designerId, 2);
            designerVO.setDesignerAuditRecord(franchiseDesignerRecordListByBusinessId);
        }
        return designerVO;
    }

    /**
     * 查询设计师管理列表
     * 
     * @param designer 设计师管理
     * @return 设计师管理
     */
    @Override
    public List<DesignerVO> selectDesignerList(DesignerQueryDto designer) {
        if(StringUtils.isNotEmpty(designer.getManagerPhone())){
            designer.setManagerPhone(ParamDecryptUtil.encrypt(designer.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        List<DesignerVO> list = designerMapper.selectDesignerList(designer);
        if(!list.isEmpty()){
            for (DesignerVO item : list) {
                item.setManagerPhone(ParamDecryptUtil.decryptParam(item.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
            }
        }
        return list;
    }

    /**
     * 新增设计师管理
     * @param dto 设计师管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResult insertDesigner(DesignerDto dto) {
        Long brandId = dto.getBrandId() != null ? dto.getBrandId() : 1L;
        String encryptedPhone = ParamDecryptUtil.encrypt(dto.getManagerPhone(), ParamDecryptUtil.PHONE_KEY);
        Long mainUserId = dto.getMainUserId();
        Long managerUserId = dto.getManagerUserId();
        // 场景一：已有主账号（mainUserId），通过中间表绑定设计师身份
        if (mainUserId != null) {
            SysUserMiddleDto middle = sysUserMiddleMapper.selectSysUserMiddleByUserIdAndUserType(mainUserId, 3);
            if (middle == null) {
                // 1. 插入中间表
                SysUserMiddleVo middleVo = createSysUserMiddle(mainUserId, 3);
                // 2. 校验设计师是否已存在
                DesignerVO existing = designerMapper.findDesignerByManagerPhone(encryptedPhone, brandId);
                validateFranchiseOwnerExist(existing);
                // 3. 构建设计师对象
                Designer designer = buildDesigner(dto, encryptedPhone, middleVo.getId(), brandId);
                // 4. 获取业务层级编码
                setDesignerBusinessCode(designer, dto.getGoodBusiness());
                // 5. 插入设计师主体
                int i = designerMapper.insertDesigner(designer);
                // 6. 更新中间表来源 sourceId
                sysUserService.updateSysUserMiddleSourceId(designer.getManagerUserId(), designer.getDesignerId());
                // 7. 插入申请记录
                insertApplyRecord(designer.getManagerUserId(), designer.getDesignerId(), brandId);
                // 8. 创建token
                LoginResult loginResult = new LoginResult();
                loginResult.setToken(sysUserService.createToken(middleVo.getId(), middleVo.getMainUserId()));
                loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
                return loginResult;
            }
        }
        // 场景二：首次注册（注册用户 + 建设计师主体）
        validatePhoneNotExists(managerUserId, encryptedPhone, brandId);
        validateSmsIfFirstTime(managerUserId, dto.getManagerPhone(), dto.getCode());
        DesignerVO designerVO = designerMapper.findDesignerByManagerPhone(encryptedPhone, brandId);
        validateFranchiseOwnerExist(designerVO);
        Designer designer = buildDesigner(dto, encryptedPhone, managerUserId, brandId);
        setDesignerBusinessCode(designer, dto.getGoodBusiness());
        // 注册用户（首次注册）
        if (designerVO == null && managerUserId == null) {
            managerUserId = registerNewSysUser(dto, encryptedPhone, brandId, 3);
            designer.setManagerUserId(managerUserId);
        }
        int i = designerMapper.insertDesigner(designer);
        insertApplyRecord(designer.getManagerUserId(), designer.getDesignerId(), brandId);
        // 创建token
        SysUserMiddleDto middleVo = sysUserMiddleMapper.selectSysUserMiddleById(designer.getManagerUserId());
        LoginResult loginResult = new LoginResult();
        loginResult.setToken(sysUserService.createToken(middleVo.getUserId(), middleVo.getMainUserId()));
        loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
        return loginResult;
    }

    /**
     * 校验设计师记录是否已存在且未被驳回
     * @param designerVO 设计师VO对象
     * @throws ApiException 如果存在且状态不是“驳回”
     */
    private void validateFranchiseOwnerExist(DesignerVO designerVO) {
        if (designerVO != null && designerVO.getCheckStatus() != null && designerVO.getCheckStatus() != 3) {
            throw new ApiException(ApiStatusEnums.DESIGNER_DATA_EXISTS.getMessage(), ApiStatusEnums.DESIGNER_DATA_EXISTS.getStatus());
        }
    }

    /**
     * 创建中间表记录（sys_user_middle）
     * @param mainUserId 主账号ID
     * @param userType 用户类型（如加设计师为3）
     * @return 插入后的中间表VO对象
     */
    private SysUserMiddleVo createSysUserMiddle(Long mainUserId, int userType) {
        SysUserMiddleVo vo = new SysUserMiddleVo();
        vo.setId(AutoIdUtil.getId());
        vo.setMainUserId(mainUserId);
        vo.setUserType(userType);
        sysUserMiddleMapper.insertSysUserMiddle(vo);
        return vo;
    }


    /**
     * 校验手机号是否已存在于用户表（用于注册时）
     */
    private void validatePhoneNotExists(Long managerUserId, String encryptedPhone, Long brandId) {
        SysUser user = userMapper.findUserByPhone(encryptedPhone);
        if (user != null && managerUserId == null) {
            throw new ApiException(ApiStatusEnums.AUTH_USER_PHONE_EXISTS.getMessage(), ApiStatusEnums.AUTH_USER_PHONE_EXISTS.getStatus());
        }
    }

    /**
     * 若是首次注册用户，则校验短信验证码
     */
    private void validateSmsIfFirstTime(Long managerUserId, String phone, String code) {
        if (managerUserId == null) {
            boolean valid = smsService.validRegisterCode(phone, code);
            if (!valid) {
                throw new ApiException(ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getMessage(), ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getStatus());
            }
        }
    }

    /**
     * 添加申请记录
     */
    private void insertApplyRecord(Long userId, Long businessId, Long brandId) {
        ApplyRecordVo record = new ApplyRecordVo();
        record.setUserId(userId);
        record.setBusinessType(2);
        record.setBusinessId(businessId);
        record.setBrandId(brandId);
        record.setCheckStatus(0); // 默认未审核
        applyRecordService.insertApplyRecord(record);
    }


    private Designer buildDesigner(DesignerDto dto, String encryptedPhone, Long managerUserId, Long brandId) {
        Designer d = BeanUtil.copyProperties(dto, Designer.class);
        d.setCreateTime(DateUtils.getNowDate());
        d.setCreateBy(dto.getManagerName());
        d.setCheckStatus(0);
        d.setManagerUserId(managerUserId);
        d.setBrandId(brandId);
        d.setManagerPhone(encryptedPhone);
        DesignerEncrypt(d);
        return d;
    }

    private void setDesignerBusinessCode(Designer designer, Long businessId) {
        try {
            ApiResult<XgwcBusinessVo> result = orderFeign.getXgwcBusinessByIdFeign(businessId);
            if (result.getData() != null) {
                designer.setBusinessCode(result.getData().getLevel());
            }
        } catch (Exception e) {
            log.error("远程调用业务线失败: {}", e.getMessage());
        }
    }

    private Long registerNewSysUser(DesignerDto dto, String encryptedPhone, Long brandId, Integer userType) {
        SysUserRegisterDTO registerDTO = new SysUserRegisterDTO();
        registerDTO.setPhone(encryptedPhone);
        registerDTO.setUserName(dto.getManagerName());
        registerDTO.setPassword(dto.getPassword());
        registerDTO.setUserType(userType);
        registerDTO.setBrandId(brandId);
        registerDTO.setSourceId(brandId);
        return sysUserService.addSysUser2(registerDTO);
    }

    /**
     * 加密
     * @param designer 设计师
     */
    private void DesignerEncrypt(Designer designer){
        designer.setIdcardFront(ParamDecryptUtil.encrypt(designer.getIdcardFront(), ParamDecryptUtil.DESIGNER_KEY));
        designer.setIdcardBack(ParamDecryptUtil.encrypt(designer.getIdcardBack(), ParamDecryptUtil.DESIGNER_KEY));
        designer.setIdcardNo(ParamDecryptUtil.encrypt(designer.getIdcardNo(), ParamDecryptUtil.DESIGNER_KEY));
        designer.setEmail(ParamDecryptUtil.encrypt(designer.getEmail(), ParamDecryptUtil.DESIGNER_KEY));
        designer.setBankName(ParamDecryptUtil.encrypt(designer.getBankName(), ParamDecryptUtil.DESIGNER_KEY));
        designer.setBankNo(ParamDecryptUtil.encrypt(designer.getBankNo(), ParamDecryptUtil.DESIGNER_KEY));
        designer.setZfbAccount(ParamDecryptUtil.encrypt(designer.getZfbAccount(), ParamDecryptUtil.DESIGNER_KEY));
        designer.setManagerName(ParamDecryptUtil.encrypt(designer.getManagerName(), ParamDecryptUtil.DESIGNER_KEY));
        designer.setManagerPhone(ParamDecryptUtil.encrypt(designer.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
        designer.setPhone(ParamDecryptUtil.encrypt(designer.getPhone(), ParamDecryptUtil.PHONE_KEY));
        designer.setEmergencyPhone(ParamDecryptUtil.encrypt(designer.getEmergencyPhone(), ParamDecryptUtil.PHONE_KEY));
    }

    /**
     * 解密 ParamDecryptUtil.decryptParam(sysUserDto.getPhone(), ParamDecryptUtil.PHONE_KEY)
     */
    private void designerDecrypt(DesignerVO designerVO) {
        designerVO.setIdcardFront(ParamDecryptUtil.decryptParam(designerVO.getIdcardFront(), ParamDecryptUtil.DESIGNER_KEY));
        designerVO.setIdcardBack(ParamDecryptUtil.decryptParam(designerVO.getIdcardBack(), ParamDecryptUtil.DESIGNER_KEY));
        designerVO.setIdcardNo(ParamDecryptUtil.decryptParam(designerVO.getIdcardNo(), ParamDecryptUtil.DESIGNER_KEY));
        designerVO.setEmail(ParamDecryptUtil.decryptParam(designerVO.getEmail(), ParamDecryptUtil.DESIGNER_KEY));
        designerVO.setBankName(ParamDecryptUtil.decryptParam(designerVO.getBankName(), ParamDecryptUtil.DESIGNER_KEY));
        designerVO.setBankNo(ParamDecryptUtil.decryptParam(designerVO.getBankNo(), ParamDecryptUtil.DESIGNER_KEY));
        designerVO.setZfbAccount(ParamDecryptUtil.decryptParam(designerVO.getZfbAccount(), ParamDecryptUtil.DESIGNER_KEY));
        designerVO.setManagerName(ParamDecryptUtil.decryptParam(designerVO.getManagerName(), ParamDecryptUtil.DESIGNER_KEY));
        designerVO.setManagerPhone(ParamDecryptUtil.decryptParam(designerVO.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
        designerVO.setPhone(ParamDecryptUtil.decryptParam(designerVO.getPhone(), ParamDecryptUtil.PHONE_KEY));
        designerVO.setEmergencyPhone(ParamDecryptUtil.decryptParam(designerVO.getEmergencyPhone(), ParamDecryptUtil.PHONE_KEY));
    }

    /**
     * 修改设计师管理
     * 
     * @param dto 设计师管理
     * @return 结果
     */
    @Override
    public int updateDesigner(DesignerVO dto) {
        validateDesignerExists(dto.getDesignerId());
        Designer designer = BeanUtil.copyProperties(dto, Designer.class);
        DesignerEncrypt(designer);
        designer.setUpdateTime(DateUtils.getNowDate());
        return designerMapper.updateDesigner(designer);
    }

    /**
     * 批量删除设计师管理
     * 
     * @param designerIds 需要删除的设计师管理主键
     * @return 结果
     */
    @Override
    public int deleteDesignerByDesignerIds(Long[] designerIds) {
        return designerMapper.deleteDesignerByDesignerIds(designerIds);
    }

    /**
     * 删除设计师管理信息
     * 
     * @param designerId 设计师管理主键
     * @return 结果
     */
    @Override
    public int deleteDesignerByDesignerId(Long designerId) {
        return designerMapper.deleteDesignerByDesignerId(designerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(DesignerAuditDto updateReqVO) {
        DesignerVO designerVO = designerMapper.selectDesignerByDesignerId(updateReqVO.getId());
        if (designerVO == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        Designer designer = new Designer();
        designer.setDesignerId(updateReqVO.getId());
        designer.setCheckStatus(updateReqVO.getCheckStatus());
        designer.setCheckTime(DateUtils.getNowDate());
        designer.setReason(updateReqVO.getReason());
        designerMapper.updateDesigner(designer);
        // 审核通过初始化角色
        if(updateReqVO.getCheckStatus() == 1 && designerVO.getManagerUserId() != null){
            sysUserService.initRole(designerVO.getManagerUserId(), ModelTypeConstant.DESIGNER_ADMIN,designerVO.getName(),null,3);
        }
        // 修改申请记录
        ApplyRecordDto applyRecordDto = applyRecordService.selectApplyRecordByUserId(designerVO.getManagerUserId(), designerVO.getBrandId(), designerVO.getDesignerId());
        if(applyRecordDto != null){
            applyRecordService.updateApplyRecordStatusById(applyRecordDto.getId(), updateReqVO.getCheckStatus());
        }
        // 审核记录
        FranchiseDesignerRecordVo record = new FranchiseDesignerRecordVo();
        record.setBrandId(designerVO.getDesignerId());
        record.setBusinessType(2);
        record.setBusinessId(updateReqVO.getId());
        record.setCheckStatus(updateReqVO.getCheckStatus());
        record.setReason(updateReqVO.getReason());
        record.setCheckTime(DateUtils.getNowDate());
        if(updateReqVO.getCheckStatus() != 3){
            record.setCreateBy(SecurityUtils.getNickName());
        }else {
            record.setCreateBy(designerVO.getCreateBy());
        }
        franchiseDesignerRecordService.insertFranchiseDesignerRecord(record);
    }

    @Override
    public void receive(DesignerReceiveDto updateReqVO) {
        validateDesignerExists(updateReqVO.getId());
        Designer designer = new Designer();
        designer.setDesignerId(updateReqVO.getId());
        designer.setReceiveOrderStatus(updateReqVO.getReceiveOrderStatus());
        designerMapper.updateDesigner(designer);
    }

    @Override
    public int reapply(DesignerDto designer) {
        validateDesignerExists(designer.getDesignerId());
        Designer designerEntity = BeanUtil.copyProperties(designer, Designer.class);
        DesignerEncrypt(designerEntity);
        designerEntity.setCheckStatus(0);
        designerEntity.setCheckTime(null);
        designerEntity.setReason(null);
        designerEntity.setUpdateTime(DateUtils.getNowDate());
        // 修改申请记录
        ApplyRecordDto applyRecordDto = applyRecordService.selectApplyRecordByUserId(designer.getManagerUserId(), designer.getBrandId(), designer.getDesignerId());
        if(applyRecordDto != null){
            applyRecordService.updateApplyRecordStatusById(applyRecordDto.getId(), 0);
        }
        return designerMapper.updateDesigner(designerEntity);
    }

    @Override
    public List<SimpleUserInfoDto> getSimpleUserInfoListByBusinessId(Integer businessId) {
        SysUser sysUser = SecurityUtils.getSysUser();
        if(sysUser.getBrandId() == null){
            log.error("根据业务获取设计师列表失败, 该用户非品牌商员工:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        return designerMapper.getSimpleUserInfoDtoListByBusinessId(businessId, sysUser.getBrandId());
    }

    /**
     * 获取加入品牌下拉框
     * @return 下拉框
     */
    @Override
    public ApiResult getInJoinBrandDropDown() {
        DesignerQueryDto designerQueryDto = new DesignerQueryDto();
         designerQueryDto.setManagerUserId(SecurityUtils.getSysUser().getUserId());
        List<DesignerVO> designerVOList = designerMapper.selectDesignerList(designerQueryDto);
        if(!CollectionUtils.isEmpty(designerVOList)){
            List<DesignerVO.BrandInfo> uniqueBrandList = new ArrayList<>(designerVOList.stream()
                    .collect(Collectors.toMap(
                            DesignerVO::getBrandName,
                            designer -> new DesignerVO.BrandInfo(designer.getBrandName(), designer.getBrandId(),
                                    designer.getDesignerId(), designer.getName()),
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ))
                    .values());
             return ApiResult.ok(uniqueBrandList);
        }
        return null;
    }

    @Override
    public List<ClassifyDesignerTree> getClassifyDesignerTree() {
        List<ClassifyDesignerTree> resultList = new ArrayList<>();
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null) {
            log.error("非品牌商员工不能获取设计师树:{}", JSONObject.toJSONString(sysUser));
        }
        List<SimpleClassifyUserInfoDto> simpleClassifyUserInfoDtos = designerMapper.getSimpleUserInfoDtoList(brandId);
        Map<Integer, List<SimpleClassifyUserInfoDto>> classifyIdMap = new HashMap<>();
        if(simpleClassifyUserInfoDtos != null && !simpleClassifyUserInfoDtos.isEmpty()){
            for(SimpleClassifyUserInfoDto simpleClassifyUserInfoDto : simpleClassifyUserInfoDtos){
                Integer classifyId = simpleClassifyUserInfoDto.getBusinessId();
                List<SimpleClassifyUserInfoDto> childs;
                if(classifyIdMap.get(classifyId) != null){
                    childs = classifyIdMap.get(classifyId);
                }else{
                    childs = new ArrayList<>();
                }
                childs.add(simpleClassifyUserInfoDto);
                classifyIdMap.put(classifyId, childs);
            }
            //第二次循环保证顺序
            Set<Integer> classifyIdSet = new HashSet<>();
            for(SimpleClassifyUserInfoDto simpleClassifyUserInfoDto : simpleClassifyUserInfoDtos){
                Integer businessId = simpleClassifyUserInfoDto.getBusinessId();
                if(!classifyIdSet.contains(businessId)){
                    ClassifyDesignerTree classifyDesignerTree = new ClassifyDesignerTree();
                    classifyDesignerTree.setBusinessId(businessId);
                    classifyDesignerTree.setBusinessName(simpleClassifyUserInfoDto.getBusinessName());
                    classifyDesignerTree.setUsers(classifyIdMap.get(businessId));
                    classifyIdSet.add(businessId);
                    resultList.add(classifyDesignerTree);
                }
            }
        }
        return resultList;
    }

    @Override
    public List<DesignerVO> selectFranchiseeDesignerList(DesignerQueryDto designerQueryDto) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long franchiseId = sysUser.getFranchiseId();
        if(franchiseId == null) return List.of();
        designerQueryDto.setFranchiseId(franchiseId);
        if(StringUtils.isNotEmpty(designerQueryDto.getManagerPhone())){
            designerQueryDto.setManagerPhone(ParamDecryptUtil.encrypt(designerQueryDto.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        List<DesignerVO> franchiseeDesignerList = designerMapper.findFranchiseeDesignerList(designerQueryDto);
        if(franchiseeDesignerList != null && !franchiseeDesignerList.isEmpty()){
            for (DesignerVO item : franchiseeDesignerList) {
                item.setManagerPhone(ParamDecryptUtil.decryptParam(item.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
            }
        }
        return franchiseeDesignerList;
    }

    @Override
    public int updateSpecialty(DesignerSpecialtyUpdateDto updateReqVO) {
        validateDesignerExists(updateReqVO.getId());
        Designer designer = new Designer();
        designer.setDesignerId(updateReqVO.getId());
        designer.setDescription(updateReqVO.getDescription());
        return designerMapper.updateDesigner(designer);
    }

    @Override
    public int setGrade(DesignerGradeUpdateDto updateReqVO) {
        validateDesignerExists(updateReqVO.getId());
        Designer designer = new Designer();
        designer.setDesignerId(updateReqVO.getId());
        designer.setDesignerLevel(updateReqVO.getDesignerLevel());
        designer.setCompanyId(updateReqVO.getCompanyId());
        designer.setWechatId(updateReqVO.getWechatId());
        designer.setReceiveOrderStatus(updateReqVO.getReceiveOrderStatus());
        return designerMapper.updateDesigner(designer);
    }

    @Override
    public int updateStatus(DesignerStatusUpdateDto updateReqVO) {
        validateDesignerExists(updateReqVO.getId());
        Designer designer = new Designer();
        designer.setDesignerId(updateReqVO.getId());
        designer.setReceiveOrderStatus(updateReqVO.getReceiveOrderStatus());
        return designerMapper.updateDesigner(designer);
    }

    /**
     * 查询申请记录
     * @param designer 查询参数
     * @return 申请记录
     */
    @Override
    public List<DesignerVO> findApplyLogList(DesignerQueryDto designer) {
        Long userId = SecurityUtils.getSysUser().getUserId();
        designer.setManagerUserId(userId);
        return designerMapper.selectDesignerList(designer);
    }

    @Override
    public List<DesignerSimpleVO> getDesignerDropdownByBusinessId(Long businessId) {
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        if(franchiseId == null){
            return List.of();
        }
        List<DesignerSimpleVO> list = designerMapper.findDesignerDropdownByBusinessId(businessId, franchiseId);
        if(!list.isEmpty()){
            for (DesignerSimpleVO item : list) {
                item.setPhone(ParamDecryptUtil.decryptParam(item.getPhone(), ParamDecryptUtil.PHONE_KEY));
            }
        }
        return list;
    }

    @Override
    public List<DesignerSimpleVO> getBrandDesignerDropdown() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null){
            return List.of();
        }
        return designerMapper.findBrandDesignerDropdown(brandId);
    }

    @Override
    public List<DesignerSimpleVO> getBrandDesignerByBusinessId(Long businessId) {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null){
            return List.of();
        }
        List<DesignerSimpleVO> list = designerMapper.findBrandDesignerByBusinessId(businessId, brandId);
        if(!list.isEmpty()){
            for (DesignerSimpleVO item : list) {
                item.setPhone(ParamDecryptUtil.decryptParam(item.getPhone(), ParamDecryptUtil.PHONE_KEY));
            }
        }
        return list;
    }

    @Override
    public Map<String, Integer> statistics() {
        Long brandId = SecurityUtils.getSysUser().getBrandId();
        if(brandId == null)return Map.of();
        Map<String, Integer> map = new HashMap<>();
        // 审核中
        map.put("checkCount", designerMapper.countApplyDesignerByBrandId(brandId, 0));
        // 拒绝
        map.put("rejectCount", designerMapper.countApplyDesignerByBrandId(brandId, 2));
        // 正常接单
        map.put("normalCount", designerMapper.countPassApplyDesignerByBrandId(brandId, 0));
        // 暂停接单
        map.put("pauseCount", designerMapper.countPassApplyDesignerByBrandId(brandId, 1));
        // 离职
        map.put("leaveCount", designerMapper.countPassApplyDesignerByBrandId(brandId, 2));
        return map;
    }

    @Override
    public Map<String, Integer> franchiseeStatistics() {
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        if(franchiseId == null)return Map.of();
        Map<String, Integer> map = new HashMap<>();
        // 正常接单数量
        map.put("normalCount", designerMapper.countFranchiseeDesignerByFranchiseId(franchiseId, 0));
        // 暂停接单数量
        map.put("pauseCount", designerMapper.countFranchiseeDesignerByFranchiseId(franchiseId, 1));
        // 离职数量
        map.put("leaveCount", designerMapper.countFranchiseeDesignerByFranchiseId(franchiseId, 2));
        return map;
    }

    @Override
    public List<Map<String, Object>> getBrandDesignerForBrand(Long brandId) {
        if (brandId == null) {
            brandId = SecurityUtils.getSysUser().getBrandId();
        }
        List<DesignerSimpleVO> brandDesignerDropdown = designerMapper.findBrandDesignerDropdown(brandId);
        if (!CollectionUtils.isEmpty(brandDesignerDropdown)) {
            List<Map<String, Object>> list = new ArrayList<>(brandDesignerDropdown.size());
            brandDesignerDropdown.forEach(item -> {
                String phone = ParamDecryptUtil.decryptParam(item.getPhone(), ParamDecryptUtil.PHONE_KEY);
                String name = item.getName();
                String businessName = item.getBusinessName();
                String result = name.trim() + "-" + businessName.trim() + "-" + phone.trim();
                Map<String, Object> map = new HashMap<>(2);
                map.put("id", item.getDesignerId());
                map.put("name", result);
                list.add(map);
            });
            return list;
        }
        return null;
    }

    private void validateDesignerExists(Long id) {
        if (designerMapper.selectDesignerByDesignerId(id) == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
    }
}
