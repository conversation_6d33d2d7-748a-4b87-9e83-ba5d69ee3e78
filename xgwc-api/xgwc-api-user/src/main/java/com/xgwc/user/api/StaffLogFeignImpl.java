package com.xgwc.user.api;

import cn.hutool.core.bean.BeanUtil;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.StaffLog;
import com.xgwc.common.entity.StaffLogDto;
import com.xgwc.user.feign.api.StaffLogFeign;
import com.xgwc.user.service.IStaffLogService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
public class StaffLogFeignImpl implements StaffLogFeign {

    @Resource
    private IStaffLogService staffLogService;

    @Override
    public ApiResult addStaffLog(StaffLog log) {
        return ApiResult.toAjax(staffLogService.insertStaffLog(log));
    }

    @Override
    public ApiResult<List<StaffLogDto>> findStaffLogByStaffIdAndBusinessType(Long staffId, int businessType) {
        List<com.xgwc.user.entity.dto.StaffLogDto> list = staffLogService.findStaffLogByStaffIdAndBusinessType(staffId, businessType);
        if (list == null || list.isEmpty()) {
            return ApiResult.ok();
        }
        List<StaffLogDto> commonStaffLogList = list.stream()
                .map(userDto -> BeanUtil.copyProperties(userDto, StaffLogDto.class))
                .collect(Collectors.toList());
        return ApiResult.ok(commonStaffLogList);
    }




}
