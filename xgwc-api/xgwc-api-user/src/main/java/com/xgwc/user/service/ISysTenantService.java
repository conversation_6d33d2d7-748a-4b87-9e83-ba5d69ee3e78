package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.SysTenant;
import com.xgwc.user.entity.vo.SysTenantVo;
import com.xgwc.user.entity.dto.SysTenantDto;
import com.xgwc.user.entity.vo.SysTenantQueryVo;

public interface ISysTenantService 
{
    /**
     * 查询租户管理
     * 
     * @param id 租户管理主键
     * @return 租户管理
     */
    public SysTenantDto selectSysTenantById(Long id);

    /**
     * 查询租户管理列表
     * 
     * @param sysTenant 租户管理
     * @return 租户管理集合
     */
    public List<SysTenantDto> selectSysTenantList(SysTenantQueryVo sysTenant);

    /**
     * 新增租户管理
     * 
     * @param sysTenant 租户管理
     * @return 结果
     */
    public int insertSysTenant(SysTenantVo sysTenant);

    /**
     * 修改租户管理
     * 
     * @param sysTenant 租户管理
     * @return 结果
     */
    public int updateSysTenant(SysTenantVo sysTenant);

    /**
     * 批量删除租户管理
     * 
     * @param ids 需要删除的租户管理主键集合
     * @return 结果
     */
    public int deleteSysTenantByIds(Long[] ids);

    /**
     * 删除租户管理信息
     * 
     * @param id 租户管理主键
     * @return 结果
     */
    public int deleteSysTenantById(Long id);
}
