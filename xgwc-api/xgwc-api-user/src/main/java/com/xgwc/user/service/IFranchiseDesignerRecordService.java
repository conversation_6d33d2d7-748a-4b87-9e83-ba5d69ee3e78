package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.FranchiseDesignerRecord;
import com.xgwc.user.entity.vo.FranchiseDesignerRecordVo;
import com.xgwc.user.entity.dto.FranchiseDesignerRecordDto;
import com.xgwc.user.entity.vo.FranchiseDesignerRecordQueryVo;
import org.apache.ibatis.annotations.Param;

public interface IFranchiseDesignerRecordService  {
    /**
     * 查询加盟商设计师审核记录
     * 
     * @param id 加盟商设计师审核记录主键
     * @return 加盟商设计师审核记录
     */
    public FranchiseDesignerRecordDto selectFranchiseDesignerRecordById(Long id);

    /**
     * 查询加盟商设计师审核记录列表
     * 
     * @param franchiseDesignerRecord 加盟商设计师审核记录
     * @return 加盟商设计师审核记录集合
     */
    public List<FranchiseDesignerRecordDto> selectFranchiseDesignerRecordList(FranchiseDesignerRecordQueryVo franchiseDesignerRecord);

    /**
     * 新增加盟商设计师审核记录
     * 
     * @param franchiseDesignerRecord 加盟商设计师审核记录
     * @return 结果
     */
    public int insertFranchiseDesignerRecord(FranchiseDesignerRecordVo franchiseDesignerRecord);

    /**
     * 修改加盟商设计师审核记录
     * 
     * @param franchiseDesignerRecord 加盟商设计师审核记录
     * @return 结果
     */
    public int updateFranchiseDesignerRecord(FranchiseDesignerRecordVo franchiseDesignerRecord);

    /**
     * 批量删除加盟商设计师审核记录
     * 
     * @param ids 需要删除的加盟商设计师审核记录主键集合
     * @return 结果
     */
    public int deleteFranchiseDesignerRecordByIds(Long[] ids);

    /**
     * 删除加盟商设计师审核记录信息
     * 
     * @param id 加盟商设计师审核记录主键
     * @return 结果
     */
    public int deleteFranchiseDesignerRecordById(Long id);

    /**
     * 根据业务ID查询加盟商设计师审核记录列表
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 加盟商设计师审核记录集合
     */
    public List<FranchiseDesignerRecordDto> findFranchiseDesignerRecordListByBusinessId(Long businessId, Integer businessType);
}
