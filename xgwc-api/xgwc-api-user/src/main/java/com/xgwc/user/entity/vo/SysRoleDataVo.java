package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.entity.vo
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-16  14:11
 */
@Data
public class SysRoleDataVo {

    /** 角色菜单关系表 */
    private Long id;

    /** 角色id */
    private Long roleId;

    /** 角色名称 */
    private String roleName;

    /** 菜单id */
    private Long menuId;

    /** 菜单名称 */
    private String menuName;

    /** 数据权限（1：全部数据 2：本部门数据 3：指定部门数据） */
    private Integer dataScope;

    /** 部门id */
    private List<Long> deptIds;

    /** 部门id */
    private Long deptId;

    /** 业务分类ids */
    private String businessIds;

    /** 限时（0-不限制时间 1-最近2个月 2-最近4个月 3-最近6个月） */
    private Integer timeLimit;

    /** 数据脱敏 （0-脱敏 1-不脱敏） */
    private Integer dataMasking;

    /** 下载次数/天 */
    private Integer downloadLimit;

    /** 下载次数/天（加盟商） */
    private List<SysRoleDateDownloadLimitVo> downloadLimitFranchise;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Long pid;

    /**
     * 创建对象
     * @param menuId 菜单id
     * @param deptIds 部门id
     * @return
     */
    public static SysRoleDataVo of(Long menuId, List<Long> deptIds) {
        SysRoleDataVo sysRoleDataVo = new SysRoleDataVo();
        sysRoleDataVo.setMenuId(menuId);
        sysRoleDataVo.setDeptIds(deptIds);
        return sysRoleDataVo;
    }
}
