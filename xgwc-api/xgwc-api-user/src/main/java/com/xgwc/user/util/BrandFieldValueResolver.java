package com.xgwc.user.util;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.xgwc.common.util.SpringUtils;
import com.xgwc.user.dao.*;
import com.xgwc.user.entity.dto.StaffDto;
import com.xgwc.user.entity.dto.SysCompanyDto;
import com.xgwc.user.entity.dto.XgwcBrandDeptDto;
import com.xgwc.user.entity.vo.XgwcBrandRoleVo;
import com.xgwc.user.entity.vo.XgwcBrandStationVo;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class BrandFieldValueResolver {

    private static final Map<String, Function<String, String>> fieldResolvers = new HashMap<>();

    static {
        fieldResolvers.put("部门", id -> {
            if (StringUtils.isBlank(id)) return "无";
            XgwcBrandDeptDto xgwcBrandDeptDto = SpringUtils.getBean(XgwcBrandDeptMapper.class).getXgwcBrandDeptById(Long.valueOf(id));
            return xgwcBrandDeptDto != null ? xgwcBrandDeptDto.getDeptName() : id;
        });

        fieldResolvers.put("岗位", id -> {
            if (StringUtils.isBlank(id)) return "无";
            XgwcBrandStationVo xgwcBrandStation = SpringUtils.getBean(XgwcBrandStationMapper.class).getXgwcBrandStationById(Integer.parseInt(id));
            return xgwcBrandStation != null ? xgwcBrandStation.getStationName() : id;
        });

        fieldResolvers.put("角色权限", ids -> {
            if (StringUtils.isBlank(ids)) return "无";
            XgwcBrandRoleMapper roleMapper = SpringUtils.getBean(XgwcBrandRoleMapper.class);
            return Arrays.stream(ids.split(","))
                    .map(id -> roleMapper.getXgwcBrandRoleById(Integer.parseInt(id)))
                    .filter(Objects::nonNull)
                    .map(XgwcBrandRoleVo::getRoleName)
                    .collect(Collectors.joining(", "));
        });

        fieldResolvers.put("工作性质", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "全职" : id.equals("1") ? "外包" : id.equals("2") ? "兼职" : id;
        });

        fieldResolvers.put("状态", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "在职" : id.equals("1") ? "离职" : id;
        });

        fieldResolvers.put("部门负责人", id -> {
            if (StringUtils.isBlank(id)) return "无";
            return id.equals("0") ? "是" : id.equals("1") ? "否" : id;
        });

        fieldResolvers.put("直属上级", id -> {
            if (StringUtils.isBlank(id)) return "无";
            StaffDto staffDto = SpringUtils.getBean(StaffMapper.class).selectStaffById(Long.parseLong(id));
            return staffDto != null ? staffDto.getStageName() : id;
        });

        fieldResolvers.put("所属公司", id -> {
            if (StringUtils.isBlank(id)) return "无";
            SysCompanyDto sysCompanyDto = SpringUtils.getBean(SysCompanyMapper.class).selectSysCompanyById(Long.parseLong(id));
            return sysCompanyDto != null ? sysCompanyDto.getName() : id;
        });
    }

    public static String resolve(String fieldName, String value) {
        Function<String, String> resolver = fieldResolvers.get(fieldName);
        return resolver != null ? resolver.apply(value) : value;
    }
}
