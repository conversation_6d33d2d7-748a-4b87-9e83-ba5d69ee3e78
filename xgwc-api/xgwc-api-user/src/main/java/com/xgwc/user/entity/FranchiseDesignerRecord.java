package com.xgwc.user.entity;

import lombok.Data;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class FranchiseDesignerRecord {

private static final long serialVersionUID=1L;

    /** 所属品牌id */
    private Long brandId;

    /** 业务id */
    private Long businessId;

    /** 业务类型：1:加盟商记录，2:设计师记录 */
    private Integer businessType;

    /** 申请状态 0审核中 1审核通过 2审核不通过 3撤回 */
    private Integer checkStatus;

    /** 审核时间 */
    private Date checkTime;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 主键id */
    private Long id;

    /** 行修改时间 */
    private Date modifyTime;

    /** 审核原因 */
    private String reason;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;



}