package com.xgwc.user.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SelectFranchiseNoticeVo {
    /**
     * 通知类型：1站内信，2短信，3企微
     */
    private Integer noticeType;

    /**
     * 分类id
     */
    private Integer classifyId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;


    /**
     * 状态：0 已发布，1定时任务
     */
    private Integer publishStatus;

    /**
     * 发布时间
     */
    private String publishTimeStart;


    /**
     * 发布时间
     */
    private String publishTimeEnd;

    /**
     * 加盟商id
     */
    private Long franchiseId;


}
