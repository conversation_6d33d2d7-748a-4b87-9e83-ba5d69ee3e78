package com.xgwc.user.service;

import java.util.List;
import com.xgwc.user.entity.SysDictType;
import com.xgwc.user.entity.vo.SysDictTypeQuery2Vo;
import com.xgwc.user.entity.vo.SysDictTypeVo;
import com.xgwc.user.entity.dto.SysDictTypeDto;
import com.xgwc.user.entity.vo.SysDictTypeQueryVo;

public interface ISysDictTypeService  {
    /**
     * 查询字典管理
     * 
     * @param dictId 字典管理主键
     * @return 字典管理
     */
    public SysDictTypeDto selectSysDictTypeByDictId(Long dictId);

    /**
     * 查询字典管理列表
     * 
     * @param sysDictType 字典管理
     * @return 字典管理集合
     */
    public List<SysDictTypeDto> selectSysDictTypeList(SysDictTypeQueryVo sysDictType);
    public List<SysDictTypeDto> selectSysDictTypeList2(SysDictTypeQuery2Vo sysDictType);

    /**
     * 新增字典管理
     * 
     * @param sysDictType 字典管理
     * @return 结果
     */
    public int insertSysDictType(SysDictTypeVo sysDictType);

    /**
     * 修改字典管理
     * 
     * @param sysDictType 字典管理
     * @return 结果
     */
    public int updateSysDictType(SysDictTypeVo sysDictType);

    public int updateStatus(Long[] ids,Integer status);

    /**
     * 批量删除字典管理
     * 
     * @param dictIds 需要删除的字典管理主键集合
     * @return 结果
     */
    public int deleteSysDictTypeByDictIds(Long[] dictIds);

    /**
     * 删除字典管理信息
     * 
     * @param dictId 字典管理主键
     * @return 结果
     */
    public int deleteSysDictTypeByDictId(Long dictId);
}
