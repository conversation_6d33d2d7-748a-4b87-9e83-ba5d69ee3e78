package com.xgwc.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.xgwc.common.constants.ModelTypeConstant;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.ApiStatusEnums;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.ParamDecryptUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.order.feign.api.ShopFranchiseFeign;
import com.xgwc.order.feign.entity.ShopFranchiseVo;
import com.xgwc.order.feign.entity.XgwcShopFranchiseDto;
import com.xgwc.user.dao.FranchiseOwnerMapper;
import com.xgwc.user.dao.FranchiseStaffMapper;
import com.xgwc.user.dao.SysUserMiddleMapper;
import com.xgwc.user.dao.UserMapper;
import com.xgwc.user.dao.XgwcCompanyInfoMapper;
import com.xgwc.user.entity.FranchiseOwner;
import com.xgwc.user.entity.FranchiseStaff;
import com.xgwc.user.entity.dto.*;
import com.xgwc.user.entity.vo.*;
import com.xgwc.user.security.constants.SecurityConstants;
import com.xgwc.user.service.*;
import com.xgwc.user.util.AutoIdUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;


@Slf4j
@Service
public class FranchiseOwnerServiceImpl implements IFranchiseOwnerService {
    @Resource
    private FranchiseOwnerMapper franchiseOwnerMapper;
    @Resource
    private SmsService smsService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private ShopFranchiseFeign orderFeign;
    @Resource
    private IFranchiseDesignerRecordService franchiseDesignerRecordService;
    @Resource
    private IApplyRecordService applyRecordService;
    @Resource
    private ShopFranchiseFeign shopFranchiseFeign;
    @Resource
    private UserMapper userMapper;
    @Resource
    private IFranchiseService franchiseService;
    @Resource
    private FranchiseStaffMapper franchiseStaffMapper;
    @Resource
    private XgwcCompanyInfoMapper xgwcCompanyInfoMapper;

    @Resource
    private SysUserMiddleMapper sysUserMiddleMapper;

    /**
     * 查询加盟商管理
     * 
     * @param id 加盟商管理主键
     * @return 加盟商管理
     */
    @Override
    public FranchiseOwnerVO selectFranchiseOwnerById(Long id) {
        FranchiseOwnerVO franchiseOwnerVO = franchiseOwnerMapper.selectFranchiseOwnerById(id);
        if (franchiseOwnerVO != null) {
            franchiseOwnerDecrypt(franchiseOwnerVO);
            List<FranchiseDesignerRecordDto> franchiseDesignerRecordListByBusinessId = franchiseDesignerRecordService.findFranchiseDesignerRecordListByBusinessId(franchiseOwnerVO.getId(), 1);
            franchiseOwnerVO.setFranchiseAuditRecord(franchiseDesignerRecordListByBusinessId);

        }
        return franchiseOwnerVO;
    }

    /**
     * 查询加盟商管理列表
     * 
     * @param franchiseOwner 加盟商管理
     * @return 加盟商管理
     */
    @Override
    public List<FranchiseOwnerVO> selectFranchiseOwnerList(FranchiseOwnerQueryDto franchiseOwner) {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null) {
            log.error("品牌商id不能为空:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        franchiseOwner.setBrandId(brandId);
        //手机号加密之后查询
        if(StringUtils.isNotEmpty(franchiseOwner.getManagerPhone())){
            franchiseOwner.setManagerPhone(ParamDecryptUtil.encrypt(franchiseOwner.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
        }
        List<FranchiseOwnerVO> list = franchiseOwnerMapper.selectFranchiseOwnerList(franchiseOwner);
        if(!list.isEmpty()){
            for(FranchiseOwnerVO item : list){
                if(StringUtils.isNotEmpty(item.getManagerPhone())){
                    item.setManagerPhone(ParamDecryptUtil.decryptPhone(item.getManagerPhone()));
                }
            }
        }
        return list;
    }

    /**
     * 新增加盟商管理
     * @param dto 加盟商管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResult insertFranchiseOwner(FranchiseOwnerDto dto) {
        Long brandId = dto.getBrandId() != null ? dto.getBrandId() : 1L;
        String encryptedPhone = ParamDecryptUtil.encrypt(dto.getManagerPhone(), ParamDecryptUtil.PHONE_KEY);
        Long managerUserId = dto.getManagerUserId();
        Long mainUserId = dto.getMainUserId();
        // 已有 mainUserId，则走中间表逻辑（代表先注册用户，再创建加盟商主体）
        if (mainUserId != null) {
            SysUserMiddleDto middle = sysUserMiddleMapper.selectSysUserMiddleByUserIdAndUserType(mainUserId, 2);
            if (middle == null) {
                // 1. 创建中间表记录
                SysUserMiddleVo middleVo = createSysUserMiddle(mainUserId, 2);
                // 2. 校验手机号是否已申请过加盟商主体
                FranchiseOwnerVO exist = franchiseOwnerMapper.findFranchiseOwnerByManagerPhone(encryptedPhone, brandId);
                validateFranchiseOwnerExist(exist);
                // 3. 构建加盟商实体
                FranchiseOwner franchiseOwner = buildFranchiseOwner(dto, encryptedPhone, middleVo.getId(), brandId);
                // 4. 更新用户基本信息
                updateSysUser(mainUserId,dto.getManagerName(), 2, brandId);
                // 5. 插入加盟商主体
                FranchiseVo franchise = insertFranchise(dto, encryptedPhone, middleVo.getId());
                franchiseOwner.setFranchiseId(franchise.getId());
                // 6. 插入加盟商记录
                franchiseOwnerMapper.insertFranchiseOwner(franchiseOwner);
                // 7. 更新中间表来源ID
                sysUserService.updateSysUserMiddleSourceId(franchiseOwner.getManagerUserId(), franchise.getId());
                // 8. 记录申请记录（apply_record）
                insertApplyRecord(franchiseOwner.getManagerUserId(), franchise.getId(), brandId);
                // 9. 创建token并返回
                LoginResult loginResult = new LoginResult();
                loginResult.setToken(sysUserService.createToken(middleVo.getId(), middleVo.getMainUserId()));
                loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
                return loginResult;
            } else {
                // 1. 校验手机号是否已申请过加盟商主体
                FranchiseOwnerVO exist = franchiseOwnerMapper.findFranchiseOwnerByManagerPhone(encryptedPhone, brandId);
                validateFranchiseOwnerExist(exist);
                // 2. 构建加盟商实体
                FranchiseOwner franchiseOwner = buildFranchiseOwner(dto, encryptedPhone, middle.getUserId(), brandId);
                franchiseOwnerMapper.insertFranchiseOwner(franchiseOwner);
                // 3. 记录申请记录（apply_record）
                insertApplyRecord(franchiseOwner.getManagerUserId(), franchiseOwner.getFranchiseId(), brandId);
                // 4. 创建token并返回
                LoginResult loginResult = new LoginResult();
                loginResult.setToken(sysUserService.createToken(middle.getUserId(), middle.getMainUserId()));
                loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
                return loginResult;
            }
        } else { // 无 mainUserId，首次注册用户和加盟商主体
            // 1. 校验手机号是否已注册系统用户
            SysUser existingUser = userMapper.findUserByPhone(encryptedPhone);
            if (existingUser != null && managerUserId == null) {
                throw new ApiException(ApiStatusEnums.AUTH_USER_PHONE_EXISTS.getMessage(), ApiStatusEnums.AUTH_USER_PHONE_EXISTS.getStatus());
            }
            // 2. 如果是首次注册用户，则需要校验验证码
            FranchiseDto franchiseDto = null;
            if (managerUserId == null) {
                if(StringUtils.isEmpty(dto.getCode())){
                    throw new ApiException("验证码不能为空");
                }
                validateSmsCode(dto.getManagerPhone(), dto.getCode());
            } else {
                franchiseDto = franchiseService.getFranchiseByManagerUserId(managerUserId);
            }
            // 3. 校验手机号是否已有加盟商记录
            FranchiseOwnerVO exist = franchiseOwnerMapper.findFranchiseOwnerByManagerPhone(encryptedPhone, brandId);
            validateFranchiseOwnerExist(exist);
            // 4. 构建加盟商记录实体
            FranchiseOwner franchiseOwner = buildFranchiseOwner(dto, encryptedPhone, managerUserId, brandId);
            // 5. 注册新用户（如果是首次注册）
            if (exist == null && managerUserId == null) {
                if(StringUtils.isEmpty(dto.getPassword())){
                    throw new ApiException("密码不能为空");
                }
                managerUserId = registerNewSysUser(dto, encryptedPhone, brandId);
                franchiseOwner.setManagerUserId(managerUserId);
            }
            // 6. 插入加盟商主体（首次建，或复用已存在主体）
            Long franchiseId = (franchiseDto != null) ? franchiseDto.getId() : insertFranchise(dto, encryptedPhone, managerUserId).getId();
            franchiseOwner.setFranchiseId(franchiseId);
            // 7. 插入加盟商记录
            franchiseOwnerMapper.insertFranchiseOwner(franchiseOwner);
            // 8. 更新中间表来源ID
            sysUserService.updateSysUserMiddleSourceId(franchiseOwner.getManagerUserId(), franchiseId);
            // 9. 插入申请记录
            insertApplyRecord(franchiseOwner.getManagerUserId(), franchiseId, brandId);
            // 10. 创建token并返回
            SysUserMiddleDto middleVo = sysUserMiddleMapper.selectSysUserMiddleById(franchiseOwner.getManagerUserId());
            LoginResult loginResult = new LoginResult();
            loginResult.setToken(sysUserService.createToken(middleVo.getUserId(), middleVo.getMainUserId()));
            loginResult.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
            return loginResult;
        }
    }

    /**
     * 创建中间表记录（sys_user_middle）
     * @param mainUserId 主账号ID
     * @param userType 用户类型（如加盟商为2）
     * @return 插入后的中间表VO对象
     */
    private SysUserMiddleVo createSysUserMiddle(Long mainUserId, int userType) {
        SysUserMiddleVo vo = new SysUserMiddleVo();
        vo.setId(AutoIdUtil.getId());
        vo.setMainUserId(mainUserId);
        vo.setUserType(userType);
        sysUserMiddleMapper.insertSysUserMiddle(vo);
        return vo;
    }

    /**
     * 校验加盟商手机号是否已存在
     * @param exist 查询到的加盟商信息（VO）
     * @throws ApiException 如果存在未被驳回的加盟商
     */
    private void validateFranchiseOwnerExist(FranchiseOwnerVO exist) {
        if (exist != null && exist.getCheckStatus() != 3) {
            throw new ApiException(ApiStatusEnums.FRANCHISEOWNER_DATA_EXIST.getMessage(), ApiStatusEnums.FRANCHISEOWNER_DATA_EXIST.getStatus());
        }
    }

    /**
     * 构建 FranchiseOwner 实体
     * @param dto 页面提交的表单参数
     * @param encryptedPhone 加密后的手机号
     * @param managerUserId 管理用户ID
     * @param brandId 品牌ID
     * @return 加盟商实体对象
     */
    private FranchiseOwner buildFranchiseOwner(FranchiseOwnerDto dto, String encryptedPhone, Long managerUserId, Long brandId) {
        FranchiseOwner owner = BeanUtil.copyProperties(dto, FranchiseOwner.class);
        owner.setCreateTime(DateUtils.getNowDate());
        owner.setCreateBy(dto.getManagerName());
        owner.setCheckStatus(0);
        owner.setManagerUserId(managerUserId);
        owner.setBrandId(brandId);
        owner.setManagerPhone(encryptedPhone);
        franchiseOwnerEncrypt(owner); // 加密其他敏感字段
        return owner;
    }

    /**
     * 创建加盟商主体并插入数据库
     * @param dto 表单信息
     * @param encryptedPhone 加密手机号
     * @param managerUserId 加盟商用户ID
     * @return 插入成功后的 FranchiseVo 对象
     */
    private FranchiseVo insertFranchise(FranchiseOwnerDto dto, String encryptedPhone, Long managerUserId) {
        FranchiseVo vo = new FranchiseVo();
        vo.setId(AutoIdUtil.getId());
        vo.setFranchiseName(dto.getFranchiseName());
        vo.setManagerPhone(encryptedPhone);
        vo.setManagerUserId(managerUserId);
        vo.setManagerName(dto.getManagerName());
        franchiseService.insertFranchise(vo);
        return vo;
    }

    /**
     * 更新用户表（sys_user）信息
     * @param mainUserId 主账号ID
     * @param userName 用户名
     * @param userType 用户类型（如加盟商为2）
     * @param brandId 所属品牌ID
     */
    private void updateSysUser(Long mainUserId,String userName, int userType, Long brandId) {
        SysUser user = new SysUser();
        user.setMainUserId(mainUserId);
        user.setUserName(userName);
        user.setUserType(userType);
        user.setBrandId(brandId);
        userMapper.updateSysUser(user);
    }

    /**
     * 校验短信验证码是否有效（注册流程）
     * @param phone 手机号
     * @param code 验证码
     * @throws ApiException 验证失败时抛出异常
     */
    private void validateSmsCode(String phone, String code) {
        if (!smsService.validRegisterCode(phone, code)) {
            throw new ApiException(ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getMessage(), ApiStatusEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR.getStatus());
        }
    }

    /**
     * 注册新用户（适用于首次提交申请）
     * @param dto 表单信息
     * @param encryptedPhone 加密后的手机号
     * @param brandId 所属品牌ID
     * @return 注册后的用户ID
     */
    private Long registerNewSysUser(FranchiseOwnerDto dto, String encryptedPhone, Long brandId) {
        SysUserRegisterDTO registerDTO = new SysUserRegisterDTO();
        registerDTO.setPhone(encryptedPhone);
        registerDTO.setUserName(dto.getManagerName());
        registerDTO.setPassword(dto.getPassword());
        registerDTO.setCompanyName(dto.getCompanyName());
        registerDTO.setUserType(2);
        registerDTO.setBrandId(brandId);
        return sysUserService.addSysUser2(registerDTO);
    }

    /**
     * 添加加盟商申请记录
     * @param userId 用户ID
     * @param businessId 加盟商主体ID
     * @param brandId 品牌ID
     */
    private void insertApplyRecord(Long userId, Long businessId, Long brandId) {
        ApplyRecordVo record = new ApplyRecordVo();
        record.setUserId(userId);
        record.setBusinessType(1); // 加盟商
        record.setBusinessId(businessId);
        record.setBrandId(brandId);
        record.setCheckStatus(0);
        applyRecordService.insertApplyRecord(record);
    }


    /**
     * 加密
     * @param franchiseOwner 加盟商
     */
    private void franchiseOwnerEncrypt(FranchiseOwner franchiseOwner){
        franchiseOwner.setIdcardFront(ParamDecryptUtil.encrypt(franchiseOwner.getIdcardFront(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwner.setIdcardBack(ParamDecryptUtil.encrypt(franchiseOwner.getIdcardBack(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwner.setIdcardName(ParamDecryptUtil.encrypt(franchiseOwner.getIdcardName(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwner.setIdcardNo(ParamDecryptUtil.encrypt(franchiseOwner.getIdcardNo(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwner.setIdcardPhone(ParamDecryptUtil.encrypt(franchiseOwner.getIdcardPhone(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwner.setIdcardEmail(ParamDecryptUtil.encrypt(franchiseOwner.getIdcardEmail(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwner.setBankName(ParamDecryptUtil.encrypt(franchiseOwner.getBankName(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwner.setBankNo(ParamDecryptUtil.encrypt(franchiseOwner.getBankNo(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwner.setManagerName(ParamDecryptUtil.encrypt(franchiseOwner.getManagerName(), ParamDecryptUtil.BRAND_KEY));
    }

    /**
     * 解密 ParamDecryptUtil.decryptParam(sysUserDto.getPhone(), ParamDecryptUtil.PHONE_KEY)
     */
    private void franchiseOwnerDecrypt(FranchiseOwnerVO franchiseOwnerVO) {
        franchiseOwnerVO.setIdcardFront(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getIdcardFront(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setIdcardBack(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getIdcardBack(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setIdcardName(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getIdcardName(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setIdcardNo(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getIdcardNo(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setIdcardPhone(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getIdcardPhone(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setIdcardEmail(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getIdcardEmail(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setBankName(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getBankName(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setBankNo(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getBankNo(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setManagerName(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getManagerName(), ParamDecryptUtil.BRAND_KEY));
        franchiseOwnerVO.setManagerPhone(ParamDecryptUtil.decryptParam(franchiseOwnerVO.getManagerPhone(), ParamDecryptUtil.PHONE_KEY));
    }

    /**
     * 修改加盟商管理
     * 
     * @param dto 加盟商管理
     * @return 结果
     */
    @Override
    public int updateFranchiseOwner(FranchiseOwnerDto dto) {
        validateDeleteBrandExists(dto.getId());
        FranchiseOwner franchiseOwner = BeanUtil.copyProperties(dto, FranchiseOwner.class);
        franchiseOwnerEncrypt(franchiseOwner);
        franchiseOwner.setUpdateTime(DateUtils.getNowDate());
        return franchiseOwnerMapper.updateFranchiseOwner(franchiseOwner);
    }

    /**
     * 批量删除加盟商管理
     * 
     * @param ids 需要删除的加盟商管理主键
     * @return 结果
     */
    @Override
    public int deleteFranchiseOwnerByIds(Long[] ids) {
        return franchiseOwnerMapper.deleteFranchiseOwnerByIds(ids);
    }

    /**
     * 删除加盟商管理信息
     * 
     * @param id 加盟商管理主键
     * @return 结果
     */
    @Override
    public int deleteFranchiseOwnerById(Long id) {
        return franchiseOwnerMapper.deleteFranchiseOwnerById(id);
    }

    @Override
    public void updateStatus(FranchiseOwnerStatusUpdateDto updateReqVO) {
        validateDeleteBrandExists(updateReqVO.getId());
        FranchiseOwner franchiseOwner = new FranchiseOwner();
        franchiseOwner.setId(updateReqVO.getId());
        franchiseOwner.setStatus(updateReqVO.getStatus());
        franchiseOwnerMapper.updateFranchiseOwner(franchiseOwner);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(FranchiseOwnerAuditDto updateReqVO) {
        FranchiseOwnerVO franchiseOwnerVO = franchiseOwnerMapper.selectFranchiseOwnerById(updateReqVO.getId());
        if (franchiseOwnerVO == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        FranchiseOwner franchiseOwner = new FranchiseOwner();
        franchiseOwner.setId(updateReqVO.getId());
        franchiseOwner.setCheckStatus(updateReqVO.getCheckStatus());
        if(updateReqVO.getCheckStatus() == 1){
            franchiseOwner.setCheckTime(DateUtils.getNowDate());
            // 审核通过添加公司主体
            XgwcCompanyInfoVo xgwcCompanyInfoVo = new XgwcCompanyInfoVo();
            BeanUtils.copyProperties(franchiseOwnerVO, xgwcCompanyInfoVo);
            xgwcCompanyInfoVo.setIsFlag(0);
            xgwcCompanyInfoVo.setBrandId(franchiseOwnerVO.getBrandId());
            xgwcCompanyInfoMapper.saveXgwcCompanyInfo(xgwcCompanyInfoVo);
        }
        franchiseOwner.setReason(updateReqVO.getReason());
        franchiseOwner.setStatus(updateReqVO.getCheckStatus());
        franchiseOwnerMapper.updateFranchiseOwner(franchiseOwner);
        // 审核通过初始化角色
        if(updateReqVO.getCheckStatus() == 1 && franchiseOwnerVO.getManagerUserId() != null){
            FranchiseStaffDto franchiseStaffDto = franchiseStaffMapper.selectFranchiseStaffByUserId(franchiseOwnerVO.getManagerUserId());
            if(franchiseStaffDto == null){
                Long roleId = sysUserService.initRole(franchiseOwnerVO.getManagerUserId(), ModelTypeConstant.FRANCHISE_ADMIN,
                        franchiseOwnerVO.getCompanyName(), franchiseOwnerVO.getFranchiseId(), 2);
                // 添加员工
                String s = ParamDecryptUtil.decryptParam(franchiseOwnerVO.getManagerName(), ParamDecryptUtil.BRAND_KEY);
                FranchiseStaff franchiseStaff = new FranchiseStaff();
                franchiseStaff.setName(s);
                franchiseStaff.setStageName(s);
                franchiseStaff.setJobNature(0);
                franchiseStaff.setRoleIds(String.valueOf(roleId));
                franchiseStaff.setFranchiseId(franchiseOwnerVO.getFranchiseId());
                franchiseStaff.setBindUserId(franchiseOwnerVO.getManagerUserId());
                franchiseStaff.setBindStatus(1);
                franchiseStaff.setLoginPhone(franchiseOwnerVO.getManagerPhone());
                franchiseStaffMapper.insertFranchiseStaff(franchiseStaff);
            }
        }
        // 修改申请记录
        ApplyRecordDto applyRecordDto = applyRecordService.selectApplyRecordByUserId(franchiseOwnerVO.getManagerUserId(), franchiseOwnerVO.getBrandId(), franchiseOwnerVO.getFranchiseId());
        if(applyRecordDto != null){
            applyRecordService.updateApplyRecordStatusById(applyRecordDto.getId(), updateReqVO.getCheckStatus());
        }
        // 审核记录
        FranchiseDesignerRecordVo record = new FranchiseDesignerRecordVo();
        record.setBrandId(franchiseOwnerVO.getBrandId());
        record.setBusinessType(1);
        record.setBusinessId(updateReqVO.getId());
        record.setCheckStatus(updateReqVO.getCheckStatus());
        record.setReason(updateReqVO.getReason());
        record.setCheckTime(DateUtils.getNowDate());
        if(updateReqVO.getCheckStatus() != 3){
            record.setCreateBy(SecurityUtils.getNickName());
        }else {
            record.setCreateBy(franchiseOwnerVO.getCreateBy());
        }
        franchiseDesignerRecordService.insertFranchiseDesignerRecord(record);
    }

    @Override
    public void authorization(FranchiseOwnerAuthorizationDto updateReqVO) {
        validateDeleteBrandExists(updateReqVO.getId());
        FranchiseOwner franchiseOwner = new FranchiseOwner();
        franchiseOwner.setId(updateReqVO.getId());
        franchiseOwner.setShopIds(updateReqVO.getShopIds());
        franchiseOwnerMapper.updateFranchiseOwner(franchiseOwner);
        ShopFranchiseVo vo = new ShopFranchiseVo();
        vo.setFranchiseId(updateReqVO.getId());
        vo.setShopId( Arrays.stream(updateReqVO.getShopIds().split(","))
                .map(s -> Long.parseLong(s.trim())).toArray(Long[]::new));
        shopFranchiseFeign.saveShopFranchise(vo);
    }

    @Override
    public FranchiseOwnerVO selectFranchiseOwnerByManagerUserIdAndBrandId(Long managerUserId, Long brandId) {
        return franchiseOwnerMapper.findFranchiseOwnerByManagerUserIdAndBrandId(managerUserId,brandId);
    }

    @Override
    public int reapply(FranchiseOwnerDto franchiseOwner) {
        FranchiseOwnerVO franchiseOwnerVO = franchiseOwnerMapper.selectFranchiseOwnerById(franchiseOwner.getId());
        if (franchiseOwnerVO == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
        FranchiseOwner franchiseOwnerEntity = BeanUtil.copyProperties(franchiseOwner, FranchiseOwner.class);
        franchiseOwnerEncrypt(franchiseOwnerEntity);
        franchiseOwnerEntity.setCheckStatus(0);
        franchiseOwnerEntity.setCheckTime(null);
        franchiseOwnerEntity.setReason(null);
        franchiseOwnerEntity.setUpdateTime(DateUtils.getNowDate());
        franchiseOwnerEntity.setStatus(0);
        // 修改申请记录
        ApplyRecordDto applyRecordDto = applyRecordService.selectApplyRecordByUserId(franchiseOwnerVO.getManagerUserId(), franchiseOwnerVO.getBrandId(), franchiseOwnerVO.getFranchiseId());
        if(applyRecordDto != null){
            applyRecordService.updateApplyRecordStatusById(applyRecordDto.getId(), 0);
        }
        return franchiseOwnerMapper.updateFranchiseOwner(franchiseOwnerEntity);
    }

    @Override
    public List<FranchiseOwnerVO> selectMyApplyList(MyFranchiseOwnerQueryDto queryDto) {
        Long franchiseId = SecurityUtils.getSysUser().getFranchiseId();
        queryDto.setFranchiseId(franchiseId);
        List<FranchiseOwnerVO> myApplyList = franchiseOwnerMapper.selectMyFranchiseOwnerApplyList(queryDto);
//        if(!myApplyList.isEmpty()){
//            for(FranchiseOwnerVO item : myApplyList){
//                try {
//                    ApiResult<XgwcShopFranchiseDto> result = orderFeign.getShopFranchise(item.getId());
//                    if (result != null && result.getData() != null) {
//                        XgwcShopFranchiseDto data = result.getData();
//                        item.setShopName(data.getShopName());
//                        item.setShopIds(data.getShopIds());
//                    }
//                } catch (Exception e) {
//                    log.error("feign查询店铺信息失败！",e);
//                }
//            }
//        }
        return myApplyList;
    }

    @Override
    public List<FranchiseOwnerSimpleVO> selectFranchiseOwnerListByBrandId(Long brandId) {
        return franchiseOwnerMapper.findFranchiseOwnerListByBrandId(brandId);
    }

    private void validateDeleteBrandExists(Long id) {
        if (franchiseOwnerMapper.selectFranchiseOwnerById(id) == null) {
            throw new ApiException(ApiStatusEnums.NOT_EXISTS.getMessage(), ApiStatusEnums.NOT_EXISTS.getStatus());
        }
    }

    /**
     * 查询加盟商下载限制
     *
     * @param franchiseOwnerQueryDto 查询参数
     * @return 下载限制列表
     */
    @Override
    public List<FranchiseOwnerVO> selectFranchiseDownloadLimit(FranchiseOwnerQueryDto franchiseOwnerQueryDto) {
        franchiseOwnerQueryDto.setBrandId(SecurityUtils.getSysUser().getBrandId());
        return franchiseOwnerMapper.selectFranchiseDownloadLimit(franchiseOwnerQueryDto);
    }

    /**
     * 修改加盟商下载限制
     *
     * @param franchiseOwnerVo 修改参数
     * @return 结果
     */
    @Override
    public ApiResult updateDownloadLimit(FranchiseOwner franchiseOwnerVo) {
        if (franchiseOwnerVo == null || franchiseOwnerVo.getId() == null) {
            return ApiResult.error("加盟商id不能为空");
        }
        if(franchiseOwnerVo.getDownloadLimit() == null){
            return ApiResult.error("下载次数上限/天/人不能为空");
        }
        franchiseOwnerVo.setIsConfigured(0);
        int updResult = franchiseOwnerMapper.updateFranchiseOwner(franchiseOwnerVo);
        if(updResult <= 0){
            return ApiResult.error("修改失败");
        }
        return ApiResult.ok();
    }

    @Override
    public int selectFranchiseOwnerByFranchiseId(Long franchiseId) {
        List<FranchiseOwnerVO> list = franchiseOwnerMapper.findFranchiseOwnerByFranchiseId(franchiseId);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        if (list.size() == 1) {
            return list.get(0).getCheckStatus();
        }
        return list.stream().filter(item -> item.getCheckStatus() == 1)
                .map(FranchiseOwnerVO::getCheckStatus)
                .findFirst()
                .orElse(0);
    }

    @Override
    public List<FranchiseOwnerVO> offlinePayment(Long brandId) {
        return franchiseOwnerMapper.selectBrandNameAndFranchiseName(brandId);
    }

    @Override
    public List<FranchiseOwnerSimpleVO> findCooperateFranchiseList() {
        Long serviceId = SecurityUtils.getSysUser().getServiceId();
        if (serviceId == null) List.of();
        return franchiseOwnerMapper.findCooperateFranchiseByServiceId(serviceId);
    }
}
