package com.xgwc.user.entity.dto.notice;

import lombok.Data;

@Data
public class NoticeDto {

    /**
     * 通知分类ID
     */
    private Integer id;

    /**
     * 通知类型：1站内信，2短信，3企微
     */
    private Integer noticeType;

    /**
     * 分类id
     */
    private String classifyId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发送数量
     */
    private Integer sendCount;

    /**
     * 发送人 json
     */
    private String sendGroup;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 来源：1品牌商，2加盟商
     */
    private Integer source;

    /**
     * source=1时为品牌商id，source=2时为加盟商id
     */
    private Long sourceId;

    /**
     * 定时时间
     */
    private String scheduleTime;

    /**
     * 发送状态：0 未发送，1发送中，2.已发送，3.发送失败
     */
    private Integer sendStatus;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 发布人
     */
    private String createBy;

    /**
     * 读取人数
     */
    private Integer readCount;

    /**
     * 发布状态
     */
    private Integer publishStatus;

}
