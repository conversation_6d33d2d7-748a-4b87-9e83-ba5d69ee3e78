package com.xgwc.user.entity.vo;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class StaffRegisterVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("员工id")
    private Long id;

    @FieldDesc("姓名")
    @NotNull(message = "姓名不能为空")
    private String name;

    @FieldDesc("手机号")
    @NotNull(message = "手机号不能为空")
    private String phone;

    @FieldDesc("验证码")
    @NotNull(message = "验证码不能为空")
    private String code;

    @FieldDesc("密码")
    @NotNull(message = "密码不能为空")
    private String password;

    @FieldDesc("确认密码")
    @NotNull(message = "确认密码不能为空")
    private String confirmPassword;

    @FieldDesc("所属业务ID")
    @NotNull(message = "所属业务ID不能为空")
    private Long businessId;

    @FieldDesc("类型：1-品牌商链接，2-加盟商链接，3-财务服务商链接，4-销售服务商链接")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @FieldDesc("花名")
    @NotNull(message = "花名不能为空")
    private String stageName;
}
