package com.xgwc.user.entity.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class FranchiseNoticeClassifyVo {
    /**
     * 通知分类ID
     */
    private Integer id;

    @NotNull(message = "通知分类名称不能为空")
    private String classifyName;

    @NotNull(message = "通知分类排序不能为空")
    private Integer sort;

    /**
     * 加盟商ID
     */
    private Long franchiseId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 状态：0正常，1禁用
     */
    private Integer status;

    public FranchiseNoticeClassifyVo() {

    }
}
