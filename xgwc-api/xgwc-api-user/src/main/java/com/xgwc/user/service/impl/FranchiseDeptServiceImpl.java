package com.xgwc.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.user.dao.FranchiseDeptMapper;
import com.xgwc.user.dao.FranchiseOwnerMapper;
import com.xgwc.user.dao.FranchiseStaffMapper;
import com.xgwc.user.dao.XgwcBrandDeptMapper;
import com.xgwc.user.entity.Staff;
import com.xgwc.user.entity.dto.BranchFranchiseDeptStaff;
import com.xgwc.user.entity.dto.FranchiseDeptDto;
import com.xgwc.user.entity.dto.FranchiseDeptStaff;
import com.xgwc.user.entity.dto.SimpleDeptUserInfoDto;
import com.xgwc.user.entity.param.FranchiseDeptParam;
import com.xgwc.user.entity.vo.*;
import com.xgwc.user.service.FranchiseDeptService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.service.impl
 * @Author: kouwenzhuo
 * @CreateTime: 2025-04-28  11:36
 */
@Service
@Slf4j
public class FranchiseDeptServiceImpl implements FranchiseDeptService {

    @Resource
    private FranchiseDeptMapper franchiseDeptMapper;

    @Resource
    private FranchiseStaffMapper franchiseStaffMapper;

    @Resource
    private FranchiseOwnerMapper franchiseOwnerMapper;

    /**
     * 获取所有部门列表
     * @param franchiseDeptParam 部门参数
     * @return 部门列表
     */
    @Override
    public List<FranchiseDeptInfo> getFranchiseDeptList(FranchiseDeptParam franchiseDeptParam) {
        Long franchiseId = franchiseDeptParam.getFranchiseId();
        franchiseDeptParam.setFranchiseId(
                (franchiseId == null)
                        ? SecurityUtils.getSysUser().getFranchiseId()
                        : franchiseId);
        List<FranchiseDeptVo> deptList = franchiseDeptMapper.getFranchiseDeptList(franchiseDeptParam);

        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }

        if (deptList.stream().anyMatch(dept -> dept.getPid() == null)) {
            log.error("获取部门信息失败，参数不完整缺少pid");
            return Collections.emptyList();
        }

        // 如果是搜索/筛选条件，直接返回扁平结构（不构建树）
        if (StringUtils.isNotEmpty(franchiseDeptParam.getDeptName())) {
            return deptList.stream()
                    .map(deptVo -> {
                        FranchiseDeptInfo deptInfo = new FranchiseDeptInfo();
                        BeanUtils.copyProperties(deptVo, deptInfo);
                        deptInfo.setLevelNum(1);
                        // 查询当前部门的员工人数
                        deptInfo.setTotalNum(getDeptStaffCount(deptVo.getDeptId()));
                        return deptInfo;
                    })
                    .collect(Collectors.toList());
        }

        // 按pid分组（构建树形结构）
        Map<Long, List<FranchiseDeptVo>> pidToChildrenMap = deptList.stream()
                .collect(Collectors.groupingBy(FranchiseDeptVo::getPid));

        // 构建树形结构（从顶级节点开始）
        return deptList.stream()
                .filter(dept -> dept.getPid() == null || dept.getPid() == 0)
                .map(rootDept -> {
                    FranchiseDeptInfo rootInfo = new FranchiseDeptInfo();
                    BeanUtils.copyProperties(rootDept, rootInfo);
                    rootInfo.setLevelNum(1);
                    // 递归构建子树并计算人数
                    rootInfo.setChiledrenList(buildDeptTree(rootInfo, pidToChildrenMap));
                    // 计算总人数（包括子部门）
                    rootInfo.setTotalNum(calculateTotalStaff(rootInfo));
                    return rootInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 递归构建部门树
     */
    private List<FranchiseDeptInfo> buildDeptTree(FranchiseDeptInfo parentDept,
                                                  Map<Long, List<FranchiseDeptVo>> pidToChildrenMap) {
        List<FranchiseDeptVo> children = pidToChildrenMap.get(parentDept.getDeptId());
        if (CollectionUtils.isEmpty(children)) {
            // 叶子节点：直接查询当前部门的员工人数
            parentDept.setTotalNum(getDeptStaffCount(parentDept.getDeptId()));
            return Collections.emptyList();
        }

        // 递归处理子节点
        List<FranchiseDeptInfo> childInfos = children.stream()
                .map(childDept -> {
                    FranchiseDeptInfo childInfo = new FranchiseDeptInfo();
                    BeanUtils.copyProperties(childDept, childInfo);
                    childInfo.setLevelNum(parentDept.getLevelNum() + 1);
                    childInfo.setChiledrenList(buildDeptTree(childInfo, pidToChildrenMap));
                    return childInfo;
                })
                .collect(Collectors.toList());

        // 计算当前节点的总人数（如果子节点人数为0，则使用当前部门的员工数）
        int childTotal = calculateTotalStaff(parentDept);
        if (childTotal == 0) {
            parentDept.setTotalNum(getDeptStaffCount(parentDept.getDeptId()));
        } else {
            parentDept.setTotalNum(childTotal);
        }

        return childInfos;
    }

    /**
     * 计算部门及其子部门的总人数（包括当前部门自身）
     */
    private int calculateTotalStaff(FranchiseDeptInfo deptInfo) {
        // 1. 获取当前部门的员工人数（自身）
        int currentDeptStaff = getDeptStaffCount(deptInfo.getDeptId());

        // 2. 递归计算子部门的总人数
        int childrenTotal = 0;
        if (!CollectionUtils.isEmpty(deptInfo.getChiledrenList())) {
            childrenTotal = deptInfo.getChiledrenList().stream()
                    .mapToInt(this::calculateTotalStaff) // 递归计算子部门总人数
                    .sum();
        }

        // 3. 返回总人数 = 当前部门人数 + 子部门人数
        return currentDeptStaff + childrenTotal;
    }

    /**
     * 查询指定部门的员工人数
     */
    private int getDeptStaffCount(Long deptId) {
        Integer count = franchiseDeptMapper.selectDeptStaffNum(deptId);
        return count != null ? count : 0;
    }

    /**
     * 新增部门
     * @param franchiseDeptDto 部门信息
     * @return 新增结果
     */
    @Override
    public ApiResult saveFranchiseDept(FranchiseDeptDto franchiseDeptDto) {
        // 参数校验
        if (franchiseDeptDto == null) {
            log.error("参数校验失败，入参不能为空");
            return ApiResult.error("参数无效，入参不能为空");
        }

        ApiResult<Object> error = getObjectApiResult(franchiseDeptDto);
        if (error != null) return error;

        try {
            franchiseDeptDto.setCreateBy(SecurityUtils.getNickName());
            franchiseDeptDto.setFranchiseId(SecurityUtils.getSysUser().getFranchiseId());
            int result = franchiseDeptMapper.saveFranchiseDept(franchiseDeptDto);
            if (result > 0) {
                log.info("新增成功，deptId：{}，参数：{}", franchiseDeptDto.getDeptId(), franchiseDeptDto);
                return ApiResult.ok();
            } else {
                log.error("新增失败，deptId：{}可能存在重复或其他约束冲突，参数：{}", franchiseDeptDto.getDeptId(), franchiseDeptDto);
                return ApiResult.error("新增失败，可能原因：ID已存在或其他约束冲突");
            }
        } catch (Exception e) {
            log.error("数据库操作异常，deptId：{}，参数：{}", franchiseDeptDto.getDeptId(), franchiseDeptDto, e);
            return ApiResult.error("数据库操作失败，请检查日志");
        }
    }

    @Nullable
    private static ApiResult<Object> getObjectApiResult(FranchiseDeptDto franchiseDeptDto) {
        if (franchiseDeptDto.getPid() == null) {
            log.error("参数校验失败，pid不能为空");
            return ApiResult.error("参数无效，pid不能为空");
        }
        if (franchiseDeptDto.getDeptName() == null) {
            log.error("参数校验失败，deptName不能为空");
            return ApiResult.error("参数无效，deptName不能为空");
        }
        return null;
    }
    /**
     * 根据部门ID查询部门信息
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public ApiResult getFranchiseDeptById(Long deptId) {
        if (deptId == null) {
            return ApiResult.error("id不能为空");
        }
        FranchiseDeptDto franchiseDeptDto = franchiseDeptMapper.getFranchiseDeptById(deptId);
        if (franchiseDeptDto == null) {
            log.error("查询失败，id：{}不存在", deptId);
            return ApiResult.error("id不存在");
        }

        Long parentId = franchiseDeptDto.getPid();
        if(parentId > 0) {
            FranchiseDeptDto parentDept = franchiseDeptMapper.getFranchiseDeptById(parentId);
            if (parentDept != null) {
                franchiseDeptDto.setDeptName(parentDept.getDeptName());
                franchiseDeptDto.setPid(parentDept.getDeptId());
            }
            return ApiResult.ok(franchiseDeptDto);
        }
        log.info("查询成功，id:{}", deptId);
        return ApiResult.ok(franchiseDeptDto);
    }

    @Override
    public ApiResult getUserDeptByUserId(Long userId) {
        return ApiResult.ok(franchiseDeptMapper.getUserDeptByUserId(userId));
    }


    /**
     * 修改部门信息
     * @param franchiseDeptDto 部门信息
     * @return 修改结果
     */
    @Override
    public ApiResult updateFranchiseDeptById(FranchiseDeptDto franchiseDeptDto) {
        try {
            if (franchiseDeptDto == null || franchiseDeptDto.getDeptId() == null) {
                log.error("参数校验失败，deptId不能为空");
                return ApiResult.error("参数校验失败，deptId不能为空");
            }

            ApiResult<Object> error = getObjectApiResult(franchiseDeptDto);
            if (error != null) return error;

            franchiseDeptDto.setUpdateBy(SecurityUtils.getNickName());
            int result = franchiseDeptMapper.updateFranchiseDeptById(franchiseDeptDto);
            if (result > 0) {
                log.info("========================id:{},修改成功", franchiseDeptDto.getDeptId());
                return ApiResult.ok();
            }
            log.error("修改失败，id：{}不存在或数据未变化", franchiseDeptDto.getDeptId());
            return ApiResult.error("修改失败,id不存在");
        } catch (Exception e) {
            log.error("修改过程中发生异常，id：{}", franchiseDeptDto.getDeptId(), e);
            return ApiResult.error("修改失败,系统异常");
        }
    }



    /**
     * 修改部门状态
     * @param deptId 部门ID
     * @param status 部门状态
     * @return 修改结果
     */
    @Override
    public ApiResult updateStatusById(Integer deptId, Integer status) {
        // 参数校验：确保 deptId 合法且 status 在允许范围内
        if (deptId == null || deptId <= 0) {
            log.error("无效的部门ID: {}", deptId);
            return ApiResult.error("更新部门状态失败");
        }
        if (status == null || (status != 0 && status != 1)) {
            log.error("无效的状态值: {}", status);
            return ApiResult.error("更新部门状态失败");
        }

        try {
            int result = franchiseDeptMapper.updateStatusById(deptId, status);
            if (result > 0) {
                log.info("更新部门状态成功，brandId: {}, 新状态: {}", deptId, status);
                return ApiResult.ok();
            } else {
                log.error("更新部门状态失败，brandId: {}，未找到记录或状态未更新", deptId);
                return ApiResult.error("更新部门状态失败");
            }
        } catch (Exception e) {
            log.error("更新部门状态时发生异常，brandId: {}, status: {}", deptId, status, e);
            return ApiResult.error("更新部门状态失败");
        }
    }


    /**
     * 获取部门员工信息
     * @param deptId 部门ID
     * @return 部门员工信息
     */
    @Override
    public ApiResult getXgwcDeptStaffInfo(Integer deptId) {
        try {
            // 1. 查询数据
            StaffVo manager = franchiseDeptMapper.selectXgwcDeptStaffManage(deptId);
            List<StaffVo> assistants = franchiseDeptMapper.selectXgwcDeptStaffSchedule(deptId);

            // 2. 构建响应对象
            XgwcBrandDeptVo response = new XgwcBrandDeptVo();
            response.setIsPrincipal(getValidStaff(manager));
            response.setIsAssistant(getValidStaffList(assistants));

            return ApiResult.ok(response);
        } catch (Exception e) {
            log.error("获取部门员工信息失败，deptId: {}", deptId, e);
            return ApiResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 确保 StaffVo 不为 null，为空时返回默认实例
     */
    private StaffVo getValidStaff(StaffVo staff) {
        return staff != null ? staff : new StaffVo();
    }

    /**
     * 确保 Staff 列表不为 null，为空时返回不可变的空列表
     */
    private List<StaffVo> getValidStaffList(List<StaffVo> staffList) {
        return staffList != null ? staffList : Collections.emptyList();
    }


    /**
     * 修改部门员工信息
     * @param franchiseDeptDto 部门员工信息
     * @return 修改结果
     */
    @Override
    public ApiResult updateXgwcDeptStaffInfo(FranchiseDeptDto franchiseDeptDto) {
        // 参数校验
        if (franchiseDeptDto == null) {
            return ApiResult.error("参数不能为空");
        }

        Staff isPrincipal = franchiseDeptDto.getIsPrincipal();
        if (isPrincipal != null) {
            int assistant = franchiseDeptMapper.updateDeptStaffAssistant(isPrincipal.getDeptId());
            int manageResult = franchiseDeptMapper.updateDeptStaffManage(isPrincipal);
            if (manageResult <= 0 && assistant <= 0) {
                log.warn("更新部门负责人、排班信息失败，参数：{}", isPrincipal);
                return ApiResult.error("更新部门负责人、排班信息失败");
            }
        }

        List<Staff> isAssistant = franchiseDeptDto.getIsAssistant();
        if (!CollectionUtils.isEmpty(isAssistant)) {
            for (Staff staff : isAssistant) {
                if (isPrincipal != null && isPrincipal.getIsSchedule() != null){
                    staff.setIsSchedule(isPrincipal.getIsSchedule());
                }
                int scheduleResult = franchiseDeptMapper.updateDeptStaffSchedule(staff);
                if (scheduleResult <= 0) {
                    log.warn("更新部门助理信息失败，参数：{}", isAssistant);
                    return ApiResult.error("更新部门助理信息失败");
                }
            }
        }

        log.info("部门人员信息更新成功");
        return ApiResult.ok();
    }

    @Override
    public List<FranchiseDeptStaff> getDeptStaffTree(Long franchiseId) {
        if(franchiseId == null){
            SysUser sysUser = SecurityUtils.getSysUser();
            franchiseId = sysUser.getFranchiseId();
        }
        if(franchiseId == null) {
            log.error("非加盟商员工不能获取:{}", JSONObject.toJSONString(SecurityUtils.getSysUser()));
            return null;
        }
        List<FranchiseDeptDto> franchiseDeptDtos = franchiseDeptMapper.getFranchiseDeptByfranchiseId(franchiseId);
        if(franchiseDeptDtos != null && !franchiseDeptDtos.isEmpty()){
            List<FranchiseDeptStaff> franchiseDeptStaffs = convertToDeptStaff(franchiseDeptDtos);
            //获取所有的员工
            List<SimpleDeptUserInfoDto> deptUserInfoDtos = franchiseStaffMapper.getStaffUserInfoListByFranchise(franchiseId);
            if(deptUserInfoDtos != null && !deptUserInfoDtos.isEmpty()){
                Map<Long, List<SimpleDeptUserInfoDto>> deptUserMap = deptUserInfoDtos.stream().collect(Collectors.groupingBy(SimpleDeptUserInfoDto::getDeptId));
                populateUsers(franchiseDeptStaffs, deptUserMap);
            }
            return franchiseDeptStaffs;
        }
        return List.of();
    }

    @Override
    public List<BranchFranchiseDeptStaff> getAllFranchiseStaffForBrand() {
        SysUser sysUser = SecurityUtils.getSysUser();
        Long brandId = sysUser.getBrandId();
        if(brandId == null) {
            log.error("品牌商员工不能获取:{}", JSONObject.toJSONString(sysUser));
            return null;
        }
        List<FranchiseOwnerSimpleVO> franchiseOwnerSimpleVOS = franchiseOwnerMapper.findFranchiseOwnerListByBrandId(brandId);
        if(franchiseOwnerSimpleVOS != null && !franchiseOwnerSimpleVOS.isEmpty()){
            return franchiseOwnerSimpleVOS.stream().map(franchiseOwnerSimpleVO -> {
                BranchFranchiseDeptStaff branchFranchiseDeptStaff = new BranchFranchiseDeptStaff();
                branchFranchiseDeptStaff.setFranchiseId(franchiseOwnerSimpleVO.getId());
                branchFranchiseDeptStaff.setFranchiseName(franchiseOwnerSimpleVO.getCompanySimpleName());
                branchFranchiseDeptStaff.setFranchiseDeptStaffList(getDeptStaffTree(franchiseOwnerSimpleVO.getId()));
                return branchFranchiseDeptStaff;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<FranchiseDeptVo> getDeptByFranchiseId(Long franchiseId) {
        if(franchiseId == null)return List.of();
        return franchiseDeptMapper.getDeptByFranchiseId(franchiseId);
    }


    public static void populateUsers(List<FranchiseDeptStaff> staffList, Map<Long, List<SimpleDeptUserInfoDto>> deptUserMap) {
        staffList.forEach(staff -> {
            populateUser(staff, deptUserMap);
        });
    }

    public static void populateUser(FranchiseDeptStaff root, Map<Long, List<SimpleDeptUserInfoDto>> deptUserMap) {
        Optional.ofNullable(root.getDeptId())
                .map(Long::valueOf)
                .map(deptUserMap::get)
                .ifPresentOrElse(
                        root::setUsers,
                        () -> root.setUsers(new ArrayList<>())
                );

        Optional.ofNullable(root.getDepts())
                .ifPresent(depts -> depts.forEach(dept -> populateUser(dept, deptUserMap)));
    }


    public static List<FranchiseDeptStaff> convertToDeptStaff(List<FranchiseDeptDto> franchiseDeptDtos) {
        // Find root departments (where pid is null or 0, depending on your data structure)
        List<FranchiseDeptDto> rootDepts = franchiseDeptDtos.stream()
                .filter(dept -> dept.getPid() == null || dept.getPid() == 0)
                .collect(Collectors.toList());
        return buildDeptTree(rootDepts, franchiseDeptDtos);
    }

    private static List<FranchiseDeptStaff> buildDeptTree(List<FranchiseDeptDto> currentLevelDepts, List<FranchiseDeptDto> allDepts) {
        return currentLevelDepts.stream()
                .map(deptVo -> {
                    FranchiseDeptStaff deptStaff = new FranchiseDeptStaff();
                    deptStaff.setDeptId(deptVo.getDeptId()); // Convert Long to Integer
                    deptStaff.setDeptName(deptVo.getDeptName());
                    // Find children departments
                    List<FranchiseDeptDto> children = allDepts.stream()
                            .filter(d -> deptVo.getDeptId().equals(d.getPid()))
                            .collect(Collectors.toList());

                    if (!children.isEmpty()) {
                        deptStaff.setDepts(buildDeptTree(children, allDepts));
                    }
                    // Initialize empty users list (you can populate this separately)
                    deptStaff.setUsers(new ArrayList<>());
                    return deptStaff;
                })
                .collect(Collectors.toList());
    }
}
