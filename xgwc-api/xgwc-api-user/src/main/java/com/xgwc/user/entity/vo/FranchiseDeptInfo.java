package com.xgwc.user.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.order.entity.vo
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-28  11:16
 */

/**
 * 加盟商部门表级联
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FranchiseDeptInfo extends FranchiseDeptVo{

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<FranchiseDeptInfo> chiledrenList;
}
