package com.xgwc.task.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.task.dao.PrivacyNumberMapper;
import com.xgwc.task.entity.PriavcyNumberCountDto;
import com.xgwc.task.service.PrivacyNumberService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class PrivacyNumberServiceImpl implements PrivacyNumberService {

    @Resource
    private PrivacyNumberMapper privacyNumberMapper;
    /**
     * 是否首次
     */
    private static boolean isFirst = true;

    @Override
    public void preStaticsPrivacyNumberCount() {
        if(isFirst){
            log.info("首次更新全量未绑定状态");
            privacyNumberMapper.updateBindStatusForExpire();
            isFirst = false;
        }else{
            log.info("按小时更新未绑定状态");
            privacyNumberMapper.updateBindStatusForExpireInHour();
        }
        //统计每个号码的使用量
        List<PriavcyNumberCountDto> priavcyNumberCountDtos = privacyNumberMapper.countPrivacyNumber();
        if(priavcyNumberCountDtos != null && !priavcyNumberCountDtos.isEmpty()){
            log.info("统计隐私号绑定数量:{}", JSONObject.toJSONString(priavcyNumberCountDtos));
            privacyNumberMapper.updatePrivacyNumberCount(priavcyNumberCountDtos);
        }
    }
}
