package com.xgwc.task.schedule;

import com.xgwc.order.feign.api.XgwcPlatformJobFeign;
import com.xgwc.task.dao.OrderCommonMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class OrderCommonJob {

    @Resource
    private OrderCommonMapper orderCommonMapper;

    @Resource
    private XgwcPlatformJobFeign xgwcPlatformJobFeign;

    /**
     * 客服提成完成率配置过期任务
     */
    @Scheduled(cron = "0 0 0 1 * ?")
    public void autoExpired() {
        log.info("客服提成完成率配置过期自动失效任务开始执行...");
        orderCommonMapper.brandCsCommissionRateConfigAutoExpired(new Date());
    }

    /**
     * 周期更新平台店铺
     */
    @Scheduled(cron = "0 0 0 1 * ?")
    public void cycleUpdatePlatformShop() {
        log.info("周期更新旺店通平台店铺");
        xgwcPlatformJobFeign.cycleUpdatePlatformShop();
    }

    /**
     * 周期更新平台货品
     */
    @Scheduled(cron = "0 0 0 1 * ?")
    public void cycleUpdatePlatformGoods() {
        log.info("周期更新旺店通平台商品");
        xgwcPlatformJobFeign.cycleUpdatePlatformGoods();
    }

    /**
     * 周期更新平台交易单
     */
    @Scheduled(cron = "0 0 0 1 * ?")
    public void cycleUpdatePlatformTrade() {
        log.info("周期更新旺店通平台订单");
        xgwcPlatformJobFeign.cycleUpdatePlatformTrade();
    }

    /**
     * 周期更新平台售后单
     */
    @Scheduled(cron = "0 0 0 1 * ?")
    public void cycleUpdatePlatformRefund() {
        log.info("周期更新旺店通平台售后单");
        xgwcPlatformJobFeign.cycleUpdatePlatformRefund();
    }

}
