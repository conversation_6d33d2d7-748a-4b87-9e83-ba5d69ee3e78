package com.xgwc.task.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.xgwc.task.config.AliyunVodConfig;
import com.xgwc.task.dao.UploadVideoRecordMapper;
import com.xgwc.task.entity.UploadVideoRecord;
import com.xgwc.task.service.UploadVideoRecordSerivce;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class UploadVideoRecordServiceImpl implements UploadVideoRecordSerivce {

    @Resource
    private AliyunVodConfig aliyunVodConfig;

    @Resource
    private UploadVideoRecordMapper uploadVideoRecordMapper;

    @Override
    public void getTranscodeInfo() {
        log.info("开始执行获取转码视频信息任务");
        List<UploadVideoRecord> uploadVideoRecords = uploadVideoRecordMapper.getNotTranscodeList();
        if(uploadVideoRecords != null && !uploadVideoRecords.isEmpty()){
            log.info("开始执行获取转码视频信息任务, 一共:{}条", uploadVideoRecords.size());
            DefaultAcsClient vodClient = initVodClient();
            for(UploadVideoRecord uploadVideoRecord : uploadVideoRecords) {
                try{
                    //根据视频ID获取转码后的视频信息
                    GetPlayInfoResponse playInfoResponse =
                            getPlayInfo(vodClient, uploadVideoRecord.getVideoId());
                    updateUploadVideoRecord(uploadVideoRecord, playInfoResponse);
                }catch (Exception e){
                    log.error("获取视频信息失败：", e);
                }
            }
        }
    }

    /**
     * 更新视频信息
     */
    private void updateUploadVideoRecord(UploadVideoRecord uploadVideoRecord, GetPlayInfoResponse playInfoResponse) {
        if(playInfoResponse != null){
            List<GetPlayInfoResponse.PlayInfo> playInfoList = playInfoResponse.getPlayInfoList();
            GetPlayInfoResponse.VideoBase videoBase = playInfoResponse.getVideoBase();
            if(playInfoList != null && !playInfoList.isEmpty()){
                JSONArray jsonArray = new JSONArray();
                for (GetPlayInfoResponse.PlayInfo playInfo : playInfoList) {
                    JSONObject transJson = new JSONObject();
                    transJson.put("url", playInfo.getPlayURL().substring(0, playInfo.getPlayURL().lastIndexOf("?")));
                    transJson.put("format", playInfo.getFormat());
                    transJson.put("bitrate", playInfo.getBitrate());
                    transJson.put("resolution", playInfo.getWidth() + "x" + playInfo.getHeight());
                    transJson.put("duration" , playInfo.getDuration());
                    transJson.put("size", playInfo.getSize());
                    jsonArray.add(transJson);
                }
                if(videoBase != null){
                    uploadVideoRecord.setCoverUrl(videoBase.getCoverURL().substring(0, videoBase.getCoverURL().lastIndexOf("?")));
                }
                uploadVideoRecord.setTranscodeResult(jsonArray.toJSONString());
                uploadVideoRecord.setTranscodeStatus(2);
            }
        }
        uploadVideoRecordMapper.updateUploadVideoRecord(uploadVideoRecord);
    }

    public GetPlayInfoResponse getPlayInfo(DefaultAcsClient client,
                                           String videoId) throws Exception {
        GetPlayInfoRequest request = new GetPlayInfoRequest();
        request.setVideoId(videoId);
        return client.getAcsResponse(request);
    }

    public DefaultAcsClient initVodClient() {
        String regionId = "cn-shenzhen"; // 区域ID
        DefaultProfile profile = DefaultProfile.getProfile(regionId, aliyunVodConfig.getAccessKeyId(), aliyunVodConfig.getAccessKeySecret());
        return new DefaultAcsClient(profile);
    }
}
