package com.xgwc.task.schedule;

import com.xgwc.task.dao.DesignerStatisticsMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class DesignerStatisticsJob {

    @Resource
    private DesignerStatisticsMapper designerStatisticsMapper;

    @Scheduled(cron = "0 0 1 * * *")
    public void statistics() {

        log.info("设计师统计-------start");
        designerStatisticsMapper.syncDesigner();
        designerStatisticsMapper.orderStatis();
        designerStatisticsMapper.yearOrderStatis();
        //评分
        designerStatisticsMapper.scoreStatis();
        designerStatisticsMapper.yearScoreStatis();

        //平均分
        designerStatisticsMapper.scoreStatisAvg();
        designerStatisticsMapper.yearScoreStatisAvg();
        log.info("设计师统计-------end");
    }
}
