package com.xgwc.task.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "aliyun.oss")
@RefreshScope
public class AliyunOssConfig {

    /**
     * 地域节点
     */
    private String endpoint;
    /**
     * AccessKey
     */
    private String accessKeyId;
    /**
     * AccessKey秘钥
     */
    private String accessKeySecret;
    /**
     * bucket名称
     */
    private String bucketName;
    /**
     * 访问域名
     */
    private String host;

}
