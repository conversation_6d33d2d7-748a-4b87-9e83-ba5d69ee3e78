package com.xgwc.task.service;

import org.quartz.Job;
import org.quartz.SchedulerException;

/**
 * <AUTHOR>
 */
public interface ScheduleService {

    /**
     * 创建定时任务
     * @param jobName 任务名称
     * @param cron 表达式
     * @param triggerName triggerName
     * @param jobClass jobClass
     * @throws SchedulerException
     */
    void createJob(String jobName,String cron, String triggerName,Class<? extends Job> jobClass) throws SchedulerException;

    /**
     * 暂停job
     * @param jobName 任务名称
     * @throws SchedulerException
     */
    void pauseJob(String jobName) throws SchedulerException;

    /**
     * 恢复job
     * @param jobName 任务名称
     * @throws SchedulerException
     */
    void remuseJob(String jobName) throws SchedulerException;

    /**ErrorLogAlarmServiceImpl
     * 移除job
     * @param jobName 任务名称
     * @throws SchedulerException
     */
    void removeJob(String jobName,String triggerName) throws SchedulerException;
}
