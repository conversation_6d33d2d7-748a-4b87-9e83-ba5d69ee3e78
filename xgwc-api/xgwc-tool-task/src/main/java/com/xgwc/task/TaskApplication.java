package com.xgwc.task;

import com.xgwc.order.feign.api.XgwcPlatformJobFeign;
import com.xgwc.serviceProvider.feign.api.ServiceStaffFeign;
import com.xgwc.user.feign.api.NoticeFeign;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.xgwc.*"})
@MapperScan("com.xgwc.task.dao")
@EnableFeignClients(clients = {NoticeFeign.class, XgwcPlatformJobFeign.class, ServiceStaffFeign.class})
@Slf4j
@EnableScheduling
public class TaskApplication {

    public static void main(String[] args) {
        SpringApplication.run(TaskApplication.class, args);
        log.info("项目启动成功!");
    }

}