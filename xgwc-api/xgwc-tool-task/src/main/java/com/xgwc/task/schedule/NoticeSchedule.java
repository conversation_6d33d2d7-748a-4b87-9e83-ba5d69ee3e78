package com.xgwc.task.schedule;

import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class NoticeSchedule {

    @Bean
    public JobDetail getNoticeJob() {
        return JobBuilder.newJob(NoticeJob.class)
                .withIdentity("noticeJob", "notice")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger getNoticeTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(getNoticeJob())
                .withIdentity("noticeTrigger", "noticeTriggerGroup")
                .startNow()
                .withSchedule(CronScheduleBuilder.cronSchedule("0 0 0 1 * ?"))
                .build();
    }
}
