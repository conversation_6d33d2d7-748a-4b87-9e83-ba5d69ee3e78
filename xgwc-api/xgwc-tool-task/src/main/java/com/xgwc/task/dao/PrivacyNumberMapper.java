package com.xgwc.task.dao;

import com.xgwc.task.entity.PriavcyNumberCountDto;

import java.util.List;

public interface PrivacyNumberMapper {

    /**
     * 过期解除绑定  更新三天前未解绑的数据
     */
    int updateBindStatusForExpire();

    /**
     * 过期解除绑定, 限定1个小时内
     */
    int updateBindStatusForExpireInHour();

    /**
     * 统计数量
     * @return 返回结果
     */
    List<PriavcyNumberCountDto> countPrivacyNumber();

    /**
     * 批量更新隐私号绑定数量
     * @return 是否成功
     */
    int updatePrivacyNumberCount(List<PriavcyNumberCountDto> priavcyNumberCountDtoList);


}
