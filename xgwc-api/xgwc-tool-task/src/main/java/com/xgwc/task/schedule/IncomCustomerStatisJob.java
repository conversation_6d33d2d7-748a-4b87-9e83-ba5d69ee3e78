package com.xgwc.task.schedule;

import com.xgwc.task.dao.DesignerStatisticsMapper;
import com.xgwc.task.dao.IncomCustomerStatisMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IncomCustomerStatisJob {

    @Resource
    private IncomCustomerStatisMapper incomCustomerStatisMapper;

    @Scheduled(cron = "0 0 1 * * *")
    public void statistics() {

        log.info("客户统计-------start");
        //统计分类：
        // 1购买过的服务
        incomCustomerStatisMapper.insertService();
        // 2购买过的店铺
        incomCustomerStatisMapper.insertShop();
        // 3服务过ta的客服
        incomCustomerStatisMapper.insertSale();
        // 4服务过ta的设计师
        incomCustomerStatisMapper.insertDesigner();
        // 5喜爱的支付方式
        incomCustomerStatisMapper.insertPaychannel();
        // 6喜爱的付款方式
        incomCustomerStatisMapper.insertPaytype();
        // 7提需求的渠道
        incomCustomerStatisMapper.insertChannel();
        // 8提过需求的业务
        incomCustomerStatisMapper.insertLineService();
        // 9需求有效性
        incomCustomerStatisMapper.insertLinevalid();
        // 10与ta沟通过的客服
        incomCustomerStatisMapper.insertLineSale();

        log.info("客户统计-------end");
    }
}
