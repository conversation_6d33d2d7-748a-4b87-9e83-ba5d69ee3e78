package com.xgwc.task.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class BillDesignerSub {

    /** 子账单ID */
    private Long subBillId;

    /** 主账单id */
    private Long billId;

    /** 设计师id */
    private Long stylistId;

    /** 设计师名称 */
    private String stylistName;

    /** 管理员用户id */
    private Long managerUserId;

    /** 账单周期开始日期 */
    private String billPeriodStart;

    /** 账单周期结束日期 */
    private String billPeriodEnd;

    /** 账单订单数 */
    private Integer orderCount;

    /** 账单佣金合计 */
    private BigDecimal totalCommission;

    /** 结算品牌商 */
    private Long brandOwnerId;

    /** 罚款金额 */
    private BigDecimal fineAmount;

    /** 无票扣款 */
    private BigDecimal noInvoice;

    /** 佣金追回 */
    private BigDecimal commissionBack;

    /** 出账时间 */
    private String billingTime;

    /** 确认时间 */
    private String confirmationTime;

    /** 结算时间 */
    private String settlementTime;

    /** 结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败) */
    private Integer settlementStatus;

    /** 是否锁定：0锁定，1未锁定 */
    private Integer isLock;

    /** 状态：0正常，其他非正常 */
    private Integer status;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private String createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private String updateTime;

    /** 行修改时间 */
    private String modifyTime;

    /** 加盟商id */
    private Long franchiseId;

    /** 品牌商 */
    private Long brandId;

    /**
     * 公司主体ID
     */
    private Long companyInfoId;

    /**
     * 应发金额
     */
    private BigDecimal payableAmount;

    /**
     * 实发金额
     */
    private BigDecimal realAmount;

}