package com.xgwc.task.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
public class CommissionRecoverBill {

    /** 主键 */
    private Long id;

    /** 账单ID */
    private Long billId;

    /** 账单开始 */
    private String billStart;

    /** 账单结束 */
    private String billEnd;

    /** 设计师ID */
    private Long desinerId;

    /** 追回金额 */
    private BigDecimal recoveredAmount;

    /** 0正常，1非正常 */
    private Long status;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String updateTime;



}