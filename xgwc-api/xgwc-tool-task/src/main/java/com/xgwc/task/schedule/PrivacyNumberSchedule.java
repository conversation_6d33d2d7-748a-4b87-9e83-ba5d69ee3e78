package com.xgwc.task.schedule;

import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PrivacyNumberSchedule {

    @Bean
    public JobDetail getPrivacyNumberJob() {
        return JobBuilder.newJob(PrivacyNumberJob.class)
                .withIdentity("privacyNumberJob", "privacyNumber")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger getPrivacyNumberTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(getPrivacyNumberJob())
                .withIdentity("privacyNumberTrigger", "privacyNumberTriggerGroup")
                .startNow()
                .withSchedule(CronScheduleBuilder.cronSchedule("0 0 0 1 * ?"))
                .build();
    }
}
