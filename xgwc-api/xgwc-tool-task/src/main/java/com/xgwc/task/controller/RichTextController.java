package com.xgwc.task.controller;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.task.service.RichTextService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("richtext")
@RestController
public class RichTextController {

    @Resource
    private RichTextService richTextService;

    @RequestMapping("handleRichText")
    public ApiResult handleRichText(Integer id){
        richTextService.asyncHanleRichText(id);
        return ApiResult.ok();
    }
}
