package com.xgwc.task.dao;

import com.xgwc.task.entity.UploadVideoRecord;

import java.util.List;

public interface UploadVideoRecordMapper {

    /**
     * 修改视频上传记录
     * @param uploadVideoRecord 参数
     * @return 插入结果
     */
    int updateUploadVideoRecord(UploadVideoRecord uploadVideoRecord);

    /**
     * 获取未转码的视频列表
     * @return 视频列表
     */
    List<UploadVideoRecord> getNotTranscodeList();
}
