package com.xgwc.task.schedule;

import com.xgwc.task.service.PrivacyNumberService;
import jakarta.annotation.Resource;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Component
public class PrivacyNumber<PERSON><PERSON> extends QuartzJobBean {

    @Resource
    private PrivacyNumberService privacyNumberService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        privacyNumberService.preStaticsPrivacyNumberCount();
    }
}
