package com.xgwc.task.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillOrderDto {

    /**
     * 账单开始
     */
    private String billStart;

    /**
     * 账单结束
     */
    private String billEnd;

    /**
     * 主键
     */
    private Long id;

    /**
     * 主账单ID
     */
    private Long billId;

    /**
     * 账单id
     */
    private Long subBillId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 谈单人员
     */
    private Long saleManId;

    /**
     * 谈单人员名称
     */
    private String saleManName;

    /**
     * 录入部门编码
     */
    private Long deptId;

    /**
     * 录入部门名称
     */
    private String deptName;

    /**
     * 订单类型：0正常单，1转化单,2协作单，3拍错单
     */
    private Integer transferState;

    /**
     * 下单日期
     */
    private String orderDate;

    /**
     * 订单来源ID（店铺id）
     */
    private Long storeId;

    /**
     * 订单来源名称（店铺名称）
     */
    private String storeName;

    /**
     * 所属业务编号-字典
     */
    private String stateDicCode;

    /**
     * 所属业务名称-字典
     */
    private String stateDicName;

    /**
     * 品牌商id
     */
    private Long brandId;

    /**
     * 支付方式  1:淘宝  2：微信  3：支付宝
     */
    private Integer payChannel;

    /**
     * 订单状态（0：未发货 1：完成  2：退款 3：部分退款）
     */
    private Integer shType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 客户ID
     */
    private String taobaoId;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 实收金额
     */
    private BigDecimal amount;

    /**
     * 当前金额：当有退款发生时有值
     */
    private BigDecimal nowAmount;

    /**
     * 付款方式:1全款/2阶段付
     */
    private Long payType;

    /**
     * 派单需求
     */
    private String allotRemark;

    /**
     * 派单设计师数量
     */
    private Long allotNum;

    /**
     * 派单人
     */
    private Long allotUserId;

    /**
     * 派单人
     */
    private String allotUserName;

    /**
     * 母订单id
     */
    private Long pid;

    /**
     * 设计师ID
     */
    private Long designerId;

    /**
     * 设计师名称
     */
    private String designerName;

    /**
     * 设计师业务类型
     */
    private String designerBusiness;

    /**
     * 佣金金额
     */
    private BigDecimal money;

    /**
     * 现佣金：当有退款发生时有值
     */
    private BigDecimal nowMoney;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 加盟商id
     */
    private Long franchiseId;

    /**
     * 行更新时间
     */
    private String modifyTime;

    /**
     * 公司主体ID
     */
    private Long companyInfoId;

    /**
     * 成交时间
     */
    private String dealTime;

    /**
     * 提交定稿时间
     */
    private String archiveTime;

    /**
     * 锁定：0未锁定，1锁定
     */
    private Integer isLock;

    /**
     * 锁定时间
     */
    private String lockTime;

    /**
     * 剩余佣金追回
     */
    private BigDecimal commissionBackRemaining;

}
