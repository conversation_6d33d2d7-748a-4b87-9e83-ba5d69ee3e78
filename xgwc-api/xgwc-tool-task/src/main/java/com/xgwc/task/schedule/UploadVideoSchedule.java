package com.xgwc.task.schedule;

import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UploadVideoSchedule {

    @Bean
    public JobDetail getUploadVideoJob() {
        return JobBuilder.newJob(UploadVideoJob.class)
                .withIdentity("uploadVideoJob", "uploadVideo")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger getUploadVideoTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(getUploadVideoJob())
                .withIdentity("uploadVideoTrigger", "uploadVideoTriggerGroup")
                .startNow()
                .withSchedule(CronScheduleBuilder.cronSchedule("0 0 0 1 * ?"))
                .build();
    }
}
