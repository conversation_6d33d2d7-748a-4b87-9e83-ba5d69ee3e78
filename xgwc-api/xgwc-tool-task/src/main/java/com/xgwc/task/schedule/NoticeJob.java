package com.xgwc.task.schedule;

import com.xgwc.task.service.NoticeService;
import jakarta.annotation.Resource;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Component
public class NoticeJob extends QuartzJobBean {

    @Resource
    private NoticeService noticeService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        noticeService.buildNoticeTask();
    }
}
