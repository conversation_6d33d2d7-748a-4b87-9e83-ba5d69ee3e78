package com.xgwc.task.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.xgwc.common.util.StringUtils;
import com.xgwc.task.config.AliyunOssConfig;
import com.xgwc.task.dao.CustomerResourceMapper;
import com.xgwc.task.entity.CustomerResource;
import com.xgwc.task.service.RichTextService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class RichTextServiceImpl implements RichTextService {

    private static final Pattern IMG_PATTERN = Pattern.compile("<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>");

    public static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd/HHmmss");

    /**
     * 线程池
     */
    private ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 阻塞队列
     */
    private BlockingQueue<CustomerResource> blockingQueue = new ArrayBlockingQueue<>(1000);

    private AtomicInteger atomicInteger = new AtomicInteger(0);

    @Resource
    private CustomerResourceMapper customerResourceMapper;

    private static Random random;

    static{
        try {
            random = new Random();
        } catch (Exception e) {
            log.error("生成随机数错误error:", e);
        }
    }

    @Resource
    private AliyunOssConfig aliyunOssConfig;

    public void processBase64Images(CustomerResource customerResource) {
        log.info("处理id:{},队列数量:{}, 已处理条数:{}", customerResource.getId(), blockingQueue.size(), atomicInteger.incrementAndGet());
        Matcher matcher = IMG_PATTERN.matcher(customerResource.getPmRemark());
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String srcValue = matcher.group(1);
            if (srcValue.startsWith("data:image")) {
                try {
                    String imageUrl = uploadBase64Images(srcValue);
                    matcher.appendReplacement(result, matcher.group().replace(srcValue, imageUrl));
                } catch (Exception e) {
                    // 处理失败时保留原图
                    matcher.appendReplacement(result, matcher.group());
                }
            } else {
                matcher.appendReplacement(result, matcher.group());
            }
        }
        matcher.appendTail(result);
        customerResource.setPmRemark(result.toString());
        customerResourceMapper.updateCustomerResource(customerResource);
    }

    public String uploadBase64Images(String base64Content) {
        String fileName = buildFileName() + "." + getImageExtensionFromBase64(base64Content);
        boolean isSuccess = uploadFile(base64Content, fileName);
        if(isSuccess){
            System.out.println(fileName);
        }else {
            System.out.println("上传失败");
        }
        return aliyunOssConfig.getHost() + "/" + fileName;
    }

    public boolean uploadFile(String base64, String filePath)  {
        // 生成 OSSClient
        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(), aliyunOssConfig.getAccessKeyId(), aliyunOssConfig.getAccessKeySecret());
        try {
            ossClient.putObject(aliyunOssConfig.getBucketName(), filePath, base64WithDataUriToInputStream(base64));
            return true;
        } catch (Exception e) {
            log.error("上传阿里云失败:", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return false;
    }

    public static String buildFileName(){
        LocalDateTime now = LocalDateTime.now();
        String fileName = now.format(formatter);
        StringBuilder randomPart = new StringBuilder();
        for (int i = 0; i < 3; i++) {
            randomPart.append(random.nextInt(10)); // 生成0-9的随机数字
        }
        return "file".concat("/").concat(fileName) + "/" +randomPart + "/" + StringUtils.remove(UUID.randomUUID().toString(), "-");
    }

    /**
     * 根据base64获取图片后缀
     */
    public static String getImageExtensionFromBase64(String base64) {
        if (base64.startsWith("data:image/")) {
            // 格式如：data:image/png;base64,...
            String mimeType = base64.substring(5, base64.indexOf(";"));
            return switch (mimeType) {
                case "image/jpeg" -> "jpg";
                case "image/png" -> "png";
                case "image/gif" -> "gif";
                case "image/webp" -> "webp";
                case "image/bmp" -> "bmp";
                // 添加更多支持的格式...
                default -> mimeType.substring(6); // 去掉"image/"前缀
            };
        }
        return null; // 无法确定格式
    }

    /**
     * 根据base6转inputstream
     */
    public InputStream base64WithDataUriToInputStream(String base64WithDataUri) {
        // 移除数据URI部分（如果有）
        String base64Data = base64WithDataUri.split(",").length > 1
                ? base64WithDataUri.split(",")[1]
                : base64WithDataUri;

        // 解码并返回InputStream
        byte[] decodedBytes = Base64.getDecoder().decode(base64Data);
        return new ByteArrayInputStream(decodedBytes);
    }

    @Override
    public void asyncHanleRichText(Integer id) {
        List<CustomerResource> customerResources;
        if(id != null){
            customerResources = customerResourceMapper.selectCustomerResourceById(id);
        }else{
            customerResources = customerResourceMapper.selectCustomerResource();
        }
        while (customerResources != null && !customerResources.isEmpty()) {
            for(CustomerResource customerResource : customerResources){
                handleCustomerResource(customerResource);
            }
            //指定倒数第一个
            id = customerResources.get(customerResources.size() - 1).getId();

            customerResources = customerResourceMapper.selectCustomerResourceById(id);
        }
    }

    private void handleCustomerResource(CustomerResource customerResource) {
        try {
            blockingQueue.put(customerResource);
            executorService.submit(() -> {
                CustomerResource customerResource1 = null;
                try {
                    customerResource1 = blockingQueue.take();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                processBase64Images(customerResource1);
            });
        } catch (Exception e) {
            log.info("处理失败:", e);
        }
    }
}
