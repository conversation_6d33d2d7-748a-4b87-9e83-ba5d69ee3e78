package com.xgwc.task.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .authorizeHttpRequests(auth -> auth
                        // 1. 放行所有公共API（按需添加具体路径）
                        .requestMatchers("/*/**", "/api/open/**").permitAll()
                        // 3. 其他请求也放行（根据需求调整）
                        .anyRequest().permitAll()
                )
                // 4. 启用HTTP Basic认证（仅用于Actuator）
                .httpBasic(withDefaults())

                // 5. 禁用CSRF（若API是无状态的，推荐禁用）
                .csrf(csrf -> csrf.disable());
        return http.build();
    }
}
