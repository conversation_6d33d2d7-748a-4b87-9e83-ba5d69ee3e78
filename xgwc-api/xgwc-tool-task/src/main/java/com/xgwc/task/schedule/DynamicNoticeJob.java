package com.xgwc.task.schedule;

import com.xgwc.task.config.TaskKeyConfig;
import com.xgwc.task.service.ScheduleService;
import com.xgwc.task.service.impl.NoticeServiceImpl;
import com.xgwc.task.service.impl.ScheduleServiceImpl;
import com.xgwc.user.feign.api.NoticeFeign;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DynamicNoticeJob extends QuartzJobBean {

    @Resource
    private NoticeFeign noticeFeign;

    @Resource
    private TaskKeyConfig taskKeyConfig;

    @Resource
    private ScheduleService scheduleService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        //获取jobName
        String jobName = context.getJobDetail().getKey().getName();
        log.info("开始执行通知任务,任务名称:" + jobName);
        //从jobName中获取通知id
        Integer noticeId = Integer.valueOf(jobName.replace(NoticeServiceImpl.JOB_NAME, ""));
        //通过feign调用发送通知服务
        log.info("开始发送通知, 通知id:{}", noticeId);
        noticeFeign.sendNotice(noticeId, taskKeyConfig.getKey());
        log.info("通知发送完成, 通知id:{}", noticeId);
        //执行完任务后销毁该job
        try {
            scheduleService.removeJob(NoticeServiceImpl.JOB_NAME + noticeId, NoticeServiceImpl.TRIGGER_NAME + noticeId);
            NoticeServiceImpl.scheduleNoticeMap.remove(noticeId);
        } catch (SchedulerException e) {
            log.error("任务:{}执行完成移除任务失败,失败原因：", jobName, e);
        }

    }
}
