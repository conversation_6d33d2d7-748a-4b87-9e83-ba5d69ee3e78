package com.xgwc.task.dao;

import com.xgwc.task.entity.CustomerResource;

import java.util.List;

public interface CustomerResourceMapper {

    /**
     * 根据id获取进线备注
     * @param id 主键id
     * @return 进线信息
     */
    List<CustomerResource> selectCustomerResourceById(Integer id);

    /**
     * 根据id获取进线备注
     * @return 进线信息
     */
    List<CustomerResource> selectCustomerResource();

    /**
     * 修改进线备注
     */
    int updateCustomerResource(CustomerResource customerResource);
}
