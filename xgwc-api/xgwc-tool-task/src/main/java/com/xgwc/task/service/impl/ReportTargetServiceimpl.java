package com.xgwc.task.service.impl;

import com.xgwc.task.service.ReportTargetService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * 品牌商和加盟商目标任务
 */
@Service
public class ReportTargetServiceimpl implements ReportTargetService {


    @Override
    public void staticsReportTarget() {
        LocalDate nowDate = LocalDate.now();
        //如果是本月第一天，则统计上个月数据
        if(nowDate.getDayOfMonth() == 1){

        }else{
            //非本月最后一天，统计本月数据
        }
    }
}
