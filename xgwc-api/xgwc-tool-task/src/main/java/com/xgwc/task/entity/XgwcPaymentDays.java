package com.xgwc.task.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class XgwcPaymentDays {

    /**
     * 品牌商ID
     */
    private Long brandId;

    /**
     * 周期：1一周一结,2半个月一结,3一个月一结,4一个季度一结
     */
    private Integer cycleType;

    /**
     * 1设计师 2客服
     */
    private Integer paymentType;

    /**
     * 扣点
     */
    private BigDecimal taxMoney;

}
