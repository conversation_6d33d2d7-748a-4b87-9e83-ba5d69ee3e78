package com.xgwc.task.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class CronUtil {

    /**
     * 获取cron表达式
     */
    public static String generateCronExpression(String timeString) {
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 解析输入的时间字符串
        LocalDateTime dateTime = LocalDateTime.parse(timeString, formatter);

        // 生成Cron表达式
        String cronExpression = String.format("%d %d %d %d %d ?",
                dateTime.getSecond(), // 秒
                dateTime.getMinute(), // 分
                dateTime.getHour(),   // 小时
                dateTime.getDayOfMonth(), // 日期
                dateTime.getMonthValue() // 月份
        );
        return cronExpression;
    }
}
