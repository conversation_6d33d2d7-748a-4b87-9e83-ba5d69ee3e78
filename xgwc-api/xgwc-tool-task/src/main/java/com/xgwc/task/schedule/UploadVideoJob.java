package com.xgwc.task.schedule;

import com.xgwc.task.service.NoticeService;
import com.xgwc.task.service.UploadVideoRecordSerivce;
import jakarta.annotation.Resource;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Component
public class UploadVideoJob extends QuartzJobBean {

    @Resource
    private UploadVideoRecordSerivce uploadVideoRecordSerivce;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        uploadVideoRecordSerivce.getTranscodeInfo();
    }
}
