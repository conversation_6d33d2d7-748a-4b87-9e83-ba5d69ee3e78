package com.xgwc.task.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xgwc.task.dao.NoticeMapper;
import com.xgwc.task.entity.ScheduleNotice;
import com.xgwc.task.schedule.DynamicNoticeJob;
import com.xgwc.task.service.NoticeService;
import com.xgwc.task.service.ScheduleService;
import com.xgwc.task.util.CronUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class NoticeServiceImpl implements NoticeService {

    @Resource
    private NoticeMapper noticeMapper;

    public static final String JOB_NAME = "job_notice_";

    public static final String TRIGGER_NAME = "triggerName_notice_";

    private static boolean IS_FIRST = true;

    @Resource
    private ScheduleService scheduleService;

    /**
     * 存储待消费的任务
     */
    public static Map<Integer, ScheduleNotice> scheduleNoticeMap = new HashMap<>();

    @Override
    public void buildNoticeTask(){
        List<ScheduleNotice> list;
        //第一次全部扫描
        if(IS_FIRST){
            list = noticeMapper.selectScheduleNoticeIdList();
            log.info("第一次全部扫描所有通知,存在定时通知:{}", list != null ? JSONObject.toJSONString(list) : "");
            IS_FIRST = false;
        }else{
            list = noticeMapper.noticeListLimitFiveMinuter();
            log.info("5分组扫描通知,存在定时通知:{}", list != null ? JSONObject.toJSONString(list) : "");
        }
        if(list != null && !list.isEmpty()){
            list.forEach(notice->{
                String cron = CronUtil.generateCronExpression(notice.getPublishTime());
                String jobName = JOB_NAME + notice.getId();
                String triggerName = TRIGGER_NAME + notice.getId();
                ScheduleNotice scheduleNotice = scheduleNoticeMap.get(notice.getId());
                //如果不为空，则说明已经加入到队列中
                if(scheduleNotice != null){
                    //比对一下两者时间，如果不一致则需要修改task
                    if(!scheduleNotice.getPublishTime().equals(notice.getPublishTime())){
                        try {
                            scheduleNoticeMap.put(notice.getId(), notice);
                            log.info("已经存在通知:{}任务, 前通知时间:{}, 后通知时间:{}, 开始移除旧任务", notice.getId(), scheduleNotice.getPublishTime(), notice.getPublishTime());
                            scheduleService.removeJob(jobName, triggerName);
                            log.info("已经存在通知:{}任务, 前通知时间:{}, 后通知时间:{}, 开始创建任务", notice.getId(), scheduleNotice.getPublishTime(), notice.getPublishTime());
                            scheduleService.createJob(jobName, cron, triggerName, DynamicNoticeJob.class);
                        } catch (SchedulerException e) {
                            log.info("移除通知定时任务失败,notice_id:{},失败原因:", notice.getId(), e);
                        }
                    }else{

                    }
                }else{
                    //不存在则创建任务
                    try {
                        scheduleNoticeMap.put(notice.getId(), notice);
                        log.info("创建任务, 任务信息:{}", JSONObject.toJSONString(notice));
                        scheduleService.createJob(jobName, cron, triggerName, DynamicNoticeJob.class);
                    } catch (SchedulerException e) {
                        log.error("创建任务失败,notice_id:{}, 失败原因:", notice.getId(), e);
                    }
                }
            });
        }
    }
}
