package com.xgwc.task.service.impl;

import com.xgwc.task.service.ScheduleService;
import jakarta.annotation.Resource;
import org.quartz.*;
import org.springframework.stereotype.Service;

/**
 * 定时任务服务类
 * <AUTHOR>
 */
@Service
public class ScheduleServiceImpl implements ScheduleService {

    @Resource
    private Scheduler scheduler;

    private static final String GROUP = "monitorJobGroup";

    @Override
    public void createJob(String jobName,String cron, String triggerName,Class<? extends Job> jobClass) throws SchedulerException {

        JobKey jobKey = new JobKey(jobName,GROUP);
        // 如果存在这个任务，则删除
        if(scheduler.checkExists(jobKey)) {
            scheduler.deleteJob(jobKey);
        }
        JobDetail jobDetail = JobBuilder.newJob(jobClass)
                .withIdentity(jobKey)
                .build();
        CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(cron);
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(triggerName,GROUP)
                .withSchedule(cronScheduleBuilder).build();
        scheduler.scheduleJob(jobDetail,trigger);
    }

    @Override
    public void pauseJob(String jobName) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey(jobName, GROUP);
        JobDetail jobDetail = scheduler.getJobDetail(jobKey);
        if (jobDetail == null) {
            return;
        }
        scheduler.pauseJob(jobKey);
    }

    @Override
    public void remuseJob(String jobName) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey(jobName, GROUP);
        JobDetail jobDetail = scheduler.getJobDetail(jobKey);
        if (jobDetail == null) {
            return;
        }
        scheduler.resumeJob(jobKey);
    }

    @Override
    public void removeJob(String jobName,String triggerName) throws SchedulerException {
        TriggerKey triggerKey = TriggerKey.triggerKey(triggerName, "taskTriggerGroup");
        JobKey jobKey = JobKey.jobKey(jobName, "taskJobGroup");
        Trigger trigger =  scheduler.getTrigger(triggerKey);
        if (trigger == null) {
            return;
        }
        // 停止触发器
        scheduler.pauseTrigger(triggerKey);
        // 移除触发器
        scheduler.unscheduleJob(triggerKey);
        // 删除任务
        scheduler.deleteJob(jobKey);
    }
}
