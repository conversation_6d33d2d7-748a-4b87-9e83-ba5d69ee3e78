package com.xgwc.task.dao;

import java.util.List;
import java.util.Map;

public interface DesignerStatisticsMapper {

    /**
     * 同步设计师
     * @return
     */
    int syncDesigner();

    /**
     * 更新 订单统计数据
     * @return
     */
    int orderStatis();

    /**
     * 更新订单近1年数据
     * @return
     */
    int yearOrderStatis();


    /**
     * 同步设计师评分
     * @return
     */
    int scoreStatis();
    int yearScoreStatis();

    /**
     * 更新平均分
     * @return
     */
    int scoreStatisAvg();
    int yearScoreStatisAvg();
}
