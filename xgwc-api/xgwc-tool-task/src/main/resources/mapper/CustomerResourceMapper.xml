<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.task.dao.CustomerResourceMapper">

    <select id="selectCustomerResourceById" resultType="com.xgwc.task.entity.CustomerResource">
        SELECT
            id,
            pm_remark
        FROM
            t_in_customer_resource t
        where t.id &gt; #{id}
            order by id asc
            limit 100
    </select>

    <select id="selectCustomerResource" resultType="com.xgwc.task.entity.CustomerResource">
        SELECT
            id,
            pm_remark
        FROM
            t_in_customer_resource t
        order by id asc
            limit 100
    </select>

    <update id="updateCustomerResource">
        update t_in_customer_resource set pm_remark = #{pmRemark} where id = #{id}
    </update>
</mapper>