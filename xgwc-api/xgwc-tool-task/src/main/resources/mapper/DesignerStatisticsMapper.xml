<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.task.dao.DesignerStatisticsMapper">

    <insert id="syncDesigner">
        INSERT INTO xgwc_designer_statis (`designer_id`, `designer_name`, `designer_level`, `designer_phone`,emergency_name, `emergency_phone`,`business_id`,`business_name`,brand_id)
        SELECT `designer_id`, `name` designer_name, `designer_level`, `phone`, emergency_name, `emergency_phone`,b.business_id,b.business_name, a.brand_id
        FROM xgwc_designer a
        left join xgwc_business b on a.`good_business` = b.business_id
        WHERE a.modify_time > (
            SELECT IFNULL(MAX(b.modify_time), '1970-01-01')
            FROM xgwc_designer_statis b
        )
            ON DUPLICATE KEY UPDATE
             designer_name = VALUES(designer_name),
             designer_level = VALUES(designer_level),
             designer_phone = VALUES(designer_phone),
             emergency_name = VALUES(emergency_name),
             emergency_phone = VALUES(emergency_phone),
             business_id = VALUES(business_id),
             business_name = VALUES(business_name),
             brand_id = VALUES(brand_id)
    </insert>

    <update id="orderStatis" >
        update xgwc_designer_statis ds left join
            (select
                o.brand_id,
                o.designer_id,
                max(so.order_time) order_last_time,
                group_concat(DISTINCT o2.state_dic_name,'(',o2.num,')') his_order_business,
                ROUND(sum(ifnull(now_money,money))/count(1),2) avg_money,
                count(1) order_num,
                sum(if(o.archive_type &lt; 5,1,0)) no_archive
            from xgwc_order o
            left join stylist_orders so on o.id = so.order_id
            left join (
                select
                    o.brand_id,
                    o.designer_id,
                    o.state_dic_name,
                    count(1) num
                from xgwc_order o
                where archive_type > 0 and  o.is_del = 0
                GROUP BY  o.brand_id,o.designer_id,o.state_dic_name
            ) o2 on o.brand_id = o2.brand_id and o.designer_id = o2.designer_id
            where o.is_del = 0 and o.archive_type > 0
            group by o.brand_id,o.designer_id
            ) ss on ds.brand_id = ss.brand_id and ds.designer_id = ss.designer_id
            set ds.order_last_time = ss.order_last_time,
                ds.avg_money = ss.avg_money,
                ds.order_num = ss.order_num,
                ds.no_archive = ss.no_archive,
                ds.his_order_business = ss.his_order_business
    </update>

    <update id="yearOrderStatis">
        update xgwc_designer_statis ds left join
            (select
                o.brand_id,
                o.designer_id,
                any_value(o2.num) order_year_num,
                any_value(o2.archive_num) year_archive_num,
                any_value(_os.good_num) good_num,
                sum(if(o.refund_status > 0, 1, 0)) refund_num
            from xgwc_order o
            left join (
                select
                    oder_id,
                    designer_id,
                    sum(if(score_num > 3, 1,0)) good_num
                from
                    xgwc_order_score
                where score_model = 'evaluation'
                group by oder_id,designer_id
            ) _os on _os.oder_id = o.id and _os.designer_id = o.designer_id
            left join (
                select
                    brand_id,
                    designer_id,
                    count(1) num,
                    sum(if(archive_type = 5,1,0)) archive_num
                from xgwc_order
                where archive_type > 0 and is_del = 0
                GROUP BY  brand_id,designer_id
                ) o2 on o.brand_id = o2.brand_id and o.designer_id = o2.designer_id
                where
                    o.order_date >=  DATE_SUB(NOW(), INTERVAL 1 YEAR)
                    and o.is_del = 0 and o.archive_type > 0
                group by o.brand_id,o.designer_id
            ) ss on ds.brand_id = ss.brand_id and ds.designer_id = ss.designer_id
            set ds.order_year_num = ss.order_year_num,
                ds.good_num = ss.good_num,
                ds.year_archive_num = ss.year_archive_num,
                ds.refund_num = ss.refund_num
    </update>


    <insert id="scoreStatis">
        INSERT INTO xgwc_designer_statis_score(brand_id,designer_id,business_id,score_model,score_total,score_num,good_num,general_num,poor_num)
        select
            brand_id,designer_id,business_id,score_model, score_total,score_num,good_num,general_num,poor_num
        FROM (
        select
            o.brand_id,
            _os.designer_id,
            o.state_dic_code business_id,
            _os.score_model,
            ROUND(sum(_os.score_num)/ (5 * count(1) )  * 100 ) score_total,
            count(1) score_num,
            sum(if(_os.score_num > 3, 1,0)) good_num,
            sum(if(_os.score_num = 3, 1,0)) general_num,
            sum(if(_os.score_num &lt; 3, 1,0)) poor_num,
            max(ifnull(_os.update_time, _os.create_time)) modify_time
            from xgwc_order_score _os
            left join xgwc_order o on _os.oder_id = o.id and _os.designer_id = o.designer_id
            where ifnull(o.brand_id ,0) > 0 and o.is_del = 0
            group by o.brand_id,_os.designer_id, o.state_dic_code,_os.score_model
        ) a WHERE a.modify_time > (
        SELECT IFNULL(MAX(b.modify_time), '1970-01-01')
        FROM xgwc_designer_statis_score b
        )  ON DUPLICATE KEY UPDATE
            score_total = VALUES(score_total),
            score_num = VALUES(score_num),
            good_num = VALUES(good_num),
            general_num = VALUES(general_num),
            poor_num = VALUES(poor_num)
    </insert>

    <insert id="yearScoreStatis">
        INSERT INTO xgwc_designer_statis_score2(brand_id,designer_id,business_id,score_model,score_total,score_num,good_num,general_num,poor_num)
        select
            brand_id,designer_id,business_id,score_model, score_total,score_num,good_num,general_num,poor_num
        FROM (
                 select
                     o.brand_id,
                     _os.designer_id,
                     o.state_dic_code business_id,
                     _os.score_model,
                     ROUND(sum(_os.score_num)/ (5 * count(1) )  * 100 ) score_total,
                     count(1) score_num,
                     sum(if(_os.score_num > 3, 1,0)) good_num,
                     sum(if(_os.score_num = 3, 1,0)) general_num,
                     sum(if(_os.score_num &lt; 3, 1,0)) poor_num,
                     max(ifnull(_os.update_time, _os.create_time)) modify_time
                 from xgwc_order_score _os
                 left join xgwc_order o on _os.oder_id = o.id and _os.designer_id = o.designer_id
                 where ifnull(o.brand_id ,0) > 0 and o.is_del = 0
                     and o.order_date >=  DATE_SUB(NOW(), INTERVAL 1 YEAR)
                 group by o.brand_id,_os.designer_id, o.state_dic_code,_os.score_model
             ) a WHERE a.modify_time > (
            SELECT IFNULL(MAX(b.modify_time), '1970-01-01')
            FROM xgwc_designer_statis_score2 b
        )  ON DUPLICATE KEY UPDATE
            score_total = VALUES(score_total),
            score_num = VALUES(score_num),
            good_num = VALUES(good_num),
            general_num = VALUES(general_num),
            poor_num = VALUES(poor_num)
    </insert>


    <update id="scoreStatisAvg">
        UPDATE xgwc_designer_statis_score ss
        LEFT JOIN ( SELECT brand_id, business_id, score_model, avg( score_total ) avg_total FROM xgwc_designer_statis_score GROUP BY brand_id, business_id, score_model ) s ON ss.brand_id = s.brand_id
        AND ss.business_id = s.business_id
        AND ss.score_model = s.score_model
        SET ss.avg_total = s.avg_total
    </update>

    <update id="yearScoreStatisAvg">
        UPDATE xgwc_designer_statis_score2 ss
        LEFT JOIN ( SELECT brand_id, business_id, score_model, avg( score_total ) avg_total FROM xgwc_designer_statis_score2 GROUP BY brand_id, business_id, score_model ) s ON ss.brand_id = s.brand_id
        AND ss.business_id = s.business_id
        AND ss.score_model = s.score_model
        SET ss.avg_total = s.avg_total
    </update>
</mapper>