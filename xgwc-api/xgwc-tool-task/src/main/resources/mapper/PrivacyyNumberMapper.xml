<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.task.dao.PrivacyNumberMapper">
    
    <update id="updateBindStatusForExpire">
        update privacy_number_bind set bind_status = 1 where expire_time &lt;=now() and bind_status = 0
    </update>

    <update id="updateBindStatusForExpireInHour">
        update privacy_number_bind set bind_status = 1 where update_time &gt;= (NOW()- INTERVAL 30 MINUTE)  and bind_status = 0 and expire_time &lt;=now()
    </update>

    <select id="countPrivacyNumber" resultType="com.xgwc.task.entity.PriavcyNumberCountDto">
        select middle_number number, count(1) count  from privacy_number_bind where bind_status = 1 group by middle_number
    </select>

    <update id="updatePrivacyNumberCount">
        UPDATE privacy_number
        SET count = CASE
        <foreach collection="list" item="item" index="index" open="" separator="" close="">
            <if test="item.number != null and item.count != null">
                WHEN privacy_number = #{item.number} THEN #{item.count}
            </if>
        </foreach>
        END
        WHERE privacy_number IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            <if test="item.number != null">
                #{item.number}
            </if>
        </foreach>
    </update>
</mapper>