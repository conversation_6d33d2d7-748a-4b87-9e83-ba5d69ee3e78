<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.task.dao.UploadVideoRecordMapper">

    <select id="getNotTranscodeList" resultType="com.xgwc.task.entity.UploadVideoRecord">
        select id, t.video_id videoId from upload_video_record t where t.`status` = 0 and t.transcode_status = 0
    </select>

    <update id="updateUploadVideoRecord">
        update upload_video_record
            <set>
                update_time = now(), transcode_result = #{transcodeResult}, transcode_status = #{transcodeStatus}, cover_url = #{coverUrl}
            </set>
            <where>
                id = #{id}
            </where>
    </update>
</mapper>