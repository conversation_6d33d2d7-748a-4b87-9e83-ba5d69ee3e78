<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.task.dao.IncomCustomerStatisMapper">


    <insert id="insertService">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            1,
            state_dic_code,
            any_value ( state_dic_name ),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            xgwc_order
        where ifnull(state_dic_code, 0) > 0
        GROUP BY taobao_id, brand_id, franchise_id, state_dic_code
        ON DUPLICATE KEY UPDATE
         type_name = VALUES(type_name),
         type_num = VALUES(type_num)
    </insert>
    <insert id="insertShop">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            2,
            store_id,
            any_value ( store_name ),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            xgwc_order
        where ifnull(store_id, 0) > 0
          and ifnull(franchise_id, 0) > 0
        GROUP BY taobao_id, brand_id, franchise_id, store_id
        ON DUPLICATE KEY UPDATE
         type_name = VALUES(type_name),
         type_num = VALUES(type_num)
    </insert>
    <insert id="insertSale">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            3,
            sale_man_id,
            any_value ( sale_man_name ),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            xgwc_order
        where ifnull(sale_man_id, 0) > 0
        GROUP BY taobao_id, brand_id, franchise_id, sale_man_id
        ON DUPLICATE KEY UPDATE
        type_name = VALUES(type_name),
        type_num = VALUES(type_num)
    </insert>
    <insert id="insertDesigner">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            4,
            designer_id,
            CONCAT(any_value ( designer_name ),'@', any_value ( designer_phone )),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            xgwc_order
        where ifnull(designer_id, 0) > 0
        GROUP BY taobao_id, brand_id, franchise_id, designer_id
            ON DUPLICATE KEY UPDATE
                                 type_name = VALUES(type_name),
                                 type_num = VALUES(type_num)
    </insert>
    <insert id="insertPaychannel">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            5,
            pay_channel,
            if(pay_channel = 1, '淘宝', if(pay_channel= 2,'微信','支付宝')),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            xgwc_order
        where ifnull(pay_channel, 0) > 0
        GROUP BY taobao_id, brand_id, franchise_id, pay_channel
            ON DUPLICATE KEY UPDATE
                                 type_name = VALUES(type_name),
                                 type_num = VALUES(type_num)
    </insert>
    <insert id="insertPaytype">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            6,
            pay_type,
            if(pay_type = 1, '全款', '阶段付'),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            xgwc_order
        where ifnull(pay_type, 0) > 0
        GROUP BY taobao_id, brand_id, franchise_id, pay_type
            ON DUPLICATE KEY UPDATE
                                 type_name = VALUES(type_name),
                                 type_num = VALUES(type_num)
    </insert>
    <insert id="insertChannel">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            o.taobao_id,
            7,
            c.channel_id,
            any_value(c.channel_name),
            count( 1 ) num,
            o.brand_id,
            o.franchise_id
        FROM
            xgwc_order o
        left join xgwc_shop s on o.store_id = s.shop_id
        left join xgwc_channel c on s.channel_id = c.channel_id
        GROUP BY o.taobao_id, o.brand_id, o.franchise_id, c.channel_id
            ON DUPLICATE KEY UPDATE
                                 type_name = VALUES(type_name),
                                 type_num = VALUES(type_num)
    </insert>
    <insert id="insertLineService">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            8,
            state_dic_code,
            any_value ( state_dic_name ),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            incom_line
        where ifnull(state_dic_code, 0) > 0
        GROUP BY taobao_id, brand_id, franchise_id, state_dic_code
            ON DUPLICATE KEY UPDATE
                                 type_name = VALUES(type_name),
                                 type_num = VALUES(type_num)
    </insert>
    <insert id="insertLinevalid">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            9,
            valid_dic_code,
            any_value ( valid_dic_name ),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            incom_line
        GROUP BY taobao_id, brand_id, franchise_id, valid_dic_code
            ON DUPLICATE KEY UPDATE
                                 type_name = VALUES(type_name),
                                 type_num = VALUES(type_num)
    </insert>
    <insert id="insertLineSale">
        insert into incom_customer_statis ( taobao_id,model,type_id,type_name,type_num,brand_id,franchise_id )
        SELECT
            taobao_id,
            10,
            sale_id,
            any_value ( sale_name ),
            count( 1 ) num,
            brand_id,
            franchise_id
        FROM
            incom_line
        where ifnull(sale_id, 0) > 0
        GROUP BY taobao_id, brand_id, franchise_id, sale_id
            ON DUPLICATE KEY UPDATE
                                 type_name = VALUES(type_name),
                                 type_num = VALUES(type_num)
    </insert>
</mapper>