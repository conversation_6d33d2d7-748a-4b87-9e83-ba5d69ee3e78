<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.task.dao.NoticeMapper">
    
    <select id="selectScheduleNoticeIdList" resultType="com.xgwc.task.entity.ScheduleNotice">
        SELECT t.id, t.publish_time FROM notice t where t.`status` = 0 and t.publish_time > now() and send_status = 0
    </select>

    <select id="noticeListLimitFiveMinuter" resultType="com.xgwc.task.entity.ScheduleNotice">
        SELECT t.id, t.publish_time FROM notice t where t.`status` = 0 and t.update_time &gt;= (NOW() - INTERVAL 5 MINUTE) and t.publish_time > now() and send_status = 0
    </select>
</mapper>