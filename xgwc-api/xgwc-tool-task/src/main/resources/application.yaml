spring:
  config:
    import: "nacos:xgwc-tool-task.yml"
  application:
    name: xgwc-tool-task
  cloud:
    nacos:
      server-addr: 192.168.1.189:8848
      username: nacos
      password: 2BEheJdWQ9tzHfYmy9FH
      config:
        namespace: 6fe5caab-df63-4394-8f17-d663a6956179
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
server:
  port: 8084
mybatis:
  type-aliases-package: com.xgwc.task.entity
  mapper-locations: classpath:mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true