management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
spring:
  cloud:
    nacos:
      discovery: # 【配置中心】配置项
        namespace: 6fe5caab-df63-4394-8f17-d663a6956179 # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
  #连接池配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
    username: root
    password: ",uPkdLhlC1,t"
  data:
    redis:
      host: *************
      port: 6379
      database: 0
      password: TqbZ4tpJA153LN5Boax4
    lettuce:
      pool:
        max-idle: 20
        min-idle: 5
        max-active: 50
        adaptiveRefreshTriggersTimeout: 5
        enablePeriodicRefresh: 10
#mybatis配置
mybatis:
  type-aliases-package: com.xgwc.task.entity
  mapper-locations: classpath:mapper/*.xml
logging:
  level:
    com.xgwc.order.dao: debug
feign:
  client:
    config:
      default:
        connectTimeout: 3000 # 连接超时时间（单位：毫秒）
        readTimeout: 3000 # 读取超时时间（单位：毫秒）
key:
  task: 95c00cc3-ae8e-4292-bfbd-f672dfa5df0b
aliyun:
  vod:
    accessKeyId: LTAI5tEDwNYZJo1Y82HAnigd
    accessKeySecret: ******************************
    templateId: 247d1c4c4ce8e9e4ceff52a32684cdf5