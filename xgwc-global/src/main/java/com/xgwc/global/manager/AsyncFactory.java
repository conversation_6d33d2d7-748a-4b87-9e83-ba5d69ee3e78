package com.xgwc.global.manager;

import com.xgwc.common.util.SpringUtils;
import com.xgwc.user.feign.api.OperLogFeign;
import com.xgwc.user.feign.entity.SysOperLog;

import java.util.TimerTask;

/**
 * 异步工厂（产生任务用）
 *
 */
public class AsyncFactory {

    /**
     * 操作日志记录
     *
     * @param operLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordOper(final SysOperLog operLog) {
        return new TimerTask() {
            @Override
            public void run() {
                // 远程查询操作地点
//                operLog.setOperLocation(AddressUtils.getRealAddressByIP(operLog.getOperIp()));
                SpringUtils.getBean(OperLogFeign.class).addLog(operLog);
            }
        };
    }

}
