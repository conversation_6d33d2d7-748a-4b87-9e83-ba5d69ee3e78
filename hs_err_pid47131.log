#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGBUS (0xa) at pc=0x00000001010abd50, pid=47131, tid=42243
#
# JRE version: Java(TM) SE Runtime Environment (17.0.14+8) (build 17.0.14+8-LTS-191)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.14+8-LTS-191, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-aarch64)
# Problematic frame:
# C  [libzip.dylib+0x13d50]  newEntry+0x68
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:56417,suspend=y,server=n -agentpath:/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/UserApplication_2025_08_02_170723.jfr,log=/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/UserApplication_2025_08_02_170723.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2024.3/captureAgent/debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 com.xgwc.user.UserApplication

Host: "Mac15,6" arm64 1 MHz, 11 cores, 18G, Darwin 24.5.0, macOS 15.5 (24F74)
Time: Sat Aug  2 17:07:24 2025 CST elapsed time: 0.808680 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000000149374800):  JavaThread "Thread-0" [_thread_in_native, id=42243, stack(0x000000030ed0c000,0x000000030ef0f000)]

Stack: [0x000000030ed0c000,0x000000030ef0f000],  sp=0x000000030ef0dea0,  free space=2055k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libzip.dylib+0x13d50]  newEntry+0x68
C  [libzip.dylib+0x13c20]  ZIP_GetEntry2+0x14c
C  [libzip.dylib+0x14608]  ZIP_FindEntry+0x3c
V  [libjvm.dylib+0x25b7ec]  ClassPathZipEntry::open_entry(JavaThread*, char const*, int*, bool)+0xb4
V  [libjvm.dylib+0x25b920]  ClassPathZipEntry::open_stream(JavaThread*, char const*)+0x20
V  [libjvm.dylib+0x25eab0]  ClassLoader::load_class(Symbol*, bool, JavaThread*)+0x150
V  [libjvm.dylib+0x8ebfa4]  SystemDictionary::load_instance_class_impl(Symbol*, Handle, JavaThread*)+0x2d0
V  [libjvm.dylib+0x8ea890]  SystemDictionary::load_instance_class(unsigned int, Symbol*, Handle, JavaThread*)+0x30
V  [libjvm.dylib+0x8e9f98]  SystemDictionary::resolve_instance_class_or_null(Symbol*, Handle, Handle, JavaThread*)+0x4f4
V  [libjvm.dylib+0x8e956c]  SystemDictionary::resolve_or_fail(Symbol*, Handle, Handle, bool, JavaThread*)+0x80
V  [libjvm.dylib+0x2ba7a8]  ConstantPool::klass_at_impl(constantPoolHandle const&, int, JavaThread*)+0x1e0
V  [libjvm.dylib+0x463d98]  InterpreterRuntime::_new(JavaThread*, ConstantPool*, int)+0x94
j  com.intellij.rt.debugger.agent.CaptureStorage.getAsyncStackTrace(Ljava/lang/Throwable;)[Ljava/lang/StackTraceElement;+11
j  java.lang.Throwable.printStackTrace(Ljava/lang/Throwable$PrintStreamOrWriter;)V+32 java.base@17.0.14
j  java.lang.Throwable.printStackTrace(Ljava/io/PrintStream;)V+9 java.base@17.0.14
j  java.lang.ThreadGroup.uncaughtException(Ljava/lang/Thread;Ljava/lang/Throwable;)V+82 java.base@17.0.14
j  java.lang.ThreadGroup.uncaughtException(Ljava/lang/Thread;Ljava/lang/Throwable;)V+13 java.base@17.0.14
j  java.lang.Thread.dispatchUncaughtException(Ljava/lang/Throwable;)V+6 java.base@17.0.14
v  ~StubRoutines::call_stub
V  [libjvm.dylib+0x46deac]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x38c
V  [libjvm.dylib+0x46cec8]  JavaCalls::call_virtual(JavaValue*, Klass*, Symbol*, Symbol*, JavaCallArguments*, JavaThread*)+0x11c
V  [libjvm.dylib+0x46d024]  JavaCalls::call_virtual(JavaValue*, Handle, Klass*, Symbol*, Symbol*, Handle, JavaThread*)+0x68
V  [libjvm.dylib+0x91c2f0]  JavaThread::exit(bool, JavaThread::ExitType)+0x1b0
V  [libjvm.dylib+0x91c088]  JavaThread::post_run()+0x20
V  [libjvm.dylib+0x91a874]  Thread::call_run()+0x124
V  [libjvm.dylib+0x7ba4b4]  thread_native_entry(Thread*)+0x158
C  [libsystem_pthread.dylib+0x6c0c]  _pthread_start+0x88

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  com.intellij.rt.debugger.agent.CaptureStorage.getAsyncStackTrace(Ljava/lang/Throwable;)[Ljava/lang/StackTraceElement;+11
j  java.lang.Throwable.printStackTrace(Ljava/lang/Throwable$PrintStreamOrWriter;)V+32 java.base@17.0.14
j  java.lang.Throwable.printStackTrace(Ljava/io/PrintStream;)V+9 java.base@17.0.14
j  java.lang.ThreadGroup.uncaughtException(Ljava/lang/Thread;Ljava/lang/Throwable;)V+82 java.base@17.0.14
j  java.lang.ThreadGroup.uncaughtException(Ljava/lang/Thread;Ljava/lang/Throwable;)V+13 java.base@17.0.14
j  java.lang.Thread.dispatchUncaughtException(Ljava/lang/Throwable;)V+6 java.base@17.0.14
v  ~StubRoutines::call_stub

siginfo: si_signo: 10 (SIGBUS), si_code: 1 (BUS_ADRALN), si_addr: 0x0000000101077637

Registers:
 x0=0x000060000a50e0d0  x1=0x0000000000000000  x2=0xffffffffffffffe0  x3=0x000060000a50e0e0
 x4=0x000060000a50e140  x5=0x00000000b1e3106a  x6=0x00000000000020d0  x7=0x0000000000000002
 x8=0x000000010118361b  x9=0x000000000010c000 x10=0x000060000a50e0d0 x11=0x0000ad4277ef0000
x12=0x0000000000000050 x13=0x0000600008760394 x14=0x00000000001ff800 x15=0x00000000000007fb
x16=0x000000018d0d3f20 x17=0x00000001fc1fc0e0 x18=0x0000000000000000 x19=0x000060000a50e0d0
x20=0x0000000000000000 x21=0x0000600003f006c0 x22=0x000000010107761b x23=0x0000000024275288
x24=0x000000000000002f x25=0x0000000000000035 x26=0x00000000000000a7 x27=0x000060000a50e0f8
x28=0x0000000146b4d050  fp=0x000000030ef0df20  lr=0x00000001010abd1c  sp=0x000000030ef0dea0
pc=0x00000001010abd50 cpsr=0x0000000080001000

Register to memory mapping:

 x0=0x000060000a50e0d0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
 x1=0x0 is NULL
 x2=0xffffffffffffffe0 is an unknown value
 x3=0x000060000a50e0e0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
 x4=0x000060000a50e140 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
 x5=0x00000000b1e3106a is an unknown value
 x6=0x00000000000020d0 is an unknown value
 x7=0x0000000000000002 is an unknown value
 x8=0x000000010118361b: tinfl_decompress+0xf8b in /private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib at 0x0000000101154000
 x9=0x000000000010c000 is an unknown value
x10=0x000060000a50e0d0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x11=0x0000ad4277ef0000 is an unknown value
x12=0x0000000000000050 is an unknown value
x13=0x0000600008760394 points into unknown readable memory: fb 0f 03 b2
x14=0x00000000001ff800 is an unknown value
x15=0x00000000000007fb is an unknown value
x16=0x000000018d0d3f20: __bzero+0 in /usr/lib/system/libsystem_platform.dylib at 0x000000018d0d1000
x17=0x00000001fc1fc0e0 points into unknown readable memory: 0x000000018d0d3f20 | 20 3f 0d 8d 01 00 00 00
x18=0x0 is NULL
x19=0x000060000a50e0d0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x20=0x0 is NULL
x21=0x0000600003f006c0 points into unknown readable memory: 0x000060000a0ec060 | 60 c0 0e 0a 00 60 00 00
x22=0x000000010107761b points into unknown readable memory: 50 4b 01 02 00
x23=0x0000000024275288 is an unknown value
x24=0x000000000000002f is an unknown value
x25=0x0000000000000035 is an unknown value
x26=0x00000000000000a7 is an unknown value
x27=0x000060000a50e0f8 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x28=0x0000000146b4d050 points into unknown readable memory: 0x65746e692f6d6f63 | 63 6f 6d 2f 69 6e 74 65


Top of Stack: (sp=0x000000030ef0dea0)
0x000000030ef0dea0:   0000000000000000 0000000000000000
0x000000030ef0deb0:   0000000000000000 0000000000000000
0x000000030ef0dec0:   0000000000000000 0000000000000000
0x000000030ef0ded0:   0000000146b4d050 000000014900e000
0x000000030ef0dee0:   00000000000000a7 0000000000000035
0x000000030ef0def0:   000000000000002f 0000000024275288
0x000000030ef0df00:   000060000a50e030 0000000000000000
0x000000030ef0df10:   0000000146b4d080 0000600003f006c0
0x000000030ef0df20:   000000030ef0df80 00000001010abc20
0x000000030ef0df30:   0000000146b4d050 000000000000002f
0x000000030ef0df40:   0000000000000001 0000000146b4d080
0x000000030ef0df50:   0000000149374b40 0000000146b4d080
0x000000030ef0df60:   0000600003f006c0 0000000146b4d080
0x000000030ef0df70:   000000030ef0e0ac 000000030ef0dfc4
0x000000030ef0df80:   000000030ef0dfb0 00000001010ac608
0x000000030ef0df90:   000000030ef0e0ac 0000600005621d70
0x000000030ef0dfa0:   0000000000000000 0000000149374800
0x000000030ef0dfb0:   000000030ef0e090 00000001023df7ec
0x000000030ef0dfc0:   0000000102ba0a0d 0000000000000100
0x000000030ef0dfd0:   000000030ef0dff0 00000001026a4254
0x000000030ef0dfe0:   0000000102ba0a0d 000000030ef0e070
0x000000030ef0dff0:   000000030ef0e040 00000001023e41dc
0x000000030ef0e000:   0000000000000000 0000000000000000
0x000000030ef0e010:   0000000000000001 00000001191822a8
0x000000030ef0e020:   0000000149374800 0000000146b4d428
0x000000030ef0e030:   0000000102bb5abd 000000030ef0e138
0x000000030ef0e040:   000000030ef0e060 cf526f9202480090
0x000000030ef0e050:   0000000000000001 0000000146b4d080
0x000000030ef0e060:   0000600005621d70 00000001191822a8
0x000000030ef0e070:   0000000149374800 0000000146b4d428
0x000000030ef0e080:   0000000146b4d040 0000600005621d70
0x000000030ef0e090:   000000030ef0e0c0 00000001023df920 

Instructions: (pc=0x00000001010abd50)
0x00000001010abc50:   6b0c017f 54ffff60 17ffffde d2800016
0x00000001010abc60:   72001ebf 54000160 b5000156 f100073f
0x00000001010abc70:   54fff7cb 8b140328 385ff108 7100bd1f
0x00000001010abc80:   54fff741 d2800016 14000002 f9004e7f
0x00000001010abc90:   f9402a60 94000464 aa1603e0 a9457bfd
0x00000001010abca0:   a9444ff4 a94357f6 a9425ff8 a94167fa
0x00000001010abcb0:   a8c66ffc d65f03c0 6b03003f 540000e1
0x00000001010abcc0:   71000421 540000eb 38401408 38401449
0x00000001010abcd0:   6b09011f 54ffff60 52800000 d65f03c0
0x00000001010abce0:   52800020 d65f03c0 d10243ff a9036ffc
0x00000001010abcf0:   a90467fa a9055ff8 a90657f6 a9074ff4
0x00000001010abd00:   a9087bfd 910203fd aa0203f4 aa0103f6
0x00000001010abd10:   aa0003f5 52800900 9400046a aa0003f3
0x00000001010abd20:   b4001320 f900027f aa1303fb f8028f7f
0x00000001010abd30:   f9001a7f 3940c2a8 34000288 f9400ea8
0x00000001010abd40:   f94006c9 8b090108 f94016a9 cb090116
0x00000001010abd50:   79403ad8 39407ada 39407edc 794042c8
0x00000001010abd60:   f90017e8 b9400ec8 f9000668 b9401ac8
0x00000001010abd70:   f9000fe8 f9000a68 794016c8 34000488
0x00000001010abd80:   b94016c8 14000023 f94006d7 34000d54
0x00000001010abd90:   f9401ea8 b4000288 f94022a9 eb17013f
0x00000001010abda0:   5400022c 5283fa4a 8b0a012a eb17015f
0x00000001010abdb0:   540001ab 9140092a 8b170108 cb090116
0x00000001010abdc0:   79403ac8 79403ec9 794042cb 8b0802e8
0x00000001010abdd0:   8b090108 8b0b0108 9100b908 eb0a011f
0x00000001010abde0:   54000b4d aa1503e0 aa1703e1 52840002
0x00000001010abdf0:   94000384 aa0003f6 b4000aa0 f9401ea0
0x00000001010abe00:   94000421 a903deb6 17ffffd2 d2800008
0x00000001010abe10:   aa0803f7 f9000e68 b94012c8 b9002268
0x00000001010abe20:   b842a2c9 f9405ea8 f9000be9 8b090108
0x00000001010abe30:   cb0803e8 f9001e68 794012c8 b9004268
0x00000001010abe40:   91000700 9400041f aa0003f9 f9000260 


Stack slot to memory mapping:
stack at sp + 0 slots: 0x0 is NULL
stack at sp + 1 slots: 0x0 is NULL
stack at sp + 2 slots: 0x0 is NULL
stack at sp + 3 slots: 0x0 is NULL
stack at sp + 4 slots: 0x0 is NULL
stack at sp + 5 slots: 0x0 is NULL
stack at sp + 6 slots: 0x0000000146b4d050 points into unknown readable memory: 0x65746e692f6d6f63 | 63 6f 6d 2f 69 6e 74 65
stack at sp + 7 slots: 0x000000014900e000 points into unknown readable memory: 0xffffffff5bbd78a2 | a2 78 bd 5b ff ff ff ff


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000600004522f20, length=15, elements={
0x000000014900d400, 0x0000000147207e00, 0x000000010b808200, 0x000000014886e200,
0x000000014886c400, 0x000000014886ca00, 0x0000000147208a00, 0x0000000147209000,
0x000000014910cc00, 0x000000014886d000, 0x000000014886d600, 0x000000014910f200,
0x0000000148a01c00, 0x0000000146615c00, 0x0000000149374800
}

Java Threads: ( => current thread )
  0x000000014900d400 JavaThread "main" [_thread_in_native, id=5379, stack(0x000000016efd4000,0x000000016f1d7000)]
  0x0000000147207e00 JavaThread "Reference Handler" daemon [_thread_blocked, id=19203, stack(0x000000030c004000,0x000000030c207000)]
  0x000000010b808200 JavaThread "Finalizer" daemon [_thread_blocked, id=18691, stack(0x000000030c210000,0x000000030c413000)]
  0x000000014886e200 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=31747, stack(0x000000030c41c000,0x000000030c61f000)]
  0x000000014886c400 JavaThread "Service Thread" daemon [_thread_blocked, id=31235, stack(0x000000030c628000,0x000000030c82b000)]
  0x000000014886ca00 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=23555, stack(0x000000030c834000,0x000000030ca37000)]
  0x0000000147208a00 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=23811, stack(0x000000030ca40000,0x000000030cc43000)]
  0x0000000147209000 JavaThread "Sweeper thread" daemon [_thread_blocked, id=30211, stack(0x000000030cc4c000,0x000000030ce4f000)]
  0x000000014910cc00 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=24067, stack(0x000000030ce58000,0x000000030d05b000)]
  0x000000014886d000 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=29699, stack(0x000000030d064000,0x000000030d267000)]
  0x000000014886d600 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=24835, stack(0x000000030d270000,0x000000030d473000)]
  0x000000014910f200 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=29187, stack(0x000000030d47c000,0x000000030d67f000)]
  0x0000000148a01c00 JavaThread "Notification Thread" daemon [_thread_blocked, id=26115, stack(0x000000030e2d0000,0x000000030e4d3000)]
  0x0000000146615c00 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=33283, stack(0x000000030e8f4000,0x000000030eaf7000)]
=>0x0000000149374800 JavaThread "Thread-0" [_thread_in_native, id=42243, stack(0x000000030ed0c000,0x000000030ef0f000)]

Other Threads:
  0x000000010a997e00 VMThread "VM Thread" [stack: 0x000000016fc1c000,0x000000016fe1f000] [id=17923]
  0x0000000101dd8c10 WatcherThread [stack: 0x000000030eb00000,0x000000030ed03000] [id=42755]
  0x0000000135e3bb50 GCTaskThread "GC Thread#0" [stack: 0x000000016f1e0000,0x000000016f3e3000] [id=12291]
  0x0000000146a0fa00 GCTaskThread "GC Thread#1" [stack: 0x000000030d688000,0x000000030d88b000] [id=28931]
  0x0000000146a100b0 GCTaskThread "GC Thread#2" [stack: 0x000000030d894000,0x000000030da97000] [id=28419]
  0x0000000146a10530 GCTaskThread "GC Thread#3" [stack: 0x000000030daa0000,0x000000030dca3000] [id=28163]
  0x0000000146a10da0 GCTaskThread "GC Thread#4" [stack: 0x000000030dcac000,0x000000030deaf000] [id=27907]
  0x0000000146a11610 GCTaskThread "GC Thread#5" [stack: 0x000000030deb8000,0x000000030e0bb000] [id=27395]
  0x0000000146a11e80 GCTaskThread "GC Thread#6" [stack: 0x000000030e0c4000,0x000000030e2c7000] [id=27139]
  0x0000000146a16f90 GCTaskThread "GC Thread#7" [stack: 0x000000030e4dc000,0x000000030e6df000] [id=26371]
  0x0000000146a177e0 GCTaskThread "GC Thread#8" [stack: 0x000000030e6e8000,0x000000030e8eb000] [id=43267]
  0x0000000135e3c200 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000016f3ec000,0x000000016f5ef000] [id=13827]
  0x0000000135e3ca90 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000016f5f8000,0x000000016f7fb000] [id=12803]
  0x0000000135e3ebc0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000016f804000,0x000000016fa07000] [id=16643]
  0x0000000101e051e0 ConcurrentGCThread "G1 Service" [stack: 0x000000016fa10000,0x000000016fc13000] [id=16899]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000006e0000000, size: 4608 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000007000000000-0x0000007000be8000-0x0000007000be8000), size 12484608, SharedBaseAddress: 0x0000007000000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000007001000000-0x0000007041000000, reserved size: 1073741824
Narrow klass base: 0x0000007000000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 11 total, 11 available
 Memory: 18432M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 288M
 Heap Max Capacity: 4608M
 Pre-touch: Disabled
 Parallel Workers: 9
 Concurrent Workers: 2
 Concurrent Refinement Workers: 9
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 303104K, used 51538K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 1 survivors (4096K)
 Metaspace       used 17659K, committed 17984K, reserved 1114112K
  class space    used 2217K, committed 2368K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000006e0000000, 0x00000006e0400000, 0x00000006e0400000|100%| O|  |TAMS 0x00000006e0000000, 0x00000006e0000000| Untracked 
|   1|0x00000006e0400000, 0x00000006e0800000, 0x00000006e0800000|100%| O|  |TAMS 0x00000006e0400000, 0x00000006e0400000| Untracked 
|   2|0x00000006e0800000, 0x00000006e0b08600, 0x00000006e0c00000| 75%| O|  |TAMS 0x00000006e0800000, 0x00000006e0800000| Untracked 
|   3|0x00000006e0c00000, 0x00000006e0c00000, 0x00000006e1000000|  0%| F|  |TAMS 0x00000006e0c00000, 0x00000006e0c00000| Untracked 
|   4|0x00000006e1000000, 0x00000006e1000000, 0x00000006e1400000|  0%| F|  |TAMS 0x00000006e1000000, 0x00000006e1000000| Untracked 
|   5|0x00000006e1400000, 0x00000006e1400000, 0x00000006e1800000|  0%| F|  |TAMS 0x00000006e1400000, 0x00000006e1400000| Untracked 
|   6|0x00000006e1800000, 0x00000006e1800000, 0x00000006e1c00000|  0%| F|  |TAMS 0x00000006e1800000, 0x00000006e1800000| Untracked 
|   7|0x00000006e1c00000, 0x00000006e1c00000, 0x00000006e2000000|  0%| F|  |TAMS 0x00000006e1c00000, 0x00000006e1c00000| Untracked 
|   8|0x00000006e2000000, 0x00000006e2000000, 0x00000006e2400000|  0%| F|  |TAMS 0x00000006e2000000, 0x00000006e2000000| Untracked 
|   9|0x00000006e2400000, 0x00000006e2400000, 0x00000006e2800000|  0%| F|  |TAMS 0x00000006e2400000, 0x00000006e2400000| Untracked 
|  10|0x00000006e2800000, 0x00000006e2800000, 0x00000006e2c00000|  0%| F|  |TAMS 0x00000006e2800000, 0x00000006e2800000| Untracked 
|  11|0x00000006e2c00000, 0x00000006e2c00000, 0x00000006e3000000|  0%| F|  |TAMS 0x00000006e2c00000, 0x00000006e2c00000| Untracked 
|  12|0x00000006e3000000, 0x00000006e3000000, 0x00000006e3400000|  0%| F|  |TAMS 0x00000006e3000000, 0x00000006e3000000| Untracked 
|  13|0x00000006e3400000, 0x00000006e3400000, 0x00000006e3800000|  0%| F|  |TAMS 0x00000006e3400000, 0x00000006e3400000| Untracked 
|  14|0x00000006e3800000, 0x00000006e3800000, 0x00000006e3c00000|  0%| F|  |TAMS 0x00000006e3800000, 0x00000006e3800000| Untracked 
|  15|0x00000006e3c00000, 0x00000006e3c00000, 0x00000006e4000000|  0%| F|  |TAMS 0x00000006e3c00000, 0x00000006e3c00000| Untracked 
|  16|0x00000006e4000000, 0x00000006e4000000, 0x00000006e4400000|  0%| F|  |TAMS 0x00000006e4000000, 0x00000006e4000000| Untracked 
|  17|0x00000006e4400000, 0x00000006e4400000, 0x00000006e4800000|  0%| F|  |TAMS 0x00000006e4400000, 0x00000006e4400000| Untracked 
|  18|0x00000006e4800000, 0x00000006e4800000, 0x00000006e4c00000|  0%| F|  |TAMS 0x00000006e4800000, 0x00000006e4800000| Untracked 
|  19|0x00000006e4c00000, 0x00000006e4c00000, 0x00000006e5000000|  0%| F|  |TAMS 0x00000006e4c00000, 0x00000006e4c00000| Untracked 
|  20|0x00000006e5000000, 0x00000006e5000000, 0x00000006e5400000|  0%| F|  |TAMS 0x00000006e5000000, 0x00000006e5000000| Untracked 
|  21|0x00000006e5400000, 0x00000006e5400000, 0x00000006e5800000|  0%| F|  |TAMS 0x00000006e5400000, 0x00000006e5400000| Untracked 
|  22|0x00000006e5800000, 0x00000006e5800000, 0x00000006e5c00000|  0%| F|  |TAMS 0x00000006e5800000, 0x00000006e5800000| Untracked 
|  23|0x00000006e5c00000, 0x00000006e5c00000, 0x00000006e6000000|  0%| F|  |TAMS 0x00000006e5c00000, 0x00000006e5c00000| Untracked 
|  24|0x00000006e6000000, 0x00000006e6000000, 0x00000006e6400000|  0%| F|  |TAMS 0x00000006e6000000, 0x00000006e6000000| Untracked 
|  25|0x00000006e6400000, 0x00000006e6400000, 0x00000006e6800000|  0%| F|  |TAMS 0x00000006e6400000, 0x00000006e6400000| Untracked 
|  26|0x00000006e6800000, 0x00000006e6800000, 0x00000006e6c00000|  0%| F|  |TAMS 0x00000006e6800000, 0x00000006e6800000| Untracked 
|  27|0x00000006e6c00000, 0x00000006e6c00000, 0x00000006e7000000|  0%| F|  |TAMS 0x00000006e6c00000, 0x00000006e6c00000| Untracked 
|  28|0x00000006e7000000, 0x00000006e7000000, 0x00000006e7400000|  0%| F|  |TAMS 0x00000006e7000000, 0x00000006e7000000| Untracked 
|  29|0x00000006e7400000, 0x00000006e7400000, 0x00000006e7800000|  0%| F|  |TAMS 0x00000006e7400000, 0x00000006e7400000| Untracked 
|  30|0x00000006e7800000, 0x00000006e7800000, 0x00000006e7c00000|  0%| F|  |TAMS 0x00000006e7800000, 0x00000006e7800000| Untracked 
|  31|0x00000006e7c00000, 0x00000006e7c00000, 0x00000006e8000000|  0%| F|  |TAMS 0x00000006e7c00000, 0x00000006e7c00000| Untracked 
|  32|0x00000006e8000000, 0x00000006e8000000, 0x00000006e8400000|  0%| F|  |TAMS 0x00000006e8000000, 0x00000006e8000000| Untracked 
|  33|0x00000006e8400000, 0x00000006e8400000, 0x00000006e8800000|  0%| F|  |TAMS 0x00000006e8400000, 0x00000006e8400000| Untracked 
|  34|0x00000006e8800000, 0x00000006e8800000, 0x00000006e8c00000|  0%| F|  |TAMS 0x00000006e8800000, 0x00000006e8800000| Untracked 
|  35|0x00000006e8c00000, 0x00000006e8c00000, 0x00000006e9000000|  0%| F|  |TAMS 0x00000006e8c00000, 0x00000006e8c00000| Untracked 
|  36|0x00000006e9000000, 0x00000006e9000000, 0x00000006e9400000|  0%| F|  |TAMS 0x00000006e9000000, 0x00000006e9000000| Untracked 
|  37|0x00000006e9400000, 0x00000006e9400000, 0x00000006e9800000|  0%| F|  |TAMS 0x00000006e9400000, 0x00000006e9400000| Untracked 
|  38|0x00000006e9800000, 0x00000006e9800000, 0x00000006e9c00000|  0%| F|  |TAMS 0x00000006e9800000, 0x00000006e9800000| Untracked 
|  39|0x00000006e9c00000, 0x00000006e9c00000, 0x00000006ea000000|  0%| F|  |TAMS 0x00000006e9c00000, 0x00000006e9c00000| Untracked 
|  40|0x00000006ea000000, 0x00000006ea000000, 0x00000006ea400000|  0%| F|  |TAMS 0x00000006ea000000, 0x00000006ea000000| Untracked 
|  41|0x00000006ea400000, 0x00000006ea400000, 0x00000006ea800000|  0%| F|  |TAMS 0x00000006ea400000, 0x00000006ea400000| Untracked 
|  42|0x00000006ea800000, 0x00000006ea800000, 0x00000006eac00000|  0%| F|  |TAMS 0x00000006ea800000, 0x00000006ea800000| Untracked 
|  43|0x00000006eac00000, 0x00000006eac00000, 0x00000006eb000000|  0%| F|  |TAMS 0x00000006eac00000, 0x00000006eac00000| Untracked 
|  44|0x00000006eb000000, 0x00000006eb000000, 0x00000006eb400000|  0%| F|  |TAMS 0x00000006eb000000, 0x00000006eb000000| Untracked 
|  45|0x00000006eb400000, 0x00000006eb400000, 0x00000006eb800000|  0%| F|  |TAMS 0x00000006eb400000, 0x00000006eb400000| Untracked 
|  46|0x00000006eb800000, 0x00000006eb800000, 0x00000006ebc00000|  0%| F|  |TAMS 0x00000006eb800000, 0x00000006eb800000| Untracked 
|  47|0x00000006ebc00000, 0x00000006ebc00000, 0x00000006ec000000|  0%| F|  |TAMS 0x00000006ebc00000, 0x00000006ebc00000| Untracked 
|  48|0x00000006ec000000, 0x00000006ec000000, 0x00000006ec400000|  0%| F|  |TAMS 0x00000006ec000000, 0x00000006ec000000| Untracked 
|  49|0x00000006ec400000, 0x00000006ec400000, 0x00000006ec800000|  0%| F|  |TAMS 0x00000006ec400000, 0x00000006ec400000| Untracked 
|  50|0x00000006ec800000, 0x00000006ec800000, 0x00000006ecc00000|  0%| F|  |TAMS 0x00000006ec800000, 0x00000006ec800000| Untracked 
|  51|0x00000006ecc00000, 0x00000006ecc00000, 0x00000006ed000000|  0%| F|  |TAMS 0x00000006ecc00000, 0x00000006ecc00000| Untracked 
|  52|0x00000006ed000000, 0x00000006ed000000, 0x00000006ed400000|  0%| F|  |TAMS 0x00000006ed000000, 0x00000006ed000000| Untracked 
|  53|0x00000006ed400000, 0x00000006ed400000, 0x00000006ed800000|  0%| F|  |TAMS 0x00000006ed400000, 0x00000006ed400000| Untracked 
|  54|0x00000006ed800000, 0x00000006ed800000, 0x00000006edc00000|  0%| F|  |TAMS 0x00000006ed800000, 0x00000006ed800000| Untracked 
|  55|0x00000006edc00000, 0x00000006edc00000, 0x00000006ee000000|  0%| F|  |TAMS 0x00000006edc00000, 0x00000006edc00000| Untracked 
|  56|0x00000006ee000000, 0x00000006ee000000, 0x00000006ee400000|  0%| F|  |TAMS 0x00000006ee000000, 0x00000006ee000000| Untracked 
|  57|0x00000006ee400000, 0x00000006ee400000, 0x00000006ee800000|  0%| F|  |TAMS 0x00000006ee400000, 0x00000006ee400000| Untracked 
|  58|0x00000006ee800000, 0x00000006ee800000, 0x00000006eec00000|  0%| F|  |TAMS 0x00000006ee800000, 0x00000006ee800000| Untracked 
|  59|0x00000006eec00000, 0x00000006eec00000, 0x00000006ef000000|  0%| F|  |TAMS 0x00000006eec00000, 0x00000006eec00000| Untracked 
|  60|0x00000006ef000000, 0x00000006ef354210, 0x00000006ef400000| 83%| S|CS|TAMS 0x00000006ef000000, 0x00000006ef000000| Complete 
|  61|0x00000006ef400000, 0x00000006ef400000, 0x00000006ef800000|  0%| F|  |TAMS 0x00000006ef400000, 0x00000006ef400000| Untracked 
|  62|0x00000006ef800000, 0x00000006ef800000, 0x00000006efc00000|  0%| F|  |TAMS 0x00000006ef800000, 0x00000006ef800000| Untracked 
|  63|0x00000006efc00000, 0x00000006eff1fbe0, 0x00000006f0000000| 78%| E|  |TAMS 0x00000006efc00000, 0x00000006efc00000| Complete 
|  64|0x00000006f0000000, 0x00000006f0400000, 0x00000006f0400000|100%| E|CS|TAMS 0x00000006f0000000, 0x00000006f0000000| Complete 
|  65|0x00000006f0400000, 0x00000006f0800000, 0x00000006f0800000|100%| E|CS|TAMS 0x00000006f0400000, 0x00000006f0400000| Complete 
|  66|0x00000006f0800000, 0x00000006f0c00000, 0x00000006f0c00000|100%| E|CS|TAMS 0x00000006f0800000, 0x00000006f0800000| Complete 
|  67|0x00000006f0c00000, 0x00000006f1000000, 0x00000006f1000000|100%| E|CS|TAMS 0x00000006f0c00000, 0x00000006f0c00000| Complete 
|  68|0x00000006f1000000, 0x00000006f1400000, 0x00000006f1400000|100%| E|CS|TAMS 0x00000006f1000000, 0x00000006f1000000| Complete 
|  69|0x00000006f1400000, 0x00000006f1800000, 0x00000006f1800000|100%| E|CS|TAMS 0x00000006f1400000, 0x00000006f1400000| Complete 
|  70|0x00000006f1800000, 0x00000006f1c00000, 0x00000006f1c00000|100%| E|CS|TAMS 0x00000006f1800000, 0x00000006f1800000| Complete 
|  71|0x00000006f1c00000, 0x00000006f2000000, 0x00000006f2000000|100%| E|CS|TAMS 0x00000006f1c00000, 0x00000006f1c00000| Complete 
|1150|0x00000007ff800000, 0x00000007ffb78000, 0x00000007ffc00000| 86%|OA|  |TAMS 0x00000007ff800000, 0x00000007ff800000| Untracked 
|1151|0x00000007ffc00000, 0x00000007ffc80000, 0x0000000800000000| 12%|CA|  |TAMS 0x00000007ffc00000, 0x00000007ffc00000| Untracked 

Card table byte_map: [0x0000000130900000,0x0000000131200000] _byte_map_base: 0x000000012d200000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000000149096c10, (CMBitMap*) 0x0000000149096c50
 Prev Bits: [0x0000000140000000, 0x0000000144800000)
 Next Bits: [0x0000000149800000, 0x000000014e000000)

Polling page: 0x0000000100f44000

Metaspace:

Usage:
  Non-class:     15.08 MB used.
      Class:      2.17 MB used.
       Both:     17.25 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      15.25 MB ( 24%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.31 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      17.56 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  496.00 KB
       Class:  13.67 MB
        Both:  14.16 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 216.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 281.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 649.
num_chunk_merges: 0.
num_chunk_splits: 454.
num_chunks_enlarged: 353.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=4909Kb max_used=4909Kb free=44242Kb
 bounds [0x000000010c800000, 0x000000010ccd0000, 0x000000010f800000]
 total_blobs=2552 nmethods=2066 adapters=417
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.792 Thread 0x0000000147208a00 nmethod 2103 0x000000010ccc3910 code [0x000000010ccc3a80, 0x000000010ccc3b18]
Event: 0.800 Thread 0x0000000147208a00 2110       1       java.util.logging.LogManager::getProperty (9 bytes)
Event: 0.801 Thread 0x0000000147208a00 nmethod 2110 0x000000010ccc6310 code [0x000000010ccc64c0, 0x000000010ccc65f8]
Event: 0.805 Thread 0x0000000147208a00 2112       1       jdk.internal.math.FDBigInteger::mult (64 bytes)
Event: 0.805 Thread 0x0000000147208a00 nmethod 2112 0x000000010ccc6d10 code [0x000000010ccc6ec0, 0x000000010ccc7058]
Event: 0.805 Thread 0x0000000147208a00 2113       1       jdk.internal.math.FDBigInteger::<init> (30 bytes)
Event: 0.805 Thread 0x0000000147208a00 nmethod 2113 0x000000010ccc7290 code [0x000000010ccc7440, 0x000000010ccc75b8]
Event: 0.805 Thread 0x0000000147208a00 2114       1       jdk.internal.math.FDBigInteger::trimLeadingZeros (57 bytes)
Event: 0.805 Thread 0x0000000147208a00 nmethod 2114 0x000000010ccc7710 code [0x000000010ccc7880, 0x000000010ccc79d8]
Event: 0.805 Thread 0x0000000147208a00 2115       1       jdk.internal.math.FDBigInteger::makeImmutable (6 bytes)
Event: 0.805 Thread 0x0000000147208a00 nmethod 2115 0x000000010ccc7b90 code [0x000000010ccc7d00, 0x000000010ccc7dd8]
Event: 0.805 Thread 0x0000000147208a00 2116       1       jdk.internal.math.FDBigInteger::mult (44 bytes)
Event: 0.805 Thread 0x0000000147208a00 nmethod 2116 0x000000010ccc7e90 code [0x000000010ccc8040, 0x000000010ccc8318]
Event: 0.806 Thread 0x0000000147208a00 2117       1       org.jetbrains.capture.org.objectweb.asm.SymbolTable::addConstantUtf8 (20 bytes)
Event: 0.806 Thread 0x0000000147208a00 nmethod 2117 0x000000010ccc8b90 code [0x000000010ccc8d40, 0x000000010ccc8f38]
Event: 0.806 Thread 0x0000000147208a00 2120       1       org.jetbrains.capture.org.objectweb.asm.SymbolTable$Entry::<init> (17 bytes)
Event: 0.807 Thread 0x0000000147208a00 nmethod 2120 0x000000010ccc9190 code [0x000000010ccc9340, 0x000000010ccc9478]
Event: 0.807 Thread 0x0000000147208a00 2118       1       org.jetbrains.capture.org.objectweb.asm.ClassReader::readClass (7 bytes)
Event: 0.807 Thread 0x0000000147208a00 nmethod 2118 0x000000010ccc9b90 code [0x000000010ccc9d40, 0x000000010ccc9fb8]
Event: 0.807 Thread 0x0000000147208a00 2121       1       java.util.ArrayList$Itr::next (66 bytes)

GC Heap History (6 events):
Event: 0.343 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 303104K, used 20448K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 0 survivors (0K)
 Metaspace       used 1280K, committed 1408K, reserved 1114112K
  class space    used 132K, committed 192K, reserved 1048576K
}
Event: 0.345 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 303104K, used 15833K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 1280K, committed 1408K, reserved 1114112K
  class space    used 132K, committed 192K, reserved 1048576K
}
Event: 0.453 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 303104K, used 24025K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 1 survivors (4096K)
 Metaspace       used 5980K, committed 6208K, reserved 1114112K
  class space    used 702K, committed 832K, reserved 1048576K
}
Event: 0.455 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 303104K, used 16853K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 5980K, committed 6208K, reserved 1114112K
  class space    used 702K, committed 832K, reserved 1048576K
}
Event: 0.688 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 303104K, used 57813K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 12354K, committed 12608K, reserved 1114112K
  class space    used 1606K, committed 1728K, reserved 1048576K
}
Event: 0.691 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 303104K, used 18770K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 12354K, committed 12608K, reserved 1114112K
  class space    used 1606K, committed 1728K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 0.650 Thread 0x000000014900d400 DEOPT PACKING pc=0x000000010cb0a0a8 sp=0x000000016f1d2ef0
Event: 0.650 Thread 0x000000014900d400 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000016f1d2b90 mode 1
Event: 0.650 Thread 0x000000014900d400 DEOPT PACKING pc=0x000000010cb09bbc sp=0x000000016f1d2fc0
Event: 0.650 Thread 0x000000014900d400 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000016f1d2c90 mode 1
Event: 0.650 Thread 0x000000014900d400 DEOPT PACKING pc=0x000000010cac68c8 sp=0x000000016f1d3760
Event: 0.650 Thread 0x000000014900d400 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000016f1d3450 mode 1
Event: 0.652 Thread 0x000000014900d400 DEOPT PACKING pc=0x000000010cb0a0a8 sp=0x000000016f1d3df0
Event: 0.652 Thread 0x000000014900d400 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000016f1d3a90 mode 1
Event: 0.652 Thread 0x000000014900d400 DEOPT PACKING pc=0x000000010cb09bbc sp=0x000000016f1d3ec0
Event: 0.652 Thread 0x000000014900d400 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000016f1d3b90 mode 1
Event: 0.699 Thread 0x000000014900d400 DEOPT PACKING pc=0x000000010cb50920 sp=0x000000016f1d36f0
Event: 0.699 Thread 0x000000014900d400 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000016f1d3470 mode 3
Event: 0.712 Thread 0x000000014900d400 DEOPT PACKING pc=0x000000010ca0a188 sp=0x000000016f1d3440
Event: 0.712 Thread 0x000000014900d400 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000016f1d31c0 mode 3
Event: 0.745 Thread 0x0000000149374800 DEOPT PACKING pc=0x000000010c981620 sp=0x000000030ef0c800
Event: 0.745 Thread 0x0000000149374800 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000030ef0c4b0 mode 3
Event: 0.806 Thread 0x0000000149374800 DEOPT PACKING pc=0x000000010cae2768 sp=0x000000030ef0c630
Event: 0.806 Thread 0x0000000149374800 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000030ef0c3b0 mode 3
Event: 0.807 Thread 0x0000000149374800 DEOPT PACKING pc=0x000000010cb0146c sp=0x000000030ef0e670
Event: 0.807 Thread 0x0000000149374800 DEOPT UNPACKING pc=0x000000010c83eb7c sp=0x000000030ef0e3e0 mode 1

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 0.382 Thread 0x000000010a997e00 redefined class name=java.lang.Throwable, count=1

Internal exceptions (20 events):
Event: 0.581 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f0724fe0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006f0724fe0) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.582 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f0729910}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006f0729910) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.583 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f07518e0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000006f07518e0) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.589 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f00860b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006f00860b0) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.589 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f0090638}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006f0090638) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.590 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f00983c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006f00983c8) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.607 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f02c31d8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006f02c31d8) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.638 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006ef870818}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006ef870818) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.655 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006efad0210}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006efad0210) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.655 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006efad8890}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006efad8890) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.655 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006efae0280}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000006efae0280) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.655 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006efaebcf8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object)'> (0x00000006efaebcf8) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.656 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006efaf0758}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006efaf0758) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.662 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006efbc1338}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006efbc1338) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.668 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006ef45c720}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000006ef45c720) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.668 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006ef4683c8}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006ef4683c8) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.709 Thread 0x000000014900d400 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f193fdf8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000006f193fdf8) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 0.806 Thread 0x0000000149374800 Exception <a 'java/io/FileNotFoundException'{0x00000006f0117de0}> (0x00000006f0117de0) 
thrown [open/src/hotspot/share/prims/jni.cpp, line 516]
Event: 0.806 Thread 0x0000000149374800 Exception <a 'java/io/FileNotFoundException'{0x00000006f0118480}> (0x00000006f0118480) 
thrown [open/src/hotspot/share/prims/jni.cpp, line 516]
Event: 0.807 Thread 0x0000000149374800 Exception <a 'java/lang/NoClassDefFoundError'{0x00000006efe24418}: com/intellij/rt/debugger/agent/CaptureStorage$3> (0x00000006efe24418) 
thrown [open/src/hotspot/share/classfile/systemDictionary.cpp, line 254]

VM Operations (20 events):
Event: 0.401 Executing VM operation: HandshakeAllThreads
Event: 0.401 Executing VM operation: HandshakeAllThreads done
Event: 0.442 Executing VM operation: HandshakeAllThreads
Event: 0.442 Executing VM operation: HandshakeAllThreads done
Event: 0.445 Executing VM operation: HandshakeAllThreads
Event: 0.445 Executing VM operation: HandshakeAllThreads done
Event: 0.453 Executing VM operation: G1CollectForAllocation
Event: 0.455 Executing VM operation: G1CollectForAllocation done
Event: 0.468 Executing VM operation: HandshakeAllThreads
Event: 0.468 Executing VM operation: HandshakeAllThreads done
Event: 0.596 Executing VM operation: ICBufferFull
Event: 0.596 Executing VM operation: ICBufferFull done
Event: 0.661 Executing VM operation: ICBufferFull
Event: 0.661 Executing VM operation: ICBufferFull done
Event: 0.688 Executing VM operation: G1CollectForAllocation
Event: 0.691 Executing VM operation: G1CollectForAllocation done
Event: 0.704 Executing VM operation: HandshakeAllThreads
Event: 0.704 Executing VM operation: HandshakeAllThreads done
Event: 0.746 Executing VM operation: HandshakeAllThreads
Event: 0.746 Executing VM operation: HandshakeAllThreads done

Events (20 events):
Event: 0.806 loading class com/intellij/rt/debugger/agent/CaptureAgent$CaptureInstrumentor done
Event: 0.806 loading class com/alibaba/nacos/shaded/com/google/common/util/concurrent/internal/InternalFutureFailureAccess
Event: 0.806 loading class com/alibaba/nacos/shaded/com/google/common/util/concurrent/internal/InternalFutureFailureAccess done
Event: 0.806 loading class com/intellij/rt/debugger/agent/CaptureAgent$CaptureInstrumentor$1
Event: 0.806 loading class com/intellij/rt/debugger/agent/CaptureAgent$CaptureInstrumentor$1 done
Event: 0.806 loading class com/alibaba/nacos/shaded/com/google/common/util/concurrent/AbstractFuture$AtomicHelper
Event: 0.806 loading class com/alibaba/nacos/shaded/com/google/common/util/concurrent/AbstractFuture$AtomicHelper done
Event: 0.807 loading class com/alibaba/nacos/shaded/com/google/common/util/concurrent/AbstractFuture$UnsafeAtomicHelper
Event: 0.807 loading class com/alibaba/nacos/shaded/com/google/common/util/concurrent/AbstractFuture$UnsafeAtomicHelper done
Event: 0.807 loading class java/util/concurrent/FutureTask done
Event: 0.807 loading class java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask done
Event: 0.807 loading class java/util/concurrent/FutureTask$WaitNode
Event: 0.807 loading class java/util/concurrent/FutureTask$WaitNode done
Event: 0.807 loading class com/intellij/rt/debugger/agent/CaptureStorage$3
Event: 0.807 loading class com/intellij/rt/debugger/agent/CaptureStorage$3 done
Event: 0.807 loading class java/lang/Throwable$WrappedPrintStream
Event: 0.807 loading class java/lang/Throwable$PrintStreamOrWriter
Event: 0.807 loading class java/lang/Throwable$PrintStreamOrWriter done
Event: 0.807 loading class java/lang/Throwable$WrappedPrintStream done
Event: 0.807 loading class com/intellij/rt/debugger/agent/CaptureStorage$8


Dynamic libraries:
0x0000000100ef0000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libjli.dylib
0x00000001a98aa000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x0000000191071000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x0000000194360000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000018e6f9000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000019b2dd000 	/usr/lib/libSystem.B.dylib
0x0000000192503000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000023b613000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00000001a28d0000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x0000000198e71000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x000000019daa7000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000019ddfe000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000026a083000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00000001f3ff9000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000026f0fc000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000026e144000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000018e35d000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x000000019cf0b000 	/usr/lib/libspindump.dylib
0x00000001926b5000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000019a6cf000 	/usr/lib/libbsm.0.dylib
0x00000001968f4000 	/usr/lib/libapp_launch_measurement.dylib
0x0000000195c9d000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00000001968f8000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x000000019848b000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00000001996af000 	/usr/lib/liblangid.dylib
0x0000000198e77000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x00000001930db000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x0000000193601000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00000001a2faf000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000019cd4e000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x0000000198468000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x0000000195cce000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000019b227000 	/usr/lib/libz.1.dylib
0x00000001a6ec2000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x0000000198e5c000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x00000001908d6000 	/usr/lib/libicucore.A.dylib
0x000000019ee85000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000019ddaf000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001b9e19000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x0000000193026000 	/usr/lib/libMobileGestalt.dylib
0x0000000198b55000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x00000001961d5000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x00000001904cb000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00000001a290c000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x00000001965fb000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000018fd96000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x0000000195dbc000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000019d374000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x0000000193024000 	/usr/lib/libenergytrace.dylib
0x00000001ae1bf000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x0000000190f21000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00000001a2d03000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x0000000196885000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001ed9c9000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x0000000196942000 	/usr/lib/libxml2.2.dylib
0x000000019a5b3000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000018cca0000 	/usr/lib/libobjc.A.dylib
0x000000018cfad000 	/usr/lib/libc++.1.dylib
0x00000001a2c84000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x0000000193d31000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000018d109000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x0000000199231000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000018fb77000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001eef0e000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001ef492000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00000001ef495000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x0000000198eb2000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001f4bbc000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001e11d8000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000019b2e2000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001ccaec000 	/usr/lib/swift/libswiftAccelerate.dylib
0x000000019e81a000 	/usr/lib/swift/libswiftCore.dylib
0x00000001b64c2000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001b651c000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001b38d6000 	/usr/lib/swift/libswiftDarwin.dylib
0x0000000275d11000 	/usr/lib/swift/libswiftDataDetection.dylib
0x00000001a47fb000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001b651d000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001c3073000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001d2049000 	/usr/lib/swift/libswiftOSLog.dylib
0x00000001a7468000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x0000000275d3e000 	/usr/lib/swift/libswiftObservation.dylib
0x00000001c849a000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001ccadd000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001b64d4000 	/usr/lib/swift/libswiftXPC.dylib
0x0000000275e24000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x0000000275e27000 	/usr/lib/swift/libswift_Concurrency.dylib
0x0000000275f86000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x0000000276019000 	/usr/lib/swift/libswift_errno.dylib
0x000000027601b000 	/usr/lib/swift/libswift_math.dylib
0x000000027601e000 	/usr/lib/swift/libswift_signal.dylib
0x000000027601f000 	/usr/lib/swift/libswift_stdio.dylib
0x0000000276020000 	/usr/lib/swift/libswift_time.dylib
0x00000001a746c000 	/usr/lib/swift/libswiftos.dylib
0x00000001b9d70000 	/usr/lib/swift/libswiftsimd.dylib
0x0000000276021000 	/usr/lib/swift/libswiftsys_time.dylib
0x0000000276022000 	/usr/lib/swift/libswiftunistd.dylib
0x000000019b50a000 	/usr/lib/libcompression.dylib
0x000000019da07000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000019c9ed000 	/usr/lib/libate.dylib
0x000000019b2d7000 	/usr/lib/system/libcache.dylib
0x000000019b292000 	/usr/lib/system/libcommonCrypto.dylib
0x000000019b2bd000 	/usr/lib/system/libcompiler_rt.dylib
0x000000019b2b2000 	/usr/lib/system/libcopyfile.dylib
0x000000018cdfb000 	/usr/lib/system/libcorecrypto.dylib
0x000000018cee1000 	/usr/lib/system/libdispatch.dylib
0x000000018d0a1000 	/usr/lib/system/libdyld.dylib
0x000000019b2cd000 	/usr/lib/system/libkeymgr.dylib
0x000000019b275000 	/usr/lib/system/libmacho.dylib
0x000000019a6a8000 	/usr/lib/system/libquarantine.dylib
0x000000019b2ca000 	/usr/lib/system/libremovefile.dylib
0x00000001930a0000 	/usr/lib/system/libsystem_asl.dylib
0x000000018cd90000 	/usr/lib/system/libsystem_blocks.dylib
0x000000018cf2b000 	/usr/lib/system/libsystem_c.dylib
0x000000019b2c1000 	/usr/lib/system/libsystem_collections.dylib
0x000000019969c000 	/usr/lib/system/libsystem_configuration.dylib
0x0000000198437000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000019adb8000 	/usr/lib/system/libsystem_coreservices.dylib
0x0000000190ba2000 	/usr/lib/system/libsystem_darwin.dylib
0x0000000276159000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000019b2ce000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000027615d000 	/usr/lib/system/libsystem_eligibility.dylib
0x000000018cf28000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000018d0d9000 	/usr/lib/system/libsystem_info.dylib
0x000000019b236000 	/usr/lib/system/libsystem_m.dylib
0x000000018ce9a000 	/usr/lib/system/libsystem_malloc.dylib
0x0000000193009000 	/usr/lib/system/libsystem_networkextension.dylib
0x0000000191004000 	/usr/lib/system/libsystem_notify.dylib
0x00000001996a1000 	/usr/lib/system/libsystem_sandbox.dylib
0x0000000276165000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000019b2c6000 	/usr/lib/system/libsystem_secinit.dylib
0x000000018d058000 	/usr/lib/system/libsystem_kernel.dylib
0x000000018d0d1000 	/usr/lib/system/libsystem_platform.dylib
0x000000018d094000 	/usr/lib/system/libsystem_pthread.dylib
0x0000000194bfa000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000018cddf000 	/usr/lib/system/libsystem_trace.dylib
0x000000019b2a0000 	/usr/lib/system/libunwind.dylib
0x000000018cd94000 	/usr/lib/system/libxpc.dylib
0x000000018d03a000 	/usr/lib/libc++abi.dylib
0x0000000274d31000 	/usr/lib/libRosetta.dylib
0x0000000190ea0000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000019b2aa000 	/usr/lib/liboah.dylib
0x000000019b2df000 	/usr/lib/libfakelink.dylib
0x00000001a6975000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00000001b33ff000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x0000000192c3e000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x00000001968bc000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x0000000190bad000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x0000000195d31000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000019adbf000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000019b42c000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x0000000194b75000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000018d648000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000019c842000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x00000001968ca000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000019b4c1000 	/usr/lib/libapple_nghttp2.dylib
0x00000001947d6000 	/usr/lib/libsqlite3.dylib
0x000000019f4fd000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x0000000194b0c000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x0000000194c03000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000019b279000 	/usr/lib/system/libkxld.dylib
0x00000002371cd000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x0000000274b89000 	/usr/lib/libCoreEntitlements.dylib
0x0000000252609000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x00000001947bb000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000019ad9f000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000019a6b7000 	/usr/lib/libcoretls.dylib
0x000000019c8b8000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000019b504000 	/usr/lib/libpam.2.dylib
0x000000019c92c000 	/usr/lib/libxar.1.dylib
0x000000019b334000 	/usr/lib/libarchive.2.dylib
0x00000001a0bff000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000023b637000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000025b630000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000025c302000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x0000000275db5000 	/usr/lib/swift/libswiftSystem.dylib
0x00000001996aa000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00000001b63ad000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00000001a3cbc000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00000001f3ebe000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x0000000196acd000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x0000000192b6c000 	/usr/lib/libboringssl.dylib
0x0000000194be9000 	/usr/lib/libdns_services.dylib
0x00000001b5561000 	/usr/lib/libquic.dylib
0x000000019e7a9000 	/usr/lib/libusrtcp.dylib
0x00000001d9eb3000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x0000000275d12000 	/usr/lib/swift/libswiftDistributed.dylib
0x0000000275dab000 	/usr/lib/swift/libswiftSynchronization.dylib
0x0000000192c3d000 	/usr/lib/libnetwork.dylib
0x00000001c70d9000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000019b49c000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00000001a4f91000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001d5b7f000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00000001a4f6d000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x0000000274a54000 	/usr/lib/libAppleArchive.dylib
0x000000019adab000 	/usr/lib/libbz2.1.0.dylib
0x000000019c899000 	/usr/lib/liblzma.5.dylib
0x000000019a3a7000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000001b6436000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x000000019ca87000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000018e154000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000024a959000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00000001a4a93000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000019a5df000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000019a741000 	/usr/lib/libgermantok.dylib
0x00000001997ce000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x0000000194a27000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00000001a4b60000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000019a5d0000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00000001a67b0000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x0000000198882000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x00000001930b8000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00000001aa34b000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x000000019d372000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000018f69e000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x0000000198726000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x0000000198481000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x0000000196a2c000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000019b502000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000025a981000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x000000019d3ba000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x00000001996a8000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x000000019c8ba000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x000000019c93b000 	/usr/lib/libutil.dylib
0x0000000265840000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x0000000195dc5000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00000001a2cde000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x000000019c973000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000018dac0000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000019f0d4000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x0000000193ee7000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x000000019d990000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000019f4c7000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000019f4be000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000019f0a6000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000019a374000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x0000000241a32000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x000000019cec0000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x00000001965a9000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x0000000275115000 	/usr/lib/libhvf.dylib
0x000000025688f000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x0000000275d5b000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x0000000275ee3000 	/usr/lib/swift/libswift_RegexParser.dylib
0x000000019d23b000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000019ccdc000 	/usr/lib/libexpat.1.dylib
0x000000019d866000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x000000019d891000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000019d97b000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x000000019d281000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000019d922000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000019d919000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x0000000246700000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x0000000241afd000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00000001ed9bb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x0000000242672000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x0000000241a33000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00000001a85b1000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x0000000259af0000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x000000019ceb4000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001eda19000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001ed9dd000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001edba5000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001ed9e6000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001ed9da000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000001ed9c3000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00000001995ec000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000019ad0a000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000019a759000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000019ab90000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000019a9a5000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000019abc2000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001f12b0000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001f1292000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000018d93b000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001bb116000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001c888f000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001b649d000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000019d94d000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000019f452000 	/usr/lib/libcups.2.dylib
0x000000019f4ec000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000019f152000 	/usr/lib/libresolv.9.dylib
0x000000019b319000 	/usr/lib/libiconv.2.dylib
0x000000019cf12000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00000001a73bf000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x000000019ccf7000 	/usr/lib/libheimdal-asn1.dylib
0x000000019688f000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000019f54f000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000019689d000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000019b274000 	/usr/lib/libcharset.1.dylib
0x00000001edf41000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001afb85000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000024e288000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000019f08d000 	/usr/lib/libAudioStatistics.dylib
0x00000001986fe000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x000000018f7bf000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00000001a96d7000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000019f030000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00000001a0967000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000019cde1000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x00000002682fb000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000023c0f4000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x00000001cd1af000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00000001b64a1000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x000000019cf01000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000019f4d0000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001b565a000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x000000019f371000 	/usr/lib/libSMC.dylib
0x000000019d830000 	/usr/lib/libAudioToolboxUtility.dylib
0x000000019f4de000 	/usr/lib/libperfcheck.dylib
0x0000000237f8a000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00000001d9c7a000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001b6468000 	/usr/lib/libmis.dylib
0x000000019ee3e000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000019d981000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001ef5e2000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00000001a509e000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000019cbdb000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00000001a3bc5000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00000001a73c0000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000019a438000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000023f098000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00000001a2fff000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001beab2000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x00000002749d2000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00000001afc41000 	/usr/lib/libAccessibility.dylib
0x000000019a282000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000019b5e0000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000019a744000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000019b4db000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000019b5db000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00000001997d5000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000018e261000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000024f9f9000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x000000019d914000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x000000019d8f4000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000019d91c000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000001da081000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00000001aee60000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000026bb37000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x000000019cc93000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00000001a4c0d000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x000000019f6c2000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00000001a3dad000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00000001a3cf8000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000019f4b1000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x0000000196a8d000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000019cd01000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000019ca7e000 	/usr/lib/libIOReport.dylib
0x0000000237b9a000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x0000000274d94000 	/usr/lib/libTLE.dylib
0x00000001eaa17000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000019a6e1000 	/usr/lib/libmecab.dylib
0x000000018e3f1000 	/usr/lib/libCRFSuite.dylib
0x00000001996b1000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000019b494000 	/usr/lib/libThaiTokenizer.dylib
0x000000019a6ab000 	/usr/lib/libCheckFix.dylib
0x0000000195cd0000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000024b23b000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x0000000190ee0000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001c8999000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x000000019c93f000 	/usr/lib/libxslt.1.dylib
0x000000019a66e000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00000001a6b33000 	/usr/lib/libcurl.4.dylib
0x0000000274fc8000 	/usr/lib/libcrypto.46.dylib
0x0000000275af0000 	/usr/lib/libssl.48.dylib
0x00000001a680c000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00000001a6848000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000019f16f000 	/usr/lib/libsasl2.2.dylib
0x00000001b38d5000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00000001a362d000 	/usr/lib/swift/libswiftFoundation.dylib
0x00000001ea4a1000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00000001d6333000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000025c3f9000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x00000001a28f9000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x0000000102184000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/server/libjvm.dylib
0x0000000100f58000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libjimage.dylib
0x0000000100fb4000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libjdwp.dylib
0x0000000100ffc000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libjava.dylib
0x0000000101154000 	/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x0000000100f94000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libinstrument.dylib
0x0000000101098000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libzip.dylib
0x00000002749c4000 	/usr/lib/i18n/libiconv_std.dylib
0x00000002749ba000 	/usr/lib/i18n/libUTF8.dylib
0x00000002749c9000 	/usr/lib/i18n/libmapper_none.dylib
0x0000000101120000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libdt_socket.dylib
0x00000001011e8000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libnio.dylib
0x0000000102100000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libnet.dylib
0x0000000101134000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libmanagement.dylib
0x0000000101cc0000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libmanagement_ext.dylib
0x0000000101cd4000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libextnet.dylib


VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:56417,suspend=y,server=n -agentpath:/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/UserApplication_2025_08_02_170723.jfr,log=/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/UserApplication_2025_08_02_170723.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2024.3/captureAgent/debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.xgwc.user.UserApplication
java_class_path (initial): /Users/<USER>/IdeaProjects/xgwc-parent/xgwc-api/xgwc-api-user/target/classes:/Users/<USER>/IdeaProjects/xgwc-parent/xgwc-common/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.jar:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.37/hutool-all-5.8.37.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/33.4.6-jre/guava-33.4.6-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.3/failureaccess-1.0.3.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/jspecify/jspecify/0.3.0/jspecify-0.3.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar:/Users/<USER>/.m2/repository/com/aliyun/ecs20140526/7.1.0/ecs20140526-7.1.0.jar:/Users/<USER>/.m2/repository/com/aliyun/alibabacloud-gateway-pop/0.0.6/alibabacloud-gateway-pop-0.0.6.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-encode-util/0.0.2/darabonba-encode-util-0.0.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.68/bcprov-jdk15on-1.68.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-signature-util/0.0.4/darabonba-signature-util-0.0.4.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-string/0.0.5/darabonba-string-0.0.5.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-map/0.0.1/darabonba-map-0.0.1.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-array/0.1.0/darabonba-array-0.1.0.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/5.4.1/poi-ooxml-5.4.1.jar:/Users/<USER>/.m2/repository/
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 9                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 301989888                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4831838208                                {product} {ergonomic}
   size_t MaxNewSize                               = 2898264064                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4831838208                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=/Users/<USER>/.nvm/versions/node/v22.14.0/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin
SHELL=/bin/zsh
LC_CTYPE=zh_CN.UTF-8

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:29 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6030 arm64
OS uptime: 6 days 19:58 hours
rlimit (soft/hard): STACK 8176k/65520k , CORE 0k/infinity , NPROC 2666/4000 , NOFILE 10240/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 5.12 3.73 3.00

CPU: total 11 (initial active 11) 0x61:0x0:0x5f4dea93:0, fp, simd, crc, lse

Memory: 16k page, physical 18874368k(95584k free), swap 9437184k(1158336k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.14+8-LTS-191) for bsd-aarch64 JRE (17.0.14+8-LTS-191), built on Dec  3 2024 11:03:03 by "mach5one" with clang Apple LLVM 12.0.0 (clang-1200.0.32.29)

END.
