#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGBUS (0xa) at pc=0x0000000105613d50, pid=51617, tid=55307
#
# JRE version: Java(TM) SE Runtime Environment (17.0.14+8) (build 17.0.14+8-LTS-191)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.14+8-LTS-191, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-aarch64)
# Problematic frame:
# C  [libzip.dylib+0x13d50]  newEntry+0x68
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:61027,suspend=y,server=n -agentpath:/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/UserApplication_2025_07_25_115157.jfr,log=/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/UserApplication_2025_07_25_115157.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2024.3/captureAgent/debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 com.xgwc.user.UserApplication

Host: "Mac15,6" arm64 1 MHz, 11 cores, 18G, Darwin 24.5.0, macOS 15.5 (24F74)
Time: Fri Jul 25 11:52:02 2025 CST elapsed time: 4.618352 seconds (0d 0h 0m 4s)

---------------  T H R E A D  ---------------

Current thread (0x0000000153df1600):  JavaThread "WebSocketConnectReadThread-81" [_thread_in_native, id=55307, stack(0x0000000175e74000,0x0000000176077000)]

Stack: [0x0000000175e74000,0x0000000176077000],  sp=0x0000000176075e50,  free space=2055k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libzip.dylib+0x13d50]  newEntry+0x68
C  [libzip.dylib+0x13c20]  ZIP_GetEntry2+0x14c
C  [libzip.dylib+0x14608]  ZIP_FindEntry+0x3c
V  [libjvm.dylib+0x25b7ec]  ClassPathZipEntry::open_entry(JavaThread*, char const*, int*, bool)+0xb4
V  [libjvm.dylib+0x25b920]  ClassPathZipEntry::open_stream(JavaThread*, char const*)+0x20
V  [libjvm.dylib+0x25eab0]  ClassLoader::load_class(Symbol*, bool, JavaThread*)+0x150
V  [libjvm.dylib+0x8ebfa4]  SystemDictionary::load_instance_class_impl(Symbol*, Handle, JavaThread*)+0x2d0
V  [libjvm.dylib+0x8ea890]  SystemDictionary::load_instance_class(unsigned int, Symbol*, Handle, JavaThread*)+0x30
V  [libjvm.dylib+0x8e9f98]  SystemDictionary::resolve_instance_class_or_null(Symbol*, Handle, Handle, JavaThread*)+0x4f4
V  [libjvm.dylib+0x8e956c]  SystemDictionary::resolve_or_fail(Symbol*, Handle, Handle, bool, JavaThread*)+0x80
V  [libjvm.dylib+0x2ba7a8]  ConstantPool::klass_at_impl(constantPoolHandle const&, int, JavaThread*)+0x1e0
V  [libjvm.dylib+0x463d98]  InterpreterRuntime::_new(JavaThread*, ConstantPool*, int)+0x94
j  com.intellij.rt.debugger.agent.CaptureStorage.getAsyncStackTrace(Ljava/lang/Throwable;)[Ljava/lang/StackTraceElement;+11
j  java.lang.Throwable.printStackTrace(Ljava/lang/Throwable$PrintStreamOrWriter;)V+32 java.base@17.0.14
j  java.lang.Throwable.printStackTrace(Ljava/io/PrintStream;)V+9 java.base@17.0.14
j  java.lang.Throwable.printStackTrace()V+4 java.base@17.0.14
j  com.xgwc.user.config.MyWebSocketClient.onError(Ljava/lang/Exception;)V+1
j  org.java_websocket.client.WebSocketClient.handleIOException(Ljava/io/IOException;)V+9
j  org.java_websocket.client.WebSocketClient.run()V+309
j  java.lang.Thread.run()V+11 java.base@17.0.14
v  ~StubRoutines::call_stub
V  [libjvm.dylib+0x46deac]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x38c
V  [libjvm.dylib+0x46cec8]  JavaCalls::call_virtual(JavaValue*, Klass*, Symbol*, Symbol*, JavaCallArguments*, JavaThread*)+0x11c
V  [libjvm.dylib+0x46cf94]  JavaCalls::call_virtual(JavaValue*, Handle, Klass*, Symbol*, Symbol*, JavaThread*)+0x64
V  [libjvm.dylib+0x520874]  thread_entry(JavaThread*, JavaThread*)+0xc4
V  [libjvm.dylib+0x91bf7c]  JavaThread::thread_main_inner()+0x148
V  [libjvm.dylib+0x91a864]  Thread::call_run()+0x114
V  [libjvm.dylib+0x7ba4b4]  thread_native_entry(Thread*)+0x158
C  [libsystem_pthread.dylib+0x6c0c]  _pthread_start+0x88

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  com.intellij.rt.debugger.agent.CaptureStorage.getAsyncStackTrace(Ljava/lang/Throwable;)[Ljava/lang/StackTraceElement;+11
j  java.lang.Throwable.printStackTrace(Ljava/lang/Throwable$PrintStreamOrWriter;)V+32 java.base@17.0.14
j  java.lang.Throwable.printStackTrace(Ljava/io/PrintStream;)V+9 java.base@17.0.14
j  java.lang.Throwable.printStackTrace()V+4 java.base@17.0.14
j  com.xgwc.user.config.MyWebSocketClient.onError(Ljava/lang/Exception;)V+1
j  org.java_websocket.client.WebSocketClient.handleIOException(Ljava/io/IOException;)V+9
j  org.java_websocket.client.WebSocketClient.run()V+309
j  java.lang.Thread.run()V+11 java.base@17.0.14
v  ~StubRoutines::call_stub

siginfo: si_signo: 10 (SIGBUS), si_code: 1 (BUS_ADRALN), si_addr: 0x0000000104743637

Registers:
 x0=0x000060000961b0c0  x1=0x0000000000000000  x2=0xffffffffffffffd0  x3=0x000060000961b0d0
 x4=0x000060000961b140  x5=0x0000000099017ffb  x6=0x0000000019200000  x7=0x0000000000000002
 x8=0x000000010484f61b  x9=0x000000000010c000 x10=0x0000600009618000 x11=0x00000000000030c0
x12=0x0000000000000050 x13=0x0000000000000001 x14=0x00000000ffffffd0 x15=0x00000000000007fb
x16=0x00000001813dbf20 x17=0x00000001f05040e0 x18=0x0000000000000000 x19=0x000060000961b0c0
x20=0x0000000000000000 x21=0x0000600000c64a80 x22=0x000000010474361b x23=0x0000000024275288
x24=0x000000000000002f x25=0x0000000000000035 x26=0x00000000000000a7 x27=0x000060000961b0e8
x28=0x000000010f7799f0  fp=0x0000000176075ed0  lr=0x0000000105613d1c  sp=0x0000000176075e50
pc=0x0000000105613d50 cpsr=0x0000000080001000

Register to memory mapping:

 x0=0x000060000961b0c0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
 x1=0x0 is NULL
 x2=0xffffffffffffffd0 is an unknown value
 x3=0x000060000961b0d0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
 x4=0x000060000961b140 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
 x5=0x0000000099017ffb is an unknown value
 x6=0x0000000019200000 is an unknown value
 x7=0x0000000000000002 is an unknown value
 x8=0x000000010484f61b points into unknown readable memory: 00 00 00 00 00
 x9=0x000000000010c000 is an unknown value
x10=0x0000600009618000 points into unknown readable memory: 0x2928003b13880002 | 02 00 88 13 3b 00 28 29
x11=0x00000000000030c0 is an unknown value
x12=0x0000000000000050 is an unknown value
x13=0x0000000000000001 is an unknown value
x14=0x00000000ffffffd0 is an unknown value
x15=0x00000000000007fb is an unknown value
x16=0x00000001813dbf20: __bzero+0 in /usr/lib/system/libsystem_platform.dylib at 0x00000001813d9000
x17=0x00000001f05040e0 points into unknown readable memory: 0x00000001813dbf20 | 20 bf 3d 81 01 00 00 00
x18=0x0 is NULL
x19=0x000060000961b0c0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x20=0x0 is NULL
x21=0x0000600000c64a80 points into unknown readable memory: 0x000060000937c3c0 | c0 c3 37 09 00 60 00 00
x22=0x000000010474361b points into unknown readable memory: 50 4b 01 02 00
x23=0x0000000024275288 is an unknown value
x24=0x000000000000002f is an unknown value
x25=0x0000000000000035 is an unknown value
x26=0x00000000000000a7 is an unknown value
x27=0x000060000961b0e8 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x28=0x000000010f7799f0 points into unknown readable memory: 0x65746e692f6d6f63 | 63 6f 6d 2f 69 6e 74 65


Top of Stack: (sp=0x0000000176075e50)
0x0000000176075e50:   0000000000000000 0000000000000000
0x0000000176075e60:   0000000000000000 0000000000000000
0x0000000176075e70:   0000000000000000 0000000000000000
0x0000000176075e80:   000000010f7799f0 000000011f8d0800
0x0000000176075e90:   00000000000000a7 0000000000000035
0x0000000176075ea0:   000000000000002f 0000000024275288
0x0000000176075eb0:   000060000976b5c0 0000000000000000
0x0000000176075ec0:   000000010f779a20 0000600000c64a80
0x0000000176075ed0:   0000000176075f30 0000000105613c20
0x0000000176075ee0:   000000010f7799f0 000000000000002f
0x0000000176075ef0:   0000000000000001 000000010f779a20
0x0000000176075f00:   0000000153df1940 000000010f779a20
0x0000000176075f10:   0000600000c64a80 000000010f779a20
0x0000000176075f20:   000000017607605c 0000000176075f74
0x0000000176075f30:   0000000176075f60 0000000105614608
0x0000000176075f40:   000000017607605c 0000600006679dd0
0x0000000176075f50:   0000000000000000 0000000153df1600
0x0000000176075f60:   0000000176076040 00000001059277ec
0x0000000176075f70:   00000001060e8a0d 0000000000000100
0x0000000176075f80:   0000000176075fa0 0000000105bec254
0x0000000176075f90:   00000001060e8a0d 0000000176076020
0x0000000176075fa0:   0000000176075ff0 000000010592c1dc
0x0000000176075fb0:   0000000000000000 0000000000000000
0x0000000176075fc0:   0000000000000001 00000001383422a8
0x0000000176075fd0:   0000000153df1600 000000010f779dc8
0x0000000176075fe0:   00000001060fdabd 00000001760760e8
0x0000000176075ff0:   0000000176076010 5bd8b542067600b9
0x0000000176076000:   0000000000000001 000000010f779a20
0x0000000176076010:   0000600006679dd0 00000001383422a8
0x0000000176076020:   0000000153df1600 000000010f779dc8
0x0000000176076030:   000000010f7799e0 0000600006679dd0
0x0000000176076040:   0000000176076070 0000000105927920 

Instructions: (pc=0x0000000105613d50)
0x0000000105613c50:   6b0c017f 54ffff60 17ffffde d2800016
0x0000000105613c60:   72001ebf 54000160 b5000156 f100073f
0x0000000105613c70:   54fff7cb 8b140328 385ff108 7100bd1f
0x0000000105613c80:   54fff741 d2800016 14000002 f9004e7f
0x0000000105613c90:   f9402a60 94000464 aa1603e0 a9457bfd
0x0000000105613ca0:   a9444ff4 a94357f6 a9425ff8 a94167fa
0x0000000105613cb0:   a8c66ffc d65f03c0 6b03003f 540000e1
0x0000000105613cc0:   71000421 540000eb 38401408 38401449
0x0000000105613cd0:   6b09011f 54ffff60 52800000 d65f03c0
0x0000000105613ce0:   52800020 d65f03c0 d10243ff a9036ffc
0x0000000105613cf0:   a90467fa a9055ff8 a90657f6 a9074ff4
0x0000000105613d00:   a9087bfd 910203fd aa0203f4 aa0103f6
0x0000000105613d10:   aa0003f5 52800900 9400046a aa0003f3
0x0000000105613d20:   b4001320 f900027f aa1303fb f8028f7f
0x0000000105613d30:   f9001a7f 3940c2a8 34000288 f9400ea8
0x0000000105613d40:   f94006c9 8b090108 f94016a9 cb090116
0x0000000105613d50:   79403ad8 39407ada 39407edc 794042c8
0x0000000105613d60:   f90017e8 b9400ec8 f9000668 b9401ac8
0x0000000105613d70:   f9000fe8 f9000a68 794016c8 34000488
0x0000000105613d80:   b94016c8 14000023 f94006d7 34000d54
0x0000000105613d90:   f9401ea8 b4000288 f94022a9 eb17013f
0x0000000105613da0:   5400022c 5283fa4a 8b0a012a eb17015f
0x0000000105613db0:   540001ab 9140092a 8b170108 cb090116
0x0000000105613dc0:   79403ac8 79403ec9 794042cb 8b0802e8
0x0000000105613dd0:   8b090108 8b0b0108 9100b908 eb0a011f
0x0000000105613de0:   54000b4d aa1503e0 aa1703e1 52840002
0x0000000105613df0:   94000384 aa0003f6 b4000aa0 f9401ea0
0x0000000105613e00:   94000421 a903deb6 17ffffd2 d2800008
0x0000000105613e10:   aa0803f7 f9000e68 b94012c8 b9002268
0x0000000105613e20:   b842a2c9 f9405ea8 f9000be9 8b090108
0x0000000105613e30:   cb0803e8 f9001e68 794012c8 b9004268
0x0000000105613e40:   91000700 9400041f aa0003f9 f9000260 


Stack slot to memory mapping:
stack at sp + 0 slots: 0x0 is NULL
stack at sp + 1 slots: 0x0 is NULL
stack at sp + 2 slots: 0x0 is NULL
stack at sp + 3 slots: 0x0 is NULL
stack at sp + 4 slots: 0x0 is NULL
stack at sp + 5 slots: 0x0 is NULL
stack at sp + 6 slots: 0x000000010f7799f0 points into unknown readable memory: 0x65746e692f6d6f63 | 63 6f 6d 2f 69 6e 74 65
stack at sp + 7 slots: 0x000000011f8d0800 points into unknown readable memory: 0xffffffff5bbd78a2 | a2 78 bd 5b ff ff ff ff


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00006000070f78e0, length=64, elements={
0x000000010e14fc00, 0x000000010e153000, 0x000000010e152200, 0x000000012001b400,
0x000000010f84b600, 0x000000012001dc00, 0x000000011f8cb200, 0x000000011f8cb800,
0x000000010e166c00, 0x000000010e165a00, 0x000000010e81f200, 0x000000010e166000,
0x000000010fab1800, 0x000000010fb61000, 0x0000000153066400, 0x000000011fc8ec00,
0x000000010ed0e600, 0x00000001530da000, 0x00000001530c5a00, 0x0000000153106a00,
0x000000010ed1b000, 0x000000011fccb800, 0x00000001531e0000, 0x000000010ed12e00,
0x000000010fe3a400, 0x000000011fdbe200, 0x000000011fe52000, 0x000000010fe6de00,
0x000000011fe67a00, 0x00000001532c1600, 0x000000011fe78800, 0x00000001532ace00,
0x000000011fea4e00, 0x000000010edc4a00, 0x000000010e6dc400, 0x000000010ed99c00,
0x0000000153305200, 0x00000001532fde00, 0x0000000152811e00, 0x000000010fe9f800,
0x00000001532f2800, 0x0000000127854200, 0x0000000153a87600, 0x000000014c852c00,
0x0000000154a06600, 0x00000001549d9000, 0x000000015417b400, 0x00000001551b4e00,
0x00000001549e1e00, 0x00000001551d3e00, 0x0000000154177c00, 0x00000001536f6e00,
0x00000001551e9c00, 0x00000001536c5c00, 0x0000000154a0a800, 0x000000015369d800,
0x0000000153717000, 0x00000001551ab600, 0x0000000154340800, 0x00000001537c9200,
0x000000015530da00, 0x00000001552e7400, 0x0000000153df1600, 0x0000000153e2ac00
}

Java Threads: ( => current thread )
  0x000000010e14fc00 JavaThread "main" [_thread_in_native, id=4867, stack(0x000000016b99c000,0x000000016bb9f000)]
  0x000000010e153000 JavaThread "Reference Handler" daemon [_thread_blocked, id=19971, stack(0x000000016c7f0000,0x000000016c9f3000)]
  0x000000010e152200 JavaThread "Finalizer" daemon [_thread_blocked, id=18947, stack(0x000000016c9fc000,0x000000016cbff000)]
  0x000000012001b400 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=23043, stack(0x000000016cd20000,0x000000016cf23000)]
  0x000000010f84b600 JavaThread "Service Thread" daemon [_thread_blocked, id=30979, stack(0x000000016cf2c000,0x000000016d12f000)]
  0x000000012001dc00 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=23555, stack(0x000000016d138000,0x000000016d33b000)]
  0x000000011f8cb200 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=30467, stack(0x000000016d344000,0x000000016d547000)]
  0x000000011f8cb800 JavaThread "Sweeper thread" daemon [_thread_blocked, id=23811, stack(0x000000016d550000,0x000000016d753000)]
  0x000000010e166c00 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=24067, stack(0x000000016d75c000,0x000000016d95f000)]
  0x000000010e165a00 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=24323, stack(0x000000016d968000,0x000000016db6b000)]
  0x000000010e81f200 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=29443, stack(0x000000016db74000,0x000000016dd77000)]
  0x000000010e166000 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=29187, stack(0x000000016dd80000,0x000000016df83000)]
  0x000000010fab1800 JavaThread "Notification Thread" daemon [_thread_blocked, id=26371, stack(0x000000016ebd4000,0x000000016edd7000)]
  0x000000010fb61000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=43011, stack(0x000000016f1f8000,0x000000016f3fb000)]
  0x0000000153066400 JavaThread "com.alibaba.nacos.client.auth.ram.identify.watcher.0" daemon [_thread_blocked, id=33795, stack(0x000000016f81c000,0x000000016fa1f000)]
  0x000000011fc8ec00 JavaThread "Keep-Alive-Timer" daemon [_thread_blocked, id=33543, stack(0x000000016f610000,0x000000016f813000)]
  0x000000010ed0e600 JavaThread "com.alibaba.nacos.client.Worker.0" daemon [_thread_blocked, id=34307, stack(0x000000016fa28000,0x000000016fc2b000)]
  0x00000001530da000 JavaThread "com.alibaba.nacos.client.Worker.1" daemon [_thread_blocked, id=34563, stack(0x000000016fc34000,0x000000016fe37000)]
  0x00000001530c5a00 JavaThread "com.alibaba.nacos.client.Worker.2" daemon [_thread_blocked, id=41731, stack(0x000000016fe40000,0x0000000170043000)]
  0x0000000153106a00 JavaThread "nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent" daemon [_thread_blocked, id=41219, stack(0x000000017004c000,0x000000017024f000)]
  0x000000010ed1b000 JavaThread "com.alibaba.nacos.client.remote.worker.0" daemon [_thread_blocked, id=40963, stack(0x0000000170258000,0x000000017045b000)]
  0x000000011fccb800 JavaThread "com.alibaba.nacos.client.remote.worker.1" daemon [_thread_blocked, id=40707, stack(0x0000000170464000,0x0000000170667000)]
  0x00000001531e0000 JavaThread "grpc-nio-worker-ELG-1-1" daemon [_thread_in_native, id=35843, stack(0x000000017087c000,0x0000000170a7f000)]
  0x000000010ed12e00 JavaThread "grpc-default-executor-0" daemon [_thread_blocked, id=36355, stack(0x0000000170a88000,0x0000000170c8b000)]
  0x000000010fe3a400 JavaThread "nacos-grpc-client-executor-192.168.1.189-0" daemon [_thread_blocked, id=36611, stack(0x0000000170c94000,0x0000000170e97000)]
  0x000000011fdbe200 JavaThread "nacos-grpc-client-executor-192.168.1.189-1" daemon [_thread_blocked, id=39683, stack(0x0000000170ea0000,0x00000001710a3000)]
  0x000000011fe52000 JavaThread "grpc-nio-worker-ELG-1-2" daemon [_thread_in_native, id=37123, stack(0x00000001710ac000,0x00000001712af000)]
  0x000000010fe6de00 JavaThread "Attach Listener" daemon [_thread_blocked, id=37635, stack(0x00000001712b8000,0x00000001714bb000)]
  0x000000011fe67a00 JavaThread "nacos-grpc-client-executor-192.168.1.189-2" daemon [_thread_blocked, id=37891, stack(0x00000001714c4000,0x00000001716c7000)]
  0x00000001532c1600 JavaThread "nacos-grpc-client-executor-192.168.1.189-3" daemon [_thread_blocked, id=38147, stack(0x00000001716d0000,0x00000001718d3000)]
  0x000000011fe78800 JavaThread "nacos-grpc-client-executor-192.168.1.189-4" daemon [_thread_blocked, id=38403, stack(0x00000001718dc000,0x0000000171adf000)]
  0x00000001532ace00 JavaThread "nacos-grpc-client-executor-192.168.1.189-5" daemon [_thread_blocked, id=43779, stack(0x0000000171ae8000,0x0000000171ceb000)]
  0x000000011fea4e00 JavaThread "nacos-grpc-client-executor-192.168.1.189-6" daemon [_thread_blocked, id=65283, stack(0x0000000171cf4000,0x0000000171ef7000)]
  0x000000010edc4a00 JavaThread "nacos.publisher-com.alibaba.nacos.common.ability.AbstractAbilityControlManager$AbilityUpdateEvent" daemon [_thread_blocked, id=65027, stack(0x0000000171f00000,0x0000000172103000)]
  0x000000010e6dc400 JavaThread "nacos-grpc-client-executor-192.168.1.189-7" daemon [_thread_blocked, id=64515, stack(0x000000017210c000,0x000000017230f000)]
  0x000000010ed99c00 JavaThread "nacos-grpc-client-executor-192.168.1.189-8" daemon [_thread_blocked, id=64003, stack(0x0000000172318000,0x000000017251b000)]
  0x0000000153305200 JavaThread "nacos-grpc-client-executor-192.168.1.189-9" daemon [_thread_blocked, id=63747, stack(0x0000000172524000,0x0000000172727000)]
  0x00000001532fde00 JavaThread "nacos-grpc-client-executor-192.168.1.189-10" daemon [_thread_blocked, id=63491, stack(0x0000000172730000,0x0000000172933000)]
  0x0000000152811e00 JavaThread "nacos-grpc-client-executor-192.168.1.189-11" daemon [_thread_blocked, id=62979, stack(0x000000017293c000,0x0000000172b3f000)]
  0x000000010fe9f800 JavaThread "RMI TCP Connection(2)-192.168.31.222" daemon [_thread_in_native, id=62723, stack(0x0000000172b48000,0x0000000172d4b000)]
  0x00000001532f2800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=45315, stack(0x0000000172f60000,0x0000000173163000)]
  0x0000000127854200 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=61459, stack(0x0000000172d54000,0x0000000172f57000)]
  0x0000000153a87600 JavaThread "RMI TCP Connection(4)-192.168.31.222" daemon [_thread_in_native, id=62215, stack(0x000000017316c000,0x000000017336f000)]
  0x000000014c852c00 JavaThread "RMI TCP Connection(5)-192.168.31.222" daemon [_thread_in_native, id=45831, stack(0x0000000173378000,0x000000017357b000)]
  0x0000000154a06600 JavaThread "Catalina-utility-1" [_thread_blocked, id=60935, stack(0x0000000173584000,0x0000000173787000)]
  0x00000001549d9000 JavaThread "Catalina-utility-2" [_thread_blocked, id=60419, stack(0x0000000173790000,0x0000000173993000)]
  0x000000015417b400 JavaThread "http-nio-8080-exec-1" daemon [_thread_blocked, id=60163, stack(0x000000017399c000,0x0000000173b9f000)]
  0x00000001551b4e00 JavaThread "http-nio-8080-exec-2" daemon [_thread_blocked, id=46339, stack(0x0000000173ba8000,0x0000000173dab000)]
  0x00000001549e1e00 JavaThread "http-nio-8080-exec-3" daemon [_thread_blocked, id=59395, stack(0x0000000173db4000,0x0000000173fb7000)]
  0x00000001551d3e00 JavaThread "http-nio-8080-exec-4" daemon [_thread_blocked, id=59139, stack(0x0000000173fc0000,0x00000001741c3000)]
  0x0000000154177c00 JavaThread "http-nio-8080-exec-5" daemon [_thread_blocked, id=47107, stack(0x00000001741cc000,0x00000001743cf000)]
  0x00000001536f6e00 JavaThread "http-nio-8080-exec-6" daemon [_thread_blocked, id=47619, stack(0x00000001743d8000,0x00000001745db000)]
  0x00000001551e9c00 JavaThread "http-nio-8080-exec-7" daemon [_thread_blocked, id=58627, stack(0x00000001745e4000,0x00000001747e7000)]
  0x00000001536c5c00 JavaThread "http-nio-8080-exec-8" daemon [_thread_blocked, id=58371, stack(0x00000001747f0000,0x00000001749f3000)]
  0x0000000154a0a800 JavaThread "http-nio-8080-exec-9" daemon [_thread_blocked, id=58115, stack(0x00000001749fc000,0x0000000174bff000)]
  0x000000015369d800 JavaThread "http-nio-8080-exec-10" daemon [_thread_blocked, id=48387, stack(0x0000000174c08000,0x0000000174e0b000)]
  0x0000000153717000 JavaThread "container-0" [_thread_blocked, id=48899, stack(0x0000000174e14000,0x0000000175017000)]
  0x00000001551ab600 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=49155, stack(0x0000000175020000,0x0000000175223000)]
  0x0000000154340800 JavaThread "lettuce-eventExecutorLoop-1-1" daemon [_thread_blocked, id=56071, stack(0x0000000175644000,0x0000000175847000)]
  0x00000001537c9200 JavaThread "lettuce-timer-3-1" daemon [_thread_blocked, id=49923, stack(0x0000000175850000,0x0000000175a53000)]
  0x000000015530da00 JavaThread "Java2D Disposer" daemon [_thread_blocked, id=55047, stack(0x0000000175a5c000,0x0000000175c5f000)]
  0x00000001552e7400 JavaThread "thd-captcha-cache-clean" [_thread_blocked, id=50439, stack(0x0000000175c68000,0x0000000175e6b000)]
=>0x0000000153df1600 JavaThread "WebSocketConnectReadThread-81" [_thread_in_native, id=55307, stack(0x0000000175e74000,0x0000000176077000)]
  0x0000000153e2ac00 JavaThread "Timer-0" [_thread_blocked, id=50947, stack(0x0000000176080000,0x0000000176283000)]

Other Threads:
  0x000000010526f200 VMThread "VM Thread" [stack: 0x000000016c5e4000,0x000000016c7e7000] [id=18179]
  0x000000010552d600 WatcherThread [stack: 0x000000016f404000,0x000000016f607000] [id=42499]
  0x000000011f6ebdb0 GCTaskThread "GC Thread#0" [stack: 0x000000016bba8000,0x000000016bdab000] [id=13827]
  0x000000011f6f77c0 GCTaskThread "GC Thread#1" [stack: 0x000000016df8c000,0x000000016e18f000] [id=28675]
  0x000000010f004b40 GCTaskThread "GC Thread#2" [stack: 0x000000016e198000,0x000000016e39b000] [id=25091]
  0x000000010f670880 GCTaskThread "GC Thread#3" [stack: 0x000000016e3a4000,0x000000016e5a7000] [id=27907]
  0x000000010df05250 GCTaskThread "GC Thread#4" [stack: 0x000000016e5b0000,0x000000016e7b3000] [id=27395]
  0x000000010f004df0 GCTaskThread "GC Thread#5" [stack: 0x000000016e7bc000,0x000000016e9bf000] [id=26883]
  0x0000000105407320 GCTaskThread "GC Thread#6" [stack: 0x000000016e9c8000,0x000000016ebcb000] [id=25603]
  0x000000010f681ad0 GCTaskThread "GC Thread#7" [stack: 0x000000016ede0000,0x000000016efe3000] [id=26115]
  0x000000010df07350 GCTaskThread "GC Thread#8" [stack: 0x000000016efec000,0x000000016f1ef000] [id=43267]
  0x000000010526e970 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000016bdb4000,0x000000016bfb7000] [id=13571]
  0x000000011f6ec230 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000016bfc0000,0x000000016c1c3000] [id=13059]
  0x000000010540d170 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000000170670000,0x0000000170873000] [id=40195]
  0x000000011f6ee360 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000016c1cc000,0x000000016c3cf000] [id=21507]
  0x000000010f5b2a70 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000017522c000,0x000000017542f000] [id=56835]
  0x000000010f4de3a0 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000000175438000,0x000000017563b000] [id=49667]
  0x000000011f6eec00 ConcurrentGCThread "G1 Service" [stack: 0x000000016c3d8000,0x000000016c5db000] [id=21251]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000006e0000000, size: 4608 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000007000000000-0x0000007000be8000-0x0000007000be8000), size 12484608, SharedBaseAddress: 0x0000007000000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000007001000000-0x0000007041000000, reserved size: 1073741824
Narrow klass base: 0x0000007000000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 11 total, 11 available
 Memory: 18432M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 288M
 Heap Max Capacity: 4608M
 Pre-touch: Disabled
 Parallel Workers: 9
 Concurrent Workers: 2
 Concurrent Refinement Workers: 9
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 311296K, used 79332K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 4 survivors (16384K)
 Metaspace       used 80084K, committed 80704K, reserved 1179648K
  class space    used 10525K, committed 10816K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000006e0000000, 0x00000006e0400000, 0x00000006e0400000|100%| O|  |TAMS 0x00000006e0400000, 0x00000006e0000000| Untracked 
|   1|0x00000006e0400000, 0x00000006e0800000, 0x00000006e0800000|100%| O|  |TAMS 0x00000006e0800000, 0x00000006e0400000| Untracked 
|   2|0x00000006e0800000, 0x00000006e0c00000, 0x00000006e0c00000|100%| O|  |TAMS 0x00000006e0c00000, 0x00000006e0800000| Untracked 
|   3|0x00000006e0c00000, 0x00000006e1000000, 0x00000006e1000000|100%| O|  |TAMS 0x00000006e1000000, 0x00000006e0c00000| Untracked 
|   4|0x00000006e1000000, 0x00000006e13b6400, 0x00000006e1400000| 92%| O|  |TAMS 0x00000006e13b6400, 0x00000006e1000000| Untracked 
|   5|0x00000006e1400000, 0x00000006e1800000, 0x00000006e1800000|100%| O|  |TAMS 0x00000006e1800000, 0x00000006e1400000| Untracked 
|   6|0x00000006e1800000, 0x00000006e1c00000, 0x00000006e1c00000|100%| O|  |TAMS 0x00000006e1c00000, 0x00000006e1800000| Untracked 
|   7|0x00000006e1c00000, 0x00000006e1c00000, 0x00000006e2000000|  0%| F|  |TAMS 0x00000006e1c00000, 0x00000006e1c00000| Untracked 
|   8|0x00000006e2000000, 0x00000006e2400000, 0x00000006e2400000|100%| O|  |TAMS 0x00000006e2000000, 0x00000006e2000000| Untracked 
|   9|0x00000006e2400000, 0x00000006e2800000, 0x00000006e2800000|100%| O|  |TAMS 0x00000006e2400000, 0x00000006e2400000| Untracked 
|  10|0x00000006e2800000, 0x00000006e2b62a00, 0x00000006e2c00000| 84%| O|  |TAMS 0x00000006e2800000, 0x00000006e2800000| Untracked 
|  11|0x00000006e2c00000, 0x00000006e2c00000, 0x00000006e3000000|  0%| F|  |TAMS 0x00000006e2c00000, 0x00000006e2c00000| Untracked 
|  12|0x00000006e3000000, 0x00000006e3000000, 0x00000006e3400000|  0%| F|  |TAMS 0x00000006e3000000, 0x00000006e3000000| Untracked 
|  13|0x00000006e3400000, 0x00000006e3400000, 0x00000006e3800000|  0%| F|  |TAMS 0x00000006e3400000, 0x00000006e3400000| Untracked 
|  14|0x00000006e3800000, 0x00000006e3800000, 0x00000006e3c00000|  0%| F|  |TAMS 0x00000006e3800000, 0x00000006e3800000| Untracked 
|  15|0x00000006e3c00000, 0x00000006e3c00000, 0x00000006e4000000|  0%| F|  |TAMS 0x00000006e3c00000, 0x00000006e3c00000| Untracked 
|  16|0x00000006e4000000, 0x00000006e4000000, 0x00000006e4400000|  0%| F|  |TAMS 0x00000006e4000000, 0x00000006e4000000| Untracked 
|  17|0x00000006e4400000, 0x00000006e4400000, 0x00000006e4800000|  0%| F|  |TAMS 0x00000006e4400000, 0x00000006e4400000| Untracked 
|  18|0x00000006e4800000, 0x00000006e4800000, 0x00000006e4c00000|  0%| F|  |TAMS 0x00000006e4800000, 0x00000006e4800000| Untracked 
|  19|0x00000006e4c00000, 0x00000006e4c00000, 0x00000006e5000000|  0%| F|  |TAMS 0x00000006e4c00000, 0x00000006e4c00000| Untracked 
|  20|0x00000006e5000000, 0x00000006e5000000, 0x00000006e5400000|  0%| F|  |TAMS 0x00000006e5000000, 0x00000006e5000000| Untracked 
|  21|0x00000006e5400000, 0x00000006e5400000, 0x00000006e5800000|  0%| F|  |TAMS 0x00000006e5400000, 0x00000006e5400000| Untracked 
|  22|0x00000006e5800000, 0x00000006e5800000, 0x00000006e5c00000|  0%| F|  |TAMS 0x00000006e5800000, 0x00000006e5800000| Untracked 
|  23|0x00000006e5c00000, 0x00000006e5c00000, 0x00000006e6000000|  0%| F|  |TAMS 0x00000006e5c00000, 0x00000006e5c00000| Untracked 
|  24|0x00000006e6000000, 0x00000006e6000000, 0x00000006e6400000|  0%| F|  |TAMS 0x00000006e6000000, 0x00000006e6000000| Untracked 
|  25|0x00000006e6400000, 0x00000006e6400000, 0x00000006e6800000|  0%| F|  |TAMS 0x00000006e6400000, 0x00000006e6400000| Untracked 
|  26|0x00000006e6800000, 0x00000006e6a683e0, 0x00000006e6c00000| 60%| S|CS|TAMS 0x00000006e6800000, 0x00000006e6800000| Complete 
|  27|0x00000006e6c00000, 0x00000006e7000000, 0x00000006e7000000|100%| S|CS|TAMS 0x00000006e6c00000, 0x00000006e6c00000| Complete 
|  28|0x00000006e7000000, 0x00000006e7400000, 0x00000006e7400000|100%| S|CS|TAMS 0x00000006e7000000, 0x00000006e7000000| Complete 
|  29|0x00000006e7400000, 0x00000006e7800000, 0x00000006e7800000|100%| S|CS|TAMS 0x00000006e7400000, 0x00000006e7400000| Complete 
|  30|0x00000006e7800000, 0x00000006e7800000, 0x00000006e7c00000|  0%| F|  |TAMS 0x00000006e7800000, 0x00000006e7800000| Untracked 
|  31|0x00000006e7c00000, 0x00000006e7c00000, 0x00000006e8000000|  0%| F|  |TAMS 0x00000006e7c00000, 0x00000006e7c00000| Untracked 
|  32|0x00000006e8000000, 0x00000006e8000000, 0x00000006e8400000|  0%| F|  |TAMS 0x00000006e8000000, 0x00000006e8000000| Untracked 
|  33|0x00000006e8400000, 0x00000006e8400000, 0x00000006e8800000|  0%| F|  |TAMS 0x00000006e8400000, 0x00000006e8400000| Untracked 
|  34|0x00000006e8800000, 0x00000006e8800000, 0x00000006e8c00000|  0%| F|  |TAMS 0x00000006e8800000, 0x00000006e8800000| Untracked 
|  35|0x00000006e8c00000, 0x00000006e8c00000, 0x00000006e9000000|  0%| F|  |TAMS 0x00000006e8c00000, 0x00000006e8c00000| Untracked 
|  36|0x00000006e9000000, 0x00000006e9000000, 0x00000006e9400000|  0%| F|  |TAMS 0x00000006e9000000, 0x00000006e9000000| Untracked 
|  37|0x00000006e9400000, 0x00000006e9400000, 0x00000006e9800000|  0%| F|  |TAMS 0x00000006e9400000, 0x00000006e9400000| Untracked 
|  38|0x00000006e9800000, 0x00000006e9800000, 0x00000006e9c00000|  0%| F|  |TAMS 0x00000006e9800000, 0x00000006e9800000| Untracked 
|  39|0x00000006e9c00000, 0x00000006e9c00000, 0x00000006ea000000|  0%| F|  |TAMS 0x00000006e9c00000, 0x00000006e9c00000| Untracked 
|  40|0x00000006ea000000, 0x00000006ea000000, 0x00000006ea400000|  0%| F|  |TAMS 0x00000006ea000000, 0x00000006ea000000| Untracked 
|  41|0x00000006ea400000, 0x00000006ea400000, 0x00000006ea800000|  0%| F|  |TAMS 0x00000006ea400000, 0x00000006ea400000| Untracked 
|  42|0x00000006ea800000, 0x00000006ea800000, 0x00000006eac00000|  0%| F|  |TAMS 0x00000006ea800000, 0x00000006ea800000| Untracked 
|  43|0x00000006eac00000, 0x00000006eac00000, 0x00000006eb000000|  0%| F|  |TAMS 0x00000006eac00000, 0x00000006eac00000| Untracked 
|  44|0x00000006eb000000, 0x00000006eb000000, 0x00000006eb400000|  0%| F|  |TAMS 0x00000006eb000000, 0x00000006eb000000| Untracked 
|  45|0x00000006eb400000, 0x00000006eb400000, 0x00000006eb800000|  0%| F|  |TAMS 0x00000006eb400000, 0x00000006eb400000| Untracked 
|  46|0x00000006eb800000, 0x00000006eb800000, 0x00000006ebc00000|  0%| F|  |TAMS 0x00000006eb800000, 0x00000006eb800000| Untracked 
|  47|0x00000006ebc00000, 0x00000006ebc00000, 0x00000006ec000000|  0%| F|  |TAMS 0x00000006ebc00000, 0x00000006ebc00000| Untracked 
|  48|0x00000006ec000000, 0x00000006ec000000, 0x00000006ec400000|  0%| F|  |TAMS 0x00000006ec000000, 0x00000006ec000000| Untracked 
|  49|0x00000006ec400000, 0x00000006ec400000, 0x00000006ec800000|  0%| F|  |TAMS 0x00000006ec400000, 0x00000006ec400000| Untracked 
|  50|0x00000006ec800000, 0x00000006ec800000, 0x00000006ecc00000|  0%| F|  |TAMS 0x00000006ec800000, 0x00000006ec800000| Untracked 
|  51|0x00000006ecc00000, 0x00000006ecc00000, 0x00000006ed000000|  0%| F|  |TAMS 0x00000006ecc00000, 0x00000006ecc00000| Untracked 
|  52|0x00000006ed000000, 0x00000006ed000000, 0x00000006ed400000|  0%| F|  |TAMS 0x00000006ed000000, 0x00000006ed000000| Untracked 
|  53|0x00000006ed400000, 0x00000006ed400000, 0x00000006ed800000|  0%| F|  |TAMS 0x00000006ed400000, 0x00000006ed400000| Untracked 
|  54|0x00000006ed800000, 0x00000006ed800000, 0x00000006edc00000|  0%| F|  |TAMS 0x00000006ed800000, 0x00000006ed800000| Untracked 
|  55|0x00000006edc00000, 0x00000006edc00000, 0x00000006ee000000|  0%| F|  |TAMS 0x00000006edc00000, 0x00000006edc00000| Untracked 
|  56|0x00000006ee000000, 0x00000006ee000000, 0x00000006ee400000|  0%| F|  |TAMS 0x00000006ee000000, 0x00000006ee000000| Untracked 
|  57|0x00000006ee400000, 0x00000006ee400000, 0x00000006ee800000|  0%| F|  |TAMS 0x00000006ee400000, 0x00000006ee400000| Untracked 
|  58|0x00000006ee800000, 0x00000006ee800000, 0x00000006eec00000|  0%| F|  |TAMS 0x00000006ee800000, 0x00000006ee800000| Untracked 
|  59|0x00000006eec00000, 0x00000006eec00000, 0x00000006ef000000|  0%| F|  |TAMS 0x00000006eec00000, 0x00000006eec00000| Untracked 
|  60|0x00000006ef000000, 0x00000006ef000000, 0x00000006ef400000|  0%| F|  |TAMS 0x00000006ef000000, 0x00000006ef000000| Untracked 
|  61|0x00000006ef400000, 0x00000006ef400000, 0x00000006ef800000|  0%| F|  |TAMS 0x00000006ef400000, 0x00000006ef400000| Untracked 
|  62|0x00000006ef800000, 0x00000006ef800000, 0x00000006efc00000|  0%| F|  |TAMS 0x00000006ef800000, 0x00000006ef800000| Untracked 
|  63|0x00000006efc00000, 0x00000006efc00000, 0x00000006f0000000|  0%| F|  |TAMS 0x00000006efc00000, 0x00000006efc00000| Untracked 
|  64|0x00000006f0000000, 0x00000006f0000000, 0x00000006f0400000|  0%| F|  |TAMS 0x00000006f0000000, 0x00000006f0000000| Untracked 
|  65|0x00000006f0400000, 0x00000006f0400000, 0x00000006f0800000|  0%| F|  |TAMS 0x00000006f0400000, 0x00000006f0400000| Untracked 
|  66|0x00000006f0800000, 0x00000006f0800000, 0x00000006f0c00000|  0%| F|  |TAMS 0x00000006f0800000, 0x00000006f0800000| Untracked 
|  67|0x00000006f0c00000, 0x00000006f0c00000, 0x00000006f1000000|  0%| F|  |TAMS 0x00000006f0c00000, 0x00000006f0c00000| Untracked 
|  68|0x00000006f1000000, 0x00000006f12e5f40, 0x00000006f1400000| 72%| E|  |TAMS 0x00000006f1000000, 0x00000006f1000000| Complete 
|  69|0x00000006f1400000, 0x00000006f1800000, 0x00000006f1800000|100%| E|CS|TAMS 0x00000006f1400000, 0x00000006f1400000| Complete 
|  70|0x00000006f1800000, 0x00000006f1c00000, 0x00000006f1c00000|100%| E|CS|TAMS 0x00000006f1800000, 0x00000006f1800000| Complete 
|  71|0x00000006f1c00000, 0x00000006f2000000, 0x00000006f2000000|100%| E|CS|TAMS 0x00000006f1c00000, 0x00000006f1c00000| Complete 
|  72|0x00000006f2000000, 0x00000006f2400000, 0x00000006f2400000|100%| E|CS|TAMS 0x00000006f2000000, 0x00000006f2000000| Complete 
|  73|0x00000006f2400000, 0x00000006f2800000, 0x00000006f2800000|100%| E|CS|TAMS 0x00000006f2400000, 0x00000006f2400000| Complete 
|1150|0x00000007ff800000, 0x00000007ffb78000, 0x00000007ffc00000| 86%|OA|  |TAMS 0x00000007ffb78000, 0x00000007ff800000| Untracked 
|1151|0x00000007ffc00000, 0x00000007ffc80000, 0x0000000800000000| 12%|CA|  |TAMS 0x00000007ffc80000, 0x00000007ffc00000| Untracked 

Card table byte_map: [0x000000011b900000,0x000000011c200000] _byte_map_base: 0x0000000118200000

Marking Bits (Prev, Next): (CMBitMap*) 0x000000010e152a10, (CMBitMap*) 0x000000010e152a50
 Prev Bits: [0x0000000120800000, 0x0000000125000000)
 Next Bits: [0x0000000148000000, 0x000000014c800000)

Polling page: 0x000000010457c000

Metaspace:

Usage:
  Non-class:     67.93 MB used.
      Class:     10.28 MB used.
       Both:     78.21 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      68.25 MB ( 53%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      10.56 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      78.81 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  11.67 MB
       Class:  5.25 MB
        Both:  16.92 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 104.81 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 15.
num_arena_births: 878.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1261.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 15.
num_chunks_taken_from_freelist: 3591.
num_chunk_merges: 15.
num_chunk_splits: 2586.
num_chunks_enlarged: 1986.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=17238Kb max_used=17238Kb free=31913Kb
 bounds [0x0000000118000000, 0x00000001190e0000, 0x000000011b000000]
 total_blobs=8038 nmethods=7335 adapters=633
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 4.616 Thread 0x0000000127854200 7771       1       sun.reflect.generics.parser.SignatureParser::parseBounds (112 bytes)
Event: 4.616 Thread 0x000000011f8cb200 7770       1       sun.reflect.generics.parser.SignatureParser::parseFormalTypeParameter (16 bytes)
Event: 4.616 Thread 0x000000011f8cb200 nmethod 7770 0x00000001190d0490 code [0x00000001190d0640, 0x00000001190d08b8]
Event: 4.616 Thread 0x000000011f8cb200 7776       1       org.aspectj.weaver.ReferenceType::equal (45 bytes)
Event: 4.617 Thread 0x000000011f8cb200 nmethod 7776 0x00000001190d0b90 code [0x00000001190d0d40, 0x00000001190d0f38]
Event: 4.617 Thread 0x0000000127854200 nmethod 7771 0x00000001190d1190 code [0x00000001190d1400, 0x00000001190d1a98]
Event: 4.617 Thread 0x000000011f8cb200 7777       1       org.aspectj.weaver.TypeVariableReferenceType::equals (29 bytes)
Event: 4.617 Thread 0x0000000127854200 7772       1       sun.reflect.generics.tree.FormalTypeParameter::make (10 bytes)
Event: 4.617 Thread 0x000000011f8cb200 nmethod 7777 0x00000001190d2290 code [0x00000001190d2440, 0x00000001190d25d8]
Event: 4.617 Thread 0x0000000127854200 nmethod 7772 0x00000001190d2710 code [0x00000001190d28c0, 0x00000001190d2a18]
Event: 4.617 Thread 0x000000011f8cb200 7773       1       sun.reflect.generics.tree.FormalTypeParameter::<init> (15 bytes)
Event: 4.617 Thread 0x0000000127854200 7774       1       sun.reflect.generics.reflectiveObjects.TypeVariableImpl::make (66 bytes)
Event: 4.617 Thread 0x000000011f8cb200 nmethod 7773 0x00000001190d2b10 code [0x00000001190d2cc0, 0x00000001190d2e18]
Event: 4.617 Thread 0x000000011f8cb200 7775       1       sun.reflect.generics.reflectiveObjects.TypeVariableImpl::<init> (22 bytes)
Event: 4.617 Thread 0x000000011f8cb200 nmethod 7775 0x00000001190d2f10 code [0x00000001190d30c0, 0x00000001190d32d8]
Event: 4.617 Thread 0x0000000127854200 nmethod 7774 0x00000001190d3410 code [0x00000001190d3640, 0x00000001190d3c38]
Event: 4.617 Thread 0x0000000127854200 7778       1       org.aspectj.weaver.ResolvedType::<init> (30 bytes)
Event: 4.617 Thread 0x0000000127854200 nmethod 7778 0x00000001190d4310 code [0x00000001190d44c0, 0x00000001190d4818]
Event: 4.617 Thread 0x0000000127854200 7779       1       java.nio.HeapByteBuffer::getInt (29 bytes)
Event: 4.617 Thread 0x0000000127854200 nmethod 7779 0x00000001190d4a10 code [0x00000001190d4c00, 0x00000001190d4f38]

GC Heap History (20 events):
Event: 2.435 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total 122880K, used 87254K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 16 young (65536K), 3 survivors (12288K)
 Metaspace       used 47211K, committed 47552K, reserved 1114112K
  class space    used 6311K, committed 6464K, reserved 1048576K
}
Event: 2.442 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total 122880K, used 36751K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 47211K, committed 47552K, reserved 1114112K
  class space    used 6311K, committed 6464K, reserved 1048576K
}
Event: 2.837 GC heap before
{Heap before GC invocations=12 (full 0):
 garbage-first heap   total 122880K, used 89999K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 15 young (61440K), 2 survivors (8192K)
 Metaspace       used 53515K, committed 54016K, reserved 1114112K
  class space    used 7167K, committed 7424K, reserved 1048576K
}
Event: 2.845 GC heap after
{Heap after GC invocations=13 (full 0):
 garbage-first heap   total 122880K, used 39392K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 53515K, committed 54016K, reserved 1114112K
  class space    used 7167K, committed 7424K, reserved 1048576K
}
Event: 3.157 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 122880K, used 88544K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 57156K, committed 57664K, reserved 1114112K
  class space    used 7786K, committed 8000K, reserved 1048576K
}
Event: 3.166 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 147456K, used 41409K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 57156K, committed 57664K, reserved 1114112K
  class space    used 7786K, committed 8000K, reserved 1048576K
}
Event: 3.331 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 147456K, used 82369K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 2 survivors (8192K)
 Metaspace       used 59949K, committed 60416K, reserved 1114112K
  class space    used 8124K, committed 8384K, reserved 1048576K
}
Event: 3.334 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 147456K, used 43226K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 59949K, committed 60416K, reserved 1114112K
  class space    used 8124K, committed 8384K, reserved 1048576K
}
Event: 3.443 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 147456K, used 112858K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 19 young (77824K), 2 survivors (8192K)
 Metaspace       used 63819K, committed 64384K, reserved 1114112K
  class space    used 8535K, committed 8832K, reserved 1048576K
}
Event: 3.445 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 147456K, used 45379K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 63819K, committed 64384K, reserved 1114112K
  class space    used 8535K, committed 8832K, reserved 1048576K
}
Event: 3.479 GC heap before
{Heap before GC invocations=18 (full 0):
 garbage-first heap   total 147456K, used 110915K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 18 young (73728K), 2 survivors (8192K)
 Metaspace       used 63847K, committed 64384K, reserved 1114112K
  class space    used 8537K, committed 8832K, reserved 1048576K
}
Event: 3.481 GC heap after
{Heap after GC invocations=19 (full 0):
 garbage-first heap   total 147456K, used 46025K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 63847K, committed 64384K, reserved 1114112K
  class space    used 8537K, committed 8832K, reserved 1048576K
}
Event: 3.484 GC heap before
{Heap before GC invocations=19 (full 0):
 garbage-first heap   total 147456K, used 50121K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 2 survivors (8192K)
 Metaspace       used 63847K, committed 64384K, reserved 1114112K
  class space    used 8537K, committed 8832K, reserved 1048576K
}
Event: 3.486 GC heap after
{Heap after GC invocations=20 (full 0):
 garbage-first heap   total 147456K, used 45519K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 63847K, committed 64384K, reserved 1114112K
  class space    used 8537K, committed 8832K, reserved 1048576K
}
Event: 3.514 GC heap before
{Heap before GC invocations=20 (full 0):
 garbage-first heap   total 147456K, used 111055K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 17 young (69632K), 1 survivors (4096K)
 Metaspace       used 63847K, committed 64384K, reserved 1114112K
  class space    used 8537K, committed 8832K, reserved 1048576K
}
Event: 3.515 GC heap after
{Heap after GC invocations=21 (full 0):
 garbage-first heap   total 147456K, used 45948K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 63847K, committed 64384K, reserved 1114112K
  class space    used 8537K, committed 8832K, reserved 1048576K
}
Event: 3.658 GC heap before
{Heap before GC invocations=21 (full 0):
 garbage-first heap   total 147456K, used 115580K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 18 young (73728K), 1 survivors (4096K)
 Metaspace       used 65480K, committed 65984K, reserved 1114112K
  class space    used 8749K, committed 9024K, reserved 1048576K
}
Event: 3.659 GC heap after
{Heap after GC invocations=22 (full 0):
 garbage-first heap   total 311296K, used 47743K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 65480K, committed 65984K, reserved 1114112K
  class space    used 8749K, committed 9024K, reserved 1048576K
}
Event: 4.499 GC heap before
{Heap before GC invocations=22 (full 0):
 garbage-first heap   total 311296K, used 227967K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 45 young (184320K), 1 survivors (4096K)
 Metaspace       used 78470K, committed 79040K, reserved 1179648K
  class space    used 10326K, committed 10624K, reserved 1048576K
}
Event: 4.506 GC heap after
{Heap after GC invocations=23 (full 0):
 garbage-first heap   total 311296K, used 58852K [0x00000006e0000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 78470K, committed 79040K, reserved 1179648K
  class space    used 10326K, committed 10624K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 4.613 Thread 0x000000014c852c00 DEOPT PACKING pc=0x0000000118afda08 sp=0x0000000173579710
Event: 4.613 Thread 0x000000014c852c00 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x0000000173579480 mode 1
Event: 4.613 Thread 0x000000014c852c00 DEOPT PACKING pc=0x0000000118afda08 sp=0x0000000173579710
Event: 4.613 Thread 0x000000014c852c00 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x0000000173579480 mode 1
Event: 4.613 Thread 0x000000014c852c00 DEOPT PACKING pc=0x0000000118afda08 sp=0x0000000173579710
Event: 4.613 Thread 0x000000014c852c00 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x0000000173579480 mode 1
Event: 4.613 Thread 0x000000014c852c00 DEOPT PACKING pc=0x0000000118afda08 sp=0x0000000173579710
Event: 4.613 Thread 0x000000014c852c00 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x0000000173579480 mode 1
Event: 4.613 Thread 0x000000014c852c00 DEOPT PACKING pc=0x0000000118afda08 sp=0x0000000173579710
Event: 4.613 Thread 0x000000014c852c00 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x0000000173579480 mode 1
Event: 4.613 Thread 0x000000014c852c00 DEOPT PACKING pc=0x0000000118afda08 sp=0x0000000173579710
Event: 4.613 Thread 0x000000014c852c00 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x0000000173579480 mode 1
Event: 4.616 Thread 0x0000000153df1600 DEOPT PACKING pc=0x00000001190c6c60 sp=0x0000000176075890
Event: 4.616 Thread 0x0000000153df1600 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x0000000176075630 mode 1
Event: 4.616 Thread 0x0000000153df1600 DEOPT PACKING pc=0x00000001190c6c60 sp=0x0000000176075890
Event: 4.616 Thread 0x0000000153df1600 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x0000000176075630 mode 1
Event: 4.616 Thread 0x0000000153df1600 DEOPT PACKING pc=0x0000000118157f08 sp=0x0000000176075bc0
Event: 4.616 Thread 0x0000000153df1600 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x00000001760758d0 mode 1
Event: 4.617 Thread 0x0000000153df1600 DEOPT PACKING pc=0x0000000118daffc0 sp=0x0000000176076910
Event: 4.617 Thread 0x0000000153df1600 DEOPT UNPACKING pc=0x000000011803eb7c sp=0x00000001760765d0 mode 1

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 0.576 Thread 0x000000010526f200 redefined class name=java.lang.Throwable, count=1

Internal exceptions (20 events):
Event: 3.208 Thread 0x000000010e14fc00 Exception <a 'java/io/FileNotFoundException'{0x00000006e7e419a0}> (0x00000006e7e419a0) 
thrown [open/src/hotspot/share/prims/jni.cpp, line 516]
Event: 3.214 Thread 0x000000010e14fc00 Exception <a 'java/lang/NoSuchMethodError'{0x00000006e7fa6be0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006e7fa6be0) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 3.251 Thread 0x000000010e14fc00 Exception <a 'java/lang/NoSuchMethodError'{0x00000006e76de600}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006e76de600) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 3.530 Thread 0x000000010e14fc00 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000006e62c3f20}: Found class java.lang.Object, but interface was expected> (0x00000006e62c3f20) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 826]
Event: 3.598 Thread 0x000000014c852c00 Exception <a 'java/lang/reflect/InvocationTargetException'{0x00000006e52b3a48}> (0x00000006e52b3a48) 
thrown [open/src/hotspot/share/runtime/reflection.cpp, line 1121]
Event: 3.714 Thread 0x000000010e14fc00 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f1c85d30}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long)'> (0x00000006f1c85d30) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 3.761 Thread 0x000000010e14fc00 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000006f171b1d0}: Found class java.lang.Object, but interface was expected> (0x00000006f171b1d0) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 826]
Event: 3.765 Thread 0x000000010e14fc00 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000006f17f8ff8}: Found class java.lang.Object, but interface was expected> (0x00000006f17f8ff8) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 826]
Event: 3.773 Thread 0x000000010e14fc00 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000006f1172f60}: Found class java.lang.Object, but interface was expected> (0x00000006f1172f60) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 826]
Event: 3.881 Thread 0x000000010e14fc00 Exception <a 'java/lang/ClassNotFoundException'{0x00000006efa74640}: sun/awt/resources/spi/awtProvider> (0x00000006efa74640) 
thrown [open/src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 3.890 Thread 0x000000010e14fc00 Exception <a 'java/io/FileNotFoundException'{0x00000006efa893e8}> (0x00000006efa893e8) 
thrown [open/src/hotspot/share/prims/jni.cpp, line 516]
Event: 3.890 Thread 0x000000010e14fc00 Exception <a 'java/io/FileNotFoundException'{0x00000006efa8adf0}> (0x00000006efa8adf0) 
thrown [open/src/hotspot/share/prims/jni.cpp, line 516]
Event: 4.044 Thread 0x000000010e14fc00 Exception <a 'java/lang/reflect/InvocationTargetException'{0x00000006ee4cb370}> (0x00000006ee4cb370) 
thrown [open/src/hotspot/share/runtime/reflection.cpp, line 1121]
Event: 4.070 Thread 0x000000010e14fc00 Exception <a 'java/lang/NoSuchMethodError'{0x00000006ee1d29b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000006ee1d29b8) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 4.106 Thread 0x000000014c852c00 Exception <a 'java/lang/reflect/InvocationTargetException'{0x00000006ed4097a0}> (0x00000006ed4097a0) 
thrown [open/src/hotspot/share/runtime/reflection.cpp, line 1121]
Event: 4.121 Thread 0x000000010e14fc00 Exception <a 'java/lang/NoSuchMethodError'{0x00000006ed03bab0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006ed03bab0) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 4.146 Thread 0x000000010e14fc00 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000006ece18b18}: Found class java.lang.Object, but interface was expected> (0x00000006ece18b18) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 826]
Event: 4.189 Thread 0x000000010e14fc00 Exception <a 'java/lang/reflect/InvocationTargetException'{0x00000006ec1099e8}> (0x00000006ec1099e8) 
thrown [open/src/hotspot/share/runtime/reflection.cpp, line 1121]
Event: 4.310 Thread 0x0000000153df1600 Exception <a 'java/lang/NoSuchMethodError'{0x00000006ea4c9c78}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006ea4c9c78) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]
Event: 4.566 Thread 0x000000010e14fc00 Exception <a 'java/lang/NoSuchMethodError'{0x00000006f1cd2070}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006f1cd2070) 
thrown [open/src/hotspot/share/interpreter/linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 3.990 Executing VM operation: HandshakeAllThreads
Event: 3.990 Executing VM operation: HandshakeAllThreads done
Event: 4.023 Executing VM operation: ICBufferFull
Event: 4.023 Executing VM operation: ICBufferFull done
Event: 4.067 Executing VM operation: HandshakeAllThreads
Event: 4.067 Executing VM operation: HandshakeAllThreads done
Event: 4.128 Executing VM operation: ICBufferFull
Event: 4.128 Executing VM operation: ICBufferFull done
Event: 4.158 Executing VM operation: HandshakeAllThreads
Event: 4.158 Executing VM operation: HandshakeAllThreads done
Event: 4.297 Executing VM operation: ICBufferFull
Event: 4.298 Executing VM operation: ICBufferFull done
Event: 4.303 Executing VM operation: HandshakeAllThreads
Event: 4.303 Executing VM operation: HandshakeAllThreads done
Event: 4.304 Executing VM operation: HandshakeAllThreads
Event: 4.304 Executing VM operation: HandshakeAllThreads done
Event: 4.367 Executing VM operation: HandshakeAllThreads
Event: 4.367 Executing VM operation: HandshakeAllThreads done
Event: 4.499 Executing VM operation: G1CollectForAllocation
Event: 4.506 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 4.616 loading class sun/security/validator/ValidatorException done
Event: 4.616 loading class sun/security/ssl/Alert
Event: 4.616 loading class sun/security/ssl/Alert done
Event: 4.616 loading class sun/security/ssl/Alert$AlertConsumer
Event: 4.616 loading class sun/security/ssl/Alert$AlertConsumer done
Event: 4.616 loading class javax/net/ssl/SSLHandshakeException
Event: 4.616 loading class javax/net/ssl/SSLHandshakeException done
Event: 4.617 loading class java/util/concurrent/ConcurrentLinkedQueue$Itr
Event: 4.617 loading class java/util/concurrent/ConcurrentLinkedQueue$Itr done
Event: 4.617 loading class sun/security/ssl/Alert$Level
Event: 4.617 loading class sun/security/ssl/Alert$Level done
Event: 4.617 loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt
Event: 4.617 loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt done
Event: 4.617 loading class com/sun/crypto/provider/GaloisCounterMode$GCTRGHASH
Event: 4.617 loading class com/sun/crypto/provider/GaloisCounterMode$GCTRGHASH done
Event: 4.617 loading class java/nio/BufferOverflowException
Event: 4.617 loading class java/nio/BufferOverflowException done
Event: 4.617 loading class java/lang/Throwable$WrappedPrintStream
Event: 4.617 loading class java/lang/Throwable$WrappedPrintStream done
Event: 4.617 loading class com/intellij/rt/debugger/agent/CaptureStorage$8


Dynamic libraries:
0x0000000104528000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libjli.dylib
0x000000019dbb2000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x0000000185379000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x0000000188668000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x0000000182a01000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000018f5e5000 	/usr/lib/libSystem.B.dylib
0x000000018680b000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000022f91b000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x0000000196bd8000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x000000018d179000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x0000000191daf000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x0000000192106000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000025e38b000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00000001e8301000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x0000000263404000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000026244c000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x0000000182665000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x0000000191213000 	/usr/lib/libspindump.dylib
0x00000001869bd000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000018e9d7000 	/usr/lib/libbsm.0.dylib
0x000000018abfc000 	/usr/lib/libapp_launch_measurement.dylib
0x0000000189fa5000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000018ac00000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x000000018c793000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x000000018d9b7000 	/usr/lib/liblangid.dylib
0x000000018d17f000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x00000001873e3000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x0000000187909000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00000001972b7000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x0000000191056000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000018c770000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x0000000189fd6000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000018f52f000 	/usr/lib/libz.1.dylib
0x000000019b1ca000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000018d164000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x0000000184bde000 	/usr/lib/libicucore.A.dylib
0x000000019318d000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00000001920b7000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001ae121000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x000000018732e000 	/usr/lib/libMobileGestalt.dylib
0x000000018ce5d000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x000000018a4dd000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x00000001847d3000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x0000000196c14000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x000000018a903000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000018409e000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000018a0c4000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000019167c000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000018732c000 	/usr/lib/libenergytrace.dylib
0x00000001a24c7000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x0000000185229000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000019700b000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000018ab8d000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001e1cd1000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000018ac4a000 	/usr/lib/libxml2.2.dylib
0x000000018e8bb000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x0000000180fa8000 	/usr/lib/libobjc.A.dylib
0x00000001812b5000 	/usr/lib/libc++.1.dylib
0x0000000196f8c000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x0000000188039000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x0000000181411000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x000000018d539000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x0000000183e7f000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001e3216000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001e379a000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00000001e379d000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000018d1ba000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001e8ec4000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001d54e0000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000018f5ea000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001c0df4000 	/usr/lib/swift/libswiftAccelerate.dylib
0x0000000192b22000 	/usr/lib/swift/libswiftCore.dylib
0x00000001aa7ca000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001aa824000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001a7bde000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000026a019000 	/usr/lib/swift/libswiftDataDetection.dylib
0x0000000198b03000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001aa825000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001b737b000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001c6351000 	/usr/lib/swift/libswiftOSLog.dylib
0x000000019b770000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x000000026a046000 	/usr/lib/swift/libswiftObservation.dylib
0x00000001bc7a2000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001c0de5000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001aa7dc000 	/usr/lib/swift/libswiftXPC.dylib
0x000000026a12c000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x000000026a12f000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000026a28e000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x000000026a321000 	/usr/lib/swift/libswift_errno.dylib
0x000000026a323000 	/usr/lib/swift/libswift_math.dylib
0x000000026a326000 	/usr/lib/swift/libswift_signal.dylib
0x000000026a327000 	/usr/lib/swift/libswift_stdio.dylib
0x000000026a328000 	/usr/lib/swift/libswift_time.dylib
0x000000019b774000 	/usr/lib/swift/libswiftos.dylib
0x00000001ae078000 	/usr/lib/swift/libswiftsimd.dylib
0x000000026a329000 	/usr/lib/swift/libswiftsys_time.dylib
0x000000026a32a000 	/usr/lib/swift/libswiftunistd.dylib
0x000000018f812000 	/usr/lib/libcompression.dylib
0x0000000191d0f000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x0000000190cf5000 	/usr/lib/libate.dylib
0x000000018f5df000 	/usr/lib/system/libcache.dylib
0x000000018f59a000 	/usr/lib/system/libcommonCrypto.dylib
0x000000018f5c5000 	/usr/lib/system/libcompiler_rt.dylib
0x000000018f5ba000 	/usr/lib/system/libcopyfile.dylib
0x0000000181103000 	/usr/lib/system/libcorecrypto.dylib
0x00000001811e9000 	/usr/lib/system/libdispatch.dylib
0x00000001813a9000 	/usr/lib/system/libdyld.dylib
0x000000018f5d5000 	/usr/lib/system/libkeymgr.dylib
0x000000018f57d000 	/usr/lib/system/libmacho.dylib
0x000000018e9b0000 	/usr/lib/system/libquarantine.dylib
0x000000018f5d2000 	/usr/lib/system/libremovefile.dylib
0x00000001873a8000 	/usr/lib/system/libsystem_asl.dylib
0x0000000181098000 	/usr/lib/system/libsystem_blocks.dylib
0x0000000181233000 	/usr/lib/system/libsystem_c.dylib
0x000000018f5c9000 	/usr/lib/system/libsystem_collections.dylib
0x000000018d9a4000 	/usr/lib/system/libsystem_configuration.dylib
0x000000018c73f000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000018f0c0000 	/usr/lib/system/libsystem_coreservices.dylib
0x0000000184eaa000 	/usr/lib/system/libsystem_darwin.dylib
0x000000026a461000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000018f5d6000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000026a465000 	/usr/lib/system/libsystem_eligibility.dylib
0x0000000181230000 	/usr/lib/system/libsystem_featureflags.dylib
0x00000001813e1000 	/usr/lib/system/libsystem_info.dylib
0x000000018f53e000 	/usr/lib/system/libsystem_m.dylib
0x00000001811a2000 	/usr/lib/system/libsystem_malloc.dylib
0x0000000187311000 	/usr/lib/system/libsystem_networkextension.dylib
0x000000018530c000 	/usr/lib/system/libsystem_notify.dylib
0x000000018d9a9000 	/usr/lib/system/libsystem_sandbox.dylib
0x000000026a46d000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000018f5ce000 	/usr/lib/system/libsystem_secinit.dylib
0x0000000181360000 	/usr/lib/system/libsystem_kernel.dylib
0x00000001813d9000 	/usr/lib/system/libsystem_platform.dylib
0x000000018139c000 	/usr/lib/system/libsystem_pthread.dylib
0x0000000188f02000 	/usr/lib/system/libsystem_symptoms.dylib
0x00000001810e7000 	/usr/lib/system/libsystem_trace.dylib
0x000000018f5a8000 	/usr/lib/system/libunwind.dylib
0x000000018109c000 	/usr/lib/system/libxpc.dylib
0x0000000181342000 	/usr/lib/libc++abi.dylib
0x0000000269039000 	/usr/lib/libRosetta.dylib
0x00000001851a8000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000018f5b2000 	/usr/lib/liboah.dylib
0x000000018f5e7000 	/usr/lib/libfakelink.dylib
0x000000019ac7d000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00000001a7707000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x0000000186f46000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000018abc4000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x0000000184eb5000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000018a039000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000018f0c7000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000018f734000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x0000000188e7d000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x0000000181950000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x0000000190b4a000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000018abd2000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000018f7c9000 	/usr/lib/libapple_nghttp2.dylib
0x0000000188ade000 	/usr/lib/libsqlite3.dylib
0x0000000193805000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x0000000188e14000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x0000000188f0b000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000018f581000 	/usr/lib/system/libkxld.dylib
0x000000022b4d5000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x0000000268e91000 	/usr/lib/libCoreEntitlements.dylib
0x0000000246911000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x0000000188ac3000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000018f0a7000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000018e9bf000 	/usr/lib/libcoretls.dylib
0x0000000190bc0000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000018f80c000 	/usr/lib/libpam.2.dylib
0x0000000190c34000 	/usr/lib/libxar.1.dylib
0x000000018f63c000 	/usr/lib/libarchive.2.dylib
0x0000000194f07000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000022f93f000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000024f938000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000025060a000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x000000026a0bd000 	/usr/lib/swift/libswiftSystem.dylib
0x000000018d9b2000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00000001aa6b5000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x0000000197fc4000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00000001e81c6000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x000000018add5000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x0000000186e74000 	/usr/lib/libboringssl.dylib
0x0000000188ef1000 	/usr/lib/libdns_services.dylib
0x00000001a9869000 	/usr/lib/libquic.dylib
0x0000000192ab1000 	/usr/lib/libusrtcp.dylib
0x00000001ce1bb000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x000000026a01a000 	/usr/lib/swift/libswiftDistributed.dylib
0x000000026a0b3000 	/usr/lib/swift/libswiftSynchronization.dylib
0x0000000186f45000 	/usr/lib/libnetwork.dylib
0x00000001bb3e1000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000018f7a4000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x0000000199299000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001c9e87000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x0000000199275000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x0000000268d5c000 	/usr/lib/libAppleArchive.dylib
0x000000018f0b3000 	/usr/lib/libbz2.1.0.dylib
0x0000000190ba1000 	/usr/lib/liblzma.5.dylib
0x000000018e6af000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000001aa73e000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x0000000190d8f000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000018245c000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000023ec61000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x0000000198d9b000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000018e8e7000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000018ea49000 	/usr/lib/libgermantok.dylib
0x000000018dad6000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x0000000188d2f000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x0000000198e68000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000018e8d8000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x000000019aab8000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000018cb8a000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x00000001873c0000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x000000019e653000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x000000019167a000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x00000001839a6000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x000000018ca2e000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x000000018c789000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000018ad34000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000018f80a000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000024ec89000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x00000001916c2000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x000000018d9b0000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x0000000190bc2000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x0000000190c43000 	/usr/lib/libutil.dylib
0x0000000259b48000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x000000018a0cd000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x0000000196fe6000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x0000000190c7b000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x0000000181dc8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x00000001933dc000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x00000001881ef000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x0000000191c98000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x00000001937cf000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x00000001937c6000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x00000001933ae000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000018e67c000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x0000000235d3a000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x00000001911c8000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x000000018a8b1000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x000000026941d000 	/usr/lib/libhvf.dylib
0x000000024ab97000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x000000026a063000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000026a1eb000 	/usr/lib/swift/libswift_RegexParser.dylib
0x0000000191543000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x0000000190fe4000 	/usr/lib/libexpat.1.dylib
0x0000000191b6e000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x0000000191b99000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x0000000191c83000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x0000000191589000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x0000000191c2a000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x0000000191c21000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x000000023aa08000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x0000000235e05000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00000001e1cc3000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x000000023697a000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x0000000235d3b000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x000000019c8b9000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x000000024ddf8000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x00000001911bc000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001e1d21000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001e1ce5000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001e1ead000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001e1cee000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001e1ce2000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000001e1ccb000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x000000018d8f4000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000018f012000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000018ea61000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000018ee98000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000018ecad000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000018eeca000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001e55b8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001e559a000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x0000000181c43000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001af41e000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001bcb97000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001aa7a5000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x0000000191c55000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000019375a000 	/usr/lib/libcups.2.dylib
0x00000001937f4000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000019345a000 	/usr/lib/libresolv.9.dylib
0x000000018f621000 	/usr/lib/libiconv.2.dylib
0x000000019121a000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x000000019b6c7000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x0000000190fff000 	/usr/lib/libheimdal-asn1.dylib
0x000000018ab97000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x0000000193857000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000018aba5000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000018f57c000 	/usr/lib/libcharset.1.dylib
0x00000001e2249000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001a3e8d000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x0000000242590000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x0000000193395000 	/usr/lib/libAudioStatistics.dylib
0x000000018ca06000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x0000000183ac7000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x000000019d9df000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x0000000193338000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x0000000194c6f000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x00000001910e9000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x000000025c603000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x00000002303fc000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x00000001c14b7000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00000001aa7a9000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x0000000191209000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x00000001937d8000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001a9962000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x0000000193679000 	/usr/lib/libSMC.dylib
0x0000000191b38000 	/usr/lib/libAudioToolboxUtility.dylib
0x00000001937e6000 	/usr/lib/libperfcheck.dylib
0x000000022c292000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00000001cdf82000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001aa770000 	/usr/lib/libmis.dylib
0x0000000193146000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x0000000191c89000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001e38ea000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00000001993a6000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x0000000190ee3000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x0000000197ecd000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x000000019b6c8000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000018e740000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x00000002333a0000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x0000000197307000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001b2dba000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x0000000268cda000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00000001a3f49000 	/usr/lib/libAccessibility.dylib
0x000000018e58a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000018f8e8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000018ea4c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000018f7e3000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000018f8e3000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000018dadd000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x0000000182569000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x0000000243d01000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x0000000191c1c000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x0000000191bfc000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x0000000191c24000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000001ce389000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00000001a3168000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000025fe3f000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x0000000190f9b000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x0000000198f15000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x00000001939ca000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00000001980b5000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x0000000198000000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x00000001937b9000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000018ad95000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x0000000191009000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x0000000190d86000 	/usr/lib/libIOReport.dylib
0x000000022bea2000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x000000026909c000 	/usr/lib/libTLE.dylib
0x00000001ded1f000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000018e9e9000 	/usr/lib/libmecab.dylib
0x00000001826f9000 	/usr/lib/libCRFSuite.dylib
0x000000018d9b9000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000018f79c000 	/usr/lib/libThaiTokenizer.dylib
0x000000018e9b3000 	/usr/lib/libCheckFix.dylib
0x0000000189fd8000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000023f543000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x00000001851e8000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001bcca1000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x0000000190c47000 	/usr/lib/libxslt.1.dylib
0x000000018e976000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x000000019ae3b000 	/usr/lib/libcurl.4.dylib
0x00000002692d0000 	/usr/lib/libcrypto.46.dylib
0x0000000269df8000 	/usr/lib/libssl.48.dylib
0x000000019ab14000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x000000019ab50000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x0000000193477000 	/usr/lib/libsasl2.2.dylib
0x00000001a7bdd000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x0000000197935000 	/usr/lib/swift/libswiftFoundation.dylib
0x00000001de7a9000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00000001ca63b000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x0000000250701000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x0000000196c01000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x00000001056cc000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/server/libjvm.dylib
0x0000000104590000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libjimage.dylib
0x00000001045ec000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libjdwp.dylib
0x0000000104634000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libjava.dylib
0x0000000104668000 	/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x00000001045cc000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libinstrument.dylib
0x0000000105600000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libzip.dylib
0x0000000268ccc000 	/usr/lib/i18n/libiconv_std.dylib
0x0000000268cc2000 	/usr/lib/i18n/libUTF8.dylib
0x0000000268cd1000 	/usr/lib/i18n/libmapper_none.dylib
0x00000001051d0000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libdt_socket.dylib
0x00000001056a8000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libnio.dylib
0x000000010de98000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libnet.dylib
0x00000001051e4000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libmanagement.dylib
0x0000000105688000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libmanagement_ext.dylib
0x000000010de74000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libextnet.dylib
0x000000011ec84000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libawt.dylib
0x000000011ed34000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libmlib_image.dylib
0x00000001e4821000 	/System/Library/Frameworks/JavaRuntimeSupport.framework/Versions/A/JavaRuntimeSupport
0x00000001a9385000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Carbon
0x00000001a0082000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/CommonPanels.framework/Versions/A/CommonPanels
0x000000019ae26000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Help.framework/Versions/A/Help
0x00000001a0086000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/ImageCapture.framework/Versions/A/ImageCapture
0x00000001a0062000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/OpenScripting.framework/Versions/A/OpenScripting
0x00000001a0081000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Ink.framework/Versions/A/Ink
0x00000001a007e000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SecurityHI.framework/Versions/A/SecurityHI
0x000000011eec4000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libawt_lwawt.dylib
0x000000011ec00000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libosxapp.dylib
0x00000001a7c42000 	/System/Library/Frameworks/ExceptionHandling.framework/Versions/A/ExceptionHandling
0x000000011f210000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libfontmanager.dylib
0x000000011efe8000 	/Library/Java/JavaVirtualMachines/jdk-17.0.14.jdk/Contents/Home/lib/libfreetype.dylib


VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:61027,suspend=y,server=n -agentpath:/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/UserApplication_2025_07_25_115157.jfr,log=/private/var/folders/4w/ctw6hhlj3hn1tfssw9j5c1l00000gn/T/UserApplication_2025_07_25_115157.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2024.3/captureAgent/debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.xgwc.user.UserApplication
java_class_path (initial): /Users/<USER>/IdeaProjects/xgwc-parent/xgwc-api/xgwc-api-user/target/classes:/Users/<USER>/IdeaProjects/xgwc-parent/xgwc-common/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.jar:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.37/hutool-all-5.8.37.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/33.4.6-jre/guava-33.4.6-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.3/failureaccess-1.0.3.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/jspecify/jspecify/0.3.0/jspecify-0.3.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar:/Users/<USER>/.m2/repository/com/aliyun/ecs20140526/7.1.0/ecs20140526-7.1.0.jar:/Users/<USER>/.m2/repository/com/aliyun/alibabacloud-gateway-pop/0.0.6/alibabacloud-gateway-pop-0.0.6.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-encode-util/0.0.2/darabonba-encode-util-0.0.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.68/bcprov-jdk15on-1.68.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-signature-util/0.0.4/darabonba-signature-util-0.0.4.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-string/0.0.5/darabonba-string-0.0.5.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-map/0.0.1/darabonba-map-0.0.1.jar:/Users/<USER>/.m2/repository/com/aliyun/darabonba-array/0.1.0/darabonba-array-0.1.0.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/5.4.1/poi-ooxml-5.4.1.jar:/Users/<USER>/.m2/repository/
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 9                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 301989888                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4831838208                                {product} {ergonomic}
   size_t MaxNewSize                               = 2898264064                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4831838208                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=/Users/<USER>/.nvm/versions/node/v22.14.0/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin
SHELL=/bin/zsh
LC_CTYPE=zh_CN.UTF-8

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:29 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6030 arm64
OS uptime: 0 days 2:27 hours
rlimit (soft/hard): STACK 8176k/65520k , CORE 0k/infinity , NPROC 2666/4000 , NOFILE 10240/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 2.17 1.85 2.06

CPU: total 11 (initial active 11) 0x61:0x0:0x5f4dea93:0, fp, simd, crc, lse

Memory: 16k page, physical 18874368k(135680k free), swap 0k(0k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.14+8-LTS-191) for bsd-aarch64 JRE (17.0.14+8-LTS-191), built on Dec  3 2024 11:03:03 by "mach5one" with clang Apple LLVM 12.0.0 (clang-1200.0.32.29)

END.
