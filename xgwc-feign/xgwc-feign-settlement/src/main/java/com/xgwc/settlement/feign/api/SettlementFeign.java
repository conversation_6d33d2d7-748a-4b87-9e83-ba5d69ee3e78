package com.xgwc.settlement.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(name = "xgwc-api-settlement", configuration= FeignConfig.class)
public interface SettlementFeign {

    /**
     * 处理主订单， 未结算的订单售后开始，售后结束都调用该接口， 已经结算的订单再售后结束之后调用
     */
    @RequestMapping("bill/handleMainOrder")
    ApiResult handleMainOrder(@RequestParam("orderId") Long orderId);

    /**
     * 触发订单佣金扣除（无票的时候直接调用，有发票的时候审核通过时调用）
     * @param billId 订单id
     * @return 订单信息
     */
    @RequestMapping("bill/preStaticsCommissionBack")
    ApiResult preStaticsCommissionBack(@RequestParam("billId") Long billId);
}
