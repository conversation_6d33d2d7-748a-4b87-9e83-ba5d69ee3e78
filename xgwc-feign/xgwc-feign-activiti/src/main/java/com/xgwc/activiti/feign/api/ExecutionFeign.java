package com.xgwc.activiti.feign.api;

import com.xgwc.activiti.feign.entity.FlowExecutionVo;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.dto.FlowExecutionDto;
import com.xgwc.feign.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "xgwc-api-activiti", contextId="executionFeign", configuration= FeignConfig.class)
public interface ExecutionFeign {


    /**
     * 创建启动流程
     * @param vo
     * @return
     */
    @PostMapping("/flow/task/start")
    ApiResult<Long> insertFlowExecution(@RequestBody FlowExecutionVo vo);

    /**
     * 流程自动审批
     * @param vo
     * @return
     */
    @PostMapping("/flow/task/automatic")
    ApiResult<Long> automaticFlowExecution(@RequestBody FlowExecutionVo vo);
}
