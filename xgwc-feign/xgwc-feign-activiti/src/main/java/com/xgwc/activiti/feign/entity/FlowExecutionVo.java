package com.xgwc.activiti.feign.entity;

import com.xgwc.common.annotation.FieldDesc;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class FlowExecutionVo {

    private static final long serialVersionUID=1L;

    @FieldDesc("流程标题")
    @NotNull(message = "流程标题不能为空")
    private String title;

    @FieldDesc("流程代码")
    @NotNull(message = "流程代码不能为空")
    private String flowValue;

    @FieldDesc("业务id")
    @NotNull(message = "业务id不能为空")
    private Long businessKey;

    @FieldDesc("品牌商id")
    @NotNull(message = "品牌商id不能为空")
    private Long brandId;

    @FieldDesc("品牌商名称")
    @NotNull(message = "品牌商名称不能为空")
    private String brandName;

    @FieldDesc("加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商名称")
    private String franchiseName;

    @FieldDesc("部门编码")
    private Long deptId;

    @FieldDesc("部门名称")
    private String deptName;

    @FieldDesc("业务数据对象")
    private Object variable;

    /** 创建人id */
    @FieldDesc("申请人id")
    private Long createBy;

    /** 创建人 */
    @FieldDesc("申请人")
    private String createName;
}
