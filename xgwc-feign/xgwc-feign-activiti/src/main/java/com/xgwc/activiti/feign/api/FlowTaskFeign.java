package com.xgwc.activiti.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;


@FeignClient(name = "xgwc-api-activiti", contextId = "FlowTaskFeign", configuration = FeignConfig.class)
public interface FlowTaskFeign {

    @PutMapping("/flow/task/feignCancel/{taskId}")
    ApiResult feignCancel(@PathVariable("taskId") Long taskId);

}