package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import com.xgwc.user.feign.entity.DesignerDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.feign.api
 * @Author: kou<PERSON><PERSON>huo
 * @CreateTime: 2025-05-27  16:58
 */
@FeignClient(name = "xgwc-api-user", contextId="designerFeign", configuration= FeignConfig.class)
public interface DesignerFeign {

    /**
     * 获取设计师列表
     *
     * @return ApiResult
     */
    @GetMapping("/designer/list")
    ApiResult list(@RequestParam(value = "name",  required = false) String name,
                   @RequestParam(value = "brandId", required = false) Long brandId,
                   @RequestParam(value = "goodBusiness" , required = false) Integer goodBusiness,
                   @RequestParam(value = "status", required = false) Integer status,
                   @RequestParam(value = "designerId") Long designerId,
                   @RequestParam(value = "managerPhone") String managerPhone,
                   @RequestParam(value = "pageSize") Integer pageSize,
                   @RequestParam(value = "pageNumber") Integer pageNumber);


    /**
     * 通过id查询设计师
     * @param designerId 设计师id
     * @return ApiResult
     */
    @GetMapping("/designer/{designerId}")
    ApiResult getDesignerById(@PathVariable("designerId") Long designerId);

    /**
     * 设计师重新申请
     * @param designer 设计师信息
     * @return ApiResult
     */
    @PostMapping("/designer/reapply")
    ApiResult reapply(@RequestBody DesignerDto designer);

    /**
     * 获取加入的品牌商下拉框
     * @return ApiResult
     */
    @GetMapping("/designer/joinBrandDropDown")
    ApiResult getInJoinBrandDropDown();
}
