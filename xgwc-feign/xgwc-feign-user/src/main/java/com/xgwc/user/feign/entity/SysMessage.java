package com.xgwc.user.feign.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SysMessage {

    /**
     * 主键id
     */
    private Long id;

    @NotNull(message = "通知类型不能为空")
    private String typeName;

    /**
     * 类型key
     */
    @NotNull(message = "通知类型key不能为空")
    private String typeKey;

    /**
     * 类型ID
     */
    private Long typeId;

    /**
     * 信息
     */
    @NotNull(message = "消息内容不能为空")
    private String message;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 更新时间
     */
    private String updateTime;

}
