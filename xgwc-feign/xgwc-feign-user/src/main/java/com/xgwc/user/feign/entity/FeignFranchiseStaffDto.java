package com.xgwc.user.feign.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.annotation.FieldLabel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class FeignFranchiseStaffDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("姓名")
    @Excel(name = "姓名")
    private String name;

    @FieldDesc("花名")
    @Excel(name = "花名")
    private String stageName;

    @FieldDesc("工作性质：0全职，1外包，2兼职，3合作方")
    @Excel(name = "工作性质：0全职，1外包，2兼职，3合作方")
    @FieldLabel("工作性质")
    private Integer jobNature;

    @FieldDesc("状态：0在职，1离职，2合作中，3未合作")
    @Excel(name = "状态：0在职，1离职，2合作中，3未合作")
    @FieldLabel("状态")
    private Integer status;

    @FieldDesc("绑定状态：0未绑，1已绑")
    @Excel(name = "绑定状态：0未绑，1已绑")
    private Integer bindStatus;

    @FieldDesc("绑定的用户id")
    @Excel(name = "绑定的用户id")
    private Long bindUserId;

    @FieldDesc("登录手机号")
    @Excel(name = "登录手机号")
    private String loginPhone;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("创建人")
    @Excel(name = "创建人")
    private String createBy;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("员工类型：0 内部员工，1合作员工")
    private Integer staffType;

}
