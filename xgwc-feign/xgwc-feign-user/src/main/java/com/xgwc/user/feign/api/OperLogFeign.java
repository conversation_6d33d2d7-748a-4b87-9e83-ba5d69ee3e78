package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import com.xgwc.user.feign.entity.SysOperLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "xgwc-api-user", contextId="operLogFeign", configuration= FeignConfig.class)
public interface OperLogFeign {

    @PostMapping("/operlog/add_log")
    ApiResult addLog(@RequestBody SysOperLog log);
}
