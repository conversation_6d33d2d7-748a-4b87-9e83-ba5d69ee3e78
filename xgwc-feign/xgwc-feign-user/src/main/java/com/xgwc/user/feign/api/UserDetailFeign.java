package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import com.xgwc.user.feign.entity.SysUserMiddle;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "xgwc-api-user", configuration= FeignConfig.class)
public interface UserDetailFeign {

    @GetMapping("/user/get_use_detail")
    ApiResult get_use_detail(@RequestParam("username") String username);

    @GetMapping("/user/delete_role_by_user_id")
    ApiResult deleteBrandRoleUserByUserId(@RequestParam("userId") Long userId);

    @GetMapping("/user/add_brand_role_user")
    ApiResult addBrandRoleUser(@RequestParam(value = "roleId") Long roleId
            , @RequestParam(value = "userId") Long userId
            , @RequestParam(value = "isFlag") Integer isFlag);

    @PostMapping("/user/insert_user_middle")
    ApiResult<Long> insertUserMiddle(@RequestBody SysUserMiddle vo);

    @GetMapping("/user/get_user_middle_by_id")
    ApiResult<Long> getUserMiddleById(@RequestParam("id") Long id);
}
