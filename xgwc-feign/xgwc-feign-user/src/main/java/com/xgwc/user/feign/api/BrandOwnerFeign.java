package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import com.xgwc.user.feign.entity.BrandOwnerDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "xgwc-api-user", contextId="BrandOwnerFeign", configuration= FeignConfig.class)
public interface BrandOwnerFeign {

    /**
     * 通过id查询品牌商信息
     * @param brandId 品牌商id
     * @return ApiResult
     */
    @GetMapping("/user/brandOwner/{brandId}")
    ApiResult<BrandOwnerDto> getBrandOwnerById(@PathVariable("brandId") Long brandId);

}
