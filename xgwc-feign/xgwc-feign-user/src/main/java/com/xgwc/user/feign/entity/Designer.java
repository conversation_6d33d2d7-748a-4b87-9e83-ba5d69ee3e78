package com.xgwc.user.feign.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


@Data
public class Designer {

private static final long serialVersionUID=1L;

    /** 设计师ID */
    private Long designerId;

    /** 身份证正面：加密 */
    private String idcardFront;

    /** 身份证反面：加密 */
    private String idcardBack;

    /** 身份证是否长期：0长期，1非长期 */
    private Integer idcardIsLongterm;

    /** 身份证开始时间 */
    private Date idcardStart;

    /** 身份证结束时间 */
    private Date idcardEnd;

    /** 姓名 */
    private String name;

    /** 身份证号：加密 */
    private String idcardNo;

    /** 手机号：加密 */
    private String phone;

    /** 邮箱：加密 */
    private String email;

    /** 紧急联系人 */
    private String emergencyName;

    /** 紧急手机号 */
    private String emergencyPhone;

    /** 擅长业务 */
    private Integer goodBusiness;

    /** 描述 */
    private String description;

    /** 开户名：加密 */
    private String bankUserName;

    /** 开户行 */
    private String bankName;

    /** 开户账号：加密 */
    private String bankNo;

    /** 支付宝姓名 */
    private String zfbName;

    /** 支付宝账号：加密 */
    private String zfbAccount;

    /** 管理员姓名 */
    private String managerName;

    /** 管理员手机号：加密 */
    private String managerPhone;

    /** 接单状态：0正常接单，1.暂停接单，2.离职 */
    private Integer receiveOrderStatus;

    /** 申请状态 */
    private Integer checkStatus;

    /** 审核时间 */
    private Date checkTime;

    /** 状态：0正常，1禁用 */
    private Integer status;

    /** 删除状态：0正常，1删除 */
    private Integer isDel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 行修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /** 审核原因 */
    private String reason;

    /** 管理员id */
    private Long managerUserId;

    /** 所属品牌 */
    private Long brandId;

}