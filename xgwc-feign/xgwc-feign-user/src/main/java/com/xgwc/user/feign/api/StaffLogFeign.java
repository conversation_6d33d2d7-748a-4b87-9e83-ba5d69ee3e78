package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.entity.StaffLog;
import com.xgwc.common.entity.StaffLogDto;
import com.xgwc.feign.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "xgwc-api-user", contextId="StaffLogFeign", configuration= FeignConfig.class)
public interface StaffLogFeign {

    @PostMapping("/staffLog/addLog")
    ApiResult addStaffLog(@RequestBody StaffLog log);

    @GetMapping("/staffLog/findLogByStaffIdAndBusinessType")
    ApiResult<List<StaffLogDto>> findStaffLogByStaffIdAndBusinessType(@RequestParam("staffId") Long staffId, @RequestParam("businessType") int businessType);
}
