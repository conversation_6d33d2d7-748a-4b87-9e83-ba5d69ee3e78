package com.xgwc.user.feign.entity;

import lombok.Data;

@Data
public class SysUserMiddle {

    /**
     * 主键id，以后作为真正唯一的userId
     */
    private Long id;

    /**
     * 主用户id
     */
    private Long mainUserId;

    /**
     * 用户类型（0：普通用户，1：品牌商，2：加盟商，3：设计师，4:品牌商员工，5：加盟商员工）
     */
    private Integer userType;

    /**
     * 状态：0正常，1非正常
     */
    private Integer status;

    /**
     * 来源id，当userType = 1时为品牌商ID， =2 为加盟商ID，=3为加盟商ID，=4为品牌商ID，=5为加盟商ID
     */
    private Long sourceId;


}
