package com.xgwc.user.feign.api;

import com.xgwc.feign.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "xgwc-api-user", contextId="ServiceAuthorizeFeign", configuration = FeignConfig.class)
public interface ServiceAuthorizeFeign {

    /**
     * 获取授权给服务商的品牌商Id列表
     */
    @GetMapping("/serviceAuthorize/getServiceBrandIdList")
    List<Long> getServiceBrandIdList(@RequestParam("serviceId") Long serviceId);

}