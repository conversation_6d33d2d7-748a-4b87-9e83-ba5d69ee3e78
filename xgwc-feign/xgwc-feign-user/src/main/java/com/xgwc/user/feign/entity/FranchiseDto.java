package com.xgwc.user.feign.entity;

import lombok.Data;

@Data
public class FranchiseDto {

    /**
     * 加盟商id
     */
    private Long id;

    /**
     * 管理员名称
     */
    private String managerName;

    /**
     * 加盟商名称
     */
    private String franchiseName;

    /**
     * 管理员手机号
     */
    private String managerPhone;

    /**
     * 管理员用户id
     */
    private Long managerUserId;

    /**
     * 状态：0正常，其余非正常
     */
    private Integer status;

    /**
     * 是否删除
     */
    private Integer isDel;

}
