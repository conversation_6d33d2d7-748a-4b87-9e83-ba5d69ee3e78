package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.feign.api
 * @Author: kouwen<PERSON>huo
 * @CreateTime: 2025-05-29  09:19
 */
@FeignClient(name = "xgwc-api-user", contextId="NoticeFeign", configuration= FeignConfig.class)
public interface NoticeFeign {


    /**
     * 设计师-个人自己收到的通知列表
     *
     * @param brandId 品牌商id
     * @return 通知列表
     */
    @GetMapping("/notice/designer/get_self_list")
    ApiResult getDesignerSelfNotice(@RequestParam("brandId") Long brandId);


    @GetMapping("/notice/send_notice")
    ApiResult sendNotice(@RequestParam(name = "noticeId") Integer noticeId,@RequestParam(name = "key") String key);
}
