package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "xgwc-api-user", contextId="userDeptFeign", configuration= FeignConfig.class)
public interface UserDeptFeign {

    @GetMapping("/user/getUserDeptByDeptId")
    ApiResult getUserDeptByDeptId(@RequestParam("deptId") Long deptId,@RequestParam("deptType") String deptType);

    @GetMapping("/user/getUserDeptByUserId")
    ApiResult getUserDeptByUserId(@RequestParam("userId") Long userId,@RequestParam("deptType") String deptType);
}
