package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import com.xgwc.user.feign.entity.SysMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "xgwc-api-user", contextId="MessageFeign", configuration= FeignConfig.class)
public interface MessageFeign {

    /**
     * 插入
     */
    @PostMapping("/sysmessage/addSysMessage")
    ApiResult addSysMessage(@RequestBody SysMessage sysMessage);

    /**
     * 批量插入
     */
    @PostMapping("/sysmessage/addSysMessages")
    ApiResult addSysMessages(@RequestBody List<SysMessage> sysMessages);
}
