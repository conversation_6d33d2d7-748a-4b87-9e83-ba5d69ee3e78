package com.xgwc.user.feign.api;

import com.xgwc.common.annotation.MethodDesc;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "xgwc-api-user", contextId="SysDictDataFeign", configuration= FeignConfig.class)
public interface SysDictDataFeign {

    @MethodDesc("获取字典数据")
    @GetMapping(value = "/user/dictData/type/{dictType}")
    ApiResult dictType(@PathVariable("dictType") String dictType, @RequestParam(value = "brandId", required = false) Long brandId);

}