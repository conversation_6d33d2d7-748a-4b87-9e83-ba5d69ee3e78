package com.xgwc.user.feign.entity;

import com.xgwc.common.annotation.Excel;
import com.xgwc.common.annotation.FieldDesc;
import lombok.Data;


@Data
public class BrandOwnerDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("品牌ID")
    @Excel(name = "品牌ID")
    private Long brandId;

    @FieldDesc("公司名称")
    @Excel(name = "公司名称")
    private String companyName;

    @FieldDesc("公司简称")
    @Excel(name = "公司简称")
    private String companySimpleName;

    @FieldDesc("联系人")
    @Excel(name = "联系人")
    private String contact;

    @FieldDesc("管理员手机号")
    @Excel(name = "管理员手机号")
    private String managerPhone;

    @FieldDesc("密码")
    @Excel(name = "密码")
    private String password;

    @FieldDesc("状态：0正常，1禁用")
    @Excel(name = "状态：0正常，1禁用")
    private Integer status;

    @FieldDesc("删除状态：0正常，1删除")
    @Excel(name = "删除状态：0正常，1删除")
    private Integer isDel;

    @FieldDesc("审核原因")
    @Excel(name = "审核原因")
    private String reason;

    private Long userId;
}
