package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import com.xgwc.user.feign.entity.FeignFranchiseStaffDto;
import com.xgwc.user.feign.entity.SysUserMiddle;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.feign.api
 * @Author: kouwenzhuo
 * @CreateTime: 2025-06-21  16:38
 */
@FeignClient(name = "xgwc-api-user", contextId="StaffFeign", configuration= FeignConfig.class)
public interface StaffFeign {

    /**
     * 根据userId查询员工信息
     *
     * @param userId  当前登录用户id
     * @param isFlag  0-品牌商，1-加盟商
     * @param brandId 品牌商id
     * @return 员工信息
     */
    @GetMapping(value = "/staff/getStaffByUserId")
    ApiResult getStaffByUserId(@RequestParam("userId") Long userId,
                               @RequestParam("isFlag") Integer isFlag,
                               @RequestParam(value = "brandId", required = false) Long brandId);

    /**
     * 根据userId查询员工信息
     *
     * @param userId 当前登录用户id
     * @return 员工信息
     */
    @GetMapping(value = "/franchiseStaff/getStaffByUserId/{userId}")
    ApiResult getFranchiseStaffByUserId(@PathVariable("userId") Long userId);

    @GetMapping(value = "/staff/getStaffInfoByUserIdAndBrandId")
    ApiResult getStaffInfoByUserIdAndBrandId(@RequestParam("userId") Long userId,
                                              @RequestParam("brandId") Long brandId);

    @GetMapping(value = "/franchiseStaff/getFranchiseStaffInfoByUserIdAndFranchiseId")
    ApiResult getFranchiseStaffInfoByUserIdAndFranchiseId(@RequestParam("userId") Long userId,
                                             @RequestParam("franchiseId") Long franchiseId);

    @PostMapping("/franchiseStaff/addFranchiseStaff")
    ApiResult<Long> addFranchiseStaff(@RequestBody FeignFranchiseStaffDto feignFranchiseStaffDto);

}
