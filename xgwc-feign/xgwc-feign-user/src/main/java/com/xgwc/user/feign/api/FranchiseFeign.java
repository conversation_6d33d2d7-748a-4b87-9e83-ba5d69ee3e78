package com.xgwc.user.feign.api;

import com.xgwc.common.entity.ApiResult;
import com.xgwc.feign.common.config.FeignConfig;
import com.xgwc.user.feign.entity.FranchiseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "xgwc-api-user", contextId = "FranchiseFeign", configuration = FeignConfig.class)
public interface FranchiseFeign {

    /**
     * 通过id查询加盟商
     *
     * @param id 加盟商id
     * @return ApiResult
     */
    @GetMapping("/franchise/{id}")
    ApiResult<FranchiseDto> getFranchiseById(@PathVariable("id") Long id);

    @PostMapping("/franchise/listByIds")
    List<FranchiseDto> listByIds(@RequestBody List<Long> ids);

}