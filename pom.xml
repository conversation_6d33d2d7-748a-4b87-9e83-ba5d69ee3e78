<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<packaging>pom</packaging>
	<modules>
		<module>xgwc-redis</module>
		<module>xgwc-common</module>
		<module>xgwc-exception</module>
		<module>xgwc-feign</module>
		<module>xgwc-api</module>
		<module>xgwc-gateway</module>
		<module>xgwc-global</module>
        <module>xgwc-feign/xgwc-feign-settlement</module>
    </modules>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.3.0</version>
	</parent>
	<groupId>com.xgwc</groupId>
	<artifactId>xgwc-parent</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>xgwc-parent</name>
	<description>sass</description>

	<properties>
		<java.version>17</java.version>
		<mybatis-spring-boot-starter.version>3.0.4</mybatis-spring-boot-starter.version>
		<mybatis.version>3.5.19</mybatis.version>
		<redis.version>3.3.0</redis.version>
		<spring-cloud-starter-alibaba-nacos.version>2023.0.1.0</spring-cloud-starter-alibaba-nacos.version>
		<spring.cloud.alibaba.version>2023.0.3.2</spring.cloud.alibaba.version>
		<commons-lang3>3.17.0</commons-lang3>
		<bcprov-jdk18on.version>1.80</bcprov-jdk18on.version>
		<fastjson.version>2.0.57</fastjson.version>
		<commons-pool2>2.12.1</commons-pool2>
		<google.guava.version>33.4.6-jre</google.guava.version>
		<aspectj.version>1.9.23</aspectj.version>
		<druid.version>1.2.24</druid.version>
		<mysql-connector-java.version>8.0.33</mysql-connector-java.version>
		<pagehelper.start.version>2.1.0</pagehelper.start.version>
		<spring.cloud.version>2023.0.1</spring.cloud.version>
		<open-feign.version>4.2.1</open-feign.version>
		<hutool.version>5.8.37</hutool.version>
		<feign-core.version>13.6</feign-core.version>
		<loadbalancer.version>4.2.1</loadbalancer.version>
		<activiti.version>8.6.0</activiti.version>
		<activiti.version>8.6.0</activiti.version>
		<apache.poi.version>5.4.1</apache.poi.version>
		<security.version>3.4.5</security.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-dependencies</artifactId>
			<version>${spring.cloud.version}</version>
			<type>pom</type>
			<scope>import</scope>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-alibaba-dependencies</artifactId>
			<version>${spring.cloud.alibaba.version}</version>
			<type>pom</type>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>${commons-lang3}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>${commons-pool2}</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-security</artifactId>
				<version>${security.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-openfeign</artifactId>
				<version>${open-feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-core</artifactId>
				<version>${feign-core.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-loadbalancer</artifactId>
				<version>${loadbalancer.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-jackson</artifactId>
				<version>${feign-core.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<version>${spring-cloud-starter-alibaba-nacos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<version>${spring-cloud-starter-alibaba-nacos.version}</version>
			</dependency>
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql-connector-java.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis.spring.boot</groupId>
				<artifactId>mybatis-spring-boot-starter</artifactId>
				<version>${mybatis-spring-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>${mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper-spring-boot-starter</artifactId>
				<version>${pagehelper.start.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			<dependency>
				<groupId>org.activiti</groupId>
				<artifactId>activiti-spring-boot-starter</artifactId>
				<version>${activiti.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>mybatis</artifactId>
						<groupId>org.mybatis</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- excel工具 -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${apache.poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${apache.poi.version}</version>
			</dependency>

			<!--<dependency>
				<groupId>org.activiti.dependencies</groupId>
				<artifactId>activiti-dependencies</artifactId>
				<version>${activiti.version}</version>
				<type>pom</type>
			</dependency>-->
		</dependencies>
	</dependencyManagement>

	<repositories>
		<repository>
			<id>aliyunmaven</id>
			<name>aliyun</name>
			<url>https://maven.aliyun.com/repository/public</url>
		</repository>
		<repository>
			<id>huaweicloud</id>
			<name>huawei</name>
			<url>https://mirrors.huaweicloud.com/repository/maven/</url>
		</repository>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<releases>
				<enabled>false</enabled>
			</releases>
		</repository>
		<repository>
			<id>activiti-releases</id>
			<url>https://artifacts.alfresco.com/nexus/content/repositories/activiti-releases</url>
		</repository>
	</repositories>

</project>
