management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
spring:
  data:
    redis:
      host: *************
      port: 6379
      database: 0
      password: TqbZ4tpJA153LN5Boax4
    lettuce:
      pool:
        max-idle: 20
        min-idle: 5
        max-active: 50
        adaptiveRefreshTriggersTimeout: 5
        enablePeriodicRefresh: 10
  cloud:
    nacos:
      discovery: # 【配置中心】配置项
        namespace: 6cc1ba75-2b9c-4ceb-8398-fc381a8d747b
        group: DEFAULT_GROUP
    gateway:
      routes:
        - id: xgwc-api-user
          uri: lb://xgwc-api-user
          predicates:
            - Path=/api/users/**
          filters:
            - StripPrefix=2
        - id: xgwc-api-order
          uri: lb://xgwc-api-order
          predicates:
            - Path=/api/order/**
          filters:
            - StripPrefix=2
        - id: xgwc-api-wechat
          uri: lb://xgwc-api-wechat
          predicates:
            - Path=/api/wechat/**
          filters:
            - StripPrefix=2
    loadbalancer:
      configurations: default
    circuitbreaker:
      resilience4j:
        instances:
          userServiceCB:
            failureRateThreshold: 50
            minimumNumberOfCalls: 5
            automaticTransitionFromOpenToHalfOpenEnabled: true
            waitDurationInOpenState: 5s
            permittedNumberOfCallsInHalfOpenState: 3
            slidingWindowSize: 10
            slidingWindowType: COUNT_BASED
            registerHealthIndicator: true