<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="server_name" source="spring.cloud.client.hostname" />
    <springProperty scope="context" name="server_ip" source="spring.cloud.client.ip-address" />
    <springProperty scope="context" name="profile" source="spring.profiles.active"
                    defaultValue="default"></springProperty>
    <property name="MAX_HISTORY" value="15"/>
    <!-- 定义日志文件总大小 -->
    <property name="TOTAL_SIZE_CAP" value="10GB"/>
    <!-- 定义单个日志文件大小 -->
    <property name="MAX_FILE_SIZE" value="50MB"/>
    <property name="LOG_PATH" value="${user.home}/logs"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </layout>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/output.log</file>
        <!-- 基于文件大小和时间的滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/output-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!-- 日志文件保留天数 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <!-- 日志归档文件总大小 -->
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            <!-- 单个日志文件大小 -->
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
        </rollingPolicy>
        <!-- 日志输出格式 -->
        <encoder>
            <pattern>[%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36}: %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>