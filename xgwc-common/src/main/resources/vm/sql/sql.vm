-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, icon, auth_name,component,path,menu_type,order_num,status,remark,create_by,create_time)
values('${functionName}', '0', '#','${permissionPrefix}:list','${businessName}', '${moduleName}/${businessName}/index',1,1,0,'${functionName}菜单','1',sysdate());

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, icon, auth_name,component,path,menu_type,order_num,status,remark,create_by,create_time)
values('${functionName}查询', @parentId, '#','${permissionPrefix}:query', '','',2,1,0,null,'1',sysdate());

insert into sys_menu (menu_name, parent_id, icon, auth_name,component,path,menu_type,order_num,status,remark,create_by,create_time)
values('${functionName}新增', @parentId, '#','${permissionPrefix}:add', '','',2,1,0,null,'1',sysdate());

insert into sys_menu (menu_name, parent_id, icon, auth_name,component,path,menu_type,order_num,status,remark,create_by,create_time)
values('${functionName}修改', @parentId, '#','${permissionPrefix}:edit', '','',2,1,0,null,'1',sysdate());

insert into sys_menu (menu_name, parent_id, icon, auth_name,component,path,menu_type,order_num,status,remark,create_by,create_time)
values('${functionName}删除', @parentId, '#','${permissionPrefix}:remove', '','',2,1,0,null,'1',sysdate());

insert into sys_menu (menu_name, parent_id, icon, auth_name,component,path,menu_type,order_num,status,remark,create_by,create_time)
values('${functionName}导出', @parentId, '#','${permissionPrefix}:export', '','',2,1,0,null,'1',sysdate());