package ${packageName}.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class ${ClassName}Vo {

    private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    #if(!$table.isVoColumn($column.javaField))
    @FieldDesc("$column.columnComment")
    #if($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    #end
    private $column.javaType $column.javaField;

    #end
#end


}
