package ${packageName}.entity.dto;

import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.util.Date;

@Data
public class ${ClassName}Dto {

    private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    @FieldDesc("$column.columnComment")
    #set($comment=$column.columnComment)
    #if($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
    #else
    @Excel(name = "${comment}")
    #end
    private $column.javaType $column.javaField;

#end




    @Override
    public String toString(){
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
    #foreach ($column in $columns)
        #if($column.javaField.length() > 2 && $column.javaField.substring(1,2).matches("[A-Z]"))
            #set($AttrName=$column.javaField)
        #else
            #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
        #end
            .append("${column.javaField}",get${AttrName}())
    #end
        .toString();
    }
}
