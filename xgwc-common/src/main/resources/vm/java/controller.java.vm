package ${packageName}.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import ${packageName}.entity.vo.${ClassName}Vo;
import ${packageName}.entity.dto.${ClassName}Dto;
import ${packageName}.entity.vo.${ClassName}QueryVo;
import ${packageName}.service.I${ClassName}Service;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.annotation.Log;
import com.xgwc.common.enums.BusinessType;
import com.xgwc.common.controller.BaseController;
import com.xgwc.common.annotation.MethodDesc;




@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseController {
    @Autowired
    private I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    @MethodDesc("查询${functionName}列表")
    @PreAuthorize("@ss.hasPermission('${permissionPrefix}:list')")
    @GetMapping("/list")
    public ApiResult<${ClassName}Dto> list(${ClassName}QueryVo ${className}) {
        startPage();
        List<${ClassName}Dto> list = ${className}Service.select${ClassName}List(${className});
        return getDataTable(list);
    }


    /**
     * 导出${functionName}列表
     */
    @MethodDesc("导出${functionName}列表")
    @PreAuthorize("@ss.hasPermission('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ${ClassName}QueryVo ${className}) {
##        List<${ClassName}VO> list = ${className}Service.select${ClassName}List(${className});
##        ExcelUtil<${ClassName}VO> util = new ExcelUtil<${ClassName}VO>(${ClassName}VO.class);
##        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @MethodDesc("获取${functionName}详细信息")
    @PreAuthorize("@ss.hasPermission('${permissionPrefix}:query')")
    @GetMapping(value = "/get")
    public ApiResult<${ClassName}Dto> getInfo(@RequestParam("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return success(${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @MethodDesc("新增${functionName}")
    @PreAuthorize("@ss.hasPermission('${permissionPrefix}:add')")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    public ApiResult add(@RequestBody ${ClassName}Vo ${className}) {
        return toAjax(${className}Service.insert${ClassName}(${className}));
    }

    /**
     * 修改${functionName}
     */
    @MethodDesc("修改${functionName}")
    @PreAuthorize("@ss.hasPermission('${permissionPrefix}:edit')")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public ApiResult edit(@RequestBody ${ClassName}Vo ${className}) {
        return toAjax(${className}Service.update${ClassName}(${className}));
    }

    /**
     * 删除${functionName}
     */
    @MethodDesc("删除${functionName}")
    @PreAuthorize("@ss.hasPermission('${permissionPrefix}:remove')")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
	@DeleteMapping("/remove")
    public ApiResult remove(@RequestBody ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s));
    }
}
