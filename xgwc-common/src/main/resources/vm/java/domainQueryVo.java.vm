package ${packageName}.entity.vo;

import lombok.Data;

import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class ${ClassName}QueryVo {

    private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    #if($column.javaType == 'Date')
    @FieldDesc("${column.columnComment}开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private $column.javaType ${column.javaField}Start;

    @FieldDesc("${column.columnComment}结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private $column.javaType ${column.javaField}End;
    #else
    @FieldDesc("$column.columnComment")
    private $column.javaType ${column.javaField};
    #end

#end


}
