package com.xgwc.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 用户表
 */
@Data
public class SysUser {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 中间与用户ID
     */
    private Long mainUserId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 手机号
     */
    private String phone;

    private Long deptId;

    /**
     * 启用状态，0:正常 1:禁用
     */
    private Integer status;

    /**
     * 删除标志（0 代表存在 2 代表删除）
     */
    private Integer delFlag;

    /**
     * 最后登陆IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 微信id
     */
    private String wechatOpenid;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 行修改时间
     */
    private Date modifyTime;

    /**
     * 密码修改时间
     */
    private Date passwordModifyTime;

    private Integer isDel;

    /**
     * 用户类型（0：普通用户，1：品牌商，2：加盟商，3：设计师，4:品牌商员工，5：加盟商员工，6:财务服务商）
     */
    private Integer userType;

    /**
     * 品牌商ID， 当为品牌商员工时
     */
    private Long brandId;

    /**
     * 加盟商ID， 当为品牌商员工时
     */
    private Long franchiseId;

    /**
     * 当，登陆用户为设计师时，设计师Map, key为品牌商id， value为设计师id
     */
    private Map<Long, Long> designerMap;

    /**
     * 设计师ID， 当为设计师时
     */
    private String designerIds;

    /**
     * 品牌商下的加盟商ID，用逗号分隔
     */
    private String franchiseIds;

    /**
     * 加盟商所加盟的品牌商，用逗号分隔
     */
    private String brandIds;

    /**
     * 服务商id
     */
    private Long serviceId;

    /**
     * 销售服务商id
     */
    private Long marketId = 6L;

    /**
     * 花名
     */
    private String stageName;

    @JsonIgnore
    public boolean isBrandUser() {
        return this.userType == 1 || this.userType == 4;
    }

    @JsonIgnore
    public boolean isFranchiseUser() {
        return this.userType == 2 || this.userType == 5;
    }

    @JsonIgnore
    public boolean isServiceUser() {
        return this.userType == 6;
    }

}