package com.xgwc.common.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgwc.common.enums.ApiStatusEnums;

/**
 * <AUTHOR>
 */
public class ApiResult<T> {

    @JsonProperty("status")
    private int status;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private T data;

    public ApiResult() {
        this.status = ApiStatusEnums.OK.getStatus();
        this.message = ApiStatusEnums.OK.getMessage();
    }

    public ApiResult(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public ApiResult(int status, String message, T data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    public void setError(String msg)
    {
        this.status = -1;
        this.message = msg;
    }

    public static <T> ApiResult<T> ok() {
        return ok("OK", null);
    }

    public static <T> ApiResult<T> ok(T data) {
        return ok("OK", data);
    }

    public static <T> ApiResult<T> ok(String message, T data) {
        return new ApiResult<>(1, message, data);
    }

    public static <T> ApiResult<T> error(String message) {
        return new ApiResult<>(ApiStatusEnums.ERROR.getStatus(), message, null);
    }

    public static ApiResult noPermission() {
        return new ApiResult<>(ApiStatusEnums.NO_PERMISSION.getStatus(), ApiStatusEnums.NO_PERMISSION.getMessage(), null);
    }

    public static <T> ApiResult<T> error(int code, String message) {
        if (code == 1) {
            throw new IllegalArgumentException();
        }
        return new ApiResult<>(code, message, null);
    }

    public static <T> ApiResult<T> error(ApiStatusEnums apiStatusEnums) {
        return new ApiResult<>(apiStatusEnums.getStatus(), apiStatusEnums.getMessage(), null);
    }


    /**
     * 状态（0调用失败；1请求成功；2参数错误
     */
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @JsonIgnore
    public boolean isOk() {
        return status == 0;
    }
    public static ApiResult toAjax(int result) {
        return result > 0 ? ok() : error("");
    }

}
