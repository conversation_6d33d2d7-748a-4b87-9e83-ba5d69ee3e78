package com.xgwc.common.entity.dto;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.util.Date;
import com.xgwc.common.annotation.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xgwc.common.annotation.Excel;

@Data
public class FlowExecutionDto {

    private static final long serialVersionUID=1L;

    @FieldDesc("主键")
    @Excel(name = "主键")
    private Long id;

    @FieldDesc("流程定义id")
    @Excel(name = "流程定义id")
    private Long defId;

    @FieldDesc("流程原数据")
    @Excel(name = "流程原数据")
    private String defData;

    @FieldDesc("流程名称")
    @Excel(name = "流程名称")
    private String title;

    @FieldDesc("业务id")
    @Excel(name = "业务id")
    private Long businessKey;

    @FieldDesc("品牌商id")
    @Excel(name = "品牌商id")
    private Long brandId;

    @FieldDesc("品牌商名称")
    @Excel(name = "品牌商名称")
    private String brandName;

    @FieldDesc("加盟商id")
    @Excel(name = "加盟商id")
    private Long franchiseId;

    @FieldDesc("加盟商名称")
    @Excel(name = "加盟商名称")
    private String franchiseName;

    @FieldDesc("部门编码")
    @Excel(name = "部门编码")
    private Long deptId;

    @FieldDesc("部门名称")
    @Excel(name = "部门名称")
    private String deptName;

    @FieldDesc("版本")
    @Excel(name = "版本")
    private Long version;

    @FieldDesc("流程状态")
    @Excel(name = "流程状态")
    private String status;

    @FieldDesc("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    @FieldDesc("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    @FieldDesc("是否可取消：1可以 0不可以")
    @Excel(name = "是否可取消：1可以 0不可以")
    private Long cancelable;

    @FieldDesc("流程代码")
    @Excel(name = "流程代码")
    private String flowValue;

    @FieldDesc("流程名称")
    @Excel(name = "流程名称")
    private String flowName;

    @FieldDesc("发起人")
    @Excel(name = "发起人")
    private Long createBy;

    @FieldDesc("发起人")
    @Excel(name = "发起人")
    private String createName;

    @FieldDesc("最后一次审批表单")
    private JSONArray flowTables;


}
