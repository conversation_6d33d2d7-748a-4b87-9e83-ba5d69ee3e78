package com.xgwc.common.entity;

import lombok.Data;

import java.util.Date;


@Data
public class StaffLog {

private static final long serialVersionUID=1L;

    /** 主键id */
    private Long id;

    /** 业务id */
    private Long staffId;

    /** 字段名 */
    private String fieldName;

    /** 内容 */
    private String remark;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 业务类型（1:品牌商；2:加盟商;3:财务服务商；4:销售服务商） */
    private Integer businessType;

    /** 业务id */
    private Long businessId;

}