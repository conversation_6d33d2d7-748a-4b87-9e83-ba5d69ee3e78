package com.xgwc.common.entity;

import com.google.common.collect.ImmutableList;
import com.xgwc.common.annotation.FieldDesc;
import com.xgwc.common.util.ImageUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 *
 * <AUTHOR>
 */

public class ApiListResult<T> {
    /**
     * 总记录数
     */
    @FieldDesc("总记录数")
    private long total;
    /**
     * 列表数据
     */
    @FieldDesc("列表数据")
    private List<T> rows;

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public static <T> ApiListResult<T> isOk() {
        ApiListResult<T> objectApiListResult = new ApiListResult<>();
        objectApiListResult.setTotal(0);
        objectApiListResult.setRows(ImmutableList.of());
        return objectApiListResult;
    }

    public static <T> ApiListResult<T> isOk(long total, List<T> rows) {
        ApiListResult<T> objectApiListResult = new ApiListResult<>();
        objectApiListResult.setTotal(total);
        objectApiListResult.setRows(rows);
        return objectApiListResult;
    }

}
