package com.xgwc.common.generator;

import com.xgwc.common.util.FileUtil;
import com.xgwc.common.util.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import java.io.File;
import java.io.StringWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Log4j2
public class JavaCodeGenerate {


    private static String WORK_PATH = "/Users/<USER>/Documents";

    public static void main(String[] args) throws Exception {

        GenTable table = new GenTable();
        table.setTableName("report_inbound_sales");
        table.setClassName("inboundSales");
        table.setBusinessName("inboundSales");
        table.setPackageName("com.xgwc.order");
        table.setModuleName("inboundSales");
        table.setFunctionName("销售进线");
        table.setServiceName("xgwc-api-order");

//        //暂不支持 feign 自动生成
//        table.setFeignName("xgwc-feign-user");
        if(StringUtils.isBlank(WORK_PATH))
            WORK_PATH = System.getProperty("user.dir");

        jdbc(table);

        VelocityInitializer.initVelocity();
        VelocityContext context = VelocityUtils.prepareContext(table);

        // 获取模板列表
        List<String> templates = VelocityUtils.getTemplateList();
        for (String template : templates) {
            // 渲染模板
            String path = getGenPath(table, template);
            log.info("渲染模板:{},path:{}",template,path);
            StringWriter sw = new StringWriter();
            Template tpl = Velocity.getTemplate(template, "UTF-8");
            tpl.merge(context, sw);
            FileUtil.write(path, sw.toString());
        }
    }


    public static String getGenPath(GenTable table, String template) {
        return  WORK_PATH + File.separator +"xgwc-sass" + File.separator  + "xgwc-parent" + File.separator +  "xgwc-api"
                + File.separator + table.getServiceName() + File.separator + "src" + File.separator + VelocityUtils.getFileName(template, table);
    }
    private static void jdbc(GenTable table) throws ClassNotFoundException, SQLException {
        //        1.加载驱动
        Class.forName("com.mysql.cj.jdbc.Driver");
//        2.用户信息和url
        String url = "********************************************************************************************";
        String username="root";
        String password=",uPkdLhlC1,t";
//        3.连接成功，数据库对象 Connection
        Connection connection = DriverManager.getConnection(url,username,password);
//        4.执行SQL对象Statement，执行SQL的对象
        Statement statement = connection.createStatement();
//        5.执行SQL的对象去执行SQL，返回结果集
        String sql =
                "SELECT " +
                        "COLUMN_NAME," +
                        "DATA_TYPE," +
                        "IS_NULLABLE," +
                        "COLUMN_KEY," +
                        "COLUMN_COMMENT" +
                " FROM " +
                "     INFORMATION_SCHEMA.COLUMNS " +
                " WHERE " +
                "  TABLE_SCHEMA = 'xgwc_sass' " +
                "  AND TABLE_NAME = '"+table.getTableName()+"';";
        log.info("执行SQL：{}", sql);
        ResultSet resultSet = statement.executeQuery(sql);
        List<GenTableColumn> columns = new ArrayList<>();
        while(resultSet.next()){
            String field = resultSet.getString(1);
            String type = resultSet.getString(2);
            String isNull = resultSet.getString(3);
            String isKey = resultSet.getString(4);
            String desc = resultSet.getString(5);

            type = typeTransform(type);
            GenTableColumn column = new GenTableColumn();
            column.setColumnName(field);
            column.setColumnComment(desc);
            column.setJavaField(StringUtils.toCamelCase(field));
            column.setJavaType(type);
            column.setIsQuery(true);
            queryTransform(column);
            if("PRI".equalsIgnoreCase(isKey))
                table.setPkColumn(column);
            columns.add(column);
        }
        table.setColumns(columns);
//        6.释放连接
        resultSet.close();
        statement.close();
        connection.close();
    }

    private static void queryTransform(GenTableColumn column) {
        if(Objects.equals(column.getJavaType(), GenConstants.TYPE_STRING))
            column.setQueryType("LIKE");
        else if (Objects.equals(column.getJavaType(), GenConstants.TYPE_LONG)
                || Objects.equals(column.getJavaType(), GenConstants.TYPE_BIGDECIMAL)
        )
            column.setQueryType("EQ");
        else if (Objects.equals(column.getJavaType(), GenConstants.TYPE_DATE))
            column.setQueryType("BETWEEN");

    }

    private static String typeTransform(String type) {
        String dataType = getDbType(type);
        if (arraysContains(GenConstants.COLUMNTYPE_STR, dataType) || arraysContains(GenConstants.COLUMNTYPE_TEXT, dataType)) {

        } else if (arraysContains(GenConstants.COLUMNTYPE_TIME, dataType)) {
            return GenConstants.TYPE_DATE;
        } else if (arraysContains(GenConstants.COLUMNTYPE_NUMBER, dataType)) {
            // 如果是浮点型 统一用BigDecimal
            String[] str = StringUtils.split(StringUtils.substringBetween(type, "(", ")"), ",");
            if (str != null && str.length == 2 && Integer.parseInt(str[1]) > 0) {
                return GenConstants.TYPE_BIGDECIMAL;
            }
            // 如果是整形
            else if (str != null && str.length == 1 && Integer.parseInt(str[0]) <= 10) {
                return GenConstants.TYPE_INTEGER;
            }
            // 长整形
            else {
                return GenConstants.TYPE_LONG;
            }
        }
        return GenConstants.TYPE_STRING;
    }

    public static boolean arraysContains(String[] arr, String targetValue) {
        return Arrays.asList(arr).contains(targetValue);
    }

    public static String getDbType(String columnType) {
        if (StringUtils.indexOf(columnType, "(") > 0) {
            return StringUtils.substringBefore(columnType, "(");
        } else {
            return columnType;
        }
    }
}
