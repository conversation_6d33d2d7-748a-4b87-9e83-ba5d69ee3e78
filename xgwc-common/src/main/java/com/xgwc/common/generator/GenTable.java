package com.xgwc.common.generator;


import com.xgwc.common.util.StringUtils;
import lombok.Data;

import java.util.List;

@Data
public class GenTable  {


    /** 表名称 */
    private String tableName;

    /** 实体类名称(首字母大写) */
    private String className;

    /** 生成模块名 */
    private String moduleName;

    /** 生成业务名 */
    private String businessName;

    private String packageName;

    /** 生成功能名  */
    private String functionName;

    /** 服务  */
    private String serviceName;

    /** 生成功能名 */
    private String feignName;

    /** 主键信息 */
    private GenTableColumn pkColumn;

    /** 表列信息 */
    private List<GenTableColumn> columns;

    private String[] BASE_ENTITY = { "creator", "createTime", "creator", "updateTime", "isDel", "createBy", "updateBy" , "modifyTime", "isDel"};

    public void setClassName(String className) {
        this.className = StringUtils.capitalize(className);
    }

    public String getFunctionName() {
        if(StringUtils.isBlank(functionName))
            return className;
        return functionName;
    }

    public String getServiceName() {
        if(StringUtils.isBlank(functionName))
            return "xgwc-api-user";
        return serviceName;
    }

    public boolean isVoColumn(String javaField) {
        return StringUtils.equalsAnyIgnoreCase(javaField, BASE_ENTITY);
    }


}