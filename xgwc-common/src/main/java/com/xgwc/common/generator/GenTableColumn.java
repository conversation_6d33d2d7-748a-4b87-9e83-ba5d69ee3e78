package com.xgwc.common.generator;


import com.xgwc.common.util.StringUtils;
import lombok.Data;

import java.io.Serializable;


@Data
public class GenTableColumn implements Serializable
{
    private static final long serialVersionUID = 1L;

    private Boolean isPk;

    private String columnName;

    private String columnComment;

    private String capJavaField;

    /** JAVA类型 */
    private String javaType;

    /** JAVA字段名 */
    private String javaField;

    /** 字典类型 */
    private String dictType;

    /** 排序 */
    private Integer sort;

    private Boolean isQuery;

    /** 查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围） */
    private String queryType;


    public String getCapJavaField() {
        return StringUtils.capitalize(javaField);
    }
}
