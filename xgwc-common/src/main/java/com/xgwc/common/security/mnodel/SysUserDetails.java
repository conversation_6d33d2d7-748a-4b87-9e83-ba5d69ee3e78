package com.xgwc.common.security.mnodel;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.enums.StatusEnums;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description: SpringSecurity 需要的用户详情
 *
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties({"enabled", "accountNonExpired", "accountNonLocked", "credentialsNonExpired", "authorities", "password"})
public class SysUserDetails implements UserDetails {
    private String username;

    private SysUser sysUser;

    private Set<String> permissions;

    private Set<String> roles;

    private String password;

    public SysUserDetails(SysUser user, Set<String> permissions, Set<String> roles, String username) {
        this.sysUser = user;
        this.permissions = permissions;
        this.roles = roles;
        this.username = username;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 返回当前用户的权限
        return permissions.stream().filter(StrUtil::isNotEmpty)
                .map(SimpleGrantedAuthority::new).collect(Collectors.toList());
    }

    @Override
    public String getPassword() {
        return sysUser.getPassword();
    }

    @Override
    public String getUsername() {
        return this.username;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     *
     * @return 是否可用
     */
    @Override
    public boolean isEnabled() {
        return StatusEnums.ENABLE.getKey() == sysUser.getStatus();
    }

}
