package com.xgwc.common.context;

public class TenantContext {

    private static final ThreadLocal<Long> CURRENT_TENANT = new ThreadLocal<>();
    private static final ThreadLocal<Boolean> IGNORE = new ThreadLocal<>();

    public static void setCurrentTenant(Long tenant) {
        CURRENT_TENANT.set(tenant);
    }

    public static Long getCurrentTenant() {
        return CURRENT_TENANT.get();
    }

    public static void setIgnore(Boolean ignore) {
        IGNORE.set(ignore);
    }

    public static boolean isIgnore() {
        return Boolean.TRUE.equals(IGNORE.get());
    }

    public static void clear() {
        CURRENT_TENANT.remove();
    }
}
