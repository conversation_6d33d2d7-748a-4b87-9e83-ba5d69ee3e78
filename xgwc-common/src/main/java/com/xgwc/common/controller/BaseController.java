package com.xgwc.common.controller;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xgwc.common.entity.ApiListResult;
import com.xgwc.common.entity.ApiResult;
import com.xgwc.common.util.DateUtils;
import com.xgwc.common.util.PageUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.common.util.page.PageDomain;
import com.xgwc.common.util.page.TableSupport;
import com.xgwc.common.util.servlet.ServletUtils;
import com.xgwc.common.util.sql.SqlUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        //不分页请求参数
        if (Objects.nonNull(ServletUtils.getParameterToBool("notPage")) && ServletUtils.getParameterToBool("notPage")) {
            return;
        }
        PageUtils.startPage();
    }

    /**
     * 设置请求排序数据
     */
    protected void startOrderBy() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotEmpty(pageDomain.getOrderBy())) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.orderBy(orderBy);
        }
    }

    /**
     * 清理分页的线程变量
     */
    protected void clearPage() {
        PageUtils.clearPage();
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected ApiResult getDataTable(List<?> list) {
        ApiListResult rspData = new ApiListResult();
        if(list != null && !list.isEmpty()) {
            rspData.setTotal(new PageInfo(list).getTotal());
            rspData.setRows(list);
        } else {
            rspData.setTotal(0);
            rspData.setRows(new ArrayList());
        }
        return ApiResult.ok(rspData);
    }


    /**
     * 返回成功
     */
    public ApiResult success() {
        return ApiResult.ok();
    }

    /**
     * 返回失败消息
     */
    public ApiResult error() {
        return ApiResult.error("");
    }

    /**
     * 返回成功消息
     */
    public ApiResult success(String message) {
        return ApiResult.ok(message);
    }

    /**
     * 返回成功消息
     */
    public ApiResult success(Object data) {
        return ApiResult.ok(data);
    }

    /**
     * 返回失败消息
     */
    public ApiResult error(String message) {
        return ApiResult.error(message);
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected ApiResult toAjax(int result) {
        return result > 0 ? success() : error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected ApiResult toAjax(long result) {
        return result > 0 ? success() : error();
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }


}
