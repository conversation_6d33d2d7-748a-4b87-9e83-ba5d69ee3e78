package com.xgwc.common.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

public class FileUtil {

    /**
     * 获取文件后缀名
     * @return 后缀名
     */
    public static String getSuffix(MultipartFile file){
        String originalName = file.getOriginalFilename();
        if(StringUtil.isNotEmpty(originalName)){
            return originalName.substring(originalName.lastIndexOf(".") + 1);
        }
        return "";
    }

    /**
     * 获取文件后缀名
     * @return 后缀名
     */
    public static String getSuffix(String fileUrl){
        if(StringUtil.isNotEmpty(fileUrl)){
            return fileUrl.substring(fileUrl.lastIndexOf(".") + 1);
        }
        return "";
    }

    public static void write(String path, String str) throws IOException {
        File txt= new File(path);
        if(!txt.getParentFile().exists())
            txt.getParentFile().mkdirs();
        if(!txt.exists()){
            txt.createNewFile();
        }
        FileWriter writer = new FileWriter(path);
        writer.write("");//清空原文件内容
        writer.write(str);
        writer.flush();
        writer.close();
    }

    /**
     * MultipartFile 转 file
     */
    public static File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        Path tempFile = Files.createTempFile("temp-", multipartFile.getOriginalFilename());
        Files.copy(multipartFile.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
        return tempFile.toFile();
    }
}
