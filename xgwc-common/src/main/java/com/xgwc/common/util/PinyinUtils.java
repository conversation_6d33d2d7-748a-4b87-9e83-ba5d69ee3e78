package com.xgwc.common.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.util
 * @Author: k<PERSON><PERSON><PERSON>huo
 * @CreateTime: 2025-06-17  14:05
 */
public class PinyinUtils {

    /**
     * 将汉字转换为拼音
     *
     * @param chinese 输入的汉字字符串
     * @return 转换后的拼音字符串
     */
    public static String convertToPinyin(String chinese) {
        if (chinese == null || chinese.isEmpty()) {
            return chinese;
        }

        StringBuilder pinyin = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE); // 设置为小写
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE); // 不带声调

        for (char c : chinese.toCharArray()) {
            if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) { // 判断是否是汉字
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyin.append(pinyinArray[0]); // 取第一个拼音
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            } else {
                pinyin.append(c); // 非汉字字符直接追加
            }
        }
        return pinyin.toString();
    }
}
