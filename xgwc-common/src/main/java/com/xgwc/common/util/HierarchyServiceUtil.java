package com.xgwc.common.util;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.user.util
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-06-10  16:01
 */
public class HierarchyServiceUtil {

    private HierarchyServiceUtil() {} // 防止实例化
    /**
     * 公共方法，用于获取唯一且具有层级结构的业务实体列表
     *
     * @param allEntities  初始业务实体列表
     * @param idGetter     用于获取实体ID的函数
     * @param pidGetter    用于获取实体父ID的函数
     * @param parentLoader 用于加载父实体的函数
     * @param <T>          实体类型
     * @return 包含唯一且具有层级结构的业务实体列表
     */
    public static  <T> List<T> getUniqueEntitiesWithHierarchy(
            List<T> allEntities,
            Function<T, Long> idGetter,
            Function<T, Long> pidGetter,
            Function<Long, T> parentLoader) {

        if (allEntities == null) {
            return new ArrayList<>(); // 返回空列表而不是null
        }

        Set<Long> uniqueEntityIds = new HashSet<>();
        List<T> result = new ArrayList<>();

        for (T entity : allEntities) {
            collectUniqueEntities(entity, uniqueEntityIds, result, idGetter, pidGetter, parentLoader);
        }

        return result;
    }

    private static  <T> void collectUniqueEntities(
            T entity,
            Set<Long> uniqueEntityIds,
            List<T> result,
            Function<T, Long> idGetter,
            Function<T, Long> pidGetter,
            Function<Long, T> parentLoader) {

        if (entity == null || uniqueEntityIds.contains(idGetter.apply(entity))) {
            return;
        }

        Long id = idGetter.apply(entity);
        if (id == null) {
            return; // 如果ID为null，跳过该实体
        }

        uniqueEntityIds.add(id);
        result.add(entity);

        Long pid = pidGetter.apply(entity);
        if (pid == null || pid == 0) {
            return; // 如果PID为null或0，停止递归
        }

        T parent = parentLoader.apply(pid);
        if (parent != null) {
            collectUniqueEntities(parent, uniqueEntityIds, result, idGetter, pidGetter, parentLoader);
        }
    }
}
