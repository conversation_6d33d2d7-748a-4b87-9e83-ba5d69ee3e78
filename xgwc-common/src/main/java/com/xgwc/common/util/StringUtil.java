package com.xgwc.common.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class StringUtil {

    public static boolean isEmpty(String str) {
            return str == null || str.isEmpty();
    }

    public static boolean isNotEmpty(String str) {
            return !isEmpty(str);
    }
    /**
     * * 判断一个对象是否非空
     *
     * @param object Object
     * @return true：非空 false：空
     */
    public static boolean isNotNull(Object object) {
        return !isNull(object);
    }
    /**
     * * 判断一个对象是否为空
     *
     * @param object Object
     * @return true：为空 false：非空
     */
    public static boolean isNull(Object object) {
        return object == null;
    }
    /**
     * 是否大于0
     */
    public static boolean isGtZero(String str){
        try {
            if (StringUtil.isNotEmpty(str)) {
                return Integer.parseInt(str) > 0;
            }
        }catch (Exception e){

        }
        return false;
    }

    /**
     * 将带逗号的字符串转换成整型数组
     */
    public static List<Integer> convertToIntegerList(String str){
        if(isEmpty(str)){
            return null;
        }
        String[] array = str.split(",");
        List<Integer> resultList = new ArrayList<>();
        for(String s : array){
            Integer result = Integer.parseInt(s);
            resultList.add(result);
        }
        return resultList;
    }

    public static String removeHttp(String url){
        if (!StringUtils.isNotEmpty(url)){
            return url;
        }
        return url.replace("http:","").replace("https:", "");

    }

    /**
     * 去重 并且保证去重之后参数的顺序
     * @param modelIds 列表
     * @return 去重后的参数
     */
    public static String duplicateRemoval(String modelIds){
        if(StringUtils.isNotEmpty(modelIds)) {
            String[] str = modelIds.split(",");
            List<String> list = new ArrayList<>();
            if(str.length > 0) {
                for (String id : str) {
                    if (StringUtils.isNotEmpty(id) && !list.contains(id)) {
                        list.add(id);
                    }
                }
                StringBuilder sb = new StringBuilder();
                for(String  string : list){
                    sb.append(string).append(",");
                }
                return sb.substring(0, sb.length() - 1);
            }
        }
        return "";
    }
    /**
     * 转换为long<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Long toLong(Object value, Long defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        final String valueStr = toStr(value, null);
        if (StringUtils.isEmpty(valueStr)) {
            return defaultValue;
        }
        try {
            // 支持科学计数法
            return new BigDecimal(valueStr.trim()).longValue();
        } catch (Exception e) {
            return defaultValue;
        }
    }
    /**
     * 转换为long<br>
     * 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>
     * 转换失败不会报错
     *
     * @param value 被转换的值
     * @return 结果
     */
    public static Long toLong(Object value) {
        return toLong(value, null);
    }


    /**
     * 转换为int<br>
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static Integer toInt(Object value, Integer defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        final String valueStr = toStr(value, null);
        if (StringUtils.isEmpty(valueStr)) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(valueStr.trim());
        } catch (Exception e) {
            return defaultValue;
        }
    }
    /**
     * 转换为字符串<br>
     * 如果给定的值为null，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static String toStr(Object value, String defaultValue) {
        if (null == value) {
            return defaultValue;
        }
        if (value instanceof String) {
            return (String) value;
        }
        return value.toString();
    }
    /***
     * //使用String的split 方法
     * List<Long>
     * */
    public static List<Integer> convertStrToIntegerList(String str) {
        try {
            String[] strArray = null;
            //拆分字符为"," ,然后把结果交给数组strArray
            strArray = str.split(",");
            return Arrays.stream(str.split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
        } catch (Exception ex) {

        }
        return null;
    }

    /**判断 判断字段真实长度（中文2个字符，英文1个字符）的方法
     */
    public static int getStringLength(String value) {
        if(!isNotEmpty(value)) {
            return 0;
        }
        int valueLength = 0;
        String chinese = "[\u4e00-\u9fa5]";
        for (int i = 0; i < value.length(); i++) {
            String temp = value.substring(i, i + 1);
            if (temp.matches(chinese)) {
                valueLength += 2;
            } else {
                valueLength += 1;
            }
        }
        return valueLength;
    }
    /**
     * int类型设置为null
     */
    public static Integer getIntegerStrNull(Integer value){
        if(value==null || value.equals(0)) {return null;}
        return value;
    }
    /**
     * int类型设置为null
     */
    public static Long getLongStrNull(Long value){
        return value;
    }
    /**
     * i类型设置为null
     */
    public static String getStringSetNull(String value){
        if(value==null || value.trim().isEmpty()) {return null;}
        return value;
    }
    /**
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static BigDecimal getBigDecimalSetDefault(BigDecimal value, BigDecimal defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        try {
            BigDecimal cpZero = new BigDecimal("0");
            if(cpZero.equals(value)){
                return defaultValue;
            }
        } catch (Exception e) {
            return defaultValue;
        }
        return value;
    }
    /**
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static String getBigDecimalDefault(BigDecimal value, BigDecimal defaultValue) {
        if (value == null) {
            return String.valueOf(defaultValue);
        }
        try {
            BigDecimal cpZero = new BigDecimal("0");
            if(cpZero.equals(value)){
                return String.valueOf(defaultValue);
            }
        } catch (Exception e) {
            return String.valueOf(defaultValue);
        }
        return String.valueOf(value);
    }
    /**
     * 如果给定的值为空，或者转换失败，返回默认值<br>
     * 转换失败不会报错
     *
     * @param value        被转换的值
     * @param defaultValue 转换错误时的默认值
     * @return 结果
     */
    public static String getBigDecimalStr(BigDecimal value, String defaultValue) {
        if (value == null) {
            return String.valueOf(defaultValue);
        }
        try {
            BigDecimal cpZero = new BigDecimal("0");
            if(cpZero.equals(value)){
                return String.valueOf(defaultValue);
            }
        } catch (Exception e) {
            return String.valueOf(defaultValue);
        }
        return String.valueOf(value);
    }
    /**
     * 去重 并且保证去重之后参数的顺序
     * @param objs 列表
     * @return 去重后的参数
     */
    public static List<Integer> duplicateRemovalToList(String objs){
        if(StringUtils.isNotEmpty(objs)) {
            String[] str = objs.split(",");
            List<Integer> list = new ArrayList<>();
            for (String id : str) {
                if (StringUtils.isNotEmpty(id) && !list.contains(Integer.parseInt(id))) {
                    list.add(Integer.parseInt(id));
                }
            }
            return list;
        }
        return new ArrayList<>();
    }
    /**
     * 去重 并且保证去重之后参数的顺序
     * @param objs 列表
     * @return 去重后的参数
     */
    public static List<Integer> duplicateRemovalToListRemoveZero(String objs){
        if(StringUtils.isNotEmpty(objs)) {
            String[] str = objs.split(",");
            List<Integer> list = new ArrayList<>();
            for (String id : str) {

                if (StringUtils.isNotEmpty(id) && !list.contains(Integer.parseInt(id)) && !id.equals("0")) {
                    if (Integer.parseInt(id) > 0) {
                        list.add(Integer.parseInt(id));
                    }
                }
            }
            return list;
        }
        return new ArrayList<>();
    }
    /**
     * 去重 并且保证去重之后参数的顺序
     * @param objs 列表
     * @return 去重后的参数
     */
    public static Map<Integer,Integer> duplicateRemovalToMapListRemoveZero(String objs,Integer type) {
        if (StringUtils.isNotEmpty(objs)) {
            String[] str = objs.split(",");
            Map<Integer, Integer> mapList = new HashMap<>();
            for (String id : str) {

                if (StringUtils.isNotEmpty(id) && !mapList.containsKey(Integer.parseInt(id)) && !id.equals("0")) {
                    if (Integer.parseInt(id) > 0) {
                        mapList.put(Integer.parseInt(id), type);
                    }
                }
            }
            return mapList;
        }
        return new HashMap<>();
    }
    /**
     * 去重 并且保证去重之后参数的顺序
     * @param objs 列表
     * @return 去重后的参数
     */
    public static List<Long> duplicateRemovalToLongList(String objs){
        if(StringUtils.isNotEmpty(objs)) {
            String[] str = objs.split(",");
            List<Long> list = new ArrayList<>();
            for (String id : str) {
                if (StringUtils.isNotEmpty(id) && !list.contains(Long.parseLong(id))) {
                    list.add(Long.parseLong(id));
                }
            }
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * 去重 并且转换字符串
     * @param ids 列表
     * @return 去重后的参数
     */
    public static String duplicateRemovalToString(List<Integer> ids){
        if(ids != null && !ids.isEmpty()){
            //先去重
            List<Integer> myList = ids.stream().distinct().collect(Collectors.toList());
            StringBuilder sb = new StringBuilder();
            for(Integer id : myList){
                sb.append(",").append(id);
            }
            return !sb.isEmpty() ? sb.substring(1) : "";
        }
        return "";
    }

    /**
     * 去重 并且转换字符串
     * @param ids 列表
     * @return 去重后的参数
     */
    public static String duplicateRemovalToString2(List<String> ids){
        if(ids != null && !ids.isEmpty()){
            //先去重
            List<String> myList = ids.stream().distinct().collect(Collectors.toList());
            StringBuilder sb = new StringBuilder();
            for(String id : myList){
                sb.append(",").append(id);
            }
            return !sb.isEmpty() ? sb.substring(1) : "";
        }
        return "";
    }
    /**
     * 去重 并且转换字符串
     * @param strVal 列表
     * @return 去重后的参数
     */
    public static List<String> duplicateRemovalToStringList(String strVal){
        if(StringUtils.isNotEmpty(strVal)) {
            String[] str = strVal.split(",");
            List<String> list = new ArrayList<>();
            for (String val : str) {
                if (StringUtils.isNotEmpty(val) && !list.contains(val)) {
                    list.add(val);
                }
            }
            return list;
        }
        return new ArrayList<>();
    }
    /**
     * 去重 并且转换字符串
     * @param strVal 列表
     * @return 去重后的参数
     */
    public static List<String> duplicateRemovalToStringListOnlyUseRobotMsg(String strVal){
        if(StringUtils.isNotEmpty(strVal)) {
            String[] str = strVal.split(",");
            List<String> list = new ArrayList<>();
            for (String val : str) {

                if (StringUtils.isNotEmpty(val) && !list.contains(val)) {
                    if (val.equalsIgnoreCase("all")) {
                        list.add("@all");
                    } else {
                        list.add(val);
                    }
                }
            }
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * 去重 并且转换字符串
     * @param strVal 列表
     * @return 去重后的参数
     */
    public static List<String> duplicateRemovalToStringListAndSplit(String strVal,String splitVal){
        if(StringUtils.isNotEmpty(strVal)) {
            String[] str = strVal.split(splitVal);
            List<String> list = new ArrayList<>();
            for (String val : str) {
                if (StringUtils.isNotEmpty(val) && !list.contains(val)) {
                    list.add(val);
                }
            }
            return list;
        }
        return new ArrayList<>();
    }
    /**
     * 去重 并且转换字符串
     * @param ids 列表
     * @return 去重后的参数
     */
    public static String duplicateRemovalLongToString(List<Long> ids){
        if(ids != null && !ids.isEmpty()){
            //先去重
            List<Long> myList = ids.stream().distinct().collect(Collectors.toList());
            StringBuilder sb = new StringBuilder();
            for(Long id : myList){
                sb.append(",").append(id);
            }
            return !sb.isEmpty() ? sb.substring(1) : "";
        }
        return "";
    }

    /**
     * 逗号分隔的数字字符串转换为Set集合，并进行去重操
     * @param commaSeparatedNumbers  commaSeparatedNumbers
     * @return 数据列表
     */
    public static Set<Integer> convertToSetInteger(String commaSeparatedNumbers) {
        if (StringUtils.isNotEmpty(commaSeparatedNumbers)) {


            // Split the string by comma
            String[] numbers = commaSeparatedNumbers.split(",");

            // Convert the string array to a list and remove duplicates
            List<Integer> numberList = Arrays.stream(numbers)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            // Convert the list to a Set to get unique elements
            Set<Integer> uniqueSet = new HashSet<>(numberList);

            return uniqueSet;
        }
        return new HashSet<>();
    }
    /**
     * 转换价格
     */
    public static String convertPrice(Integer price){
        if(price != null) {
            //没有小数
            DecimalFormat df = new DecimalFormat(",###,##0");
            return df.format(price);
        }
        return "";
    }

    /**
     * 格式化指导价
     */
    public static String convertPrice(String referPrice){
        if(StringUtil.isNotEmpty(referPrice)){
            DecimalFormat df = new DecimalFormat(",###,##0");
            return "¥" + df.format(Double.valueOf(referPrice));
        }
        return "";
    }

    /**
     * 转化位set
     */
    public static  Set<String> convertToSet(String str, String split){
        if(isNotEmpty(str)){
            return new HashSet<>(Arrays.asList(str.split(split)));
        }
        return new HashSet<>();
    }

    /**
     * 格式化指导价
     */
    public static String formatNowMsrp(String nowMsrpStr){
        if (!org.springframework.util.StringUtils.isEmpty(nowMsrpStr)) {
            Double nowMsrp = Double.valueOf(nowMsrpStr);
            DecimalFormat formater = new DecimalFormat("#.00");
            if (nowMsrp == 0) {
                return "";
            } else {
                return formater.format(nowMsrp);
            }
        }
        return "";
    }

    /**
     * 格式话图片尺寸
     */
    public static String formatePhotoSize(String photoSize){
        // 原图大小
        if (StringUtil.isNotEmpty(photoSize) && !"0".equals(photoSize)){
            BigDecimal bigDecimal = new BigDecimal(photoSize);
            bigDecimal = bigDecimal.divide(new BigDecimal(1024 * 1024)).setScale(1, RoundingMode.UP);
            return bigDecimal + "M";
        }else {
            return "";
        }
    }
    /**
     * 版本号
     */
    public static String handleAppVer(String appVer){
        StringBuilder result = new StringBuilder();
        if(StringUtil.isNotEmpty(appVer)){
            String[] array = appVer.split("\\.");
            for (String part : array) {
                String convertedPart = String.format("%03d", Integer.parseInt(part));
                result.append(convertedPart);
            }
        }
        return result.toString();
    }
}
