package com.xgwc.common.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class AspectUtil {

    public static Map<String, Object> getAllParams(String[] paramNames, Object[] paramValues){
        Map<String, Object> allParams = new HashMap<>();
        if(paramValues.length > 0){
            int length = paramNames.length;
            for(int i = 0; i < length; i++){
                if(paramValues[i] == null){
                    allParams.put(paramNames[i].toLowerCase(), paramValues[i]);
                    continue;
                }
                if(checkJavaType(paramValues[i].getClass())){
                    //如果是基础类型
                    allParams.put(paramNames[i].toLowerCase(), paramValues[i]);
                }else{
                    //处理不是基础类型的情况
                    Field[] fields = getAllFields(paramValues[i]);
                    for(Field field : fields){
                        field.setAccessible(true);
                        try {
                            allParams.put(field.getName().toLowerCase(), field.get(paramValues[i]));
                        }catch (Exception e){
                            log.error("redis切面 获取参数所有字段信息失败:", e);
                        }
                    }
                }
            }
        }
        return allParams;
    }

    /**
     * 获取类所有的字段，包括父类
     */
    private static Field[] getAllFields(Object object){
        Class clazz = object.getClass();
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null){
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }

    /**
     * 判断类型是否为java类型
     * 验证是否为java的基本类型或者基本类型的包装类型
     */
    private static Boolean checkJavaType(Class<?> cls) {
        return cls != null && cls.getClassLoader() == null;
    }
}
