package com.xgwc.common.util;

import com.google.common.collect.Maps;
import com.google.common.net.InetAddresses;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Enumeration;
import java.util.Map;

public final class NetUtil {
    private static final Logger logger = LoggerFactory.getLogger(NetUtil.class);
    private static InetAddress localAddress;
    @Getter
    private static String localHost;


    private static void initLocalAddress() {
        NetworkInterface nic = null;

        try {
            localAddress = InetAddress.getLocalHost();
            nic = NetworkInterface.getByInetAddress(localAddress);
        } catch (Exception var4) {
        }

        if (localAddress == null || nic == null || localAddress.isLoopbackAddress() || localAddress instanceof Inet6Address) {
            InetAddress lookedUpAddr = findLocalAddressViaNetworkInterface();

            try {
                localAddress = lookedUpAddr != null ? lookedUpAddr : InetAddress.getByName("127.0.0.1");
            } catch (UnknownHostException var3) {
            }
        }

        localHost = InetAddresses.toAddrString(localAddress);
        logger.info("localhost is {}", localHost);
    }

    private static InetAddress findLocalAddressViaNetworkInterface() {
        String preferNamePrefix = System.getProperty("localhost.prefer.nic.prefix", "bond0.");
        String defaultNicList = System.getProperty("localhost.default.nic.list", "bond0,eth0,em0,br0,ens32");
        Map<String, NetworkInterface> candidateInterfaces = Maps.newHashMap();

        try {
            Enumeration<NetworkInterface> allInterfaces = NetworkInterface.getNetworkInterfaces();

            while (allInterfaces.hasMoreElements()) {
                NetworkInterface nic = allInterfaces.nextElement();

                try {
                    if (!nic.isUp() || !nic.supportsMulticast()) {
                        continue;
                    }
                } catch (SocketException var9) {
                    continue;
                }

                String name = nic.getName();
                if (name.startsWith(preferNamePrefix)) {
                    InetAddress resultAddress = findAvailableInetAddress(nic);
                    if (resultAddress != null) {
                        return resultAddress;
                    }
                } else {
                    candidateInterfaces.put(name, nic);
                }
            }

            for (String nifName : defaultNicList.split(",")) {
                NetworkInterface nic = candidateInterfaces.get(nifName);
                InetAddress resultAddress = findAvailableInetAddress(nic);
                if (resultAddress != null) {
                    return resultAddress;
                }
            }

            return null;
        } catch (SocketException var10) {
            return null;
        }
    }

    private static InetAddress findAvailableInetAddress(NetworkInterface nic) {
        if (nic == null) {
            return null;
        } else {
            Enumeration<InetAddress> indetAddresses = nic.getInetAddresses();

            while (indetAddresses.hasMoreElements()) {
                InetAddress inetAddress = indetAddresses.nextElement();
                if (!(inetAddress instanceof Inet6Address) && !inetAddress.isLoopbackAddress()) {
                    return inetAddress;
                }
            }

            return null;
        }
    }

    private NetUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    static {
        initLocalAddress();
    }
}