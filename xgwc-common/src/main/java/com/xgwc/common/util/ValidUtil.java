package com.xgwc.common.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

public class ValidUtil {

    private static final Pattern PHONE_REGEX = Pattern.compile("^1[3-9]\\d{9}$");

    // 缓存校验结果（可选）
    private static final ConcurrentHashMap<String, AtomicBoolean> cache = new ConcurrentHashMap<>();

    public static boolean isValidPhoneNumber(String phoneNumber) {
        // 检查缓存
        AtomicBoolean cachedResult = cache.get(phoneNumber);
        if (cachedResult != null) {
            return cachedResult.get();
        }
        // 校验手机号
        boolean isValid = PHONE_REGEX.matcher(phoneNumber).matches();
        // 将结果存入缓存
        cache.put(phoneNumber, new AtomicBoolean(isValid));

        return isValid;
    }
}
