package com.xgwc.common.util;


import com.github.pagehelper.PageHelper;
import com.xgwc.common.util.page.PageDomain;
import com.xgwc.common.util.page.TableSupport;
import com.xgwc.common.util.sql.SqlUtil;

/**
 * 分页工具类
 *
 * <AUTHOR>
 */
public class PageUtils extends PageHelper {
    /**
     * 设置请求分页数据
     */
    public static void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        if(pageNum == null || pageNum == 0) pageNum = 1;
        Integer pageSize = pageDomain.getPageSize();
        if(pageSize == null || pageSize == 0) pageSize = 10;

        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        }
    }

    /**
     * 清理分页的线程变量
     */
    public static void clearPage() {
        PageHelper.clearPage();
    }
}
