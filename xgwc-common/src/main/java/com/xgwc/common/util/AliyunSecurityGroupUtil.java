package com.xgwc.common.util;

import com.aliyun.ecs20140526.models.AuthorizeSecurityGroupRequest;
import com.aliyun.tea.TeaException;

public class AliyunSecurityGroupUtil {



    public static com.aliyun.ecs20140526.Client createClient() throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId("LTAI5tCa7R7SUqreZSFweeYc")
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret("******************************");
        // Endpoint 请参考 https://api.aliyun.com/product/Ecs
        config.endpoint = "ecs.cn-shenzhen.aliyuncs.com";
        return new com.aliyun.ecs20140526.Client(config);
    }

    public static void main(String[] args_) throws Exception {
        new AliyunSecurityGroupUtil().addAliyunSecurityGroupIp("********", "test");

    }

    public void addAliyunSecurityGroupIp(String ip, String description) throws Exception{
        addAliyunSecurityGroupItem(ip, "8051/8051", description);
        addAliyunSecurityGroupItem(ip, "443/443", description);
        addAliyunSecurityGroupItem(ip, "80/80", description);
    }

    public void addAliyunSecurityGroupItem(String ip, String port, String des) throws Exception{
        com.aliyun.ecs20140526.Client client = AliyunSecurityGroupUtil.createClient();
        com.aliyun.ecs20140526.models.AuthorizeSecurityGroupRequest.AuthorizeSecurityGroupRequestPermissions permissions0 = new AuthorizeSecurityGroupRequest.AuthorizeSecurityGroupRequestPermissions()
                .setPolicy("accept")
                .setIpProtocol("TCP")
                .setSourceCidrIp(ip)
                .setDescription(des)
                //.setSourceGroupId("sg-wz9g1bq3kqvqtjzjgn5c")
                .setPortRange(port);
        com.aliyun.ecs20140526.models.AuthorizeSecurityGroupRequest authorizeSecurityGroupRequest = new com.aliyun.ecs20140526.models.AuthorizeSecurityGroupRequest()
                .setRegionId("cn-shenzhen")
                .setSecurityGroupId("sg-wz9g1bq3kqvqtjzjgn5c")

                .setPermissions(java.util.Arrays.asList(
                        permissions0
                ));
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            client.authorizeSecurityGroupWithOptions(authorizeSecurityGroupRequest, runtime);
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }

}
