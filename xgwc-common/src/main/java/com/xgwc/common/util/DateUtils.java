package com.xgwc.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
@Slf4j
public class DateUtils {

    private static final String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};


    private final SimpleDateFormat SHORT_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private final SimpleDateFormat LONG_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    //1970-01-01 UTC 时间
    private static final long lng1970 = 5233041986427387904L;

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd）
     */
    public static String getDate() {
        return getDate("yyyy-MM-dd");
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate(String pattern) {
        return DateFormatUtils.format(new Date(), pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 得到日期字符串，转换格式（yyyy-MM-dd）
     */
    public static String formatDate(Date date) {
        return formatDate(date, "yyyy-MM-dd");
    }

    /**
     * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String formatDateTime(Date date) {
        return formatDate(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前时间字符串 格式（HH:mm:ss）
     */
    public static String getTime() {
        return formatDate(new Date(), "HH:mm:ss");
    }

    /**
     * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String getDateTime() {
        return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前年份字符串 格式（yyyy）
     */
    public static String getYear() {
        return formatDate(new Date(), "yyyy");
    }

    /**
     * 得到当前月份字符串 格式（MM）
     */
    public static String getMonth() {
        return formatDate(new Date(), "MM");
    }

    /**
     * 得到当天字符串 格式（dd）
     */
    public static String getDay() {
        return formatDate(new Date(), "dd");
    }

    /**
     * 得到当前星期字符串 格式（E）星期几
     */
    public static String getWeek() {
        return formatDate(new Date(), "E");
    }

    /**
     * 获取过去的天数
     */
    public static long pastDays(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (24 * 60 * 60 * 1000);
    }

    /**
     * 获取过去的小时
     */
    public static long pastHour(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (60 * 60 * 1000);
    }

    /**
     * 获取过去的分钟
     */
    public static long pastMinutes(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (60 * 1000);
    }

    /**
     * 转换为时间（天,时:分:秒.毫秒）
     */
    public static String formatDateTime(long timeMillis) {
        long day = timeMillis / (24 * 60 * 60 * 1000);
        long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
        long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000 - min * 60 * 1000 - s * 1000);
        return (day > 0 ? day + "," : "") + hour + ":" + min + ":" + s + "." + sss;
    }

    /**
     * 计算年龄
     *
     * @param birthDay 生日
     */
    public static int getAge(Long birthDay) {
        // 若出生日期为空，默认返回0
        if (null != birthDay) {

            Date date = new Date(birthDay);
            Calendar cal = Calendar.getInstance();
            if (cal.before(birthDay)) {
                return 0;
            }
            int yearNow = cal.get(Calendar.YEAR);
            int monthNow = cal.get(Calendar.MONTH) + 1;
            int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
            cal.setTime(date);
            int yearBirth = cal.get(Calendar.YEAR);
            int monthBirth = cal.get(Calendar.MONTH);
            int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
            int age = yearNow - yearBirth;
            if (monthNow <= monthBirth) {
                if (monthNow == monthBirth) {
                    if (dayOfMonthNow < dayOfMonthBirth) {
                        age--;
                    }
                } else {
                    age--;
                }
            }
            return age;
        } else {
            return 0;
        }
    }

    public static String timestamp() {
        return formatDate(new Date(), "yyyyMMddHHmmss");
    }

    /**
     * 获取第二天0点的毫秒数
     */
    public static Long getTomorrowTime() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime().getTime();
    }

    /**
     * 获取当前days天以前所有的日期列表
     *
     * @param prefix 前缀
     */
    public static List<String> getPreAllDateByDays(String prefix, int days) {
        List<String> dateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        for (int i = 0; i < days; i++) {
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            dateList.add(prefix + (new SimpleDateFormat("yyyy-MM-dd")).format(calendar.getTime()));
        }
        dateList.add(prefix + getShortDateStr());
        return dateList;
    }

    /**
     * 获取当前短日期
     */
    public static String getShortDateStr() {
        return new DateUtils().SHORT_DATE_FORMAT.format(new Date());
    }

    public static String geCurrentDateStr(String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(new Date());
    }

    /**
     * 得到长日期格式字串
     */
    public static String getLongDateStr() {
        return new DateUtils().LONG_DATE_FORMAT.format(new Date());
    }

    /**
     * 获取几分钟前时间
     */
    public static String getMinutAgoStr(int minuter) {
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(Calendar.MINUTE, -minuter);
        Date beforeD = beforeTime.getTime();
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:00").format(beforeD);
    }

    /**
     * 两个时间相差天数
     */
    public static int diffDay(Date before, Date after) {
        return Integer.parseInt(String.valueOf(((after.getTime() - before.getTime()) / 86400000)));
    }

    /**
     * 计算两个时间差值 方法获取到毫秒数来进行比较 两个时间相差毫秒数
     *
     * @param startDate startDate
     * @param endDate   endDate
     */
    public static long compareToDateBetween(Date startDate, Date endDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        long sTime = calendar.getTimeInMillis();
        calendar.setTime(endDate);
        long endTime = calendar.getTimeInMillis();
        return endTime - sTime;
    }

    /**
     * 计算两个时间差值 方法获取到毫秒数来进行比较 两个时间相差毫秒数
     *
     * @param startDate startDate
     * @param endDate   endDate
     */
    public static int isBefore(String startDate, String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析字符串为LocalDate
        LocalDate date1 = LocalDate.parse(startDate, formatter);
        LocalDate date2 = LocalDate.parse(endDate, formatter);
        return date1.isBefore(date2) ? 1 : -1;
    }

    /**
     * 校验两个时间差
     */
    public static boolean validTwoDate(String before, String after, int diff) {
        try {
            Date startDate = new SimpleDateFormat("yyyyMMddHHmmss").parse(before);
            Date endDate = new SimpleDateFormat("yyyyMMddHHmmss").parse(after);
            int day = DateUtils.diffDay(startDate, endDate);
            return day <= diff;
        } catch (Exception e) {
            log.error("格式化时间错误: startTime:{}, endTime:{}", before, after);
        }
        return false;
    }

    /**
     * 根据UTC时间转换为对应的Date
     *
     * @param netDate UTC时间对应的long值
     * @return 转换后的日期
     * <AUTHOR>
     * @date 2019-09-23
     */
    public static Date getByNetDateUTCTime(long netDate) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        calendar.setTimeInMillis((netDate - lng1970) / 10000);
        calendar.setTimeInMillis(calendar.getTimeInMillis() - calendar.getTimeZone().getRawOffset());
        return calendar.getTime();
    }

    /**
     * 获取时间区间
     *
     * @param minute 时间间隔
     */
    public static List<String> getTimeZoneList(String startTime, String endTime, int minute) {
        List<String> timeList = new ArrayList<>();
        try {
            timeList.add(startTime);
            Calendar cal = Calendar.getInstance();
            cal.setTime(new DateUtils().LONG_DATE_FORMAT.parse(startTime));
            long endTimeStamp = new DateUtils().LONG_DATE_FORMAT.parse(endTime).getTime();
            while (cal.getTimeInMillis() < endTimeStamp) {
                cal.add(Calendar.MINUTE, minute);
                timeList.add(formatDateTime(cal.getTime()));
            }
        } catch (Exception e) {
            log.error("dateUtil getTimeZoneList error:", e);
        }
        return timeList;
    }

    /**
     * 获取前n天时间
     */
    public static String getPreDate(int n) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, n);
        return formatDate(cal.getTime());
    }

    /**
     * 获取前n个月时间
     */
    public static String getPreMonth(int n) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, n);
        return formatDate(cal.getTime());
    }

    /**
     * 获取前n个月时间
     */
    public static String getPreMonth(int n, String format) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, n);
        return formatDate(cal.getTime(), format);
    }

    /**
     * 获取N个月
     */
    public static String getPreviousMonth(String dateStr, int n) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 解析输入日期为 YearMonth
        YearMonth yearMonth = YearMonth.parse(dateStr, formatter);
        // 获取上个月
        YearMonth previousYearMonth = yearMonth.minusMonths(n);
        // 格式化输出
        return previousYearMonth.format(formatter);
    }

    /**
     * 获取每月的最后一天
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int lastDay = calendar.getActualMaximum(Calendar.DATE);
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);
        return calendar.getTime();
    }

    /**
     * 获取当前短日期
     */
    public static String getStartTime(Date nowTime) {
        SimpleDateFormat longDateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        return longDateFormat.format(nowTime);
    }

    /**
     * 获取当前短日期
     */
    public static String getEndTime(Date nowTime) {
        SimpleDateFormat longDateFormat = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        return longDateFormat.format(nowTime);
    }

    /**
     * 将时间戳转换为时间字符串
     */
    public static String convertTimeStampToDateStr(Long timeStamp) {
        if (timeStamp != null && timeStamp > 0) {
            Date date = new Date(timeStamp);
            // 创建SimpleDateFormat对象，指定目标格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 将Date对象格式化为字符串
            return sdf.format(date);
        }
        return null;
    }

    /**
     * 比较两个时间差距多少分钟
     */
    public static long calculateTimeDifferenceInMinutes(String timeStr1, String timeStr2) {
        // 1. 定义时间格式（根据实际字符串格式修改）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 2. 解析字符串为时间对象
        LocalDateTime dateTime1 = LocalDateTime.parse(timeStr1, formatter);
        LocalDateTime dateTime2 = LocalDateTime.parse(timeStr2, formatter);

        // 3. 计算时间差（按分钟）
        return Math.abs(ChronoUnit.MINUTES.between(dateTime1, dateTime2));
    }

    /**
     * 获取当前短日期
     */
    public static String getStartYear(String nowTime) {
        return nowTime + "-01-01 00:00:00";
    }

    /**
     * 获取当前短日期
     */
    public static String getEndYear(String nowTime) {
        return nowTime + "-12-31 23:59:59";
    }

    public static Date getNowDate() {
        return new Date();
    }

    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 将 {@link Date} 转换为 {@link LocalDate}（使用系统默认时区）
     */
    public static LocalDate toLocalDate(Date date) {
        return date != null
                ? date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                : null;
    }

    /**
     * 判断日期是否在当前月份
     */
    public static boolean isDateInCurrentMonth(Date date) {
        if (date == null) return false;
        LocalDate localDate = toLocalDate(date);
        return YearMonth.from(localDate).equals(YearMonth.now());
    }

    /**
     * 判断日期是否在从当前日期开始的三个月内（包含当天）
     */
    public static boolean isWithinNextThreeMonths(Date date) {
        if (date == null) return false;
        LocalDate localDate = toLocalDate(date);
        LocalDate now = LocalDate.now();
        return !localDate.isBefore(now) && !localDate.isAfter(now.plusMonths(3));
    }

    public static Date addMonth(Date date, int months) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, months);
        return calendar.getTime();
    }


    public static Date addDays(Date date, int days) {
        long time = date.getTime() + (long) days * 24 * 60 * 60 * 1000;
        return new Date(time);
    }

    public static Date addDays(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_WEEK, days);
        return calendar.getTime();
    }

    public static Date addWeek(int week) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.WEDNESDAY, week);
        return calendar.getTime();
    }

    public static Date addMonth(int months) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, months);
        return calendar.getTime();
    }

    public static Date addYear(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, year);
        return calendar.getTime();
    }

    public static Date addHour(Date date, int hour) {
        long time = date.getTime() + (long) hour * 60 * 60 * 1000;
        return new Date(time);
    }


    public static Date yearsLater(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, year);
        return calendar.getTime();
    }

    /**
     * 获取second秒之后的时间
     */
    public static String addSeconds(long nowTime, int second) {
        long time = nowTime + second * 1000L;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将Date对象格式化为字符串
        return sdf.format(new Date(time));
    }


    /**
     * 字符串转date
     */
    public static Date converStrToDate(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(dateStr);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 判断在不在当前日期之前
     */
    public static boolean isBeforeNow(String scheduleTime) {
        if (StringUtil.isEmpty(scheduleTime)) {
            return true;
        }
        Date date = DateUtils.converStrToDate(scheduleTime);
        if (date != null) {
            return date.before(new Date());
        }
        return false;
    }

    /**
     * 转换为string
     */
    public static String convertToDateStr(LocalDate localDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDate.format(formatter);
    }

    /**
     * 判断日期是否在范围内， A,B为短日期，C为长日期，判断C是否在A之内
     */
    public static boolean isDateInRange(String dateA, String dateB, String dateC) {
        // 定义日期和时间格式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try {
            // 解析日期A和B为LocalDate
            LocalDate startDate = LocalDate.parse(dateA, dateFormatter);
            LocalDate endDate = LocalDate.parse(dateB, dateFormatter);

            // 解析日期C为LocalDateTime
            LocalDateTime dateTimeC = LocalDateTime.parse(dateC, dateTimeFormatter);

            // 将dateTimeC转换为LocalDate进行比较
            LocalDate datePartOfC = dateTimeC.toLocalDate();

            // 检查datePartOfC是否在startDate和endDate之间（包含边界）
            return !datePartOfC.isBefore(startDate) && !datePartOfC.isAfter(endDate);

        } catch (Exception e) {
            System.err.println("日期格式错误: " + e.getMessage());
            return false;
        }
    }

    public static Date getStartOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    public static Date getEndOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    public static void main(String[] args) {
        System.out.println(geCurrentDateStr("yyyy-MM"));
    }
}
