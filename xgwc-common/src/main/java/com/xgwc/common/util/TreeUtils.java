package com.xgwc.common.util;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TreeUtils {
    /**
     * 树节点接口
     */
    public interface TreeNode<T> {
        T getId();
        T getPid();
        void setChildren(List<? extends TreeNode<T>> children);
    }

    /**
     * 将列表转换为树形结构
     * @param list 源数据列表
     * @param rootId 根节点ID
     * @return 树形结构列表
     */
    public static <T, E extends TreeNode<T>> List<E> listToTree(List<E> list, T rootId) {
        // 按父节点ID分组
        Map<T, List<E>> parentMap = list.stream()
                .filter(node -> !node.getPid().equals(node.getId())) // 过滤掉自己引用自己的情况
                .collect(Collectors.groupingBy(TreeNode::getPid));

        // 设置每个节点的子节点
        list.forEach(node -> node.setChildren(parentMap.get(node.getId())));

        // 获取根节点
        return parentMap.get(rootId);
    }
}
