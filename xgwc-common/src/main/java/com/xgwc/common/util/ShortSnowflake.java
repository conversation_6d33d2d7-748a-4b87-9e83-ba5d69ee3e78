package com.xgwc.common.util;

public class ShortSnowflake {

    private final static long START_TIMESTAMP = 1672531200000L; // 2023-01-01
    private final static long SEQUENCE_BIT = 10;
    private final static long MAX_SEQUENCE = ~(-1L << SEQUENCE_BIT);

    private long sequence = 0L;
    private long lastTimestamp = -1L;

    private static final ShortSnowflake INSTANCE = new ShortSnowflake();

    public static ShortSnowflake getInstance() {
        return INSTANCE;
    }

    public synchronized long nextId() {
        long currentTimestamp = timeGen();

        if (currentTimestamp < lastTimestamp) {
            throw new RuntimeException("Clock moved backwards. Refusing to generate id");
        }

        if (currentTimestamp == lastTimestamp) {
            // 同一毫秒内，序列号自增
            sequence = (sequence + 1) & MAX_SEQUENCE;
            if (sequence == 0) {
                // 当前毫秒序列号用尽，等待下一毫秒
                currentTimestamp = waitNextMill(lastTimestamp);
            }
        } else {
            // 新毫秒开始，序列号重置
            sequence = 0L;
        }

        lastTimestamp = currentTimestamp;
        return (currentTimestamp << SEQUENCE_BIT) | sequence;
    }

    private long waitNextMill(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    private long timeGen() {
        return System.currentTimeMillis() - START_TIMESTAMP;
    }
}
