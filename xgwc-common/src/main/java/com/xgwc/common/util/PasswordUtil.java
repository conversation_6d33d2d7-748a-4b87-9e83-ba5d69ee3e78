package com.xgwc.common.util;

import java.util.regex.Pattern;

/**
 * 作为密码强度校验
 * <AUTHOR>
 */
public class PasswordUtil {

    private static final String SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;':\",./<>?";

    private static final String PASSWORD_REGEX =
            "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[" + Pattern.quote(SPECIAL_CHARS) + "])" + // 包含大小写字母、数字和特殊字符
                    "[a-zA-Z\\d" + Pattern.quote(SPECIAL_CHARS) + "]{8,}$"; // 长度至少8位

    private static final Pattern PASSWORD_PATTERN = Pattern.compile(PASSWORD_REGEX);

    /**
     * 验证密码是否符合要求: 至少8位，包含
     * @param password 待验证的密码
     * @return 验证结果
     */
    public static boolean validate(String password) {
        return PASSWORD_PATTERN.matcher(password).matches();
    }

    public static void main(String[] args) {
        System.out.println(validate("Lw123456.."));
    }

}
