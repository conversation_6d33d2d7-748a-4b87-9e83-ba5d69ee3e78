package com.xgwc.common.util;

/**
 * <AUTHOR>
 */
public class IntegerUtil {

    /**
     * 将 0 置为null
     * @return 整型
     */
    public static Integer zeroToNull(Integer value){
        if(value != null){
            return value == 0 ? null : value;
        }
        return value;
    }

    /**
     * 是否大于0
     */
    public static boolean gtZero(Integer value){
        return value != null && value > 0;
    }
}
