package com.xgwc.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Slf4j
public class ObjectConvertHelper {

    public static Long objectToLong(Object data) {
        try {
            BigDecimal bigDecimal = stringToBigDecimal(data + "");
            return bigDecimal.longValue();
        } catch (Exception ex) {
            log.error("error:", ex);
            return 0L;
        }
    }

    public static String objectToString(Object data) {
        try {
            if (data == null) {
                return "";
            }
            return String.valueOf(data);
        } catch (Exception ex) {
            log.error("error:", ex);
            return "";
        }
    }

    public static int stringToInt(String data) {
        try {
            return Integer.parseInt(data);
        } catch (Exception var2) {
            log.error("error:", var2);
            return 0;
        }
    }

    private static BigDecimal stringToBigDecimal(String data) {
        try {
            return new BigDecimal(data);
        } catch (Exception var2) {
            log.error("error:", var2);
            return BigDecimal.valueOf(0L);
        }
    }

    public static String intToString(int value) {
        try {
            return Integer.toString(value);
        } catch (Exception var2) {
            log.error("error:", var2);
            return "";
        }
    }

    /**
     * 字段串转float,转换失败返回0
     */
    public static float stringToFloat(String data) {
        try {
            if (StringUtils.isEmpty(data) || "暂无".equals(data)) {
                return 0f;
            }
            return Float.parseFloat(data);
        } catch (Exception var2) {
            log.error("error:", var2);
            return 0.0F;
        }
    }

    /**
     * string 转换为double
     */
    public static double stringToDouble(String data, Double defaultVal) {
        try {
            return Double.parseDouble(data);
        } catch (Exception var2) {
            log.error("error:", var2);
            return defaultVal;
        }
    }

    public static String floatToString(float value) {
        try {
            return Float.toString(value);
        } catch (Exception var2) {
            log.error("error:", var2);
            return "";
        }
    }

    public static String getToString(Object obj) {
        if (obj == null) {
            return "";
        } else {
            return obj.toString();
        }
    }

    public static Integer doubleToInteger(Double obj) {
        try {
            if (obj == null) {
                return 0;
            } else {
                return (int) obj.doubleValue();
            }
        } catch (Exception var2) {
            log.error("error:", var2);
            return 0;
        }
    }


    public static Integer doubleToInteger(double obj) {
        try {
            return (int) obj;
        } catch (Exception var2) {
            log.error("error:", var2);
            return 0;
        }
    }
}
