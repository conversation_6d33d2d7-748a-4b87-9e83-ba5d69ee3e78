package com.xgwc.common.util;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.util
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-19  09:07
 */

import com.xgwc.common.annotation.Excel;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

/**
 * Excel导入工具类
 */
public class ExcelImportUtil {


    /**
     * Excel导入方法
     * @param file 上传的Excel文件
     * @param clazz 目标实体类Class对象
     * @param <T> 实体类型
     * @return 解析后的实体列表
     * @throws IOException 文件读取异常
     */
    public static <T> List<T> importExcel(MultipartFile file, Class<T> clazz) throws IOException {
        List<T> dataList = new ArrayList<>();

        try (InputStream is = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(is)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();

            // 获取表头映射关系
            List<FieldMapping> fieldMappings = getFieldMappings(clazz);

            // 跳过表头行
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }

            // 遍历数据行
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                T instance = createInstanceFromRow(row, clazz, fieldMappings);
                dataList.add(instance);
            }
        } catch (Exception e) {
            throw new IOException("Excel导入失败: " + e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 获取字段映射关系
     */
    private static <T> List<FieldMapping> getFieldMappings(Class<T> clazz) {
        List<FieldMapping> mappings = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            Excel excelAnnotation = field.getAnnotation(Excel.class);
            if (excelAnnotation != null) {
                mappings.add(new FieldMapping(excelAnnotation.name(), field));
            }
        }

        // 按sort排序
        mappings.sort(Comparator.comparingInt(m -> m.field.getAnnotation(Excel.class).sort()));

        return mappings;
    }

    /**
     * 从行数据创建实体实例
     */
    private static <T> T createInstanceFromRow(Row row, Class<T> clazz, List<FieldMapping> mappings)
            throws IllegalAccessException, InvocationTargetException, InstantiationException, NoSuchMethodException {
        T instance = clazz.getDeclaredConstructor().newInstance();

        for (int i = 0; i < mappings.size(); i++) {
            if (i >= row.getLastCellNum()) {
                break; // 超出列数则跳过
            }

            FieldMapping mapping = mappings.get(i);
            Cell cell = row.getCell(i);
            Object value = getCellValue(cell, mapping.field.getType());

            if (value != null) {
                mapping.field.setAccessible(true);
                mapping.field.set(instance, value);
            }
        }

        return instance;
    }

    /**
     * 根据字段类型获取单元格值
     */
    private static Object getCellValue(Cell cell, Class<?> fieldType) {
        if (cell == null) {
            return null;
        }

        return switch (cell.getCellType()) {
            case STRING -> convertStringValue(cell.getStringCellValue(), fieldType);
            case NUMERIC -> convertNumericValue(cell.getNumericCellValue(), fieldType);
            case BOOLEAN -> cell.getBooleanCellValue();
            default -> null;
        };
    }

    /**
     * 转换字符串值到目标类型
     */
    private static Object convertStringValue(String value, Class<?> targetType) {
        if (value == null || value.isEmpty()) {
            return null;
        }

        try {
            if (targetType == String.class) {
                return value;
            } else if (targetType == Integer.class || targetType == int.class) {
                return Integer.parseInt(value);
            } else if (targetType == Double.class || targetType == double.class) {
                return Double.parseDouble(value);
            } else if (targetType == Long.class || targetType == long.class) {
                return Long.parseLong(value);
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                return Boolean.parseBoolean(value);
            }
        } catch (NumberFormatException e) {
            return null;
        }

        return null;
    }

    /**
     * 转换数值到目标类型
     */
    private static Object convertNumericValue(double value, Class<?> targetType) {
        if (targetType == String.class) {
            return String.valueOf(value);
        } else if (targetType == Integer.class || targetType == int.class) {
            return (int) value;
        } else if (targetType == Double.class || targetType == double.class) {
            return value;
        } else if (targetType == Long.class || targetType == long.class) {
            return (long) value;
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return value != 0;
        }

        return null;
    }

    /**
     * 字段映射内部类
     */
    private static class FieldMapping {
        String excelHeader;
        Field field;

        FieldMapping(String excelHeader, Field field) {
            this.excelHeader = excelHeader;
            this.field = field;
        }
    }
}
