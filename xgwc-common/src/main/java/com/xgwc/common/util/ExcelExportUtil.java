package com.xgwc.common.util;

import com.xgwc.common.annotation.Excel;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.util
 * @Author: kouwenzhuo
 * @CreateTime: 2025-07-17  19:06
 */
/**
 * Excel导出工具类
 */
public class ExcelExportUtil {


    /**
     * 生成Excel模板
     *
     * @param sheetName Sheet名称
     * @param clazz     数据类型 Class
     * @param response  HttpServletResponse
     * @param downloadFileName 下载文件名(不含时间戳和扩展名)
     */
    public static <T> void exportEmptyTemplate(
            String sheetName,
            Class<T> clazz,
            HttpServletResponse response,
            String downloadFileName) throws IOException {

        // 参数校验
        if (clazz == null) throw new IllegalArgumentException("数据类型 Class 不能为空");
        if (sheetName == null || sheetName.isEmpty()) sheetName = "Sheet1";
        if (downloadFileName == null || downloadFileName.isEmpty()) downloadFileName = "template";

        // 设置响应头
        setExcelResponseHeader(response, downloadFileName);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);

            // 1. 获取所有带有 @Excel 注解的字段
            List<Field> excelFields = getExcelFields(clazz);

            // 2. 创建表头行
            Row headerRow = sheet.createRow(0);
            CellStyle headerStyle = createHeaderStyle(workbook);

            // 3. 填充表头
            for (int i = 0; i < excelFields.size(); i++) {
                Field field = excelFields.get(i);
                Excel excel = field.getAnnotation(Excel.class);
                String headerName = excel.name();

                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headerName);
                cell.setCellStyle(headerStyle);
            }

            // 4. 自动调整列宽
            for (int i = 0; i < excelFields.size(); i++) {
                sheet.autoSizeColumn(i);
                sheet.setColumnWidth(i, Math.min(sheet.getColumnWidth(i) + 2000, 12000));
            }

            // 5. 写入响应流
            workbook.write(response.getOutputStream());
            response.flushBuffer();
        }
    }

    /**
     * 直接导出Excel到HttpServletResponse
     *
     * @param sheetName Sheet名称
     * @param title     表格标题
     * @param basicInfo 基本信息对象
     * @param dataList  业务数据列表
     * @param response HttpServletResponse
     * @param downloadFileName 下载文件名(不含时间戳和扩展名)
     */
    public static <T, K> void exportExcelToResponse(
            String sheetName,
            String title,
            K basicInfo,
            List<T> dataList,
            HttpServletResponse response,
            String downloadFileName // 新增参数，指定下载文件名
    ) throws IOException {

        // 参数校验（在设置响应头之前）
        if (dataList == null || dataList.isEmpty()) {
            throw new IllegalArgumentException("数据列表不能为空");
        }
        if (sheetName == null || sheetName.isEmpty()) {
            sheetName = "Sheet1";
        }
        if (downloadFileName == null || downloadFileName.isEmpty()) {
            downloadFileName = "export";
        }

        // 设置响应头
        setExcelResponseHeader(response, downloadFileName);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);
            AtomicInteger rowNum = new AtomicInteger(0);

            // 1. 添加基本信息
            addBasicInfo(workbook, sheet, basicInfo, rowNum);

            // 2. 添加标题
            addTitle(workbook, sheet, title, rowNum, dataList.get(0).getClass());

            // 3. 添加表头和业务数据
            addData(workbook, sheet, dataList, rowNum);

            // 直接写入响应流
            workbook.write(response.getOutputStream());
            response.flushBuffer();
        } catch (IllegalAccessException e) {
            throw new IOException("生成Excel内容失败", e);
        }
    }

    /**
     * 设置Excel下载响应头
     */
    private static void setExcelResponseHeader(HttpServletResponse response, String fileName) throws IOException {
        // 为文件名添加时间戳
        String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String fullFileName = fileName + "_" + timeStamp + ".xlsx";

        // 更标准的文件名编码处理
        String encodedFileName = "filename*=UTF-8''" +
                java.net.URLEncoder.encode(fullFileName, StandardCharsets.UTF_8.name())
                        .replace("+", "%20");

        // 设置响应头
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;" + encodedFileName);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
    }

    /**
     * 添加基本信息
     */
    private static <K> void addBasicInfo(Workbook workbook, Sheet sheet, K basicInfo, AtomicInteger rowNum)
            throws IllegalAccessException {
        if (basicInfo == null) return;

        List<Field> basicInfoFields = getExcelFields(basicInfo.getClass());
        CellStyle labelStyle = createLabelStyle(workbook);

        for (Field field : basicInfoFields) {
            field.setAccessible(true);
            Excel excel = field.getAnnotation(Excel.class);
            Object value = field.get(basicInfo);

            Row row = sheet.createRow(rowNum.getAndIncrement());
            Cell labelCell = row.createCell(0);
            labelCell.setCellValue(excel.name());
            labelCell.setCellStyle(labelStyle);

            row.createCell(1).setCellValue(value != null ? value.toString() : "");
        }
        rowNum.getAndIncrement(); // 空一行
    }

    /**
     * 添加标题
     */
    private static void addTitle(Workbook workbook, Sheet sheet, String title, AtomicInteger rowNum, Class<?> dataClass) {
        if (title == null || title.isEmpty()) return;

        Row titleRow = sheet.createRow(rowNum.getAndIncrement());
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(title);

        // 合并单元格
        int columnCount = getExcelFields(dataClass).size();
        if (columnCount > 1) {
            sheet.addMergedRegion(new CellRangeAddress(
                    titleRow.getRowNum(),
                    titleRow.getRowNum(),
                    0,
                    columnCount - 1));
        }

        // 设置标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleCell.setCellStyle(titleStyle);
    }

    /**
     * 添加数据表头和内容
     */
    private static <T> void addData(Workbook workbook, Sheet sheet, List<T> dataList, AtomicInteger rowNum)
            throws IllegalAccessException {
        List<Field> dataFields = getExcelFields(dataList.get(0).getClass());

        // 添加表头
        addHeaderRow(workbook, sheet, dataFields, rowNum);

        // 添加数据行
        addDataRows(sheet, dataList, dataFields, rowNum);

        // 调整列宽
        autoSizeColumns(sheet, dataFields.size());
    }

    /**
     * 添加表头行
     */
    private static void addHeaderRow(Workbook workbook, Sheet sheet, List<Field> dataFields, AtomicInteger rowNum) {
        Row headerRow = sheet.createRow(rowNum.getAndIncrement());
        CellStyle headerStyle = createHeaderStyle(workbook);

        for (int i = 0; i < dataFields.size(); i++) {
            Excel excel = dataFields.get(i).getAnnotation(Excel.class);
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(excel.name());
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 添加数据行（支持日期格式化）
     */
    private static <T> void addDataRows(Sheet sheet, List<T> dataList, List<Field> dataFields, AtomicInteger rowNum)
            throws IllegalAccessException {
        Map<String, CellStyle> dateStyles = new HashMap<>(); // 缓存日期样式

        for (T data : dataList) {
            Row row = sheet.createRow(rowNum.getAndIncrement());
            for (int i = 0; i < dataFields.size(); i++) {
                Field field = dataFields.get(i);
                field.setAccessible(true);
                Object value = field.get(data);

                Excel excel = field.getAnnotation(Excel.class);
                String dateFormat = excel.dateFormat();

                Cell cell = row.createCell(i);

                if (value instanceof Date && !dateFormat.isEmpty()) {
                    // 处理日期字段
                    CellStyle dateStyle = dateStyles.computeIfAbsent(dateFormat, fmt -> {
                        CellStyle style = sheet.getWorkbook().createCellStyle();
                        CreationHelper createHelper = sheet.getWorkbook().getCreationHelper();
                        style.setDataFormat(createHelper.createDataFormat().getFormat(fmt));
                        return style;
                    });
                    cell.setCellStyle(dateStyle);
                    cell.setCellValue((Date) value);
                } else {
                    // 处理普通字段
                    cell.setCellValue(value != null ? value.toString() : "");
                }
            }
        }
    }

    /**
     * 自动调整列宽
     */
    private static void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
            // 防止自动调整列宽失效
            sheet.setColumnWidth(i, Math.min(sheet.getColumnWidth(i) + 2000, 8000));
        }
    }

    /**
     * 创建表头样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.BLUE_GREY.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    /**
     * 创建标签样式
     */
    private static CellStyle createLabelStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    /**
     * 获取带有@Excel注解的字段，并按order排序
     */
    private static List<Field> getExcelFields(Class<?> clazz) {
        List<Field> excelFields = new ArrayList<>();

        // 获取当前类及父类的所有字段
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            for (Field field : currentClass.getDeclaredFields()) {
                if (field.isAnnotationPresent(Excel.class)) {
                    excelFields.add(field);
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        // 按@Excel的order排序
        excelFields.sort(Comparator.comparingInt(f -> f.getAnnotation(Excel.class).sort()));

        return excelFields;
    }
}
