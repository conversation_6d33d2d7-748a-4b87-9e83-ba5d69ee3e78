package com.xgwc.common.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
public class Md5Util {

    /**
     * 获取md5的值
     */
    public static String getMd5(String message) {
        return DigestUtils.md5Hex(message);
    }

    /**
     * 获取 map排序后md5的值,map值前和后都加secret的值
     *
     * @param secret 加密key
     */
    public static String getMd5ByMap(Map<String, String> map, String secret) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!StringUtils.isEmpty(secret)) {
            stringBuilder.append(secret);
        }
        TreeMap treeMap = new TreeMap(map);

        for (Object o : treeMap.entrySet()) {
            Map.Entry entity = (Map.Entry) o;
            stringBuilder.append(entity.getKey()).append(entity.getValue());
        }

        if (!StringUtils.isEmpty(secret)) {
            stringBuilder.append(secret);
        }
        String strMd5 = stringBuilder.toString();
        return getMd5(strMd5);
    }

    /**
     * 获取 map排序后md5的值,map值尾部加secret的值
     * @param secret 加密key
     */
    public static String getMd5ByMapLastSecret(Map<String, String> map, String secret) {
        StringBuilder stringBuilder = new StringBuilder();
        TreeMap<String, String> treeMap = new TreeMap(map);

        for (Map.Entry<String, String> stringStringEntry : treeMap.entrySet()) {
            stringBuilder.append(((Map.Entry) stringStringEntry).getKey()).append(((Map.Entry) stringStringEntry).getValue());
        }
        if (!StringUtils.isEmpty(secret)) {
            stringBuilder.append(secret);
        }
        String strMd5 = stringBuilder.toString();
        return getMd5(strMd5);
    }

    public static String bytesToHexString(byte[] bytes) {
        StringBuilder stringBuilder = new StringBuilder();
        if (bytes != null && bytes.length > 0) {
            for (byte aByte : bytes) {
                int v = aByte & 255;
                String hv = Integer.toHexString(v);
                if (hv.length() < 2) {
                    stringBuilder.append(0);
                }

                stringBuilder.append(hv);
            }

            return stringBuilder.toString();
        } else {
            return null;
        }
    }


    /**
     * SHA1算法
     * @return String
     */
    public static String getsha1Encode(String str) {
        if (str == null) {
            return null;
        }
        try {
            // MD5
            String algorithm = "SHA1";
            MessageDigest messageDigest = MessageDigest.getInstance(algorithm);
            messageDigest.update(str.getBytes());
            return getFormattedText(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    /**
     * Takes the raw bytes from the digest and formats them correct.
     *
     * @param bytes the raw bytes from the digest.
     * @return the formatted bytes.
     */
    private static String getFormattedText(byte[] bytes) {
        int len = bytes.length;
        StringBuilder buf = new StringBuilder(len * 2);
        // 把密文转换成十六进制的字符串形式
        for (byte aByte : bytes) {
            buf.append(HEX_DIGITS[(aByte >> 4) & 0x0f]);
            buf.append(HEX_DIGITS[aByte & 0x0f]);
        }
        return buf.toString();
    }



}
