package com.xgwc.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class ImageUtil {

    /**
     * 原始图片裁切
     */
    public static String getUrl(String url){
        if(StringUtils.isNotEmpty(url)){
            return url.concat("?x-oss-process=image/resize,w_{0}");
        }
        return "";
    }

    /**
     * webp图片
     */
    public static String getUrlWp(String url){
        if(StringUtils.isNotEmpty(url)){
            return url.concat("?x-oss-process=image/resize,w_{0},image/format,webp");
        }
        return "";
    }

    /**
     * 原始图片裁切
     * @param width 图片宽度
     */
    public static String getUrl(String url, int width){
        if(StringUtils.isNotEmpty(url)){
            return url.concat("?x-oss-process=image/resize,w_" + width);
        }
        return "";
    }

    /**
     * webp图片
     * @param width 图片宽度
     */
    public static String getUrlWp(String url, int width){
        if(StringUtils.isNotEmpty(url)){
            return url.concat("?x-oss-process=image/resize,w_" + width + ",image/format,webp");
        }
        return "";
    }




}
