package com.xgwc.common.util;

import com.xgwc.common.exception.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.Security;

/**
 * <AUTHOR>
 */
@Slf4j
public class ParamDecryptUtil {

    /**
     * 字符编码
     */
    public static final Charset CHAR_SET = StandardCharsets.UTF_8;

    /**
     * 解密key 值
     */
    private static final String KEY = "20mk22w01y12ss1q";

    /**
     * 解密key 值
     */
    public static final String PHONE_KEY = "lZnp8IJ6rw7GkKgV9hxw";

    /**
     * 加盟商 解密key 值
     */
    public static final String BRAND_KEY = "a7QmX2KpL9TgN5RvH";

    /**
     * 设计师 解密key 值
     */
    public static final String DESIGNER_KEY = "ZbY8WnQ4RtLfJ1HcM";

    /**
     * 初始化 Cipher
     */
    static {
        try {
            Security.addProvider(new BouncyCastleProvider());
        } catch (Exception e){
            log.error("加解密工具 初始化AES工具类异常, 异常原因: ", e);
        }
    }




    /**
     * 解密参数
     * @param param 加密参数
     * @return 解密后参数
     */
    public static String decryptParam(String param){
        String unsafeStr = param.replace('-', '+').replace('_', '/');
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(2, initKey(KEY));
            byte[] decryptResult = cipher.doFinal(Base64.decodeBase64(unsafeStr.getBytes(CHAR_SET)));
            return new String(decryptResult, CHAR_SET);
        } catch (Exception e){
            log.info("AES解密参数异常, 异常原因: param = {}, 异常信息:", param, e);
        }
        return  param;
    }

    public static String decryptParam(String param, String key){
        String unsafeStr = param.replace('-', '+').replace('_', '/');
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(2, initKey(key));
            byte[] decryptResult = cipher.doFinal(Base64.decodeBase64(unsafeStr.getBytes(CHAR_SET)));
            return new String(decryptResult, CHAR_SET);
        } catch (Exception e){
            log.info("AES解密参数异常, 异常原因: param = {}, 异常信息:", param, e);
        }
        return  param;
    }

    /**
     * 手机号解密
     * @param param
     * @return
     */
    public static String decryptPhone(String param){
        String unsafeStr = param.replace('-', '+').replace('_', '/');
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(2, initKey(PHONE_KEY));
            byte[] decryptResult = cipher.doFinal(Base64.decodeBase64(unsafeStr.getBytes(CHAR_SET)));
            String phone =  new String(decryptResult, CHAR_SET);
            return hidePhoneByRegular(phone);
        } catch (Exception e){
            log.info("AES解密参数异常, 异常原因: param = {}, 异常信息:", param, e);
        }
        return  param;
    }

    /**
     * 手机号是否脱敏
     * @param phone
     * @return
     */
    static String hidePhoneByRegular(String phone){
        if(StringUtils.isNotBlank(phone) && phone.length() == 11) {
            return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2");
        }
        return phone;
    }

    /**
     * aes加密
     * @param content 内容
     * @return 加密后数据
     */
    public static String encrypt(String content) {
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(1, initKey(KEY));
            byte[] decryptResult = cipher.doFinal(content.getBytes(CHAR_SET));
            return new String (Base64.encodeBase64(decryptResult), CHAR_SET);
        } catch (Exception e) {
            log.error("加密数据失败");
        }
        return null;
    }

    public static String encryptPhone(String content) {
        return encrypt(content, PHONE_KEY);
    }
    /**
     * aes加密
     * @param content 内容
     * @return 加密后数据
     */
    public static String encrypt(String content, String key) {
        try {
            if(PHONE_KEY.equals(key)) {
                if(!isNumeric1(content)) {
                    log.error("加密数据不是手机号");
                    return content;
                }
            }
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(1, initKey(key));
            byte[] decryptResult = cipher.doFinal(content.getBytes(CHAR_SET));
            return new String (Base64.encodeBase64(decryptResult), CHAR_SET);
        } catch (Exception e) {
            log.error("加密数据失败");
        }
        return null;
    }

    public static boolean isNumeric1(String str) {
        try {
            Long.valueOf(str);
            return true;
        } catch(Exception e){
            return false;
        }
    }
    /**
     * 生成偏移规则
     * @param key 秘钥
     * @return SecretKeySpec
     */
    private static SecretKeySpec initKey(String key) {
        byte[] keys = key.getBytes(CHAR_SET);
        byte[] bytes = new byte[128 / 8];
        for (int i = 0; i < bytes.length; i++) {
            if (keys.length > i) {
                bytes[i] = keys[i];
            } else {
                bytes[i] = 0;
            }
        }
        return new SecretKeySpec(bytes, "AES");
    }

    // 通用加密/解密方法 true-加密 false-解密
    public static String encryptField(String plainText, String fieldName, boolean isDecrypt) {
        if (StringUtils.isBlank(plainText)) {
            //log.warn("字段为空 字段名:{}", fieldName);
            return null;
        }
        try {
            if (isDecrypt) {
                return ParamDecryptUtil.encrypt(plainText, ParamDecryptUtil.BRAND_KEY);
            }
            return ParamDecryptUtil.decryptParam(plainText, ParamDecryptUtil.BRAND_KEY);
        } catch (ApiException e) {
            throw new ApiException("加密/解密失败", e);
        }
    }

    public static void main(String[] args) {
        System.out.println(decryptParam("NzJjABJkWI6J14n3ieExpw==",PHONE_KEY));
    }
}
