package com.xgwc.common.util;

import com.xgwc.common.constants.CommonConstant;
import com.xgwc.common.constants.ResultCode;
import com.xgwc.common.entity.SysUser;
import com.xgwc.common.exception.ApiException;
import com.xgwc.common.security.mnodel.SysUserDetails;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Set;



public class SecurityUtils {


    public static UserDetails getUserDetails() {
        try {
            return (UserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new ApiException(ResultCode.FORBIDDEN);
        }
    }

    public static SysUserDetails getSysUserDetails() {
        try {
            return (SysUserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new ApiException(ResultCode.UNAUTHORIZED);
        }
    }

    /**
     * 是否超级管理员
     * <p>
     * 超级管理员忽视任何权限判断
     */
    public static boolean isRoot() {
        Set<String> roles = getRoles();
        return roles.contains(CommonConstant.SUPER_ADMIN_ROOT);
    }

    /**
     * 获取当前 用户id
     *
     * @return 用户id
     */
    public static Long getUserId() {
        return getSysUser().getUserId();
    }

    /**
     * 获取昵称
     */
    public static String getNickName() {
        return getSysUser().getUserName();
    }


    public static Set<String> getRoles() {
        return getSysUserDetails().getRoles();
    }

    public static SysUser getSysUser() {
        try {
            SecurityContext ctx = SecurityContextHolder.getContext();
            Authentication auth = ctx.getAuthentication();
            SysUserDetails sysUserDetails = (SysUserDetails) auth.getPrincipal();
            return sysUserDetails.getSysUser();
        } catch (Exception e) {
            throw new ApiException(ResultCode.UNAUTHORIZED);
        }
    }

    // 支持免登接口的请求
    public static SysUser getUserSupportNotLogin() {
        try {
            return SecurityUtils.getSysUser();
        } catch (Exception e) {
            return new SysUser();
        }
    }

//    public static Long getDeptId() {
//        return getSysUserDetails().getSysUser().getDeptId();
//    }
}
