package com.xgwc.common.util.page;


import com.alibaba.fastjson.JSONObject;
import com.xgwc.common.util.StringUtils;
import com.xgwc.common.util.servlet.ServletUtils;

/**
 * 表格数据处理
 *
 * <AUTHOR>
 */
public class TableSupport {
    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 分页参数合理化
     */
    public static final String REASONABLE = "reasonable";

    /**
     * 封装分页对象
     */
    public static PageDomain getPageDomain() {
        PageDomain pageDomain = new PageDomain();
        String body = ServletUtils.getBody(ServletUtils.getRequest());
        Integer pageNum = ServletUtils.getParameterToInt(PAGE_NUM);
        Integer pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
        if(StringUtils.isNotEmpty(body)) {
            JSONObject json = JSONObject.parseObject(body);
            if(pageNum == null && json.getInteger(PAGE_NUM) != null) pageNum = json.getInteger(PAGE_NUM);
            if(pageSize == null && json.getInteger(PAGE_SIZE) != null) pageSize = json.getInteger(PAGE_SIZE);
        }
        pageDomain.setPageNum(pageNum);
        pageDomain.setPageSize(pageSize);
        pageDomain.setOrderByColumn(ServletUtils.getParameter(ORDER_BY_COLUMN));
        pageDomain.setIsAsc(ServletUtils.getParameter(IS_ASC));
        pageDomain.setReasonable(ServletUtils.getParameterToBool(REASONABLE));
        return pageDomain;
    }

    public static PageDomain buildPageRequest() {
        return getPageDomain();
    }
}
