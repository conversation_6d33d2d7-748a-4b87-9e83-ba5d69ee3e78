package com.xgwc.common.util;

import com.google.common.collect.Maps;
import com.xgwc.common.exception.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.BeansException;
import org.springframework.beans.FatalBeanException;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class BizUtils {

    private static final Map<String, PropertyDescriptor> propertyDescriptorCache = Maps.newHashMap();
    private static final Map<Class<?>, PropertyDescriptor[]> classPropertyDescriptorCache = Maps.newHashMap();


    public static void assertByFlag(boolean flag, String str) {
        // 断言
        if (!flag) {
            throw new ApiException(str);
        }
    }

    public static void copyProperties(Object source, Object target) {
        try {
            copyPropertiesInternal(source, target);
        } catch (Exception e) {
            log.error("copyProperties fail,target class={},source class={}", target.getClass(), source.getClass(), e);
        }
    }

    public static void copyPropertiesIgnoreNull(Object source, Object target) {
        try {
            copyPropertiesInternal(source, target, getNullPropertyNames(source));
        } catch (Exception e) {
            log.error("copyPropertiesIgnoreNull fail,target class={},source class={}", target.getClass(), source.getClass(), e);
        }
    }

    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 从spring copy的代码 参考org.springframework.beans.BeanUtils
     */
    private static void copyPropertiesInternal(Object source, Object target, @Nullable String... ignoreProperties) throws BeansException {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");
        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);
        List<String> ignoreList = (ignoreProperties != null ? Arrays.asList(ignoreProperties) : null);

        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            if (writeMethod != null && (ignoreList == null || !ignoreList.contains(targetPd.getName()))) {
                PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), targetPd.getName());
                if (sourcePd == null) {
                    continue;
                }
                Method readMethod = sourcePd.getReadMethod();
                if (readMethod == null) {
                    continue;
                }
                try {
                    if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                        readMethod.setAccessible(true);
                    }
                    Object value = readMethod.invoke(source);
                    if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                        writeMethod.setAccessible(true);
                    }
                    Class writeMethodParameterType = writeMethod.getParameterTypes()[0];
                    if (ClassUtils.isAssignable(writeMethodParameterType, readMethod.getReturnType())) {
                        writeMethod.invoke(target, value);
                    } else if (readMethod.getReturnType().isEnum() && writeMethodParameterType.getName().equals("java.lang.String")) {
                        writeMethod.invoke(target, value.toString());
                    } else if (writeMethodParameterType.isEnum() && readMethod.getReturnType().getName().equals("java.lang.String")) {
                        try {
                            writeMethod.invoke(target, Enum.valueOf(writeMethodParameterType, value.toString()));
                        } catch (Exception e) {
                            log.warn("{}不能转为{}", value, writeMethodParameterType, e);
                        }
                    }
                } catch (Throwable ex) {
                    throw new FatalBeanException(
                            "Could not copy property '" + targetPd.getName() + "' from source to target", ex);
                }

            }
        }
    }

    private static PropertyDescriptor[] getPropertyDescriptors(Class<?> clazz) {
        PropertyDescriptor[] propertyDescriptors = classPropertyDescriptorCache.get(clazz);
        if (propertyDescriptors == null) {
            synchronized (clazz) {
                if (propertyDescriptors == null) {
                    propertyDescriptors = org.springframework.beans.BeanUtils.getPropertyDescriptors(clazz);
                    classPropertyDescriptorCache.put(clazz, propertyDescriptors);
                }

            }
        }
        return propertyDescriptors;
    }

    private static PropertyDescriptor getPropertyDescriptor(Class<?> clazz, String propertyName) {
        String key = clazz.getName() + "." + propertyName;
        PropertyDescriptor propertyDescriptor = propertyDescriptorCache.get(key);
        if (propertyDescriptor == null) {
            synchronized (clazz) {
                if (propertyDescriptor == null) {
                    propertyDescriptor = org.springframework.beans.BeanUtils.getPropertyDescriptor(clazz, propertyName);
                    propertyDescriptorCache.put(key, propertyDescriptor);
                }

            }
        }
        return propertyDescriptor;
    }

}
