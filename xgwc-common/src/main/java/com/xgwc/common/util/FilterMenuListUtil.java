package com.xgwc.common.util;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.util
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-07-16  14:44
 */
public class FilterMenuListUtil {

    /**
     * 只取菜单最后一级数据
     * @param sourceList 源列表
     * @param externalCondition 筛选条件（Mapper）
     * @param <T> 列表元素类型
     * @return 过滤后的列表
     */
    public static <T> List<T> filterList(
            List<T> sourceList,
            Function<T, Boolean> externalCondition
    ) {
        return sourceList.stream()
                .filter(item -> !externalCondition.apply(item))  // 默认取反
                .collect(Collectors.toList());
    }
}
