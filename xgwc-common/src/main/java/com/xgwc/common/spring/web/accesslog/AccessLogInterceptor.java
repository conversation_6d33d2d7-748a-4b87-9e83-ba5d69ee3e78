package com.xgwc.common.spring.web.accesslog;

import com.xgwc.common.entity.SysUser;
import com.xgwc.common.spring.httpwrapper.CachedBodyHttpServletRequest;
import com.xgwc.common.spring.httpwrapper.CachedBodyHttpServletResponse;
import com.xgwc.common.util.NetUtil;
import com.xgwc.common.util.SecurityUtils;
import com.xgwc.common.util.StringUtils;
import com.xgwc.common.util.json.JsonUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Date;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.EMPTY;


/**
 * 日志拦截器
 */
@Slf4j
@Component
public class AccessLogInterceptor implements HandlerInterceptor {

    private static final ThreadLocal<Long> START_TIME_THREAD_LOCAL = new NamedThreadLocal<>("ThreadLocal StartTime");

    // 考虑列表接口的responseBody数据量非常大, 且列表接口众多, 改为默认关闭, 按接口维度按需开启
    @Value("${accesslog.responseBody.url:}")
    private String responseBodyUrl;
    @Value("${accesslog.requestBody.ignoreUrl:}")
    private String requestBodyIgnoreUrl;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        START_TIME_THREAD_LOCAL.set(System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) {
        Long beginTime = START_TIME_THREAD_LOCAL.get();
        START_TIME_THREAD_LOCAL.remove();
        try {
            this.saveLog(request, response, ex, beginTime, System.currentTimeMillis());
        } catch (Exception e) {
            log.error("访问日志构建异常, 急需处理 :", e);
        }
    }

    /**
     * 保存日志
     */
    public void saveLog(HttpServletRequest request, HttpServletResponse response, Exception ex, long startTime, long endTime) {
        AccessLog accessLogEntity = buildLog(request, response, ex, startTime, endTime);
        saveLog(accessLogEntity);
    }

    private AccessLog buildLog(HttpServletRequest request, HttpServletResponse response, Exception ex, long startTime, long endTime) {
        AccessLog accessLog = new AccessLog();
        accessLog.setStartTime(new Date(startTime));
        accessLog.setEndTime(new Date(endTime));
        accessLog.setResponseTime(endTime - startTime);
        Long userId = getCurrentUser().getUserId();
        accessLog.setUri(request.getRequestURI());
        accessLog.setUserId(Objects.requireNonNullElse(userId, -1L)); // "anon"

        accessLog.setParamsMap(request.getParameterMap());
        if (StringUtils.stringToSet(responseBodyUrl).contains(accessLog.getUri())) {
            accessLog.setResponseBody(getResponseBody(response));
        }
        if (!StringUtils.stringToSet(requestBodyIgnoreUrl).contains(accessLog.getUri())) {
            accessLog.setRequestBody(getRequestBody(request));
        }
        accessLog.setUserAgent(request.getHeader("user-agent"));
        accessLog.setMethod(request.getMethod());
        accessLog.setThread(Thread.currentThread().getName());
        accessLog.setHost(NetUtil.getLocalHost());
        accessLog.setIp(getRemoteAddr(request));
        accessLog.setHttpCode(response.getStatus());
        if (ex != null) {
            accessLog.setExceptionClass(ex.getClass().getName());
            accessLog.setException(ex.getMessage());
        }
        return accessLog;
    }

    private static SysUser getCurrentUser() {
        try {
            return SecurityUtils.getSysUser();
        } catch (Exception e) {
            log.debug("访问日志获取当前登录人异常;", e);
        }
        // 支持免登接口的请求
        return new SysUser();
    }

    public static String getRemoteAddr(HttpServletRequest request) {
        if (request == null) {
            return EMPTY;
        }
        String remoteAddr = request.getHeader("X-Real-IP");
        if (StringUtils.isNotBlank(remoteAddr)) {
            return remoteAddr;
        }
        remoteAddr = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(remoteAddr)) {
            return StringUtils.split(remoteAddr, ",")[0];
        }
        remoteAddr = request.getHeader("Proxy-Client-IP");
        if (StringUtils.isNotBlank(remoteAddr)) {
            return remoteAddr;
        }
        remoteAddr = request.getHeader("WL-Proxy-Client-IP");
        return StringUtils.isNotBlank(remoteAddr) ? remoteAddr : request.getRemoteAddr();
    }

    public void saveLog(AccessLog accessLog) {
        log.info(JsonUtils.toJsonString(accessLog));
    }

    private static String getRequestBody(HttpServletRequest request) {
        if (request instanceof CachedBodyHttpServletRequest) {
            return ((CachedBodyHttpServletRequest) request).getBody();
        }
        return EMPTY;
    }

    /**
     * Description: 获取response body
     *
     * @since JDK 1.8
     */
    private static String getResponseBody(HttpServletResponse response) {
        if (response instanceof CachedBodyHttpServletResponse) {
            return ((CachedBodyHttpServletResponse) response).getContent();
        }
        return EMPTY;
    }


}