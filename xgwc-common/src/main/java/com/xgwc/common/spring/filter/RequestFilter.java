package com.xgwc.common.spring.filter;

import com.xgwc.common.spring.httpwrapper.CachedBodyHttpServletRequest;
import com.xgwc.common.spring.httpwrapper.CachedBodyHttpServletResponse;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
public class RequestFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        filterChain.doFilter(new CachedBodyHttpServletRequest(request), new CachedBodyHttpServletResponse(response));
    }

}