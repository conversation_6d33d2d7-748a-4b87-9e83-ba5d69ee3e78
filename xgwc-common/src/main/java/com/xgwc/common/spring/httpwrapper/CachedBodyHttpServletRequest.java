package com.xgwc.common.spring.httpwrapper;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.Getter;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.util.stream.Collectors;

@Getter
public class CachedBodyHttpServletRequest extends HttpServletRequestWrapper {

    private String body;

    public static boolean hasFileUpload(HttpServletRequest request) {
        return request.getContentType() != null &&
                request.getContentType().toLowerCase().startsWith("multipart/form-data");
    }

    public CachedBodyHttpServletRequest(HttpServletRequest request) {
        super(request);
        try {
            if (!hasFileUpload(request)) {
                body = request.getReader().lines().collect(Collectors.joining());
            } else {
                body = "";
            }
        } catch (Exception e) {
            body = "";
        }
    }

    @Override
    public BufferedReader getReader() {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() {
        final ByteArrayInputStream bais = new ByteArrayInputStream(body.getBytes());
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener listener) {

            }

            @Override
            public int read() {
                return bais.read();
            }
        };
    }

}