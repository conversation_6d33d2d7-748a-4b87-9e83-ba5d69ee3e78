package com.xgwc.common.spring.web.accesslog;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

/**
 * 请求日志
 */
@Getter
@Setter
public class AccessLog {

    // 请求开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date startTime;
    // 请求结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date endTime;
    // 请求耗时, 毫秒
    private Long responseTime;
    private Long userId;
    private String uri;
    private String params;
    private String requestBody;
    private String responseBody;
    private String method;
    private String thread;
    private String host;
    private String ip;
    private String userAgent;
    private String exceptionClass;
    private String exception;
    private Integer httpCode;

    /**
     * 设置请求参数
     */
    public void setParamsMap(Map<String, String[]> paramMap) {
        if (paramMap == null) {
            return;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, String[]> param : paramMap.entrySet()) {
            stringBuilder.append("".contentEquals(stringBuilder) ? "" : '&').append(param.getKey()).append('=');
            String paramValue = (param.getValue() != null && param.getValue().length > 0 ? param.getValue()[0] : "");
            stringBuilder.append(paramValue);
        }
        this.params = stringBuilder.toString();
    }

}