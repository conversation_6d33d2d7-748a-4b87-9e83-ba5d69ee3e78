package com.xgwc.common.spring.httpwrapper;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import org.apache.commons.io.output.TeeOutputStream;
import org.jetbrains.annotations.NotNull;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.Writer;

public class CachedBodyHttpServletResponse extends HttpServletResponseWrapper {
    private TeeServletOutputStream teeStream;
    private ByteArrayOutputStream bos;
    private MyWriter myWriter;

    public CachedBodyHttpServletResponse(HttpServletResponse response) {
        super(response);
    }

    public String getContent() {
        if (bos != null) {
            return bos.toString();
        }
        if (myWriter != null) {
            return myWriter.getContent();
        }
        return "";
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        myWriter = new MyWriter(super.getWriter());
        return myWriter;
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (teeStream == null) {
            bos = new ByteArrayOutputStream();
            teeStream = new TeeServletOutputStream(getResponse().getOutputStream(), bos);
        }
        return teeStream;
    }

    @Override
    public void flushBuffer() throws IOException {
        super.flushBuffer();
        if (teeStream != null) {
            teeStream.flush();
        }
    }

    public static class TeeServletOutputStream extends ServletOutputStream {

        private final TeeOutputStream targetStream;

        TeeServletOutputStream(OutputStream one, OutputStream two) {
            targetStream = new TeeOutputStream(one, two);
        }

        @Override
        public void write(int arg0) throws IOException {
            this.targetStream.write(arg0);
        }

        @Override
        public void flush() throws IOException {
            super.flush();
            this.targetStream.flush();
        }

        @Override
        public void close() throws IOException {
            super.close();
            this.targetStream.close();
        }

        @Override
        public boolean isReady() {
            return false;
        }

        @Override
        public void setWriteListener(WriteListener listener) {
            // ignore
        }
    }

    public static class MyWriter extends PrintWriter {
        private final StringBuilder buffer;

        public MyWriter(Writer out) {
            super(out);
            buffer = new StringBuilder();
        }

        @Override
        public void write(@NotNull char[] buf, int off, int len) {
            char[] dest = new char[len];
            System.arraycopy(buf, off, dest, 0, len);
            buffer.append(dest);
        }

        @Override
        public void write(@NotNull char[] buf) {
            buffer.append(buf);
            super.write(buf);
        }

        @Override
        public void write(int c) {
            buffer.append(c);
            super.write(c);
        }

        @Override
        public void write(@NotNull String s, int off, int len) {
            super.write(s, off, len);
        }

        @Override
        public void write(@NotNull String s) {
            buffer.append(s);
            super.write(s);
        }

        public String getContent() {
            return buffer.toString();
        }

    }

}