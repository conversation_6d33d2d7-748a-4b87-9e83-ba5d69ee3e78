package com.xgwc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-04-22  17:29
 */
@Getter
@AllArgsConstructor
public enum XgwcShopEnum {
    ENABLE(1, "启用"),
    DISABLE(0, "禁用");

    private Integer code;
    private String text;


    public static String getTextByCode(Integer code) {
        for (XgwcShopEnum item : XgwcShopEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.text;
            }
        }
        return null;
    }

    public static Integer getCodeByText(String text) {
        for (XgwcShopEnum item : XgwcShopEnum.values()) {
            if (item.getText().equals(text)) {
                return item.code;
            }
        }
        return null;
    }

}
