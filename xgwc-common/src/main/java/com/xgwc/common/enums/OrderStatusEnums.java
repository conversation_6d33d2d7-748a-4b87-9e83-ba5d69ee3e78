package com.xgwc.common.enums;

public enum OrderStatusEnums {

    ARCHIVED(1, 5, "已交稿"),

    AFTER_SALE_START(2, 1, "售后开始"),

    AFTER_SALE_END(3, 0, "售后结束"),

    IS_ARCHIVING(4, 1, "归档"),

    SETTLEMENT(5, 1, "已结算");

    private final Integer type;

    private final Integer code;

    private final String desc;

    OrderStatusEnums(Integer type, Integer code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
