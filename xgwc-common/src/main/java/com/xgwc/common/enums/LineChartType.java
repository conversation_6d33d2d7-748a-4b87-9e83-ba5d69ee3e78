package com.xgwc.common.enums;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-27  15:51
 */

import lombok.Getter;

import java.util.Arrays;

/**
 * 设计师工作台首页折线图枚举
 */
@Getter
public enum LineChartType {

    //接单数
    YEARLY(0),
    //退款订单数
    MONTHLY_ORDER(1),
    //服务客户数
    MONTHLY_CUSTOMER(2),
    //已结算佣金
    MONTHLY_SETTLEMENT(3);

    private final int code;

    LineChartType(int code) { this.code = code; }

    public static LineChartType fromValue(int code) {
        return Arrays.stream(values())
                .filter(t -> t.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的折线图类型"));
    }
}
