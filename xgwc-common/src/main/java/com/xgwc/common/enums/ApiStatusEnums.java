package com.xgwc.common.enums;

/**
 * <AUTHOR>
 */
public enum ApiStatusEnums {

    //调用成功
    OK(1, "OK"),
    //调用错误
    ERROR(-1, "error"),

    NOT_EXISTS(405,"数据不存在"),

    /**
     * 登陆失效，重新登陆
     */
    LOGIN_OUT(999,"relogin"),

    /**
     * 没有权限
     */
    NO_PERMISSION(401,"no permission"),

    /**
     * 未切换角色
     */
    NOT_SWITCH_ROLE(402,"no permission"),

    //调用失败
    FAILURE(0, "failure"),
    //参数错误
    PARAMS_ERROR(2, "params_error"),

    SMS_SENDCODE_ERROR(600, "发送验证码失败"),

    SMS_SENDCODE_LIMIT(601, "发送验证码次数超限"),

    SMS_SENDCODE_TIME(602, "发送验证码太频繁,1分钟后重试"),

    AUTH_LOGIN_CAPTCHA_CODE_ERROR(700,"验证码不正确"),

    SMS_CODE_NOT_FOUND(701,"验证码不存在"),

    SMS_CODE_EXPIRED(702, "验证码已过期"),

    USER_MOBILE_EXISTS(703,"手机号已经存在"),

    FRANCHISEOWNER_DATA_EXIST(706,"加盟商已存在"),

    DESIGNER_DATA_EXISTS(706,"设计师已存在"),

    STAFF_NAME_EXISTS(705,"姓名/手机号不正确"),

    USER_PASSWORD_NOT_MATCH(707,"密码不匹配"),

    USER_PASSWORD_FAILED(704,"密码不符合规则"),

    STAFF_NAME_NOT_EXISTS(709,"姓名不存在"),

    ROLE_NAME_EXISTS(710,"角色已存在"),

    NAME_NOT_CORRECT(709,"姓名不正确，有疑问请联系贵公司人事部"),

    USER_NAME_EXISTS(708,"用户名已存在"),

    AUTH_USER_PHONE_EXISTS(712,"账号已存在"),

    AUTH_USER_NOT_EXISTS(713,"账号不存在"),

    ADMIN_NAME_EXISTS(711,"管理员姓名不能重复"),

    STAFF_STAGE_NAME_EXISTS(714,"该花名已存在,请换个花名");

    private final int status;

    private final String message;

    ApiStatusEnums(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public int getStatus() {
        return status;
    }


    public String getMessage() {
        return message;
    }

}
