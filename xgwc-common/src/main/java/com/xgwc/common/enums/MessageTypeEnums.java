package com.xgwc.common.enums;

public enum MessageTypeEnums {

    INNER_NOTICE(1, "内部通知"),

    ORDER_REIMBURSEMENT(2, "订单退款流程"),

    ORDER_REFUND(3, "订单报销流程"),

    ORDER_CONTRACT(4, "订单合同流程"),

    ORDER_INVOICE(5, "订单发票流程");

    private final int type;

    private final String desc;

    MessageTypeEnums(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
