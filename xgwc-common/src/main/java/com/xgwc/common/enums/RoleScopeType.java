package com.xgwc.common.enums;

import lombok.Getter;


/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-08-2  11:50
 */

/**
 * saas后台角色权限范围枚举
 */
@Getter
public enum RoleScopeType {
    // 0-品普, 1-品管, 2-加普, 3-加管, 4-服普, 5-服管, 6-销普, 7-销管, 8-saas普, 9-saas管
    PIN_PU(0, "品牌商普通权限", "brandAdmin"),
    PIN_GUAN(1, "品牌商管理员权限", "brandAdmin"),
    JIA_PU(2, "加盟商普通权限", "franchiseeAdmin"),
    JIA_GUAN(3, "加管理员权限", "franchiseeAdmin"),
    FU_PU(4, "财务服务商普通权限", "serviceAdmin"),
    FU_GUAN(5, "财务服管理员权限", "serviceAdmin"),
    XIAO_PU(6, "销售服务商普通权限", "marketAdmin"),
    XIAO_GUAN(7, "销售服务商理员权限", "marketAdmin"),
    SAAS_PU(8, "saas普通权限", "saasAdmin"),
    SAAS_GUAN(9, "saas管理员权限", "saasAdmin");

    private final int code;
    private final String desc;
    private final String isFlag;

    RoleScopeType(int code, String desc, String isFlag) {
        this.code = code;
        this.desc = desc;
        this.isFlag = isFlag;
    }

    public static RoleScopeType fromCode(int code) {
        for (RoleScopeType type : RoleScopeType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的 roleScope 代码: " + code);
    }

    public static String getIsFlag(int code) {
        for (RoleScopeType type : RoleScopeType.values()) {
            if (type.code == code) {
                return type.getIsFlag();
            }
        }
        return null;
    }
}