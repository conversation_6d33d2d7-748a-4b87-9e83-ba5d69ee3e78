package com.xgwc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-06  14:40
 */
/**
 * 时间类型枚举
 */
@Getter
@AllArgsConstructor
public enum EmployeesDateFlag {

    ENTRY_DATE(0, "入职日期"),
    CONTRACT_END_DATE(1, "合同到期日期"),
    RESIGNATION_DATE(2, "离职日期"),
    BIRTHDATE(3, "出生日期");

    private Integer code;
    private String text;


    public static EmployeesDateFlag getTextByCode(Integer code) {
        for (EmployeesDateFlag item : EmployeesDateFlag.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
