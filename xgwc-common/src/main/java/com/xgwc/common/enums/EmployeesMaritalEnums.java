package com.xgwc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-07  11:35
 */

/**
 * 婚姻状况类型枚举
 */
@Getter
@AllArgsConstructor
public enum EmployeesMaritalEnums {

    MARRIED(0, "已婚"),
    SINGLE(1, "未婚"),
    DIVORCE(2, "离异");

    private Integer code;
    private String text;

    public static String getTextByCode(Integer code) {
        for (EmployeesMaritalEnums maritalEnums : EmployeesMaritalEnums.values()) {
            if (maritalEnums.code.equals(code)) {
                return maritalEnums.text;
            }
        }
        return null;
    }
}
