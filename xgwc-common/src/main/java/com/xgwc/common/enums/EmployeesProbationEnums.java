package com.xgwc.common.enums;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-07  11:41
 */

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否转正枚举
 */
@Getter
@AllArgsConstructor
public enum EmployeesProbationEnums {

    NO(0, "未转正"),
    YES(1, "已转正");

    private Integer code;
    private String text;

    public static String getTextByCode(Integer code) {
        for (EmployeesProbationEnums value : EmployeesProbationEnums.values()) {
            if (value.code.equals(code)) {
                return value.text;
            }
        }
        return null;
    }

}
