package com.xgwc.common.enums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-07  11:32
 */

/**
 * 政治面貌类型枚举
 */
@Getter
@AllArgsConstructor
public enum EmployeesPoliticalEnums {

    /**
     * 党员
     */
    PARTICIPATE(1, "党员"),

    /**
     * 团员
     */
    MEMBER(2, "团员"),

    /**
     * 群众
     */
    COMMUNIST(3, "群众");

    private Integer code;
    private String text;

    public static String getTextByCode(Integer code) {
        for (EmployeesPoliticalEnums value : EmployeesPoliticalEnums.values()) {
            if (value.code.equals(code)) {
                return value.text;
            }
        }
        return null;
    }


}
