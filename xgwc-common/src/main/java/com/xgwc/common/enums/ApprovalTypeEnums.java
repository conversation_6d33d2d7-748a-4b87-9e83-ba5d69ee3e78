package com.xgwc.common.enums;

/**
 * 流程类型枚举
 */
public enum ApprovalTypeEnums {

    ORDER_REIMBURSEMENT(1, "订单报销", "order_reimbursementProcess"),

    ORDER_REFUND(2, "订单退款", "order_reimbursementProcess"),

    ORDER_CONTRACT(3, "订单合同", "order_contractProcess"),

    ORDER_INVOICE(4, "订单发票", "order_invoiceProcess"),

    MONTHLY_COMMISSION(5, "月度提成", "monthly_commissionProcess");

    private final int type;

    private final String desc;

    private final String key;

    ApprovalTypeEnums(int type, String desc, String key) {
        this.type = type;
        this.desc = desc;
        this.key = key;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static ApprovalTypeEnums getApprovalType(int type) {
        ApprovalTypeEnums[] approvalTypeEnums = ApprovalTypeEnums.values();
        for (ApprovalTypeEnums approvalTypeEnum : approvalTypeEnums) {
            if (approvalTypeEnum.getType() == type) {
                return approvalTypeEnum;
            }
        }
        return null;
    }
}
