package com.xgwc.common.enums;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-07  11:10
 */

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别类型枚举
 */
@Getter
@AllArgsConstructor
public enum EmployeesSexEnums {

    MALE(0, "男"),
    FEMALE(1, "女"),
    UNKNOWN(2, "未知");

    private Integer code;
    private String text;

    public static String getTextByCode(Integer code) {
        for (EmployeesSexEnums sex : EmployeesSexEnums.values()) {
            if (sex.getCode().equals(code)) {
                return sex.text;
            }
        }
        return null;
    }
}
