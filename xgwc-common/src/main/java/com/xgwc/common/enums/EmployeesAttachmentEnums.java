package com.xgwc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-07  11:43
 */

/**
 * 附件类型枚举
 */
@Getter
@AllArgsConstructor
public enum EmployeesAttachmentEnums {

    RESUME(0, "简历"),
    LABOR_CONTRACT(1, "劳动合同"),
    PROOF(2, "证明材料");

    private Integer code;
    private String text;

    public static String getTextByCode(Integer code) {
        for (EmployeesAttachmentEnums enums : EmployeesAttachmentEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getText();
            }
        }
        return null;
    }
}
