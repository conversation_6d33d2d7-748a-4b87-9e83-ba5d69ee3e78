package com.xgwc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @BelongsProject: xgwc-parent
 * @BelongsPackage: com.xgwc.common.enums
 * @Author: k<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-07  11:37
 */

/**
 * 社保状态类型枚举
 */
@Getter
@AllArgsConstructor
public enum EmployeesSocialEnums {

    NONE(0, "未买"),

    BUY(1, "已买"),

    STOP(2, "停保");

    private Integer code;

    private String text;

    public static String getTextByCode(Integer code) {
        for (EmployeesSocialEnums employeesSocialEnums : EmployeesSocialEnums.values()) {
            if (employeesSocialEnums.getCode().equals(code)) {
                return employeesSocialEnums.getText();
            }
        }
        return null;
    }
}
