//package com.xgwc.common.interceptor;
//
//import com.xgwc.common.annotation.TenantIgnore;
//import com.xgwc.common.constants.SecurityConstants;
//import com.xgwc.common.context.TenantContext;
//import com.xgwc.common.util.SecurityUtils;
//import com.xgwc.common.util.StringUtils;
//import net.sf.jsqlparser.JSQLParserException;
//import net.sf.jsqlparser.expression.Expression;
//import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
//import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
//import net.sf.jsqlparser.expression.operators.relational.ParenthesedExpressionList;
//import net.sf.jsqlparser.parser.CCJSqlParserUtil;
//import net.sf.jsqlparser.schema.Column;
//import net.sf.jsqlparser.statement.Statement;
//import net.sf.jsqlparser.statement.delete.Delete;
//import net.sf.jsqlparser.statement.insert.Insert;
//import net.sf.jsqlparser.statement.select.PlainSelect;
//import net.sf.jsqlparser.statement.select.Select;
//import net.sf.jsqlparser.statement.update.Update;
//import org.apache.ibatis.executor.statement.StatementHandler;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.SqlCommandType;
//import org.apache.ibatis.plugin.*;
//import org.apache.ibatis.reflection.MetaObject;
//import org.apache.ibatis.reflection.SystemMetaObject;
//
//import java.lang.reflect.Method;
//import java.sql.Connection;
//import java.util.*;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
//@Intercepts({
//        @Signature(type = StatementHandler.class,
//                method = "prepare",
//                args = {Connection.class, Integer.class})
//})
//public class TenantInterceptor implements Interceptor {
//
//    // 租户ID列名，默认为tenant_id
//    private String tenantIdColumn = "tenant_id";
//
//    // 需要忽略的表（不添加租户条件）
//    private Set<String> ignoreTables = new HashSet<>(Arrays.asList("sys_tenant", "sys_tenant_package", "sys_menu"));
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        StatementHandler handler = (StatementHandler) invocation.getTarget();
//        BoundSql boundSql = handler.getBoundSql();
//
////        if(true) return invocation.proceed();
//        // 解析原始SQL
//        String sql = boundSql.getSql();
//        MetaObject metaObject = SystemMetaObject.forObject(handler);
//        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
//
//        if(getTenantIgnoreAnnotation(mappedStatement)) return invocation.proceed();
//
//        // 判断是否需要添加租户条件
//        if (shouldFilterTenant(mappedStatement, sql)) {
//            // 获取当前租户ID
//            Long tenantId = SecurityUtils.getTenantId();
//            if (tenantId == null) {
//                throw new NullPointerException("TenantContext 租户编号不存在！");
//            }
//            // 修改SQL添加租户条件
//            String newSql = addTenantCondition(sql, tenantId);
//            resetSql(handler, boundSql, newSql);
//        }
//
//        return invocation.proceed();
//    }
//
//
//    private boolean getTenantIgnoreAnnotation(MappedStatement mappedStatement) {
//        try {
//            String id = mappedStatement.getId();
//            String className = id.substring(0, id.lastIndexOf("."));
//            String methodName = id.substring(id.lastIndexOf(".") + 1);
//
//            if(methodName.endsWith("_COUNT")) {
//                methodName = methodName.replace("_COUNT", "");
//            }
//            Class clazz = Class.forName(className);
//            if(clazz.isAnnotationPresent(TenantIgnore.class)) return true;
//
//            for (Method me : clazz.getMethods()) {
//                if (me.getName().equals(methodName)) {
//                    return me.isAnnotationPresent(TenantIgnore.class);
//                }
//            }
//        } catch (Exception ex) {
//        }
//        return false;
//    }
//
//    private boolean shouldFilterTenant(MappedStatement mappedStatement, String sql) {
//        // 根据表名判断
//        String tableName = extractTableName(sql);
//        return !(tableName == null || ignoreTables.contains(tableName.toLowerCase()));
//    }
//
//    private String addTenantCondition(String originalSql, Object tenantId) {
//        originalSql = originalSql.trim();
//        String upperSql = originalSql.toUpperCase();
//
//        // 解析SQL并添加租户条件
//        if (upperSql.startsWith("SELECT")) {
//
//            StringBuffer sb = new StringBuffer();
//            List<TableInfo> tableInfos = parseTables(originalSql);
//            if(tableInfos != null && tableInfos.size() > 0) {
//                try {
//                    Select select = (Select) CCJSqlParserUtil.parse(originalSql);
//                    PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
//                    long count = tableInfos.stream().filter(t -> t.tableName.equals(plainSelect.getFromItem().toString())).count();
//                    if(count > 0) {
//                        TableInfo tableInfo = tableInfos.stream().filter(t -> t.tableName.equals(plainSelect.getFromItem().toString())).findAny().get();
//                        sb.append(tableInfo.getAlias() +"."+ tenantIdColumn + " = '" + tenantId + "'");
//                    }
//                    if(StringUtils.isNotBlank(sb.toString())) {
//                        Expression existingWhere = plainSelect.getWhere();
//                        Expression newCondition = CCJSqlParserUtil.parseCondExpression(sb.toString());
//                        if (existingWhere != null) {
//                            plainSelect.setWhere(new AndExpression(existingWhere, newCondition));
//                        } else {
//                            plainSelect.setWhere(newCondition);
//                        }
//                        originalSql = plainSelect.toString();
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            }
//
//            return originalSql;
//
//        } else if (upperSql.startsWith("INSERT")) {
//            // 处理INSERT语句，添加租户列和值
//            return handleInsertSql(originalSql, tenantId);
//        } else if (upperSql.startsWith("UPDATE")) {
//            // 处理UPDATE语句，添加租户条件
//            if (!upperSql.contains(" WHERE ")) {
//                throw new RuntimeException("UPDATE statement must have WHERE clause in multi-tenant environment");
//            }
//            return originalSql + " AND " + tenantIdColumn + " = '" + tenantId + "'";
//        } else if (upperSql.startsWith("DELETE")) {
//            // 处理DELETE语句，添加租户条件
//            if (!upperSql.contains(" WHERE ")) {
//                throw new RuntimeException("DELETE statement must have WHERE clause in multi-tenant environment");
//            }
//            return originalSql + " AND " + tenantIdColumn + " = '" + tenantId + "'";
//        }
//
//        return originalSql;
//    }
//
//    private String handleInsertSql(String originalSql, Object tenantId) {
//        // 简单实现，实际应根据SQL语法解析
//        try {
//            Statement statement = CCJSqlParserUtil.parse(originalSql);
//            if (statement instanceof Insert) {
//                Insert insert = (Insert) statement;
//                ExpressionList<Column> columns =  insert.getColumns();
//                columns.add(new Column("tenant_id"));
//                insert.setColumns(columns);
//                insert.getValues().getExpressions().stream().forEach(v -> {
//                    ParenthesedExpressionList list = (ParenthesedExpressionList)v;
//                    list.add(tenantId);
//                });
//            }
//            originalSql = statement.toString();
//        } catch (Exception e) {
//
//        }
//        return originalSql;
//    }
//
//    private void resetSql(StatementHandler handler, BoundSql boundSql, String newSql) {
//        // 使用反射修改BoundSql的sql字段
//        MetaObject boundSqlMeta = SystemMetaObject.forObject(boundSql);
//        boundSqlMeta.setValue("sql", newSql);
//    }
//
//    private String extractTableName(String sql) {
//        // 简单实现，实际应使用SQL解析器
//        String upperSql = sql.toUpperCase();
//        if (upperSql.startsWith("SELECT")) {
//            try {
//                Statement statement = CCJSqlParserUtil.parse(sql);
//                if (statement instanceof Select) {
//                    Select select = (Select) statement;
//                    if (select.getSelectBody() instanceof PlainSelect) {
//                        PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
//                        if (plainSelect.getFromItem() != null) {
//                            return plainSelect.getFromItem().toString();
//                        }
//                    }
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        } else if (upperSql.startsWith("INSERT")) {
//            try {
//                Insert insert = (Insert) CCJSqlParserUtil.parse(sql);
//                return insert.getTable().getName();
//            } catch (JSQLParserException e) {
//
//            }
//
//        } else if (upperSql.startsWith("UPDATE")) {
//
//            try {
//                Update update =  (Update) CCJSqlParserUtil.parse(sql);
//                return update.getTable().getName();
//            } catch (JSQLParserException e) {
//
//            }
//        } else if (upperSql.startsWith("DELETE")) {
//
//            try {
//                Delete delete =  (Delete) CCJSqlParserUtil.parse(sql);
//                return delete.getTable().getName();
//            } catch (JSQLParserException e) {
//
//            }
//        }
//        // 其他SQL类型的处理...
//        return null;
//    }
//
//    private int indexOfFirst(String str, char c) {
//        int index = str.indexOf(c);
//        return index >= 0 ? index : str.length();
//    }
//
//    @Override
//    public Object plugin(Object target) {
//        return Plugin.wrap(target, this);
//    }
//
//    @Override
//    public void setProperties(Properties properties) {
//        if (properties.containsKey("tenantIdColumn")) {
//            this.tenantIdColumn = properties.getProperty("tenantIdColumn");
//        }
//        if (properties.containsKey("ignoreTables")) {
//            this.ignoreTables = new HashSet<>(
//                    Arrays.asList(properties.getProperty("ignoreTables").split(","))
//            );
//        }
//    }
//    private List<TableInfo> parseTables(String sql) {
//        List<TableInfo> tables = new ArrayList<>();
//
//        // 主正则表达式，匹配FROM和JOIN后的表名和别名
//        Pattern pattern = Pattern.compile(
//                "(?:from|join)\\s+" +                   // FROM或JOIN关键字
//                        "(?:\\w+\\.)?" +                         // 可选的模式名前缀(如schema.)
//                        "([\\w_]+)" +                           // 表名(第1捕获组)
//                        "(?:\\s+(?:AS\\s+)?([\\w_]+))?" +        // 可选的别名(第2捕获组)
//                        "(?=\\s+|$)",                           // 后面必须是空格或结束
//                Pattern.CASE_INSENSITIVE);
//
//        Matcher matcher = pattern.matcher(sql);
//        while (matcher.find()) {
//            String tableName = matcher.group(1);
//            String alias = matcher.group(2);
//            if(tableName != null && ignoreTables.contains(tableName.toLowerCase())) continue;
//            tables.add(new TableInfo(tableName, alias));
//        }
//        return tables;
//    }
//    public static class TableInfo {
//        private final String tableName;
//        private final String alias;
//
//        public TableInfo(String tableName, String alias) {
//            this.tableName = tableName;
//            if(alias != null && !alias.equalsIgnoreCase("where")) this.alias = alias;
//            else this.alias = tableName;
//        }
//
//        public String getTableName() { return tableName; }
//        public String getAlias() { return alias; }
//
//        @Override
//        public String toString() {
//            return alias == null ? tableName : tableName + " AS " + alias;
//        }
//    }
//}
