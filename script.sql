2025-07-17
alter table bill_comfirm_detail add company_info_id BIGINT comment '公司主体ID'

DROP TABLE IF EXISTS `sys_message`;
CREATE TABLE `sys_message`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '类型名称',
  `type_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型key',
  `message` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `type_id` bigint NULL DEFAULT NULL COMMENT '类型ID',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `is_read` tinyint(1) NOT NULL COMMENT '是否已读：0未读，1已读',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `id_typeKey`(`type_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


-- 2025-07-17
CREATE TABLE `xgwc_platform_config` (
  `id` bigint NOT NULL COMMENT '主键id, 也是品牌商id',
  `sid` varchar(128) NOT NULL COMMENT 'sid',
  `app_key` varchar(128) NOT NULL COMMENT 'appkey',
  `app_secret` varchar(128) NOT NULL COMMENT 'appsecret',
  `qimen_sid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `qimen_app_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `qimen_app_secret` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台配置表(旺店通)';

CREATE TABLE `xgwc_platform_shop` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `shop_id` varchar(128) NOT NULL COMMENT '平台店铺id',
  `shop_name` varchar(255) NOT NULL COMMENT '店铺名称',
  `platform_id` bigint NOT NULL COMMENT '平台id',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4081 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台店铺表';

CREATE TABLE `xgwc_platform_goods`(
    `id`               bigint       NOT NULL AUTO_INCREMENT,
    `goods_id`         varchar(128) NOT NULL COMMENT '平台货品id',
    `goods_name`       varchar(255) NOT NULL COMMENT '货品名称',
    `platform_id`      bigint(11) NOT NULL COMMENT '平台id',
    `platform_shop_id` varchar(128) NOT NULL COMMENT '平台店铺id',
    `brand_id`         bigint       NOT NULL COMMENT '品牌商id',
    `biz_type`         bigint       DEFAULT NULL COMMENT '业务分类',
    `status`           tinyint(1) NOT NULL COMMENT '0删除1在架2下架',
    `is_del`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0正常，1删除',
    `create_by`        varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time`      datetime    DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(50) DEFAULT NULL COMMENT '修改人',
    `update_time`      datetime    DEFAULT NULL COMMENT '修改时间',
    `modify_time`      datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台货品表';

CREATE TABLE `xgwc_platform_goods_spec`(
    `id`        bigint       NOT NULL AUTO_INCREMENT,
    `goods_id`  varchar(128) NOT NULL COMMENT '平台货品id',
    `spec_id`   varchar(255) NOT NULL COMMENT '规格id',
    `spec_name` varchar(255) NOT NULL COMMENT '规格名称',
    `brand_id`  bigint       NOT NULL COMMENT '品牌商id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台货品规格表';

CREATE TABLE `xgwc_platform_trade` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(128) NOT NULL COMMENT '平台订单编号',
  `real_amount` decimal(11,2) NOT NULL COMMENT '商家实收金额',
  `pay_amount` decimal(11,2) NOT NULL COMMENT '买家实付金额',
  `order_amount` decimal(11,2) NOT NULL COMMENT '订单金额',
  `platform_id` bigint NOT NULL COMMENT '平台id',
  `platform_shop_id` varchar(128) NOT NULL COMMENT '平台店铺id',
  `trade_time` datetime DEFAULT NULL COMMENT '下单时间',
  `refund_status` varchar(16) NOT NULL COMMENT '退款状态： 0.无退款 1.申请退款 2.部分退款 3.全部退款',
  `trade_status` varchar(16) NOT NULL COMMENT '平台订单状态：30已支付 50已发货 70已完成 80已退款 90已关闭(付款前取消)',
  `pay_time` datetime NOT NULL COMMENT '支付时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '买家备注',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=824 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台交易单表';

CREATE TABLE `xgwc_platform_trade_details` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(128) NOT NULL COMMENT '平台订单编号',
  `oid` varchar(128) NOT NULL COMMENT '平台子单号',
  `goods_id` varchar(128) NOT NULL COMMENT '平台货品id',
  `goods_spec_id` varchar(128) DEFAULT NULL COMMENT '平台货品规格id',
  `num` bigint NOT NULL COMMENT '下单数量',
  `share_amount` decimal(11,2) NOT NULL COMMENT '分摊应收金额',
  `refund_status` varchar(16) NOT NULL COMMENT '退款状态： 0.无退款 1.申请退款 2.部分退款 3.全部退款',
  `status` varchar(16) NOT NULL COMMENT '平台子单状态：10未确认 20待尾款 30待发货 40部分发货 50已发货 60已签收 70已完成 80已退款 90已关闭',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=824 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台交易单明细表';

CREATE TABLE `xgwc_platform_refund` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(128) NOT NULL COMMENT '平台订单编号',
  `refund_no` varchar(128) NOT NULL COMMENT '平台退款编号',
  `reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `status` varchar(128) NOT NULL COMMENT '退款状态 ：1取消退款,2已申请退款,3等待退货,4等待收货,5退款成功',
  `refund_amount` decimal(11,2) NOT NULL COMMENT '买家申请退款金额',
  `actual_refund_amount` decimal(11,2) NOT NULL COMMENT '实际退款',
  `refund_time` datetime NOT NULL COMMENT '退款申请时间',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台售后单表';

CREATE TABLE `xgwc_platform_refund_details` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(128) NOT NULL COMMENT '平台订单编号',
  `oid` varchar(128) NOT NULL COMMENT '平台子单号',
  `refund_no` varchar(128) NOT NULL COMMENT '平台退款编号',
  `goods_id` varchar(128) NOT NULL COMMENT '平台货品id',
  `goods_spec_id` varchar(128) DEFAULT NULL COMMENT '平台货品规格id',
  `refund_num` bigint NOT NULL COMMENT '退款数量',
  `refund_order_amount` decimal(11,2) NOT NULL COMMENT '明细退款金额，由主单退款金额分摊',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台售后单明细表';


ALTER TABLE `bill_designer` ADD uncleared_amount decimal(10, 2) COMMENT '未结清金额';
ALTER TABLE `bill_designer` ADD payable_amount decimal(10, 2) COMMENT '应发金额';
ALTER TABLE `bill_designer` ADD real_amount decimal(10, 2) COMMENT '实发金额';

ALTER TABLE `bill_designer_sub` ADD payable_amount decimal(10, 2) COMMENT '应发金额';
ALTER TABLE `bill_designer_sub` ADD real_amount decimal(10, 2) COMMENT '实发金额';
ALTER TABLE `bill_order_modify` ADD commission_backed decimal(10, 2) COMMENT '已追回佣金';
ALTER TABLE `bill_order_modify` ADD back_status decimal(10, 2) COMMENT '追回状态：0待追回，1部分追回，2全部追回';
ALTER TABLE `bill_designer` ADD fine_amount_deduction decimal(10, 2) COMMENT '罚款抵扣';
ALTER TABLE `bill_designer` ADD fine_amount_remaining decimal(10, 2) COMMENT '罚款剩余';
ALTER TABLE `bill_designer` ADD real_commission_back decimal(10, 2) COMMENT '实际追回';
ALTER TABLE `bill_designer` ADD commission_back_remaining decimal(10, 2) COMMENT '剩余佣金追回';
ALTER TABLE `bill_order_modify` ADD commission_back_remaining decimal(10, 2) COMMENT '剩余佣金追回';


-- 2025-07-18
ALTER TABLE order_invoice_apply ADD execution_id BIGINT NULL COMMENT '流程实例Id';
ALTER TABLE order_expenses_apply ADD execution_id BIGINT NULL COMMENT '流程实例Id';

-- 2025-07-19
ALTER TABLE order_refund_apply ADD customer_phone varchar(100) NULL COMMENT '客户手机号';

-- 2025-07-19
DROP TABLE IF EXISTS `bill_designer_sub`;
CREATE TABLE `bill_designer_sub` (
  `sub_bill_id` bigint NOT NULL AUTO_INCREMENT COMMENT '子账单ID',
  `bill_id` bigint DEFAULT NULL COMMENT '主账单id',
  `stylist_id` bigint DEFAULT NULL COMMENT '设计师id',
  `stylist_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设计师名称',
  `manager_user_id` bigint DEFAULT NULL COMMENT '管理员用户id',
  `bill_period_start` date DEFAULT NULL COMMENT '账单周期开始日期',
  `bill_period_end` date DEFAULT NULL COMMENT '账单周期结束日期',
  `order_count` int DEFAULT NULL COMMENT '账单订单数',
  `total_commission` decimal(10,2) DEFAULT NULL COMMENT '账单佣金合计',
  `brand_owner_id` bigint DEFAULT NULL COMMENT '结算品牌商',
  `fine_amount` decimal(10,2) DEFAULT NULL COMMENT '罚款金额',
  `no_invoice` decimal(10,2) DEFAULT NULL COMMENT '无票扣款',
  `commission_back` decimal(10,2) DEFAULT NULL COMMENT '佣金追回',
  `billing_time` datetime DEFAULT NULL COMMENT '出账时间',
  `confirmation_time` datetime DEFAULT NULL COMMENT '确认时间',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `settlement_status` tinyint(1) DEFAULT NULL COMMENT '结算状态(0-待确认, 1-有异议, 2-待结算, 3-已结算，4-发票审核中，5-发票被驳回，6-部分结算，7-打款失败)',
  `is_lock` tinyint(1) DEFAULT NULL COMMENT '是否锁定：0锁定，1未锁定',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态：0正常，其他非正常',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行修改时间',
  `franchise_id` bigint DEFAULT NULL COMMENT '加盟商id',
  `brand_id` bigint DEFAULT NULL COMMENT '品牌商',
  `company_info_id` bigint DEFAULT NULL COMMENT '公司主体ID',
  `payable_amount` decimal(10,2) DEFAULT NULL COMMENT '应发金额',
  `real_amount` decimal(10,2) DEFAULT NULL COMMENT '实发金额',
  `fail_reason` text COLLATE utf8mb4_unicode_ci COMMENT '打款失败原因',
  PRIMARY KEY (`sub_bill_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设计师对账表';


CREATE TABLE `bill_payment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `excel_name` varchar(100) DEFAULT NULL COMMENT '打款Excel名称',
  `brand_id` bigint DEFAULT NULL COMMENT '所属品牌商id',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '行修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='打款记录表';


CREATE TABLE `bill_payment_sub` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `payment_id` bigint DEFAULT NULL COMMENT '打款记录id',
  `sub_bill_id` bigint DEFAULT NULL COMMENT '子账单id',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='子账单关联打款记录表';


-- 2025-07-21
ALTER TABLE xgwc_sass.order_refund_apply ADD platform_refund_time DATETIME NULL COMMENT '平台发起退款时间(多个子单一起退时,取最早的)';

ALTER TABLE `xgwc_order` ADD finish_time DATETIME DEFAULT NULL COMMENT '订单完成时间（手动完成、自动完成(归档且已交稿且订单金额与实收金额相等)）';

-- 2025-07-23
DROP TABLE IF EXISTS `xgwc_shop`;
CREATE TABLE `xgwc_shop` (
  `shop_id` bigint NOT NULL AUTO_INCREMENT COMMENT '店铺id',
  `shop_name` varchar(100) DEFAULT NULL COMMENT '店铺名称',
  `brand_id` bigint DEFAULT NULL COMMENT '品牌id',
  `brand_owner_id` bigint DEFAULT NULL COMMENT '品牌商id',
  `franchise_id` bigint DEFAULT NULL COMMENT '所属加盟商id',
  `channel_id` bigint DEFAULT NULL COMMENT '渠道id',
  `dept_id` bigint DEFAULT NULL COMMENT '部门id',
  `pay_type` tinyint(1) DEFAULT NULL COMMENT '默认支付方式 1-淘宝  2-微信  3-支付宝 4-对公 5-拼多多 6-小程序 7-天猫 ',
  `platform_shop_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台店铺ID',
  `shop_deposit` decimal(10,2) DEFAULT NULL COMMENT '店铺保证金',
  `company_info_id` bigint DEFAULT NULL COMMENT '经营主体id',
  `status` tinyint DEFAULT '0' COMMENT '状态：0正常，1禁用',
  `is_del` tinyint DEFAULT '0' COMMENT '是否删除：0正常，1删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`shop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='店铺管理表';


-- 2025-07-22
ALTER TABLE xgwc_designer MODIFY COLUMN receive_order_status tinyint NULL COMMENT '接单状态：0正常接单，1暂停接单，2离职';

-- 2025-07-23
ALTER TABLE xgwc_sass.xgwc_order ADD order_category TINYINT NULL COMMENT '订单归类: 1平台订单, 2线下';

-- 2025-07-24
DROP TABLE IF EXISTS `finance_offline_payments`;
CREATE TABLE `finance_offline_payments` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `pay_id` bigint DEFAULT NULL COMMENT '订单支付流水id',
  `order_id` bigint DEFAULT NULL COMMENT '订单表主键id',
  `amount` double(10,2) DEFAULT '0.00' COMMENT '实收金额',
  `difference_amount` decimal(10,2) DEFAULT NULL COMMENT '差额',
  `company_info_id` bigint DEFAULT NULL COMMENT '付款主体id',
  `payment_flow_number` varchar(50) DEFAULT NULL COMMENT '付款流水号',
  `payment_status` tinyint DEFAULT '0' COMMENT '收款状态:0-财务未收 1-财务已收',
  `archival_status` tinyint DEFAULT '0' COMMENT '归档结果:0-未锁定 1-已锁定',
  `remarks` text COMMENT '备注',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_flow_number` (`payment_flow_number`) USING BTREE COMMENT '唯一流水号'
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线下收款归档信息表';

DROP TABLE IF EXISTS `finance_platform_payments`;


-- 2025-07-24
ALTER TABLE xgwc_order ADD refund_status TINYINT NULL DEFAULT 0 COMMENT '退款状态：0 未退款，1 部分退款，2 全额退款';
ALTER TABLE xgwc_after_agency_audit ADD audit_remark varchar(1000) NULL COMMENT '审核原因';

-- 2025-07-25
ALTER TABLE xgwc_chat_record MODIFY COLUMN classify bigint NULL COMMENT '分类';
ALTER TABLE xgwc_designer MODIFY COLUMN bank_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '开户行';
ALTER TABLE xgwc_designer MODIFY COLUMN manager_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '管理员姓名';

-- 2025-07-26
ALTER TABLE xgwc_order ADD invoice_status TINYINT DEFAULT 0 NULL COMMENT '开票状态：0 未开票，1 部分开票，2全额开票';
ALTER TABLE xgwc_staff ADD resignation_time datetime NULL COMMENT '离职时间';
ALTER TABLE xgwc_service_staff ADD resignation_time datetime NULL COMMENT '离职时间';
ALTER TABLE xgwc_franchise_staff ADD resignation_time datetime NULL COMMENT '离职时间';
ALTER TABLE xgwc_shop ADD `franchise_owner_id` bigint DEFAULT NULL COMMENT '加盟商id（franchise_owner主键）';


-- 2025-07-26
drop TABLE xgwc_platform_config;
CREATE TABLE `xgwc_platform_config` (
  `id` bigint NOT NULL COMMENT '主键id, 也是品牌商id',
  `sid` varchar(128) NOT NULL COMMENT 'sid',
  `app_key` varchar(128) NOT NULL COMMENT 'appkey',
  `app_secret` varchar(128) NOT NULL COMMENT 'appsecret',
  `qimen_sid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `qimen_app_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `qimen_app_secret` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台配置表(旺店通)';

drop TABLE xgwc_platform_shop;
CREATE TABLE `xgwc_platform_shop` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `shop_id` varchar(128) NOT NULL COMMENT '平台店铺id',
  `shop_name` varchar(255) NOT NULL COMMENT '店铺名称',
  `platform_id` bigint NOT NULL COMMENT '平台id',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4081 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台店铺表';

drop TABLE xgwc_platform_trade;
CREATE TABLE `xgwc_platform_trade` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(128) NOT NULL COMMENT '平台订单编号',
  `real_amount` decimal(11,2) NOT NULL COMMENT '商家实收金额',
  `pay_amount` decimal(11,2) NOT NULL COMMENT '买家实付金额',
  `order_amount` decimal(11,2) NOT NULL COMMENT '订单金额',
  `platform_id` bigint NOT NULL COMMENT '平台id',
  `platform_shop_id` varchar(128) NOT NULL COMMENT '平台店铺id',
  `trade_time` datetime DEFAULT NULL COMMENT '下单时间',
  `refund_status` varchar(16) NOT NULL COMMENT '退款状态： 0.无退款 1.申请退款 2.部分退款 3.全部退款',
  `trade_status` varchar(16) NOT NULL COMMENT '平台订单状态：30已支付 50已发货 70已完成 80已退款 90已关闭(付款前取消)',
  `pay_time` datetime NOT NULL COMMENT '支付时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '买家备注',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=824 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台交易单表';

drop TABLE xgwc_platform_trade_details;
CREATE TABLE `xgwc_platform_trade_details` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(128) NOT NULL COMMENT '平台订单编号',
  `oid` varchar(128) NOT NULL COMMENT '平台子单号',
  `goods_id` varchar(128) NOT NULL COMMENT '平台货品id',
  `goods_spec_id` varchar(128) DEFAULT NULL COMMENT '平台货品规格id',
  `num` bigint NOT NULL COMMENT '下单数量',
  `share_amount` decimal(11,2) NOT NULL COMMENT '分摊应收金额',
  `refund_status` varchar(16) NOT NULL COMMENT '退款状态： 0.无退款 1.申请退款 2.部分退款 3.全部退款',
  `status` varchar(16) NOT NULL COMMENT '平台子单状态：10未确认 20待尾款 30待发货 40部分发货 50已发货 60已签收 70已完成 80已退款 90已关闭',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=824 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台交易单明细表';

drop TABLE xgwc_platform_refund;
CREATE TABLE `xgwc_platform_refund` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(128) NOT NULL COMMENT '平台订单编号',
  `refund_no` varchar(128) NOT NULL COMMENT '平台退款编号',
  `reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `status` varchar(128) NOT NULL COMMENT '退款状态 ：1取消退款,2已申请退款,3等待退货,4等待收货,5退款成功',
  `refund_amount` decimal(11,2) NOT NULL COMMENT '买家申请退款金额',
  `actual_refund_amount` decimal(11,2) NOT NULL COMMENT '实际退款',
  `refund_time` datetime NOT NULL COMMENT '退款申请时间',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台售后单表';

drop TABLE xgwc_platform_refund_details;
CREATE TABLE `xgwc_platform_refund_details` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_no` varchar(128) NOT NULL COMMENT '平台订单编号',
  `oid` varchar(128) NOT NULL COMMENT '平台子单号',
  `refund_no` varchar(128) NOT NULL COMMENT '平台退款编号',
  `goods_id` varchar(128) NOT NULL COMMENT '平台货品id',
  `goods_spec_id` varchar(128) DEFAULT NULL COMMENT '平台货品规格id',
  `refund_num` bigint NOT NULL COMMENT '退款数量',
  `refund_order_amount` decimal(11,2) NOT NULL COMMENT '明细退款金额，由主单退款金额分摊',
  `brand_id` bigint NOT NULL COMMENT '品牌商id',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台售后单明细表';


-- 2025-07-28
drop table xgwc_after_sales_exec_data;
CREATE TABLE `xgwc_after_sales_exec_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_id` bigint DEFAULT NULL COMMENT '业务id',
  `key_name` varchar(255) DEFAULT NULL COMMENT '参数名',
  `key_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '参数值',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='售后表单数据';
drop table xgwc_after_sales_flow_log;
CREATE TABLE `xgwc_after_sales_flow_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_id` bigint DEFAULT NULL COMMENT '业务id',
  `node_type` tinyint(1) DEFAULT NULL COMMENT '节点类型（1:开始，2:售后审核）',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `status` tinyint(1) COMMENT '节点状态（0发起审批，1通过，2拒绝）',
  `remark` TEXT COMMENT '备注',
  `sort_order` INT DEFAULT 0 COMMENT '顺序',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='售后审批记录表';




--2025-7-29
ALTER TABLE incom_line ADD track_code varchar(255) NULL COMMENT '跟进状态编码';


ALTER TABLE xgwc_sass.xgwc_platform_refund MODIFY COLUMN create_time datetime NOT NULL COMMENT '创建时间';
ALTER TABLE xgwc_sass.xgwc_platform_refund MODIFY COLUMN update_time datetime NOT NULL COMMENT '平台修改时间';



ALTER TABLE xgwc_sass.xgwc_platform_config DROP COLUMN qimen_sid;
ALTER TABLE xgwc_sass.xgwc_platform_config ADD start_time DATETIME NOT NULL COMMENT '数据同步起始时间';
ALTER TABLE xgwc_sass.xgwc_platform_config MODIFY COLUMN qimen_app_key varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL;
ALTER TABLE xgwc_sass.xgwc_platform_config MODIFY COLUMN qimen_app_secret varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL;

ALTER TABLE xgwc_sass.finance_offline_payments ADD `payment_code_body` bigint DEFAULT NULL COMMENT '付款码主体（字典值主键）';

-- 2025-7-31
ALTER TABLE xgwc_staff MODIFY COLUMN stage_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '花名';
ALTER TABLE xgwc_franchise_staff MODIFY COLUMN stage_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '花名';
ALTER TABLE xgwc_service_staff MODIFY COLUMN stage_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '花名';
ALTER TABLE sys_user ADD stage_name varchar(100) NULL COMMENT '花名';
ALTER TABLE xgwc_shop ADD `manager_id` bigint DEFAULT NULL COMMENT '财务服务商部门负责人id（员工表bind_user_id）';
ALTER TABLE sys_user_middle MODIFY COLUMN user_type tinyint(1) NOT NULL COMMENT '用户类型（0：saas管理员，1：品牌商，2：加盟商，3：设计师，4:品牌商员工，5：加盟商员工，6：财务服务商，7:财务服务商员工，8:销售服务商，9:销售服务商员工）';
ALTER TABLE xgwc_franchise_staff MODIFY COLUMN status tinyint(1) DEFAULT 0 NOT NULL COMMENT '状态：0在职，1离职，2合作中，3未合作';
ALTER TABLE xgwc_shop ADD `manager_id` bigint DEFAULT NULL COMMENT '财务服务商部门负责人id（xgwc_service_staff主键）';

CREATE TABLE `market_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `market_id` bigint DEFAULT NULL COMMENT '销售服务商id',
  `pid` bigint DEFAULT NULL COMMENT '父类id',
  `level` int DEFAULT NULL COMMENT '层级',
  `sort` int DEFAULT NULL COMMENT '排序：越小越前',
  `status` tinyint DEFAULT '0' COMMENT '状态：0正常，1禁用',
  `is_del` tinyint DEFAULT '0' COMMENT '是否删除：0正常，1删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售服务商部门表';

CREATE TABLE `market_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色id',
  `role_name` varchar(100) DEFAULT NULL COMMENT '角色名称',
  `market_id` bigint DEFAULT NULL COMMENT '销售服务商id',
  `status` tinyint DEFAULT '0' COMMENT '状态：0正常，1禁用',
  `is_del` tinyint DEFAULT '0' COMMENT '是否删除：0正常，1删除',
  `sort` int DEFAULT NULL COMMENT '排序：越小越前',
  `is_flag` varchar(50) DEFAULT NULL COMMENT '标识字段',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=125 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售服务商角色表';

CREATE TABLE `market_role_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `role_id` bigint DEFAULT NULL COMMENT '角色id',
  `menu_id` bigint DEFAULT NULL COMMENT '菜单id',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=901 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售服务商角色关联菜单权限表';

CREATE TABLE `market_station` (
  `station_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位id',
  `station_name` varchar(100) DEFAULT NULL COMMENT '岗位名称',
  `service_id` bigint DEFAULT NULL COMMENT '服务商id',
  `sort` int DEFAULT NULL COMMENT '排序：越小越前',
  `status` tinyint DEFAULT '0' COMMENT '状态：0正常，1禁用',
  `is_del` tinyint DEFAULT '0' COMMENT '是否删除：0否，1是',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`station_id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务商岗位表';

CREATE TABLE `market_employees` (
  `employee_id` bigint NOT NULL AUTO_INCREMENT COMMENT '员工唯一标识',
  `staff_id` bigint DEFAULT NULL COMMENT '员工外键',
  `account_id` bigint DEFAULT NULL COMMENT '员工账户外键',
  `attachment_id` bigint DEFAULT NULL COMMENT '员工附件外键',
  `market_id` bigint DEFAULT NULL COMMENT '销售服务商id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '员工姓名',
  `sex` tinyint(1) DEFAULT NULL COMMENT '用户性别（0男 1女 2未知）',
  `ethnicity` tinyint DEFAULT NULL COMMENT '民族',
  `education` tinyint DEFAULT NULL COMMENT '学历',
  `birthdate` date DEFAULT NULL COMMENT '出生日期',
  `political_status` tinyint DEFAULT NULL COMMENT '政治面貌',
  `marital_status` tinyint DEFAULT NULL COMMENT '婚姻状况',
  `id_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号(加密)',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电子邮箱（加密）',
  `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码（加密）',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系地址（加密）',
  `emer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '紧急联系人姓名',
  `emer_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '紧急联系人电话（加密）',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `contract_end_date` date DEFAULT NULL COMMENT '合同到期日期',
  `social_status` tinyint DEFAULT NULL COMMENT '社保状态，（0-未买，1-已买，2-停保）',
  `buy_social_date` date DEFAULT NULL COMMENT '购买社保日期',
  `probation_status` tinyint DEFAULT NULL COMMENT '是否转正，（0-否，1-是）',
  `probation_end_date` date DEFAULT NULL COMMENT '转正日期',
  `annual_leave_days` int DEFAULT NULL COMMENT '年假天数',
  `resignation_date` date DEFAULT NULL COMMENT '离职日期',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行修改时间',
  PRIMARY KEY (`employee_id`),
  UNIQUE KEY `id_number` (`id_number`),
  UNIQUE KEY `phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=134 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售服务商员工档案表';

CREATE TABLE `market_emp_attachments` (
  `attachment_id` bigint NOT NULL AUTO_INCREMENT COMMENT '附件唯一标识',
  `employee_id` bigint NOT NULL COMMENT '员工ID',
  `attachment_type` tinyint(1) DEFAULT NULL COMMENT '附件类型（0-简历 1-劳动合同 2-证明材料）',
  `file_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '文件名',
  `file_path` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '文件路径',
  `file_count` int DEFAULT NULL COMMENT '文件数量',
  `upload_date` date DEFAULT NULL COMMENT '上传日期',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行修改时间',
  PRIMARY KEY (`attachment_id`)
) ENGINE=InnoDB AUTO_INCREMENT=172 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售服务商员工档案-附件关联表';

CREATE TABLE `market_emp_accounts` (
  `account_id` bigint NOT NULL AUTO_INCREMENT COMMENT '账户唯一标识',
  `employee_id` bigint DEFAULT NULL COMMENT '员工id',
  `account_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开户名',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开户行',
  `account_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '银行卡号（加密）',
  `alipay_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付宝姓名',
  `alipay_account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付宝账号（加密）',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行修改时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `account_number` (`account_number`),
  UNIQUE KEY `alipay_account` (`alipay_account`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售服务商员工档案-工资账户关联表';

--2025/8/1
ALTER TABLE xgwc_role_data ADD `business_ids` varchar(100) DEFAULT NULL COMMENT '业务分类id';

CREATE TABLE `xgwc_business_setting` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `business_id` bigint DEFAULT NULL COMMENT '业务id',
  `relation_business_id` bigint DEFAULT NULL COMMENT '关联业务id',
  `business_name` varchar(100) DEFAULT NULL COMMENT '关联业务名称',
  `status` tinyint DEFAULT '1' COMMENT '是否支持品牌商派单：0支持，1不支持',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=155 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设置业务关联表';

CREATE TABLE `report_realtime_inbound` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `franchise_id` bigint DEFAULT NULL COMMENT '加盟商',
  `date` date DEFAULT NULL COMMENT '日期',
  `time_period` varchar(50) DEFAULT NULL COMMENT '时间段',
  `dept_id` bigint DEFAULT NULL COMMENT '部门',
  `inbound_volume` int DEFAULT NULL COMMENT '进线量',
  `current_direct_fee` decimal(10,2) DEFAULT NULL COMMENT '当前直通车费用',
  `click_volume` int DEFAULT NULL COMMENT '点击量',
  `remarks` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时进线';

ALTER TABLE xgwc_franchise_staff MODIFY COLUMN job_nature tinyint(1) NULL COMMENT '工作性质：0全职，1外包，2兼职，3合作方';
ALTER TABLE xgwc_franchise_staff ADD staff_type tinyint(1) DEFAULT 0 NOT NULL COMMENT '员工类型：0 内部员工，1合作员工';

drop TABLE xgwc_market_staff;
CREATE TABLE `xgwc_market_staff` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `stage_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '花名',
  `dept_id` bigint DEFAULT NULL COMMENT '部门id',
  `post_id` bigint DEFAULT NULL COMMENT '岗位id',
  `role_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色权限',
  `job_nature` tinyint(1) DEFAULT NULL COMMENT '工作性质：0全职，1外包，2兼职',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0在职，1离职',
  `is_principal` tinyint(1) NOT NULL DEFAULT '1' COMMENT '部门负责人：0是，1否',
  `superior` bigint DEFAULT NULL COMMENT '直属上级',
  `bind_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '绑定状态：0未绑，1已绑',
  `bind_user_id` bigint DEFAULT NULL COMMENT '绑定的用户id',
  `login_phone` varchar(100) DEFAULT NULL COMMENT '登录手机号',
  `service_owner_id` bigint NOT NULL COMMENT '服务商id',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0正常，1删除',
  `create_by_id` bigint NOT NULL COMMENT '创建人id',
  `create_by` varchar(100) DEFAULT '0' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by_id` bigint DEFAULT NULL COMMENT '修改人id',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  `resignation_time` datetime DEFAULT NULL COMMENT '离职时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售服务商员工表';

drop TABLE report_inbound_sales;
CREATE TABLE `report_inbound_sales` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `inquiries_number` bigint DEFAULT NULL COMMENT '进线咨询人数',
  `online_duration` bigint DEFAULT NULL COMMENT '晚间上线时长',
  `dept_id` bigint DEFAULT NULL COMMENT '部门id',
  `franchise_id` bigint DEFAULT NULL COMMENT '加盟商id',
  `brand_id` bigint DEFAULT NULL COMMENT '品牌商id',
  `proceed_date` date DEFAULT NULL COMMENT '进行日期',
  `user_id` bigint DEFAULT NULL COMMENT '客服id',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0正常，1删除',
  `create_by_id` bigint NOT NULL COMMENT '创建人id',
  `create_by` varchar(100) DEFAULT '0' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by_id` bigint DEFAULT NULL COMMENT '修改人id',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  `resignation_time` datetime DEFAULT NULL COMMENT '离职时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售进线';

drop TABLE xgwc_market_cooperative;
CREATE TABLE `xgwc_market_cooperative` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `service_owner_id` bigint NOT NULL COMMENT '销售服务商id',
  `franchise_id` bigint DEFAULT NULL COMMENT '加盟商id',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0合作中，1终止合作',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0正常，1删除',
  `create_by_id` bigint NOT NULL COMMENT '创建人id',
  `create_by` varchar(100) DEFAULT '0' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by_id` bigint DEFAULT NULL COMMENT '修改人id',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售合作';

CREATE TABLE `saas_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_name` varchar(50) DEFAULT NULL COMMENT '角色名称',
  `sort` int DEFAULT '0' COMMENT '排序',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否系统默认角色（0-否，1-是）',
  `role_scope` tinyint DEFAULT NULL COMMENT '角色权限范围:0-品普 1-品管 2-加普 3-加管 4-服普 5-服管 6-销普 7-销管 8-saas普 9-saas管',
  `business_system` tinyint DEFAULT NULL COMMENT '业务系统 0-猴霸 1-人资',
  `status` tinyint DEFAULT '0' COMMENT '角色状态（0-正常/1-禁用）',
  `is_del` tinyint DEFAULT '0' NULL COMMENT '删除 0-正常 1-删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='saas后台角色表';

CREATE TABLE `saas_role_scope` (
  `id` bigint NOT NULL COMMENT '主键',
  `role_id` bigint DEFAULT NULL COMMENT 'saas后台角色id',
  `effective_scope` bigint DEFAULT NULL COMMENT '生效范围 品牌商/加盟商/财务服务商/销售服务商（id）',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
-- 2025-08-01
CREATE TABLE `brand_cs_commission_rate_config` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `brand_id` bigint NOT NULL COMMENT '品牌商ID',
   `type` tinyint NOT NULL COMMENT '配置类型: 1 基础, 2 团队负责人, 3 单人',
   `franchisee_id` bigint NOT NULL COMMENT '加盟商ID',
   `gear_name` varchar(128) NOT NULL COMMENT '档位名称',
   `staff_id` bigint NOT NULL COMMENT '加盟商员工ID',
   `dept_id` bigint NOT NULL COMMENT '加盟商部门ID',
   `business_id` bigint NOT NULL COMMENT '业务ID',
   `effective_cycle_type` tinyint(1) NOT NULL COMMENT '生效周期类型: 1 长期有效, 2 指定时间',
   `start_time` datetime NOT NULL COMMENT '生效开始时间',
   `end_time` datetime NOT NULL COMMENT '生效截止时间',
   `status` tinyint(1) NOT NULL COMMENT '状态: 1生效, 2失效',
   `is_new_business` tinyint(1) NOT NULL COMMENT '是否新业务(0:否 1:是)',
   `extra_month_count` tinyint NOT NULL COMMENT '新业务额外前x月',
   `extra_type` tinyint(1) NOT NULL COMMENT '额外类型: 1本月生效, 2次月生效',
   `extra_commission_rate` decimal(4,2) NOT NULL COMMENT '额外加x比例',
   `is_contains_min` tinyint(1) NOT NULL COMMENT '是否包含设定区间最小值',
   `range_rate_json` json NOT NULL COMMENT '区间比例配置(JSON格式)',
   `create_by_id` bigint NOT NULL COMMENT '创建人id',
   `update_time` datetime NOT NULL COMMENT '更新时间',
   `create_time` datetime NOT NULL COMMENT '创建时间',
   `update_by_id` bigint NOT NULL COMMENT '修改人id',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='品牌商客服提成完成率配置';

-- 2025-08-02
CREATE TABLE `brand_night_performance_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `brand_id` bigint NOT NULL COMMENT '品牌商ID',
    `franchisee_id` bigint NOT NULL COMMENT '加盟商ID',
    `department_id` bigint NOT NULL COMMENT '部门ID',
    `night_time_json` json DEFAULT NULL COMMENT '晚间时间段配置JSON',
    `create_by_id` bigint NOT NULL COMMENT '创建人id',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_by_id` bigint NOT NULL COMMENT '修改人id',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='品牌商晚间业绩配置表';

CREATE TABLE `report_brand_night_performance_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `brand_id` bigint NOT NULL COMMENT '品牌商ID',
    `stat_dimension` tinyint NOT NULL COMMENT '统计维度: 1 加盟商月, 2 加盟商部门月, 3 加盟商日, ',
    `stat_start_time` datetime NOT NULL COMMENT '统计开始时间',
    `stat_end_time` datetime NOT NULL COMMENT '统计截止时间',
    `franchisee_id` bigint NOT NULL COMMENT '加盟商ID',
    `department_id` bigint NOT NULL COMMENT '部门ID',
    `order_count` int NOT NULL COMMENT '业绩单量',
    `order_amount` decimal(15,2) NOT NULL COMMENT '订单金额',
    `actual_amount` decimal(15,2) NOT NULL COMMENT '实收金额',
    `other_biz_json` json DEFAULT NULL COMMENT '其他内容JSON',
    `updated_time` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='品牌商晚间业绩统计表';

CREATE TABLE `xgwc_market_staff_cooperative` (
`id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
`staff_id` bigint NOT NULL COMMENT '员工id',
`service_owner_id` bigint NOT NULL COMMENT '销售服务商id',
`franchise_id` bigint DEFAULT NULL COMMENT '加盟商id',
`franchise_staff_id` bigint NOT NULL COMMENT '加盟商员工id',
`is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0正常，1删除',
`create_by_id` bigint NOT NULL COMMENT '创建人id',
`create_by` varchar(100) DEFAULT '0' COMMENT '创建人',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_by_id` bigint DEFAULT NULL COMMENT '修改人id',
`update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
`modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '行更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='员工加盟表';

-- 2025-08-04
ALTER TABLE report_inbound_sales DROP COLUMN resignation_time;
