<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xgwc.datamigration.basic.mapper.MigrationTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xgwc.datamigration.basic.entity.MigrationTask">
        <id column="id" property="id" />
        <result column="source_table" property="sourceTable" />
        <result column="target_table" property="targetTable" />
        <result column="field_mapping" property="fieldMapping" />
        <result column="status" property="status" />
        <result column="min_id" property="minId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, source_table, target_table, field_mapping, status, min_id
    </sql>

</mapper>
