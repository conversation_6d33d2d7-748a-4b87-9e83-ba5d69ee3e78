package com.xgwc.datamigration;

import com.github.pagehelper.PageInterceptor;
import com.xgwc.datamigration.config.SourceDataSourceConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
@EnableConfigurationProperties({SourceDataSourceConfig.class})
@SpringBootApplication
@MapperScan({"com.xgwc.**.mapper"})
public class XgwcDataMigrationApplication {

    @Bean
    public PageInterceptor pageInterceptor() {
        PageInterceptor interceptor = new PageInterceptor();
        Properties properties = new Properties();
        properties.setProperty("supportMethodsArguments", "true");
        properties.setProperty("helperDialect", "postgresql");
        properties.setProperty("reasonable", "true");
        properties.setProperty("params", "count=countSql");
        interceptor.setProperties(properties);
        return interceptor;
    }

    public static void main(String[] args) {
        SpringApplication.run(XgwcDataMigrationApplication.class, args);
    }

}