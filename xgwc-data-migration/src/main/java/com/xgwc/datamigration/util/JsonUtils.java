package com.xgwc.datamigration.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.ParserConfig;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

@SuppressWarnings("unused")
public class JsonUtils {

    static {
        ParserConfig.getGlobalInstance().setSafeMode(true);
    }

    public static <T> T toBean(String json, Class<T> clazz) {
        return JSONObject.parseObject(json, clazz);
    }

    public static <T> List<T> toList(String json, Class<T> clazz) {
        return JSONObject.parseArray(json, clazz);
    }

    public static <T> T toBean(String json, TypeReference<T> type) {
        return JSONObject.parseObject(json, type);
    }

    public static <T> T toBean(String json, Type type) {
        return JSONObject.parseObject(json, type);
    }

    public static String toJsonString(Object obj) {
        return JSONObject.toJSONString(obj);
    }

    public static String toJsonString(Object obj, boolean prettyFormat) {
        return JSONObject.toJSONString(obj, prettyFormat);
    }

    public static Map<?, ?> toMap(String json) {
        return JSONObject.parseObject(json, Map.class);
    }

}