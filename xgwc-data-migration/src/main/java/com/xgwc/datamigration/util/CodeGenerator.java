package com.xgwc.datamigration.util;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.Collections;

public class CodeGenerator {

    public static void main(String[] args) {
        String url = "********************************************************************************************";
        String username = "root";
        String password = ",uPkdLhlC1,t";

        FastAutoGenerator.create(url, username, password)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author("hehairong") // 设置作者
                            .outputDir("src/main/java"); // 指定输出目录
                })
                // 包配置
                .packageConfig(builder -> {
                    builder.parent("com.xgwc.datamigration") // 设置父包名
                            .moduleName("basic") // 设置模块名
                            .pathInfo(Collections.singletonMap(OutputFile.xml, "xgwc-data-migration/src/main/resources/mapper")); // 设置mapperXml生成路径
                })
                // 策略配置
                .strategyConfig(builder -> {
                    builder.addInclude("migration_task") // 设置需要生成的表名
                            .addTablePrefix("t_", "sys_"); // 设置过滤表前缀

                    // 实体类策略
                    builder.entityBuilder().enableFileOverride().enableLombok() // 开启 Lombok
                            .logicDeletePropertyName("deleted") // 逻辑删除字段名
                            .versionPropertyName(""); // 乐观锁字段名

                    // Controller 策略
                    builder.controllerBuilder().enableFileOverride().enableRestStyle(); // 开启 RESTful 风格

                    // Service 策略
                    builder.serviceBuilder().enableFileOverride().formatServiceFileName("%sService"); // 自定义 Service 名称格式

//                    // Mapper 策略
                    builder.mapperBuilder().enableFileOverride().enableBaseResultMap() // 启用 BaseResultMap 生成
                            .enableBaseColumnList(); // 启用 BaseColumnList
                })
                // 使用Freemarker引擎模板，默认的是Velocity引擎
                .templateEngine(new FreemarkerTemplateEngine()).execute();
    }
}