package com.xgwc.datamigration.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xgwc.datamigration.util.JsonUtils;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 迁移任务配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Setter
@TableName("migration_task")
public class MigrationTask implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 源头表
     */
    private String sourceTable;

    /**
     * 目标表
     */
    private String targetTable;

    /**
     * 字段对应关系json
     */
    private String fieldMapping;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 最小id
     */
    private Long minId;


    public void complete() {
        this.status = "complete";
    }

    public void fail() {
        this.status = "fail";
    }


    public List<String> getTargetTableFields() {
        return JsonUtils.toList(this.fieldMapping, String.class).stream().map(x -> x.split("#")[0]).toList();
    }

    public List<String> getSourceTableFields() {
        return JsonUtils.toList(this.fieldMapping, String.class).stream().map(x -> x.split("#")[1]).toList();
    }

}
