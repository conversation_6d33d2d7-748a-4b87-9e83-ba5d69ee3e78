package com.xgwc.datamigration.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.ImmutableList;
import com.xgwc.datamigration.basic.entity.MigrationTask;
import com.xgwc.datamigration.basic.mapper.MigrationTaskMapper;
import com.xgwc.datamigration.basic.service.MigrationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xgwc.datamigration.basic.service.MigrationTaskService;
import com.xgwc.datamigration.config.SourceDataSourceConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.List;

/**
 * <p>
 * 迁移任务配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
public class MigrationTaskServiceImpl extends ServiceImpl<MigrationTaskMapper, MigrationTask> implements MigrationTaskService {

    @Resource
    private MigrationService migrationService;
    @Resource
    private MigrationTaskMapper migrationTaskMapper;
    @Resource
    private SourceDataSourceConfig sourceDataSourceConfig;

    @Override
    public void startMigrations() {
        LambdaQueryWrapper<MigrationTask> query = new QueryWrapper<MigrationTask>().lambda();
        query.in(MigrationTask::getStatus, ImmutableList.of("pending", "fail"));
        List<MigrationTask> taskList = migrationTaskMapper.selectList(query);
        if (taskList.isEmpty()) {
            return;
        }

        DataSource sourceDataSource = this.getSourceDataSource();
        taskList.forEach(task -> {
            try {
                migrationService.migration(task, sourceDataSource);
            } catch (Exception e) {
                log.error("{}表迁移异常", task.getTargetTable(), e);
                migrationTaskMapper.updateById(task);
            }
        });
    }

    public String checkData() {
        LambdaQueryWrapper<MigrationTask> query = new QueryWrapper<MigrationTask>().lambda();
        query.eq(MigrationTask::getStatus, "complete");
        List<MigrationTask> taskList = migrationTaskMapper.selectList(query);
        if (taskList.isEmpty()) {
            return "暂无迁移完成的表";
        }
        DataSource sourceDataSource = this.getSourceDataSource();
        return migrationService.checkData(taskList, sourceDataSource);
    }

    private DataSource getSourceDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(sourceDataSourceConfig.getJdbcUrl());
        config.setUsername(sourceDataSourceConfig.getUsername());
        config.setPassword(sourceDataSourceConfig.getPassword());
        config.setDriverClassName(sourceDataSourceConfig.getDriverClassName());
        config.setPoolName("SourceDB-Pool");
        return new HikariDataSource(config);
    }

}