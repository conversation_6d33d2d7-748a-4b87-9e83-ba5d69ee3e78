package com.xgwc.datamigration.basic.service;

import com.xgwc.datamigration.basic.entity.MigrationTask;
import com.xgwc.datamigration.basic.mapper.MigrationTaskMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Service
public class MigrationService {

    private final long limit = 2000;

    @Resource
    private DataSource targetDataSource;
    @Resource
    private MigrationTaskMapper migrationTaskMapper;

    public void migration(MigrationTask task, DataSource sourceDataSource) {
        try (Connection sourceConn = sourceDataSource.getConnection();
             Connection targetConn = targetDataSource.getConnection()) {

            long startTime = System.currentTimeMillis();
            AtomicLong successCount = new AtomicLong(0);

            // 分页处理
            while (this.pageHandle(task, sourceConn, targetConn, successCount)) {
                log.info("limit={}, {} 当前进度id={}", limit, task.getTargetTable(), task.getMinId());
            }

            if (successCount.get() > 0) {
                ResultSet rs = sourceConn.prepareStatement("SELECT max(id) FROM " + task.getSourceTable() + " WHERE id > " + task.getMinId()).executeQuery();
                if (rs.next() && task.getMinId() >= rs.getLong(1)) {
                    task.complete();
                    log.info("{} 迁移完成, limit={}, 耗时: {}毫秒", task.getTargetTable(), limit, System.currentTimeMillis() - startTime);
                }
                migrationTaskMapper.updateById(task);
            }
        } catch (SQLException e) {
            log.error("{} 迁移任务出现异常", task.getTargetTable(), e);
            task.fail();
            migrationTaskMapper.updateById(task);
        }
    }

    private boolean pageHandle(MigrationTask task, Connection sourceConn, Connection targetConn, AtomicLong successCount) throws SQLException {
        long minId = task.getMinId();
        ResultSet queryRs = sourceConn.prepareStatement(this.buildQuerySourceSql(task)).executeQuery();
        // 构建批处理插入
        PreparedStatement insertStmt = targetConn.prepareStatement(this.buildInsertStatement(task));
        targetConn.setAutoCommit(false);
        long dataCount = 0;
        try {
            while (queryRs.next()) {
                // 设置插入参数
                this.setInsertParameters(insertStmt, queryRs, task);
                insertStmt.addBatch();
                dataCount++;
                minId = queryRs.getLong(1);
            }
        } catch (Exception e) {
            log.error("{} 迁移分页处理时出现异常", task.getTargetTable(), e);
            task.fail();
            migrationTaskMapper.updateById(task);
            return false;
        }
        if (dataCount > 0) {
            insertStmt.executeBatch();
            targetConn.commit();
            insertStmt.clearBatch();

            task.setMinId(minId);
            successCount.addAndGet(dataCount);
            return true;
        }
        return false;
    }

    private String buildQuerySourceSql(MigrationTask task) {
        StringBuilder sql = new StringBuilder("SELECT ");
        // 添加源头表字段
        for (String field : task.getSourceTableFields()) {
            sql.append(field).append(", ");
        }
        sql.delete(sql.length() - 2, sql.length()); // 移除最后逗号
        sql.append(" FROM ").append(task.getSourceTable()).append(" WHERE id > ").append(task.getMinId()).append(" ORDER BY ID ASC LIMIT ").append(limit);
        return sql.toString();
    }

    private String buildInsertStatement(MigrationTask task) {
        StringBuilder columns = new StringBuilder();
        StringBuilder placeholders = new StringBuilder();

        for (String targetField : task.getTargetTableFields()) {
            columns.append(targetField).append(", ");
            placeholders.append("?, ");
        }
        if (!columns.isEmpty()) {
            columns.setLength(columns.length() - 2);  // 移除最后的 ", "
            placeholders.setLength(placeholders.length() - 2);
        }
        return "INSERT INTO " + task.getTargetTable() + " (" + columns + ") " + "VALUES (" + placeholders + ")";
    }

    private void setInsertParameters(PreparedStatement stmt, ResultSet rs, MigrationTask task) throws SQLException {
        for (int i = 1; i < task.getTargetTableFields().size() + 1; i++) {
            stmt.setObject(i, rs.getObject(i));
        }
    }

    /**
     * 检查数据迁移情况
     */
    public String checkData(List<MigrationTask> taskList, DataSource sourceDataSource) {
        List<String> expList = new ArrayList<>();
        List<String> okList = new ArrayList<>();
        try (Connection sourceConn = sourceDataSource.getConnection();
             Connection targetConn = targetDataSource.getConnection()) {

            for (MigrationTask task : taskList) {
                ResultSet rs = sourceConn.prepareStatement("SELECT max(id) FROM " + task.getSourceTable()).executeQuery();
                if (rs.next()) {
                    long maxId = rs.getLong(1);
                    ResultSet resultSet = targetConn.prepareStatement("SELECT id FROM " + task.getTargetTable() + " where id = " + maxId).executeQuery();
                    if (resultSet.next()) {
                        if (maxId == resultSet.getLong(1)) {
                            okList.add(task.getTargetTable());
                            continue;
                        }
                    }
                }
                expList.add(task.getTargetTable());
            }
        } catch (SQLException e) {
            log.error("迁移数据检查出现异常", e);
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (!okList.isEmpty()) {
            stringBuilder.append("迁移成功的表有: ").append(String.join(",", okList)).append("\n");
        }
        if (!expList.isEmpty()) {
            stringBuilder.append("迁移异常的表有:").append(String.join(",", expList));
        }
        return stringBuilder.toString();
    }
}