package com.xgwc.datamigration.basic.controller;

import com.xgwc.datamigration.basic.service.MigrationTaskService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 迁移任务配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@RestController
@RequestMapping("/basic/migrationTask")
public class MigrationTaskController {

    @Resource
    private MigrationTaskService migrationTaskService;

    @PostMapping("/start")
    public void start() {
        migrationTaskService.startMigrations();
    }

    @PostMapping("/checkData")
    public String checkData() {
        return migrationTaskService.checkData();
    }

}
